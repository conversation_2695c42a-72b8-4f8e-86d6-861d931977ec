{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/EdgeCaseHandler.test.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gEAA+D;AAK/D,sDAAsD;AACtD,IAAM,0BAA0B,GAAG,cAA0C,OAAA,CAAC;IAC5E,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;IAC3B,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;IAC5B,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;IACzB,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;IAC3B,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;IACxB,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;CACxB,CAAA,EAPoE,CAOpE,CAAC;AAEV,IAAM,2BAA2B,GAAG,cAA2C,OAAA,CAAC;IAC9E,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;IAC7B,2BAA2B,EAAE,IAAI,CAAC,EAAE,EAAE;IACtC,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE;IAC9B,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;IAC5B,0BAA0B,EAAE,IAAI,CAAC,EAAE,EAAE;IACrC,6BAA6B,EAAE,IAAI,CAAC,EAAE,EAAE;CACjC,CAAA,EAPsE,CAOtE,CAAC;AAEV,IAAM,6BAA6B,GAAG,cAAoD,OAAA,CAAC;IACzF,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;IAC/B,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;IACzB,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;CACrB,CAAA,EAJiF,CAIjF,CAAC;AAEV,QAAQ,CAAC,mCAAmC,EAAE;IAC5C,IAAI,eAAgC,CAAC;IACrC,IAAI,oBAAwD,CAAC;IAC7D,IAAI,qBAA0D,CAAC;IAC/D,IAAI,uBAAqE,CAAC;IAE1E,UAAU,CAAC;QACT,0BAA0B;QAC1B,oBAAoB,GAAG,0BAA0B,EAAE,CAAC;QACpD,qBAAqB,GAAG,2BAA2B,EAAE,CAAC;QACtD,uBAAuB,GAAG,6BAA6B,EAAE,CAAC;QAE1D,eAAe,GAAG,IAAI,iCAAe,CACnC,oBAAoB,EACpB,qBAAqB,EACrB,uBAAuB,CACxB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC;QACR,0BAA0B;QAC1B,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE;QACvC,EAAE,CAAC,uCAAuC,EAAE;;;;4BAC3B,qBAAM,eAAe,CAAC,qBAAqB,CAAC,IAAW,CAAC,EAAA;;wBAAjE,MAAM,GAAG,SAAwD;wBAEvE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;wBAChD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBAClD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC3C,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,8CAA8C,EAAE;;;;;wBAC3C,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,YAAY,EAAE,UAAU;yBACzB,CAAC;wBAEF,oBAAoB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC;4BACtD,EAAE,EAAE,gBAAgB;4BACpB,MAAM,EAAE,OAAO,CAAC,MAAM;yBAChB,CAAC,CAAC;wBAEK,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAA;;wBAA7D,MAAM,GAAG,SAAoD;wBAEnE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aACnC,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,2CAA2C,EAAE;;;;;wBACxC,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,YAAY,EAAE,UAAU;yBACzB,CAAC;wBAEF,oBAAoB,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;4BACvD,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;wBACzC,CAAC,CAAC,CAAC;wBAEY,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAA;;wBAA7D,MAAM,GAAG,SAAoD;wBAEnE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;wBACtD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC3C,EAAE,IAAI,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,yEAAyE;IACzE,uCAAuC;IACvC,2CAA2C;IAC3C,4CAA4C;IAC5C,qDAAqD;AAGvD,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/EdgeCaseHandler.test.ts"], "sourcesContent": ["import { EdgeCaseHandler } from '@/lib/skills/EdgeCaseHandler';\nimport { SkillAssessmentEngine } from '@/lib/skills/SkillAssessmentEngine';\nimport { SkillMarketDataService } from '@/lib/skills/SkillMarketDataService';\nimport { PersonalizedLearningPathService } from '@/lib/skills/PersonalizedLearningPathService';\n\n// Performance optimization: Use shared mock instances\nconst createMockAssessmentEngine = (): jest.Mocked<SkillAssessmentEngine> => ({\n  createAssessment: jest.fn(),\n  generateQuestions: jest.fn(),\n  submitResponse: jest.fn(),\n  calculateResults: jest.fn(),\n  getAssessment: jest.fn(),\n  getAssessmentsByUser: jest.fn(),\n} as any);\n\nconst createMockMarketDataService = (): jest.Mocked<SkillMarketDataService> => ({\n  getSkillMarketData: jest.fn(),\n  getMultipleSkillsMarketData: jest.fn(),\n  analyzeMarketTrends: jest.fn(),\n  getSalaryInsights: jest.fn(),\n  getLocationBasedMarketData: jest.fn(),\n  getMarketBasedRecommendations: jest.fn(),\n} as any);\n\nconst createMockLearningPathService = (): jest.Mocked<PersonalizedLearningPathService> => ({\n  generateLearningPath: jest.fn(),\n  updateProgress: jest.fn(),\n  completeMilestone: jest.fn(),\n} as any);\n\ndescribe('EdgeCaseHandler - Core Validation', () => {\n  let edgeCaseHandler: EdgeCaseHandler;\n  let mockAssessmentEngine: jest.Mocked<SkillAssessmentEngine>;\n  let mockMarketDataService: jest.Mocked<SkillMarketDataService>;\n  let mockLearningPathService: jest.Mocked<PersonalizedLearningPathService>;\n\n  beforeEach(() => {\n    // Reset mocks efficiently\n    mockAssessmentEngine = createMockAssessmentEngine();\n    mockMarketDataService = createMockMarketDataService();\n    mockLearningPathService = createMockLearningPathService();\n\n    edgeCaseHandler = new EdgeCaseHandler(\n      mockAssessmentEngine,\n      mockMarketDataService,\n      mockLearningPathService\n    );\n  });\n\n  afterEach(() => {\n    // Cleanup for performance\n    jest.clearAllMocks();\n  });\n\n  describe('Core Edge Case Functionality', () => {\n    it('should handle basic validation errors', async () => {\n      const result = await edgeCaseHandler.handleSkillAssessment(null as any);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Invalid input');\n      expect(result.errorType).toBe('VALIDATION_ERROR');\n      expect(result.fallbackData).toBeDefined();\n    }, 3000);\n\n    it('should handle successful assessment creation', async () => {\n      const request = {\n        userId: 'user-123',\n        skillIds: ['javascript'],\n        careerPathId: 'path-456',\n      };\n\n      mockAssessmentEngine.createAssessment.mockResolvedValue({\n        id: 'assessment-123',\n        userId: request.userId,\n      } as any);\n\n      const result = await edgeCaseHandler.handleSkillAssessment(request);\n\n      expect(result.success).toBe(true);\n      expect(result.data).toBeDefined();\n    }, 3000);\n\n    it('should handle service failures gracefully', async () => {\n      const request = {\n        userId: 'user-123',\n        skillIds: ['javascript'],\n        careerPathId: 'path-456',\n      };\n\n      mockAssessmentEngine.createAssessment.mockImplementation(() => {\n        throw new Error('Service unavailable');\n      });\n\n      const result = await edgeCaseHandler.handleSkillAssessment(request);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Service unavailable');\n      expect(result.fallbackData).toBeDefined();\n    }, 3000);\n  });\n\n  // Note: Comprehensive edge case tests have been moved to separate files:\n  // - EdgeCaseHandler.validation.test.ts\n  // - EdgeCaseHandler.business-logic.test.ts\n  // - EdgeCaseHandler.system-failures.test.ts\n  // This improves test performance and maintainability\n\n\n});\n"], "version": 3}