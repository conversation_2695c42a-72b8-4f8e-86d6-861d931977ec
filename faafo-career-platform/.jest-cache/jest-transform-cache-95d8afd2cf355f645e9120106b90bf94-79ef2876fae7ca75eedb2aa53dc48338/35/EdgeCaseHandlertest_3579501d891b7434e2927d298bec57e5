04df5f2b01b02fe6cfeaed95bc3ede51
"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EdgeCaseHandler_1 = require("@/lib/skills/EdgeCaseHandler");
// Performance optimization: Use shared mock instances
var createMockAssessmentEngine = function () { return ({
    createAssessment: jest.fn(),
    generateQuestions: jest.fn(),
    submitResponse: jest.fn(),
    calculateResults: jest.fn(),
    getAssessment: jest.fn(),
    getAssessmentsByUser: jest.fn(),
}); };
var createMockMarketDataService = function () { return ({
    getSkillMarketData: jest.fn(),
    getMultipleSkillsMarketData: jest.fn(),
    analyzeMarketTrends: jest.fn(),
    getSalaryInsights: jest.fn(),
    getLocationBasedMarketData: jest.fn(),
    getMarketBasedRecommendations: jest.fn(),
}); };
var createMockLearningPathService = function () { return ({
    generateLearningPath: jest.fn(),
    updateProgress: jest.fn(),
    completeMilestone: jest.fn(),
}); };
describe('EdgeCaseHandler - Core Validation', function () {
    var edgeCaseHandler;
    var mockAssessmentEngine;
    var mockMarketDataService;
    var mockLearningPathService;
    beforeEach(function () {
        // Reset mocks efficiently
        mockAssessmentEngine = createMockAssessmentEngine();
        mockMarketDataService = createMockMarketDataService();
        mockLearningPathService = createMockLearningPathService();
        edgeCaseHandler = new EdgeCaseHandler_1.EdgeCaseHandler(mockAssessmentEngine, mockMarketDataService, mockLearningPathService);
    });
    afterEach(function () {
        // Cleanup for performance
        jest.clearAllMocks();
    });
    describe('Core Edge Case Functionality', function () {
        it('should handle basic validation errors', function () { return __awaiter(void 0, void 0, void 0, function () {
            var result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(null)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Invalid input');
                        expect(result.errorType).toBe('VALIDATION_ERROR');
                        expect(result.fallbackData).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
        it('should handle successful assessment creation', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                        };
                        mockAssessmentEngine.createAssessment.mockResolvedValue({
                            id: 'assessment-123',
                            userId: request.userId,
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(true);
                        expect(result.data).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
        it('should handle service failures gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                        };
                        mockAssessmentEngine.createAssessment.mockImplementation(function () {
                            throw new Error('Service unavailable');
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Service unavailable');
                        expect(result.fallbackData).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
    });
    // Note: Comprehensive edge case tests have been moved to separate files:
    // - EdgeCaseHandler.validation.test.ts
    // - EdgeCaseHandler.business-logic.test.ts
    // - EdgeCaseHandler.system-failures.test.ts
    // This improves test performance and maintainability
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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