3eca92600704fd9fe0949b3cdddebe14
"use strict";
'use client';

/* istanbul ignore next */
function cov_206w2db2qd() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/skills/visualizations/SkillGapRadarChart.tsx";
  var hash = "7eb79edec65d2de0263b9780f3eff4152d70098b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/skills/visualizations/SkillGapRadarChart.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 22
        },
        end: {
          line: 5,
          column: 1
        }
      },
      "1": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 4,
          column: 62
        }
      },
      "2": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 62
        }
      },
      "3": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 7,
          column: 37
        }
      },
      "4": {
        start: {
          line: 8,
          column: 20
        },
        end: {
          line: 8,
          column: 48
        }
      },
      "5": {
        start: {
          line: 9,
          column: 14
        },
        end: {
          line: 9,
          column: 47
        }
      },
      "6": {
        start: {
          line: 10,
          column: 17
        },
        end: {
          line: 10,
          column: 36
        }
      },
      "7": {
        start: {
          line: 11,
          column: 13
        },
        end: {
          line: 11,
          column: 44
        }
      },
      "8": {
        start: {
          line: 13,
          column: 15
        },
        end: {
          line: 13,
          column: 22
        }
      },
      "9": {
        start: {
          line: 13,
          column: 29
        },
        end: {
          line: 13,
          column: 37
        }
      },
      "10": {
        start: {
          line: 13,
          column: 47
        },
        end: {
          line: 13,
          column: 88
        }
      },
      "11": {
        start: {
          line: 13,
          column: 95
        },
        end: {
          line: 13,
          column: 109
        }
      },
      "12": {
        start: {
          line: 13,
          column: 125
        },
        end: {
          line: 13,
          column: 211
        }
      },
      "13": {
        start: {
          line: 13,
          column: 218
        },
        end: {
          line: 13,
          column: 227
        }
      },
      "14": {
        start: {
          line: 13,
          column: 238
        },
        end: {
          line: 13,
          column: 262
        }
      },
      "15": {
        start: {
          line: 13,
          column: 269
        },
        end: {
          line: 13,
          column: 282
        }
      },
      "16": {
        start: {
          line: 13,
          column: 297
        },
        end: {
          line: 13,
          column: 322
        }
      },
      "17": {
        start: {
          line: 13,
          column: 329
        },
        end: {
          line: 13,
          column: 340
        }
      },
      "18": {
        start: {
          line: 13,
          column: 353
        },
        end: {
          line: 13,
          column: 376
        }
      },
      "19": {
        start: {
          line: 14,
          column: 24
        },
        end: {
          line: 21,
          column: 5
        }
      },
      "20": {
        start: {
          line: 15,
          column: 21
        },
        end: {
          line: 19,
          column: 9
        }
      },
      "21": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 20,
          column: 63
        }
      },
      "22": {
        start: {
          line: 22,
          column: 22
        },
        end: {
          line: 25,
          column: 5
        }
      },
      "23": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 84
        }
      },
      "24": {
        start: {
          line: 27,
          column: 22
        },
        end: {
          line: 31,
          column: 5
        }
      },
      "25": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 29,
          column: 21
        }
      },
      "26": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 21
        }
      },
      "27": {
        start: {
          line: 30,
          column: 8
        },
        end: {
          line: 30,
          column: 95
        }
      },
      "28": {
        start: {
          line: 30,
          column: 53
        },
        end: {
          line: 30,
          column: 72
        }
      },
      "29": {
        start: {
          line: 32,
          column: 25
        },
        end: {
          line: 32,
          column: 88
        }
      },
      "30": {
        start: {
          line: 32,
          column: 64
        },
        end: {
          line: 32,
          column: 84
        }
      },
      "31": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 86
        }
      },
      "32": {
        start: {
          line: 33,
          column: 63
        },
        end: {
          line: 33,
          column: 82
        }
      },
      "33": {
        start: {
          line: 34,
          column: 21
        },
        end: {
          line: 34,
          column: 111
        }
      },
      "34": {
        start: {
          line: 34,
          column: 60
        },
        end: {
          line: 34,
          column: 107
        }
      },
      "35": {
        start: {
          line: 35,
          column: 4
        },
        end: {
          line: 40,
          column: 1589
        }
      },
      "36": {
        start: {
          line: 40,
          column: 179
        },
        end: {
          line: 40,
          column: 271
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 57
          }
        },
        loc: {
          start: {
            line: 3,
            column: 71
          },
          end: {
            line: 5,
            column: 1
          }
        },
        line: 3
      },
      "1": {
        name: "SkillGapRadarChart",
        decl: {
          start: {
            line: 12,
            column: 9
          },
          end: {
            line: 12,
            column: 27
          }
        },
        loc: {
          start: {
            line: 12,
            column: 32
          },
          end: {
            line: 41,
            column: 1
          }
        },
        line: 12
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 14,
            column: 24
          },
          end: {
            line: 14,
            column: 25
          }
        },
        loc: {
          start: {
            line: 14,
            column: 47
          },
          end: {
            line: 21,
            column: 5
          }
        },
        line: 14
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 22,
            column: 22
          },
          end: {
            line: 22,
            column: 23
          }
        },
        loc: {
          start: {
            line: 22,
            column: 39
          },
          end: {
            line: 25,
            column: 5
          }
        },
        line: 22
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 27,
            column: 22
          },
          end: {
            line: 27,
            column: 23
          }
        },
        loc: {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 31,
            column: 5
          }
        },
        line: 27
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 30,
            column: 29
          },
          end: {
            line: 30,
            column: 30
          }
        },
        loc: {
          start: {
            line: 30,
            column: 51
          },
          end: {
            line: 30,
            column: 74
          }
        },
        line: 30
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 32,
            column: 46
          },
          end: {
            line: 32,
            column: 47
          }
        },
        loc: {
          start: {
            line: 32,
            column: 62
          },
          end: {
            line: 32,
            column: 86
          }
        },
        line: 32
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 33,
            column: 45
          },
          end: {
            line: 33,
            column: 46
          }
        },
        loc: {
          start: {
            line: 33,
            column: 61
          },
          end: {
            line: 33,
            column: 84
          }
        },
        line: 33
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 34,
            column: 42
          },
          end: {
            line: 34,
            column: 43
          }
        },
        loc: {
          start: {
            line: 34,
            column: 58
          },
          end: {
            line: 34,
            column: 109
          }
        },
        line: 34
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 40,
            column: 153
          },
          end: {
            line: 40,
            column: 154
          }
        },
        loc: {
          start: {
            line: 40,
            column: 177
          },
          end: {
            line: 40,
            column: 273
          }
        },
        line: 40
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 5,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 23
          },
          end: {
            line: 3,
            column: 27
          }
        }, {
          start: {
            line: 3,
            column: 31
          },
          end: {
            line: 3,
            column: 51
          }
        }, {
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 5,
            column: 1
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 4,
            column: 11
          },
          end: {
            line: 4,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 4,
            column: 37
          },
          end: {
            line: 4,
            column: 40
          }
        }, {
          start: {
            line: 4,
            column: 43
          },
          end: {
            line: 4,
            column: 61
          }
        }],
        line: 4
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 12
          },
          end: {
            line: 4,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 12
          },
          end: {
            line: 4,
            column: 15
          }
        }, {
          start: {
            line: 4,
            column: 19
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 47
          },
          end: {
            line: 13,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 13,
            column: 63
          },
          end: {
            line: 13,
            column: 83
          }
        }, {
          start: {
            line: 13,
            column: 86
          },
          end: {
            line: 13,
            column: 88
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 13,
            column: 125
          },
          end: {
            line: 13,
            column: 211
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 13,
            column: 141
          },
          end: {
            line: 13,
            column: 206
          }
        }, {
          start: {
            line: 13,
            column: 209
          },
          end: {
            line: 13,
            column: 211
          }
        }],
        line: 13
      },
      "5": {
        loc: {
          start: {
            line: 13,
            column: 238
          },
          end: {
            line: 13,
            column: 262
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 13,
            column: 254
          },
          end: {
            line: 13,
            column: 257
          }
        }, {
          start: {
            line: 13,
            column: 260
          },
          end: {
            line: 13,
            column: 262
          }
        }],
        line: 13
      },
      "6": {
        loc: {
          start: {
            line: 13,
            column: 297
          },
          end: {
            line: 13,
            column: 322
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 13,
            column: 313
          },
          end: {
            line: 13,
            column: 317
          }
        }, {
          start: {
            line: 13,
            column: 320
          },
          end: {
            line: 13,
            column: 322
          }
        }],
        line: 13
      },
      "7": {
        loc: {
          start: {
            line: 13,
            column: 353
          },
          end: {
            line: 13,
            column: 376
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 13,
            column: 369
          },
          end: {
            line: 13,
            column: 371
          }
        }, {
          start: {
            line: 13,
            column: 374
          },
          end: {
            line: 13,
            column: 376
          }
        }],
        line: 13
      },
      "8": {
        loc: {
          start: {
            line: 20,
            column: 41
          },
          end: {
            line: 20,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 20,
            column: 41
          },
          end: {
            line: 20,
            column: 53
          }
        }, {
          start: {
            line: 20,
            column: 57
          },
          end: {
            line: 20,
            column: 61
          }
        }],
        line: 20
      },
      "9": {
        loc: {
          start: {
            line: 24,
            column: 15
          },
          end: {
            line: 24,
            column: 83
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 24,
            column: 35
          },
          end: {
            line: 24,
            column: 75
          }
        }, {
          start: {
            line: 24,
            column: 78
          },
          end: {
            line: 24,
            column: 83
          }
        }],
        line: 24
      },
      "10": {
        loc: {
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 29,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 29,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "11": {
        loc: {
          start: {
            line: 35,
            column: 219
          },
          end: {
            line: 35,
            column: 309
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 35,
            column: 219
          },
          end: {
            line: 35,
            column: 230
          }
        }, {
          start: {
            line: 35,
            column: 235
          },
          end: {
            line: 35,
            column: 308
          }
        }],
        line: 35
      },
      "12": {
        loc: {
          start: {
            line: 40,
            column: 46
          },
          end: {
            line: 40,
            column: 277
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 46
          },
          end: {
            line: 40,
            column: 56
          }
        }, {
          start: {
            line: 40,
            column: 61
          },
          end: {
            line: 40,
            column: 276
          }
        }],
        line: 40
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/skills/visualizations/SkillGapRadarChart.tsx",
      mappings: ";AAAA,YAAY,CAAC;;;;;AAgCb,qCAmJC;;AAjLD,gDAA0B;AAC1B,qCASkB;AAClB,6CAAiG;AAmBjG,SAAwB,kBAAkB,CAAC,EAOjB;QANxB,IAAI,UAAA,EACJ,aAA4B,EAA5B,KAAK,mBAAG,oBAAoB,KAAA,EAC5B,mBAA+E,EAA/E,WAAW,mBAAG,iEAAiE,KAAA,EAC/E,cAAY,EAAZ,MAAM,mBAAG,GAAG,KAAA,EACZ,kBAAiB,EAAjB,UAAU,mBAAG,IAAI,KAAA,EACjB,gBAAa,EAAb,QAAQ,mBAAG,EAAE,KAAA;IAEb,IAAM,aAAa,GAAG,UAAC,KAAa,EAAE,IAAY;QAChD,IAAM,MAAM,GAAG;YACb,OAAO,EAAE,eAAe;YACxB,MAAM,EAAE,cAAc;YACtB,MAAM,EAAE,gBAAgB;SACzB,CAAC;QACF,OAAO,CAAC,UAAG,KAAK,QAAK,EAAE,MAAM,CAAC,IAA2B,CAAC,IAAI,IAAI,CAAC,CAAC;IACtE,CAAC,CAAC;IAEF,IAAM,WAAW,GAAG,UAAC,KAAa;QAChC,+CAA+C;QAC/C,OAAO,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,UAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,QAAK,CAAC,CAAC,CAAC,KAAK,CAAC;IACpE,CAAC,CAAC;IAEF,+BAA+B;IAC/B,IAAM,WAAW,GAAG,UAAC,MAAgB;QACnC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAClC,OAAO,MAAM,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,KAAK,IAAK,OAAA,GAAG,GAAG,KAAK,EAAX,CAAW,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;IACvE,CAAC,CAAC;IAEF,IAAM,cAAc,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,OAAO,EAAZ,CAAY,CAAC,CAAC,CAAC;IACnE,IAAM,aAAa,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,EAAX,CAAW,CAAC,CAAC,CAAC;IACjE,IAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,EAAvC,CAAuC,CAAC,CAAC,CAAC;IAE1F,OAAO,CACL,wBAAC,WAAI,eACH,wBAAC,iBAAU,eACT,uBAAC,gBAAS,IAAC,SAAS,EAAC,yBAAyB,YAC3C,KAAK,GACI,EACX,WAAW,IAAI,CACd,uBAAC,sBAAe,cAAE,WAAW,GAAmB,CACjD,IACU,EACb,wBAAC,kBAAW,eACV,gCAAK,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,QAAA,EAAE,YACnC,uBAAC,8BAAmB,cAClB,wBAAC,qBAAU,IAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,aAC1E,uBAAC,oBAAS,IACR,SAAS,EAAC,sCAAsC,EAChD,WAAW,EAAE,IAAI,GACjB,EACF,uBAAC,yBAAc,IACb,OAAO,EAAC,OAAO,EACf,IAAI,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE,EAC5C,aAAa,EAAE,WAAW,EAC1B,SAAS,EAAC,kCAAkC,GAC5C,EACF,uBAAC,0BAAe,IACd,KAAK,EAAE,EAAE,EACT,MAAM,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,EACrB,IAAI,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE,EAC5C,SAAS,EAAC,kCAAkC,GAC5C,EAEF,uBAAC,gBAAK,IACJ,IAAI,EAAC,eAAe,EACpB,OAAO,EAAC,SAAS,EACjB,MAAM,EAAC,SAAS,EAChB,IAAI,EAAC,SAAS,EACd,WAAW,EAAE,GAAG,EAChB,WAAW,EAAE,CAAC,GACd,EAEF,uBAAC,gBAAK,IACJ,IAAI,EAAC,cAAc,EACnB,OAAO,EAAC,QAAQ,EAChB,MAAM,EAAC,SAAS,EAChB,IAAI,EAAC,SAAS,EACd,WAAW,EAAE,GAAG,EAChB,WAAW,EAAE,CAAC,EACd,eAAe,EAAC,KAAK,GACrB,EAEF,uBAAC,gBAAK,IACJ,IAAI,EAAC,gBAAgB,EACrB,OAAO,EAAC,QAAQ,EAChB,MAAM,EAAC,SAAS,EAChB,IAAI,EAAC,SAAS,EACd,WAAW,EAAE,IAAI,EACjB,WAAW,EAAE,CAAC,EACd,eAAe,EAAC,KAAK,GACrB,EAEF,uBAAC,kBAAO,IACN,SAAS,EAAE,aAAa,EACxB,YAAY,EAAE;4CACZ,eAAe,EAAE,mBAAmB;4CACpC,MAAM,EAAE,yBAAyB;4CACjC,YAAY,EAAE,KAAK;4CACnB,KAAK,EAAE,mBAAmB;yCAC3B,GACD,EAED,UAAU,IAAI,CACb,uBAAC,iBAAM,IACL,aAAa,EAAC,QAAQ,EACtB,MAAM,EAAE,EAAE,EACV,SAAS,EAAE,UAAC,KAAK,EAAE,KAAK,IAAK,OAAA,CAC3B,iCAAM,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,YAAG,KAAK,GAAQ,CACpD,EAF4B,CAE5B,GACD,CACH,IACU,GACO,GAClB,EAGN,iCAAK,SAAS,EAAC,4CAA4C,aACzD,iCAAK,SAAS,EAAC,2DAA2D,aACxE,gCAAK,SAAS,EAAC,sDAAsD,gCAE/D,EACN,iCAAK,SAAS,EAAC,oDAAoD,aAChE,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,WACtB,IACF,EAEN,iCAAK,SAAS,EAAC,6DAA6D,aAC1E,gCAAK,SAAS,EAAC,wDAAwD,+BAEjE,EACN,iCAAK,SAAS,EAAC,sDAAsD,aAClE,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,WACrB,IACF,EAEN,iCAAK,SAAS,EAAC,6DAA6D,aAC1E,gCAAK,SAAS,EAAC,wDAAwD,6BAEjE,EACN,gCAAK,SAAS,EAAC,sDAAsD,YAClE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,GAClB,IACF,IACF,IACM,IACT,CACR,CAAC;AACJ,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/skills/visualizations/SkillGapRadarChart.tsx"],
      sourcesContent: ["'use client';\n\nimport React from 'react';\nimport {\n  RadarChart,\n  PolarGrid,\n  PolarAngleAxis,\n  PolarRadiusAxis,\n  Radar,\n  ResponsiveContainer,\n  Legend,\n  Tooltip,\n} from 'recharts';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\n\ninterface SkillData {\n  skill: string;\n  current: number;\n  target: number;\n  market: number;\n  category: string;\n}\n\ninterface SkillGapRadarChartProps {\n  data: SkillData[];\n  title?: string;\n  description?: string;\n  height?: number;\n  showLegend?: boolean;\n  maxValue?: number;\n}\n\nexport default function SkillGapRadarChart({\n  data,\n  title = \"Skill Gap Analysis\",\n  description = \"Compare your current skills with target and market requirements\",\n  height = 400,\n  showLegend = true,\n  maxValue = 10,\n}: SkillGapRadarChartProps) {\n  const formatTooltip = (value: number, name: string) => {\n    const labels = {\n      current: 'Current Level',\n      target: 'Target Level',\n      market: 'Market Average',\n    };\n    return [`${value}/10`, labels[name as keyof typeof labels] || name];\n  };\n\n  const formatLabel = (value: string) => {\n    // Truncate long skill names for better display\n    return value.length > 15 ? `${value.substring(0, 12)}...` : value;\n  };\n\n  // Handle empty data gracefully\n  const safeAverage = (values: number[]) => {\n    if (values.length === 0) return 0;\n    return values.reduce((sum, value) => sum + value, 0) / values.length;\n  };\n\n  const averageCurrent = safeAverage(data.map(item => item.current));\n  const averageTarget = safeAverage(data.map(item => item.target));\n  const averageGap = safeAverage(data.map(item => Math.max(0, item.target - item.current)));\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          {title}\n        </CardTitle>\n        {description && (\n          <CardDescription>{description}</CardDescription>\n        )}\n      </CardHeader>\n      <CardContent>\n        <div style={{ width: '100%', height }}>\n          <ResponsiveContainer>\n            <RadarChart data={data} margin={{ top: 20, right: 30, bottom: 20, left: 30 }}>\n              <PolarGrid\n                className=\"stroke-gray-200 dark:stroke-gray-700\"\n                radialLines={true}\n              />\n              <PolarAngleAxis\n                dataKey=\"skill\"\n                tick={{ fontSize: 12, fill: 'currentColor' }}\n                tickFormatter={formatLabel}\n                className=\"fill-gray-600 dark:fill-gray-400\"\n              />\n              <PolarRadiusAxis\n                angle={90}\n                domain={[0, maxValue]}\n                tick={{ fontSize: 10, fill: 'currentColor' }}\n                className=\"fill-gray-500 dark:fill-gray-500\"\n              />\n\n              <Radar\n                name=\"Current Level\"\n                dataKey=\"current\"\n                stroke=\"#3b82f6\"\n                fill=\"#3b82f6\"\n                fillOpacity={0.1}\n                strokeWidth={2}\n              />\n\n              <Radar\n                name=\"Target Level\"\n                dataKey=\"target\"\n                stroke=\"#10b981\"\n                fill=\"#10b981\"\n                fillOpacity={0.1}\n                strokeWidth={2}\n                strokeDasharray=\"5 5\"\n              />\n\n              <Radar\n                name=\"Market Average\"\n                dataKey=\"market\"\n                stroke=\"#f59e0b\"\n                fill=\"#f59e0b\"\n                fillOpacity={0.05}\n                strokeWidth={1}\n                strokeDasharray=\"2 2\"\n              />\n\n              <Tooltip\n                formatter={formatTooltip}\n                contentStyle={{\n                  backgroundColor: 'var(--background)',\n                  border: '1px solid var(--border)',\n                  borderRadius: '6px',\n                  color: 'var(--foreground)',\n                }}\n              />\n\n              {showLegend && (\n                <Legend\n                  verticalAlign=\"bottom\"\n                  height={36}\n                  formatter={(value, entry) => (\n                    <span style={{ color: entry.color }}>{value}</span>\n                  )}\n                />\n              )}\n            </RadarChart>\n          </ResponsiveContainer>\n        </div>\n\n        {/* Skill Gap Summary */}\n        <div className=\"mt-4 grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div className=\"text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n            <div className=\"text-sm font-medium text-blue-700 dark:text-blue-300\">\n              Average Current\n            </div>\n            <div className=\"text-lg font-bold text-blue-900 dark:text-blue-100\">\n              {averageCurrent.toFixed(1)}/10\n            </div>\n          </div>\n\n          <div className=\"text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n            <div className=\"text-sm font-medium text-green-700 dark:text-green-300\">\n              Average Target\n            </div>\n            <div className=\"text-lg font-bold text-green-900 dark:text-green-100\">\n              {averageTarget.toFixed(1)}/10\n            </div>\n          </div>\n\n          <div className=\"text-center p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg\">\n            <div className=\"text-sm font-medium text-amber-700 dark:text-amber-300\">\n              Gap to Close\n            </div>\n            <div className=\"text-lg font-bold text-amber-900 dark:text-amber-100\">\n              {averageGap.toFixed(1)}\n            </div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "7eb79edec65d2de0263b9780f3eff4152d70098b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_206w2db2qd = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_206w2db2qd();
var __importDefault =
/* istanbul ignore next */
(cov_206w2db2qd().s[0]++,
/* istanbul ignore next */
(cov_206w2db2qd().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_206w2db2qd().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_206w2db2qd().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_206w2db2qd().f[0]++;
  cov_206w2db2qd().s[1]++;
  return /* istanbul ignore next */(cov_206w2db2qd().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_206w2db2qd().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_206w2db2qd().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_206w2db2qd().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_206w2db2qd().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_206w2db2qd().s[3]++;
exports.default = SkillGapRadarChart;
var jsx_runtime_1 =
/* istanbul ignore next */
(cov_206w2db2qd().s[4]++, require("react/jsx-runtime"));
var react_1 =
/* istanbul ignore next */
(cov_206w2db2qd().s[5]++, __importDefault(require("react")));
var recharts_1 =
/* istanbul ignore next */
(cov_206w2db2qd().s[6]++, require("recharts"));
var card_1 =
/* istanbul ignore next */
(cov_206w2db2qd().s[7]++, require("@/components/ui/card"));
function SkillGapRadarChart(_a) {
  /* istanbul ignore next */
  cov_206w2db2qd().f[1]++;
  var data =
    /* istanbul ignore next */
    (cov_206w2db2qd().s[8]++, _a.data),
    _b =
    /* istanbul ignore next */
    (cov_206w2db2qd().s[9]++, _a.title),
    title =
    /* istanbul ignore next */
    (cov_206w2db2qd().s[10]++, _b === void 0 ?
    /* istanbul ignore next */
    (cov_206w2db2qd().b[3][0]++, "Skill Gap Analysis") :
    /* istanbul ignore next */
    (cov_206w2db2qd().b[3][1]++, _b)),
    _c =
    /* istanbul ignore next */
    (cov_206w2db2qd().s[11]++, _a.description),
    description =
    /* istanbul ignore next */
    (cov_206w2db2qd().s[12]++, _c === void 0 ?
    /* istanbul ignore next */
    (cov_206w2db2qd().b[4][0]++, "Compare your current skills with target and market requirements") :
    /* istanbul ignore next */
    (cov_206w2db2qd().b[4][1]++, _c)),
    _d =
    /* istanbul ignore next */
    (cov_206w2db2qd().s[13]++, _a.height),
    height =
    /* istanbul ignore next */
    (cov_206w2db2qd().s[14]++, _d === void 0 ?
    /* istanbul ignore next */
    (cov_206w2db2qd().b[5][0]++, 400) :
    /* istanbul ignore next */
    (cov_206w2db2qd().b[5][1]++, _d)),
    _e =
    /* istanbul ignore next */
    (cov_206w2db2qd().s[15]++, _a.showLegend),
    showLegend =
    /* istanbul ignore next */
    (cov_206w2db2qd().s[16]++, _e === void 0 ?
    /* istanbul ignore next */
    (cov_206w2db2qd().b[6][0]++, true) :
    /* istanbul ignore next */
    (cov_206w2db2qd().b[6][1]++, _e)),
    _f =
    /* istanbul ignore next */
    (cov_206w2db2qd().s[17]++, _a.maxValue),
    maxValue =
    /* istanbul ignore next */
    (cov_206w2db2qd().s[18]++, _f === void 0 ?
    /* istanbul ignore next */
    (cov_206w2db2qd().b[7][0]++, 10) :
    /* istanbul ignore next */
    (cov_206w2db2qd().b[7][1]++, _f));
  /* istanbul ignore next */
  cov_206w2db2qd().s[19]++;
  var formatTooltip = function (value, name) {
    /* istanbul ignore next */
    cov_206w2db2qd().f[2]++;
    var labels =
    /* istanbul ignore next */
    (cov_206w2db2qd().s[20]++, {
      current: 'Current Level',
      target: 'Target Level',
      market: 'Market Average'
    });
    /* istanbul ignore next */
    cov_206w2db2qd().s[21]++;
    return ["".concat(value, "/10"),
    /* istanbul ignore next */
    (cov_206w2db2qd().b[8][0]++, labels[name]) ||
    /* istanbul ignore next */
    (cov_206w2db2qd().b[8][1]++, name)];
  };
  /* istanbul ignore next */
  cov_206w2db2qd().s[22]++;
  var formatLabel = function (value) {
    /* istanbul ignore next */
    cov_206w2db2qd().f[3]++;
    cov_206w2db2qd().s[23]++;
    // Truncate long skill names for better display
    return value.length > 15 ?
    /* istanbul ignore next */
    (cov_206w2db2qd().b[9][0]++, "".concat(value.substring(0, 12), "...")) :
    /* istanbul ignore next */
    (cov_206w2db2qd().b[9][1]++, value);
  };
  // Handle empty data gracefully
  /* istanbul ignore next */
  cov_206w2db2qd().s[24]++;
  var safeAverage = function (values) {
    /* istanbul ignore next */
    cov_206w2db2qd().f[4]++;
    cov_206w2db2qd().s[25]++;
    if (values.length === 0) {
      /* istanbul ignore next */
      cov_206w2db2qd().b[10][0]++;
      cov_206w2db2qd().s[26]++;
      return 0;
    } else
    /* istanbul ignore next */
    {
      cov_206w2db2qd().b[10][1]++;
    }
    cov_206w2db2qd().s[27]++;
    return values.reduce(function (sum, value) {
      /* istanbul ignore next */
      cov_206w2db2qd().f[5]++;
      cov_206w2db2qd().s[28]++;
      return sum + value;
    }, 0) / values.length;
  };
  var averageCurrent =
  /* istanbul ignore next */
  (cov_206w2db2qd().s[29]++, safeAverage(data.map(function (item) {
    /* istanbul ignore next */
    cov_206w2db2qd().f[6]++;
    cov_206w2db2qd().s[30]++;
    return item.current;
  })));
  var averageTarget =
  /* istanbul ignore next */
  (cov_206w2db2qd().s[31]++, safeAverage(data.map(function (item) {
    /* istanbul ignore next */
    cov_206w2db2qd().f[7]++;
    cov_206w2db2qd().s[32]++;
    return item.target;
  })));
  var averageGap =
  /* istanbul ignore next */
  (cov_206w2db2qd().s[33]++, safeAverage(data.map(function (item) {
    /* istanbul ignore next */
    cov_206w2db2qd().f[8]++;
    cov_206w2db2qd().s[34]++;
    return Math.max(0, item.target - item.current);
  })));
  /* istanbul ignore next */
  cov_206w2db2qd().s[35]++;
  return (0, jsx_runtime_1.jsxs)(card_1.Card, {
    children: [(0, jsx_runtime_1.jsxs)(card_1.CardHeader, {
      children: [(0, jsx_runtime_1.jsx)(card_1.CardTitle, {
        className: "flex items-center gap-2",
        children: title
      }),
      /* istanbul ignore next */
      (cov_206w2db2qd().b[11][0]++, description) &&
      /* istanbul ignore next */
      (cov_206w2db2qd().b[11][1]++, (0, jsx_runtime_1.jsx)(card_1.CardDescription, {
        children: description
      }))]
    }), (0, jsx_runtime_1.jsxs)(card_1.CardContent, {
      children: [(0, jsx_runtime_1.jsx)("div", {
        style: {
          width: '100%',
          height: height
        },
        children: (0, jsx_runtime_1.jsx)(recharts_1.ResponsiveContainer, {
          children: (0, jsx_runtime_1.jsxs)(recharts_1.RadarChart, {
            data: data,
            margin: {
              top: 20,
              right: 30,
              bottom: 20,
              left: 30
            },
            children: [(0, jsx_runtime_1.jsx)(recharts_1.PolarGrid, {
              className: "stroke-gray-200 dark:stroke-gray-700",
              radialLines: true
            }), (0, jsx_runtime_1.jsx)(recharts_1.PolarAngleAxis, {
              dataKey: "skill",
              tick: {
                fontSize: 12,
                fill: 'currentColor'
              },
              tickFormatter: formatLabel,
              className: "fill-gray-600 dark:fill-gray-400"
            }), (0, jsx_runtime_1.jsx)(recharts_1.PolarRadiusAxis, {
              angle: 90,
              domain: [0, maxValue],
              tick: {
                fontSize: 10,
                fill: 'currentColor'
              },
              className: "fill-gray-500 dark:fill-gray-500"
            }), (0, jsx_runtime_1.jsx)(recharts_1.Radar, {
              name: "Current Level",
              dataKey: "current",
              stroke: "#3b82f6",
              fill: "#3b82f6",
              fillOpacity: 0.1,
              strokeWidth: 2
            }), (0, jsx_runtime_1.jsx)(recharts_1.Radar, {
              name: "Target Level",
              dataKey: "target",
              stroke: "#10b981",
              fill: "#10b981",
              fillOpacity: 0.1,
              strokeWidth: 2,
              strokeDasharray: "5 5"
            }), (0, jsx_runtime_1.jsx)(recharts_1.Radar, {
              name: "Market Average",
              dataKey: "market",
              stroke: "#f59e0b",
              fill: "#f59e0b",
              fillOpacity: 0.05,
              strokeWidth: 1,
              strokeDasharray: "2 2"
            }), (0, jsx_runtime_1.jsx)(recharts_1.Tooltip, {
              formatter: formatTooltip,
              contentStyle: {
                backgroundColor: 'var(--background)',
                border: '1px solid var(--border)',
                borderRadius: '6px',
                color: 'var(--foreground)'
              }
            }),
            /* istanbul ignore next */
            (cov_206w2db2qd().b[12][0]++, showLegend) &&
            /* istanbul ignore next */
            (cov_206w2db2qd().b[12][1]++, (0, jsx_runtime_1.jsx)(recharts_1.Legend, {
              verticalAlign: "bottom",
              height: 36,
              formatter: function (value, entry) {
                /* istanbul ignore next */
                cov_206w2db2qd().f[9]++;
                cov_206w2db2qd().s[36]++;
                return (0, jsx_runtime_1.jsx)("span", {
                  style: {
                    color: entry.color
                  },
                  children: value
                });
              }
            }))]
          })
        })
      }), (0, jsx_runtime_1.jsxs)("div", {
        className: "mt-4 grid grid-cols-1 md:grid-cols-3 gap-4",
        children: [(0, jsx_runtime_1.jsxs)("div", {
          className: "text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",
          children: [(0, jsx_runtime_1.jsx)("div", {
            className: "text-sm font-medium text-blue-700 dark:text-blue-300",
            children: "Average Current"
          }), (0, jsx_runtime_1.jsxs)("div", {
            className: "text-lg font-bold text-blue-900 dark:text-blue-100",
            children: [averageCurrent.toFixed(1), "/10"]
          })]
        }), (0, jsx_runtime_1.jsxs)("div", {
          className: "text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",
          children: [(0, jsx_runtime_1.jsx)("div", {
            className: "text-sm font-medium text-green-700 dark:text-green-300",
            children: "Average Target"
          }), (0, jsx_runtime_1.jsxs)("div", {
            className: "text-lg font-bold text-green-900 dark:text-green-100",
            children: [averageTarget.toFixed(1), "/10"]
          })]
        }), (0, jsx_runtime_1.jsxs)("div", {
          className: "text-center p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg",
          children: [(0, jsx_runtime_1.jsx)("div", {
            className: "text-sm font-medium text-amber-700 dark:text-amber-300",
            children: "Gap to Close"
          }), (0, jsx_runtime_1.jsx)("div", {
            className: "text-lg font-bold text-amber-900 dark:text-amber-100",
            children: averageGap.toFixed(1)
          })]
        })]
      })]
    })]
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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