63190432c14a6b02714996a6c24be0f5
"use strict";
'use client';

/* istanbul ignore next */
function cov_83fadhtc9() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/skills/SkillAssessmentForm.tsx";
  var hash = "3510abe02f2148b19491fbd6131d3877186c6d88";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/skills/SkillAssessmentForm.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 15
        },
        end: {
          line: 13,
          column: 1
        }
      },
      "1": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 11,
          column: 6
        }
      },
      "2": {
        start: {
          line: 5,
          column: 8
        },
        end: {
          line: 9,
          column: 9
        }
      },
      "3": {
        start: {
          line: 5,
          column: 24
        },
        end: {
          line: 5,
          column: 25
        }
      },
      "4": {
        start: {
          line: 5,
          column: 31
        },
        end: {
          line: 5,
          column: 47
        }
      },
      "5": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 6,
          column: 29
        }
      },
      "6": {
        start: {
          line: 7,
          column: 12
        },
        end: {
          line: 8,
          column: 28
        }
      },
      "7": {
        start: {
          line: 7,
          column: 29
        },
        end: {
          line: 8,
          column: 28
        }
      },
      "8": {
        start: {
          line: 8,
          column: 16
        },
        end: {
          line: 8,
          column: 28
        }
      },
      "9": {
        start: {
          line: 10,
          column: 8
        },
        end: {
          line: 10,
          column: 17
        }
      },
      "10": {
        start: {
          line: 12,
          column: 4
        },
        end: {
          line: 12,
          column: 43
        }
      },
      "11": {
        start: {
          line: 14,
          column: 22
        },
        end: {
          line: 24,
          column: 3
        }
      },
      "12": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "13": {
        start: {
          line: 15,
          column: 26
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "14": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 52
        }
      },
      "15": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 19,
          column: 5
        }
      },
      "16": {
        start: {
          line: 18,
          column: 6
        },
        end: {
          line: 18,
          column: 68
        }
      },
      "17": {
        start: {
          line: 18,
          column: 51
        },
        end: {
          line: 18,
          column: 63
        }
      },
      "18": {
        start: {
          line: 20,
          column: 4
        },
        end: {
          line: 20,
          column: 39
        }
      },
      "19": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 33
        }
      },
      "20": {
        start: {
          line: 22,
          column: 26
        },
        end: {
          line: 22,
          column: 33
        }
      },
      "21": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 23,
          column: 17
        }
      },
      "22": {
        start: {
          line: 25,
          column: 25
        },
        end: {
          line: 29,
          column: 2
        }
      },
      "23": {
        start: {
          line: 26,
          column: 4
        },
        end: {
          line: 26,
          column: 72
        }
      },
      "24": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 28,
          column: 21
        }
      },
      "25": {
        start: {
          line: 30,
          column: 19
        },
        end: {
          line: 46,
          column: 4
        }
      },
      "26": {
        start: {
          line: 31,
          column: 18
        },
        end: {
          line: 38,
          column: 5
        }
      },
      "27": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 36,
          column: 10
        }
      },
      "28": {
        start: {
          line: 33,
          column: 21
        },
        end: {
          line: 33,
          column: 23
        }
      },
      "29": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 95
        }
      },
      "30": {
        start: {
          line: 34,
          column: 29
        },
        end: {
          line: 34,
          column: 95
        }
      },
      "31": {
        start: {
          line: 34,
          column: 77
        },
        end: {
          line: 34,
          column: 95
        }
      },
      "32": {
        start: {
          line: 35,
          column: 12
        },
        end: {
          line: 35,
          column: 22
        }
      },
      "33": {
        start: {
          line: 37,
          column: 8
        },
        end: {
          line: 37,
          column: 26
        }
      },
      "34": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 45,
          column: 6
        }
      },
      "35": {
        start: {
          line: 40,
          column: 8
        },
        end: {
          line: 40,
          column: 46
        }
      },
      "36": {
        start: {
          line: 40,
          column: 35
        },
        end: {
          line: 40,
          column: 46
        }
      },
      "37": {
        start: {
          line: 41,
          column: 21
        },
        end: {
          line: 41,
          column: 23
        }
      },
      "38": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 42,
          column: 137
        }
      },
      "39": {
        start: {
          line: 42,
          column: 25
        },
        end: {
          line: 42,
          column: 137
        }
      },
      "40": {
        start: {
          line: 42,
          column: 38
        },
        end: {
          line: 42,
          column: 50
        }
      },
      "41": {
        start: {
          line: 42,
          column: 56
        },
        end: {
          line: 42,
          column: 57
        }
      },
      "42": {
        start: {
          line: 42,
          column: 78
        },
        end: {
          line: 42,
          column: 137
        }
      },
      "43": {
        start: {
          line: 42,
          column: 102
        },
        end: {
          line: 42,
          column: 137
        }
      },
      "44": {
        start: {
          line: 43,
          column: 8
        },
        end: {
          line: 43,
          column: 40
        }
      },
      "45": {
        start: {
          line: 44,
          column: 8
        },
        end: {
          line: 44,
          column: 22
        }
      },
      "46": {
        start: {
          line: 47,
          column: 16
        },
        end: {
          line: 55,
          column: 1
        }
      },
      "47": {
        start: {
          line: 48,
          column: 28
        },
        end: {
          line: 48,
          column: 110
        }
      },
      "48": {
        start: {
          line: 48,
          column: 91
        },
        end: {
          line: 48,
          column: 106
        }
      },
      "49": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 54,
          column: 7
        }
      },
      "50": {
        start: {
          line: 50,
          column: 36
        },
        end: {
          line: 50,
          column: 97
        }
      },
      "51": {
        start: {
          line: 50,
          column: 42
        },
        end: {
          line: 50,
          column: 70
        }
      },
      "52": {
        start: {
          line: 50,
          column: 85
        },
        end: {
          line: 50,
          column: 95
        }
      },
      "53": {
        start: {
          line: 51,
          column: 35
        },
        end: {
          line: 51,
          column: 100
        }
      },
      "54": {
        start: {
          line: 51,
          column: 41
        },
        end: {
          line: 51,
          column: 73
        }
      },
      "55": {
        start: {
          line: 51,
          column: 88
        },
        end: {
          line: 51,
          column: 98
        }
      },
      "56": {
        start: {
          line: 52,
          column: 32
        },
        end: {
          line: 52,
          column: 116
        }
      },
      "57": {
        start: {
          line: 53,
          column: 8
        },
        end: {
          line: 53,
          column: 78
        }
      },
      "58": {
        start: {
          line: 56,
          column: 18
        },
        end: {
          line: 82,
          column: 1
        }
      },
      "59": {
        start: {
          line: 57,
          column: 12
        },
        end: {
          line: 57,
          column: 104
        }
      },
      "60": {
        start: {
          line: 57,
          column: 43
        },
        end: {
          line: 57,
          column: 68
        }
      },
      "61": {
        start: {
          line: 57,
          column: 57
        },
        end: {
          line: 57,
          column: 68
        }
      },
      "62": {
        start: {
          line: 57,
          column: 69
        },
        end: {
          line: 57,
          column: 81
        }
      },
      "63": {
        start: {
          line: 57,
          column: 119
        },
        end: {
          line: 57,
          column: 196
        }
      },
      "64": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 58,
          column: 160
        }
      },
      "65": {
        start: {
          line: 58,
          column: 141
        },
        end: {
          line: 58,
          column: 153
        }
      },
      "66": {
        start: {
          line: 59,
          column: 23
        },
        end: {
          line: 59,
          column: 68
        }
      },
      "67": {
        start: {
          line: 59,
          column: 45
        },
        end: {
          line: 59,
          column: 65
        }
      },
      "68": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 61,
          column: 70
        }
      },
      "69": {
        start: {
          line: 61,
          column: 15
        },
        end: {
          line: 61,
          column: 70
        }
      },
      "70": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 79,
          column: 66
        }
      },
      "71": {
        start: {
          line: 62,
          column: 50
        },
        end: {
          line: 79,
          column: 66
        }
      },
      "72": {
        start: {
          line: 63,
          column: 12
        },
        end: {
          line: 63,
          column: 169
        }
      },
      "73": {
        start: {
          line: 63,
          column: 160
        },
        end: {
          line: 63,
          column: 169
        }
      },
      "74": {
        start: {
          line: 64,
          column: 12
        },
        end: {
          line: 64,
          column: 52
        }
      },
      "75": {
        start: {
          line: 64,
          column: 26
        },
        end: {
          line: 64,
          column: 52
        }
      },
      "76": {
        start: {
          line: 65,
          column: 12
        },
        end: {
          line: 77,
          column: 13
        }
      },
      "77": {
        start: {
          line: 66,
          column: 32
        },
        end: {
          line: 66,
          column: 39
        }
      },
      "78": {
        start: {
          line: 66,
          column: 40
        },
        end: {
          line: 66,
          column: 46
        }
      },
      "79": {
        start: {
          line: 67,
          column: 24
        },
        end: {
          line: 67,
          column: 34
        }
      },
      "80": {
        start: {
          line: 67,
          column: 35
        },
        end: {
          line: 67,
          column: 72
        }
      },
      "81": {
        start: {
          line: 68,
          column: 24
        },
        end: {
          line: 68,
          column: 34
        }
      },
      "82": {
        start: {
          line: 68,
          column: 35
        },
        end: {
          line: 68,
          column: 45
        }
      },
      "83": {
        start: {
          line: 68,
          column: 46
        },
        end: {
          line: 68,
          column: 55
        }
      },
      "84": {
        start: {
          line: 68,
          column: 56
        },
        end: {
          line: 68,
          column: 65
        }
      },
      "85": {
        start: {
          line: 69,
          column: 24
        },
        end: {
          line: 69,
          column: 41
        }
      },
      "86": {
        start: {
          line: 69,
          column: 42
        },
        end: {
          line: 69,
          column: 55
        }
      },
      "87": {
        start: {
          line: 69,
          column: 56
        },
        end: {
          line: 69,
          column: 65
        }
      },
      "88": {
        start: {
          line: 71,
          column: 20
        },
        end: {
          line: 71,
          column: 128
        }
      },
      "89": {
        start: {
          line: 71,
          column: 110
        },
        end: {
          line: 71,
          column: 116
        }
      },
      "90": {
        start: {
          line: 71,
          column: 117
        },
        end: {
          line: 71,
          column: 126
        }
      },
      "91": {
        start: {
          line: 72,
          column: 20
        },
        end: {
          line: 72,
          column: 106
        }
      },
      "92": {
        start: {
          line: 72,
          column: 81
        },
        end: {
          line: 72,
          column: 97
        }
      },
      "93": {
        start: {
          line: 72,
          column: 98
        },
        end: {
          line: 72,
          column: 104
        }
      },
      "94": {
        start: {
          line: 73,
          column: 20
        },
        end: {
          line: 73,
          column: 89
        }
      },
      "95": {
        start: {
          line: 73,
          column: 57
        },
        end: {
          line: 73,
          column: 72
        }
      },
      "96": {
        start: {
          line: 73,
          column: 73
        },
        end: {
          line: 73,
          column: 80
        }
      },
      "97": {
        start: {
          line: 73,
          column: 81
        },
        end: {
          line: 73,
          column: 87
        }
      },
      "98": {
        start: {
          line: 74,
          column: 20
        },
        end: {
          line: 74,
          column: 87
        }
      },
      "99": {
        start: {
          line: 74,
          column: 47
        },
        end: {
          line: 74,
          column: 62
        }
      },
      "100": {
        start: {
          line: 74,
          column: 63
        },
        end: {
          line: 74,
          column: 78
        }
      },
      "101": {
        start: {
          line: 74,
          column: 79
        },
        end: {
          line: 74,
          column: 85
        }
      },
      "102": {
        start: {
          line: 75,
          column: 20
        },
        end: {
          line: 75,
          column: 42
        }
      },
      "103": {
        start: {
          line: 75,
          column: 30
        },
        end: {
          line: 75,
          column: 42
        }
      },
      "104": {
        start: {
          line: 76,
          column: 20
        },
        end: {
          line: 76,
          column: 33
        }
      },
      "105": {
        start: {
          line: 76,
          column: 34
        },
        end: {
          line: 76,
          column: 43
        }
      },
      "106": {
        start: {
          line: 78,
          column: 12
        },
        end: {
          line: 78,
          column: 39
        }
      },
      "107": {
        start: {
          line: 79,
          column: 22
        },
        end: {
          line: 79,
          column: 34
        }
      },
      "108": {
        start: {
          line: 79,
          column: 35
        },
        end: {
          line: 79,
          column: 41
        }
      },
      "109": {
        start: {
          line: 79,
          column: 54
        },
        end: {
          line: 79,
          column: 64
        }
      },
      "110": {
        start: {
          line: 80,
          column: 8
        },
        end: {
          line: 80,
          column: 35
        }
      },
      "111": {
        start: {
          line: 80,
          column: 23
        },
        end: {
          line: 80,
          column: 35
        }
      },
      "112": {
        start: {
          line: 80,
          column: 36
        },
        end: {
          line: 80,
          column: 89
        }
      },
      "113": {
        start: {
          line: 83,
          column: 20
        },
        end: {
          line: 91,
          column: 1
        }
      },
      "114": {
        start: {
          line: 84,
          column: 4
        },
        end: {
          line: 89,
          column: 5
        }
      },
      "115": {
        start: {
          line: 84,
          column: 40
        },
        end: {
          line: 89,
          column: 5
        }
      },
      "116": {
        start: {
          line: 84,
          column: 53
        },
        end: {
          line: 84,
          column: 54
        }
      },
      "117": {
        start: {
          line: 84,
          column: 60
        },
        end: {
          line: 84,
          column: 71
        }
      },
      "118": {
        start: {
          line: 85,
          column: 8
        },
        end: {
          line: 88,
          column: 9
        }
      },
      "119": {
        start: {
          line: 86,
          column: 12
        },
        end: {
          line: 86,
          column: 65
        }
      },
      "120": {
        start: {
          line: 86,
          column: 21
        },
        end: {
          line: 86,
          column: 65
        }
      },
      "121": {
        start: {
          line: 87,
          column: 12
        },
        end: {
          line: 87,
          column: 28
        }
      },
      "122": {
        start: {
          line: 90,
          column: 4
        },
        end: {
          line: 90,
          column: 61
        }
      },
      "123": {
        start: {
          line: 92,
          column: 22
        },
        end: {
          line: 94,
          column: 1
        }
      },
      "124": {
        start: {
          line: 93,
          column: 4
        },
        end: {
          line: 93,
          column: 62
        }
      },
      "125": {
        start: {
          line: 95,
          column: 0
        },
        end: {
          line: 95,
          column: 62
        }
      },
      "126": {
        start: {
          line: 96,
          column: 0
        },
        end: {
          line: 96,
          column: 38
        }
      },
      "127": {
        start: {
          line: 97,
          column: 20
        },
        end: {
          line: 97,
          column: 48
        }
      },
      "128": {
        start: {
          line: 98,
          column: 14
        },
        end: {
          line: 98,
          column: 44
        }
      },
      "129": {
        start: {
          line: 99,
          column: 13
        },
        end: {
          line: 99,
          column: 44
        }
      },
      "130": {
        start: {
          line: 100,
          column: 15
        },
        end: {
          line: 100,
          column: 48
        }
      },
      "131": {
        start: {
          line: 101,
          column: 14
        },
        end: {
          line: 101,
          column: 46
        }
      },
      "132": {
        start: {
          line: 102,
          column: 14
        },
        end: {
          line: 102,
          column: 46
        }
      },
      "133": {
        start: {
          line: 103,
          column: 17
        },
        end: {
          line: 103,
          column: 52
        }
      },
      "134": {
        start: {
          line: 104,
          column: 15
        },
        end: {
          line: 104,
          column: 48
        }
      },
      "135": {
        start: {
          line: 105,
          column: 14
        },
        end: {
          line: 105,
          column: 46
        }
      },
      "136": {
        start: {
          line: 106,
          column: 17
        },
        end: {
          line: 106,
          column: 52
        }
      },
      "137": {
        start: {
          line: 107,
          column: 21
        },
        end: {
          line: 107,
          column: 44
        }
      },
      "138": {
        start: {
          line: 108,
          column: 15
        },
        end: {
          line: 108,
          column: 32
        }
      },
      "139": {
        start: {
          line: 109,
          column: 30
        },
        end: {
          line: 109,
          column: 99
        }
      },
      "140": {
        start: {
          line: 111,
          column: 16
        },
        end: {
          line: 111,
          column: 20
        }
      },
      "141": {
        start: {
          line: 112,
          column: 19
        },
        end: {
          line: 112,
          column: 30
        }
      },
      "142": {
        start: {
          line: 112,
          column: 48
        },
        end: {
          line: 112,
          column: 64
        }
      },
      "143": {
        start: {
          line: 112,
          column: 71
        },
        end: {
          line: 112,
          column: 92
        }
      },
      "144": {
        start: {
          line: 112,
          column: 115
        },
        end: {
          line: 112,
          column: 138
        }
      },
      "145": {
        start: {
          line: 112,
          column: 145
        },
        end: {
          line: 112,
          column: 152
        }
      },
      "146": {
        start: {
          line: 112,
          column: 161
        },
        end: {
          line: 112,
          column: 190
        }
      },
      "147": {
        start: {
          line: 112,
          column: 197
        },
        end: {
          line: 112,
          column: 214
        }
      },
      "148": {
        start: {
          line: 112,
          column: 233
        },
        end: {
          line: 112,
          column: 256
        }
      },
      "149": {
        start: {
          line: 112,
          column: 263
        },
        end: {
          line: 112,
          column: 287
        }
      },
      "150": {
        start: {
          line: 112,
          column: 313
        },
        end: {
          line: 112,
          column: 339
        }
      },
      "151": {
        start: {
          line: 112,
          column: 363
        },
        end: {
          line: 112,
          column: 385
        }
      },
      "152": {
        start: {
          line: 113,
          column: 13
        },
        end: {
          line: 113,
          column: 114
        }
      },
      "153": {
        start: {
          line: 113,
          column: 130
        },
        end: {
          line: 113,
          column: 135
        }
      },
      "154": {
        start: {
          line: 113,
          column: 154
        },
        end: {
          line: 113,
          column: 159
        }
      },
      "155": {
        start: {
          line: 114,
          column: 13
        },
        end: {
          line: 114,
          column: 40
        }
      },
      "156": {
        start: {
          line: 114,
          column: 59
        },
        end: {
          line: 114,
          column: 64
        }
      },
      "157": {
        start: {
          line: 114,
          column: 86
        },
        end: {
          line: 114,
          column: 91
        }
      },
      "158": {
        start: {
          line: 116,
          column: 4
        },
        end: {
          line: 120,
          column: 29
        }
      },
      "159": {
        start: {
          line: 117,
          column: 8
        },
        end: {
          line: 119,
          column: 9
        }
      },
      "160": {
        start: {
          line: 118,
          column: 12
        },
        end: {
          line: 118,
          column: 47
        }
      },
      "161": {
        start: {
          line: 122,
          column: 28
        },
        end: {
          line: 127,
          column: 29
        }
      },
      "162": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 123,
          column: 39
        }
      },
      "163": {
        start: {
          line: 124,
          column: 8
        },
        end: {
          line: 126,
          column: 9
        }
      },
      "164": {
        start: {
          line: 125,
          column: 12
        },
        end: {
          line: 125,
          column: 48
        }
      },
      "165": {
        start: {
          line: 128,
          column: 13
        },
        end: {
          line: 128,
          column: 41
        }
      },
      "166": {
        start: {
          line: 128,
          column: 58
        },
        end: {
          line: 128,
          column: 63
        }
      },
      "167": {
        start: {
          line: 128,
          column: 83
        },
        end: {
          line: 128,
          column: 88
        }
      },
      "168": {
        start: {
          line: 129,
          column: 13
        },
        end: {
          line: 129,
          column: 38
        }
      },
      "169": {
        start: {
          line: 129,
          column: 54
        },
        end: {
          line: 129,
          column: 59
        }
      },
      "170": {
        start: {
          line: 129,
          column: 78
        },
        end: {
          line: 129,
          column: 83
        }
      },
      "171": {
        start: {
          line: 130,
          column: 13
        },
        end: {
          line: 130,
          column: 38
        }
      },
      "172": {
        start: {
          line: 130,
          column: 56
        },
        end: {
          line: 130,
          column: 61
        }
      },
      "173": {
        start: {
          line: 130,
          column: 82
        },
        end: {
          line: 130,
          column: 87
        }
      },
      "174": {
        start: {
          line: 131,
          column: 13
        },
        end: {
          line: 131,
          column: 41
        }
      },
      "175": {
        start: {
          line: 131,
          column: 57
        },
        end: {
          line: 131,
          column: 62
        }
      },
      "176": {
        start: {
          line: 131,
          column: 81
        },
        end: {
          line: 131,
          column: 86
        }
      },
      "177": {
        start: {
          line: 132,
          column: 13
        },
        end: {
          line: 132,
          column: 38
        }
      },
      "178": {
        start: {
          line: 132,
          column: 49
        },
        end: {
          line: 132,
          column: 54
        }
      },
      "179": {
        start: {
          line: 132,
          column: 68
        },
        end: {
          line: 132,
          column: 73
        }
      },
      "180": {
        start: {
          line: 134,
          column: 8
        },
        end: {
          line: 144,
          column: 10
        }
      },
      "181": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 180,
          column: 37
        }
      },
      "182": {
        start: {
          line: 148,
          column: 27
        },
        end: {
          line: 177,
          column: 13
        }
      },
      "183": {
        start: {
          line: 148,
          column: 41
        },
        end: {
          line: 177,
          column: 11
        }
      },
      "184": {
        start: {
          line: 150,
          column: 12
        },
        end: {
          line: 176,
          column: 15
        }
      },
      "185": {
        start: {
          line: 151,
          column: 16
        },
        end: {
          line: 175,
          column: 17
        }
      },
      "186": {
        start: {
          line: 153,
          column: 24
        },
        end: {
          line: 156,
          column: 25
        }
      },
      "187": {
        start: {
          line: 154,
          column: 28
        },
        end: {
          line: 154,
          column: 49
        }
      },
      "188": {
        start: {
          line: 155,
          column: 28
        },
        end: {
          line: 155,
          column: 50
        }
      },
      "189": {
        start: {
          line: 157,
          column: 24
        },
        end: {
          line: 157,
          column: 45
        }
      },
      "190": {
        start: {
          line: 158,
          column: 24
        },
        end: {
          line: 158,
          column: 37
        }
      },
      "191": {
        start: {
          line: 160,
          column: 24
        },
        end: {
          line: 160,
          column: 51
        }
      },
      "192": {
        start: {
          line: 161,
          column: 24
        },
        end: {
          line: 161,
          column: 73
        }
      },
      "193": {
        start: {
          line: 163,
          column: 24
        },
        end: {
          line: 163,
          column: 44
        }
      },
      "194": {
        start: {
          line: 164,
          column: 24
        },
        end: {
          line: 164,
          column: 50
        }
      },
      "195": {
        start: {
          line: 165,
          column: 24
        },
        end: {
          line: 165,
          column: 48
        }
      },
      "196": {
        start: {
          line: 167,
          column: 24
        },
        end: {
          line: 167,
          column: 44
        }
      },
      "197": {
        start: {
          line: 168,
          column: 24
        },
        end: {
          line: 168,
          column: 74
        }
      },
      "198": {
        start: {
          line: 169,
          column: 24
        },
        end: {
          line: 169,
          column: 72
        }
      },
      "199": {
        start: {
          line: 170,
          column: 24
        },
        end: {
          line: 170,
          column: 48
        }
      },
      "200": {
        start: {
          line: 172,
          column: 24
        },
        end: {
          line: 172,
          column: 46
        }
      },
      "201": {
        start: {
          line: 173,
          column: 24
        },
        end: {
          line: 173,
          column: 50
        }
      },
      "202": {
        start: {
          line: 174,
          column: 28
        },
        end: {
          line: 174,
          column: 50
        }
      },
      "203": {
        start: {
          line: 178,
          column: 28
        },
        end: {
          line: 178,
          column: 57
        }
      },
      "204": {
        start: {
          line: 179,
          column: 8
        },
        end: {
          line: 179,
          column: 67
        }
      },
      "205": {
        start: {
          line: 179,
          column: 29
        },
        end: {
          line: 179,
          column: 64
        }
      },
      "206": {
        start: {
          line: 181,
          column: 24
        },
        end: {
          line: 187,
          column: 63
        }
      },
      "207": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 185,
          column: 9
        }
      },
      "208": {
        start: {
          line: 183,
          column: 12
        },
        end: {
          line: 183,
          column: 92
        }
      },
      "209": {
        start: {
          line: 184,
          column: 12
        },
        end: {
          line: 184,
          column: 19
        }
      },
      "210": {
        start: {
          line: 186,
          column: 8
        },
        end: {
          line: 186,
          column: 113
        }
      },
      "211": {
        start: {
          line: 188,
          column: 27
        },
        end: {
          line: 194,
          column: 47
        }
      },
      "212": {
        start: {
          line: 189,
          column: 8
        },
        end: {
          line: 192,
          column: 9
        }
      },
      "213": {
        start: {
          line: 190,
          column: 12
        },
        end: {
          line: 190,
          column: 72
        }
      },
      "214": {
        start: {
          line: 191,
          column: 12
        },
        end: {
          line: 191,
          column: 19
        }
      },
      "215": {
        start: {
          line: 193,
          column: 8
        },
        end: {
          line: 193,
          column: 87
        }
      },
      "216": {
        start: {
          line: 193,
          column: 63
        },
        end: {
          line: 193,
          column: 82
        }
      },
      "217": {
        start: {
          line: 195,
          column: 22
        },
        end: {
          line: 215,
          column: 42
        }
      },
      "218": {
        start: {
          line: 197,
          column: 8
        },
        end: {
          line: 200,
          column: 9
        }
      },
      "219": {
        start: {
          line: 198,
          column: 12
        },
        end: {
          line: 198,
          column: 84
        }
      },
      "220": {
        start: {
          line: 199,
          column: 12
        },
        end: {
          line: 199,
          column: 19
        }
      },
      "221": {
        start: {
          line: 202,
          column: 29
        },
        end: {
          line: 202,
          column: 65
        }
      },
      "222": {
        start: {
          line: 203,
          column: 8
        },
        end: {
          line: 205,
          column: 105
        }
      },
      "223": {
        start: {
          line: 207,
          column: 8
        },
        end: {
          line: 207,
          column: 39
        }
      },
      "224": {
        start: {
          line: 208,
          column: 8
        },
        end: {
          line: 210,
          column: 9
        }
      },
      "225": {
        start: {
          line: 209,
          column: 12
        },
        end: {
          line: 209,
          column: 48
        }
      },
      "226": {
        start: {
          line: 211,
          column: 8
        },
        end: {
          line: 211,
          column: 27
        }
      },
      "227": {
        start: {
          line: 212,
          column: 8
        },
        end: {
          line: 212,
          column: 29
        }
      },
      "228": {
        start: {
          line: 214,
          column: 8
        },
        end: {
          line: 214,
          column: 108
        }
      },
      "229": {
        start: {
          line: 216,
          column: 27
        },
        end: {
          line: 230,
          column: 48
        }
      },
      "230": {
        start: {
          line: 218,
          column: 22
        },
        end: {
          line: 218,
          column: 58
        }
      },
      "231": {
        start: {
          line: 219,
          column: 8
        },
        end: {
          line: 219,
          column: 98
        }
      },
      "232": {
        start: {
          line: 220,
          column: 8
        },
        end: {
          line: 220,
          column: 35
        }
      },
      "233": {
        start: {
          line: 222,
          column: 23
        },
        end: {
          line: 222,
          column: 58
        }
      },
      "234": {
        start: {
          line: 223,
          column: 8
        },
        end: {
          line: 229,
          column: 9
        }
      },
      "235": {
        start: {
          line: 224,
          column: 12
        },
        end: {
          line: 228,
          column: 15
        }
      },
      "236": {
        start: {
          line: 225,
          column: 32
        },
        end: {
          line: 225,
          column: 50
        }
      },
      "237": {
        start: {
          line: 226,
          column: 16
        },
        end: {
          line: 226,
          column: 43
        }
      },
      "238": {
        start: {
          line: 227,
          column: 16
        },
        end: {
          line: 227,
          column: 33
        }
      },
      "239": {
        start: {
          line: 231,
          column: 30
        },
        end: {
          line: 249,
          column: 21
        }
      },
      "240": {
        start: {
          line: 232,
          column: 24
        },
        end: {
          line: 232,
          column: 26
        }
      },
      "241": {
        start: {
          line: 233,
          column: 8
        },
        end: {
          line: 246,
          column: 11
        }
      },
      "242": {
        start: {
          line: 234,
          column: 12
        },
        end: {
          line: 236,
          column: 13
        }
      },
      "243": {
        start: {
          line: 235,
          column: 16
        },
        end: {
          line: 235,
          column: 85
        }
      },
      "244": {
        start: {
          line: 237,
          column: 12
        },
        end: {
          line: 239,
          column: 13
        }
      },
      "245": {
        start: {
          line: 238,
          column: 16
        },
        end: {
          line: 238,
          column: 95
        }
      },
      "246": {
        start: {
          line: 240,
          column: 12
        },
        end: {
          line: 242,
          column: 13
        }
      },
      "247": {
        start: {
          line: 241,
          column: 16
        },
        end: {
          line: 241,
          column: 104
        }
      },
      "248": {
        start: {
          line: 243,
          column: 12
        },
        end: {
          line: 245,
          column: 13
        }
      },
      "249": {
        start: {
          line: 244,
          column: 16
        },
        end: {
          line: 244,
          column: 109
        }
      },
      "250": {
        start: {
          line: 247,
          column: 8
        },
        end: {
          line: 247,
          column: 29
        }
      },
      "251": {
        start: {
          line: 248,
          column: 8
        },
        end: {
          line: 248,
          column: 51
        }
      },
      "252": {
        start: {
          line: 250,
          column: 23
        },
        end: {
          line: 315,
          column: 104
        }
      },
      "253": {
        start: {
          line: 250,
          column: 63
        },
        end: {
          line: 315,
          column: 7
        }
      },
      "254": {
        start: {
          line: 252,
          column: 8
        },
        end: {
          line: 314,
          column: 11
        }
      },
      "255": {
        start: {
          line: 253,
          column: 12
        },
        end: {
          line: 313,
          column: 13
        }
      },
      "256": {
        start: {
          line: 255,
          column: 20
        },
        end: {
          line: 255,
          column: 39
        }
      },
      "257": {
        start: {
          line: 256,
          column: 20
        },
        end: {
          line: 259,
          column: 21
        }
      },
      "258": {
        start: {
          line: 257,
          column: 24
        },
        end: {
          line: 257,
          column: 81
        }
      },
      "259": {
        start: {
          line: 258,
          column: 24
        },
        end: {
          line: 258,
          column: 46
        }
      },
      "260": {
        start: {
          line: 260,
          column: 20
        },
        end: {
          line: 260,
          column: 42
        }
      },
      "261": {
        start: {
          line: 261,
          column: 20
        },
        end: {
          line: 261,
          column: 44
        }
      },
      "262": {
        start: {
          line: 262,
          column: 20
        },
        end: {
          line: 262,
          column: 33
        }
      },
      "263": {
        start: {
          line: 264,
          column: 20
        },
        end: {
          line: 264,
          column: 47
        }
      },
      "264": {
        start: {
          line: 265,
          column: 20
        },
        end: {
          line: 265,
          column: 64
        }
      },
      "265": {
        start: {
          line: 267,
          column: 20
        },
        end: {
          line: 267,
          column: 39
        }
      },
      "266": {
        start: {
          line: 269,
          column: 20
        },
        end: {
          line: 296,
          column: 21
        }
      },
      "267": {
        start: {
          line: 270,
          column: 24
        },
        end: {
          line: 270,
          column: 50
        }
      },
      "268": {
        start: {
          line: 271,
          column: 24
        },
        end: {
          line: 285,
          column: 25
        }
      },
      "269": {
        start: {
          line: 273,
          column: 28
        },
        end: {
          line: 280,
          column: 29
        }
      },
      "270": {
        start: {
          line: 274,
          column: 32
        },
        end: {
          line: 274,
          column: 77
        }
      },
      "271": {
        start: {
          line: 275,
          column: 32
        },
        end: {
          line: 275,
          column: 99
        }
      },
      "272": {
        start: {
          line: 279,
          column: 32
        },
        end: {
          line: 279,
          column: 124
        }
      },
      "273": {
        start: {
          line: 284,
          column: 28
        },
        end: {
          line: 284,
          column: 97
        }
      },
      "274": {
        start: {
          line: 289,
          column: 24
        },
        end: {
          line: 295,
          column: 25
        }
      },
      "275": {
        start: {
          line: 290,
          column: 28
        },
        end: {
          line: 290,
          column: 73
        }
      },
      "276": {
        start: {
          line: 291,
          column: 28
        },
        end: {
          line: 291,
          column: 95
        }
      },
      "277": {
        start: {
          line: 294,
          column: 28
        },
        end: {
          line: 294,
          column: 120
        }
      },
      "278": {
        start: {
          line: 297,
          column: 20
        },
        end: {
          line: 297,
          column: 44
        }
      },
      "279": {
        start: {
          line: 299,
          column: 20
        },
        end: {
          line: 299,
          column: 40
        }
      },
      "280": {
        start: {
          line: 300,
          column: 20
        },
        end: {
          line: 300,
          column: 76
        }
      },
      "281": {
        start: {
          line: 301,
          column: 20
        },
        end: {
          line: 306,
          column: 23
        }
      },
      "282": {
        start: {
          line: 307,
          column: 20
        },
        end: {
          line: 307,
          column: 73
        }
      },
      "283": {
        start: {
          line: 308,
          column: 20
        },
        end: {
          line: 308,
          column: 44
        }
      },
      "284": {
        start: {
          line: 310,
          column: 20
        },
        end: {
          line: 310,
          column: 43
        }
      },
      "285": {
        start: {
          line: 311,
          column: 20
        },
        end: {
          line: 311,
          column: 46
        }
      },
      "286": {
        start: {
          line: 312,
          column: 24
        },
        end: {
          line: 312,
          column: 46
        }
      },
      "287": {
        start: {
          line: 316,
          column: 25
        },
        end: {
          line: 326,
          column: 10
        }
      },
      "288": {
        start: {
          line: 317,
          column: 8
        },
        end: {
          line: 318,
          column: 30
        }
      },
      "289": {
        start: {
          line: 318,
          column: 12
        },
        end: {
          line: 318,
          column: 30
        }
      },
      "290": {
        start: {
          line: 319,
          column: 8
        },
        end: {
          line: 320,
          column: 27
        }
      },
      "291": {
        start: {
          line: 320,
          column: 12
        },
        end: {
          line: 320,
          column: 27
        }
      },
      "292": {
        start: {
          line: 321,
          column: 8
        },
        end: {
          line: 322,
          column: 34
        }
      },
      "293": {
        start: {
          line: 322,
          column: 12
        },
        end: {
          line: 322,
          column: 34
        }
      },
      "294": {
        start: {
          line: 323,
          column: 8
        },
        end: {
          line: 324,
          column: 30
        }
      },
      "295": {
        start: {
          line: 324,
          column: 12
        },
        end: {
          line: 324,
          column: 30
        }
      },
      "296": {
        start: {
          line: 325,
          column: 8
        },
        end: {
          line: 325,
          column: 24
        }
      },
      "297": {
        start: {
          line: 327,
          column: 25
        },
        end: {
          line: 337,
          column: 10
        }
      },
      "298": {
        start: {
          line: 328,
          column: 8
        },
        end: {
          line: 329,
          column: 32
        }
      },
      "299": {
        start: {
          line: 329,
          column: 12
        },
        end: {
          line: 329,
          column: 32
        }
      },
      "300": {
        start: {
          line: 330,
          column: 8
        },
        end: {
          line: 331,
          column: 35
        }
      },
      "301": {
        start: {
          line: 331,
          column: 12
        },
        end: {
          line: 331,
          column: 35
        }
      },
      "302": {
        start: {
          line: 332,
          column: 8
        },
        end: {
          line: 333,
          column: 35
        }
      },
      "303": {
        start: {
          line: 333,
          column: 12
        },
        end: {
          line: 333,
          column: 35
        }
      },
      "304": {
        start: {
          line: 334,
          column: 8
        },
        end: {
          line: 335,
          column: 33
        }
      },
      "305": {
        start: {
          line: 335,
          column: 12
        },
        end: {
          line: 335,
          column: 33
        }
      },
      "306": {
        start: {
          line: 336,
          column: 8
        },
        end: {
          line: 336,
          column: 30
        }
      },
      "307": {
        start: {
          line: 338,
          column: 4
        },
        end: {
          line: 360,
          column: 6061
        }
      },
      "308": {
        start: {
          line: 339,
          column: 20
        },
        end: {
          line: 339,
          column: 44
        }
      },
      "309": {
        start: {
          line: 341,
          column: 20
        },
        end: {
          line: 341,
          column: 54
        }
      },
      "310": {
        start: {
          line: 344,
          column: 20
        },
        end: {
          line: 349,
          column: 21
        }
      },
      "311": {
        start: {
          line: 346,
          column: 44
        },
        end: {
          line: 346,
          column: 67
        }
      },
      "312": {
        start: {
          line: 347,
          column: 24
        },
        end: {
          line: 347,
          column: 62
        }
      },
      "313": {
        start: {
          line: 348,
          column: 24
        },
        end: {
          line: 348,
          column: 119
        }
      },
      "314": {
        start: {
          line: 350,
          column: 20
        },
        end: {
          line: 350,
          column: 44
        }
      },
      "315": {
        start: {
          line: 353,
          column: 20
        },
        end: {
          line: 353,
          column: 44
        }
      },
      "316": {
        start: {
          line: 354,
          column: 20
        },
        end: {
          line: 354,
          column: 63
        }
      },
      "317": {
        start: {
          line: 355,
          column: 1012
        },
        end: {
          line: 355,
          column: 1050
        }
      },
      "318": {
        start: {
          line: 355,
          column: 1376
        },
        end: {
          line: 360,
          column: 384
        }
      },
      "319": {
        start: {
          line: 357,
          column: 69
        },
        end: {
          line: 357,
          column: 158
        }
      },
      "320": {
        start: {
          line: 357,
          column: 106
        },
        end: {
          line: 357,
          column: 155
        }
      },
      "321": {
        start: {
          line: 358,
          column: 70
        },
        end: {
          line: 358,
          column: 125
        }
      },
      "322": {
        start: {
          line: 359,
          column: 52
        },
        end: {
          line: 359,
          column: 84
        }
      },
      "323": {
        start: {
          line: 360,
          column: 445
        },
        end: {
          line: 360,
          column: 4821
        }
      },
      "324": {
        start: {
          line: 360,
          column: 977
        },
        end: {
          line: 360,
          column: 1008
        }
      },
      "325": {
        start: {
          line: 360,
          column: 1474
        },
        end: {
          line: 360,
          column: 1534
        }
      },
      "326": {
        start: {
          line: 360,
          column: 2129
        },
        end: {
          line: 360,
          column: 2184
        }
      },
      "327": {
        start: {
          line: 360,
          column: 3017
        },
        end: {
          line: 360,
          column: 3077
        }
      },
      "328": {
        start: {
          line: 360,
          column: 3820
        },
        end: {
          line: 360,
          column: 3903
        }
      },
      "329": {
        start: {
          line: 360,
          column: 4254
        },
        end: {
          line: 360,
          column: 4313
        }
      },
      "330": {
        start: {
          line: 360,
          column: 4712
        },
        end: {
          line: 360,
          column: 4768
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 42
          },
          end: {
            line: 3,
            column: 43
          }
        },
        loc: {
          start: {
            line: 3,
            column: 54
          },
          end: {
            line: 13,
            column: 1
          }
        },
        line: 3
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 4,
            column: 32
          },
          end: {
            line: 4,
            column: 33
          }
        },
        loc: {
          start: {
            line: 4,
            column: 44
          },
          end: {
            line: 11,
            column: 5
          }
        },
        line: 4
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 14,
            column: 74
          },
          end: {
            line: 14,
            column: 75
          }
        },
        loc: {
          start: {
            line: 14,
            column: 96
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 14
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 18,
            column: 38
          },
          end: {
            line: 18,
            column: 39
          }
        },
        loc: {
          start: {
            line: 18,
            column: 49
          },
          end: {
            line: 18,
            column: 65
          }
        },
        line: 18
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 21,
            column: 6
          },
          end: {
            line: 21,
            column: 7
          }
        },
        loc: {
          start: {
            line: 21,
            column: 28
          },
          end: {
            line: 24,
            column: 1
          }
        },
        line: 21
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 25,
            column: 80
          },
          end: {
            line: 25,
            column: 81
          }
        },
        loc: {
          start: {
            line: 25,
            column: 95
          },
          end: {
            line: 27,
            column: 1
          }
        },
        line: 25
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 27,
            column: 5
          },
          end: {
            line: 27,
            column: 6
          }
        },
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 29,
            column: 1
          }
        },
        line: 27
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 30,
            column: 51
          },
          end: {
            line: 30,
            column: 52
          }
        },
        loc: {
          start: {
            line: 30,
            column: 63
          },
          end: {
            line: 46,
            column: 1
          }
        },
        line: 30
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 31,
            column: 18
          },
          end: {
            line: 31,
            column: 19
          }
        },
        loc: {
          start: {
            line: 31,
            column: 30
          },
          end: {
            line: 38,
            column: 5
          }
        },
        line: 31
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 32,
            column: 48
          },
          end: {
            line: 32,
            column: 49
          }
        },
        loc: {
          start: {
            line: 32,
            column: 61
          },
          end: {
            line: 36,
            column: 9
          }
        },
        line: 32
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 39,
            column: 11
          },
          end: {
            line: 39,
            column: 12
          }
        },
        loc: {
          start: {
            line: 39,
            column: 26
          },
          end: {
            line: 45,
            column: 5
          }
        },
        line: 39
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 47,
            column: 44
          },
          end: {
            line: 47,
            column: 45
          }
        },
        loc: {
          start: {
            line: 47,
            column: 89
          },
          end: {
            line: 55,
            column: 1
          }
        },
        line: 47
      },
      "12": {
        name: "adopt",
        decl: {
          start: {
            line: 48,
            column: 13
          },
          end: {
            line: 48,
            column: 18
          }
        },
        loc: {
          start: {
            line: 48,
            column: 26
          },
          end: {
            line: 48,
            column: 112
          }
        },
        line: 48
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 48,
            column: 70
          },
          end: {
            line: 48,
            column: 71
          }
        },
        loc: {
          start: {
            line: 48,
            column: 89
          },
          end: {
            line: 48,
            column: 108
          }
        },
        line: 48
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 49,
            column: 36
          },
          end: {
            line: 49,
            column: 37
          }
        },
        loc: {
          start: {
            line: 49,
            column: 63
          },
          end: {
            line: 54,
            column: 5
          }
        },
        line: 49
      },
      "15": {
        name: "fulfilled",
        decl: {
          start: {
            line: 50,
            column: 17
          },
          end: {
            line: 50,
            column: 26
          }
        },
        loc: {
          start: {
            line: 50,
            column: 34
          },
          end: {
            line: 50,
            column: 99
          }
        },
        line: 50
      },
      "16": {
        name: "rejected",
        decl: {
          start: {
            line: 51,
            column: 17
          },
          end: {
            line: 51,
            column: 25
          }
        },
        loc: {
          start: {
            line: 51,
            column: 33
          },
          end: {
            line: 51,
            column: 102
          }
        },
        line: 51
      },
      "17": {
        name: "step",
        decl: {
          start: {
            line: 52,
            column: 17
          },
          end: {
            line: 52,
            column: 21
          }
        },
        loc: {
          start: {
            line: 52,
            column: 30
          },
          end: {
            line: 52,
            column: 118
          }
        },
        line: 52
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 56,
            column: 48
          },
          end: {
            line: 56,
            column: 49
          }
        },
        loc: {
          start: {
            line: 56,
            column: 73
          },
          end: {
            line: 82,
            column: 1
          }
        },
        line: 56
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 57,
            column: 30
          },
          end: {
            line: 57,
            column: 31
          }
        },
        loc: {
          start: {
            line: 57,
            column: 41
          },
          end: {
            line: 57,
            column: 83
          }
        },
        line: 57
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 58,
            column: 128
          },
          end: {
            line: 58,
            column: 129
          }
        },
        loc: {
          start: {
            line: 58,
            column: 139
          },
          end: {
            line: 58,
            column: 155
          }
        },
        line: 58
      },
      "21": {
        name: "verb",
        decl: {
          start: {
            line: 59,
            column: 13
          },
          end: {
            line: 59,
            column: 17
          }
        },
        loc: {
          start: {
            line: 59,
            column: 21
          },
          end: {
            line: 59,
            column: 70
          }
        },
        line: 59
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 59,
            column: 30
          },
          end: {
            line: 59,
            column: 31
          }
        },
        loc: {
          start: {
            line: 59,
            column: 43
          },
          end: {
            line: 59,
            column: 67
          }
        },
        line: 59
      },
      "23": {
        name: "step",
        decl: {
          start: {
            line: 60,
            column: 13
          },
          end: {
            line: 60,
            column: 17
          }
        },
        loc: {
          start: {
            line: 60,
            column: 22
          },
          end: {
            line: 81,
            column: 5
          }
        },
        line: 60
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 83,
            column: 52
          },
          end: {
            line: 83,
            column: 53
          }
        },
        loc: {
          start: {
            line: 83,
            column: 78
          },
          end: {
            line: 91,
            column: 1
          }
        },
        line: 83
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 92,
            column: 56
          },
          end: {
            line: 92,
            column: 57
          }
        },
        loc: {
          start: {
            line: 92,
            column: 71
          },
          end: {
            line: 94,
            column: 1
          }
        },
        line: 92
      },
      "26": {
        name: "SkillAssessmentForm",
        decl: {
          start: {
            line: 110,
            column: 9
          },
          end: {
            line: 110,
            column: 28
          }
        },
        loc: {
          start: {
            line: 110,
            column: 33
          },
          end: {
            line: 361,
            column: 1
          }
        },
        line: 110
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 116,
            column: 27
          },
          end: {
            line: 116,
            column: 28
          }
        },
        loc: {
          start: {
            line: 116,
            column: 39
          },
          end: {
            line: 120,
            column: 5
          }
        },
        line: 116
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 122,
            column: 53
          },
          end: {
            line: 122,
            column: 54
          }
        },
        loc: {
          start: {
            line: 122,
            column: 79
          },
          end: {
            line: 127,
            column: 5
          }
        },
        line: 122
      },
      "29": {
        name: "createEmptyAssessment",
        decl: {
          start: {
            line: 133,
            column: 13
          },
          end: {
            line: 133,
            column: 34
          }
        },
        loc: {
          start: {
            line: 133,
            column: 37
          },
          end: {
            line: 145,
            column: 5
          }
        },
        line: 133
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 147,
            column: 27
          },
          end: {
            line: 147,
            column: 28
          }
        },
        loc: {
          start: {
            line: 147,
            column: 39
          },
          end: {
            line: 180,
            column: 5
          }
        },
        line: 147
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 148,
            column: 27
          },
          end: {
            line: 148,
            column: 28
          }
        },
        loc: {
          start: {
            line: 148,
            column: 39
          },
          end: {
            line: 177,
            column: 13
          }
        },
        line: 148
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 148,
            column: 81
          },
          end: {
            line: 148,
            column: 82
          }
        },
        loc: {
          start: {
            line: 148,
            column: 93
          },
          end: {
            line: 177,
            column: 9
          }
        },
        line: 148
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 150,
            column: 37
          },
          end: {
            line: 150,
            column: 38
          }
        },
        loc: {
          start: {
            line: 150,
            column: 51
          },
          end: {
            line: 176,
            column: 13
          }
        },
        line: 150
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 179,
            column: 15
          },
          end: {
            line: 179,
            column: 16
          }
        },
        loc: {
          start: {
            line: 179,
            column: 27
          },
          end: {
            line: 179,
            column: 66
          }
        },
        line: 179
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 181,
            column: 49
          },
          end: {
            line: 181,
            column: 50
          }
        },
        loc: {
          start: {
            line: 181,
            column: 61
          },
          end: {
            line: 187,
            column: 5
          }
        },
        line: 181
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 188,
            column: 52
          },
          end: {
            line: 188,
            column: 53
          }
        },
        loc: {
          start: {
            line: 188,
            column: 69
          },
          end: {
            line: 194,
            column: 5
          }
        },
        line: 188
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 193,
            column: 45
          },
          end: {
            line: 193,
            column: 46
          }
        },
        loc: {
          start: {
            line: 193,
            column: 61
          },
          end: {
            line: 193,
            column: 84
          }
        },
        line: 193
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 195,
            column: 47
          },
          end: {
            line: 195,
            column: 48
          }
        },
        loc: {
          start: {
            line: 195,
            column: 71
          },
          end: {
            line: 215,
            column: 5
          }
        },
        line: 195
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 216,
            column: 52
          },
          end: {
            line: 216,
            column: 53
          }
        },
        loc: {
          start: {
            line: 216,
            column: 83
          },
          end: {
            line: 230,
            column: 5
          }
        },
        line: 216
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 224,
            column: 22
          },
          end: {
            line: 224,
            column: 23
          }
        },
        loc: {
          start: {
            line: 224,
            column: 38
          },
          end: {
            line: 228,
            column: 13
          }
        },
        line: 224
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 231,
            column: 55
          },
          end: {
            line: 231,
            column: 56
          }
        },
        loc: {
          start: {
            line: 231,
            column: 67
          },
          end: {
            line: 249,
            column: 5
          }
        },
        line: 231
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 233,
            column: 28
          },
          end: {
            line: 233,
            column: 29
          }
        },
        loc: {
          start: {
            line: 233,
            column: 57
          },
          end: {
            line: 246,
            column: 9
          }
        },
        line: 233
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 250,
            column: 48
          },
          end: {
            line: 250,
            column: 49
          }
        },
        loc: {
          start: {
            line: 250,
            column: 61
          },
          end: {
            line: 315,
            column: 9
          }
        },
        line: 250
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 250,
            column: 103
          },
          end: {
            line: 250,
            column: 104
          }
        },
        loc: {
          start: {
            line: 250,
            column: 115
          },
          end: {
            line: 315,
            column: 5
          }
        },
        line: 250
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 252,
            column: 33
          },
          end: {
            line: 252,
            column: 34
          }
        },
        loc: {
          start: {
            line: 252,
            column: 47
          },
          end: {
            line: 314,
            column: 9
          }
        },
        line: 252
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 316,
            column: 50
          },
          end: {
            line: 316,
            column: 51
          }
        },
        loc: {
          start: {
            line: 316,
            column: 68
          },
          end: {
            line: 326,
            column: 5
          }
        },
        line: 316
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 327,
            column: 50
          },
          end: {
            line: 327,
            column: 51
          }
        },
        loc: {
          start: {
            line: 327,
            column: 68
          },
          end: {
            line: 337,
            column: 5
          }
        },
        line: 327
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 338,
            column: 190
          },
          end: {
            line: 338,
            column: 191
          }
        },
        loc: {
          start: {
            line: 338,
            column: 202
          },
          end: {
            line: 342,
            column: 17
          }
        },
        line: 338
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 342,
            column: 37
          },
          end: {
            line: 342,
            column: 38
          }
        },
        loc: {
          start: {
            line: 342,
            column: 60
          },
          end: {
            line: 351,
            column: 17
          }
        },
        line: 342
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 351,
            column: 34
          },
          end: {
            line: 351,
            column: 35
          }
        },
        loc: {
          start: {
            line: 351,
            column: 46
          },
          end: {
            line: 355,
            column: 17
          }
        },
        line: 351
      },
      "51": {
        name: "(anonymous_51)",
        decl: {
          start: {
            line: 355,
            column: 997
          },
          end: {
            line: 355,
            column: 998
          }
        },
        loc: {
          start: {
            line: 355,
            column: 1010
          },
          end: {
            line: 355,
            column: 1052
          }
        },
        line: 355
      },
      "52": {
        name: "(anonymous_52)",
        decl: {
          start: {
            line: 355,
            column: 1357
          },
          end: {
            line: 355,
            column: 1358
          }
        },
        loc: {
          start: {
            line: 355,
            column: 1374
          },
          end: {
            line: 360,
            column: 386
          }
        },
        line: 355
      },
      "53": {
        name: "(anonymous_53)",
        decl: {
          start: {
            line: 355,
            column: 1506
          },
          end: {
            line: 355,
            column: 1507
          }
        },
        loc: {
          start: {
            line: 355,
            column: 1518
          },
          end: {
            line: 360,
            column: 49
          }
        },
        line: 355
      },
      "54": {
        name: "(anonymous_54)",
        decl: {
          start: {
            line: 357,
            column: 91
          },
          end: {
            line: 357,
            column: 92
          }
        },
        loc: {
          start: {
            line: 357,
            column: 104
          },
          end: {
            line: 357,
            column: 157
          }
        },
        line: 357
      },
      "55": {
        name: "(anonymous_55)",
        decl: {
          start: {
            line: 360,
            column: 414
          },
          end: {
            line: 360,
            column: 415
          }
        },
        loc: {
          start: {
            line: 360,
            column: 443
          },
          end: {
            line: 360,
            column: 4823
          }
        },
        line: 360
      },
      "56": {
        name: "(anonymous_56)",
        decl: {
          start: {
            line: 360,
            column: 963
          },
          end: {
            line: 360,
            column: 964
          }
        },
        loc: {
          start: {
            line: 360,
            column: 975
          },
          end: {
            line: 360,
            column: 1010
          }
        },
        line: 360
      },
      "57": {
        name: "(anonymous_57)",
        decl: {
          start: {
            line: 360,
            column: 1459
          },
          end: {
            line: 360,
            column: 1460
          }
        },
        loc: {
          start: {
            line: 360,
            column: 1472
          },
          end: {
            line: 360,
            column: 1536
          }
        },
        line: 360
      },
      "58": {
        name: "(anonymous_58)",
        decl: {
          start: {
            line: 360,
            column: 2110
          },
          end: {
            line: 360,
            column: 2111
          }
        },
        loc: {
          start: {
            line: 360,
            column: 2127
          },
          end: {
            line: 360,
            column: 2186
          }
        },
        line: 360
      },
      "59": {
        name: "(anonymous_59)",
        decl: {
          start: {
            line: 360,
            column: 2998
          },
          end: {
            line: 360,
            column: 2999
          }
        },
        loc: {
          start: {
            line: 360,
            column: 3015
          },
          end: {
            line: 360,
            column: 3079
          }
        },
        line: 360
      },
      "60": {
        name: "(anonymous_60)",
        decl: {
          start: {
            line: 360,
            column: 3805
          },
          end: {
            line: 360,
            column: 3806
          }
        },
        loc: {
          start: {
            line: 360,
            column: 3818
          },
          end: {
            line: 360,
            column: 3905
          }
        },
        line: 360
      },
      "61": {
        name: "(anonymous_61)",
        decl: {
          start: {
            line: 360,
            column: 4239
          },
          end: {
            line: 360,
            column: 4240
          }
        },
        loc: {
          start: {
            line: 360,
            column: 4252
          },
          end: {
            line: 360,
            column: 4315
          }
        },
        line: 360
      },
      "62": {
        name: "(anonymous_62)",
        decl: {
          start: {
            line: 360,
            column: 4697
          },
          end: {
            line: 360,
            column: 4698
          }
        },
        loc: {
          start: {
            line: 360,
            column: 4710
          },
          end: {
            line: 360,
            column: 4770
          }
        },
        line: 360
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 13,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 16
          },
          end: {
            line: 3,
            column: 20
          }
        }, {
          start: {
            line: 3,
            column: 24
          },
          end: {
            line: 3,
            column: 37
          }
        }, {
          start: {
            line: 3,
            column: 42
          },
          end: {
            line: 13,
            column: 1
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 4,
            column: 15
          },
          end: {
            line: 11,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 15
          },
          end: {
            line: 4,
            column: 28
          }
        }, {
          start: {
            line: 4,
            column: 32
          },
          end: {
            line: 11,
            column: 5
          }
        }],
        line: 4
      },
      "2": {
        loc: {
          start: {
            line: 7,
            column: 29
          },
          end: {
            line: 8,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 7,
            column: 29
          },
          end: {
            line: 8,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 7
      },
      "3": {
        loc: {
          start: {
            line: 14,
            column: 22
          },
          end: {
            line: 24,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 14,
            column: 23
          },
          end: {
            line: 14,
            column: 27
          }
        }, {
          start: {
            line: 14,
            column: 31
          },
          end: {
            line: 14,
            column: 51
          }
        }, {
          start: {
            line: 14,
            column: 57
          },
          end: {
            line: 24,
            column: 2
          }
        }],
        line: 14
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 57
          },
          end: {
            line: 24,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 74
          },
          end: {
            line: 21,
            column: 1
          }
        }, {
          start: {
            line: 21,
            column: 6
          },
          end: {
            line: 24,
            column: 1
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 4
          },
          end: {
            line: 15,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 15,
            column: 4
          },
          end: {
            line: 15,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 17,
            column: 4
          },
          end: {
            line: 19,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 17,
            column: 4
          },
          end: {
            line: 19,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 17
      },
      "7": {
        loc: {
          start: {
            line: 17,
            column: 8
          },
          end: {
            line: 17,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 8
          },
          end: {
            line: 17,
            column: 13
          }
        }, {
          start: {
            line: 17,
            column: 18
          },
          end: {
            line: 17,
            column: 84
          }
        }],
        line: 17
      },
      "8": {
        loc: {
          start: {
            line: 17,
            column: 18
          },
          end: {
            line: 17,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 17,
            column: 34
          },
          end: {
            line: 17,
            column: 47
          }
        }, {
          start: {
            line: 17,
            column: 50
          },
          end: {
            line: 17,
            column: 84
          }
        }],
        line: 17
      },
      "9": {
        loc: {
          start: {
            line: 17,
            column: 50
          },
          end: {
            line: 17,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 50
          },
          end: {
            line: 17,
            column: 63
          }
        }, {
          start: {
            line: 17,
            column: 67
          },
          end: {
            line: 17,
            column: 84
          }
        }],
        line: 17
      },
      "10": {
        loc: {
          start: {
            line: 22,
            column: 4
          },
          end: {
            line: 22,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 4
          },
          end: {
            line: 22,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "11": {
        loc: {
          start: {
            line: 25,
            column: 25
          },
          end: {
            line: 29,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 25,
            column: 26
          },
          end: {
            line: 25,
            column: 30
          }
        }, {
          start: {
            line: 25,
            column: 34
          },
          end: {
            line: 25,
            column: 57
          }
        }, {
          start: {
            line: 25,
            column: 63
          },
          end: {
            line: 29,
            column: 1
          }
        }],
        line: 25
      },
      "12": {
        loc: {
          start: {
            line: 25,
            column: 63
          },
          end: {
            line: 29,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 25,
            column: 80
          },
          end: {
            line: 27,
            column: 1
          }
        }, {
          start: {
            line: 27,
            column: 5
          },
          end: {
            line: 29,
            column: 1
          }
        }],
        line: 25
      },
      "13": {
        loc: {
          start: {
            line: 30,
            column: 19
          },
          end: {
            line: 46,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 24
          }
        }, {
          start: {
            line: 30,
            column: 28
          },
          end: {
            line: 30,
            column: 45
          }
        }, {
          start: {
            line: 30,
            column: 50
          },
          end: {
            line: 46,
            column: 4
          }
        }],
        line: 30
      },
      "14": {
        loc: {
          start: {
            line: 32,
            column: 18
          },
          end: {
            line: 36,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 18
          },
          end: {
            line: 32,
            column: 44
          }
        }, {
          start: {
            line: 32,
            column: 48
          },
          end: {
            line: 36,
            column: 9
          }
        }],
        line: 32
      },
      "15": {
        loc: {
          start: {
            line: 34,
            column: 29
          },
          end: {
            line: 34,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 29
          },
          end: {
            line: 34,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "16": {
        loc: {
          start: {
            line: 40,
            column: 8
          },
          end: {
            line: 40,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 8
          },
          end: {
            line: 40,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "17": {
        loc: {
          start: {
            line: 40,
            column: 12
          },
          end: {
            line: 40,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 12
          },
          end: {
            line: 40,
            column: 15
          }
        }, {
          start: {
            line: 40,
            column: 19
          },
          end: {
            line: 40,
            column: 33
          }
        }],
        line: 40
      },
      "18": {
        loc: {
          start: {
            line: 42,
            column: 8
          },
          end: {
            line: 42,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 8
          },
          end: {
            line: 42,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "19": {
        loc: {
          start: {
            line: 42,
            column: 78
          },
          end: {
            line: 42,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 78
          },
          end: {
            line: 42,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "20": {
        loc: {
          start: {
            line: 47,
            column: 16
          },
          end: {
            line: 55,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 47,
            column: 17
          },
          end: {
            line: 47,
            column: 21
          }
        }, {
          start: {
            line: 47,
            column: 25
          },
          end: {
            line: 47,
            column: 39
          }
        }, {
          start: {
            line: 47,
            column: 44
          },
          end: {
            line: 55,
            column: 1
          }
        }],
        line: 47
      },
      "21": {
        loc: {
          start: {
            line: 48,
            column: 35
          },
          end: {
            line: 48,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 48,
            column: 56
          },
          end: {
            line: 48,
            column: 61
          }
        }, {
          start: {
            line: 48,
            column: 64
          },
          end: {
            line: 48,
            column: 109
          }
        }],
        line: 48
      },
      "22": {
        loc: {
          start: {
            line: 49,
            column: 16
          },
          end: {
            line: 49,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 16
          },
          end: {
            line: 49,
            column: 17
          }
        }, {
          start: {
            line: 49,
            column: 22
          },
          end: {
            line: 49,
            column: 33
          }
        }],
        line: 49
      },
      "23": {
        loc: {
          start: {
            line: 52,
            column: 32
          },
          end: {
            line: 52,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 52,
            column: 46
          },
          end: {
            line: 52,
            column: 67
          }
        }, {
          start: {
            line: 52,
            column: 70
          },
          end: {
            line: 52,
            column: 115
          }
        }],
        line: 52
      },
      "24": {
        loc: {
          start: {
            line: 53,
            column: 51
          },
          end: {
            line: 53,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 53,
            column: 51
          },
          end: {
            line: 53,
            column: 61
          }
        }, {
          start: {
            line: 53,
            column: 65
          },
          end: {
            line: 53,
            column: 67
          }
        }],
        line: 53
      },
      "25": {
        loc: {
          start: {
            line: 56,
            column: 18
          },
          end: {
            line: 82,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 56,
            column: 19
          },
          end: {
            line: 56,
            column: 23
          }
        }, {
          start: {
            line: 56,
            column: 27
          },
          end: {
            line: 56,
            column: 43
          }
        }, {
          start: {
            line: 56,
            column: 48
          },
          end: {
            line: 82,
            column: 1
          }
        }],
        line: 56
      },
      "26": {
        loc: {
          start: {
            line: 57,
            column: 43
          },
          end: {
            line: 57,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 57,
            column: 43
          },
          end: {
            line: 57,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 57
      },
      "27": {
        loc: {
          start: {
            line: 57,
            column: 134
          },
          end: {
            line: 57,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 57,
            column: 167
          },
          end: {
            line: 57,
            column: 175
          }
        }, {
          start: {
            line: 57,
            column: 178
          },
          end: {
            line: 57,
            column: 184
          }
        }],
        line: 57
      },
      "28": {
        loc: {
          start: {
            line: 58,
            column: 74
          },
          end: {
            line: 58,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 58,
            column: 74
          },
          end: {
            line: 58,
            column: 102
          }
        }, {
          start: {
            line: 58,
            column: 107
          },
          end: {
            line: 58,
            column: 155
          }
        }],
        line: 58
      },
      "29": {
        loc: {
          start: {
            line: 61,
            column: 8
          },
          end: {
            line: 61,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 61,
            column: 8
          },
          end: {
            line: 61,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 61
      },
      "30": {
        loc: {
          start: {
            line: 62,
            column: 15
          },
          end: {
            line: 62,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 62,
            column: 15
          },
          end: {
            line: 62,
            column: 16
          }
        }, {
          start: {
            line: 62,
            column: 21
          },
          end: {
            line: 62,
            column: 44
          }
        }],
        line: 62
      },
      "31": {
        loc: {
          start: {
            line: 62,
            column: 28
          },
          end: {
            line: 62,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 62,
            column: 28
          },
          end: {
            line: 62,
            column: 33
          }
        }, {
          start: {
            line: 62,
            column: 38
          },
          end: {
            line: 62,
            column: 43
          }
        }],
        line: 62
      },
      "32": {
        loc: {
          start: {
            line: 63,
            column: 12
          },
          end: {
            line: 63,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 63,
            column: 12
          },
          end: {
            line: 63,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 63
      },
      "33": {
        loc: {
          start: {
            line: 63,
            column: 23
          },
          end: {
            line: 63,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 63,
            column: 23
          },
          end: {
            line: 63,
            column: 24
          }
        }, {
          start: {
            line: 63,
            column: 29
          },
          end: {
            line: 63,
            column: 125
          }
        }, {
          start: {
            line: 63,
            column: 130
          },
          end: {
            line: 63,
            column: 158
          }
        }],
        line: 63
      },
      "34": {
        loc: {
          start: {
            line: 63,
            column: 33
          },
          end: {
            line: 63,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 63,
            column: 45
          },
          end: {
            line: 63,
            column: 56
          }
        }, {
          start: {
            line: 63,
            column: 59
          },
          end: {
            line: 63,
            column: 125
          }
        }],
        line: 63
      },
      "35": {
        loc: {
          start: {
            line: 63,
            column: 59
          },
          end: {
            line: 63,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 63,
            column: 67
          },
          end: {
            line: 63,
            column: 116
          }
        }, {
          start: {
            line: 63,
            column: 119
          },
          end: {
            line: 63,
            column: 125
          }
        }],
        line: 63
      },
      "36": {
        loc: {
          start: {
            line: 63,
            column: 67
          },
          end: {
            line: 63,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 63,
            column: 67
          },
          end: {
            line: 63,
            column: 77
          }
        }, {
          start: {
            line: 63,
            column: 82
          },
          end: {
            line: 63,
            column: 115
          }
        }],
        line: 63
      },
      "37": {
        loc: {
          start: {
            line: 63,
            column: 82
          },
          end: {
            line: 63,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 63,
            column: 83
          },
          end: {
            line: 63,
            column: 98
          }
        }, {
          start: {
            line: 63,
            column: 103
          },
          end: {
            line: 63,
            column: 112
          }
        }],
        line: 63
      },
      "38": {
        loc: {
          start: {
            line: 64,
            column: 12
          },
          end: {
            line: 64,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 64,
            column: 12
          },
          end: {
            line: 64,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 64
      },
      "39": {
        loc: {
          start: {
            line: 65,
            column: 12
          },
          end: {
            line: 77,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 66,
            column: 16
          },
          end: {
            line: 66,
            column: 23
          }
        }, {
          start: {
            line: 66,
            column: 24
          },
          end: {
            line: 66,
            column: 46
          }
        }, {
          start: {
            line: 67,
            column: 16
          },
          end: {
            line: 67,
            column: 72
          }
        }, {
          start: {
            line: 68,
            column: 16
          },
          end: {
            line: 68,
            column: 65
          }
        }, {
          start: {
            line: 69,
            column: 16
          },
          end: {
            line: 69,
            column: 65
          }
        }, {
          start: {
            line: 70,
            column: 16
          },
          end: {
            line: 76,
            column: 43
          }
        }],
        line: 65
      },
      "40": {
        loc: {
          start: {
            line: 71,
            column: 20
          },
          end: {
            line: 71,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 71,
            column: 20
          },
          end: {
            line: 71,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 71
      },
      "41": {
        loc: {
          start: {
            line: 71,
            column: 24
          },
          end: {
            line: 71,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 71,
            column: 24
          },
          end: {
            line: 71,
            column: 74
          }
        }, {
          start: {
            line: 71,
            column: 79
          },
          end: {
            line: 71,
            column: 90
          }
        }, {
          start: {
            line: 71,
            column: 94
          },
          end: {
            line: 71,
            column: 105
          }
        }],
        line: 71
      },
      "42": {
        loc: {
          start: {
            line: 71,
            column: 42
          },
          end: {
            line: 71,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 71,
            column: 42
          },
          end: {
            line: 71,
            column: 54
          }
        }, {
          start: {
            line: 71,
            column: 58
          },
          end: {
            line: 71,
            column: 73
          }
        }],
        line: 71
      },
      "43": {
        loc: {
          start: {
            line: 72,
            column: 20
          },
          end: {
            line: 72,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 20
          },
          end: {
            line: 72,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "44": {
        loc: {
          start: {
            line: 72,
            column: 24
          },
          end: {
            line: 72,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 72,
            column: 24
          },
          end: {
            line: 72,
            column: 35
          }
        }, {
          start: {
            line: 72,
            column: 40
          },
          end: {
            line: 72,
            column: 42
          }
        }, {
          start: {
            line: 72,
            column: 47
          },
          end: {
            line: 72,
            column: 59
          }
        }, {
          start: {
            line: 72,
            column: 63
          },
          end: {
            line: 72,
            column: 75
          }
        }],
        line: 72
      },
      "45": {
        loc: {
          start: {
            line: 73,
            column: 20
          },
          end: {
            line: 73,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 73,
            column: 20
          },
          end: {
            line: 73,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 73
      },
      "46": {
        loc: {
          start: {
            line: 73,
            column: 24
          },
          end: {
            line: 73,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 73,
            column: 24
          },
          end: {
            line: 73,
            column: 35
          }
        }, {
          start: {
            line: 73,
            column: 39
          },
          end: {
            line: 73,
            column: 53
          }
        }],
        line: 73
      },
      "47": {
        loc: {
          start: {
            line: 74,
            column: 20
          },
          end: {
            line: 74,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 20
          },
          end: {
            line: 74,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "48": {
        loc: {
          start: {
            line: 74,
            column: 24
          },
          end: {
            line: 74,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 74,
            column: 24
          },
          end: {
            line: 74,
            column: 25
          }
        }, {
          start: {
            line: 74,
            column: 29
          },
          end: {
            line: 74,
            column: 43
          }
        }],
        line: 74
      },
      "49": {
        loc: {
          start: {
            line: 75,
            column: 20
          },
          end: {
            line: 75,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 75,
            column: 20
          },
          end: {
            line: 75,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 75
      },
      "50": {
        loc: {
          start: {
            line: 80,
            column: 8
          },
          end: {
            line: 80,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 80,
            column: 8
          },
          end: {
            line: 80,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 80
      },
      "51": {
        loc: {
          start: {
            line: 80,
            column: 52
          },
          end: {
            line: 80,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 80,
            column: 60
          },
          end: {
            line: 80,
            column: 65
          }
        }, {
          start: {
            line: 80,
            column: 68
          },
          end: {
            line: 80,
            column: 74
          }
        }],
        line: 80
      },
      "52": {
        loc: {
          start: {
            line: 83,
            column: 20
          },
          end: {
            line: 91,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 83,
            column: 21
          },
          end: {
            line: 83,
            column: 25
          }
        }, {
          start: {
            line: 83,
            column: 29
          },
          end: {
            line: 83,
            column: 47
          }
        }, {
          start: {
            line: 83,
            column: 52
          },
          end: {
            line: 91,
            column: 1
          }
        }],
        line: 83
      },
      "53": {
        loc: {
          start: {
            line: 84,
            column: 4
          },
          end: {
            line: 89,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 84,
            column: 4
          },
          end: {
            line: 89,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 84
      },
      "54": {
        loc: {
          start: {
            line: 84,
            column: 8
          },
          end: {
            line: 84,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 84,
            column: 8
          },
          end: {
            line: 84,
            column: 12
          }
        }, {
          start: {
            line: 84,
            column: 16
          },
          end: {
            line: 84,
            column: 38
          }
        }],
        line: 84
      },
      "55": {
        loc: {
          start: {
            line: 85,
            column: 8
          },
          end: {
            line: 88,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 85,
            column: 8
          },
          end: {
            line: 88,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 85
      },
      "56": {
        loc: {
          start: {
            line: 85,
            column: 12
          },
          end: {
            line: 85,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 85,
            column: 12
          },
          end: {
            line: 85,
            column: 14
          }
        }, {
          start: {
            line: 85,
            column: 18
          },
          end: {
            line: 85,
            column: 30
          }
        }],
        line: 85
      },
      "57": {
        loc: {
          start: {
            line: 86,
            column: 12
          },
          end: {
            line: 86,
            column: 65
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 86,
            column: 12
          },
          end: {
            line: 86,
            column: 65
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 86
      },
      "58": {
        loc: {
          start: {
            line: 90,
            column: 21
          },
          end: {
            line: 90,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 90,
            column: 21
          },
          end: {
            line: 90,
            column: 23
          }
        }, {
          start: {
            line: 90,
            column: 27
          },
          end: {
            line: 90,
            column: 59
          }
        }],
        line: 90
      },
      "59": {
        loc: {
          start: {
            line: 92,
            column: 22
          },
          end: {
            line: 94,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 92,
            column: 23
          },
          end: {
            line: 92,
            column: 27
          }
        }, {
          start: {
            line: 92,
            column: 31
          },
          end: {
            line: 92,
            column: 51
          }
        }, {
          start: {
            line: 92,
            column: 56
          },
          end: {
            line: 94,
            column: 1
          }
        }],
        line: 92
      },
      "60": {
        loc: {
          start: {
            line: 93,
            column: 11
          },
          end: {
            line: 93,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 93,
            column: 37
          },
          end: {
            line: 93,
            column: 40
          }
        }, {
          start: {
            line: 93,
            column: 43
          },
          end: {
            line: 93,
            column: 61
          }
        }],
        line: 93
      },
      "61": {
        loc: {
          start: {
            line: 93,
            column: 12
          },
          end: {
            line: 93,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 93,
            column: 12
          },
          end: {
            line: 93,
            column: 15
          }
        }, {
          start: {
            line: 93,
            column: 19
          },
          end: {
            line: 93,
            column: 33
          }
        }],
        line: 93
      },
      "62": {
        loc: {
          start: {
            line: 112,
            column: 115
          },
          end: {
            line: 112,
            column: 138
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 112,
            column: 131
          },
          end: {
            line: 112,
            column: 133
          }
        }, {
          start: {
            line: 112,
            column: 136
          },
          end: {
            line: 112,
            column: 138
          }
        }],
        line: 112
      },
      "63": {
        loc: {
          start: {
            line: 112,
            column: 161
          },
          end: {
            line: 112,
            column: 190
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 112,
            column: 177
          },
          end: {
            line: 112,
            column: 185
          }
        }, {
          start: {
            line: 112,
            column: 188
          },
          end: {
            line: 112,
            column: 190
          }
        }],
        line: 112
      },
      "64": {
        loc: {
          start: {
            line: 112,
            column: 233
          },
          end: {
            line: 112,
            column: 256
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 112,
            column: 249
          },
          end: {
            line: 112,
            column: 251
          }
        }, {
          start: {
            line: 112,
            column: 254
          },
          end: {
            line: 112,
            column: 256
          }
        }],
        line: 112
      },
      "65": {
        loc: {
          start: {
            line: 112,
            column: 313
          },
          end: {
            line: 112,
            column: 339
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 112,
            column: 329
          },
          end: {
            line: 112,
            column: 334
          }
        }, {
          start: {
            line: 112,
            column: 337
          },
          end: {
            line: 112,
            column: 339
          }
        }],
        line: 112
      },
      "66": {
        loc: {
          start: {
            line: 113,
            column: 35
          },
          end: {
            line: 113,
            column: 113
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 113,
            column: 67
          },
          end: {
            line: 113,
            column: 85
          }
        }, {
          start: {
            line: 113,
            column: 88
          },
          end: {
            line: 113,
            column: 113
          }
        }],
        line: 113
      },
      "67": {
        loc: {
          start: {
            line: 117,
            column: 8
          },
          end: {
            line: 119,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 117,
            column: 8
          },
          end: {
            line: 119,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 117
      },
      "68": {
        loc: {
          start: {
            line: 124,
            column: 8
          },
          end: {
            line: 126,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 124,
            column: 8
          },
          end: {
            line: 126,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 124
      },
      "69": {
        loc: {
          start: {
            line: 151,
            column: 16
          },
          end: {
            line: 175,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 152,
            column: 20
          },
          end: {
            line: 158,
            column: 37
          }
        }, {
          start: {
            line: 159,
            column: 20
          },
          end: {
            line: 161,
            column: 73
          }
        }, {
          start: {
            line: 162,
            column: 20
          },
          end: {
            line: 165,
            column: 48
          }
        }, {
          start: {
            line: 166,
            column: 20
          },
          end: {
            line: 170,
            column: 48
          }
        }, {
          start: {
            line: 171,
            column: 20
          },
          end: {
            line: 173,
            column: 50
          }
        }, {
          start: {
            line: 174,
            column: 20
          },
          end: {
            line: 174,
            column: 50
          }
        }],
        line: 151
      },
      "70": {
        loc: {
          start: {
            line: 153,
            column: 24
          },
          end: {
            line: 156,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 153,
            column: 24
          },
          end: {
            line: 156,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 153
      },
      "71": {
        loc: {
          start: {
            line: 153,
            column: 28
          },
          end: {
            line: 153,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 153,
            column: 28
          },
          end: {
            line: 153,
            column: 47
          }
        }, {
          start: {
            line: 153,
            column: 51
          },
          end: {
            line: 153,
            column: 65
          }
        }],
        line: 153
      },
      "72": {
        loc: {
          start: {
            line: 182,
            column: 8
          },
          end: {
            line: 185,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 182,
            column: 8
          },
          end: {
            line: 185,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 182
      },
      "73": {
        loc: {
          start: {
            line: 189,
            column: 8
          },
          end: {
            line: 192,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 189,
            column: 8
          },
          end: {
            line: 192,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 189
      },
      "74": {
        loc: {
          start: {
            line: 197,
            column: 8
          },
          end: {
            line: 200,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 197,
            column: 8
          },
          end: {
            line: 200,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 197
      },
      "75": {
        loc: {
          start: {
            line: 197,
            column: 12
          },
          end: {
            line: 197,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 197,
            column: 12
          },
          end: {
            line: 197,
            column: 21
          }
        }, {
          start: {
            line: 197,
            column: 25
          },
          end: {
            line: 197,
            column: 52
          }
        }],
        line: 197
      },
      "76": {
        loc: {
          start: {
            line: 203,
            column: 112
          },
          end: {
            line: 205,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 204,
            column: 18
          },
          end: {
            line: 204,
            column: 26
          }
        }, {
          start: {
            line: 205,
            column: 18
          },
          end: {
            line: 205,
            column: 101
          }
        }],
        line: 203
      },
      "77": {
        loc: {
          start: {
            line: 203,
            column: 112
          },
          end: {
            line: 203,
            column: 155
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 203,
            column: 112
          },
          end: {
            line: 203,
            column: 120
          }
        }, {
          start: {
            line: 203,
            column: 124
          },
          end: {
            line: 203,
            column: 155
          }
        }],
        line: 203
      },
      "78": {
        loc: {
          start: {
            line: 208,
            column: 8
          },
          end: {
            line: 210,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 208,
            column: 8
          },
          end: {
            line: 210,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 208
      },
      "79": {
        loc: {
          start: {
            line: 223,
            column: 8
          },
          end: {
            line: 229,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 223,
            column: 8
          },
          end: {
            line: 229,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 223
      },
      "80": {
        loc: {
          start: {
            line: 234,
            column: 12
          },
          end: {
            line: 236,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 234,
            column: 12
          },
          end: {
            line: 236,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 234
      },
      "81": {
        loc: {
          start: {
            line: 237,
            column: 12
          },
          end: {
            line: 239,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 237,
            column: 12
          },
          end: {
            line: 239,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 237
      },
      "82": {
        loc: {
          start: {
            line: 237,
            column: 16
          },
          end: {
            line: 237,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 237,
            column: 16
          },
          end: {
            line: 237,
            column: 41
          }
        }, {
          start: {
            line: 237,
            column: 45
          },
          end: {
            line: 237,
            column: 71
          }
        }],
        line: 237
      },
      "83": {
        loc: {
          start: {
            line: 240,
            column: 12
          },
          end: {
            line: 242,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 240,
            column: 12
          },
          end: {
            line: 242,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 240
      },
      "84": {
        loc: {
          start: {
            line: 240,
            column: 16
          },
          end: {
            line: 240,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 240,
            column: 16
          },
          end: {
            line: 240,
            column: 46
          }
        }, {
          start: {
            line: 240,
            column: 50
          },
          end: {
            line: 240,
            column: 81
          }
        }],
        line: 240
      },
      "85": {
        loc: {
          start: {
            line: 243,
            column: 12
          },
          end: {
            line: 245,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 243,
            column: 12
          },
          end: {
            line: 245,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 243
      },
      "86": {
        loc: {
          start: {
            line: 243,
            column: 16
          },
          end: {
            line: 243,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 243,
            column: 16
          },
          end: {
            line: 243,
            column: 44
          }
        }, {
          start: {
            line: 243,
            column: 48
          },
          end: {
            line: 243,
            column: 80
          }
        }],
        line: 243
      },
      "87": {
        loc: {
          start: {
            line: 253,
            column: 12
          },
          end: {
            line: 313,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 254,
            column: 16
          },
          end: {
            line: 262,
            column: 33
          }
        }, {
          start: {
            line: 263,
            column: 16
          },
          end: {
            line: 265,
            column: 64
          }
        }, {
          start: {
            line: 266,
            column: 16
          },
          end: {
            line: 297,
            column: 44
          }
        }, {
          start: {
            line: 298,
            column: 16
          },
          end: {
            line: 308,
            column: 44
          }
        }, {
          start: {
            line: 309,
            column: 16
          },
          end: {
            line: 311,
            column: 46
          }
        }, {
          start: {
            line: 312,
            column: 16
          },
          end: {
            line: 312,
            column: 46
          }
        }],
        line: 253
      },
      "88": {
        loc: {
          start: {
            line: 256,
            column: 20
          },
          end: {
            line: 259,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 256,
            column: 20
          },
          end: {
            line: 259,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 256
      },
      "89": {
        loc: {
          start: {
            line: 269,
            column: 20
          },
          end: {
            line: 296,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 269,
            column: 20
          },
          end: {
            line: 296,
            column: 21
          }
        }, {
          start: {
            line: 287,
            column: 25
          },
          end: {
            line: 296,
            column: 21
          }
        }],
        line: 269
      },
      "90": {
        loc: {
          start: {
            line: 269,
            column: 24
          },
          end: {
            line: 269,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 269,
            column: 24
          },
          end: {
            line: 269,
            column: 30
          }
        }, {
          start: {
            line: 269,
            column: 34
          },
          end: {
            line: 269,
            column: 60
          }
        }, {
          start: {
            line: 269,
            column: 64
          },
          end: {
            line: 269,
            column: 83
          }
        }],
        line: 269
      },
      "91": {
        loc: {
          start: {
            line: 271,
            column: 24
          },
          end: {
            line: 285,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 271,
            column: 24
          },
          end: {
            line: 285,
            column: 25
          }
        }, {
          start: {
            line: 282,
            column: 29
          },
          end: {
            line: 285,
            column: 25
          }
        }],
        line: 271
      },
      "92": {
        loc: {
          start: {
            line: 273,
            column: 28
          },
          end: {
            line: 280,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 273,
            column: 28
          },
          end: {
            line: 280,
            column: 29
          }
        }, {
          start: {
            line: 277,
            column: 33
          },
          end: {
            line: 280,
            column: 29
          }
        }],
        line: 273
      },
      "93": {
        loc: {
          start: {
            line: 273,
            column: 32
          },
          end: {
            line: 273,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 273,
            column: 32
          },
          end: {
            line: 273,
            column: 49
          }
        }, {
          start: {
            line: 273,
            column: 53
          },
          end: {
            line: 273,
            column: 75
          }
        }],
        line: 273
      },
      "94": {
        loc: {
          start: {
            line: 284,
            column: 49
          },
          end: {
            line: 284,
            column: 95
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 284,
            column: 49
          },
          end: {
            line: 284,
            column: 61
          }
        }, {
          start: {
            line: 284,
            column: 65
          },
          end: {
            line: 284,
            column: 95
          }
        }],
        line: 284
      },
      "95": {
        loc: {
          start: {
            line: 289,
            column: 24
          },
          end: {
            line: 295,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 289,
            column: 24
          },
          end: {
            line: 295,
            column: 25
          }
        }, {
          start: {
            line: 293,
            column: 29
          },
          end: {
            line: 295,
            column: 25
          }
        }],
        line: 289
      },
      "96": {
        loc: {
          start: {
            line: 289,
            column: 28
          },
          end: {
            line: 289,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 289,
            column: 28
          },
          end: {
            line: 289,
            column: 45
          }
        }, {
          start: {
            line: 289,
            column: 49
          },
          end: {
            line: 289,
            column: 71
          }
        }],
        line: 289
      },
      "97": {
        loc: {
          start: {
            line: 303,
            column: 31
          },
          end: {
            line: 303,
            column: 106
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 303,
            column: 58
          },
          end: {
            line: 303,
            column: 73
          }
        }, {
          start: {
            line: 303,
            column: 76
          },
          end: {
            line: 303,
            column: 106
          }
        }],
        line: 303
      },
      "98": {
        loc: {
          start: {
            line: 317,
            column: 8
          },
          end: {
            line: 318,
            column: 30
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 317,
            column: 8
          },
          end: {
            line: 318,
            column: 30
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 317
      },
      "99": {
        loc: {
          start: {
            line: 319,
            column: 8
          },
          end: {
            line: 320,
            column: 27
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 319,
            column: 8
          },
          end: {
            line: 320,
            column: 27
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 319
      },
      "100": {
        loc: {
          start: {
            line: 321,
            column: 8
          },
          end: {
            line: 322,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 321,
            column: 8
          },
          end: {
            line: 322,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 321
      },
      "101": {
        loc: {
          start: {
            line: 323,
            column: 8
          },
          end: {
            line: 324,
            column: 30
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 323,
            column: 8
          },
          end: {
            line: 324,
            column: 30
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 323
      },
      "102": {
        loc: {
          start: {
            line: 328,
            column: 8
          },
          end: {
            line: 329,
            column: 32
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 328,
            column: 8
          },
          end: {
            line: 329,
            column: 32
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 328
      },
      "103": {
        loc: {
          start: {
            line: 330,
            column: 8
          },
          end: {
            line: 331,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 330,
            column: 8
          },
          end: {
            line: 331,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 330
      },
      "104": {
        loc: {
          start: {
            line: 332,
            column: 8
          },
          end: {
            line: 333,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 332,
            column: 8
          },
          end: {
            line: 333,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 332
      },
      "105": {
        loc: {
          start: {
            line: 334,
            column: 8
          },
          end: {
            line: 335,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 334,
            column: 8
          },
          end: {
            line: 335,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 334
      },
      "106": {
        loc: {
          start: {
            line: 338,
            column: 80
          },
          end: {
            line: 355,
            column: 21
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 338,
            column: 80
          },
          end: {
            line: 338,
            column: 94
          }
        }, {
          start: {
            line: 338,
            column: 99
          },
          end: {
            line: 355,
            column: 20
          }
        }],
        line: 338
      },
      "107": {
        loc: {
          start: {
            line: 344,
            column: 20
          },
          end: {
            line: 349,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 344,
            column: 20
          },
          end: {
            line: 349,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 344
      },
      "108": {
        loc: {
          start: {
            line: 355,
            column: 612
          },
          end: {
            line: 360,
            column: 396
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 355,
            column: 612
          },
          end: {
            line: 355,
            column: 625
          }
        }, {
          start: {
            line: 355,
            column: 630
          },
          end: {
            line: 360,
            column: 395
          }
        }],
        line: 355
      },
      "109": {
        loc: {
          start: {
            line: 355,
            column: 1057
          },
          end: {
            line: 355,
            column: 1182
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 355,
            column: 1057
          },
          end: {
            line: 355,
            column: 1068
          }
        }, {
          start: {
            line: 355,
            column: 1073
          },
          end: {
            line: 355,
            column: 1181
          }
        }],
        line: 355
      },
      "110": {
        loc: {
          start: {
            line: 355,
            column: 1188
          },
          end: {
            line: 360,
            column: 391
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 355,
            column: 1188
          },
          end: {
            line: 355,
            column: 1212
          }
        }, {
          start: {
            line: 355,
            column: 1217
          },
          end: {
            line: 360,
            column: 390
          }
        }],
        line: 355
      },
      "111": {
        loc: {
          start: {
            line: 357,
            column: 113
          },
          end: {
            line: 357,
            column: 154
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 357,
            column: 113
          },
          end: {
            line: 357,
            column: 125
          }
        }, {
          start: {
            line: 357,
            column: 129
          },
          end: {
            line: 357,
            column: 154
          }
        }],
        line: 357
      },
      "112": {
        loc: {
          start: {
            line: 358,
            column: 70
          },
          end: {
            line: 358,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 358,
            column: 90
          },
          end: {
            line: 358,
            column: 100
          }
        }, {
          start: {
            line: 358,
            column: 103
          },
          end: {
            line: 358,
            column: 125
          }
        }],
        line: 358
      },
      "113": {
        loc: {
          start: {
            line: 360,
            column: 242
          },
          end: {
            line: 360,
            column: 368
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 360,
            column: 242
          },
          end: {
            line: 360,
            column: 259
          }
        }, {
          start: {
            line: 360,
            column: 264
          },
          end: {
            line: 360,
            column: 367
          }
        }],
        line: 360
      },
      "114": {
        loc: {
          start: {
            line: 360,
            column: 818
          },
          end: {
            line: 360,
            column: 1034
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 360,
            column: 818
          },
          end: {
            line: 360,
            column: 833
          }
        }, {
          start: {
            line: 360,
            column: 837
          },
          end: {
            line: 360,
            column: 859
          }
        }, {
          start: {
            line: 360,
            column: 864
          },
          end: {
            line: 360,
            column: 1033
          }
        }],
        line: 360
      },
      "115": {
        loc: {
          start: {
            line: 360,
            column: 1549
          },
          end: {
            line: 360,
            column: 1611
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 360,
            column: 1590
          },
          end: {
            line: 360,
            column: 1606
          }
        }, {
          start: {
            line: 360,
            column: 1609
          },
          end: {
            line: 360,
            column: 1611
          }
        }],
        line: 360
      },
      "116": {
        loc: {
          start: {
            line: 360,
            column: 1616
          },
          end: {
            line: 360,
            column: 1776
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 360,
            column: 1616
          },
          end: {
            line: 360,
            column: 1654
          }
        }, {
          start: {
            line: 360,
            column: 1659
          },
          end: {
            line: 360,
            column: 1775
          }
        }],
        line: 360
      },
      "117": {
        loc: {
          start: {
            line: 360,
            column: 3759
          },
          end: {
            line: 360,
            column: 3793
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 360,
            column: 3759
          },
          end: {
            line: 360,
            column: 3787
          }
        }, {
          start: {
            line: 360,
            column: 3791
          },
          end: {
            line: 360,
            column: 3793
          }
        }],
        line: 360
      },
      "118": {
        loc: {
          start: {
            line: 360,
            column: 3872
          },
          end: {
            line: 360,
            column: 3901
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 360,
            column: 3872
          },
          end: {
            line: 360,
            column: 3896
          }
        }, {
          start: {
            line: 360,
            column: 3900
          },
          end: {
            line: 360,
            column: 3901
          }
        }],
        line: 360
      },
      "119": {
        loc: {
          start: {
            line: 360,
            column: 4202
          },
          end: {
            line: 360,
            column: 4227
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 360,
            column: 4202
          },
          end: {
            line: 360,
            column: 4221
          }
        }, {
          start: {
            line: 360,
            column: 4225
          },
          end: {
            line: 360,
            column: 4227
          }
        }],
        line: 360
      },
      "120": {
        loc: {
          start: {
            line: 360,
            column: 4663
          },
          end: {
            line: 360,
            column: 4685
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 360,
            column: 4663
          },
          end: {
            line: 360,
            column: 4679
          }
        }, {
          start: {
            line: 360,
            column: 4683
          },
          end: {
            line: 360,
            column: 4685
          }
        }],
        line: 360
      },
      "121": {
        loc: {
          start: {
            line: 360,
            column: 4795
          },
          end: {
            line: 360,
            column: 4818
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 360,
            column: 4795
          },
          end: {
            line: 360,
            column: 4809
          }
        }, {
          start: {
            line: 360,
            column: 4813
          },
          end: {
            line: 360,
            column: 4818
          }
        }],
        line: 360
      },
      "122": {
        loc: {
          start: {
            line: 360,
            column: 4826
          },
          end: {
            line: 360,
            column: 5052
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 360,
            column: 4826
          },
          end: {
            line: 360,
            column: 4841
          }
        }, {
          start: {
            line: 360,
            column: 4845
          },
          end: {
            line: 360,
            column: 4880
          }
        }, {
          start: {
            line: 360,
            column: 4885
          },
          end: {
            line: 360,
            column: 5051
          }
        }],
        line: 360
      },
      "123": {
        loc: {
          start: {
            line: 360,
            column: 5252
          },
          end: {
            line: 360,
            column: 5465
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 360,
            column: 5268
          },
          end: {
            line: 360,
            column: 5440
          }
        }, {
          start: {
            line: 360,
            column: 5445
          },
          end: {
            line: 360,
            column: 5464
          }
        }],
        line: 360
      },
      "124": {
        loc: {
          start: {
            line: 360,
            column: 5484
          },
          end: {
            line: 360,
            column: 6055
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 360,
            column: 5484
          },
          end: {
            line: 360,
            column: 5506
          }
        }, {
          start: {
            line: 360,
            column: 5511
          },
          end: {
            line: 360,
            column: 6054
          }
        }],
        line: 360
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0,
      "265": 0,
      "266": 0,
      "267": 0,
      "268": 0,
      "269": 0,
      "270": 0,
      "271": 0,
      "272": 0,
      "273": 0,
      "274": 0,
      "275": 0,
      "276": 0,
      "277": 0,
      "278": 0,
      "279": 0,
      "280": 0,
      "281": 0,
      "282": 0,
      "283": 0,
      "284": 0,
      "285": 0,
      "286": 0,
      "287": 0,
      "288": 0,
      "289": 0,
      "290": 0,
      "291": 0,
      "292": 0,
      "293": 0,
      "294": 0,
      "295": 0,
      "296": 0,
      "297": 0,
      "298": 0,
      "299": 0,
      "300": 0,
      "301": 0,
      "302": 0,
      "303": 0,
      "304": 0,
      "305": 0,
      "306": 0,
      "307": 0,
      "308": 0,
      "309": 0,
      "310": 0,
      "311": 0,
      "312": 0,
      "313": 0,
      "314": 0,
      "315": 0,
      "316": 0,
      "317": 0,
      "318": 0,
      "319": 0,
      "320": 0,
      "321": 0,
      "322": 0,
      "323": 0,
      "324": 0,
      "325": 0,
      "326": 0,
      "327": 0,
      "328": 0,
      "329": 0,
      "330": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0, 0, 0, 0, 0],
      "40": [0, 0],
      "41": [0, 0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0, 0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0, 0, 0, 0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0],
      "81": [0, 0],
      "82": [0, 0],
      "83": [0, 0],
      "84": [0, 0],
      "85": [0, 0],
      "86": [0, 0],
      "87": [0, 0, 0, 0, 0, 0],
      "88": [0, 0],
      "89": [0, 0],
      "90": [0, 0, 0],
      "91": [0, 0],
      "92": [0, 0],
      "93": [0, 0],
      "94": [0, 0],
      "95": [0, 0],
      "96": [0, 0],
      "97": [0, 0],
      "98": [0, 0],
      "99": [0, 0],
      "100": [0, 0],
      "101": [0, 0],
      "102": [0, 0],
      "103": [0, 0],
      "104": [0, 0],
      "105": [0, 0],
      "106": [0, 0],
      "107": [0, 0],
      "108": [0, 0],
      "109": [0, 0],
      "110": [0, 0],
      "111": [0, 0],
      "112": [0, 0],
      "113": [0, 0],
      "114": [0, 0, 0],
      "115": [0, 0],
      "116": [0, 0],
      "117": [0, 0],
      "118": [0, 0],
      "119": [0, 0],
      "120": [0, 0],
      "121": [0, 0],
      "122": [0, 0, 0],
      "123": [0, 0],
      "124": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/skills/SkillAssessmentForm.tsx",
      mappings: ";AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4Cb,sCAgeC;;AA1gBD,6CAAgE;AAChE,6CAAiG;AACjG,iDAAgD;AAChD,+CAA8C;AAC9C,+CAA8C;AAC9C,qDAAoD;AACpD,iDAAgD;AAChD,+CAA8C;AAC9C,qDAAoD;AACpD,6CAA6C;AAC7C,iCAA+B;AAC/B,oGAAkG;AA+BlG,SAAwB,mBAAmB,CAAC,EAQjB;IAR3B,iBAgeC;QA/dC,QAAQ,cAAA,EACR,aAAa,mBAAA,EACb,0BAAuB,EAAvB,kBAAkB,mBAAG,EAAE,KAAA,EACvB,YAAe,EAAf,IAAI,mBAAG,QAAQ,KAAA,EACf,sBAAmB,EAAnB,cAAc,mBAAG,EAAE,KAAA,EACnB,6BAA6B,EAA7B,qBAAqB,mBAAG,KAAK,KAAA,EAC7B,mBAAmB,yBAAA;IAEb,IAAA,KAAgC,IAAA,gBAAQ,EAC5C,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC,CAC/E,EAFM,WAAW,QAAA,EAAE,cAAc,QAEjC,CAAC;IACI,IAAA,KAAsC,IAAA,gBAAQ,EAAwB,IAAI,CAAC,EAA1E,cAAc,QAAA,EAAE,iBAAiB,QAAyC,CAAC;IAElF,mEAAmE;IACnE,IAAA,iBAAS,EAAC;QACR,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,cAAc,CAAC,kBAAkB,CAAC,CAAC;QACrC,CAAC;IACH,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC;IAEzB,+DAA+D;IAC/D,IAAM,iBAAiB,GAAG,IAAA,mBAAW,EAAC,UAAC,cAAiC;QACtE,cAAc,CAAC,cAAc,CAAC,CAAC;QAC/B,IAAI,mBAAmB,EAAE,CAAC;YACxB,mBAAmB,CAAC,cAAc,CAAC,CAAC;QACtC,CAAC;IACH,CAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAAC;IAEpB,IAAA,KAAkC,IAAA,gBAAQ,EAAC,KAAK,CAAC,EAAhD,YAAY,QAAA,EAAE,eAAe,QAAmB,CAAC;IAClD,IAAA,KAAgC,IAAA,gBAAQ,EAAC,EAAE,CAAC,EAA3C,WAAW,QAAA,EAAE,cAAc,QAAgB,CAAC;IAC7C,IAAA,KAAoC,IAAA,gBAAQ,EAAU,EAAE,CAAC,EAAxD,aAAa,QAAA,EAAE,gBAAgB,QAAyB,CAAC;IAC1D,IAAA,KAAgC,IAAA,gBAAQ,EAAC,KAAK,CAAC,EAA9C,WAAW,QAAA,EAAE,cAAc,QAAmB,CAAC;IAChD,IAAA,KAAsB,IAAA,gBAAQ,EAAyB,EAAE,CAAC,EAAzD,MAAM,QAAA,EAAE,SAAS,QAAwC,CAAC;IAEjE,SAAS,qBAAqB;QAC5B,OAAO;YACL,GAAG,EAAE,qBAAc,IAAI,CAAC,GAAG,EAAE,cAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAE;YAC9E,OAAO,EAAE,EAAE;YACX,SAAS,EAAE,EAAE;YACb,UAAU,EAAE,CAAC;YACb,eAAe,EAAE,CAAC;YAClB,cAAc,EAAE,iBAAiB;YACjC,KAAK,EAAE,EAAE;YACT,iBAAiB,EAAE,CAAC;YACpB,QAAQ,EAAE,EAAE;SACb,CAAC;IACJ,CAAC;IAED,oBAAoB;IACpB,IAAA,iBAAS,EAAC;QACR,IAAM,YAAY,GAAG;;;;;wBACnB,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC;4BAC1C,gBAAgB,CAAC,EAAE,CAAC,CAAC;4BACrB,sBAAO;wBACT,CAAC;wBAED,cAAc,CAAC,IAAI,CAAC,CAAC;;;;wBAEH,qBAAM,aAAa,CAAC,WAAW,CAAC,EAAA;;wBAA1C,OAAO,GAAG,SAAgC;wBAChD,gBAAgB,CAAC,OAAO,CAAC,CAAC;;;;wBAE1B,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,OAAK,CAAC,CAAC;wBAChD,cAAK,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;;;wBAEvC,cAAc,CAAC,KAAK,CAAC,CAAC;;;;;aAEzB,CAAC;QAEF,IAAM,aAAa,GAAG,UAAU,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QACpD,OAAO,cAAM,OAAA,YAAY,CAAC,aAAa,CAAC,EAA3B,CAA2B,CAAC;IAC3C,CAAC,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC;IAEjC,IAAM,aAAa,GAAG,IAAA,mBAAW,EAAC;QAChC,IAAI,WAAW,CAAC,MAAM,IAAI,cAAc,EAAE,CAAC;YACzC,cAAK,CAAC,KAAK,CAAC,kBAAW,cAAc,yBAAsB,CAAC,CAAC;YAC7D,OAAO;QACT,CAAC;QACD,iBAAiB,iCAAK,WAAW,UAAE,qBAAqB,EAAE,UAAE,CAAC;IAC/D,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC,CAAC;IAE5D,IAAM,gBAAgB,GAAG,IAAA,mBAAW,EAAC,UAAC,KAAa;QACjD,IAAI,WAAW,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC5B,cAAK,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;YACnD,OAAO;QACT,CAAC;QACD,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,KAAK,KAAK,EAAX,CAAW,CAAC,CAAC,CAAC;IAC/D,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC,CAAC;IAE5C,IAAM,WAAW,GAAG,IAAA,mBAAW,EAAC,UAAC,KAAa,EAAE,KAAY;QAC1D,4BAA4B;QAC5B,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC;YAC7C,cAAK,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;YAC/D,OAAO;QACT,CAAC;QAED,wDAAwD;QACxD,IAAM,cAAc,qBAAO,WAAW,OAAC,CAAC;QACxC,cAAc,CAAC,KAAK,CAAC,yBAChB,cAAc,CAAC,KAAK,CAAC,KACxB,SAAS,EAAE,KAAK,CAAC,IAAI,EACrB,OAAO,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC;gBAClD,CAAC,CAAC,KAAK,CAAC,EAAE;gBACV,CAAC,CAAC,eAAQ,IAAI,CAAC,GAAG,EAAE,cAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAE,GACxE,CAAC;QAEF,wBAAwB;QACxB,cAAc,CAAC,cAAc,CAAC,CAAC;QAC/B,IAAI,mBAAmB,EAAE,CAAC;YACxB,mBAAmB,CAAC,cAAc,CAAC,CAAC;QACtC,CAAC;QAED,cAAc,CAAC,EAAE,CAAC,CAAC;QACnB,gBAAgB,CAAC,EAAE,CAAC,CAAC;QAErB,wBAAwB;QACxB,cAAK,CAAC,OAAO,CAAC,0BAAmB,KAAK,CAAC,IAAI,6BAAmB,KAAK,GAAG,CAAC,CAAE,CAAC,CAAC;IAC7E,CAAC,EAAE,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC,CAAC;IAEvC,IAAM,gBAAgB,GAAG,IAAA,mBAAW,EAAC,UAAC,KAAa,EAAE,KAA4B,EAAE,KAAU;;QAC3F,IAAM,OAAO,qBAAO,WAAW,OAAC,CAAC;QACjC,OAAO,CAAC,KAAK,CAAC,yBAAQ,OAAO,CAAC,KAAK,CAAC,gBAAG,KAAK,IAAG,KAAK,MAAE,CAAC;QACvD,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAE3B,6BAA6B;QAC7B,IAAM,QAAQ,GAAG,UAAG,KAAK,cAAI,KAAK,CAAE,CAAC;QACrC,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrB,SAAS,CAAC,UAAA,IAAI;gBACZ,IAAM,SAAS,gBAAQ,IAAI,CAAE,CAAC;gBAC9B,OAAO,SAAS,CAAC,QAAQ,CAAC,CAAC;gBAC3B,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC,EAAE,CAAC,WAAW,EAAE,iBAAiB,EAAE,MAAM,CAAC,CAAC,CAAC;IAI7C,IAAM,mBAAmB,GAAG,IAAA,mBAAW,EAAC;QACtC,IAAM,SAAS,GAA2B,EAAE,CAAC;QAE7C,WAAW,CAAC,OAAO,CAAC,UAAC,UAAU,EAAE,KAAK;YACpC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC;gBACjC,SAAS,CAAC,UAAG,KAAK,eAAY,CAAC,GAAG,wBAAwB,CAAC;YAC7D,CAAC;YACD,IAAI,UAAU,CAAC,UAAU,GAAG,CAAC,IAAI,UAAU,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC;gBAC5D,SAAS,CAAC,UAAG,KAAK,gBAAa,CAAC,GAAG,iCAAiC,CAAC;YACvE,CAAC;YACD,IAAI,UAAU,CAAC,eAAe,GAAG,CAAC,IAAI,UAAU,CAAC,eAAe,GAAG,EAAE,EAAE,CAAC;gBACtE,SAAS,CAAC,UAAG,KAAK,qBAAkB,CAAC,GAAG,qCAAqC,CAAC;YAChF,CAAC;YACD,IAAI,UAAU,CAAC,iBAAiB,IAAI,UAAU,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;gBACrE,SAAS,CAAC,UAAG,KAAK,uBAAoB,CAAC,GAAG,wCAAwC,CAAC;YACrF,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,SAAS,CAAC,SAAS,CAAC,CAAC;QACrB,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;IAC7C,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;IAElB,IAAM,YAAY,GAAG,IAAA,mBAAW,EAAC,UAAO,CAAkB;;;;;oBACxD,CAAC,CAAC,cAAc,EAAE,CAAC;oBAEnB,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;wBAC3B,cAAK,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;wBAChD,sBAAO;oBACT,CAAC;oBAED,eAAe,CAAC,IAAI,CAAC,CAAC;oBACtB,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,yBAAyB;;;;oBAGjC,qBAAM,QAAQ,CAAC,WAAW,CAAC,EAAA;;oBAApC,MAAM,GAAG,SAA2B;oBAE1C,2CAA2C;oBAC3C,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;wBAChE,iBAAiB,CAAC,MAAwB,CAAC,CAAC;wBAE5C,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;4BACnB,0DAA0D;4BAC1D,IAAI,IAAI,KAAK,QAAQ,IAAI,CAAC,qBAAqB,EAAE,CAAC;gCAChD,iBAAiB,CAAC,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;gCAC7C,cAAK,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;4BAC5D,CAAC;iCAAM,CAAC;gCACN,0EAA0E;gCAC1E,cAAK,CAAC,OAAO,CAAC,mEAAmE,CAAC,CAAC;4BACrF,CAAC;wBACH,CAAC;6BAAM,CAAC;4BACN,4DAA4D;4BAC5D,cAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,IAAI,8BAA8B,CAAC,CAAC;wBAC9D,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,yBAAyB;wBACzB,IAAI,IAAI,KAAK,QAAQ,IAAI,CAAC,qBAAqB,EAAE,CAAC;4BAChD,iBAAiB,CAAC,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;4BAC7C,cAAK,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC;wBAC5D,CAAC;6BAAM,CAAC;4BACN,cAAK,CAAC,OAAO,CAAC,mEAAmE,CAAC,CAAC;wBACrF,CAAC;oBACH,CAAC;;;;oBAED,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,OAAK,CAAC,CAAC;oBACtD,iBAAiB,CAAC;wBAChB,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B;wBAC9E,SAAS,EAAE,cAAc;wBACzB,SAAS,EAAE,IAAI;qBAChB,CAAC,CAAC;oBACH,cAAK,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;;;oBAE5C,eAAe,CAAC,KAAK,CAAC,CAAC;;;;;SAE1B,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,qBAAqB,EAAE,iBAAiB,CAAC,CAAC,CAAC;IAEjG,IAAM,cAAc,GAAG,IAAA,mBAAW,EAAC,UAAC,MAAc;QAChD,IAAI,MAAM,IAAI,CAAC;YAAE,OAAO,UAAU,CAAC;QACnC,IAAI,MAAM,IAAI,CAAC;YAAE,OAAO,OAAO,CAAC;QAChC,IAAI,MAAM,IAAI,CAAC;YAAE,OAAO,cAAc,CAAC;QACvC,IAAI,MAAM,IAAI,CAAC;YAAE,OAAO,UAAU,CAAC;QACnC,OAAO,QAAQ,CAAC;IAClB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAM,cAAc,GAAG,IAAA,mBAAW,EAAC,UAAC,MAAc;QAChD,IAAI,MAAM,IAAI,CAAC;YAAE,OAAO,YAAY,CAAC;QACrC,IAAI,MAAM,IAAI,CAAC;YAAE,OAAO,eAAe,CAAC;QACxC,IAAI,MAAM,IAAI,CAAC;YAAE,OAAO,eAAe,CAAC;QACxC,IAAI,MAAM,IAAI,CAAC;YAAE,OAAO,aAAa,CAAC;QACtC,OAAO,cAAc,CAAC;IACxB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,CACL,iCAAK,SAAS,EAAC,WAAW,aAEvB,cAAc,IAAI,CACjB,uBAAC,+BAAqB,IACpB,MAAM,EAAE,cAAc,EACtB,OAAO,EAAE;oBACP,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBACxB,4BAA4B;oBAC5B,YAAY,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAQ,CAAC,CAAC;gBAC3C,CAAC,EACD,gBAAgB,EAAE,UAAC,WAAW;oBAC5B,gCAAgC;oBAChC,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,CAAC;wBACpC,gCAAgC;wBAChC,IAAM,aAAa,GAAG,qBAAqB,EAAE,CAAC;wBAC9C,aAAa,CAAC,SAAS,GAAG,WAAW,CAAC;wBACtC,iBAAiB,iCAAK,WAAW,UAAE,aAAa,UAAE,CAAC;oBACrD,CAAC;oBACD,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBAC1B,CAAC,EACD,aAAa,EAAE;oBACb,iCAAiC;oBACjC,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBACxB,cAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACpC,CAAC,GACD,CACH,EAED,wBAAC,WAAI,eACH,wBAAC,iBAAU,eACT,wBAAC,gBAAS,IAAC,SAAS,EAAC,yBAAyB,aAC5C,uBAAC,mBAAI,IAAC,SAAS,EAAC,SAAS,GAAG,wBAElB,EACZ,uBAAC,sBAAe,gGAEE,IACP,EACb,uBAAC,kBAAW,cACV,kCAAM,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAC,WAAW,aAEhD,aAAa,IAAI,CAChB,iCAAK,SAAS,EAAC,WAAW,aACxB,uBAAC,aAAK,IAAC,OAAO,EAAC,cAAc,8BAAsB,EACnD,iCAAK,SAAS,EAAC,UAAU,aACvB,uBAAC,aAAK,IACJ,EAAE,EAAC,cAAc,EACjB,WAAW,EAAC,gCAAgC,EAC5C,KAAK,EAAE,WAAW,EAClB,QAAQ,EAAE,UAAC,CAAC,IAAK,OAAA,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAA9B,CAA8B,GAC/C,EACD,WAAW,IAAI,CACd,uBAAC,sBAAO,IAAC,SAAS,EAAC,6CAA6C,GAAG,CACpE,IACG,EAEL,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,CAC3B,gCAAK,SAAS,EAAC,mEAAmE,YAC/E,aAAa,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,CAC5B,oCAEE,IAAI,EAAC,QAAQ,EACb,SAAS,EAAC,gDAAgD,EAC1D,OAAO,EAAE;oDACP,sDAAsD;oDACtD,IAAM,UAAU,GAAG,WAAW,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,EAAzC,CAAyC,CAAC,CAAC;oDACzF,IAAM,WAAW,GAAG,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;oDAC5E,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;gDAClC,CAAC,aAED,gCAAK,SAAS,EAAC,aAAa,YAAE,KAAK,CAAC,IAAI,GAAO,EAC/C,gCAAK,SAAS,EAAC,uBAAuB,YAAE,KAAK,CAAC,QAAQ,GAAO,EAC5D,KAAK,CAAC,WAAW,IAAI,CACpB,gCAAK,SAAS,EAAC,4BAA4B,YAAE,KAAK,CAAC,WAAW,GAAO,CACtE,KAdI,KAAK,CAAC,EAAE,CAeN,CACV,EAlB6B,CAkB7B,CAAC,GACE,CACP,IACG,CACP,EAGA,WAAW,CAAC,GAAG,CAAC,UAAC,UAAU,EAAE,KAAK,IAAK,OAAA,CACtC,wBAAC,WAAI,IAAwC,SAAS,EAAC,8BAA8B,aACnF,uBAAC,iBAAU,IAAC,SAAS,EAAC,MAAM,YAC1B,iCAAK,SAAS,EAAC,mCAAmC,aAChD,wBAAC,gBAAS,IAAC,SAAS,EAAC,SAAS,4BAChB,KAAK,GAAG,CAAC,IACX,EACX,IAAI,KAAK,MAAM,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,CAC5C,uBAAC,eAAM,IACL,IAAI,EAAC,QAAQ,EACb,OAAO,EAAC,SAAS,EACjB,IAAI,EAAC,IAAI,EACT,OAAO,EAAE,cAAM,OAAA,gBAAgB,CAAC,KAAK,CAAC,EAAvB,CAAuB,uBAG/B,CACV,IACG,GACK,EACb,wBAAC,kBAAW,IAAC,SAAS,EAAC,WAAW,aAEhC,iCAAK,SAAS,EAAC,WAAW,aACxB,uBAAC,aAAK,IAAC,OAAO,EAAE,qBAAc,KAAK,CAAE,6BAAsB,EAC3D,uBAAC,aAAK,IACJ,EAAE,EAAE,qBAAc,KAAK,CAAE,EACzB,WAAW,EAAC,iCAAiC,EAC7C,KAAK,EAAE,UAAU,CAAC,SAAS,EAC3B,QAAQ,EAAE,UAAC,CAAC,IAAK,OAAA,gBAAgB,CAAC,KAAK,EAAE,WAAW,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAApD,CAAoD,EACrE,SAAS,EAAE,MAAM,CAAC,UAAG,KAAK,eAAY,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,GAC/D,EACD,MAAM,CAAC,UAAG,KAAK,eAAY,CAAC,IAAI,CAC/B,8BAAG,SAAS,EAAC,sBAAsB,YAAE,MAAM,CAAC,UAAG,KAAK,eAAY,CAAC,GAAK,CACvE,IACG,EAGN,iCAAK,SAAS,EAAC,WAAW,aACxB,wBAAC,aAAK,gCAAe,UAAU,CAAC,UAAU,WAAY,EACtD,iCAAK,SAAS,EAAC,WAAW,aACxB,uBAAC,eAAM,IACL,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAC9B,aAAa,EAAE,UAAC,KAAK,IAAK,OAAA,gBAAgB,CAAC,KAAK,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAA/C,CAA+C,EACzE,GAAG,EAAE,EAAE,EACP,GAAG,EAAE,CAAC,EACN,IAAI,EAAE,CAAC,EACP,SAAS,EAAC,QAAQ,GAClB,EACF,iCAAK,SAAS,EAAC,4CAA4C,aACzD,wDAAqB,EACrB,sDAAmB,IACf,EACN,uBAAC,aAAK,IACJ,OAAO,EAAC,WAAW,EACnB,SAAS,EAAE,UAAG,cAAc,CAAC,UAAU,CAAC,UAAU,CAAC,gBAAa,YAE/D,cAAc,CAAC,UAAU,CAAC,UAAU,CAAC,GAChC,IACJ,IACF,EAGN,iCAAK,SAAS,EAAC,WAAW,aACxB,wBAAC,aAAK,qCAAoB,UAAU,CAAC,eAAe,WAAY,EAChE,iCAAK,SAAS,EAAC,WAAW,aACxB,uBAAC,eAAM,IACL,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EACnC,aAAa,EAAE,UAAC,KAAK,IAAK,OAAA,gBAAgB,CAAC,KAAK,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAApD,CAAoD,EAC9E,GAAG,EAAE,EAAE,EACP,GAAG,EAAE,CAAC,EACN,IAAI,EAAE,CAAC,EACP,SAAS,EAAC,QAAQ,GAClB,EACF,iCAAK,SAAS,EAAC,4CAA4C,aACzD,6DAA0B,EAC1B,8DAA2B,IACvB,IACF,IACF,EAGN,iCAAK,SAAS,EAAC,wBAAwB,aACrC,iCAAK,SAAS,EAAC,WAAW,aACxB,uBAAC,aAAK,IAAC,OAAO,EAAE,gBAAS,KAAK,CAAE,oCAA6B,EAC7D,uBAAC,aAAK,IACJ,EAAE,EAAE,gBAAS,KAAK,CAAE,EACpB,IAAI,EAAC,QAAQ,EACb,GAAG,EAAC,GAAG,EACP,GAAG,EAAC,IAAI,EACR,WAAW,EAAC,GAAG,EACf,KAAK,EAAE,UAAU,CAAC,iBAAiB,IAAI,EAAE,EACzC,QAAQ,EAAE,UAAC,CAAC,IAAK,OAAA,gBAAgB,CAAC,KAAK,EAAE,mBAAmB,EAAE,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAA3E,CAA2E,GAC5F,IACE,EAEN,iCAAK,SAAS,EAAC,WAAW,aACxB,uBAAC,aAAK,IAAC,OAAO,EAAE,oBAAa,KAAK,CAAE,0BAAmB,EACvD,uBAAC,aAAK,IACJ,EAAE,EAAE,oBAAa,KAAK,CAAE,EACxB,WAAW,EAAC,uBAAuB,EACnC,KAAK,EAAE,UAAU,CAAC,QAAQ,IAAI,EAAE,EAChC,QAAQ,EAAE,UAAC,CAAC,IAAK,OAAA,gBAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAnD,CAAmD,GACpE,IACE,IACF,EAGN,iCAAK,SAAS,EAAC,WAAW,aACxB,uBAAC,aAAK,IAAC,OAAO,EAAE,gBAAS,KAAK,CAAE,iCAA0B,EAC1D,uBAAC,mBAAQ,IACP,EAAE,EAAE,gBAAS,KAAK,CAAE,EACpB,WAAW,EAAC,iEAAiE,EAC7E,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,EAAE,EAC7B,QAAQ,EAAE,UAAC,CAAC,IAAK,OAAA,gBAAgB,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAhD,CAAgD,EACjE,IAAI,EAAE,CAAC,GACP,IACE,IACM,KAnHJ,UAAkB,CAAC,GAAG,IAAI,KAAK,CAoHpC,CACR,EAtHuC,CAsHvC,CAAC,EAGD,IAAI,KAAK,MAAM,IAAI,WAAW,CAAC,MAAM,GAAG,cAAc,IAAI,CACzD,uBAAC,eAAM,IACL,IAAI,EAAC,QAAQ,EACb,OAAO,EAAC,SAAS,EACjB,OAAO,EAAE,aAAa,EACtB,SAAS,EAAC,QAAQ,6CAGX,CACV,EAGD,gCAAK,SAAS,EAAC,4BAA4B,YACzC,uBAAC,eAAM,IACL,IAAI,EAAC,QAAQ,EACb,QAAQ,EAAE,YAAY,EACtB,SAAS,EAAC,UAAU,YAEnB,YAAY,CAAC,CAAC,CAAC,CACd,6DACE,uBAAC,sBAAO,IAAC,SAAS,EAAC,2BAA2B,GAAG,qBAEhD,CACJ,CAAC,CAAC,CAAC,CACF,mBAAmB,CACpB,GACM,GACL,IACD,GACK,IACT,EAGN,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,CACzB,uBAAC,WAAI,cACH,wBAAC,kBAAW,IAAC,SAAS,EAAC,MAAM,aAC3B,iCAAK,SAAS,EAAC,8DAA8D,aAC3E,mEAAgC,EAChC,6CAAO,WAAW,CAAC,MAAM,SAAK,cAAc,eAAe,IACvD,EACN,uBAAC,mBAAQ,IAAC,KAAK,EAAE,CAAC,WAAW,CAAC,MAAM,GAAG,cAAc,CAAC,GAAG,GAAG,EAAE,SAAS,EAAC,KAAK,GAAG,IACpE,GACT,CACR,IACG,CACP,CAAC;AACJ,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/components/skills/SkillAssessmentForm.tsx"],
      sourcesContent: ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Slider } from '@/components/ui/slider';\nimport { Badge } from '@/components/ui/badge';\nimport { Progress } from '@/components/ui/progress';\nimport { Loader2, Star } from 'lucide-react';\nimport { toast } from 'sonner';\nimport EdgeCaseResultHandler, { EdgeCaseResult } from '@/components/common/EdgeCaseResultHandler';\n\ninterface Skill {\n  id: string;\n  name: string;\n  category: string;\n  description?: string;\n}\n\ninterface SkillAssessment {\n  skillId: string;\n  skillName: string;\n  selfRating: number;\n  confidenceLevel: number;\n  assessmentType: 'SELF_ASSESSMENT' | 'PEER_VALIDATION' | 'CERTIFICATION' | 'PERFORMANCE_BASED' | 'AI_EVALUATED';\n  notes?: string;\n  yearsOfExperience?: number;\n  lastUsed?: string;\n}\n\ninterface SkillAssessmentFormProps {\n  skills?: Skill[];\n  onSubmit: (assessments: SkillAssessment[]) => Promise<void | EdgeCaseResult>;\n  onSkillSearch?: (query: string) => Promise<Skill[]>;\n  initialAssessments?: SkillAssessment[];\n  mode?: 'single' | 'bulk';\n  maxAssessments?: number;\n  preserveStateOnSubmit?: boolean; // New prop to control form reset behavior\n  onAssessmentsChange?: (assessments: SkillAssessment[]) => void; // Callback for external state management\n}\n\nexport default function SkillAssessmentForm({\n  onSubmit,\n  onSkillSearch,\n  initialAssessments = [],\n  mode = 'single',\n  maxAssessments = 20,\n  preserveStateOnSubmit = false,\n  onAssessmentsChange,\n}: SkillAssessmentFormProps) {\n  const [assessments, setAssessments] = useState<SkillAssessment[]>(\n    initialAssessments.length > 0 ? initialAssessments : [createEmptyAssessment()]\n  );\n  const [edgeCaseResult, setEdgeCaseResult] = useState<EdgeCaseResult | null>(null);\n\n  // Sync internal state with initialAssessments prop when it changes\n  useEffect(() => {\n    if (initialAssessments.length > 0) {\n      setAssessments(initialAssessments);\n    }\n  }, [initialAssessments]);\n\n  // Update assessments and notify parent if callback is provided\n  const updateAssessments = useCallback((newAssessments: SkillAssessment[]) => {\n    setAssessments(newAssessments);\n    if (onAssessmentsChange) {\n      onAssessmentsChange(newAssessments);\n    }\n  }, [onAssessmentsChange]);\n\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [searchResults, setSearchResults] = useState<Skill[]>([]);\n  const [isSearching, setIsSearching] = useState(false);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  function createEmptyAssessment(): SkillAssessment & { _id: string } {\n    return {\n      _id: `assessment-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,\n      skillId: '',\n      skillName: '',\n      selfRating: 5,\n      confidenceLevel: 5,\n      assessmentType: 'SELF_ASSESSMENT',\n      notes: '',\n      yearsOfExperience: 0,\n      lastUsed: '',\n    };\n  }\n\n  // Search for skills\n  useEffect(() => {\n    const searchSkills = async () => {\n      if (!searchQuery.trim() || !onSkillSearch) {\n        setSearchResults([]);\n        return;\n      }\n\n      setIsSearching(true);\n      try {\n        const results = await onSkillSearch(searchQuery);\n        setSearchResults(results);\n      } catch (error) {\n        console.error('Error searching skills:', error);\n        toast.error('Failed to search skills');\n      } finally {\n        setIsSearching(false);\n      }\n    };\n\n    const debounceTimer = setTimeout(searchSkills, 300);\n    return () => clearTimeout(debounceTimer);\n  }, [searchQuery, onSkillSearch]);\n\n  const addAssessment = useCallback(() => {\n    if (assessments.length >= maxAssessments) {\n      toast.error(`Maximum ${maxAssessments} assessments allowed`);\n      return;\n    }\n    updateAssessments([...assessments, createEmptyAssessment()]);\n  }, [assessments.length, maxAssessments, updateAssessments]);\n\n  const removeAssessment = useCallback((index: number) => {\n    if (assessments.length <= 1) {\n      toast.error('At least one assessment is required');\n      return;\n    }\n    updateAssessments(assessments.filter((_, i) => i !== index));\n  }, [assessments.length, updateAssessments]);\n\n  const selectSkill = useCallback((index: number, skill: Skill) => {\n    // Ensure the index is valid\n    if (index < 0 || index >= assessments.length) {\n      toast.error('Error selecting skill: Invalid assessment index');\n      return;\n    }\n\n    // Create a new assessments array with the updated skill\n    const newAssessments = [...assessments];\n    newAssessments[index] = {\n      ...newAssessments[index],\n      skillName: skill.name,\n      skillId: skill.id && !skill.id.startsWith('common-')\n        ? skill.id\n        : `temp-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`\n    };\n\n    // Update state directly\n    setAssessments(newAssessments);\n    if (onAssessmentsChange) {\n      onAssessmentsChange(newAssessments);\n    }\n\n    setSearchQuery('');\n    setSearchResults([]);\n\n    // Show success feedback\n    toast.success(`Selected skill: ${skill.name} for Assessment ${index + 1}`);\n  }, [assessments, onAssessmentsChange]);\n\n  const updateAssessment = useCallback((index: number, field: keyof SkillAssessment, value: any) => {\n    const updated = [...assessments];\n    updated[index] = { ...updated[index], [field]: value };\n    updateAssessments(updated);\n\n    // Clear error for this field\n    const errorKey = `${index}.${field}`;\n    if (errors[errorKey]) {\n      setErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors[errorKey];\n        return newErrors;\n      });\n    }\n  }, [assessments, updateAssessments, errors]);\n\n\n\n  const validateAssessments = useCallback((): boolean => {\n    const newErrors: Record<string, string> = {};\n\n    assessments.forEach((assessment, index) => {\n      if (!assessment.skillName.trim()) {\n        newErrors[`${index}.skillName`] = 'Skill name is required';\n      }\n      if (assessment.selfRating < 1 || assessment.selfRating > 10) {\n        newErrors[`${index}.selfRating`] = 'Rating must be between 1 and 10';\n      }\n      if (assessment.confidenceLevel < 1 || assessment.confidenceLevel > 10) {\n        newErrors[`${index}.confidenceLevel`] = 'Confidence must be between 1 and 10';\n      }\n      if (assessment.yearsOfExperience && assessment.yearsOfExperience < 0) {\n        newErrors[`${index}.yearsOfExperience`] = 'Years of experience cannot be negative';\n      }\n    });\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  }, [assessments]);\n\n  const handleSubmit = useCallback(async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateAssessments()) {\n      toast.error('Please fix the validation errors');\n      return;\n    }\n\n    setIsSubmitting(true);\n    setEdgeCaseResult(null); // Clear previous results\n\n    try {\n      const result = await onSubmit(assessments);\n\n      // Check if the result is an EdgeCaseResult\n      if (result && typeof result === 'object' && 'success' in result) {\n        setEdgeCaseResult(result as EdgeCaseResult);\n\n        if (result.success) {\n          // Reset form based on mode and preserveStateOnSubmit prop\n          if (mode === 'single' && !preserveStateOnSubmit) {\n            updateAssessments([createEmptyAssessment()]);\n            toast.success('Skill assessment submitted successfully!');\n          } else {\n            // For bulk mode or when preserveStateOnSubmit is true, keep the form data\n            toast.success('Skill assessments saved! You can add more or proceed to analysis.');\n          }\n        } else {\n          // Error case - EdgeCaseResultHandler will display the error\n          toast.error(result.error || 'Failed to submit assessments');\n        }\n      } else {\n        // Legacy response format\n        if (mode === 'single' && !preserveStateOnSubmit) {\n          updateAssessments([createEmptyAssessment()]);\n          toast.success('Skill assessment submitted successfully!');\n        } else {\n          toast.success('Skill assessments saved! You can add more or proceed to analysis.');\n        }\n      }\n    } catch (error) {\n      console.error('Error submitting assessments:', error);\n      setEdgeCaseResult({\n        success: false,\n        error: error instanceof Error ? error.message : 'Failed to submit assessments',\n        errorType: 'SYSTEM_ERROR',\n        retryable: true\n      });\n      toast.error('Failed to submit assessments');\n    } finally {\n      setIsSubmitting(false);\n    }\n  }, [validateAssessments, onSubmit, assessments, mode, preserveStateOnSubmit, updateAssessments]);\n\n  const getRatingLabel = useCallback((rating: number): string => {\n    if (rating <= 2) return 'Beginner';\n    if (rating <= 4) return 'Basic';\n    if (rating <= 6) return 'Intermediate';\n    if (rating <= 8) return 'Advanced';\n    return 'Expert';\n  }, []);\n\n  const getRatingColor = useCallback((rating: number): string => {\n    if (rating <= 2) return 'bg-red-500';\n    if (rating <= 4) return 'bg-orange-500';\n    if (rating <= 6) return 'bg-yellow-500';\n    if (rating <= 8) return 'bg-blue-500';\n    return 'bg-green-500';\n  }, []);\n\n  return (\n    <div className=\"space-y-6\">\n      {/* EdgeCase Result Handler */}\n      {edgeCaseResult && (\n        <EdgeCaseResultHandler\n          result={edgeCaseResult}\n          onRetry={() => {\n            setEdgeCaseResult(null);\n            // Retry the last submission\n            handleSubmit(new Event('submit') as any);\n          }}\n          onUseAlternative={(alternative) => {\n            // Handle suggested alternatives\n            if (typeof alternative === 'string') {\n              // Add as a new skill assessment\n              const newAssessment = createEmptyAssessment();\n              newAssessment.skillName = alternative;\n              updateAssessments([...assessments, newAssessment]);\n            }\n            setEdgeCaseResult(null);\n          }}\n          onUseFallback={() => {\n            // Use fallback data if available\n            setEdgeCaseResult(null);\n            toast.info('Using fallback data');\n          }}\n        />\n      )}\n\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Star className=\"h-5 w-5\" />\n            Skill Assessment\n          </CardTitle>\n          <CardDescription>\n            Assess your current skill levels to get personalized learning recommendations\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Skill Search */}\n            {onSkillSearch && (\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"skill-search\">Search Skills</Label>\n                <div className=\"relative\">\n                  <Input\n                    id=\"skill-search\"\n                    placeholder=\"Search for skills to assess...\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                  />\n                  {isSearching && (\n                    <Loader2 className=\"absolute right-3 top-3 h-4 w-4 animate-spin\" />\n                  )}\n                </div>\n                \n                {searchResults.length > 0 && (\n                  <div className=\"border rounded-md p-2 bg-white shadow-sm max-h-40 overflow-y-auto\">\n                    {searchResults.map((skill) => (\n                      <button\n                        key={skill.id}\n                        type=\"button\"\n                        className=\"w-full text-left p-2 hover:bg-gray-100 rounded\"\n                        onClick={() => {\n                          // Find the first empty assessment or use the last one\n                          const emptyIndex = assessments.findIndex(a => !a.skillName || a.skillName.trim() === '');\n                          const targetIndex = emptyIndex !== -1 ? emptyIndex : assessments.length - 1;\n                          selectSkill(targetIndex, skill);\n                        }}\n                      >\n                        <div className=\"font-medium\">{skill.name}</div>\n                        <div className=\"text-sm text-gray-500\">{skill.category}</div>\n                        {skill.description && (\n                          <div className=\"text-xs text-gray-400 mt-1\">{skill.description}</div>\n                        )}\n                      </button>\n                    ))}\n                  </div>\n                )}\n              </div>\n            )}\n\n            {/* Assessment Forms */}\n            {assessments.map((assessment, index) => (\n              <Card key={(assessment as any)._id || index} className=\"border-l-4 border-l-blue-500\">\n                <CardHeader className=\"pb-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <CardTitle className=\"text-lg\">\n                      Assessment {index + 1}\n                    </CardTitle>\n                    {mode === 'bulk' && assessments.length > 1 && (\n                      <Button\n                        type=\"button\"\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={() => removeAssessment(index)}\n                      >\n                        Remove\n                      </Button>\n                    )}\n                  </div>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  {/* Skill Name */}\n                  <div className=\"space-y-2\">\n                    <Label htmlFor={`skill-name-${index}`}>Skill Name *</Label>\n                    <Input\n                      id={`skill-name-${index}`}\n                      placeholder=\"e.g., JavaScript, React, Python\"\n                      value={assessment.skillName}\n                      onChange={(e) => updateAssessment(index, 'skillName', e.target.value)}\n                      className={errors[`${index}.skillName`] ? 'border-red-500' : ''}\n                    />\n                    {errors[`${index}.skillName`] && (\n                      <p className=\"text-sm text-red-500\">{errors[`${index}.skillName`]}</p>\n                    )}\n                  </div>\n\n                  {/* Self Rating */}\n                  <div className=\"space-y-3\">\n                    <Label>Self Rating: {assessment.selfRating}/10</Label>\n                    <div className=\"space-y-2\">\n                      <Slider\n                        value={[assessment.selfRating]}\n                        onValueChange={(value) => updateAssessment(index, 'selfRating', value[0])}\n                        max={10}\n                        min={1}\n                        step={1}\n                        className=\"w-full\"\n                      />\n                      <div className=\"flex justify-between text-xs text-gray-500\">\n                        <span>Beginner</span>\n                        <span>Expert</span>\n                      </div>\n                      <Badge \n                        variant=\"secondary\" \n                        className={`${getRatingColor(assessment.selfRating)} text-white`}\n                      >\n                        {getRatingLabel(assessment.selfRating)}\n                      </Badge>\n                    </div>\n                  </div>\n\n                  {/* Confidence Level */}\n                  <div className=\"space-y-3\">\n                    <Label>Confidence Level: {assessment.confidenceLevel}/10</Label>\n                    <div className=\"space-y-2\">\n                      <Slider\n                        value={[assessment.confidenceLevel]}\n                        onValueChange={(value) => updateAssessment(index, 'confidenceLevel', value[0])}\n                        max={10}\n                        min={1}\n                        step={1}\n                        className=\"w-full\"\n                      />\n                      <div className=\"flex justify-between text-xs text-gray-500\">\n                        <span>Not Confident</span>\n                        <span>Very Confident</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Years of Experience */}\n                  <div className=\"grid grid-cols-2 gap-4\">\n                    <div className=\"space-y-2\">\n                      <Label htmlFor={`years-${index}`}>Years of Experience</Label>\n                      <Input\n                        id={`years-${index}`}\n                        type=\"number\"\n                        min=\"0\"\n                        max=\"50\"\n                        placeholder=\"0\"\n                        value={assessment.yearsOfExperience || ''}\n                        onChange={(e) => updateAssessment(index, 'yearsOfExperience', parseInt(e.target.value) || 0)}\n                      />\n                    </div>\n                    \n                    <div className=\"space-y-2\">\n                      <Label htmlFor={`last-used-${index}`}>Last Used</Label>\n                      <Input\n                        id={`last-used-${index}`}\n                        placeholder=\"e.g., Currently using\"\n                        value={assessment.lastUsed || ''}\n                        onChange={(e) => updateAssessment(index, 'lastUsed', e.target.value)}\n                      />\n                    </div>\n                  </div>\n\n                  {/* Notes */}\n                  <div className=\"space-y-2\">\n                    <Label htmlFor={`notes-${index}`}>Notes (Optional)</Label>\n                    <Textarea\n                      id={`notes-${index}`}\n                      placeholder=\"Any additional context about your experience with this skill...\"\n                      value={assessment.notes || ''}\n                      onChange={(e) => updateAssessment(index, 'notes', e.target.value)}\n                      rows={3}\n                    />\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n\n            {/* Add Assessment Button */}\n            {mode === 'bulk' && assessments.length < maxAssessments && (\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={addAssessment}\n                className=\"w-full\"\n              >\n                Add Another Skill Assessment\n              </Button>\n            )}\n\n            {/* Submit Button */}\n            <div className=\"flex justify-end space-x-4\">\n              <Button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"min-w-32\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Submitting...\n                  </>\n                ) : (\n                  'Submit Assessment'\n                )}\n              </Button>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n\n      {/* Progress Indicator */}\n      {assessments.length > 1 && (\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center justify-between text-sm text-gray-600 mb-2\">\n              <span>Assessment Progress</span>\n              <span>{assessments.length} / {maxAssessments} skills</span>\n            </div>\n            <Progress value={(assessments.length / maxAssessments) * 100} className=\"h-2\" />\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "3510abe02f2148b19491fbd6131d3877186c6d88"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_83fadhtc9 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_83fadhtc9();
var __assign =
/* istanbul ignore next */
(cov_83fadhtc9().s[0]++,
/* istanbul ignore next */
(cov_83fadhtc9().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_83fadhtc9().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_83fadhtc9().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_83fadhtc9().f[0]++;
  cov_83fadhtc9().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_83fadhtc9().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_83fadhtc9().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_83fadhtc9().f[1]++;
    cov_83fadhtc9().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_83fadhtc9().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_83fadhtc9().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_83fadhtc9().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_83fadhtc9().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_83fadhtc9().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_83fadhtc9().b[2][0]++;
          cov_83fadhtc9().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_83fadhtc9().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_83fadhtc9().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_83fadhtc9().s[10]++;
  return __assign.apply(this, arguments);
}));
var __createBinding =
/* istanbul ignore next */
(cov_83fadhtc9().s[11]++,
/* istanbul ignore next */
(cov_83fadhtc9().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_83fadhtc9().b[3][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_83fadhtc9().b[3][2]++, Object.create ?
/* istanbul ignore next */
(cov_83fadhtc9().b[4][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_83fadhtc9().f[2]++;
  cov_83fadhtc9().s[12]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_83fadhtc9().b[5][0]++;
    cov_83fadhtc9().s[13]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_83fadhtc9().b[5][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_83fadhtc9().s[14]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_83fadhtc9().s[15]++;
  if (
  /* istanbul ignore next */
  (cov_83fadhtc9().b[7][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_83fadhtc9().b[7][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_83fadhtc9().b[8][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_83fadhtc9().b[8][1]++,
  /* istanbul ignore next */
  (cov_83fadhtc9().b[9][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_83fadhtc9().b[9][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_83fadhtc9().b[6][0]++;
    cov_83fadhtc9().s[16]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_83fadhtc9().f[3]++;
        cov_83fadhtc9().s[17]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_83fadhtc9().b[6][1]++;
  }
  cov_83fadhtc9().s[18]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_83fadhtc9().b[4][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_83fadhtc9().f[4]++;
  cov_83fadhtc9().s[19]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_83fadhtc9().b[10][0]++;
    cov_83fadhtc9().s[20]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_83fadhtc9().b[10][1]++;
  }
  cov_83fadhtc9().s[21]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_83fadhtc9().s[22]++,
/* istanbul ignore next */
(cov_83fadhtc9().b[11][0]++, this) &&
/* istanbul ignore next */
(cov_83fadhtc9().b[11][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_83fadhtc9().b[11][2]++, Object.create ?
/* istanbul ignore next */
(cov_83fadhtc9().b[12][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_83fadhtc9().f[5]++;
  cov_83fadhtc9().s[23]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_83fadhtc9().b[12][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_83fadhtc9().f[6]++;
  cov_83fadhtc9().s[24]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_83fadhtc9().s[25]++,
/* istanbul ignore next */
(cov_83fadhtc9().b[13][0]++, this) &&
/* istanbul ignore next */
(cov_83fadhtc9().b[13][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_83fadhtc9().b[13][2]++, function () {
  /* istanbul ignore next */
  cov_83fadhtc9().f[7]++;
  cov_83fadhtc9().s[26]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_83fadhtc9().f[8]++;
    cov_83fadhtc9().s[27]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_83fadhtc9().b[14][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_83fadhtc9().b[14][1]++, function (o) {
      /* istanbul ignore next */
      cov_83fadhtc9().f[9]++;
      var ar =
      /* istanbul ignore next */
      (cov_83fadhtc9().s[28]++, []);
      /* istanbul ignore next */
      cov_83fadhtc9().s[29]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_83fadhtc9().s[30]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_83fadhtc9().b[15][0]++;
          cov_83fadhtc9().s[31]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_83fadhtc9().b[15][1]++;
        }
      }
      /* istanbul ignore next */
      cov_83fadhtc9().s[32]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_83fadhtc9().s[33]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_83fadhtc9().s[34]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_83fadhtc9().f[10]++;
    cov_83fadhtc9().s[35]++;
    if (
    /* istanbul ignore next */
    (cov_83fadhtc9().b[17][0]++, mod) &&
    /* istanbul ignore next */
    (cov_83fadhtc9().b[17][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[16][0]++;
      cov_83fadhtc9().s[36]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[16][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[37]++, {});
    /* istanbul ignore next */
    cov_83fadhtc9().s[38]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[18][0]++;
      cov_83fadhtc9().s[39]++;
      for (var k =
        /* istanbul ignore next */
        (cov_83fadhtc9().s[40]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_83fadhtc9().s[41]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_83fadhtc9().s[42]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_83fadhtc9().b[19][0]++;
          cov_83fadhtc9().s[43]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_83fadhtc9().b[19][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[18][1]++;
    }
    cov_83fadhtc9().s[44]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_83fadhtc9().s[45]++;
    return result;
  };
}()));
var __awaiter =
/* istanbul ignore next */
(cov_83fadhtc9().s[46]++,
/* istanbul ignore next */
(cov_83fadhtc9().b[20][0]++, this) &&
/* istanbul ignore next */
(cov_83fadhtc9().b[20][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_83fadhtc9().b[20][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_83fadhtc9().f[11]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_83fadhtc9().f[12]++;
    cov_83fadhtc9().s[47]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_83fadhtc9().b[21][0]++, value) :
    /* istanbul ignore next */
    (cov_83fadhtc9().b[21][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_83fadhtc9().f[13]++;
      cov_83fadhtc9().s[48]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_83fadhtc9().s[49]++;
  return new (
  /* istanbul ignore next */
  (cov_83fadhtc9().b[22][0]++, P) ||
  /* istanbul ignore next */
  (cov_83fadhtc9().b[22][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_83fadhtc9().f[14]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_83fadhtc9().f[15]++;
      cov_83fadhtc9().s[50]++;
      try {
        /* istanbul ignore next */
        cov_83fadhtc9().s[51]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_83fadhtc9().s[52]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_83fadhtc9().f[16]++;
      cov_83fadhtc9().s[53]++;
      try {
        /* istanbul ignore next */
        cov_83fadhtc9().s[54]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_83fadhtc9().s[55]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_83fadhtc9().f[17]++;
      cov_83fadhtc9().s[56]++;
      result.done ?
      /* istanbul ignore next */
      (cov_83fadhtc9().b[23][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_83fadhtc9().b[23][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_83fadhtc9().s[57]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_83fadhtc9().b[24][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_83fadhtc9().b[24][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_83fadhtc9().s[58]++,
/* istanbul ignore next */
(cov_83fadhtc9().b[25][0]++, this) &&
/* istanbul ignore next */
(cov_83fadhtc9().b[25][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_83fadhtc9().b[25][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_83fadhtc9().f[18]++;
  var _ =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[59]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_83fadhtc9().f[19]++;
        cov_83fadhtc9().s[60]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_83fadhtc9().b[26][0]++;
          cov_83fadhtc9().s[61]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_83fadhtc9().b[26][1]++;
        }
        cov_83fadhtc9().s[62]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[63]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_83fadhtc9().b[27][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_83fadhtc9().b[27][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_83fadhtc9().s[64]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_83fadhtc9().b[28][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_83fadhtc9().b[28][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_83fadhtc9().f[20]++;
    cov_83fadhtc9().s[65]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_83fadhtc9().f[21]++;
    cov_83fadhtc9().s[66]++;
    return function (v) {
      /* istanbul ignore next */
      cov_83fadhtc9().f[22]++;
      cov_83fadhtc9().s[67]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_83fadhtc9().f[23]++;
    cov_83fadhtc9().s[68]++;
    if (f) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[29][0]++;
      cov_83fadhtc9().s[69]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[29][1]++;
    }
    cov_83fadhtc9().s[70]++;
    while (
    /* istanbul ignore next */
    (cov_83fadhtc9().b[30][0]++, g) &&
    /* istanbul ignore next */
    (cov_83fadhtc9().b[30][1]++, g = 0,
    /* istanbul ignore next */
    (cov_83fadhtc9().b[31][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_83fadhtc9().b[31][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_83fadhtc9().s[71]++;
      try {
        /* istanbul ignore next */
        cov_83fadhtc9().s[72]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_83fadhtc9().b[33][0]++, y) &&
        /* istanbul ignore next */
        (cov_83fadhtc9().b[33][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_83fadhtc9().b[34][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_83fadhtc9().b[34][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_83fadhtc9().b[35][0]++,
        /* istanbul ignore next */
        (cov_83fadhtc9().b[36][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_83fadhtc9().b[36][1]++,
        /* istanbul ignore next */
        (cov_83fadhtc9().b[37][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_83fadhtc9().b[37][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_83fadhtc9().b[35][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_83fadhtc9().b[33][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_83fadhtc9().b[32][0]++;
          cov_83fadhtc9().s[73]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_83fadhtc9().b[32][1]++;
        }
        cov_83fadhtc9().s[74]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_83fadhtc9().b[38][0]++;
          cov_83fadhtc9().s[75]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_83fadhtc9().b[38][1]++;
        }
        cov_83fadhtc9().s[76]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_83fadhtc9().b[39][0]++;
          case 1:
            /* istanbul ignore next */
            cov_83fadhtc9().b[39][1]++;
            cov_83fadhtc9().s[77]++;
            t = op;
            /* istanbul ignore next */
            cov_83fadhtc9().s[78]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_83fadhtc9().b[39][2]++;
            cov_83fadhtc9().s[79]++;
            _.label++;
            /* istanbul ignore next */
            cov_83fadhtc9().s[80]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_83fadhtc9().b[39][3]++;
            cov_83fadhtc9().s[81]++;
            _.label++;
            /* istanbul ignore next */
            cov_83fadhtc9().s[82]++;
            y = op[1];
            /* istanbul ignore next */
            cov_83fadhtc9().s[83]++;
            op = [0];
            /* istanbul ignore next */
            cov_83fadhtc9().s[84]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_83fadhtc9().b[39][4]++;
            cov_83fadhtc9().s[85]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_83fadhtc9().s[86]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_83fadhtc9().s[87]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_83fadhtc9().b[39][5]++;
            cov_83fadhtc9().s[88]++;
            if (
            /* istanbul ignore next */
            (cov_83fadhtc9().b[41][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_83fadhtc9().b[42][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_83fadhtc9().b[42][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_83fadhtc9().b[41][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_83fadhtc9().b[41][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_83fadhtc9().b[40][0]++;
              cov_83fadhtc9().s[89]++;
              _ = 0;
              /* istanbul ignore next */
              cov_83fadhtc9().s[90]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_83fadhtc9().b[40][1]++;
            }
            cov_83fadhtc9().s[91]++;
            if (
            /* istanbul ignore next */
            (cov_83fadhtc9().b[44][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_83fadhtc9().b[44][1]++, !t) ||
            /* istanbul ignore next */
            (cov_83fadhtc9().b[44][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_83fadhtc9().b[44][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_83fadhtc9().b[43][0]++;
              cov_83fadhtc9().s[92]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_83fadhtc9().s[93]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_83fadhtc9().b[43][1]++;
            }
            cov_83fadhtc9().s[94]++;
            if (
            /* istanbul ignore next */
            (cov_83fadhtc9().b[46][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_83fadhtc9().b[46][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_83fadhtc9().b[45][0]++;
              cov_83fadhtc9().s[95]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_83fadhtc9().s[96]++;
              t = op;
              /* istanbul ignore next */
              cov_83fadhtc9().s[97]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_83fadhtc9().b[45][1]++;
            }
            cov_83fadhtc9().s[98]++;
            if (
            /* istanbul ignore next */
            (cov_83fadhtc9().b[48][0]++, t) &&
            /* istanbul ignore next */
            (cov_83fadhtc9().b[48][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_83fadhtc9().b[47][0]++;
              cov_83fadhtc9().s[99]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_83fadhtc9().s[100]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_83fadhtc9().s[101]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_83fadhtc9().b[47][1]++;
            }
            cov_83fadhtc9().s[102]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_83fadhtc9().b[49][0]++;
              cov_83fadhtc9().s[103]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_83fadhtc9().b[49][1]++;
            }
            cov_83fadhtc9().s[104]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_83fadhtc9().s[105]++;
            continue;
        }
        /* istanbul ignore next */
        cov_83fadhtc9().s[106]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_83fadhtc9().s[107]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_83fadhtc9().s[108]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_83fadhtc9().s[109]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_83fadhtc9().s[110]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[50][0]++;
      cov_83fadhtc9().s[111]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[50][1]++;
    }
    cov_83fadhtc9().s[112]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_83fadhtc9().b[51][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_83fadhtc9().b[51][1]++, void 0),
      done: true
    };
  }
}));
var __spreadArray =
/* istanbul ignore next */
(cov_83fadhtc9().s[113]++,
/* istanbul ignore next */
(cov_83fadhtc9().b[52][0]++, this) &&
/* istanbul ignore next */
(cov_83fadhtc9().b[52][1]++, this.__spreadArray) ||
/* istanbul ignore next */
(cov_83fadhtc9().b[52][2]++, function (to, from, pack) {
  /* istanbul ignore next */
  cov_83fadhtc9().f[24]++;
  cov_83fadhtc9().s[114]++;
  if (
  /* istanbul ignore next */
  (cov_83fadhtc9().b[54][0]++, pack) ||
  /* istanbul ignore next */
  (cov_83fadhtc9().b[54][1]++, arguments.length === 2)) {
    /* istanbul ignore next */
    cov_83fadhtc9().b[53][0]++;
    cov_83fadhtc9().s[115]++;
    for (var i =
      /* istanbul ignore next */
      (cov_83fadhtc9().s[116]++, 0), l =
      /* istanbul ignore next */
      (cov_83fadhtc9().s[117]++, from.length), ar; i < l; i++) {
      /* istanbul ignore next */
      cov_83fadhtc9().s[118]++;
      if (
      /* istanbul ignore next */
      (cov_83fadhtc9().b[56][0]++, ar) ||
      /* istanbul ignore next */
      (cov_83fadhtc9().b[56][1]++, !(i in from))) {
        /* istanbul ignore next */
        cov_83fadhtc9().b[55][0]++;
        cov_83fadhtc9().s[119]++;
        if (!ar) {
          /* istanbul ignore next */
          cov_83fadhtc9().b[57][0]++;
          cov_83fadhtc9().s[120]++;
          ar = Array.prototype.slice.call(from, 0, i);
        } else
        /* istanbul ignore next */
        {
          cov_83fadhtc9().b[57][1]++;
        }
        cov_83fadhtc9().s[121]++;
        ar[i] = from[i];
      } else
      /* istanbul ignore next */
      {
        cov_83fadhtc9().b[55][1]++;
      }
    }
  } else
  /* istanbul ignore next */
  {
    cov_83fadhtc9().b[53][1]++;
  }
  cov_83fadhtc9().s[122]++;
  return to.concat(
  /* istanbul ignore next */
  (cov_83fadhtc9().b[58][0]++, ar) ||
  /* istanbul ignore next */
  (cov_83fadhtc9().b[58][1]++, Array.prototype.slice.call(from)));
}));
var __importDefault =
/* istanbul ignore next */
(cov_83fadhtc9().s[123]++,
/* istanbul ignore next */
(cov_83fadhtc9().b[59][0]++, this) &&
/* istanbul ignore next */
(cov_83fadhtc9().b[59][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_83fadhtc9().b[59][2]++, function (mod) {
  /* istanbul ignore next */
  cov_83fadhtc9().f[25]++;
  cov_83fadhtc9().s[124]++;
  return /* istanbul ignore next */(cov_83fadhtc9().b[61][0]++, mod) &&
  /* istanbul ignore next */
  (cov_83fadhtc9().b[61][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_83fadhtc9().b[60][0]++, mod) :
  /* istanbul ignore next */
  (cov_83fadhtc9().b[60][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_83fadhtc9().s[125]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_83fadhtc9().s[126]++;
exports.default = SkillAssessmentForm;
var jsx_runtime_1 =
/* istanbul ignore next */
(cov_83fadhtc9().s[127]++, require("react/jsx-runtime"));
var react_1 =
/* istanbul ignore next */
(cov_83fadhtc9().s[128]++, __importStar(require("react")));
var card_1 =
/* istanbul ignore next */
(cov_83fadhtc9().s[129]++, require("@/components/ui/card"));
var button_1 =
/* istanbul ignore next */
(cov_83fadhtc9().s[130]++, require("@/components/ui/button"));
var input_1 =
/* istanbul ignore next */
(cov_83fadhtc9().s[131]++, require("@/components/ui/input"));
var label_1 =
/* istanbul ignore next */
(cov_83fadhtc9().s[132]++, require("@/components/ui/label"));
var textarea_1 =
/* istanbul ignore next */
(cov_83fadhtc9().s[133]++, require("@/components/ui/textarea"));
var slider_1 =
/* istanbul ignore next */
(cov_83fadhtc9().s[134]++, require("@/components/ui/slider"));
var badge_1 =
/* istanbul ignore next */
(cov_83fadhtc9().s[135]++, require("@/components/ui/badge"));
var progress_1 =
/* istanbul ignore next */
(cov_83fadhtc9().s[136]++, require("@/components/ui/progress"));
var lucide_react_1 =
/* istanbul ignore next */
(cov_83fadhtc9().s[137]++, require("lucide-react"));
var sonner_1 =
/* istanbul ignore next */
(cov_83fadhtc9().s[138]++, require("sonner"));
var EdgeCaseResultHandler_1 =
/* istanbul ignore next */
(cov_83fadhtc9().s[139]++, __importDefault(require("@/components/common/EdgeCaseResultHandler")));
function SkillAssessmentForm(_a) {
  /* istanbul ignore next */
  cov_83fadhtc9().f[26]++;
  var _this =
  /* istanbul ignore next */
  (cov_83fadhtc9().s[140]++, this);
  var onSubmit =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[141]++, _a.onSubmit),
    onSkillSearch =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[142]++, _a.onSkillSearch),
    _b =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[143]++, _a.initialAssessments),
    initialAssessments =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[144]++, _b === void 0 ?
    /* istanbul ignore next */
    (cov_83fadhtc9().b[62][0]++, []) :
    /* istanbul ignore next */
    (cov_83fadhtc9().b[62][1]++, _b)),
    _c =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[145]++, _a.mode),
    mode =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[146]++, _c === void 0 ?
    /* istanbul ignore next */
    (cov_83fadhtc9().b[63][0]++, 'single') :
    /* istanbul ignore next */
    (cov_83fadhtc9().b[63][1]++, _c)),
    _d =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[147]++, _a.maxAssessments),
    maxAssessments =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[148]++, _d === void 0 ?
    /* istanbul ignore next */
    (cov_83fadhtc9().b[64][0]++, 20) :
    /* istanbul ignore next */
    (cov_83fadhtc9().b[64][1]++, _d)),
    _e =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[149]++, _a.preserveStateOnSubmit),
    preserveStateOnSubmit =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[150]++, _e === void 0 ?
    /* istanbul ignore next */
    (cov_83fadhtc9().b[65][0]++, false) :
    /* istanbul ignore next */
    (cov_83fadhtc9().b[65][1]++, _e)),
    onAssessmentsChange =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[151]++, _a.onAssessmentsChange);
  var _f =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[152]++, (0, react_1.useState)(initialAssessments.length > 0 ?
    /* istanbul ignore next */
    (cov_83fadhtc9().b[66][0]++, initialAssessments) :
    /* istanbul ignore next */
    (cov_83fadhtc9().b[66][1]++, [createEmptyAssessment()]))),
    assessments =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[153]++, _f[0]),
    setAssessments =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[154]++, _f[1]);
  var _g =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[155]++, (0, react_1.useState)(null)),
    edgeCaseResult =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[156]++, _g[0]),
    setEdgeCaseResult =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[157]++, _g[1]);
  // Sync internal state with initialAssessments prop when it changes
  /* istanbul ignore next */
  cov_83fadhtc9().s[158]++;
  (0, react_1.useEffect)(function () {
    /* istanbul ignore next */
    cov_83fadhtc9().f[27]++;
    cov_83fadhtc9().s[159]++;
    if (initialAssessments.length > 0) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[67][0]++;
      cov_83fadhtc9().s[160]++;
      setAssessments(initialAssessments);
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[67][1]++;
    }
  }, [initialAssessments]);
  // Update assessments and notify parent if callback is provided
  var updateAssessments =
  /* istanbul ignore next */
  (cov_83fadhtc9().s[161]++, (0, react_1.useCallback)(function (newAssessments) {
    /* istanbul ignore next */
    cov_83fadhtc9().f[28]++;
    cov_83fadhtc9().s[162]++;
    setAssessments(newAssessments);
    /* istanbul ignore next */
    cov_83fadhtc9().s[163]++;
    if (onAssessmentsChange) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[68][0]++;
      cov_83fadhtc9().s[164]++;
      onAssessmentsChange(newAssessments);
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[68][1]++;
    }
  }, [onAssessmentsChange]));
  var _h =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[165]++, (0, react_1.useState)(false)),
    isSubmitting =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[166]++, _h[0]),
    setIsSubmitting =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[167]++, _h[1]);
  var _j =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[168]++, (0, react_1.useState)('')),
    searchQuery =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[169]++, _j[0]),
    setSearchQuery =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[170]++, _j[1]);
  var _k =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[171]++, (0, react_1.useState)([])),
    searchResults =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[172]++, _k[0]),
    setSearchResults =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[173]++, _k[1]);
  var _l =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[174]++, (0, react_1.useState)(false)),
    isSearching =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[175]++, _l[0]),
    setIsSearching =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[176]++, _l[1]);
  var _m =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[177]++, (0, react_1.useState)({})),
    errors =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[178]++, _m[0]),
    setErrors =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[179]++, _m[1]);
  function createEmptyAssessment() {
    /* istanbul ignore next */
    cov_83fadhtc9().f[29]++;
    cov_83fadhtc9().s[180]++;
    return {
      _id: "assessment-".concat(Date.now(), "-").concat(Math.random().toString(36).substring(2, 11)),
      skillId: '',
      skillName: '',
      selfRating: 5,
      confidenceLevel: 5,
      assessmentType: 'SELF_ASSESSMENT',
      notes: '',
      yearsOfExperience: 0,
      lastUsed: ''
    };
  }
  // Search for skills
  /* istanbul ignore next */
  cov_83fadhtc9().s[181]++;
  (0, react_1.useEffect)(function () {
    /* istanbul ignore next */
    cov_83fadhtc9().f[30]++;
    cov_83fadhtc9().s[182]++;
    var searchSkills = function () {
      /* istanbul ignore next */
      cov_83fadhtc9().f[31]++;
      cov_83fadhtc9().s[183]++;
      return __awaiter(_this, void 0, void 0, function () {
        /* istanbul ignore next */
        cov_83fadhtc9().f[32]++;
        var results, error_1;
        /* istanbul ignore next */
        cov_83fadhtc9().s[184]++;
        return __generator(this, function (_a) {
          /* istanbul ignore next */
          cov_83fadhtc9().f[33]++;
          cov_83fadhtc9().s[185]++;
          switch (_a.label) {
            case 0:
              /* istanbul ignore next */
              cov_83fadhtc9().b[69][0]++;
              cov_83fadhtc9().s[186]++;
              if (
              /* istanbul ignore next */
              (cov_83fadhtc9().b[71][0]++, !searchQuery.trim()) ||
              /* istanbul ignore next */
              (cov_83fadhtc9().b[71][1]++, !onSkillSearch)) {
                /* istanbul ignore next */
                cov_83fadhtc9().b[70][0]++;
                cov_83fadhtc9().s[187]++;
                setSearchResults([]);
                /* istanbul ignore next */
                cov_83fadhtc9().s[188]++;
                return [2 /*return*/];
              } else
              /* istanbul ignore next */
              {
                cov_83fadhtc9().b[70][1]++;
              }
              cov_83fadhtc9().s[189]++;
              setIsSearching(true);
              /* istanbul ignore next */
              cov_83fadhtc9().s[190]++;
              _a.label = 1;
            case 1:
              /* istanbul ignore next */
              cov_83fadhtc9().b[69][1]++;
              cov_83fadhtc9().s[191]++;
              _a.trys.push([1, 3, 4, 5]);
              /* istanbul ignore next */
              cov_83fadhtc9().s[192]++;
              return [4 /*yield*/, onSkillSearch(searchQuery)];
            case 2:
              /* istanbul ignore next */
              cov_83fadhtc9().b[69][2]++;
              cov_83fadhtc9().s[193]++;
              results = _a.sent();
              /* istanbul ignore next */
              cov_83fadhtc9().s[194]++;
              setSearchResults(results);
              /* istanbul ignore next */
              cov_83fadhtc9().s[195]++;
              return [3 /*break*/, 5];
            case 3:
              /* istanbul ignore next */
              cov_83fadhtc9().b[69][3]++;
              cov_83fadhtc9().s[196]++;
              error_1 = _a.sent();
              /* istanbul ignore next */
              cov_83fadhtc9().s[197]++;
              console.error('Error searching skills:', error_1);
              /* istanbul ignore next */
              cov_83fadhtc9().s[198]++;
              sonner_1.toast.error('Failed to search skills');
              /* istanbul ignore next */
              cov_83fadhtc9().s[199]++;
              return [3 /*break*/, 5];
            case 4:
              /* istanbul ignore next */
              cov_83fadhtc9().b[69][4]++;
              cov_83fadhtc9().s[200]++;
              setIsSearching(false);
              /* istanbul ignore next */
              cov_83fadhtc9().s[201]++;
              return [7 /*endfinally*/];
            case 5:
              /* istanbul ignore next */
              cov_83fadhtc9().b[69][5]++;
              cov_83fadhtc9().s[202]++;
              return [2 /*return*/];
          }
        });
      });
    };
    var debounceTimer =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[203]++, setTimeout(searchSkills, 300));
    /* istanbul ignore next */
    cov_83fadhtc9().s[204]++;
    return function () {
      /* istanbul ignore next */
      cov_83fadhtc9().f[34]++;
      cov_83fadhtc9().s[205]++;
      return clearTimeout(debounceTimer);
    };
  }, [searchQuery, onSkillSearch]);
  var addAssessment =
  /* istanbul ignore next */
  (cov_83fadhtc9().s[206]++, (0, react_1.useCallback)(function () {
    /* istanbul ignore next */
    cov_83fadhtc9().f[35]++;
    cov_83fadhtc9().s[207]++;
    if (assessments.length >= maxAssessments) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[72][0]++;
      cov_83fadhtc9().s[208]++;
      sonner_1.toast.error("Maximum ".concat(maxAssessments, " assessments allowed"));
      /* istanbul ignore next */
      cov_83fadhtc9().s[209]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[72][1]++;
    }
    cov_83fadhtc9().s[210]++;
    updateAssessments(__spreadArray(__spreadArray([], assessments, true), [createEmptyAssessment()], false));
  }, [assessments.length, maxAssessments, updateAssessments]));
  var removeAssessment =
  /* istanbul ignore next */
  (cov_83fadhtc9().s[211]++, (0, react_1.useCallback)(function (index) {
    /* istanbul ignore next */
    cov_83fadhtc9().f[36]++;
    cov_83fadhtc9().s[212]++;
    if (assessments.length <= 1) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[73][0]++;
      cov_83fadhtc9().s[213]++;
      sonner_1.toast.error('At least one assessment is required');
      /* istanbul ignore next */
      cov_83fadhtc9().s[214]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[73][1]++;
    }
    cov_83fadhtc9().s[215]++;
    updateAssessments(assessments.filter(function (_, i) {
      /* istanbul ignore next */
      cov_83fadhtc9().f[37]++;
      cov_83fadhtc9().s[216]++;
      return i !== index;
    }));
  }, [assessments.length, updateAssessments]));
  var selectSkill =
  /* istanbul ignore next */
  (cov_83fadhtc9().s[217]++, (0, react_1.useCallback)(function (index, skill) {
    /* istanbul ignore next */
    cov_83fadhtc9().f[38]++;
    cov_83fadhtc9().s[218]++;
    // Ensure the index is valid
    if (
    /* istanbul ignore next */
    (cov_83fadhtc9().b[75][0]++, index < 0) ||
    /* istanbul ignore next */
    (cov_83fadhtc9().b[75][1]++, index >= assessments.length)) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[74][0]++;
      cov_83fadhtc9().s[219]++;
      sonner_1.toast.error('Error selecting skill: Invalid assessment index');
      /* istanbul ignore next */
      cov_83fadhtc9().s[220]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[74][1]++;
    }
    // Create a new assessments array with the updated skill
    var newAssessments =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[221]++, __spreadArray([], assessments, true));
    /* istanbul ignore next */
    cov_83fadhtc9().s[222]++;
    newAssessments[index] = __assign(__assign({}, newAssessments[index]), {
      skillName: skill.name,
      skillId:
      /* istanbul ignore next */
      (cov_83fadhtc9().b[77][0]++, skill.id) &&
      /* istanbul ignore next */
      (cov_83fadhtc9().b[77][1]++, !skill.id.startsWith('common-')) ?
      /* istanbul ignore next */
      (cov_83fadhtc9().b[76][0]++, skill.id) :
      /* istanbul ignore next */
      (cov_83fadhtc9().b[76][1]++, "temp-".concat(Date.now(), "-").concat(Math.random().toString(36).substring(2, 11)))
    });
    // Update state directly
    /* istanbul ignore next */
    cov_83fadhtc9().s[223]++;
    setAssessments(newAssessments);
    /* istanbul ignore next */
    cov_83fadhtc9().s[224]++;
    if (onAssessmentsChange) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[78][0]++;
      cov_83fadhtc9().s[225]++;
      onAssessmentsChange(newAssessments);
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[78][1]++;
    }
    cov_83fadhtc9().s[226]++;
    setSearchQuery('');
    /* istanbul ignore next */
    cov_83fadhtc9().s[227]++;
    setSearchResults([]);
    // Show success feedback
    /* istanbul ignore next */
    cov_83fadhtc9().s[228]++;
    sonner_1.toast.success("Selected skill: ".concat(skill.name, " for Assessment ").concat(index + 1));
  }, [assessments, onAssessmentsChange]));
  var updateAssessment =
  /* istanbul ignore next */
  (cov_83fadhtc9().s[229]++, (0, react_1.useCallback)(function (index, field, value) {
    /* istanbul ignore next */
    cov_83fadhtc9().f[39]++;
    var _a;
    var updated =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[230]++, __spreadArray([], assessments, true));
    /* istanbul ignore next */
    cov_83fadhtc9().s[231]++;
    updated[index] = __assign(__assign({}, updated[index]), (_a = {}, _a[field] = value, _a));
    /* istanbul ignore next */
    cov_83fadhtc9().s[232]++;
    updateAssessments(updated);
    // Clear error for this field
    var errorKey =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[233]++, "".concat(index, ".").concat(field));
    /* istanbul ignore next */
    cov_83fadhtc9().s[234]++;
    if (errors[errorKey]) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[79][0]++;
      cov_83fadhtc9().s[235]++;
      setErrors(function (prev) {
        /* istanbul ignore next */
        cov_83fadhtc9().f[40]++;
        var newErrors =
        /* istanbul ignore next */
        (cov_83fadhtc9().s[236]++, __assign({}, prev));
        /* istanbul ignore next */
        cov_83fadhtc9().s[237]++;
        delete newErrors[errorKey];
        /* istanbul ignore next */
        cov_83fadhtc9().s[238]++;
        return newErrors;
      });
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[79][1]++;
    }
  }, [assessments, updateAssessments, errors]));
  var validateAssessments =
  /* istanbul ignore next */
  (cov_83fadhtc9().s[239]++, (0, react_1.useCallback)(function () {
    /* istanbul ignore next */
    cov_83fadhtc9().f[41]++;
    var newErrors =
    /* istanbul ignore next */
    (cov_83fadhtc9().s[240]++, {});
    /* istanbul ignore next */
    cov_83fadhtc9().s[241]++;
    assessments.forEach(function (assessment, index) {
      /* istanbul ignore next */
      cov_83fadhtc9().f[42]++;
      cov_83fadhtc9().s[242]++;
      if (!assessment.skillName.trim()) {
        /* istanbul ignore next */
        cov_83fadhtc9().b[80][0]++;
        cov_83fadhtc9().s[243]++;
        newErrors["".concat(index, ".skillName")] = 'Skill name is required';
      } else
      /* istanbul ignore next */
      {
        cov_83fadhtc9().b[80][1]++;
      }
      cov_83fadhtc9().s[244]++;
      if (
      /* istanbul ignore next */
      (cov_83fadhtc9().b[82][0]++, assessment.selfRating < 1) ||
      /* istanbul ignore next */
      (cov_83fadhtc9().b[82][1]++, assessment.selfRating > 10)) {
        /* istanbul ignore next */
        cov_83fadhtc9().b[81][0]++;
        cov_83fadhtc9().s[245]++;
        newErrors["".concat(index, ".selfRating")] = 'Rating must be between 1 and 10';
      } else
      /* istanbul ignore next */
      {
        cov_83fadhtc9().b[81][1]++;
      }
      cov_83fadhtc9().s[246]++;
      if (
      /* istanbul ignore next */
      (cov_83fadhtc9().b[84][0]++, assessment.confidenceLevel < 1) ||
      /* istanbul ignore next */
      (cov_83fadhtc9().b[84][1]++, assessment.confidenceLevel > 10)) {
        /* istanbul ignore next */
        cov_83fadhtc9().b[83][0]++;
        cov_83fadhtc9().s[247]++;
        newErrors["".concat(index, ".confidenceLevel")] = 'Confidence must be between 1 and 10';
      } else
      /* istanbul ignore next */
      {
        cov_83fadhtc9().b[83][1]++;
      }
      cov_83fadhtc9().s[248]++;
      if (
      /* istanbul ignore next */
      (cov_83fadhtc9().b[86][0]++, assessment.yearsOfExperience) &&
      /* istanbul ignore next */
      (cov_83fadhtc9().b[86][1]++, assessment.yearsOfExperience < 0)) {
        /* istanbul ignore next */
        cov_83fadhtc9().b[85][0]++;
        cov_83fadhtc9().s[249]++;
        newErrors["".concat(index, ".yearsOfExperience")] = 'Years of experience cannot be negative';
      } else
      /* istanbul ignore next */
      {
        cov_83fadhtc9().b[85][1]++;
      }
    });
    /* istanbul ignore next */
    cov_83fadhtc9().s[250]++;
    setErrors(newErrors);
    /* istanbul ignore next */
    cov_83fadhtc9().s[251]++;
    return Object.keys(newErrors).length === 0;
  }, [assessments]));
  var handleSubmit =
  /* istanbul ignore next */
  (cov_83fadhtc9().s[252]++, (0, react_1.useCallback)(function (e) {
    /* istanbul ignore next */
    cov_83fadhtc9().f[43]++;
    cov_83fadhtc9().s[253]++;
    return __awaiter(_this, void 0, void 0, function () {
      /* istanbul ignore next */
      cov_83fadhtc9().f[44]++;
      var result, error_2;
      /* istanbul ignore next */
      cov_83fadhtc9().s[254]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_83fadhtc9().f[45]++;
        cov_83fadhtc9().s[255]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_83fadhtc9().b[87][0]++;
            cov_83fadhtc9().s[256]++;
            e.preventDefault();
            /* istanbul ignore next */
            cov_83fadhtc9().s[257]++;
            if (!validateAssessments()) {
              /* istanbul ignore next */
              cov_83fadhtc9().b[88][0]++;
              cov_83fadhtc9().s[258]++;
              sonner_1.toast.error('Please fix the validation errors');
              /* istanbul ignore next */
              cov_83fadhtc9().s[259]++;
              return [2 /*return*/];
            } else
            /* istanbul ignore next */
            {
              cov_83fadhtc9().b[88][1]++;
            }
            cov_83fadhtc9().s[260]++;
            setIsSubmitting(true);
            /* istanbul ignore next */
            cov_83fadhtc9().s[261]++;
            setEdgeCaseResult(null); // Clear previous results
            /* istanbul ignore next */
            cov_83fadhtc9().s[262]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_83fadhtc9().b[87][1]++;
            cov_83fadhtc9().s[263]++;
            _a.trys.push([1, 3, 4, 5]);
            /* istanbul ignore next */
            cov_83fadhtc9().s[264]++;
            return [4 /*yield*/, onSubmit(assessments)];
          case 2:
            /* istanbul ignore next */
            cov_83fadhtc9().b[87][2]++;
            cov_83fadhtc9().s[265]++;
            result = _a.sent();
            // Check if the result is an EdgeCaseResult
            /* istanbul ignore next */
            cov_83fadhtc9().s[266]++;
            if (
            /* istanbul ignore next */
            (cov_83fadhtc9().b[90][0]++, result) &&
            /* istanbul ignore next */
            (cov_83fadhtc9().b[90][1]++, typeof result === 'object') &&
            /* istanbul ignore next */
            (cov_83fadhtc9().b[90][2]++, 'success' in result)) {
              /* istanbul ignore next */
              cov_83fadhtc9().b[89][0]++;
              cov_83fadhtc9().s[267]++;
              setEdgeCaseResult(result);
              /* istanbul ignore next */
              cov_83fadhtc9().s[268]++;
              if (result.success) {
                /* istanbul ignore next */
                cov_83fadhtc9().b[91][0]++;
                cov_83fadhtc9().s[269]++;
                // Reset form based on mode and preserveStateOnSubmit prop
                if (
                /* istanbul ignore next */
                (cov_83fadhtc9().b[93][0]++, mode === 'single') &&
                /* istanbul ignore next */
                (cov_83fadhtc9().b[93][1]++, !preserveStateOnSubmit)) {
                  /* istanbul ignore next */
                  cov_83fadhtc9().b[92][0]++;
                  cov_83fadhtc9().s[270]++;
                  updateAssessments([createEmptyAssessment()]);
                  /* istanbul ignore next */
                  cov_83fadhtc9().s[271]++;
                  sonner_1.toast.success('Skill assessment submitted successfully!');
                } else {
                  /* istanbul ignore next */
                  cov_83fadhtc9().b[92][1]++;
                  cov_83fadhtc9().s[272]++;
                  // For bulk mode or when preserveStateOnSubmit is true, keep the form data
                  sonner_1.toast.success('Skill assessments saved! You can add more or proceed to analysis.');
                }
              } else {
                /* istanbul ignore next */
                cov_83fadhtc9().b[91][1]++;
                cov_83fadhtc9().s[273]++;
                // Error case - EdgeCaseResultHandler will display the error
                sonner_1.toast.error(
                /* istanbul ignore next */
                (cov_83fadhtc9().b[94][0]++, result.error) ||
                /* istanbul ignore next */
                (cov_83fadhtc9().b[94][1]++, 'Failed to submit assessments'));
              }
            } else {
              /* istanbul ignore next */
              cov_83fadhtc9().b[89][1]++;
              cov_83fadhtc9().s[274]++;
              // Legacy response format
              if (
              /* istanbul ignore next */
              (cov_83fadhtc9().b[96][0]++, mode === 'single') &&
              /* istanbul ignore next */
              (cov_83fadhtc9().b[96][1]++, !preserveStateOnSubmit)) {
                /* istanbul ignore next */
                cov_83fadhtc9().b[95][0]++;
                cov_83fadhtc9().s[275]++;
                updateAssessments([createEmptyAssessment()]);
                /* istanbul ignore next */
                cov_83fadhtc9().s[276]++;
                sonner_1.toast.success('Skill assessment submitted successfully!');
              } else {
                /* istanbul ignore next */
                cov_83fadhtc9().b[95][1]++;
                cov_83fadhtc9().s[277]++;
                sonner_1.toast.success('Skill assessments saved! You can add more or proceed to analysis.');
              }
            }
            /* istanbul ignore next */
            cov_83fadhtc9().s[278]++;
            return [3 /*break*/, 5];
          case 3:
            /* istanbul ignore next */
            cov_83fadhtc9().b[87][3]++;
            cov_83fadhtc9().s[279]++;
            error_2 = _a.sent();
            /* istanbul ignore next */
            cov_83fadhtc9().s[280]++;
            console.error('Error submitting assessments:', error_2);
            /* istanbul ignore next */
            cov_83fadhtc9().s[281]++;
            setEdgeCaseResult({
              success: false,
              error: error_2 instanceof Error ?
              /* istanbul ignore next */
              (cov_83fadhtc9().b[97][0]++, error_2.message) :
              /* istanbul ignore next */
              (cov_83fadhtc9().b[97][1]++, 'Failed to submit assessments'),
              errorType: 'SYSTEM_ERROR',
              retryable: true
            });
            /* istanbul ignore next */
            cov_83fadhtc9().s[282]++;
            sonner_1.toast.error('Failed to submit assessments');
            /* istanbul ignore next */
            cov_83fadhtc9().s[283]++;
            return [3 /*break*/, 5];
          case 4:
            /* istanbul ignore next */
            cov_83fadhtc9().b[87][4]++;
            cov_83fadhtc9().s[284]++;
            setIsSubmitting(false);
            /* istanbul ignore next */
            cov_83fadhtc9().s[285]++;
            return [7 /*endfinally*/];
          case 5:
            /* istanbul ignore next */
            cov_83fadhtc9().b[87][5]++;
            cov_83fadhtc9().s[286]++;
            return [2 /*return*/];
        }
      });
    });
  }, [validateAssessments, onSubmit, assessments, mode, preserveStateOnSubmit, updateAssessments]));
  var getRatingLabel =
  /* istanbul ignore next */
  (cov_83fadhtc9().s[287]++, (0, react_1.useCallback)(function (rating) {
    /* istanbul ignore next */
    cov_83fadhtc9().f[46]++;
    cov_83fadhtc9().s[288]++;
    if (rating <= 2) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[98][0]++;
      cov_83fadhtc9().s[289]++;
      return 'Beginner';
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[98][1]++;
    }
    cov_83fadhtc9().s[290]++;
    if (rating <= 4) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[99][0]++;
      cov_83fadhtc9().s[291]++;
      return 'Basic';
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[99][1]++;
    }
    cov_83fadhtc9().s[292]++;
    if (rating <= 6) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[100][0]++;
      cov_83fadhtc9().s[293]++;
      return 'Intermediate';
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[100][1]++;
    }
    cov_83fadhtc9().s[294]++;
    if (rating <= 8) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[101][0]++;
      cov_83fadhtc9().s[295]++;
      return 'Advanced';
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[101][1]++;
    }
    cov_83fadhtc9().s[296]++;
    return 'Expert';
  }, []));
  var getRatingColor =
  /* istanbul ignore next */
  (cov_83fadhtc9().s[297]++, (0, react_1.useCallback)(function (rating) {
    /* istanbul ignore next */
    cov_83fadhtc9().f[47]++;
    cov_83fadhtc9().s[298]++;
    if (rating <= 2) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[102][0]++;
      cov_83fadhtc9().s[299]++;
      return 'bg-red-500';
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[102][1]++;
    }
    cov_83fadhtc9().s[300]++;
    if (rating <= 4) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[103][0]++;
      cov_83fadhtc9().s[301]++;
      return 'bg-orange-500';
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[103][1]++;
    }
    cov_83fadhtc9().s[302]++;
    if (rating <= 6) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[104][0]++;
      cov_83fadhtc9().s[303]++;
      return 'bg-yellow-500';
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[104][1]++;
    }
    cov_83fadhtc9().s[304]++;
    if (rating <= 8) {
      /* istanbul ignore next */
      cov_83fadhtc9().b[105][0]++;
      cov_83fadhtc9().s[305]++;
      return 'bg-blue-500';
    } else
    /* istanbul ignore next */
    {
      cov_83fadhtc9().b[105][1]++;
    }
    cov_83fadhtc9().s[306]++;
    return 'bg-green-500';
  }, []));
  /* istanbul ignore next */
  cov_83fadhtc9().s[307]++;
  return (0, jsx_runtime_1.jsxs)("div", {
    className: "space-y-6",
    children: [
    /* istanbul ignore next */
    (cov_83fadhtc9().b[106][0]++, edgeCaseResult) &&
    /* istanbul ignore next */
    (cov_83fadhtc9().b[106][1]++, (0, jsx_runtime_1.jsx)(EdgeCaseResultHandler_1.default, {
      result: edgeCaseResult,
      onRetry: function () {
        /* istanbul ignore next */
        cov_83fadhtc9().f[48]++;
        cov_83fadhtc9().s[308]++;
        setEdgeCaseResult(null);
        // Retry the last submission
        /* istanbul ignore next */
        cov_83fadhtc9().s[309]++;
        handleSubmit(new Event('submit'));
      },
      onUseAlternative: function (alternative) {
        /* istanbul ignore next */
        cov_83fadhtc9().f[49]++;
        cov_83fadhtc9().s[310]++;
        // Handle suggested alternatives
        if (typeof alternative === 'string') {
          /* istanbul ignore next */
          cov_83fadhtc9().b[107][0]++;
          // Add as a new skill assessment
          var newAssessment =
          /* istanbul ignore next */
          (cov_83fadhtc9().s[311]++, createEmptyAssessment());
          /* istanbul ignore next */
          cov_83fadhtc9().s[312]++;
          newAssessment.skillName = alternative;
          /* istanbul ignore next */
          cov_83fadhtc9().s[313]++;
          updateAssessments(__spreadArray(__spreadArray([], assessments, true), [newAssessment], false));
        } else
        /* istanbul ignore next */
        {
          cov_83fadhtc9().b[107][1]++;
        }
        cov_83fadhtc9().s[314]++;
        setEdgeCaseResult(null);
      },
      onUseFallback: function () {
        /* istanbul ignore next */
        cov_83fadhtc9().f[50]++;
        cov_83fadhtc9().s[315]++;
        // Use fallback data if available
        setEdgeCaseResult(null);
        /* istanbul ignore next */
        cov_83fadhtc9().s[316]++;
        sonner_1.toast.info('Using fallback data');
      }
    })), (0, jsx_runtime_1.jsxs)(card_1.Card, {
      children: [(0, jsx_runtime_1.jsxs)(card_1.CardHeader, {
        children: [(0, jsx_runtime_1.jsxs)(card_1.CardTitle, {
          className: "flex items-center gap-2",
          children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Star, {
            className: "h-5 w-5"
          }), "Skill Assessment"]
        }), (0, jsx_runtime_1.jsx)(card_1.CardDescription, {
          children: "Assess your current skill levels to get personalized learning recommendations"
        })]
      }), (0, jsx_runtime_1.jsx)(card_1.CardContent, {
        children: (0, jsx_runtime_1.jsxs)("form", {
          onSubmit: handleSubmit,
          className: "space-y-6",
          children: [
          /* istanbul ignore next */
          (cov_83fadhtc9().b[108][0]++, onSkillSearch) &&
          /* istanbul ignore next */
          (cov_83fadhtc9().b[108][1]++, (0, jsx_runtime_1.jsxs)("div", {
            className: "space-y-2",
            children: [(0, jsx_runtime_1.jsx)(label_1.Label, {
              htmlFor: "skill-search",
              children: "Search Skills"
            }), (0, jsx_runtime_1.jsxs)("div", {
              className: "relative",
              children: [(0, jsx_runtime_1.jsx)(input_1.Input, {
                id: "skill-search",
                placeholder: "Search for skills to assess...",
                value: searchQuery,
                onChange: function (e) {
                  /* istanbul ignore next */
                  cov_83fadhtc9().f[51]++;
                  cov_83fadhtc9().s[317]++;
                  return setSearchQuery(e.target.value);
                }
              }),
              /* istanbul ignore next */
              (cov_83fadhtc9().b[109][0]++, isSearching) &&
              /* istanbul ignore next */
              (cov_83fadhtc9().b[109][1]++, (0, jsx_runtime_1.jsx)(lucide_react_1.Loader2, {
                className: "absolute right-3 top-3 h-4 w-4 animate-spin"
              }))]
            }),
            /* istanbul ignore next */
            (cov_83fadhtc9().b[110][0]++, searchResults.length > 0) &&
            /* istanbul ignore next */
            (cov_83fadhtc9().b[110][1]++, (0, jsx_runtime_1.jsx)("div", {
              className: "border rounded-md p-2 bg-white shadow-sm max-h-40 overflow-y-auto",
              children: searchResults.map(function (skill) {
                /* istanbul ignore next */
                cov_83fadhtc9().f[52]++;
                cov_83fadhtc9().s[318]++;
                return (0, jsx_runtime_1.jsxs)("button", {
                  type: "button",
                  className: "w-full text-left p-2 hover:bg-gray-100 rounded",
                  onClick: function () {
                    /* istanbul ignore next */
                    cov_83fadhtc9().f[53]++;
                    // Find the first empty assessment or use the last one
                    var emptyIndex =
                    /* istanbul ignore next */
                    (cov_83fadhtc9().s[319]++, assessments.findIndex(function (a) {
                      /* istanbul ignore next */
                      cov_83fadhtc9().f[54]++;
                      cov_83fadhtc9().s[320]++;
                      return /* istanbul ignore next */(cov_83fadhtc9().b[111][0]++, !a.skillName) ||
                      /* istanbul ignore next */
                      (cov_83fadhtc9().b[111][1]++, a.skillName.trim() === '');
                    }));
                    var targetIndex =
                    /* istanbul ignore next */
                    (cov_83fadhtc9().s[321]++, emptyIndex !== -1 ?
                    /* istanbul ignore next */
                    (cov_83fadhtc9().b[112][0]++, emptyIndex) :
                    /* istanbul ignore next */
                    (cov_83fadhtc9().b[112][1]++, assessments.length - 1));
                    /* istanbul ignore next */
                    cov_83fadhtc9().s[322]++;
                    selectSkill(targetIndex, skill);
                  },
                  children: [(0, jsx_runtime_1.jsx)("div", {
                    className: "font-medium",
                    children: skill.name
                  }), (0, jsx_runtime_1.jsx)("div", {
                    className: "text-sm text-gray-500",
                    children: skill.category
                  }),
                  /* istanbul ignore next */
                  (cov_83fadhtc9().b[113][0]++, skill.description) &&
                  /* istanbul ignore next */
                  (cov_83fadhtc9().b[113][1]++, (0, jsx_runtime_1.jsx)("div", {
                    className: "text-xs text-gray-400 mt-1",
                    children: skill.description
                  }))]
                }, skill.id);
              })
            }))]
          })), assessments.map(function (assessment, index) {
            /* istanbul ignore next */
            cov_83fadhtc9().f[55]++;
            cov_83fadhtc9().s[323]++;
            return (0, jsx_runtime_1.jsxs)(card_1.Card, {
              className: "border-l-4 border-l-blue-500",
              children: [(0, jsx_runtime_1.jsx)(card_1.CardHeader, {
                className: "pb-4",
                children: (0, jsx_runtime_1.jsxs)("div", {
                  className: "flex items-center justify-between",
                  children: [(0, jsx_runtime_1.jsxs)(card_1.CardTitle, {
                    className: "text-lg",
                    children: ["Assessment ", index + 1]
                  }),
                  /* istanbul ignore next */
                  (cov_83fadhtc9().b[114][0]++, mode === 'bulk') &&
                  /* istanbul ignore next */
                  (cov_83fadhtc9().b[114][1]++, assessments.length > 1) &&
                  /* istanbul ignore next */
                  (cov_83fadhtc9().b[114][2]++, (0, jsx_runtime_1.jsx)(button_1.Button, {
                    type: "button",
                    variant: "outline",
                    size: "sm",
                    onClick: function () {
                      /* istanbul ignore next */
                      cov_83fadhtc9().f[56]++;
                      cov_83fadhtc9().s[324]++;
                      return removeAssessment(index);
                    },
                    children: "Remove"
                  }))]
                })
              }), (0, jsx_runtime_1.jsxs)(card_1.CardContent, {
                className: "space-y-4",
                children: [(0, jsx_runtime_1.jsxs)("div", {
                  className: "space-y-2",
                  children: [(0, jsx_runtime_1.jsx)(label_1.Label, {
                    htmlFor: "skill-name-".concat(index),
                    children: "Skill Name *"
                  }), (0, jsx_runtime_1.jsx)(input_1.Input, {
                    id: "skill-name-".concat(index),
                    placeholder: "e.g., JavaScript, React, Python",
                    value: assessment.skillName,
                    onChange: function (e) {
                      /* istanbul ignore next */
                      cov_83fadhtc9().f[57]++;
                      cov_83fadhtc9().s[325]++;
                      return updateAssessment(index, 'skillName', e.target.value);
                    },
                    className: errors["".concat(index, ".skillName")] ?
                    /* istanbul ignore next */
                    (cov_83fadhtc9().b[115][0]++, 'border-red-500') :
                    /* istanbul ignore next */
                    (cov_83fadhtc9().b[115][1]++, '')
                  }),
                  /* istanbul ignore next */
                  (cov_83fadhtc9().b[116][0]++, errors["".concat(index, ".skillName")]) &&
                  /* istanbul ignore next */
                  (cov_83fadhtc9().b[116][1]++, (0, jsx_runtime_1.jsx)("p", {
                    className: "text-sm text-red-500",
                    children: errors["".concat(index, ".skillName")]
                  }))]
                }), (0, jsx_runtime_1.jsxs)("div", {
                  className: "space-y-3",
                  children: [(0, jsx_runtime_1.jsxs)(label_1.Label, {
                    children: ["Self Rating: ", assessment.selfRating, "/10"]
                  }), (0, jsx_runtime_1.jsxs)("div", {
                    className: "space-y-2",
                    children: [(0, jsx_runtime_1.jsx)(slider_1.Slider, {
                      value: [assessment.selfRating],
                      onValueChange: function (value) {
                        /* istanbul ignore next */
                        cov_83fadhtc9().f[58]++;
                        cov_83fadhtc9().s[326]++;
                        return updateAssessment(index, 'selfRating', value[0]);
                      },
                      max: 10,
                      min: 1,
                      step: 1,
                      className: "w-full"
                    }), (0, jsx_runtime_1.jsxs)("div", {
                      className: "flex justify-between text-xs text-gray-500",
                      children: [(0, jsx_runtime_1.jsx)("span", {
                        children: "Beginner"
                      }), (0, jsx_runtime_1.jsx)("span", {
                        children: "Expert"
                      })]
                    }), (0, jsx_runtime_1.jsx)(badge_1.Badge, {
                      variant: "secondary",
                      className: "".concat(getRatingColor(assessment.selfRating), " text-white"),
                      children: getRatingLabel(assessment.selfRating)
                    })]
                  })]
                }), (0, jsx_runtime_1.jsxs)("div", {
                  className: "space-y-3",
                  children: [(0, jsx_runtime_1.jsxs)(label_1.Label, {
                    children: ["Confidence Level: ", assessment.confidenceLevel, "/10"]
                  }), (0, jsx_runtime_1.jsxs)("div", {
                    className: "space-y-2",
                    children: [(0, jsx_runtime_1.jsx)(slider_1.Slider, {
                      value: [assessment.confidenceLevel],
                      onValueChange: function (value) {
                        /* istanbul ignore next */
                        cov_83fadhtc9().f[59]++;
                        cov_83fadhtc9().s[327]++;
                        return updateAssessment(index, 'confidenceLevel', value[0]);
                      },
                      max: 10,
                      min: 1,
                      step: 1,
                      className: "w-full"
                    }), (0, jsx_runtime_1.jsxs)("div", {
                      className: "flex justify-between text-xs text-gray-500",
                      children: [(0, jsx_runtime_1.jsx)("span", {
                        children: "Not Confident"
                      }), (0, jsx_runtime_1.jsx)("span", {
                        children: "Very Confident"
                      })]
                    })]
                  })]
                }), (0, jsx_runtime_1.jsxs)("div", {
                  className: "grid grid-cols-2 gap-4",
                  children: [(0, jsx_runtime_1.jsxs)("div", {
                    className: "space-y-2",
                    children: [(0, jsx_runtime_1.jsx)(label_1.Label, {
                      htmlFor: "years-".concat(index),
                      children: "Years of Experience"
                    }), (0, jsx_runtime_1.jsx)(input_1.Input, {
                      id: "years-".concat(index),
                      type: "number",
                      min: "0",
                      max: "50",
                      placeholder: "0",
                      value:
                      /* istanbul ignore next */
                      (cov_83fadhtc9().b[117][0]++, assessment.yearsOfExperience) ||
                      /* istanbul ignore next */
                      (cov_83fadhtc9().b[117][1]++, ''),
                      onChange: function (e) {
                        /* istanbul ignore next */
                        cov_83fadhtc9().f[60]++;
                        cov_83fadhtc9().s[328]++;
                        return updateAssessment(index, 'yearsOfExperience',
                        /* istanbul ignore next */
                        (cov_83fadhtc9().b[118][0]++, parseInt(e.target.value)) ||
                        /* istanbul ignore next */
                        (cov_83fadhtc9().b[118][1]++, 0));
                      }
                    })]
                  }), (0, jsx_runtime_1.jsxs)("div", {
                    className: "space-y-2",
                    children: [(0, jsx_runtime_1.jsx)(label_1.Label, {
                      htmlFor: "last-used-".concat(index),
                      children: "Last Used"
                    }), (0, jsx_runtime_1.jsx)(input_1.Input, {
                      id: "last-used-".concat(index),
                      placeholder: "e.g., Currently using",
                      value:
                      /* istanbul ignore next */
                      (cov_83fadhtc9().b[119][0]++, assessment.lastUsed) ||
                      /* istanbul ignore next */
                      (cov_83fadhtc9().b[119][1]++, ''),
                      onChange: function (e) {
                        /* istanbul ignore next */
                        cov_83fadhtc9().f[61]++;
                        cov_83fadhtc9().s[329]++;
                        return updateAssessment(index, 'lastUsed', e.target.value);
                      }
                    })]
                  })]
                }), (0, jsx_runtime_1.jsxs)("div", {
                  className: "space-y-2",
                  children: [(0, jsx_runtime_1.jsx)(label_1.Label, {
                    htmlFor: "notes-".concat(index),
                    children: "Notes (Optional)"
                  }), (0, jsx_runtime_1.jsx)(textarea_1.Textarea, {
                    id: "notes-".concat(index),
                    placeholder: "Any additional context about your experience with this skill...",
                    value:
                    /* istanbul ignore next */
                    (cov_83fadhtc9().b[120][0]++, assessment.notes) ||
                    /* istanbul ignore next */
                    (cov_83fadhtc9().b[120][1]++, ''),
                    onChange: function (e) {
                      /* istanbul ignore next */
                      cov_83fadhtc9().f[62]++;
                      cov_83fadhtc9().s[330]++;
                      return updateAssessment(index, 'notes', e.target.value);
                    },
                    rows: 3
                  })]
                })]
              })]
            },
            /* istanbul ignore next */
            (cov_83fadhtc9().b[121][0]++, assessment._id) ||
            /* istanbul ignore next */
            (cov_83fadhtc9().b[121][1]++, index));
          }),
          /* istanbul ignore next */
          (cov_83fadhtc9().b[122][0]++, mode === 'bulk') &&
          /* istanbul ignore next */
          (cov_83fadhtc9().b[122][1]++, assessments.length < maxAssessments) &&
          /* istanbul ignore next */
          (cov_83fadhtc9().b[122][2]++, (0, jsx_runtime_1.jsx)(button_1.Button, {
            type: "button",
            variant: "outline",
            onClick: addAssessment,
            className: "w-full",
            children: "Add Another Skill Assessment"
          })), (0, jsx_runtime_1.jsx)("div", {
            className: "flex justify-end space-x-4",
            children: (0, jsx_runtime_1.jsx)(button_1.Button, {
              type: "submit",
              disabled: isSubmitting,
              className: "min-w-32",
              children: isSubmitting ?
              /* istanbul ignore next */
              (cov_83fadhtc9().b[123][0]++, (0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, {
                children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Loader2, {
                  className: "mr-2 h-4 w-4 animate-spin"
                }), "Submitting..."]
              })) :
              /* istanbul ignore next */
              (cov_83fadhtc9().b[123][1]++, 'Submit Assessment')
            })
          })]
        })
      })]
    }),
    /* istanbul ignore next */
    (cov_83fadhtc9().b[124][0]++, assessments.length > 1) &&
    /* istanbul ignore next */
    (cov_83fadhtc9().b[124][1]++, (0, jsx_runtime_1.jsx)(card_1.Card, {
      children: (0, jsx_runtime_1.jsxs)(card_1.CardContent, {
        className: "pt-6",
        children: [(0, jsx_runtime_1.jsxs)("div", {
          className: "flex items-center justify-between text-sm text-gray-600 mb-2",
          children: [(0, jsx_runtime_1.jsx)("span", {
            children: "Assessment Progress"
          }), (0, jsx_runtime_1.jsxs)("span", {
            children: [assessments.length, " / ", maxAssessments, " skills"]
          })]
        }), (0, jsx_runtime_1.jsx)(progress_1.Progress, {
          value: assessments.length / maxAssessments * 100,
          className: "h-2"
        })]
      })
    }))]
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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