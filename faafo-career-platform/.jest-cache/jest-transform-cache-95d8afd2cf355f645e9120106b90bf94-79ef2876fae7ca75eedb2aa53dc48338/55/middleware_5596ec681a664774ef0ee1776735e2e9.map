{"version": 3, "names": ["exports", "middleware", "server_1", "cov_1ad6gddzlg", "s", "require", "jwt_1", "protectedRoutes", "adminRoutes", "protectedApiRoutes", "adminApiRoutes", "publicApiRoutes", "rateLimitStore", "Map", "getClientIP", "request", "f", "forwarded", "headers", "get", "b", "split", "trim", "realIP", "ip", "isRateLimited", "windowMs", "maxRequests", "clientIP", "now", "Date", "windowStart", "Array", "from", "entries", "for<PERSON>ach", "_a", "key", "value", "resetTime", "delete", "entry", "count", "set", "addSecurityHeaders", "response", "nonce", "crypto", "randomUUID", "csp", "concat", "join", "pathname", "nextUrl", "allowedStaticExtensions", "blockedPatterns", "hasBlockedPattern", "some", "pattern", "toLowerCase", "includes", "console", "warn", "NextResponse", "json", "error", "status", "isNextInternal", "startsWith", "isFavicon", "isAllowedStatic", "ext", "endsWith", "next", "getToken", "req", "secret", "process", "env", "NEXTAUTH_SECRET", "token", "sent", "isProtectedRoute", "route", "isAdminRoute", "isProtectedApiRoute", "isAdminApiRoute", "isPublicApiRoute", "userAgent", "isAdmin", "loginUrl", "URL", "url", "fullPath", "search", "searchParams", "redirect", "role", "userId", "sub", "email", "isRedirectLoop", "parseInt", "response_1", "redirectCount", "toString", "response_2", "callbackUrl", "redirectTo", "response_3", "config", "matcher"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getToken } from 'next-auth/jwt';\n\n// Define protected routes that require authentication\nconst protectedRoutes = [\n  '/dashboard',\n  '/profile',\n  '/assessment',\n  '/forum',\n  '/freedom-fund',\n  '/progress',\n  '/recommendations',\n  '/interview-practice',\n  '/resume-builder',\n  '/tools',\n];\n\n// Define admin routes that require admin authentication\nconst adminRoutes = [\n  '/admin',\n];\n\n// Define API routes that require authentication\nconst protectedApiRoutes = [\n  '/api/assessment',\n  '/api/profile',\n  '/api/freedom-fund',\n  '/api/learning-progress',\n  '/api/personalized-resources',\n  '/api/progress-tracker',\n  '/api/recommendations',\n  '/api/resource-ratings',\n  '/api/interview-practice',\n  '/api/resume-builder',\n  '/api/tools',\n];\n\n// Define admin API routes that require admin authentication\nconst adminApiRoutes = [\n  '/api/admin',\n];\n\n// Define public API routes that don't require authentication\nconst publicApiRoutes = [\n  '/api/auth',\n  '/api/signup',\n  '/api/career-paths',\n  '/api/learning-resources',\n  '/api/contact',\n  '/api/csrf-token',\n];\n\n// Rate limiting store (use Redis in production)\nconst rateLimitStore = new Map<string, { count: number; resetTime: number }>();\n\nfunction getClientIP(request: NextRequest): string {\n  const forwarded = request.headers.get('x-forwarded-for');\n  if (forwarded) {\n    return forwarded.split(',')[0].trim();\n  }\n  \n  const realIP = request.headers.get('x-real-ip');\n  if (realIP) {\n    return realIP;\n  }\n  \n  return (request as any).ip || 'unknown';\n}\n\nfunction isRateLimited(request: NextRequest, windowMs: number = 15 * 60 * 1000, maxRequests: number = 100): boolean {\n  const clientIP = getClientIP(request);\n  const now = Date.now();\n  const windowStart = now - windowMs;\n  \n  // Clean up old entries\n  Array.from(rateLimitStore.entries()).forEach(([key, value]) => {\n    if (value.resetTime < windowStart) {\n      rateLimitStore.delete(key);\n    }\n  });\n  \n  // Get or create entry for this IP\n  const entry = rateLimitStore.get(clientIP) || { count: 0, resetTime: now + windowMs };\n  \n  // Reset if window has expired\n  if (entry.resetTime < now) {\n    entry.count = 0;\n    entry.resetTime = now + windowMs;\n  }\n  \n  // Increment count\n  entry.count++;\n  rateLimitStore.set(clientIP, entry);\n  \n  return entry.count > maxRequests;\n}\n\nfunction addSecurityHeaders(response: NextResponse): NextResponse {\n  // Add comprehensive security headers\n  response.headers.set('X-Frame-Options', 'DENY');\n  response.headers.set('X-Content-Type-Options', 'nosniff');\n  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');\n  response.headers.set('X-XSS-Protection', '1; mode=block');\n  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');\n\n  // Generate nonce for inline scripts\n  const nonce = crypto.randomUUID();\n\n  // Enhanced Content Security Policy - Removed unsafe-inline for better security\n  const csp = [\n    \"default-src 'self'\",\n    `script-src 'self' 'nonce-${nonce}' 'unsafe-eval' https://vercel.live https://va.vercel-scripts.com https://browser.sentry-cdn.com`,\n    \"style-src 'self' 'unsafe-inline' https://fonts.googleapis.com\",\n    \"img-src 'self' data: https: blob:\",\n    \"font-src 'self' data: https://fonts.gstatic.com\",\n    \"connect-src 'self' https://vercel.live https://vitals.vercel-insights.com https://*.sentry.io\",\n    \"worker-src 'self' blob:\",\n    \"child-src 'self' blob:\",\n    \"frame-ancestors 'none'\",\n    \"base-uri 'self'\",\n    \"form-action 'self'\"\n  ].join('; ');\n\n  response.headers.set('Content-Security-Policy', csp);\n  response.headers.set('X-Nonce', nonce);\n\n  // Add HSTS header for HTTPS (always set for security testing)\n  response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');\n\n  // Additional security headers\n  response.headers.set('Cross-Origin-Embedder-Policy', 'require-corp');\n  response.headers.set('Cross-Origin-Opener-Policy', 'same-origin');\n  response.headers.set('Cross-Origin-Resource-Policy', 'same-origin');\n\n  return response;\n}\n\nexport async function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n\n  // Define allowed static file extensions (security-first approach)\n  const allowedStaticExtensions = [\n    '.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico',\n    '.woff', '.woff2', '.ttf', '.eot', '.webp', '.avif', '.mp4', '.webm'\n  ];\n\n  // Define explicitly blocked file patterns (security critical)\n  const blockedPatterns = [\n    '.env', '.git', '.db', '.sqlite', '.log', '.bak', '.backup',\n    '.config', '.ini', '.conf', '.key', '.pem', '.crt', '.p12',\n    '.json', '.yaml', '.yml', '.xml', '.md', '.txt', '.csv'\n  ];\n\n  // Check for blocked file patterns first (highest security priority)\n  const hasBlockedPattern = blockedPatterns.some(pattern =>\n    pathname.toLowerCase().includes(pattern.toLowerCase())\n  );\n\n  if (hasBlockedPattern) {\n    console.warn(`Blocked access to sensitive file: ${pathname}`);\n    return NextResponse.json(\n      { error: 'Access denied' },\n      { status: 403 }\n    );\n  }\n\n  // Skip middleware only for explicitly allowed static files and Next.js internals\n  const isNextInternal = pathname.startsWith('/_next') || pathname.startsWith('/api/_next');\n  const isFavicon = pathname === '/favicon.ico';\n  const isAllowedStatic = allowedStaticExtensions.some(ext =>\n    pathname.toLowerCase().endsWith(ext.toLowerCase())\n  );\n\n  if (isNextInternal || isFavicon || (isAllowedStatic && pathname.startsWith('/public/'))) {\n    return NextResponse.next();\n  }\n\n  // Get the token to check authentication status\n  const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });\n\n  // Check if the route requires authentication\n  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route));\n  const isAdminRoute = adminRoutes.some(route => pathname.startsWith(route));\n  const isProtectedApiRoute = protectedApiRoutes.some(route => pathname.startsWith(route));\n  const isAdminApiRoute = adminApiRoutes.some(route => pathname.startsWith(route));\n  const isPublicApiRoute = publicApiRoutes.some(route => pathname.startsWith(route));\n\n  // Apply rate limiting for API routes and sensitive endpoints\n  if (pathname.startsWith('/api/') || isAdminRoute || isAdminApiRoute) {\n    // Use stricter rate limiting for admin routes\n    const maxRequests = (isAdminRoute || isAdminApiRoute) ? 50 : 100;\n    const windowMs = (isAdminRoute || isAdminApiRoute) ? 15 * 60 * 1000 : 15 * 60 * 1000;\n\n    if (isRateLimited(request, windowMs, maxRequests)) {\n      console.warn(`Rate limit exceeded for ${pathname}`, {\n        ip: getClientIP(request),\n        userAgent: request.headers.get('user-agent'),\n        isAdmin: isAdminRoute || isAdminApiRoute\n      });\n\n      return NextResponse.json(\n        { error: 'Too many requests. Please try again later.' },\n        { status: 429 }\n      );\n    }\n  }\n\n  // Handle admin routes (require admin authentication)\n  if (isAdminRoute) {\n    if (!token) {\n      const loginUrl = new URL('/login', request.url);\n      const fullPath = pathname + (request.nextUrl.search || '');\n      loginUrl.searchParams.set('callbackUrl', fullPath);\n      return NextResponse.redirect(loginUrl);\n    }\n\n    // Check if user has admin role\n    if (!token.role || token.role !== 'admin') {\n      console.warn(`Non-admin user attempted to access admin route: ${pathname}`, {\n        userId: token.sub,\n        email: token.email,\n        role: token.role\n      });\n      return NextResponse.json(\n        { error: 'Admin access required' },\n        { status: 403 }\n      );\n    }\n  }\n\n  // Handle protected routes\n  if (isProtectedRoute && !token) {\n    // Prevent redirect loops by checking if we're already being redirected\n    const isRedirectLoop = request.headers.get('x-middleware-redirect-count');\n    if (isRedirectLoop && parseInt(isRedirectLoop) > 3) {\n      console.error('Redirect loop detected, breaking chain');\n      return NextResponse.json(\n        { error: 'Authentication redirect loop detected' },\n        { status: 500 }\n      );\n    }\n\n    const loginUrl = new URL('/login', request.url);\n    // Include query parameters in the redirect URL\n    const fullPath = pathname + (request.nextUrl.search || '');\n    loginUrl.searchParams.set('callbackUrl', fullPath);\n\n    const response = NextResponse.redirect(loginUrl);\n    // Track redirect count to prevent loops\n    const redirectCount = parseInt(isRedirectLoop || '0') + 1;\n    response.headers.set('x-middleware-redirect-count', redirectCount.toString());\n    return response;\n  }\n\n  // Handle admin API routes (require admin authentication)\n  if (isAdminApiRoute) {\n    if (!token) {\n      return NextResponse.json(\n        { error: 'Authentication required' },\n        { status: 401 }\n      );\n    }\n\n    // Check if user has admin role\n    if (!token.role || token.role !== 'admin') {\n      console.warn(`Non-admin user attempted to access admin API: ${pathname}`, {\n        userId: token.sub,\n        email: token.email,\n        role: token.role\n      });\n      return NextResponse.json(\n        { error: 'Admin access required' },\n        { status: 403 }\n      );\n    }\n  }\n\n  // Handle protected API routes\n  if (isProtectedApiRoute && !token && !isPublicApiRoute) {\n    return NextResponse.json(\n      { error: 'Authentication required' },\n      { status: 401 }\n    );\n  }\n\n  // Redirect authenticated users away from auth pages (with loop prevention)\n  if (token && (pathname === '/login' || pathname === '/signup')) {\n    // Check for redirect loops\n    const isRedirectLoop = request.headers.get('x-middleware-redirect-count');\n    if (isRedirectLoop && parseInt(isRedirectLoop) > 3) {\n      console.error('Auth redirect loop detected, allowing access to auth page');\n      // Allow access to prevent infinite loops\n      const response = NextResponse.next();\n      return addSecurityHeaders(response);\n    }\n\n    // Check if user is trying to access login with a callback URL\n    const callbackUrl = request.nextUrl.searchParams.get('callbackUrl');\n    const redirectTo = callbackUrl && callbackUrl !== '/login' && callbackUrl !== '/signup'\n      ? callbackUrl\n      : '/dashboard';\n\n    const response = NextResponse.redirect(new URL(redirectTo, request.url));\n    // Track redirect count\n    const redirectCount = parseInt(isRedirectLoop || '0') + 1;\n    response.headers.set('x-middleware-redirect-count', redirectCount.toString());\n    return response;\n  }\n\n  const response = NextResponse.next();\n  return addSecurityHeaders(response);\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder files\n     */\n    '/((?!_next/static|_next/image|favicon.ico|public/).*)',\n  ],\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyIAA,OAAA,CAAAC,UAAA,GAAAA,UAAA;AAzIA,IAAAC,QAAA;AAAA;AAAA,CAAAC,cAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAC,KAAA;AAAA;AAAA,CAAAH,cAAA,GAAAC,CAAA,QAAAC,OAAA;AAEA;AACA,IAAME,eAAe;AAAA;AAAA,CAAAJ,cAAA,GAAAC,CAAA,QAAG,CACtB,YAAY,EACZ,UAAU,EACV,aAAa,EACb,QAAQ,EACR,eAAe,EACf,WAAW,EACX,kBAAkB,EAClB,qBAAqB,EACrB,iBAAiB,EACjB,QAAQ,CACT;AAED;AACA,IAAMI,WAAW;AAAA;AAAA,CAAAL,cAAA,GAAAC,CAAA,QAAG,CAClB,QAAQ,CACT;AAED;AACA,IAAMK,kBAAkB;AAAA;AAAA,CAAAN,cAAA,GAAAC,CAAA,QAAG,CACzB,iBAAiB,EACjB,cAAc,EACd,mBAAmB,EACnB,wBAAwB,EACxB,6BAA6B,EAC7B,uBAAuB,EACvB,sBAAsB,EACtB,uBAAuB,EACvB,yBAAyB,EACzB,qBAAqB,EACrB,YAAY,CACb;AAED;AACA,IAAMM,cAAc;AAAA;AAAA,CAAAP,cAAA,GAAAC,CAAA,QAAG,CACrB,YAAY,CACb;AAED;AACA,IAAMO,eAAe;AAAA;AAAA,CAAAR,cAAA,GAAAC,CAAA,QAAG,CACtB,WAAW,EACX,aAAa,EACb,mBAAmB,EACnB,yBAAyB,EACzB,cAAc,EACd,iBAAiB,CAClB;AAED;AACA,IAAMQ,cAAc;AAAA;AAAA,CAAAT,cAAA,GAAAC,CAAA,QAAG,IAAIS,GAAG,EAAgD;AAE9E,SAASC,WAAWA,CAACC,OAAoB;EAAA;EAAAZ,cAAA,GAAAa,CAAA;EACvC,IAAMC,SAAS;EAAA;EAAA,CAAAd,cAAA,GAAAC,CAAA,QAAGW,OAAO,CAACG,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;EAAC;EAAAhB,cAAA,GAAAC,CAAA;EACzD,IAAIa,SAAS,EAAE;IAAA;IAAAd,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAC,CAAA;IACb,OAAOa,SAAS,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,EAAE;EACvC,CAAC;EAAA;EAAA;IAAAnB,cAAA,GAAAiB,CAAA;EAAA;EAED,IAAMG,MAAM;EAAA;EAAA,CAAApB,cAAA,GAAAC,CAAA,QAAGW,OAAO,CAACG,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;EAAC;EAAAhB,cAAA,GAAAC,CAAA;EAChD,IAAImB,MAAM,EAAE;IAAA;IAAApB,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAC,CAAA;IACV,OAAOmB,MAAM;EACf,CAAC;EAAA;EAAA;IAAApB,cAAA,GAAAiB,CAAA;EAAA;EAAAjB,cAAA,GAAAC,CAAA;EAED,OAAQ,2BAAAD,cAAA,GAAAiB,CAAA,WAAAL,OAAe,CAACS,EAAE;EAAA;EAAA,CAAArB,cAAA,GAAAiB,CAAA,WAAI,SAAS;AACzC;AAEA,SAASK,aAAaA,CAACV,OAAoB,EAAEW,QAAiC,EAAEC,WAAyB;EAAA;EAAAxB,cAAA,GAAAa,CAAA;EAAAb,cAAA,GAAAC,CAAA;EAA5D,IAAAsB,QAAA;IAAA;IAAAvB,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAC,CAAA;IAAAsB,QAAA,GAAmB,EAAE,GAAG,EAAE,GAAG,IAAI;EAAA;EAAA;EAAA;IAAAvB,cAAA,GAAAiB,CAAA;EAAA;EAAAjB,cAAA,GAAAC,CAAA;EAAE,IAAAuB,WAAA;IAAA;IAAAxB,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAC,CAAA;IAAAuB,WAAA,MAAyB;EAAA;EAAA;EAAA;IAAAxB,cAAA,GAAAiB,CAAA;EAAA;EACvG,IAAMQ,QAAQ;EAAA;EAAA,CAAAzB,cAAA,GAAAC,CAAA,QAAGU,WAAW,CAACC,OAAO,CAAC;EACrC,IAAMc,GAAG;EAAA;EAAA,CAAA1B,cAAA,GAAAC,CAAA,QAAG0B,IAAI,CAACD,GAAG,EAAE;EACtB,IAAME,WAAW;EAAA;EAAA,CAAA5B,cAAA,GAAAC,CAAA,QAAGyB,GAAG,GAAGH,QAAQ;EAElC;EAAA;EAAAvB,cAAA,GAAAC,CAAA;EACA4B,KAAK,CAACC,IAAI,CAACrB,cAAc,CAACsB,OAAO,EAAE,CAAC,CAACC,OAAO,CAAC,UAACC,EAAY;IAAA;IAAAjC,cAAA,GAAAa,CAAA;QAAXqB,GAAG;MAAA;MAAA,CAAAlC,cAAA,GAAAC,CAAA,QAAAgC,EAAA;MAAEE,KAAK;MAAA;MAAA,CAAAnC,cAAA,GAAAC,CAAA,QAAAgC,EAAA;IAAA;IAAAjC,cAAA,GAAAC,CAAA;IACvD,IAAIkC,KAAK,CAACC,SAAS,GAAGR,WAAW,EAAE;MAAA;MAAA5B,cAAA,GAAAiB,CAAA;MAAAjB,cAAA,GAAAC,CAAA;MACjCQ,cAAc,CAAC4B,MAAM,CAACH,GAAG,CAAC;IAC5B,CAAC;IAAA;IAAA;MAAAlC,cAAA,GAAAiB,CAAA;IAAA;EACH,CAAC,CAAC;EAEF;EACA,IAAMqB,KAAK;EAAA;EAAA,CAAAtC,cAAA,GAAAC,CAAA;EAAG;EAAA,CAAAD,cAAA,GAAAiB,CAAA,WAAAR,cAAc,CAACO,GAAG,CAACS,QAAQ,CAAC;EAAA;EAAA,CAAAzB,cAAA,GAAAiB,CAAA,WAAI;IAAEsB,KAAK,EAAE,CAAC;IAAEH,SAAS,EAAEV,GAAG,GAAGH;EAAQ,CAAE;EAErF;EAAA;EAAAvB,cAAA,GAAAC,CAAA;EACA,IAAIqC,KAAK,CAACF,SAAS,GAAGV,GAAG,EAAE;IAAA;IAAA1B,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAC,CAAA;IACzBqC,KAAK,CAACC,KAAK,GAAG,CAAC;IAAC;IAAAvC,cAAA,GAAAC,CAAA;IAChBqC,KAAK,CAACF,SAAS,GAAGV,GAAG,GAAGH,QAAQ;EAClC,CAAC;EAAA;EAAA;IAAAvB,cAAA,GAAAiB,CAAA;EAAA;EAED;EAAAjB,cAAA,GAAAC,CAAA;EACAqC,KAAK,CAACC,KAAK,EAAE;EAAC;EAAAvC,cAAA,GAAAC,CAAA;EACdQ,cAAc,CAAC+B,GAAG,CAACf,QAAQ,EAAEa,KAAK,CAAC;EAAC;EAAAtC,cAAA,GAAAC,CAAA;EAEpC,OAAOqC,KAAK,CAACC,KAAK,GAAGf,WAAW;AAClC;AAEA,SAASiB,kBAAkBA,CAACC,QAAsB;EAAA;EAAA1C,cAAA,GAAAa,CAAA;EAAAb,cAAA,GAAAC,CAAA;EAChD;EACAyC,QAAQ,CAAC3B,OAAO,CAACyB,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC;EAAC;EAAAxC,cAAA,GAAAC,CAAA;EAChDyC,QAAQ,CAAC3B,OAAO,CAACyB,GAAG,CAAC,wBAAwB,EAAE,SAAS,CAAC;EAAC;EAAAxC,cAAA,GAAAC,CAAA;EAC1DyC,QAAQ,CAAC3B,OAAO,CAACyB,GAAG,CAAC,iBAAiB,EAAE,iCAAiC,CAAC;EAAC;EAAAxC,cAAA,GAAAC,CAAA;EAC3EyC,QAAQ,CAAC3B,OAAO,CAACyB,GAAG,CAAC,kBAAkB,EAAE,eAAe,CAAC;EAAC;EAAAxC,cAAA,GAAAC,CAAA;EAC1DyC,QAAQ,CAAC3B,OAAO,CAACyB,GAAG,CAAC,oBAAoB,EAAE,0CAA0C,CAAC;EAEtF;EACA,IAAMG,KAAK;EAAA;EAAA,CAAA3C,cAAA,GAAAC,CAAA,SAAG2C,MAAM,CAACC,UAAU,EAAE;EAEjC;EACA,IAAMC,GAAG;EAAA;EAAA,CAAA9C,cAAA,GAAAC,CAAA,SAAG,CACV,oBAAoB,EACpB,4BAAA8C,MAAA,CAA4BJ,KAAK,qGAAkG,EACnI,+DAA+D,EAC/D,mCAAmC,EACnC,iDAAiD,EACjD,+FAA+F,EAC/F,yBAAyB,EACzB,wBAAwB,EACxB,wBAAwB,EACxB,iBAAiB,EACjB,oBAAoB,CACrB,CAACK,IAAI,CAAC,IAAI,CAAC;EAAC;EAAAhD,cAAA,GAAAC,CAAA;EAEbyC,QAAQ,CAAC3B,OAAO,CAACyB,GAAG,CAAC,yBAAyB,EAAEM,GAAG,CAAC;EAAC;EAAA9C,cAAA,GAAAC,CAAA;EACrDyC,QAAQ,CAAC3B,OAAO,CAACyB,GAAG,CAAC,SAAS,EAAEG,KAAK,CAAC;EAEtC;EAAA;EAAA3C,cAAA,GAAAC,CAAA;EACAyC,QAAQ,CAAC3B,OAAO,CAACyB,GAAG,CAAC,2BAA2B,EAAE,8CAA8C,CAAC;EAEjG;EAAA;EAAAxC,cAAA,GAAAC,CAAA;EACAyC,QAAQ,CAAC3B,OAAO,CAACyB,GAAG,CAAC,8BAA8B,EAAE,cAAc,CAAC;EAAC;EAAAxC,cAAA,GAAAC,CAAA;EACrEyC,QAAQ,CAAC3B,OAAO,CAACyB,GAAG,CAAC,4BAA4B,EAAE,aAAa,CAAC;EAAC;EAAAxC,cAAA,GAAAC,CAAA;EAClEyC,QAAQ,CAAC3B,OAAO,CAACyB,GAAG,CAAC,8BAA8B,EAAE,aAAa,CAAC;EAAC;EAAAxC,cAAA,GAAAC,CAAA;EAEpE,OAAOyC,QAAQ;AACjB;AAEA,SAAsB5C,UAAUA,CAACc,OAAoB;EAAA;EAAAZ,cAAA,GAAAa,CAAA;EAAAb,cAAA,GAAAC,CAAA;;;;;;;;;;;;;;;;UAC3CgD,QAAQ,GAAKrC,OAAO,CAACsC,OAAO,CAAAD,QAApB;UAAqB;UAAAjD,cAAA,GAAAC,CAAA;UAG/BkD,uBAAuB,GAAG,CAC9B,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAC9D,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CACrE;UAAC;UAAAnD,cAAA,GAAAC,CAAA;UAGImD,eAAe,GAAG,CACtB,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAC3D,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAC1D,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CACxD;UAAC;UAAApD,cAAA,GAAAC,CAAA;UAGIoD,iBAAiB,GAAGD,eAAe,CAACE,IAAI,CAAC,UAAAC,OAAO;YAAA;YAAAvD,cAAA,GAAAa,CAAA;YAAAb,cAAA,GAAAC,CAAA;YACpD,OAAAgD,QAAQ,CAACO,WAAW,EAAE,CAACC,QAAQ,CAACF,OAAO,CAACC,WAAW,EAAE,CAAC;UAAtD,CAAsD,CACvD;UAAC;UAAAxD,cAAA,GAAAC,CAAA;UAEF,IAAIoD,iBAAiB,EAAE;YAAA;YAAArD,cAAA,GAAAiB,CAAA;YAAAjB,cAAA,GAAAC,CAAA;YACrByD,OAAO,CAACC,IAAI,CAAC,qCAAAZ,MAAA,CAAqCE,QAAQ,CAAE,CAAC;YAAC;YAAAjD,cAAA,GAAAC,CAAA;YAC9D,sBAAOF,QAAA,CAAA6D,YAAY,CAACC,IAAI,CACtB;cAAEC,KAAK,EAAE;YAAe,CAAE,EAC1B;cAAEC,MAAM,EAAE;YAAG,CAAE,CAChB;UACH,CAAC;UAAA;UAAA;YAAA/D,cAAA,GAAAiB,CAAA;UAAA;UAAAjB,cAAA,GAAAC,CAAA;UAGK+D,cAAc;UAAG;UAAA,CAAAhE,cAAA,GAAAiB,CAAA,WAAAgC,QAAQ,CAACgB,UAAU,CAAC,QAAQ,CAAC;UAAA;UAAA,CAAAjE,cAAA,GAAAiB,CAAA,WAAIgC,QAAQ,CAACgB,UAAU,CAAC,YAAY,CAAC;UAAC;UAAAjE,cAAA,GAAAC,CAAA;UACpFiE,SAAS,GAAGjB,QAAQ,KAAK,cAAc;UAAC;UAAAjD,cAAA,GAAAC,CAAA;UACxCkE,eAAe,GAAGhB,uBAAuB,CAACG,IAAI,CAAC,UAAAc,GAAG;YAAA;YAAApE,cAAA,GAAAa,CAAA;YAAAb,cAAA,GAAAC,CAAA;YACtD,OAAAgD,QAAQ,CAACO,WAAW,EAAE,CAACa,QAAQ,CAACD,GAAG,CAACZ,WAAW,EAAE,CAAC;UAAlD,CAAkD,CACnD;UAAC;UAAAxD,cAAA,GAAAC,CAAA;UAEF;UAAI;UAAA,CAAAD,cAAA,GAAAiB,CAAA,WAAA+C,cAAc;UAAA;UAAA,CAAAhE,cAAA,GAAAiB,CAAA,WAAIiD,SAAS;UAAK;UAAA,CAAAlE,cAAA,GAAAiB,CAAA,WAAAkD,eAAe;UAAA;UAAA,CAAAnE,cAAA,GAAAiB,CAAA,WAAIgC,QAAQ,CAACgB,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE;YAAA;YAAAjE,cAAA,GAAAiB,CAAA;YAAAjB,cAAA,GAAAC,CAAA;YACvF,sBAAOF,QAAA,CAAA6D,YAAY,CAACU,IAAI,EAAE;UAC5B,CAAC;UAAA;UAAA;YAAAtE,cAAA,GAAAiB,CAAA;UAAA;UAAAjB,cAAA,GAAAC,CAAA;UAGa,qBAAM,IAAAE,KAAA,CAAAoE,QAAQ,EAAC;YAAEC,GAAG,EAAE5D,OAAO;YAAE6D,MAAM,EAAEC,OAAO,CAACC,GAAG,CAACC;UAAe,CAAE,CAAC;;;;;UAA7EC,KAAK,GAAG5C,EAAA,CAAA6C,IAAA,EAAqE;UAAA;UAAA9E,cAAA,GAAAC,CAAA;UAG7E8E,gBAAgB,GAAG3E,eAAe,CAACkD,IAAI,CAAC,UAAA0B,KAAK;YAAA;YAAAhF,cAAA,GAAAa,CAAA;YAAAb,cAAA,GAAAC,CAAA;YAAI,OAAAgD,QAAQ,CAACgB,UAAU,CAACe,KAAK,CAAC;UAA1B,CAA0B,CAAC;UAAC;UAAAhF,cAAA,GAAAC,CAAA;UAC7EgF,YAAY,GAAG5E,WAAW,CAACiD,IAAI,CAAC,UAAA0B,KAAK;YAAA;YAAAhF,cAAA,GAAAa,CAAA;YAAAb,cAAA,GAAAC,CAAA;YAAI,OAAAgD,QAAQ,CAACgB,UAAU,CAACe,KAAK,CAAC;UAA1B,CAA0B,CAAC;UAAC;UAAAhF,cAAA,GAAAC,CAAA;UACrEiF,mBAAmB,GAAG5E,kBAAkB,CAACgD,IAAI,CAAC,UAAA0B,KAAK;YAAA;YAAAhF,cAAA,GAAAa,CAAA;YAAAb,cAAA,GAAAC,CAAA;YAAI,OAAAgD,QAAQ,CAACgB,UAAU,CAACe,KAAK,CAAC;UAA1B,CAA0B,CAAC;UAAC;UAAAhF,cAAA,GAAAC,CAAA;UACnFkF,eAAe,GAAG5E,cAAc,CAAC+C,IAAI,CAAC,UAAA0B,KAAK;YAAA;YAAAhF,cAAA,GAAAa,CAAA;YAAAb,cAAA,GAAAC,CAAA;YAAI,OAAAgD,QAAQ,CAACgB,UAAU,CAACe,KAAK,CAAC;UAA1B,CAA0B,CAAC;UAAC;UAAAhF,cAAA,GAAAC,CAAA;UAC3EmF,gBAAgB,GAAG5E,eAAe,CAAC8C,IAAI,CAAC,UAAA0B,KAAK;YAAA;YAAAhF,cAAA,GAAAa,CAAA;YAAAb,cAAA,GAAAC,CAAA;YAAI,OAAAgD,QAAQ,CAACgB,UAAU,CAACe,KAAK,CAAC;UAA1B,CAA0B,CAAC;UAElF;UAAA;UAAAhF,cAAA,GAAAC,CAAA;UACA;UAAI;UAAA,CAAAD,cAAA,GAAAiB,CAAA,WAAAgC,QAAQ,CAACgB,UAAU,CAAC,OAAO,CAAC;UAAA;UAAA,CAAAjE,cAAA,GAAAiB,CAAA,WAAIgE,YAAY;UAAA;UAAA,CAAAjF,cAAA,GAAAiB,CAAA,WAAIkE,eAAe,GAAE;YAAA;YAAAnF,cAAA,GAAAiB,CAAA;YAAAjB,cAAA,GAAAC,CAAA;YAE7DuB,WAAW;YAAI;YAAA,CAAAxB,cAAA,GAAAiB,CAAA,WAAAgE,YAAY;YAAA;YAAA,CAAAjF,cAAA,GAAAiB,CAAA,WAAIkE,eAAe;YAAA;YAAA,CAAAnF,cAAA,GAAAiB,CAAA,WAAI,EAAE;YAAA;YAAA,CAAAjB,cAAA,GAAAiB,CAAA,WAAG,GAAG;YAAC;YAAAjB,cAAA,GAAAC,CAAA;YAC3DsB,QAAQ;YAAI;YAAA,CAAAvB,cAAA,GAAAiB,CAAA,WAAAgE,YAAY;YAAA;YAAA,CAAAjF,cAAA,GAAAiB,CAAA,WAAIkE,eAAe;YAAA;YAAA,CAAAnF,cAAA,GAAAiB,CAAA,WAAI,EAAE,GAAG,EAAE,GAAG,IAAI;YAAA;YAAA,CAAAjB,cAAA,GAAAiB,CAAA,WAAG,EAAE,GAAG,EAAE,GAAG,IAAI;YAAC;YAAAjB,cAAA,GAAAC,CAAA;YAErF,IAAIqB,aAAa,CAACV,OAAO,EAAEW,QAAQ,EAAEC,WAAW,CAAC,EAAE;cAAA;cAAAxB,cAAA,GAAAiB,CAAA;cAAAjB,cAAA,GAAAC,CAAA;cACjDyD,OAAO,CAACC,IAAI,CAAC,2BAAAZ,MAAA,CAA2BE,QAAQ,CAAE,EAAE;gBAClD5B,EAAE,EAAEV,WAAW,CAACC,OAAO,CAAC;gBACxByE,SAAS,EAAEzE,OAAO,CAACG,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;gBAC5CsE,OAAO;gBAAE;gBAAA,CAAAtF,cAAA,GAAAiB,CAAA,WAAAgE,YAAY;gBAAA;gBAAA,CAAAjF,cAAA,GAAAiB,CAAA,WAAIkE,eAAe;eACzC,CAAC;cAAC;cAAAnF,cAAA,GAAAC,CAAA;cAEH,sBAAOF,QAAA,CAAA6D,YAAY,CAACC,IAAI,CACtB;gBAAEC,KAAK,EAAE;cAA4C,CAAE,EACvD;gBAAEC,MAAM,EAAE;cAAG,CAAE,CAChB;YACH,CAAC;YAAA;YAAA;cAAA/D,cAAA,GAAAiB,CAAA;YAAA;UACH,CAAC;UAAA;UAAA;YAAAjB,cAAA,GAAAiB,CAAA;UAAA;UAED;UAAAjB,cAAA,GAAAC,CAAA;UACA,IAAIgF,YAAY,EAAE;YAAA;YAAAjF,cAAA,GAAAiB,CAAA;YAAAjB,cAAA,GAAAC,CAAA;YAChB,IAAI,CAAC4E,KAAK,EAAE;cAAA;cAAA7E,cAAA,GAAAiB,CAAA;cAAAjB,cAAA,GAAAC,CAAA;cACJsF,QAAQ,GAAG,IAAIC,GAAG,CAAC,QAAQ,EAAE5E,OAAO,CAAC6E,GAAG,CAAC;cAAC;cAAAzF,cAAA,GAAAC,CAAA;cAC1CyF,QAAQ,GAAGzC,QAAQ;cAAI;cAAA,CAAAjD,cAAA,GAAAiB,CAAA,WAAAL,OAAO,CAACsC,OAAO,CAACyC,MAAM;cAAA;cAAA,CAAA3F,cAAA,GAAAiB,CAAA,WAAI,EAAE,EAAC;cAAC;cAAAjB,cAAA,GAAAC,CAAA;cAC3DsF,QAAQ,CAACK,YAAY,CAACpD,GAAG,CAAC,aAAa,EAAEkD,QAAQ,CAAC;cAAC;cAAA1F,cAAA,GAAAC,CAAA;cACnD,sBAAOF,QAAA,CAAA6D,YAAY,CAACiC,QAAQ,CAACN,QAAQ,CAAC;YACxC,CAAC;YAAA;YAAA;cAAAvF,cAAA,GAAAiB,CAAA;YAAA;YAED;YAAAjB,cAAA,GAAAC,CAAA;YACA;YAAI;YAAA,CAAAD,cAAA,GAAAiB,CAAA,YAAC4D,KAAK,CAACiB,IAAI;YAAA;YAAA,CAAA9F,cAAA,GAAAiB,CAAA,WAAI4D,KAAK,CAACiB,IAAI,KAAK,OAAO,GAAE;cAAA;cAAA9F,cAAA,GAAAiB,CAAA;cAAAjB,cAAA,GAAAC,CAAA;cACzCyD,OAAO,CAACC,IAAI,CAAC,mDAAAZ,MAAA,CAAmDE,QAAQ,CAAE,EAAE;gBAC1E8C,MAAM,EAAElB,KAAK,CAACmB,GAAG;gBACjBC,KAAK,EAAEpB,KAAK,CAACoB,KAAK;gBAClBH,IAAI,EAAEjB,KAAK,CAACiB;eACb,CAAC;cAAC;cAAA9F,cAAA,GAAAC,CAAA;cACH,sBAAOF,QAAA,CAAA6D,YAAY,CAACC,IAAI,CACtB;gBAAEC,KAAK,EAAE;cAAuB,CAAE,EAClC;gBAAEC,MAAM,EAAE;cAAG,CAAE,CAChB;YACH,CAAC;YAAA;YAAA;cAAA/D,cAAA,GAAAiB,CAAA;YAAA;UACH,CAAC;UAAA;UAAA;YAAAjB,cAAA,GAAAiB,CAAA;UAAA;UAED;UAAAjB,cAAA,GAAAC,CAAA;UACA;UAAI;UAAA,CAAAD,cAAA,GAAAiB,CAAA,WAAA8D,gBAAgB;UAAA;UAAA,CAAA/E,cAAA,GAAAiB,CAAA,WAAI,CAAC4D,KAAK,GAAE;YAAA;YAAA7E,cAAA,GAAAiB,CAAA;YAAAjB,cAAA,GAAAC,CAAA;YAExBiG,cAAc,GAAGtF,OAAO,CAACG,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;YAAC;YAAAhB,cAAA,GAAAC,CAAA;YAC1E;YAAI;YAAA,CAAAD,cAAA,GAAAiB,CAAA,WAAAiF,cAAc;YAAA;YAAA,CAAAlG,cAAA,GAAAiB,CAAA,WAAIkF,QAAQ,CAACD,cAAc,CAAC,GAAG,CAAC,GAAE;cAAA;cAAAlG,cAAA,GAAAiB,CAAA;cAAAjB,cAAA,GAAAC,CAAA;cAClDyD,OAAO,CAACI,KAAK,CAAC,wCAAwC,CAAC;cAAC;cAAA9D,cAAA,GAAAC,CAAA;cACxD,sBAAOF,QAAA,CAAA6D,YAAY,CAACC,IAAI,CACtB;gBAAEC,KAAK,EAAE;cAAuC,CAAE,EAClD;gBAAEC,MAAM,EAAE;cAAG,CAAE,CAChB;YACH,CAAC;YAAA;YAAA;cAAA/D,cAAA,GAAAiB,CAAA;YAAA;YAAAjB,cAAA,GAAAC,CAAA;YAEKsF,QAAQ,GAAG,IAAIC,GAAG,CAAC,QAAQ,EAAE5E,OAAO,CAAC6E,GAAG,CAAC;YAAC;YAAAzF,cAAA,GAAAC,CAAA;YAE1CyF,QAAQ,GAAGzC,QAAQ;YAAI;YAAA,CAAAjD,cAAA,GAAAiB,CAAA,WAAAL,OAAO,CAACsC,OAAO,CAACyC,MAAM;YAAA;YAAA,CAAA3F,cAAA,GAAAiB,CAAA,WAAI,EAAE,EAAC;YAAC;YAAAjB,cAAA,GAAAC,CAAA;YAC3DsF,QAAQ,CAACK,YAAY,CAACpD,GAAG,CAAC,aAAa,EAAEkD,QAAQ,CAAC;YAAC;YAAA1F,cAAA,GAAAC,CAAA;YAE7CmG,UAAA,GAAWrG,QAAA,CAAA6D,YAAY,CAACiC,QAAQ,CAACN,QAAQ,CAAC;YAAC;YAAAvF,cAAA,GAAAC,CAAA;YAE3CoG,aAAa,GAAGF,QAAQ;YAAC;YAAA,CAAAnG,cAAA,GAAAiB,CAAA,WAAAiF,cAAc;YAAA;YAAA,CAAAlG,cAAA,GAAAiB,CAAA,WAAI,GAAG,EAAC,GAAG,CAAC;YAAC;YAAAjB,cAAA,GAAAC,CAAA;YAC1DmG,UAAQ,CAACrF,OAAO,CAACyB,GAAG,CAAC,6BAA6B,EAAE6D,aAAa,CAACC,QAAQ,EAAE,CAAC;YAAC;YAAAtG,cAAA,GAAAC,CAAA;YAC9E,sBAAOmG,UAAQ;UACjB,CAAC;UAAA;UAAA;YAAApG,cAAA,GAAAiB,CAAA;UAAA;UAED;UAAAjB,cAAA,GAAAC,CAAA;UACA,IAAIkF,eAAe,EAAE;YAAA;YAAAnF,cAAA,GAAAiB,CAAA;YAAAjB,cAAA,GAAAC,CAAA;YACnB,IAAI,CAAC4E,KAAK,EAAE;cAAA;cAAA7E,cAAA,GAAAiB,CAAA;cAAAjB,cAAA,GAAAC,CAAA;cACV,sBAAOF,QAAA,CAAA6D,YAAY,CAACC,IAAI,CACtB;gBAAEC,KAAK,EAAE;cAAyB,CAAE,EACpC;gBAAEC,MAAM,EAAE;cAAG,CAAE,CAChB;YACH,CAAC;YAAA;YAAA;cAAA/D,cAAA,GAAAiB,CAAA;YAAA;YAED;YAAAjB,cAAA,GAAAC,CAAA;YACA;YAAI;YAAA,CAAAD,cAAA,GAAAiB,CAAA,YAAC4D,KAAK,CAACiB,IAAI;YAAA;YAAA,CAAA9F,cAAA,GAAAiB,CAAA,WAAI4D,KAAK,CAACiB,IAAI,KAAK,OAAO,GAAE;cAAA;cAAA9F,cAAA,GAAAiB,CAAA;cAAAjB,cAAA,GAAAC,CAAA;cACzCyD,OAAO,CAACC,IAAI,CAAC,iDAAAZ,MAAA,CAAiDE,QAAQ,CAAE,EAAE;gBACxE8C,MAAM,EAAElB,KAAK,CAACmB,GAAG;gBACjBC,KAAK,EAAEpB,KAAK,CAACoB,KAAK;gBAClBH,IAAI,EAAEjB,KAAK,CAACiB;eACb,CAAC;cAAC;cAAA9F,cAAA,GAAAC,CAAA;cACH,sBAAOF,QAAA,CAAA6D,YAAY,CAACC,IAAI,CACtB;gBAAEC,KAAK,EAAE;cAAuB,CAAE,EAClC;gBAAEC,MAAM,EAAE;cAAG,CAAE,CAChB;YACH,CAAC;YAAA;YAAA;cAAA/D,cAAA,GAAAiB,CAAA;YAAA;UACH,CAAC;UAAA;UAAA;YAAAjB,cAAA,GAAAiB,CAAA;UAAA;UAED;UAAAjB,cAAA,GAAAC,CAAA;UACA;UAAI;UAAA,CAAAD,cAAA,GAAAiB,CAAA,WAAAiE,mBAAmB;UAAA;UAAA,CAAAlF,cAAA,GAAAiB,CAAA,WAAI,CAAC4D,KAAK;UAAA;UAAA,CAAA7E,cAAA,GAAAiB,CAAA,WAAI,CAACmE,gBAAgB,GAAE;YAAA;YAAApF,cAAA,GAAAiB,CAAA;YAAAjB,cAAA,GAAAC,CAAA;YACtD,sBAAOF,QAAA,CAAA6D,YAAY,CAACC,IAAI,CACtB;cAAEC,KAAK,EAAE;YAAyB,CAAE,EACpC;cAAEC,MAAM,EAAE;YAAG,CAAE,CAChB;UACH,CAAC;UAAA;UAAA;YAAA/D,cAAA,GAAAiB,CAAA;UAAA;UAED;UAAAjB,cAAA,GAAAC,CAAA;UACA;UAAI;UAAA,CAAAD,cAAA,GAAAiB,CAAA,WAAA4D,KAAK;UAAK;UAAA,CAAA7E,cAAA,GAAAiB,CAAA,WAAAgC,QAAQ,KAAK,QAAQ;UAAA;UAAA,CAAAjD,cAAA,GAAAiB,CAAA,WAAIgC,QAAQ,KAAK,SAAS,EAAC,EAAE;YAAA;YAAAjD,cAAA,GAAAiB,CAAA;YAAAjB,cAAA,GAAAC,CAAA;YAExDiG,cAAc,GAAGtF,OAAO,CAACG,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;YAAC;YAAAhB,cAAA,GAAAC,CAAA;YAC1E;YAAI;YAAA,CAAAD,cAAA,GAAAiB,CAAA,WAAAiF,cAAc;YAAA;YAAA,CAAAlG,cAAA,GAAAiB,CAAA,WAAIkF,QAAQ,CAACD,cAAc,CAAC,GAAG,CAAC,GAAE;cAAA;cAAAlG,cAAA,GAAAiB,CAAA;cAAAjB,cAAA,GAAAC,CAAA;cAClDyD,OAAO,CAACI,KAAK,CAAC,2DAA2D,CAAC;cAAC;cAAA9D,cAAA,GAAAC,CAAA;cAErEsG,UAAA,GAAWxG,QAAA,CAAA6D,YAAY,CAACU,IAAI,EAAE;cAAC;cAAAtE,cAAA,GAAAC,CAAA;cACrC,sBAAOwC,kBAAkB,CAAC8D,UAAQ,CAAC;YACrC,CAAC;YAAA;YAAA;cAAAvG,cAAA,GAAAiB,CAAA;YAAA;YAAAjB,cAAA,GAAAC,CAAA;YAGKuG,WAAW,GAAG5F,OAAO,CAACsC,OAAO,CAAC0C,YAAY,CAAC5E,GAAG,CAAC,aAAa,CAAC;YAAC;YAAAhB,cAAA,GAAAC,CAAA;YAC9DwG,UAAU;YAAG;YAAA,CAAAzG,cAAA,GAAAiB,CAAA,WAAAuF,WAAW;YAAA;YAAA,CAAAxG,cAAA,GAAAiB,CAAA,WAAIuF,WAAW,KAAK,QAAQ;YAAA;YAAA,CAAAxG,cAAA,GAAAiB,CAAA,WAAIuF,WAAW,KAAK,SAAS;YAAA;YAAA,CAAAxG,cAAA,GAAAiB,CAAA,WACnFuF,WAAW;YAAA;YAAA,CAAAxG,cAAA,GAAAiB,CAAA,WACX,YAAY;YAAC;YAAAjB,cAAA,GAAAC,CAAA;YAEXyG,UAAA,GAAW3G,QAAA,CAAA6D,YAAY,CAACiC,QAAQ,CAAC,IAAIL,GAAG,CAACiB,UAAU,EAAE7F,OAAO,CAAC6E,GAAG,CAAC,CAAC;YAAC;YAAAzF,cAAA,GAAAC,CAAA;YAEnEoG,aAAa,GAAGF,QAAQ;YAAC;YAAA,CAAAnG,cAAA,GAAAiB,CAAA,WAAAiF,cAAc;YAAA;YAAA,CAAAlG,cAAA,GAAAiB,CAAA,WAAI,GAAG,EAAC,GAAG,CAAC;YAAC;YAAAjB,cAAA,GAAAC,CAAA;YAC1DyG,UAAQ,CAAC3F,OAAO,CAACyB,GAAG,CAAC,6BAA6B,EAAE6D,aAAa,CAACC,QAAQ,EAAE,CAAC;YAAC;YAAAtG,cAAA,GAAAC,CAAA;YAC9E,sBAAOyG,UAAQ;UACjB,CAAC;UAAA;UAAA;YAAA1G,cAAA,GAAAiB,CAAA;UAAA;UAAAjB,cAAA,GAAAC,CAAA;UAEKyC,QAAQ,GAAG3C,QAAA,CAAA6D,YAAY,CAACU,IAAI,EAAE;UAAC;UAAAtE,cAAA,GAAAC,CAAA;UACrC,sBAAOwC,kBAAkB,CAACC,QAAQ,CAAC;;;;;AACpC;AAAA1C,cAAA,GAAAC,CAAA;AAEYJ,OAAA,CAAA8G,MAAM,GAAG;EACpBC,OAAO,EAAE;EACP;;;;;;;EAOA,uDAAuD;CAE1D", "ignoreList": []}