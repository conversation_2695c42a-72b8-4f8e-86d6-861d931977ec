839402f189e50ca4dc94c2185a123da6
"use strict";

/* istanbul ignore next */
function cov_1ad6gddzlg() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/middleware.ts";
  var hash = "d092a395a65fa204a191b9f4137770421b0e0247";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/middleware.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 0
        },
        end: {
          line: 38,
          column: 62
        }
      },
      "68": {
        start: {
          line: 39,
          column: 0
        },
        end: {
          line: 39,
          column: 24
        }
      },
      "69": {
        start: {
          line: 40,
          column: 0
        },
        end: {
          line: 40,
          column: 32
        }
      },
      "70": {
        start: {
          line: 41,
          column: 15
        },
        end: {
          line: 41,
          column: 37
        }
      },
      "71": {
        start: {
          line: 42,
          column: 12
        },
        end: {
          line: 42,
          column: 36
        }
      },
      "72": {
        start: {
          line: 44,
          column: 22
        },
        end: {
          line: 55,
          column: 1
        }
      },
      "73": {
        start: {
          line: 57,
          column: 18
        },
        end: {
          line: 59,
          column: 1
        }
      },
      "74": {
        start: {
          line: 61,
          column: 25
        },
        end: {
          line: 73,
          column: 1
        }
      },
      "75": {
        start: {
          line: 75,
          column: 21
        },
        end: {
          line: 77,
          column: 1
        }
      },
      "76": {
        start: {
          line: 79,
          column: 22
        },
        end: {
          line: 86,
          column: 1
        }
      },
      "77": {
        start: {
          line: 88,
          column: 21
        },
        end: {
          line: 88,
          column: 30
        }
      },
      "78": {
        start: {
          line: 90,
          column: 20
        },
        end: {
          line: 90,
          column: 58
        }
      },
      "79": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 93,
          column: 5
        }
      },
      "80": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 92,
          column: 46
        }
      },
      "81": {
        start: {
          line: 94,
          column: 17
        },
        end: {
          line: 94,
          column: 49
        }
      },
      "82": {
        start: {
          line: 95,
          column: 4
        },
        end: {
          line: 97,
          column: 5
        }
      },
      "83": {
        start: {
          line: 96,
          column: 8
        },
        end: {
          line: 96,
          column: 22
        }
      },
      "84": {
        start: {
          line: 98,
          column: 4
        },
        end: {
          line: 98,
          column: 35
        }
      },
      "85": {
        start: {
          line: 101,
          column: 4
        },
        end: {
          line: 101,
          column: 59
        }
      },
      "86": {
        start: {
          line: 101,
          column: 31
        },
        end: {
          line: 101,
          column: 57
        }
      },
      "87": {
        start: {
          line: 102,
          column: 4
        },
        end: {
          line: 102,
          column: 54
        }
      },
      "88": {
        start: {
          line: 102,
          column: 34
        },
        end: {
          line: 102,
          column: 52
        }
      },
      "89": {
        start: {
          line: 103,
          column: 19
        },
        end: {
          line: 103,
          column: 39
        }
      },
      "90": {
        start: {
          line: 104,
          column: 14
        },
        end: {
          line: 104,
          column: 24
        }
      },
      "91": {
        start: {
          line: 105,
          column: 22
        },
        end: {
          line: 105,
          column: 36
        }
      },
      "92": {
        start: {
          line: 107,
          column: 4
        },
        end: {
          line: 112,
          column: 7
        }
      },
      "93": {
        start: {
          line: 108,
          column: 18
        },
        end: {
          line: 108,
          column: 23
        }
      },
      "94": {
        start: {
          line: 108,
          column: 33
        },
        end: {
          line: 108,
          column: 38
        }
      },
      "95": {
        start: {
          line: 109,
          column: 8
        },
        end: {
          line: 111,
          column: 9
        }
      },
      "96": {
        start: {
          line: 110,
          column: 12
        },
        end: {
          line: 110,
          column: 39
        }
      },
      "97": {
        start: {
          line: 114,
          column: 16
        },
        end: {
          line: 114,
          column: 87
        }
      },
      "98": {
        start: {
          line: 116,
          column: 4
        },
        end: {
          line: 119,
          column: 5
        }
      },
      "99": {
        start: {
          line: 117,
          column: 8
        },
        end: {
          line: 117,
          column: 24
        }
      },
      "100": {
        start: {
          line: 118,
          column: 8
        },
        end: {
          line: 118,
          column: 41
        }
      },
      "101": {
        start: {
          line: 121,
          column: 4
        },
        end: {
          line: 121,
          column: 18
        }
      },
      "102": {
        start: {
          line: 122,
          column: 4
        },
        end: {
          line: 122,
          column: 40
        }
      },
      "103": {
        start: {
          line: 123,
          column: 4
        },
        end: {
          line: 123,
          column: 37
        }
      },
      "104": {
        start: {
          line: 127,
          column: 4
        },
        end: {
          line: 127,
          column: 52
        }
      },
      "105": {
        start: {
          line: 128,
          column: 4
        },
        end: {
          line: 128,
          column: 62
        }
      },
      "106": {
        start: {
          line: 129,
          column: 4
        },
        end: {
          line: 129,
          column: 79
        }
      },
      "107": {
        start: {
          line: 130,
          column: 4
        },
        end: {
          line: 130,
          column: 62
        }
      },
      "108": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 131,
          column: 91
        }
      },
      "109": {
        start: {
          line: 133,
          column: 16
        },
        end: {
          line: 133,
          column: 35
        }
      },
      "110": {
        start: {
          line: 135,
          column: 14
        },
        end: {
          line: 147,
          column: 16
        }
      },
      "111": {
        start: {
          line: 148,
          column: 4
        },
        end: {
          line: 148,
          column: 57
        }
      },
      "112": {
        start: {
          line: 149,
          column: 4
        },
        end: {
          line: 149,
          column: 43
        }
      },
      "113": {
        start: {
          line: 151,
          column: 4
        },
        end: {
          line: 151,
          column: 102
        }
      },
      "114": {
        start: {
          line: 153,
          column: 4
        },
        end: {
          line: 153,
          column: 73
        }
      },
      "115": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 154,
          column: 70
        }
      },
      "116": {
        start: {
          line: 155,
          column: 4
        },
        end: {
          line: 155,
          column: 72
        }
      },
      "117": {
        start: {
          line: 156,
          column: 4
        },
        end: {
          line: 156,
          column: 20
        }
      },
      "118": {
        start: {
          line: 159,
          column: 4
        },
        end: {
          line: 283,
          column: 7
        }
      },
      "119": {
        start: {
          line: 161,
          column: 8
        },
        end: {
          line: 282,
          column: 11
        }
      },
      "120": {
        start: {
          line: 162,
          column: 12
        },
        end: {
          line: 281,
          column: 13
        }
      },
      "121": {
        start: {
          line: 164,
          column: 20
        },
        end: {
          line: 164,
          column: 56
        }
      },
      "122": {
        start: {
          line: 165,
          column: 20
        },
        end: {
          line: 168,
          column: 22
        }
      },
      "123": {
        start: {
          line: 169,
          column: 20
        },
        end: {
          line: 173,
          column: 22
        }
      },
      "124": {
        start: {
          line: 174,
          column: 20
        },
        end: {
          line: 176,
          column: 23
        }
      },
      "125": {
        start: {
          line: 175,
          column: 24
        },
        end: {
          line: 175,
          column: 86
        }
      },
      "126": {
        start: {
          line: 177,
          column: 20
        },
        end: {
          line: 180,
          column: 21
        }
      },
      "127": {
        start: {
          line: 178,
          column: 24
        },
        end: {
          line: 178,
          column: 92
        }
      },
      "128": {
        start: {
          line: 179,
          column: 24
        },
        end: {
          line: 179,
          column: 119
        }
      },
      "129": {
        start: {
          line: 181,
          column: 20
        },
        end: {
          line: 181,
          column: 104
        }
      },
      "130": {
        start: {
          line: 182,
          column: 20
        },
        end: {
          line: 182,
          column: 60
        }
      },
      "131": {
        start: {
          line: 183,
          column: 20
        },
        end: {
          line: 185,
          column: 23
        }
      },
      "132": {
        start: {
          line: 184,
          column: 24
        },
        end: {
          line: 184,
          column: 82
        }
      },
      "133": {
        start: {
          line: 186,
          column: 20
        },
        end: {
          line: 188,
          column: 21
        }
      },
      "134": {
        start: {
          line: 187,
          column: 24
        },
        end: {
          line: 187,
          column: 76
        }
      },
      "135": {
        start: {
          line: 189,
          column: 20
        },
        end: {
          line: 189,
          column: 117
        }
      },
      "136": {
        start: {
          line: 191,
          column: 20
        },
        end: {
          line: 191,
          column: 38
        }
      },
      "137": {
        start: {
          line: 192,
          column: 20
        },
        end: {
          line: 192,
          column: 117
        }
      },
      "138": {
        start: {
          line: 192,
          column: 79
        },
        end: {
          line: 192,
          column: 113
        }
      },
      "139": {
        start: {
          line: 193,
          column: 20
        },
        end: {
          line: 193,
          column: 109
        }
      },
      "140": {
        start: {
          line: 193,
          column: 71
        },
        end: {
          line: 193,
          column: 105
        }
      },
      "141": {
        start: {
          line: 194,
          column: 20
        },
        end: {
          line: 194,
          column: 123
        }
      },
      "142": {
        start: {
          line: 194,
          column: 85
        },
        end: {
          line: 194,
          column: 119
        }
      },
      "143": {
        start: {
          line: 195,
          column: 20
        },
        end: {
          line: 195,
          column: 115
        }
      },
      "144": {
        start: {
          line: 195,
          column: 77
        },
        end: {
          line: 195,
          column: 111
        }
      },
      "145": {
        start: {
          line: 196,
          column: 20
        },
        end: {
          line: 196,
          column: 117
        }
      },
      "146": {
        start: {
          line: 196,
          column: 79
        },
        end: {
          line: 196,
          column: 113
        }
      },
      "147": {
        start: {
          line: 198,
          column: 20
        },
        end: {
          line: 209,
          column: 21
        }
      },
      "148": {
        start: {
          line: 199,
          column: 24
        },
        end: {
          line: 199,
          column: 83
        }
      },
      "149": {
        start: {
          line: 200,
          column: 24
        },
        end: {
          line: 200,
          column: 103
        }
      },
      "150": {
        start: {
          line: 201,
          column: 24
        },
        end: {
          line: 208,
          column: 25
        }
      },
      "151": {
        start: {
          line: 202,
          column: 28
        },
        end: {
          line: 206,
          column: 31
        }
      },
      "152": {
        start: {
          line: 207,
          column: 28
        },
        end: {
          line: 207,
          column: 152
        }
      },
      "153": {
        start: {
          line: 211,
          column: 20
        },
        end: {
          line: 227,
          column: 21
        }
      },
      "154": {
        start: {
          line: 212,
          column: 24
        },
        end: {
          line: 217,
          column: 25
        }
      },
      "155": {
        start: {
          line: 213,
          column: 28
        },
        end: {
          line: 213,
          column: 70
        }
      },
      "156": {
        start: {
          line: 214,
          column: 28
        },
        end: {
          line: 214,
          column: 81
        }
      },
      "157": {
        start: {
          line: 215,
          column: 28
        },
        end: {
          line: 215,
          column: 79
        }
      },
      "158": {
        start: {
          line: 216,
          column: 28
        },
        end: {
          line: 216,
          column: 92
        }
      },
      "159": {
        start: {
          line: 219,
          column: 24
        },
        end: {
          line: 226,
          column: 25
        }
      },
      "160": {
        start: {
          line: 220,
          column: 28
        },
        end: {
          line: 224,
          column: 31
        }
      },
      "161": {
        start: {
          line: 225,
          column: 28
        },
        end: {
          line: 225,
          column: 131
        }
      },
      "162": {
        start: {
          line: 229,
          column: 20
        },
        end: {
          line: 242,
          column: 21
        }
      },
      "163": {
        start: {
          line: 230,
          column: 24
        },
        end: {
          line: 230,
          column: 92
        }
      },
      "164": {
        start: {
          line: 231,
          column: 24
        },
        end: {
          line: 234,
          column: 25
        }
      },
      "165": {
        start: {
          line: 232,
          column: 28
        },
        end: {
          line: 232,
          column: 84
        }
      },
      "166": {
        start: {
          line: 233,
          column: 28
        },
        end: {
          line: 233,
          column: 147
        }
      },
      "167": {
        start: {
          line: 235,
          column: 24
        },
        end: {
          line: 235,
          column: 66
        }
      },
      "168": {
        start: {
          line: 236,
          column: 24
        },
        end: {
          line: 236,
          column: 77
        }
      },
      "169": {
        start: {
          line: 237,
          column: 24
        },
        end: {
          line: 237,
          column: 75
        }
      },
      "170": {
        start: {
          line: 238,
          column: 24
        },
        end: {
          line: 238,
          column: 78
        }
      },
      "171": {
        start: {
          line: 239,
          column: 24
        },
        end: {
          line: 239,
          column: 76
        }
      },
      "172": {
        start: {
          line: 240,
          column: 24
        },
        end: {
          line: 240,
          column: 104
        }
      },
      "173": {
        start: {
          line: 241,
          column: 24
        },
        end: {
          line: 241,
          column: 58
        }
      },
      "174": {
        start: {
          line: 244,
          column: 20
        },
        end: {
          line: 257,
          column: 21
        }
      },
      "175": {
        start: {
          line: 245,
          column: 24
        },
        end: {
          line: 247,
          column: 25
        }
      },
      "176": {
        start: {
          line: 246,
          column: 28
        },
        end: {
          line: 246,
          column: 133
        }
      },
      "177": {
        start: {
          line: 249,
          column: 24
        },
        end: {
          line: 256,
          column: 25
        }
      },
      "178": {
        start: {
          line: 250,
          column: 28
        },
        end: {
          line: 254,
          column: 31
        }
      },
      "179": {
        start: {
          line: 255,
          column: 28
        },
        end: {
          line: 255,
          column: 131
        }
      },
      "180": {
        start: {
          line: 259,
          column: 20
        },
        end: {
          line: 261,
          column: 21
        }
      },
      "181": {
        start: {
          line: 260,
          column: 24
        },
        end: {
          line: 260,
          column: 129
        }
      },
      "182": {
        start: {
          line: 263,
          column: 20
        },
        end: {
          line: 278,
          column: 21
        }
      },
      "183": {
        start: {
          line: 264,
          column: 24
        },
        end: {
          line: 264,
          column: 92
        }
      },
      "184": {
        start: {
          line: 265,
          column: 24
        },
        end: {
          line: 269,
          column: 25
        }
      },
      "185": {
        start: {
          line: 266,
          column: 28
        },
        end: {
          line: 266,
          column: 103
        }
      },
      "186": {
        start: {
          line: 267,
          column: 28
        },
        end: {
          line: 267,
          column: 70
        }
      },
      "187": {
        start: {
          line: 268,
          column: 28
        },
        end: {
          line: 268,
          column: 82
        }
      },
      "188": {
        start: {
          line: 270,
          column: 24
        },
        end: {
          line: 270,
          column: 86
        }
      },
      "189": {
        start: {
          line: 271,
          column: 24
        },
        end: {
          line: 273,
          column: 43
        }
      },
      "190": {
        start: {
          line: 274,
          column: 24
        },
        end: {
          line: 274,
          column: 102
        }
      },
      "191": {
        start: {
          line: 275,
          column: 24
        },
        end: {
          line: 275,
          column: 76
        }
      },
      "192": {
        start: {
          line: 276,
          column: 24
        },
        end: {
          line: 276,
          column: 104
        }
      },
      "193": {
        start: {
          line: 277,
          column: 24
        },
        end: {
          line: 277,
          column: 58
        }
      },
      "194": {
        start: {
          line: 279,
          column: 20
        },
        end: {
          line: 279,
          column: 60
        }
      },
      "195": {
        start: {
          line: 280,
          column: 20
        },
        end: {
          line: 280,
          column: 72
        }
      },
      "196": {
        start: {
          line: 285,
          column: 0
        },
        end: {
          line: 296,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "getClientIP",
        decl: {
          start: {
            line: 89,
            column: 9
          },
          end: {
            line: 89,
            column: 20
          }
        },
        loc: {
          start: {
            line: 89,
            column: 30
          },
          end: {
            line: 99,
            column: 1
          }
        },
        line: 89
      },
      "14": {
        name: "isRateLimited",
        decl: {
          start: {
            line: 100,
            column: 9
          },
          end: {
            line: 100,
            column: 22
          }
        },
        loc: {
          start: {
            line: 100,
            column: 55
          },
          end: {
            line: 124,
            column: 1
          }
        },
        line: 100
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 107,
            column: 49
          },
          end: {
            line: 107,
            column: 50
          }
        },
        loc: {
          start: {
            line: 107,
            column: 63
          },
          end: {
            line: 112,
            column: 5
          }
        },
        line: 107
      },
      "16": {
        name: "addSecurityHeaders",
        decl: {
          start: {
            line: 125,
            column: 9
          },
          end: {
            line: 125,
            column: 27
          }
        },
        loc: {
          start: {
            line: 125,
            column: 38
          },
          end: {
            line: 157,
            column: 1
          }
        },
        line: 125
      },
      "17": {
        name: "middleware",
        decl: {
          start: {
            line: 158,
            column: 9
          },
          end: {
            line: 158,
            column: 19
          }
        },
        loc: {
          start: {
            line: 158,
            column: 29
          },
          end: {
            line: 284,
            column: 1
          }
        },
        line: 158
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 159,
            column: 43
          },
          end: {
            line: 159,
            column: 44
          }
        },
        loc: {
          start: {
            line: 159,
            column: 55
          },
          end: {
            line: 283,
            column: 5
          }
        },
        line: 159
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 161,
            column: 33
          },
          end: {
            line: 161,
            column: 34
          }
        },
        loc: {
          start: {
            line: 161,
            column: 47
          },
          end: {
            line: 282,
            column: 9
          }
        },
        line: 161
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 174,
            column: 61
          },
          end: {
            line: 174,
            column: 62
          }
        },
        loc: {
          start: {
            line: 174,
            column: 80
          },
          end: {
            line: 176,
            column: 21
          }
        },
        line: 174
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 183,
            column: 67
          },
          end: {
            line: 183,
            column: 68
          }
        },
        loc: {
          start: {
            line: 183,
            column: 82
          },
          end: {
            line: 185,
            column: 21
          }
        },
        line: 183
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 192,
            column: 60
          },
          end: {
            line: 192,
            column: 61
          }
        },
        loc: {
          start: {
            line: 192,
            column: 77
          },
          end: {
            line: 192,
            column: 115
          }
        },
        line: 192
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 193,
            column: 52
          },
          end: {
            line: 193,
            column: 53
          }
        },
        loc: {
          start: {
            line: 193,
            column: 69
          },
          end: {
            line: 193,
            column: 107
          }
        },
        line: 193
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 194,
            column: 66
          },
          end: {
            line: 194,
            column: 67
          }
        },
        loc: {
          start: {
            line: 194,
            column: 83
          },
          end: {
            line: 194,
            column: 121
          }
        },
        line: 194
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 195,
            column: 58
          },
          end: {
            line: 195,
            column: 59
          }
        },
        loc: {
          start: {
            line: 195,
            column: 75
          },
          end: {
            line: 195,
            column: 113
          }
        },
        line: 195
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 196,
            column: 60
          },
          end: {
            line: 196,
            column: 61
          }
        },
        loc: {
          start: {
            line: 196,
            column: 77
          },
          end: {
            line: 196,
            column: 115
          }
        },
        line: 196
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 91,
            column: 4
          },
          end: {
            line: 93,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 91,
            column: 4
          },
          end: {
            line: 93,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 91
      },
      "33": {
        loc: {
          start: {
            line: 95,
            column: 4
          },
          end: {
            line: 97,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 95,
            column: 4
          },
          end: {
            line: 97,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 95
      },
      "34": {
        loc: {
          start: {
            line: 98,
            column: 11
          },
          end: {
            line: 98,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 98,
            column: 11
          },
          end: {
            line: 98,
            column: 21
          }
        }, {
          start: {
            line: 98,
            column: 25
          },
          end: {
            line: 98,
            column: 34
          }
        }],
        line: 98
      },
      "35": {
        loc: {
          start: {
            line: 101,
            column: 4
          },
          end: {
            line: 101,
            column: 59
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 101,
            column: 4
          },
          end: {
            line: 101,
            column: 59
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 101
      },
      "36": {
        loc: {
          start: {
            line: 102,
            column: 4
          },
          end: {
            line: 102,
            column: 54
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 102,
            column: 4
          },
          end: {
            line: 102,
            column: 54
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 102
      },
      "37": {
        loc: {
          start: {
            line: 109,
            column: 8
          },
          end: {
            line: 111,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 109,
            column: 8
          },
          end: {
            line: 111,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 109
      },
      "38": {
        loc: {
          start: {
            line: 114,
            column: 16
          },
          end: {
            line: 114,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 114,
            column: 16
          },
          end: {
            line: 114,
            column: 44
          }
        }, {
          start: {
            line: 114,
            column: 48
          },
          end: {
            line: 114,
            column: 87
          }
        }],
        line: 114
      },
      "39": {
        loc: {
          start: {
            line: 116,
            column: 4
          },
          end: {
            line: 119,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 116,
            column: 4
          },
          end: {
            line: 119,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 116
      },
      "40": {
        loc: {
          start: {
            line: 162,
            column: 12
          },
          end: {
            line: 281,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 163,
            column: 16
          },
          end: {
            line: 189,
            column: 117
          }
        }, {
          start: {
            line: 190,
            column: 16
          },
          end: {
            line: 280,
            column: 72
          }
        }],
        line: 162
      },
      "41": {
        loc: {
          start: {
            line: 177,
            column: 20
          },
          end: {
            line: 180,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 177,
            column: 20
          },
          end: {
            line: 180,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 177
      },
      "42": {
        loc: {
          start: {
            line: 181,
            column: 37
          },
          end: {
            line: 181,
            column: 103
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 181,
            column: 37
          },
          end: {
            line: 181,
            column: 66
          }
        }, {
          start: {
            line: 181,
            column: 70
          },
          end: {
            line: 181,
            column: 103
          }
        }],
        line: 181
      },
      "43": {
        loc: {
          start: {
            line: 186,
            column: 20
          },
          end: {
            line: 188,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 186,
            column: 20
          },
          end: {
            line: 188,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 186
      },
      "44": {
        loc: {
          start: {
            line: 186,
            column: 24
          },
          end: {
            line: 186,
            column: 107
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 186,
            column: 24
          },
          end: {
            line: 186,
            column: 38
          }
        }, {
          start: {
            line: 186,
            column: 42
          },
          end: {
            line: 186,
            column: 51
          }
        }, {
          start: {
            line: 186,
            column: 56
          },
          end: {
            line: 186,
            column: 71
          }
        }, {
          start: {
            line: 186,
            column: 75
          },
          end: {
            line: 186,
            column: 106
          }
        }],
        line: 186
      },
      "45": {
        loc: {
          start: {
            line: 198,
            column: 20
          },
          end: {
            line: 209,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 198,
            column: 20
          },
          end: {
            line: 209,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 198
      },
      "46": {
        loc: {
          start: {
            line: 198,
            column: 24
          },
          end: {
            line: 198,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 198,
            column: 24
          },
          end: {
            line: 198,
            column: 52
          }
        }, {
          start: {
            line: 198,
            column: 56
          },
          end: {
            line: 198,
            column: 68
          }
        }, {
          start: {
            line: 198,
            column: 72
          },
          end: {
            line: 198,
            column: 87
          }
        }],
        line: 198
      },
      "47": {
        loc: {
          start: {
            line: 199,
            column: 38
          },
          end: {
            line: 199,
            column: 82
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 199,
            column: 74
          },
          end: {
            line: 199,
            column: 76
          }
        }, {
          start: {
            line: 199,
            column: 79
          },
          end: {
            line: 199,
            column: 82
          }
        }],
        line: 199
      },
      "48": {
        loc: {
          start: {
            line: 199,
            column: 39
          },
          end: {
            line: 199,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 199,
            column: 39
          },
          end: {
            line: 199,
            column: 51
          }
        }, {
          start: {
            line: 199,
            column: 55
          },
          end: {
            line: 199,
            column: 70
          }
        }],
        line: 199
      },
      "49": {
        loc: {
          start: {
            line: 200,
            column: 35
          },
          end: {
            line: 200,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 200,
            column: 71
          },
          end: {
            line: 200,
            column: 85
          }
        }, {
          start: {
            line: 200,
            column: 88
          },
          end: {
            line: 200,
            column: 102
          }
        }],
        line: 200
      },
      "50": {
        loc: {
          start: {
            line: 200,
            column: 36
          },
          end: {
            line: 200,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 200,
            column: 36
          },
          end: {
            line: 200,
            column: 48
          }
        }, {
          start: {
            line: 200,
            column: 52
          },
          end: {
            line: 200,
            column: 67
          }
        }],
        line: 200
      },
      "51": {
        loc: {
          start: {
            line: 201,
            column: 24
          },
          end: {
            line: 208,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 201,
            column: 24
          },
          end: {
            line: 208,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 201
      },
      "52": {
        loc: {
          start: {
            line: 205,
            column: 41
          },
          end: {
            line: 205,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 205,
            column: 41
          },
          end: {
            line: 205,
            column: 53
          }
        }, {
          start: {
            line: 205,
            column: 57
          },
          end: {
            line: 205,
            column: 72
          }
        }],
        line: 205
      },
      "53": {
        loc: {
          start: {
            line: 211,
            column: 20
          },
          end: {
            line: 227,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 211,
            column: 20
          },
          end: {
            line: 227,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 211
      },
      "54": {
        loc: {
          start: {
            line: 212,
            column: 24
          },
          end: {
            line: 217,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 212,
            column: 24
          },
          end: {
            line: 217,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 212
      },
      "55": {
        loc: {
          start: {
            line: 214,
            column: 51
          },
          end: {
            line: 214,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 214,
            column: 51
          },
          end: {
            line: 214,
            column: 73
          }
        }, {
          start: {
            line: 214,
            column: 77
          },
          end: {
            line: 214,
            column: 79
          }
        }],
        line: 214
      },
      "56": {
        loc: {
          start: {
            line: 219,
            column: 24
          },
          end: {
            line: 226,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 219,
            column: 24
          },
          end: {
            line: 226,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 219
      },
      "57": {
        loc: {
          start: {
            line: 219,
            column: 28
          },
          end: {
            line: 219,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 219,
            column: 28
          },
          end: {
            line: 219,
            column: 39
          }
        }, {
          start: {
            line: 219,
            column: 43
          },
          end: {
            line: 219,
            column: 65
          }
        }],
        line: 219
      },
      "58": {
        loc: {
          start: {
            line: 229,
            column: 20
          },
          end: {
            line: 242,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 229,
            column: 20
          },
          end: {
            line: 242,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 229
      },
      "59": {
        loc: {
          start: {
            line: 229,
            column: 24
          },
          end: {
            line: 229,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 229,
            column: 24
          },
          end: {
            line: 229,
            column: 40
          }
        }, {
          start: {
            line: 229,
            column: 44
          },
          end: {
            line: 229,
            column: 50
          }
        }],
        line: 229
      },
      "60": {
        loc: {
          start: {
            line: 231,
            column: 24
          },
          end: {
            line: 234,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 231,
            column: 24
          },
          end: {
            line: 234,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 231
      },
      "61": {
        loc: {
          start: {
            line: 231,
            column: 28
          },
          end: {
            line: 231,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 231,
            column: 28
          },
          end: {
            line: 231,
            column: 42
          }
        }, {
          start: {
            line: 231,
            column: 46
          },
          end: {
            line: 231,
            column: 74
          }
        }],
        line: 231
      },
      "62": {
        loc: {
          start: {
            line: 236,
            column: 47
          },
          end: {
            line: 236,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 236,
            column: 47
          },
          end: {
            line: 236,
            column: 69
          }
        }, {
          start: {
            line: 236,
            column: 73
          },
          end: {
            line: 236,
            column: 75
          }
        }],
        line: 236
      },
      "63": {
        loc: {
          start: {
            line: 239,
            column: 49
          },
          end: {
            line: 239,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 239,
            column: 49
          },
          end: {
            line: 239,
            column: 63
          }
        }, {
          start: {
            line: 239,
            column: 67
          },
          end: {
            line: 239,
            column: 70
          }
        }],
        line: 239
      },
      "64": {
        loc: {
          start: {
            line: 244,
            column: 20
          },
          end: {
            line: 257,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 244,
            column: 20
          },
          end: {
            line: 257,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 244
      },
      "65": {
        loc: {
          start: {
            line: 245,
            column: 24
          },
          end: {
            line: 247,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 24
          },
          end: {
            line: 247,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 245
      },
      "66": {
        loc: {
          start: {
            line: 249,
            column: 24
          },
          end: {
            line: 256,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 249,
            column: 24
          },
          end: {
            line: 256,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 249
      },
      "67": {
        loc: {
          start: {
            line: 249,
            column: 28
          },
          end: {
            line: 249,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 249,
            column: 28
          },
          end: {
            line: 249,
            column: 39
          }
        }, {
          start: {
            line: 249,
            column: 43
          },
          end: {
            line: 249,
            column: 65
          }
        }],
        line: 249
      },
      "68": {
        loc: {
          start: {
            line: 259,
            column: 20
          },
          end: {
            line: 261,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 259,
            column: 20
          },
          end: {
            line: 261,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 259
      },
      "69": {
        loc: {
          start: {
            line: 259,
            column: 24
          },
          end: {
            line: 259,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 259,
            column: 24
          },
          end: {
            line: 259,
            column: 43
          }
        }, {
          start: {
            line: 259,
            column: 47
          },
          end: {
            line: 259,
            column: 53
          }
        }, {
          start: {
            line: 259,
            column: 57
          },
          end: {
            line: 259,
            column: 74
          }
        }],
        line: 259
      },
      "70": {
        loc: {
          start: {
            line: 263,
            column: 20
          },
          end: {
            line: 278,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 20
          },
          end: {
            line: 278,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 263
      },
      "71": {
        loc: {
          start: {
            line: 263,
            column: 24
          },
          end: {
            line: 263,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 263,
            column: 24
          },
          end: {
            line: 263,
            column: 29
          }
        }, {
          start: {
            line: 263,
            column: 34
          },
          end: {
            line: 263,
            column: 55
          }
        }, {
          start: {
            line: 263,
            column: 59
          },
          end: {
            line: 263,
            column: 81
          }
        }],
        line: 263
      },
      "72": {
        loc: {
          start: {
            line: 265,
            column: 24
          },
          end: {
            line: 269,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 265,
            column: 24
          },
          end: {
            line: 269,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 265
      },
      "73": {
        loc: {
          start: {
            line: 265,
            column: 28
          },
          end: {
            line: 265,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 265,
            column: 28
          },
          end: {
            line: 265,
            column: 42
          }
        }, {
          start: {
            line: 265,
            column: 46
          },
          end: {
            line: 265,
            column: 74
          }
        }],
        line: 265
      },
      "74": {
        loc: {
          start: {
            line: 271,
            column: 37
          },
          end: {
            line: 273,
            column: 42
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 272,
            column: 30
          },
          end: {
            line: 272,
            column: 41
          }
        }, {
          start: {
            line: 273,
            column: 30
          },
          end: {
            line: 273,
            column: 42
          }
        }],
        line: 271
      },
      "75": {
        loc: {
          start: {
            line: 271,
            column: 37
          },
          end: {
            line: 271,
            column: 105
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 271,
            column: 37
          },
          end: {
            line: 271,
            column: 48
          }
        }, {
          start: {
            line: 271,
            column: 52
          },
          end: {
            line: 271,
            column: 76
          }
        }, {
          start: {
            line: 271,
            column: 80
          },
          end: {
            line: 271,
            column: 105
          }
        }],
        line: 271
      },
      "76": {
        loc: {
          start: {
            line: 275,
            column: 49
          },
          end: {
            line: 275,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 275,
            column: 49
          },
          end: {
            line: 275,
            column: 63
          }
        }, {
          start: {
            line: 275,
            column: 67
          },
          end: {
            line: 275,
            column: 70
          }
        }],
        line: 275
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0, 0, 0],
      "45": [0, 0],
      "46": [0, 0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0, 0],
      "70": [0, 0],
      "71": [0, 0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0, 0],
      "76": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/middleware.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyIA,gCA8KC;AAvTD,sCAAwD;AACxD,qCAAyC;AAEzC,sDAAsD;AACtD,IAAM,eAAe,GAAG;IACtB,YAAY;IACZ,UAAU;IACV,aAAa;IACb,QAAQ;IACR,eAAe;IACf,WAAW;IACX,kBAAkB;IAClB,qBAAqB;IACrB,iBAAiB;IACjB,QAAQ;CACT,CAAC;AAEF,wDAAwD;AACxD,IAAM,WAAW,GAAG;IAClB,QAAQ;CACT,CAAC;AAEF,gDAAgD;AAChD,IAAM,kBAAkB,GAAG;IACzB,iBAAiB;IACjB,cAAc;IACd,mBAAmB;IACnB,wBAAwB;IACxB,6BAA6B;IAC7B,uBAAuB;IACvB,sBAAsB;IACtB,uBAAuB;IACvB,yBAAyB;IACzB,qBAAqB;IACrB,YAAY;CACb,CAAC;AAEF,4DAA4D;AAC5D,IAAM,cAAc,GAAG;IACrB,YAAY;CACb,CAAC;AAEF,6DAA6D;AAC7D,IAAM,eAAe,GAAG;IACtB,WAAW;IACX,aAAa;IACb,mBAAmB;IACnB,yBAAyB;IACzB,cAAc;IACd,iBAAiB;CAClB,CAAC;AAEF,gDAAgD;AAChD,IAAM,cAAc,GAAG,IAAI,GAAG,EAAgD,CAAC;AAE/E,SAAS,WAAW,CAAC,OAAoB;IACvC,IAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IACzD,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IACxC,CAAC;IAED,IAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAChD,IAAI,MAAM,EAAE,CAAC;QACX,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,OAAQ,OAAe,CAAC,EAAE,IAAI,SAAS,CAAC;AAC1C,CAAC;AAED,SAAS,aAAa,CAAC,OAAoB,EAAE,QAAiC,EAAE,WAAyB;IAA5D,yBAAA,EAAA,WAAmB,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE,4BAAA,EAAA,iBAAyB;IACvG,IAAM,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IACtC,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACvB,IAAM,WAAW,GAAG,GAAG,GAAG,QAAQ,CAAC;IAEnC,uBAAuB;IACvB,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,UAAC,EAAY;YAAX,GAAG,QAAA,EAAE,KAAK,QAAA;QACvD,IAAI,KAAK,CAAC,SAAS,GAAG,WAAW,EAAE,CAAC;YAClC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,kCAAkC;IAClC,IAAM,KAAK,GAAG,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,GAAG,QAAQ,EAAE,CAAC;IAEtF,8BAA8B;IAC9B,IAAI,KAAK,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC;QAC1B,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;QAChB,KAAK,CAAC,SAAS,GAAG,GAAG,GAAG,QAAQ,CAAC;IACnC,CAAC;IAED,kBAAkB;IAClB,KAAK,CAAC,KAAK,EAAE,CAAC;IACd,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAEpC,OAAO,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC;AACnC,CAAC;AAED,SAAS,kBAAkB,CAAC,QAAsB;IAChD,qCAAqC;IACrC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;IAChD,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;IAC1D,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,iCAAiC,CAAC,CAAC;IAC3E,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;IAC1D,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,0CAA0C,CAAC,CAAC;IAEvF,oCAAoC;IACpC,IAAM,KAAK,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;IAElC,+EAA+E;IAC/E,IAAM,GAAG,GAAG;QACV,oBAAoB;QACpB,mCAA4B,KAAK,qGAAkG;QACnI,+DAA+D;QAC/D,mCAAmC;QACnC,iDAAiD;QACjD,+FAA+F;QAC/F,yBAAyB;QACzB,wBAAwB;QACxB,wBAAwB;QACxB,iBAAiB;QACjB,oBAAoB;KACrB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEb,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC;IACrD,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAEvC,8DAA8D;IAC9D,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,8CAA8C,CAAC,CAAC;IAElG,8BAA8B;IAC9B,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,cAAc,CAAC,CAAC;IACrE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,aAAa,CAAC,CAAC;IAClE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,aAAa,CAAC,CAAC;IAEpE,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAsB,UAAU,CAAC,OAAoB;;;;;;oBAC3C,QAAQ,GAAK,OAAO,CAAC,OAAO,SAApB,CAAqB;oBAG/B,uBAAuB,GAAG;wBAC9B,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;wBAC9D,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO;qBACrE,CAAC;oBAGI,eAAe,GAAG;wBACtB,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS;wBAC3D,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;wBAC1D,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM;qBACxD,CAAC;oBAGI,iBAAiB,GAAG,eAAe,CAAC,IAAI,CAAC,UAAA,OAAO;wBACpD,OAAA,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;oBAAtD,CAAsD,CACvD,CAAC;oBAEF,IAAI,iBAAiB,EAAE,CAAC;wBACtB,OAAO,CAAC,IAAI,CAAC,4CAAqC,QAAQ,CAAE,CAAC,CAAC;wBAC9D,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,KAAK,EAAE,eAAe,EAAE,EAC1B,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;oBACJ,CAAC;oBAGK,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;oBACpF,SAAS,GAAG,QAAQ,KAAK,cAAc,CAAC;oBACxC,eAAe,GAAG,uBAAuB,CAAC,IAAI,CAAC,UAAA,GAAG;wBACtD,OAAA,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;oBAAlD,CAAkD,CACnD,CAAC;oBAEF,IAAI,cAAc,IAAI,SAAS,IAAI,CAAC,eAAe,IAAI,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC;wBACxF,sBAAO,qBAAY,CAAC,IAAI,EAAE,EAAC;oBAC7B,CAAC;oBAGa,qBAAM,IAAA,cAAQ,EAAC,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC,EAAA;;oBAA7E,KAAK,GAAG,SAAqE;oBAG7E,gBAAgB,GAAG,eAAe,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,EAA1B,CAA0B,CAAC,CAAC;oBAC7E,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,EAA1B,CAA0B,CAAC,CAAC;oBACrE,mBAAmB,GAAG,kBAAkB,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,EAA1B,CAA0B,CAAC,CAAC;oBACnF,eAAe,GAAG,cAAc,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,EAA1B,CAA0B,CAAC,CAAC;oBAC3E,gBAAgB,GAAG,eAAe,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,EAA1B,CAA0B,CAAC,CAAC;oBAEnF,6DAA6D;oBAC7D,IAAI,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,YAAY,IAAI,eAAe,EAAE,CAAC;wBAE9D,WAAW,GAAG,CAAC,YAAY,IAAI,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;wBAC3D,QAAQ,GAAG,CAAC,YAAY,IAAI,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;wBAErF,IAAI,aAAa,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC,EAAE,CAAC;4BAClD,OAAO,CAAC,IAAI,CAAC,kCAA2B,QAAQ,CAAE,EAAE;gCAClD,EAAE,EAAE,WAAW,CAAC,OAAO,CAAC;gCACxB,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;gCAC5C,OAAO,EAAE,YAAY,IAAI,eAAe;6BACzC,CAAC,CAAC;4BAEH,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,KAAK,EAAE,4CAA4C,EAAE,EACvD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;wBACJ,CAAC;oBACH,CAAC;oBAED,qDAAqD;oBACrD,IAAI,YAAY,EAAE,CAAC;wBACjB,IAAI,CAAC,KAAK,EAAE,CAAC;4BACL,QAAQ,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;4BAC1C,QAAQ,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;4BAC3D,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;4BACnD,sBAAO,qBAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAC;wBACzC,CAAC;wBAED,+BAA+B;wBAC/B,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;4BAC1C,OAAO,CAAC,IAAI,CAAC,0DAAmD,QAAQ,CAAE,EAAE;gCAC1E,MAAM,EAAE,KAAK,CAAC,GAAG;gCACjB,KAAK,EAAE,KAAK,CAAC,KAAK;gCAClB,IAAI,EAAE,KAAK,CAAC,IAAI;6BACjB,CAAC,CAAC;4BACH,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAClC,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;wBACJ,CAAC;oBACH,CAAC;oBAED,0BAA0B;oBAC1B,IAAI,gBAAgB,IAAI,CAAC,KAAK,EAAE,CAAC;wBAEzB,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;wBAC1E,IAAI,cAAc,IAAI,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;4BACnD,OAAO,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAC;4BACxD,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,KAAK,EAAE,uCAAuC,EAAE,EAClD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;wBACJ,CAAC;wBAEK,QAAQ,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;wBAE1C,QAAQ,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;wBAC3D,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;wBAE7C,aAAW,qBAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;wBAE3C,aAAa,GAAG,QAAQ,CAAC,cAAc,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;wBAC1D,UAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC;wBAC9E,sBAAO,UAAQ,EAAC;oBAClB,CAAC;oBAED,yDAAyD;oBACzD,IAAI,eAAe,EAAE,CAAC;wBACpB,IAAI,CAAC,KAAK,EAAE,CAAC;4BACX,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,KAAK,EAAE,yBAAyB,EAAE,EACpC,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;wBACJ,CAAC;wBAED,+BAA+B;wBAC/B,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;4BAC1C,OAAO,CAAC,IAAI,CAAC,wDAAiD,QAAQ,CAAE,EAAE;gCACxE,MAAM,EAAE,KAAK,CAAC,GAAG;gCACjB,KAAK,EAAE,KAAK,CAAC,KAAK;gCAClB,IAAI,EAAE,KAAK,CAAC,IAAI;6BACjB,CAAC,CAAC;4BACH,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAClC,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;wBACJ,CAAC;oBACH,CAAC;oBAED,8BAA8B;oBAC9B,IAAI,mBAAmB,IAAI,CAAC,KAAK,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBACvD,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,KAAK,EAAE,yBAAyB,EAAE,EACpC,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;oBACJ,CAAC;oBAED,2EAA2E;oBAC3E,IAAI,KAAK,IAAI,CAAC,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,SAAS,CAAC,EAAE,CAAC;wBAEzD,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;wBAC1E,IAAI,cAAc,IAAI,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;4BACnD,OAAO,CAAC,KAAK,CAAC,2DAA2D,CAAC,CAAC;4BAErE,aAAW,qBAAY,CAAC,IAAI,EAAE,CAAC;4BACrC,sBAAO,kBAAkB,CAAC,UAAQ,CAAC,EAAC;wBACtC,CAAC;wBAGK,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;wBAC9D,UAAU,GAAG,WAAW,IAAI,WAAW,KAAK,QAAQ,IAAI,WAAW,KAAK,SAAS;4BACrF,CAAC,CAAC,WAAW;4BACb,CAAC,CAAC,YAAY,CAAC;wBAEX,aAAW,qBAAY,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;wBAEnE,aAAa,GAAG,QAAQ,CAAC,cAAc,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;wBAC1D,UAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC;wBAC9E,sBAAO,UAAQ,EAAC;oBAClB,CAAC;oBAEK,QAAQ,GAAG,qBAAY,CAAC,IAAI,EAAE,CAAC;oBACrC,sBAAO,kBAAkB,CAAC,QAAQ,CAAC,EAAC;;;;CACrC;AAEY,QAAA,MAAM,GAAG;IACpB,OAAO,EAAE;QACP;;;;;;WAMG;QACH,uDAAuD;KACxD;CACF,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/middleware.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getToken } from 'next-auth/jwt';\n\n// Define protected routes that require authentication\nconst protectedRoutes = [\n  '/dashboard',\n  '/profile',\n  '/assessment',\n  '/forum',\n  '/freedom-fund',\n  '/progress',\n  '/recommendations',\n  '/interview-practice',\n  '/resume-builder',\n  '/tools',\n];\n\n// Define admin routes that require admin authentication\nconst adminRoutes = [\n  '/admin',\n];\n\n// Define API routes that require authentication\nconst protectedApiRoutes = [\n  '/api/assessment',\n  '/api/profile',\n  '/api/freedom-fund',\n  '/api/learning-progress',\n  '/api/personalized-resources',\n  '/api/progress-tracker',\n  '/api/recommendations',\n  '/api/resource-ratings',\n  '/api/interview-practice',\n  '/api/resume-builder',\n  '/api/tools',\n];\n\n// Define admin API routes that require admin authentication\nconst adminApiRoutes = [\n  '/api/admin',\n];\n\n// Define public API routes that don't require authentication\nconst publicApiRoutes = [\n  '/api/auth',\n  '/api/signup',\n  '/api/career-paths',\n  '/api/learning-resources',\n  '/api/contact',\n  '/api/csrf-token',\n];\n\n// Rate limiting store (use Redis in production)\nconst rateLimitStore = new Map<string, { count: number; resetTime: number }>();\n\nfunction getClientIP(request: NextRequest): string {\n  const forwarded = request.headers.get('x-forwarded-for');\n  if (forwarded) {\n    return forwarded.split(',')[0].trim();\n  }\n  \n  const realIP = request.headers.get('x-real-ip');\n  if (realIP) {\n    return realIP;\n  }\n  \n  return (request as any).ip || 'unknown';\n}\n\nfunction isRateLimited(request: NextRequest, windowMs: number = 15 * 60 * 1000, maxRequests: number = 100): boolean {\n  const clientIP = getClientIP(request);\n  const now = Date.now();\n  const windowStart = now - windowMs;\n  \n  // Clean up old entries\n  Array.from(rateLimitStore.entries()).forEach(([key, value]) => {\n    if (value.resetTime < windowStart) {\n      rateLimitStore.delete(key);\n    }\n  });\n  \n  // Get or create entry for this IP\n  const entry = rateLimitStore.get(clientIP) || { count: 0, resetTime: now + windowMs };\n  \n  // Reset if window has expired\n  if (entry.resetTime < now) {\n    entry.count = 0;\n    entry.resetTime = now + windowMs;\n  }\n  \n  // Increment count\n  entry.count++;\n  rateLimitStore.set(clientIP, entry);\n  \n  return entry.count > maxRequests;\n}\n\nfunction addSecurityHeaders(response: NextResponse): NextResponse {\n  // Add comprehensive security headers\n  response.headers.set('X-Frame-Options', 'DENY');\n  response.headers.set('X-Content-Type-Options', 'nosniff');\n  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');\n  response.headers.set('X-XSS-Protection', '1; mode=block');\n  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');\n\n  // Generate nonce for inline scripts\n  const nonce = crypto.randomUUID();\n\n  // Enhanced Content Security Policy - Removed unsafe-inline for better security\n  const csp = [\n    \"default-src 'self'\",\n    `script-src 'self' 'nonce-${nonce}' 'unsafe-eval' https://vercel.live https://va.vercel-scripts.com https://browser.sentry-cdn.com`,\n    \"style-src 'self' 'unsafe-inline' https://fonts.googleapis.com\",\n    \"img-src 'self' data: https: blob:\",\n    \"font-src 'self' data: https://fonts.gstatic.com\",\n    \"connect-src 'self' https://vercel.live https://vitals.vercel-insights.com https://*.sentry.io\",\n    \"worker-src 'self' blob:\",\n    \"child-src 'self' blob:\",\n    \"frame-ancestors 'none'\",\n    \"base-uri 'self'\",\n    \"form-action 'self'\"\n  ].join('; ');\n\n  response.headers.set('Content-Security-Policy', csp);\n  response.headers.set('X-Nonce', nonce);\n\n  // Add HSTS header for HTTPS (always set for security testing)\n  response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');\n\n  // Additional security headers\n  response.headers.set('Cross-Origin-Embedder-Policy', 'require-corp');\n  response.headers.set('Cross-Origin-Opener-Policy', 'same-origin');\n  response.headers.set('Cross-Origin-Resource-Policy', 'same-origin');\n\n  return response;\n}\n\nexport async function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n\n  // Define allowed static file extensions (security-first approach)\n  const allowedStaticExtensions = [\n    '.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico',\n    '.woff', '.woff2', '.ttf', '.eot', '.webp', '.avif', '.mp4', '.webm'\n  ];\n\n  // Define explicitly blocked file patterns (security critical)\n  const blockedPatterns = [\n    '.env', '.git', '.db', '.sqlite', '.log', '.bak', '.backup',\n    '.config', '.ini', '.conf', '.key', '.pem', '.crt', '.p12',\n    '.json', '.yaml', '.yml', '.xml', '.md', '.txt', '.csv'\n  ];\n\n  // Check for blocked file patterns first (highest security priority)\n  const hasBlockedPattern = blockedPatterns.some(pattern =>\n    pathname.toLowerCase().includes(pattern.toLowerCase())\n  );\n\n  if (hasBlockedPattern) {\n    console.warn(`Blocked access to sensitive file: ${pathname}`);\n    return NextResponse.json(\n      { error: 'Access denied' },\n      { status: 403 }\n    );\n  }\n\n  // Skip middleware only for explicitly allowed static files and Next.js internals\n  const isNextInternal = pathname.startsWith('/_next') || pathname.startsWith('/api/_next');\n  const isFavicon = pathname === '/favicon.ico';\n  const isAllowedStatic = allowedStaticExtensions.some(ext =>\n    pathname.toLowerCase().endsWith(ext.toLowerCase())\n  );\n\n  if (isNextInternal || isFavicon || (isAllowedStatic && pathname.startsWith('/public/'))) {\n    return NextResponse.next();\n  }\n\n  // Get the token to check authentication status\n  const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });\n\n  // Check if the route requires authentication\n  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route));\n  const isAdminRoute = adminRoutes.some(route => pathname.startsWith(route));\n  const isProtectedApiRoute = protectedApiRoutes.some(route => pathname.startsWith(route));\n  const isAdminApiRoute = adminApiRoutes.some(route => pathname.startsWith(route));\n  const isPublicApiRoute = publicApiRoutes.some(route => pathname.startsWith(route));\n\n  // Apply rate limiting for API routes and sensitive endpoints\n  if (pathname.startsWith('/api/') || isAdminRoute || isAdminApiRoute) {\n    // Use stricter rate limiting for admin routes\n    const maxRequests = (isAdminRoute || isAdminApiRoute) ? 50 : 100;\n    const windowMs = (isAdminRoute || isAdminApiRoute) ? 15 * 60 * 1000 : 15 * 60 * 1000;\n\n    if (isRateLimited(request, windowMs, maxRequests)) {\n      console.warn(`Rate limit exceeded for ${pathname}`, {\n        ip: getClientIP(request),\n        userAgent: request.headers.get('user-agent'),\n        isAdmin: isAdminRoute || isAdminApiRoute\n      });\n\n      return NextResponse.json(\n        { error: 'Too many requests. Please try again later.' },\n        { status: 429 }\n      );\n    }\n  }\n\n  // Handle admin routes (require admin authentication)\n  if (isAdminRoute) {\n    if (!token) {\n      const loginUrl = new URL('/login', request.url);\n      const fullPath = pathname + (request.nextUrl.search || '');\n      loginUrl.searchParams.set('callbackUrl', fullPath);\n      return NextResponse.redirect(loginUrl);\n    }\n\n    // Check if user has admin role\n    if (!token.role || token.role !== 'admin') {\n      console.warn(`Non-admin user attempted to access admin route: ${pathname}`, {\n        userId: token.sub,\n        email: token.email,\n        role: token.role\n      });\n      return NextResponse.json(\n        { error: 'Admin access required' },\n        { status: 403 }\n      );\n    }\n  }\n\n  // Handle protected routes\n  if (isProtectedRoute && !token) {\n    // Prevent redirect loops by checking if we're already being redirected\n    const isRedirectLoop = request.headers.get('x-middleware-redirect-count');\n    if (isRedirectLoop && parseInt(isRedirectLoop) > 3) {\n      console.error('Redirect loop detected, breaking chain');\n      return NextResponse.json(\n        { error: 'Authentication redirect loop detected' },\n        { status: 500 }\n      );\n    }\n\n    const loginUrl = new URL('/login', request.url);\n    // Include query parameters in the redirect URL\n    const fullPath = pathname + (request.nextUrl.search || '');\n    loginUrl.searchParams.set('callbackUrl', fullPath);\n\n    const response = NextResponse.redirect(loginUrl);\n    // Track redirect count to prevent loops\n    const redirectCount = parseInt(isRedirectLoop || '0') + 1;\n    response.headers.set('x-middleware-redirect-count', redirectCount.toString());\n    return response;\n  }\n\n  // Handle admin API routes (require admin authentication)\n  if (isAdminApiRoute) {\n    if (!token) {\n      return NextResponse.json(\n        { error: 'Authentication required' },\n        { status: 401 }\n      );\n    }\n\n    // Check if user has admin role\n    if (!token.role || token.role !== 'admin') {\n      console.warn(`Non-admin user attempted to access admin API: ${pathname}`, {\n        userId: token.sub,\n        email: token.email,\n        role: token.role\n      });\n      return NextResponse.json(\n        { error: 'Admin access required' },\n        { status: 403 }\n      );\n    }\n  }\n\n  // Handle protected API routes\n  if (isProtectedApiRoute && !token && !isPublicApiRoute) {\n    return NextResponse.json(\n      { error: 'Authentication required' },\n      { status: 401 }\n    );\n  }\n\n  // Redirect authenticated users away from auth pages (with loop prevention)\n  if (token && (pathname === '/login' || pathname === '/signup')) {\n    // Check for redirect loops\n    const isRedirectLoop = request.headers.get('x-middleware-redirect-count');\n    if (isRedirectLoop && parseInt(isRedirectLoop) > 3) {\n      console.error('Auth redirect loop detected, allowing access to auth page');\n      // Allow access to prevent infinite loops\n      const response = NextResponse.next();\n      return addSecurityHeaders(response);\n    }\n\n    // Check if user is trying to access login with a callback URL\n    const callbackUrl = request.nextUrl.searchParams.get('callbackUrl');\n    const redirectTo = callbackUrl && callbackUrl !== '/login' && callbackUrl !== '/signup'\n      ? callbackUrl\n      : '/dashboard';\n\n    const response = NextResponse.redirect(new URL(redirectTo, request.url));\n    // Track redirect count\n    const redirectCount = parseInt(isRedirectLoop || '0') + 1;\n    response.headers.set('x-middleware-redirect-count', redirectCount.toString());\n    return response;\n  }\n\n  const response = NextResponse.next();\n  return addSecurityHeaders(response);\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder files\n     */\n    '/((?!_next/static|_next/image|favicon.ico|public/).*)',\n  ],\n};\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d092a395a65fa204a191b9f4137770421b0e0247"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1ad6gddzlg = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1ad6gddzlg();
var __awaiter =
/* istanbul ignore next */
(cov_1ad6gddzlg().s[0]++,
/* istanbul ignore next */
(cov_1ad6gddzlg().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1ad6gddzlg().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1ad6gddzlg().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1ad6gddzlg().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1ad6gddzlg().f[1]++;
    cov_1ad6gddzlg().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1ad6gddzlg().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_1ad6gddzlg().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1ad6gddzlg().f[2]++;
      cov_1ad6gddzlg().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_1ad6gddzlg().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_1ad6gddzlg().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1ad6gddzlg().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1ad6gddzlg().f[4]++;
      cov_1ad6gddzlg().s[4]++;
      try {
        /* istanbul ignore next */
        cov_1ad6gddzlg().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1ad6gddzlg().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1ad6gddzlg().f[5]++;
      cov_1ad6gddzlg().s[7]++;
      try {
        /* istanbul ignore next */
        cov_1ad6gddzlg().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1ad6gddzlg().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1ad6gddzlg().f[6]++;
      cov_1ad6gddzlg().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1ad6gddzlg().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1ad6gddzlg().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1ad6gddzlg().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1ad6gddzlg().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1ad6gddzlg().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1ad6gddzlg().s[12]++,
/* istanbul ignore next */
(cov_1ad6gddzlg().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_1ad6gddzlg().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1ad6gddzlg().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1ad6gddzlg().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_1ad6gddzlg().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1ad6gddzlg().f[8]++;
        cov_1ad6gddzlg().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1ad6gddzlg().b[6][0]++;
          cov_1ad6gddzlg().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1ad6gddzlg().b[6][1]++;
        }
        cov_1ad6gddzlg().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1ad6gddzlg().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1ad6gddzlg().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1ad6gddzlg().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1ad6gddzlg().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1ad6gddzlg().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1ad6gddzlg().f[9]++;
    cov_1ad6gddzlg().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1ad6gddzlg().f[10]++;
    cov_1ad6gddzlg().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1ad6gddzlg().f[11]++;
      cov_1ad6gddzlg().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1ad6gddzlg().f[12]++;
    cov_1ad6gddzlg().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_1ad6gddzlg().b[9][0]++;
      cov_1ad6gddzlg().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1ad6gddzlg().b[9][1]++;
    }
    cov_1ad6gddzlg().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_1ad6gddzlg().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_1ad6gddzlg().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1ad6gddzlg().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1ad6gddzlg().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1ad6gddzlg().s[25]++;
      try {
        /* istanbul ignore next */
        cov_1ad6gddzlg().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1ad6gddzlg().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_1ad6gddzlg().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1ad6gddzlg().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1ad6gddzlg().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1ad6gddzlg().b[15][0]++,
        /* istanbul ignore next */
        (cov_1ad6gddzlg().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1ad6gddzlg().b[16][1]++,
        /* istanbul ignore next */
        (cov_1ad6gddzlg().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1ad6gddzlg().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1ad6gddzlg().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1ad6gddzlg().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1ad6gddzlg().b[12][0]++;
          cov_1ad6gddzlg().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1ad6gddzlg().b[12][1]++;
        }
        cov_1ad6gddzlg().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1ad6gddzlg().b[18][0]++;
          cov_1ad6gddzlg().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1ad6gddzlg().b[18][1]++;
        }
        cov_1ad6gddzlg().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1ad6gddzlg().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1ad6gddzlg().b[19][1]++;
            cov_1ad6gddzlg().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1ad6gddzlg().b[19][2]++;
            cov_1ad6gddzlg().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1ad6gddzlg().b[19][3]++;
            cov_1ad6gddzlg().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1ad6gddzlg().b[19][4]++;
            cov_1ad6gddzlg().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1ad6gddzlg().b[19][5]++;
            cov_1ad6gddzlg().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1ad6gddzlg().b[20][0]++;
              cov_1ad6gddzlg().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1ad6gddzlg().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1ad6gddzlg().b[20][1]++;
            }
            cov_1ad6gddzlg().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1ad6gddzlg().b[23][0]++;
              cov_1ad6gddzlg().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1ad6gddzlg().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1ad6gddzlg().b[23][1]++;
            }
            cov_1ad6gddzlg().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1ad6gddzlg().b[25][0]++;
              cov_1ad6gddzlg().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1ad6gddzlg().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_1ad6gddzlg().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1ad6gddzlg().b[25][1]++;
            }
            cov_1ad6gddzlg().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1ad6gddzlg().b[27][0]++;
              cov_1ad6gddzlg().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1ad6gddzlg().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1ad6gddzlg().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1ad6gddzlg().b[27][1]++;
            }
            cov_1ad6gddzlg().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1ad6gddzlg().b[29][0]++;
              cov_1ad6gddzlg().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1ad6gddzlg().b[29][1]++;
            }
            cov_1ad6gddzlg().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1ad6gddzlg().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1ad6gddzlg().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1ad6gddzlg().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1ad6gddzlg().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1ad6gddzlg().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1ad6gddzlg().b[30][0]++;
      cov_1ad6gddzlg().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1ad6gddzlg().b[30][1]++;
    }
    cov_1ad6gddzlg().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1ad6gddzlg().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1ad6gddzlg().b[31][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_1ad6gddzlg().s[67]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1ad6gddzlg().s[68]++;
exports.config = void 0;
/* istanbul ignore next */
cov_1ad6gddzlg().s[69]++;
exports.middleware = middleware;
var server_1 =
/* istanbul ignore next */
(cov_1ad6gddzlg().s[70]++, require("next/server"));
var jwt_1 =
/* istanbul ignore next */
(cov_1ad6gddzlg().s[71]++, require("next-auth/jwt"));
// Define protected routes that require authentication
var protectedRoutes =
/* istanbul ignore next */
(cov_1ad6gddzlg().s[72]++, ['/dashboard', '/profile', '/assessment', '/forum', '/freedom-fund', '/progress', '/recommendations', '/interview-practice', '/resume-builder', '/tools']);
// Define admin routes that require admin authentication
var adminRoutes =
/* istanbul ignore next */
(cov_1ad6gddzlg().s[73]++, ['/admin']);
// Define API routes that require authentication
var protectedApiRoutes =
/* istanbul ignore next */
(cov_1ad6gddzlg().s[74]++, ['/api/assessment', '/api/profile', '/api/freedom-fund', '/api/learning-progress', '/api/personalized-resources', '/api/progress-tracker', '/api/recommendations', '/api/resource-ratings', '/api/interview-practice', '/api/resume-builder', '/api/tools']);
// Define admin API routes that require admin authentication
var adminApiRoutes =
/* istanbul ignore next */
(cov_1ad6gddzlg().s[75]++, ['/api/admin']);
// Define public API routes that don't require authentication
var publicApiRoutes =
/* istanbul ignore next */
(cov_1ad6gddzlg().s[76]++, ['/api/auth', '/api/signup', '/api/career-paths', '/api/learning-resources', '/api/contact', '/api/csrf-token']);
// Rate limiting store (use Redis in production)
var rateLimitStore =
/* istanbul ignore next */
(cov_1ad6gddzlg().s[77]++, new Map());
function getClientIP(request) {
  /* istanbul ignore next */
  cov_1ad6gddzlg().f[13]++;
  var forwarded =
  /* istanbul ignore next */
  (cov_1ad6gddzlg().s[78]++, request.headers.get('x-forwarded-for'));
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[79]++;
  if (forwarded) {
    /* istanbul ignore next */
    cov_1ad6gddzlg().b[32][0]++;
    cov_1ad6gddzlg().s[80]++;
    return forwarded.split(',')[0].trim();
  } else
  /* istanbul ignore next */
  {
    cov_1ad6gddzlg().b[32][1]++;
  }
  var realIP =
  /* istanbul ignore next */
  (cov_1ad6gddzlg().s[81]++, request.headers.get('x-real-ip'));
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[82]++;
  if (realIP) {
    /* istanbul ignore next */
    cov_1ad6gddzlg().b[33][0]++;
    cov_1ad6gddzlg().s[83]++;
    return realIP;
  } else
  /* istanbul ignore next */
  {
    cov_1ad6gddzlg().b[33][1]++;
  }
  cov_1ad6gddzlg().s[84]++;
  return /* istanbul ignore next */(cov_1ad6gddzlg().b[34][0]++, request.ip) ||
  /* istanbul ignore next */
  (cov_1ad6gddzlg().b[34][1]++, 'unknown');
}
function isRateLimited(request, windowMs, maxRequests) {
  /* istanbul ignore next */
  cov_1ad6gddzlg().f[14]++;
  cov_1ad6gddzlg().s[85]++;
  if (windowMs === void 0) {
    /* istanbul ignore next */
    cov_1ad6gddzlg().b[35][0]++;
    cov_1ad6gddzlg().s[86]++;
    windowMs = 15 * 60 * 1000;
  } else
  /* istanbul ignore next */
  {
    cov_1ad6gddzlg().b[35][1]++;
  }
  cov_1ad6gddzlg().s[87]++;
  if (maxRequests === void 0) {
    /* istanbul ignore next */
    cov_1ad6gddzlg().b[36][0]++;
    cov_1ad6gddzlg().s[88]++;
    maxRequests = 100;
  } else
  /* istanbul ignore next */
  {
    cov_1ad6gddzlg().b[36][1]++;
  }
  var clientIP =
  /* istanbul ignore next */
  (cov_1ad6gddzlg().s[89]++, getClientIP(request));
  var now =
  /* istanbul ignore next */
  (cov_1ad6gddzlg().s[90]++, Date.now());
  var windowStart =
  /* istanbul ignore next */
  (cov_1ad6gddzlg().s[91]++, now - windowMs);
  // Clean up old entries
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[92]++;
  Array.from(rateLimitStore.entries()).forEach(function (_a) {
    /* istanbul ignore next */
    cov_1ad6gddzlg().f[15]++;
    var key =
      /* istanbul ignore next */
      (cov_1ad6gddzlg().s[93]++, _a[0]),
      value =
      /* istanbul ignore next */
      (cov_1ad6gddzlg().s[94]++, _a[1]);
    /* istanbul ignore next */
    cov_1ad6gddzlg().s[95]++;
    if (value.resetTime < windowStart) {
      /* istanbul ignore next */
      cov_1ad6gddzlg().b[37][0]++;
      cov_1ad6gddzlg().s[96]++;
      rateLimitStore.delete(key);
    } else
    /* istanbul ignore next */
    {
      cov_1ad6gddzlg().b[37][1]++;
    }
  });
  // Get or create entry for this IP
  var entry =
  /* istanbul ignore next */
  (cov_1ad6gddzlg().s[97]++,
  /* istanbul ignore next */
  (cov_1ad6gddzlg().b[38][0]++, rateLimitStore.get(clientIP)) ||
  /* istanbul ignore next */
  (cov_1ad6gddzlg().b[38][1]++, {
    count: 0,
    resetTime: now + windowMs
  }));
  // Reset if window has expired
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[98]++;
  if (entry.resetTime < now) {
    /* istanbul ignore next */
    cov_1ad6gddzlg().b[39][0]++;
    cov_1ad6gddzlg().s[99]++;
    entry.count = 0;
    /* istanbul ignore next */
    cov_1ad6gddzlg().s[100]++;
    entry.resetTime = now + windowMs;
  } else
  /* istanbul ignore next */
  {
    cov_1ad6gddzlg().b[39][1]++;
  }
  // Increment count
  cov_1ad6gddzlg().s[101]++;
  entry.count++;
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[102]++;
  rateLimitStore.set(clientIP, entry);
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[103]++;
  return entry.count > maxRequests;
}
function addSecurityHeaders(response) {
  /* istanbul ignore next */
  cov_1ad6gddzlg().f[16]++;
  cov_1ad6gddzlg().s[104]++;
  // Add comprehensive security headers
  response.headers.set('X-Frame-Options', 'DENY');
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[105]++;
  response.headers.set('X-Content-Type-Options', 'nosniff');
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[106]++;
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[107]++;
  response.headers.set('X-XSS-Protection', '1; mode=block');
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[108]++;
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  // Generate nonce for inline scripts
  var nonce =
  /* istanbul ignore next */
  (cov_1ad6gddzlg().s[109]++, crypto.randomUUID());
  // Enhanced Content Security Policy - Removed unsafe-inline for better security
  var csp =
  /* istanbul ignore next */
  (cov_1ad6gddzlg().s[110]++, ["default-src 'self'", "script-src 'self' 'nonce-".concat(nonce, "' 'unsafe-eval' https://vercel.live https://va.vercel-scripts.com https://browser.sentry-cdn.com"), "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com", "img-src 'self' data: https: blob:", "font-src 'self' data: https://fonts.gstatic.com", "connect-src 'self' https://vercel.live https://vitals.vercel-insights.com https://*.sentry.io", "worker-src 'self' blob:", "child-src 'self' blob:", "frame-ancestors 'none'", "base-uri 'self'", "form-action 'self'"].join('; '));
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[111]++;
  response.headers.set('Content-Security-Policy', csp);
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[112]++;
  response.headers.set('X-Nonce', nonce);
  // Add HSTS header for HTTPS (always set for security testing)
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[113]++;
  response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  // Additional security headers
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[114]++;
  response.headers.set('Cross-Origin-Embedder-Policy', 'require-corp');
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[115]++;
  response.headers.set('Cross-Origin-Opener-Policy', 'same-origin');
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[116]++;
  response.headers.set('Cross-Origin-Resource-Policy', 'same-origin');
  /* istanbul ignore next */
  cov_1ad6gddzlg().s[117]++;
  return response;
}
function middleware(request) {
  /* istanbul ignore next */
  cov_1ad6gddzlg().f[17]++;
  cov_1ad6gddzlg().s[118]++;
  return __awaiter(this, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_1ad6gddzlg().f[18]++;
    var pathname, allowedStaticExtensions, blockedPatterns, hasBlockedPattern, isNextInternal, isFavicon, isAllowedStatic, token, isProtectedRoute, isAdminRoute, isProtectedApiRoute, isAdminApiRoute, isPublicApiRoute, maxRequests, windowMs, loginUrl, fullPath, isRedirectLoop, loginUrl, fullPath, response_1, redirectCount, isRedirectLoop, response_2, callbackUrl, redirectTo, response_3, redirectCount, response;
    /* istanbul ignore next */
    cov_1ad6gddzlg().s[119]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_1ad6gddzlg().f[19]++;
      cov_1ad6gddzlg().s[120]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_1ad6gddzlg().b[40][0]++;
          cov_1ad6gddzlg().s[121]++;
          pathname = request.nextUrl.pathname;
          /* istanbul ignore next */
          cov_1ad6gddzlg().s[122]++;
          allowedStaticExtensions = ['.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot', '.webp', '.avif', '.mp4', '.webm'];
          /* istanbul ignore next */
          cov_1ad6gddzlg().s[123]++;
          blockedPatterns = ['.env', '.git', '.db', '.sqlite', '.log', '.bak', '.backup', '.config', '.ini', '.conf', '.key', '.pem', '.crt', '.p12', '.json', '.yaml', '.yml', '.xml', '.md', '.txt', '.csv'];
          /* istanbul ignore next */
          cov_1ad6gddzlg().s[124]++;
          hasBlockedPattern = blockedPatterns.some(function (pattern) {
            /* istanbul ignore next */
            cov_1ad6gddzlg().f[20]++;
            cov_1ad6gddzlg().s[125]++;
            return pathname.toLowerCase().includes(pattern.toLowerCase());
          });
          /* istanbul ignore next */
          cov_1ad6gddzlg().s[126]++;
          if (hasBlockedPattern) {
            /* istanbul ignore next */
            cov_1ad6gddzlg().b[41][0]++;
            cov_1ad6gddzlg().s[127]++;
            console.warn("Blocked access to sensitive file: ".concat(pathname));
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[128]++;
            return [2 /*return*/, server_1.NextResponse.json({
              error: 'Access denied'
            }, {
              status: 403
            })];
          } else
          /* istanbul ignore next */
          {
            cov_1ad6gddzlg().b[41][1]++;
          }
          cov_1ad6gddzlg().s[129]++;
          isNextInternal =
          /* istanbul ignore next */
          (cov_1ad6gddzlg().b[42][0]++, pathname.startsWith('/_next')) ||
          /* istanbul ignore next */
          (cov_1ad6gddzlg().b[42][1]++, pathname.startsWith('/api/_next'));
          /* istanbul ignore next */
          cov_1ad6gddzlg().s[130]++;
          isFavicon = pathname === '/favicon.ico';
          /* istanbul ignore next */
          cov_1ad6gddzlg().s[131]++;
          isAllowedStatic = allowedStaticExtensions.some(function (ext) {
            /* istanbul ignore next */
            cov_1ad6gddzlg().f[21]++;
            cov_1ad6gddzlg().s[132]++;
            return pathname.toLowerCase().endsWith(ext.toLowerCase());
          });
          /* istanbul ignore next */
          cov_1ad6gddzlg().s[133]++;
          if (
          /* istanbul ignore next */
          (cov_1ad6gddzlg().b[44][0]++, isNextInternal) ||
          /* istanbul ignore next */
          (cov_1ad6gddzlg().b[44][1]++, isFavicon) ||
          /* istanbul ignore next */
          (cov_1ad6gddzlg().b[44][2]++, isAllowedStatic) &&
          /* istanbul ignore next */
          (cov_1ad6gddzlg().b[44][3]++, pathname.startsWith('/public/'))) {
            /* istanbul ignore next */
            cov_1ad6gddzlg().b[43][0]++;
            cov_1ad6gddzlg().s[134]++;
            return [2 /*return*/, server_1.NextResponse.next()];
          } else
          /* istanbul ignore next */
          {
            cov_1ad6gddzlg().b[43][1]++;
          }
          cov_1ad6gddzlg().s[135]++;
          return [4 /*yield*/, (0, jwt_1.getToken)({
            req: request,
            secret: process.env.NEXTAUTH_SECRET
          })];
        case 1:
          /* istanbul ignore next */
          cov_1ad6gddzlg().b[40][1]++;
          cov_1ad6gddzlg().s[136]++;
          token = _a.sent();
          /* istanbul ignore next */
          cov_1ad6gddzlg().s[137]++;
          isProtectedRoute = protectedRoutes.some(function (route) {
            /* istanbul ignore next */
            cov_1ad6gddzlg().f[22]++;
            cov_1ad6gddzlg().s[138]++;
            return pathname.startsWith(route);
          });
          /* istanbul ignore next */
          cov_1ad6gddzlg().s[139]++;
          isAdminRoute = adminRoutes.some(function (route) {
            /* istanbul ignore next */
            cov_1ad6gddzlg().f[23]++;
            cov_1ad6gddzlg().s[140]++;
            return pathname.startsWith(route);
          });
          /* istanbul ignore next */
          cov_1ad6gddzlg().s[141]++;
          isProtectedApiRoute = protectedApiRoutes.some(function (route) {
            /* istanbul ignore next */
            cov_1ad6gddzlg().f[24]++;
            cov_1ad6gddzlg().s[142]++;
            return pathname.startsWith(route);
          });
          /* istanbul ignore next */
          cov_1ad6gddzlg().s[143]++;
          isAdminApiRoute = adminApiRoutes.some(function (route) {
            /* istanbul ignore next */
            cov_1ad6gddzlg().f[25]++;
            cov_1ad6gddzlg().s[144]++;
            return pathname.startsWith(route);
          });
          /* istanbul ignore next */
          cov_1ad6gddzlg().s[145]++;
          isPublicApiRoute = publicApiRoutes.some(function (route) {
            /* istanbul ignore next */
            cov_1ad6gddzlg().f[26]++;
            cov_1ad6gddzlg().s[146]++;
            return pathname.startsWith(route);
          });
          // Apply rate limiting for API routes and sensitive endpoints
          /* istanbul ignore next */
          cov_1ad6gddzlg().s[147]++;
          if (
          /* istanbul ignore next */
          (cov_1ad6gddzlg().b[46][0]++, pathname.startsWith('/api/')) ||
          /* istanbul ignore next */
          (cov_1ad6gddzlg().b[46][1]++, isAdminRoute) ||
          /* istanbul ignore next */
          (cov_1ad6gddzlg().b[46][2]++, isAdminApiRoute)) {
            /* istanbul ignore next */
            cov_1ad6gddzlg().b[45][0]++;
            cov_1ad6gddzlg().s[148]++;
            maxRequests =
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[48][0]++, isAdminRoute) ||
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[48][1]++, isAdminApiRoute) ?
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[47][0]++, 50) :
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[47][1]++, 100);
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[149]++;
            windowMs =
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[50][0]++, isAdminRoute) ||
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[50][1]++, isAdminApiRoute) ?
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[49][0]++, 15 * 60 * 1000) :
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[49][1]++, 15 * 60 * 1000);
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[150]++;
            if (isRateLimited(request, windowMs, maxRequests)) {
              /* istanbul ignore next */
              cov_1ad6gddzlg().b[51][0]++;
              cov_1ad6gddzlg().s[151]++;
              console.warn("Rate limit exceeded for ".concat(pathname), {
                ip: getClientIP(request),
                userAgent: request.headers.get('user-agent'),
                isAdmin:
                /* istanbul ignore next */
                (cov_1ad6gddzlg().b[52][0]++, isAdminRoute) ||
                /* istanbul ignore next */
                (cov_1ad6gddzlg().b[52][1]++, isAdminApiRoute)
              });
              /* istanbul ignore next */
              cov_1ad6gddzlg().s[152]++;
              return [2 /*return*/, server_1.NextResponse.json({
                error: 'Too many requests. Please try again later.'
              }, {
                status: 429
              })];
            } else
            /* istanbul ignore next */
            {
              cov_1ad6gddzlg().b[51][1]++;
            }
          } else
          /* istanbul ignore next */
          {
            cov_1ad6gddzlg().b[45][1]++;
          }
          // Handle admin routes (require admin authentication)
          cov_1ad6gddzlg().s[153]++;
          if (isAdminRoute) {
            /* istanbul ignore next */
            cov_1ad6gddzlg().b[53][0]++;
            cov_1ad6gddzlg().s[154]++;
            if (!token) {
              /* istanbul ignore next */
              cov_1ad6gddzlg().b[54][0]++;
              cov_1ad6gddzlg().s[155]++;
              loginUrl = new URL('/login', request.url);
              /* istanbul ignore next */
              cov_1ad6gddzlg().s[156]++;
              fullPath = pathname + (
              /* istanbul ignore next */
              (cov_1ad6gddzlg().b[55][0]++, request.nextUrl.search) ||
              /* istanbul ignore next */
              (cov_1ad6gddzlg().b[55][1]++, ''));
              /* istanbul ignore next */
              cov_1ad6gddzlg().s[157]++;
              loginUrl.searchParams.set('callbackUrl', fullPath);
              /* istanbul ignore next */
              cov_1ad6gddzlg().s[158]++;
              return [2 /*return*/, server_1.NextResponse.redirect(loginUrl)];
            } else
            /* istanbul ignore next */
            {
              cov_1ad6gddzlg().b[54][1]++;
            }
            // Check if user has admin role
            cov_1ad6gddzlg().s[159]++;
            if (
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[57][0]++, !token.role) ||
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[57][1]++, token.role !== 'admin')) {
              /* istanbul ignore next */
              cov_1ad6gddzlg().b[56][0]++;
              cov_1ad6gddzlg().s[160]++;
              console.warn("Non-admin user attempted to access admin route: ".concat(pathname), {
                userId: token.sub,
                email: token.email,
                role: token.role
              });
              /* istanbul ignore next */
              cov_1ad6gddzlg().s[161]++;
              return [2 /*return*/, server_1.NextResponse.json({
                error: 'Admin access required'
              }, {
                status: 403
              })];
            } else
            /* istanbul ignore next */
            {
              cov_1ad6gddzlg().b[56][1]++;
            }
          } else
          /* istanbul ignore next */
          {
            cov_1ad6gddzlg().b[53][1]++;
          }
          // Handle protected routes
          cov_1ad6gddzlg().s[162]++;
          if (
          /* istanbul ignore next */
          (cov_1ad6gddzlg().b[59][0]++, isProtectedRoute) &&
          /* istanbul ignore next */
          (cov_1ad6gddzlg().b[59][1]++, !token)) {
            /* istanbul ignore next */
            cov_1ad6gddzlg().b[58][0]++;
            cov_1ad6gddzlg().s[163]++;
            isRedirectLoop = request.headers.get('x-middleware-redirect-count');
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[164]++;
            if (
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[61][0]++, isRedirectLoop) &&
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[61][1]++, parseInt(isRedirectLoop) > 3)) {
              /* istanbul ignore next */
              cov_1ad6gddzlg().b[60][0]++;
              cov_1ad6gddzlg().s[165]++;
              console.error('Redirect loop detected, breaking chain');
              /* istanbul ignore next */
              cov_1ad6gddzlg().s[166]++;
              return [2 /*return*/, server_1.NextResponse.json({
                error: 'Authentication redirect loop detected'
              }, {
                status: 500
              })];
            } else
            /* istanbul ignore next */
            {
              cov_1ad6gddzlg().b[60][1]++;
            }
            cov_1ad6gddzlg().s[167]++;
            loginUrl = new URL('/login', request.url);
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[168]++;
            fullPath = pathname + (
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[62][0]++, request.nextUrl.search) ||
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[62][1]++, ''));
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[169]++;
            loginUrl.searchParams.set('callbackUrl', fullPath);
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[170]++;
            response_1 = server_1.NextResponse.redirect(loginUrl);
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[171]++;
            redirectCount = parseInt(
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[63][0]++, isRedirectLoop) ||
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[63][1]++, '0')) + 1;
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[172]++;
            response_1.headers.set('x-middleware-redirect-count', redirectCount.toString());
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[173]++;
            return [2 /*return*/, response_1];
          } else
          /* istanbul ignore next */
          {
            cov_1ad6gddzlg().b[58][1]++;
          }
          // Handle admin API routes (require admin authentication)
          cov_1ad6gddzlg().s[174]++;
          if (isAdminApiRoute) {
            /* istanbul ignore next */
            cov_1ad6gddzlg().b[64][0]++;
            cov_1ad6gddzlg().s[175]++;
            if (!token) {
              /* istanbul ignore next */
              cov_1ad6gddzlg().b[65][0]++;
              cov_1ad6gddzlg().s[176]++;
              return [2 /*return*/, server_1.NextResponse.json({
                error: 'Authentication required'
              }, {
                status: 401
              })];
            } else
            /* istanbul ignore next */
            {
              cov_1ad6gddzlg().b[65][1]++;
            }
            // Check if user has admin role
            cov_1ad6gddzlg().s[177]++;
            if (
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[67][0]++, !token.role) ||
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[67][1]++, token.role !== 'admin')) {
              /* istanbul ignore next */
              cov_1ad6gddzlg().b[66][0]++;
              cov_1ad6gddzlg().s[178]++;
              console.warn("Non-admin user attempted to access admin API: ".concat(pathname), {
                userId: token.sub,
                email: token.email,
                role: token.role
              });
              /* istanbul ignore next */
              cov_1ad6gddzlg().s[179]++;
              return [2 /*return*/, server_1.NextResponse.json({
                error: 'Admin access required'
              }, {
                status: 403
              })];
            } else
            /* istanbul ignore next */
            {
              cov_1ad6gddzlg().b[66][1]++;
            }
          } else
          /* istanbul ignore next */
          {
            cov_1ad6gddzlg().b[64][1]++;
          }
          // Handle protected API routes
          cov_1ad6gddzlg().s[180]++;
          if (
          /* istanbul ignore next */
          (cov_1ad6gddzlg().b[69][0]++, isProtectedApiRoute) &&
          /* istanbul ignore next */
          (cov_1ad6gddzlg().b[69][1]++, !token) &&
          /* istanbul ignore next */
          (cov_1ad6gddzlg().b[69][2]++, !isPublicApiRoute)) {
            /* istanbul ignore next */
            cov_1ad6gddzlg().b[68][0]++;
            cov_1ad6gddzlg().s[181]++;
            return [2 /*return*/, server_1.NextResponse.json({
              error: 'Authentication required'
            }, {
              status: 401
            })];
          } else
          /* istanbul ignore next */
          {
            cov_1ad6gddzlg().b[68][1]++;
          }
          // Redirect authenticated users away from auth pages (with loop prevention)
          cov_1ad6gddzlg().s[182]++;
          if (
          /* istanbul ignore next */
          (cov_1ad6gddzlg().b[71][0]++, token) && (
          /* istanbul ignore next */
          (cov_1ad6gddzlg().b[71][1]++, pathname === '/login') ||
          /* istanbul ignore next */
          (cov_1ad6gddzlg().b[71][2]++, pathname === '/signup'))) {
            /* istanbul ignore next */
            cov_1ad6gddzlg().b[70][0]++;
            cov_1ad6gddzlg().s[183]++;
            isRedirectLoop = request.headers.get('x-middleware-redirect-count');
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[184]++;
            if (
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[73][0]++, isRedirectLoop) &&
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[73][1]++, parseInt(isRedirectLoop) > 3)) {
              /* istanbul ignore next */
              cov_1ad6gddzlg().b[72][0]++;
              cov_1ad6gddzlg().s[185]++;
              console.error('Auth redirect loop detected, allowing access to auth page');
              /* istanbul ignore next */
              cov_1ad6gddzlg().s[186]++;
              response_2 = server_1.NextResponse.next();
              /* istanbul ignore next */
              cov_1ad6gddzlg().s[187]++;
              return [2 /*return*/, addSecurityHeaders(response_2)];
            } else
            /* istanbul ignore next */
            {
              cov_1ad6gddzlg().b[72][1]++;
            }
            cov_1ad6gddzlg().s[188]++;
            callbackUrl = request.nextUrl.searchParams.get('callbackUrl');
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[189]++;
            redirectTo =
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[75][0]++, callbackUrl) &&
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[75][1]++, callbackUrl !== '/login') &&
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[75][2]++, callbackUrl !== '/signup') ?
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[74][0]++, callbackUrl) :
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[74][1]++, '/dashboard');
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[190]++;
            response_3 = server_1.NextResponse.redirect(new URL(redirectTo, request.url));
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[191]++;
            redirectCount = parseInt(
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[76][0]++, isRedirectLoop) ||
            /* istanbul ignore next */
            (cov_1ad6gddzlg().b[76][1]++, '0')) + 1;
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[192]++;
            response_3.headers.set('x-middleware-redirect-count', redirectCount.toString());
            /* istanbul ignore next */
            cov_1ad6gddzlg().s[193]++;
            return [2 /*return*/, response_3];
          } else
          /* istanbul ignore next */
          {
            cov_1ad6gddzlg().b[70][1]++;
          }
          cov_1ad6gddzlg().s[194]++;
          response = server_1.NextResponse.next();
          /* istanbul ignore next */
          cov_1ad6gddzlg().s[195]++;
          return [2 /*return*/, addSecurityHeaders(response)];
      }
    });
  });
}
/* istanbul ignore next */
cov_1ad6gddzlg().s[196]++;
exports.config = {
  matcher: [
  /*
   * Match all request paths except for the ones starting with:
   * - _next/static (static files)
   * - _next/image (image optimization files)
   * - favicon.ico (favicon file)
   * - public folder files
   */
  '/((?!_next/static|_next/image|favicon.ico|public/).*)']
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJleHBvcnRzIiwibWlkZGxld2FyZSIsInNlcnZlcl8xIiwiY292XzFhZDZnZGR6bGciLCJzIiwicmVxdWlyZSIsImp3dF8xIiwicHJvdGVjdGVkUm91dGVzIiwiYWRtaW5Sb3V0ZXMiLCJwcm90ZWN0ZWRBcGlSb3V0ZXMiLCJhZG1pbkFwaVJvdXRlcyIsInB1YmxpY0FwaVJvdXRlcyIsInJhdGVMaW1pdFN0b3JlIiwiTWFwIiwiZ2V0Q2xpZW50SVAiLCJyZXF1ZXN0IiwiZiIsImZvcndhcmRlZCIsImhlYWRlcnMiLCJnZXQiLCJiIiwic3BsaXQiLCJ0cmltIiwicmVhbElQIiwiaXAiLCJpc1JhdGVMaW1pdGVkIiwid2luZG93TXMiLCJtYXhSZXF1ZXN0cyIsImNsaWVudElQIiwibm93IiwiRGF0ZSIsIndpbmRvd1N0YXJ0IiwiQXJyYXkiLCJmcm9tIiwiZW50cmllcyIsImZvckVhY2giLCJfYSIsImtleSIsInZhbHVlIiwicmVzZXRUaW1lIiwiZGVsZXRlIiwiZW50cnkiLCJjb3VudCIsInNldCIsImFkZFNlY3VyaXR5SGVhZGVycyIsInJlc3BvbnNlIiwibm9uY2UiLCJjcnlwdG8iLCJyYW5kb21VVUlEIiwiY3NwIiwiY29uY2F0Iiwiam9pbiIsInBhdGhuYW1lIiwibmV4dFVybCIsImFsbG93ZWRTdGF0aWNFeHRlbnNpb25zIiwiYmxvY2tlZFBhdHRlcm5zIiwiaGFzQmxvY2tlZFBhdHRlcm4iLCJzb21lIiwicGF0dGVybiIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJjb25zb2xlIiwid2FybiIsIk5leHRSZXNwb25zZSIsImpzb24iLCJlcnJvciIsInN0YXR1cyIsImlzTmV4dEludGVybmFsIiwic3RhcnRzV2l0aCIsImlzRmF2aWNvbiIsImlzQWxsb3dlZFN0YXRpYyIsImV4dCIsImVuZHNXaXRoIiwibmV4dCIsImdldFRva2VuIiwicmVxIiwic2VjcmV0IiwicHJvY2VzcyIsImVudiIsIk5FWFRBVVRIX1NFQ1JFVCIsInRva2VuIiwic2VudCIsImlzUHJvdGVjdGVkUm91dGUiLCJyb3V0ZSIsImlzQWRtaW5Sb3V0ZSIsImlzUHJvdGVjdGVkQXBpUm91dGUiLCJpc0FkbWluQXBpUm91dGUiLCJpc1B1YmxpY0FwaVJvdXRlIiwidXNlckFnZW50IiwiaXNBZG1pbiIsImxvZ2luVXJsIiwiVVJMIiwidXJsIiwiZnVsbFBhdGgiLCJzZWFyY2giLCJzZWFyY2hQYXJhbXMiLCJyZWRpcmVjdCIsInJvbGUiLCJ1c2VySWQiLCJzdWIiLCJlbWFpbCIsImlzUmVkaXJlY3RMb29wIiwicGFyc2VJbnQiLCJyZXNwb25zZV8xIiwicmVkaXJlY3RDb3VudCIsInRvU3RyaW5nIiwicmVzcG9uc2VfMiIsImNhbGxiYWNrVXJsIiwicmVkaXJlY3RUbyIsInJlc3BvbnNlXzMiLCJjb25maWciLCJtYXRjaGVyIl0sInNvdXJjZXMiOlsiL1VzZXJzL2RkNjAvZmFhZm8vZmFhZm8vZmFhZm8tY2FyZWVyLXBsYXRmb3JtL21pZGRsZXdhcmUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcbmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAnbmV4dC1hdXRoL2p3dCc7XG5cbi8vIERlZmluZSBwcm90ZWN0ZWQgcm91dGVzIHRoYXQgcmVxdWlyZSBhdXRoZW50aWNhdGlvblxuY29uc3QgcHJvdGVjdGVkUm91dGVzID0gW1xuICAnL2Rhc2hib2FyZCcsXG4gICcvcHJvZmlsZScsXG4gICcvYXNzZXNzbWVudCcsXG4gICcvZm9ydW0nLFxuICAnL2ZyZWVkb20tZnVuZCcsXG4gICcvcHJvZ3Jlc3MnLFxuICAnL3JlY29tbWVuZGF0aW9ucycsXG4gICcvaW50ZXJ2aWV3LXByYWN0aWNlJyxcbiAgJy9yZXN1bWUtYnVpbGRlcicsXG4gICcvdG9vbHMnLFxuXTtcblxuLy8gRGVmaW5lIGFkbWluIHJvdXRlcyB0aGF0IHJlcXVpcmUgYWRtaW4gYXV0aGVudGljYXRpb25cbmNvbnN0IGFkbWluUm91dGVzID0gW1xuICAnL2FkbWluJyxcbl07XG5cbi8vIERlZmluZSBBUEkgcm91dGVzIHRoYXQgcmVxdWlyZSBhdXRoZW50aWNhdGlvblxuY29uc3QgcHJvdGVjdGVkQXBpUm91dGVzID0gW1xuICAnL2FwaS9hc3Nlc3NtZW50JyxcbiAgJy9hcGkvcHJvZmlsZScsXG4gICcvYXBpL2ZyZWVkb20tZnVuZCcsXG4gICcvYXBpL2xlYXJuaW5nLXByb2dyZXNzJyxcbiAgJy9hcGkvcGVyc29uYWxpemVkLXJlc291cmNlcycsXG4gICcvYXBpL3Byb2dyZXNzLXRyYWNrZXInLFxuICAnL2FwaS9yZWNvbW1lbmRhdGlvbnMnLFxuICAnL2FwaS9yZXNvdXJjZS1yYXRpbmdzJyxcbiAgJy9hcGkvaW50ZXJ2aWV3LXByYWN0aWNlJyxcbiAgJy9hcGkvcmVzdW1lLWJ1aWxkZXInLFxuICAnL2FwaS90b29scycsXG5dO1xuXG4vLyBEZWZpbmUgYWRtaW4gQVBJIHJvdXRlcyB0aGF0IHJlcXVpcmUgYWRtaW4gYXV0aGVudGljYXRpb25cbmNvbnN0IGFkbWluQXBpUm91dGVzID0gW1xuICAnL2FwaS9hZG1pbicsXG5dO1xuXG4vLyBEZWZpbmUgcHVibGljIEFQSSByb3V0ZXMgdGhhdCBkb24ndCByZXF1aXJlIGF1dGhlbnRpY2F0aW9uXG5jb25zdCBwdWJsaWNBcGlSb3V0ZXMgPSBbXG4gICcvYXBpL2F1dGgnLFxuICAnL2FwaS9zaWdudXAnLFxuICAnL2FwaS9jYXJlZXItcGF0aHMnLFxuICAnL2FwaS9sZWFybmluZy1yZXNvdXJjZXMnLFxuICAnL2FwaS9jb250YWN0JyxcbiAgJy9hcGkvY3NyZi10b2tlbicsXG5dO1xuXG4vLyBSYXRlIGxpbWl0aW5nIHN0b3JlICh1c2UgUmVkaXMgaW4gcHJvZHVjdGlvbilcbmNvbnN0IHJhdGVMaW1pdFN0b3JlID0gbmV3IE1hcDxzdHJpbmcsIHsgY291bnQ6IG51bWJlcjsgcmVzZXRUaW1lOiBudW1iZXIgfT4oKTtcblxuZnVuY3Rpb24gZ2V0Q2xpZW50SVAocmVxdWVzdDogTmV4dFJlcXVlc3QpOiBzdHJpbmcge1xuICBjb25zdCBmb3J3YXJkZWQgPSByZXF1ZXN0LmhlYWRlcnMuZ2V0KCd4LWZvcndhcmRlZC1mb3InKTtcbiAgaWYgKGZvcndhcmRlZCkge1xuICAgIHJldHVybiBmb3J3YXJkZWQuc3BsaXQoJywnKVswXS50cmltKCk7XG4gIH1cbiAgXG4gIGNvbnN0IHJlYWxJUCA9IHJlcXVlc3QuaGVhZGVycy5nZXQoJ3gtcmVhbC1pcCcpO1xuICBpZiAocmVhbElQKSB7XG4gICAgcmV0dXJuIHJlYWxJUDtcbiAgfVxuICBcbiAgcmV0dXJuIChyZXF1ZXN0IGFzIGFueSkuaXAgfHwgJ3Vua25vd24nO1xufVxuXG5mdW5jdGlvbiBpc1JhdGVMaW1pdGVkKHJlcXVlc3Q6IE5leHRSZXF1ZXN0LCB3aW5kb3dNczogbnVtYmVyID0gMTUgKiA2MCAqIDEwMDAsIG1heFJlcXVlc3RzOiBudW1iZXIgPSAxMDApOiBib29sZWFuIHtcbiAgY29uc3QgY2xpZW50SVAgPSBnZXRDbGllbnRJUChyZXF1ZXN0KTtcbiAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcbiAgY29uc3Qgd2luZG93U3RhcnQgPSBub3cgLSB3aW5kb3dNcztcbiAgXG4gIC8vIENsZWFuIHVwIG9sZCBlbnRyaWVzXG4gIEFycmF5LmZyb20ocmF0ZUxpbWl0U3RvcmUuZW50cmllcygpKS5mb3JFYWNoKChba2V5LCB2YWx1ZV0pID0+IHtcbiAgICBpZiAodmFsdWUucmVzZXRUaW1lIDwgd2luZG93U3RhcnQpIHtcbiAgICAgIHJhdGVMaW1pdFN0b3JlLmRlbGV0ZShrZXkpO1xuICAgIH1cbiAgfSk7XG4gIFxuICAvLyBHZXQgb3IgY3JlYXRlIGVudHJ5IGZvciB0aGlzIElQXG4gIGNvbnN0IGVudHJ5ID0gcmF0ZUxpbWl0U3RvcmUuZ2V0KGNsaWVudElQKSB8fCB7IGNvdW50OiAwLCByZXNldFRpbWU6IG5vdyArIHdpbmRvd01zIH07XG4gIFxuICAvLyBSZXNldCBpZiB3aW5kb3cgaGFzIGV4cGlyZWRcbiAgaWYgKGVudHJ5LnJlc2V0VGltZSA8IG5vdykge1xuICAgIGVudHJ5LmNvdW50ID0gMDtcbiAgICBlbnRyeS5yZXNldFRpbWUgPSBub3cgKyB3aW5kb3dNcztcbiAgfVxuICBcbiAgLy8gSW5jcmVtZW50IGNvdW50XG4gIGVudHJ5LmNvdW50Kys7XG4gIHJhdGVMaW1pdFN0b3JlLnNldChjbGllbnRJUCwgZW50cnkpO1xuICBcbiAgcmV0dXJuIGVudHJ5LmNvdW50ID4gbWF4UmVxdWVzdHM7XG59XG5cbmZ1bmN0aW9uIGFkZFNlY3VyaXR5SGVhZGVycyhyZXNwb25zZTogTmV4dFJlc3BvbnNlKTogTmV4dFJlc3BvbnNlIHtcbiAgLy8gQWRkIGNvbXByZWhlbnNpdmUgc2VjdXJpdHkgaGVhZGVyc1xuICByZXNwb25zZS5oZWFkZXJzLnNldCgnWC1GcmFtZS1PcHRpb25zJywgJ0RFTlknKTtcbiAgcmVzcG9uc2UuaGVhZGVycy5zZXQoJ1gtQ29udGVudC1UeXBlLU9wdGlvbnMnLCAnbm9zbmlmZicpO1xuICByZXNwb25zZS5oZWFkZXJzLnNldCgnUmVmZXJyZXItUG9saWN5JywgJ3N0cmljdC1vcmlnaW4td2hlbi1jcm9zcy1vcmlnaW4nKTtcbiAgcmVzcG9uc2UuaGVhZGVycy5zZXQoJ1gtWFNTLVByb3RlY3Rpb24nLCAnMTsgbW9kZT1ibG9jaycpO1xuICByZXNwb25zZS5oZWFkZXJzLnNldCgnUGVybWlzc2lvbnMtUG9saWN5JywgJ2NhbWVyYT0oKSwgbWljcm9waG9uZT0oKSwgZ2VvbG9jYXRpb249KCknKTtcblxuICAvLyBHZW5lcmF0ZSBub25jZSBmb3IgaW5saW5lIHNjcmlwdHNcbiAgY29uc3Qgbm9uY2UgPSBjcnlwdG8ucmFuZG9tVVVJRCgpO1xuXG4gIC8vIEVuaGFuY2VkIENvbnRlbnQgU2VjdXJpdHkgUG9saWN5IC0gUmVtb3ZlZCB1bnNhZmUtaW5saW5lIGZvciBiZXR0ZXIgc2VjdXJpdHlcbiAgY29uc3QgY3NwID0gW1xuICAgIFwiZGVmYXVsdC1zcmMgJ3NlbGYnXCIsXG4gICAgYHNjcmlwdC1zcmMgJ3NlbGYnICdub25jZS0ke25vbmNlfScgJ3Vuc2FmZS1ldmFsJyBodHRwczovL3ZlcmNlbC5saXZlIGh0dHBzOi8vdmEudmVyY2VsLXNjcmlwdHMuY29tIGh0dHBzOi8vYnJvd3Nlci5zZW50cnktY2RuLmNvbWAsXG4gICAgXCJzdHlsZS1zcmMgJ3NlbGYnICd1bnNhZmUtaW5saW5lJyBodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tXCIsXG4gICAgXCJpbWctc3JjICdzZWxmJyBkYXRhOiBodHRwczogYmxvYjpcIixcbiAgICBcImZvbnQtc3JjICdzZWxmJyBkYXRhOiBodHRwczovL2ZvbnRzLmdzdGF0aWMuY29tXCIsXG4gICAgXCJjb25uZWN0LXNyYyAnc2VsZicgaHR0cHM6Ly92ZXJjZWwubGl2ZSBodHRwczovL3ZpdGFscy52ZXJjZWwtaW5zaWdodHMuY29tIGh0dHBzOi8vKi5zZW50cnkuaW9cIixcbiAgICBcIndvcmtlci1zcmMgJ3NlbGYnIGJsb2I6XCIsXG4gICAgXCJjaGlsZC1zcmMgJ3NlbGYnIGJsb2I6XCIsXG4gICAgXCJmcmFtZS1hbmNlc3RvcnMgJ25vbmUnXCIsXG4gICAgXCJiYXNlLXVyaSAnc2VsZidcIixcbiAgICBcImZvcm0tYWN0aW9uICdzZWxmJ1wiXG4gIF0uam9pbignOyAnKTtcblxuICByZXNwb25zZS5oZWFkZXJzLnNldCgnQ29udGVudC1TZWN1cml0eS1Qb2xpY3knLCBjc3ApO1xuICByZXNwb25zZS5oZWFkZXJzLnNldCgnWC1Ob25jZScsIG5vbmNlKTtcblxuICAvLyBBZGQgSFNUUyBoZWFkZXIgZm9yIEhUVFBTIChhbHdheXMgc2V0IGZvciBzZWN1cml0eSB0ZXN0aW5nKVxuICByZXNwb25zZS5oZWFkZXJzLnNldCgnU3RyaWN0LVRyYW5zcG9ydC1TZWN1cml0eScsICdtYXgtYWdlPTMxNTM2MDAwOyBpbmNsdWRlU3ViRG9tYWluczsgcHJlbG9hZCcpO1xuXG4gIC8vIEFkZGl0aW9uYWwgc2VjdXJpdHkgaGVhZGVyc1xuICByZXNwb25zZS5oZWFkZXJzLnNldCgnQ3Jvc3MtT3JpZ2luLUVtYmVkZGVyLVBvbGljeScsICdyZXF1aXJlLWNvcnAnKTtcbiAgcmVzcG9uc2UuaGVhZGVycy5zZXQoJ0Nyb3NzLU9yaWdpbi1PcGVuZXItUG9saWN5JywgJ3NhbWUtb3JpZ2luJyk7XG4gIHJlc3BvbnNlLmhlYWRlcnMuc2V0KCdDcm9zcy1PcmlnaW4tUmVzb3VyY2UtUG9saWN5JywgJ3NhbWUtb3JpZ2luJyk7XG5cbiAgcmV0dXJuIHJlc3BvbnNlO1xufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbWlkZGxld2FyZShyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICBjb25zdCB7IHBhdGhuYW1lIH0gPSByZXF1ZXN0Lm5leHRVcmw7XG5cbiAgLy8gRGVmaW5lIGFsbG93ZWQgc3RhdGljIGZpbGUgZXh0ZW5zaW9ucyAoc2VjdXJpdHktZmlyc3QgYXBwcm9hY2gpXG4gIGNvbnN0IGFsbG93ZWRTdGF0aWNFeHRlbnNpb25zID0gW1xuICAgICcuanMnLCAnLmNzcycsICcucG5nJywgJy5qcGcnLCAnLmpwZWcnLCAnLmdpZicsICcuc3ZnJywgJy5pY28nLFxuICAgICcud29mZicsICcud29mZjInLCAnLnR0ZicsICcuZW90JywgJy53ZWJwJywgJy5hdmlmJywgJy5tcDQnLCAnLndlYm0nXG4gIF07XG5cbiAgLy8gRGVmaW5lIGV4cGxpY2l0bHkgYmxvY2tlZCBmaWxlIHBhdHRlcm5zIChzZWN1cml0eSBjcml0aWNhbClcbiAgY29uc3QgYmxvY2tlZFBhdHRlcm5zID0gW1xuICAgICcuZW52JywgJy5naXQnLCAnLmRiJywgJy5zcWxpdGUnLCAnLmxvZycsICcuYmFrJywgJy5iYWNrdXAnLFxuICAgICcuY29uZmlnJywgJy5pbmknLCAnLmNvbmYnLCAnLmtleScsICcucGVtJywgJy5jcnQnLCAnLnAxMicsXG4gICAgJy5qc29uJywgJy55YW1sJywgJy55bWwnLCAnLnhtbCcsICcubWQnLCAnLnR4dCcsICcuY3N2J1xuICBdO1xuXG4gIC8vIENoZWNrIGZvciBibG9ja2VkIGZpbGUgcGF0dGVybnMgZmlyc3QgKGhpZ2hlc3Qgc2VjdXJpdHkgcHJpb3JpdHkpXG4gIGNvbnN0IGhhc0Jsb2NrZWRQYXR0ZXJuID0gYmxvY2tlZFBhdHRlcm5zLnNvbWUocGF0dGVybiA9PlxuICAgIHBhdGhuYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMocGF0dGVybi50b0xvd2VyQ2FzZSgpKVxuICApO1xuXG4gIGlmIChoYXNCbG9ja2VkUGF0dGVybikge1xuICAgIGNvbnNvbGUud2FybihgQmxvY2tlZCBhY2Nlc3MgdG8gc2Vuc2l0aXZlIGZpbGU6ICR7cGF0aG5hbWV9YCk7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogJ0FjY2VzcyBkZW5pZWQnIH0sXG4gICAgICB7IHN0YXR1czogNDAzIH1cbiAgICApO1xuICB9XG5cbiAgLy8gU2tpcCBtaWRkbGV3YXJlIG9ubHkgZm9yIGV4cGxpY2l0bHkgYWxsb3dlZCBzdGF0aWMgZmlsZXMgYW5kIE5leHQuanMgaW50ZXJuYWxzXG4gIGNvbnN0IGlzTmV4dEludGVybmFsID0gcGF0aG5hbWUuc3RhcnRzV2l0aCgnL19uZXh0JykgfHwgcGF0aG5hbWUuc3RhcnRzV2l0aCgnL2FwaS9fbmV4dCcpO1xuICBjb25zdCBpc0Zhdmljb24gPSBwYXRobmFtZSA9PT0gJy9mYXZpY29uLmljbyc7XG4gIGNvbnN0IGlzQWxsb3dlZFN0YXRpYyA9IGFsbG93ZWRTdGF0aWNFeHRlbnNpb25zLnNvbWUoZXh0ID0+XG4gICAgcGF0aG5hbWUudG9Mb3dlckNhc2UoKS5lbmRzV2l0aChleHQudG9Mb3dlckNhc2UoKSlcbiAgKTtcblxuICBpZiAoaXNOZXh0SW50ZXJuYWwgfHwgaXNGYXZpY29uIHx8IChpc0FsbG93ZWRTdGF0aWMgJiYgcGF0aG5hbWUuc3RhcnRzV2l0aCgnL3B1YmxpYy8nKSkpIHtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLm5leHQoKTtcbiAgfVxuXG4gIC8vIEdldCB0aGUgdG9rZW4gdG8gY2hlY2sgYXV0aGVudGljYXRpb24gc3RhdHVzXG4gIGNvbnN0IHRva2VuID0gYXdhaXQgZ2V0VG9rZW4oeyByZXE6IHJlcXVlc3QsIHNlY3JldDogcHJvY2Vzcy5lbnYuTkVYVEFVVEhfU0VDUkVUIH0pO1xuXG4gIC8vIENoZWNrIGlmIHRoZSByb3V0ZSByZXF1aXJlcyBhdXRoZW50aWNhdGlvblxuICBjb25zdCBpc1Byb3RlY3RlZFJvdXRlID0gcHJvdGVjdGVkUm91dGVzLnNvbWUocm91dGUgPT4gcGF0aG5hbWUuc3RhcnRzV2l0aChyb3V0ZSkpO1xuICBjb25zdCBpc0FkbWluUm91dGUgPSBhZG1pblJvdXRlcy5zb21lKHJvdXRlID0+IHBhdGhuYW1lLnN0YXJ0c1dpdGgocm91dGUpKTtcbiAgY29uc3QgaXNQcm90ZWN0ZWRBcGlSb3V0ZSA9IHByb3RlY3RlZEFwaVJvdXRlcy5zb21lKHJvdXRlID0+IHBhdGhuYW1lLnN0YXJ0c1dpdGgocm91dGUpKTtcbiAgY29uc3QgaXNBZG1pbkFwaVJvdXRlID0gYWRtaW5BcGlSb3V0ZXMuc29tZShyb3V0ZSA9PiBwYXRobmFtZS5zdGFydHNXaXRoKHJvdXRlKSk7XG4gIGNvbnN0IGlzUHVibGljQXBpUm91dGUgPSBwdWJsaWNBcGlSb3V0ZXMuc29tZShyb3V0ZSA9PiBwYXRobmFtZS5zdGFydHNXaXRoKHJvdXRlKSk7XG5cbiAgLy8gQXBwbHkgcmF0ZSBsaW1pdGluZyBmb3IgQVBJIHJvdXRlcyBhbmQgc2Vuc2l0aXZlIGVuZHBvaW50c1xuICBpZiAocGF0aG5hbWUuc3RhcnRzV2l0aCgnL2FwaS8nKSB8fCBpc0FkbWluUm91dGUgfHwgaXNBZG1pbkFwaVJvdXRlKSB7XG4gICAgLy8gVXNlIHN0cmljdGVyIHJhdGUgbGltaXRpbmcgZm9yIGFkbWluIHJvdXRlc1xuICAgIGNvbnN0IG1heFJlcXVlc3RzID0gKGlzQWRtaW5Sb3V0ZSB8fCBpc0FkbWluQXBpUm91dGUpID8gNTAgOiAxMDA7XG4gICAgY29uc3Qgd2luZG93TXMgPSAoaXNBZG1pblJvdXRlIHx8IGlzQWRtaW5BcGlSb3V0ZSkgPyAxNSAqIDYwICogMTAwMCA6IDE1ICogNjAgKiAxMDAwO1xuXG4gICAgaWYgKGlzUmF0ZUxpbWl0ZWQocmVxdWVzdCwgd2luZG93TXMsIG1heFJlcXVlc3RzKSkge1xuICAgICAgY29uc29sZS53YXJuKGBSYXRlIGxpbWl0IGV4Y2VlZGVkIGZvciAke3BhdGhuYW1lfWAsIHtcbiAgICAgICAgaXA6IGdldENsaWVudElQKHJlcXVlc3QpLFxuICAgICAgICB1c2VyQWdlbnQ6IHJlcXVlc3QuaGVhZGVycy5nZXQoJ3VzZXItYWdlbnQnKSxcbiAgICAgICAgaXNBZG1pbjogaXNBZG1pblJvdXRlIHx8IGlzQWRtaW5BcGlSb3V0ZVxuICAgICAgfSk7XG5cbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ1RvbyBtYW55IHJlcXVlc3RzLiBQbGVhc2UgdHJ5IGFnYWluIGxhdGVyLicgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQyOSB9XG4gICAgICApO1xuICAgIH1cbiAgfVxuXG4gIC8vIEhhbmRsZSBhZG1pbiByb3V0ZXMgKHJlcXVpcmUgYWRtaW4gYXV0aGVudGljYXRpb24pXG4gIGlmIChpc0FkbWluUm91dGUpIHtcbiAgICBpZiAoIXRva2VuKSB7XG4gICAgICBjb25zdCBsb2dpblVybCA9IG5ldyBVUkwoJy9sb2dpbicsIHJlcXVlc3QudXJsKTtcbiAgICAgIGNvbnN0IGZ1bGxQYXRoID0gcGF0aG5hbWUgKyAocmVxdWVzdC5uZXh0VXJsLnNlYXJjaCB8fCAnJyk7XG4gICAgICBsb2dpblVybC5zZWFyY2hQYXJhbXMuc2V0KCdjYWxsYmFja1VybCcsIGZ1bGxQYXRoKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UucmVkaXJlY3QobG9naW5VcmwpO1xuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIHVzZXIgaGFzIGFkbWluIHJvbGVcbiAgICBpZiAoIXRva2VuLnJvbGUgfHwgdG9rZW4ucm9sZSAhPT0gJ2FkbWluJykge1xuICAgICAgY29uc29sZS53YXJuKGBOb24tYWRtaW4gdXNlciBhdHRlbXB0ZWQgdG8gYWNjZXNzIGFkbWluIHJvdXRlOiAke3BhdGhuYW1lfWAsIHtcbiAgICAgICAgdXNlcklkOiB0b2tlbi5zdWIsXG4gICAgICAgIGVtYWlsOiB0b2tlbi5lbWFpbCxcbiAgICAgICAgcm9sZTogdG9rZW4ucm9sZVxuICAgICAgfSk7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdBZG1pbiBhY2Nlc3MgcmVxdWlyZWQnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDMgfVxuICAgICAgKTtcbiAgICB9XG4gIH1cblxuICAvLyBIYW5kbGUgcHJvdGVjdGVkIHJvdXRlc1xuICBpZiAoaXNQcm90ZWN0ZWRSb3V0ZSAmJiAhdG9rZW4pIHtcbiAgICAvLyBQcmV2ZW50IHJlZGlyZWN0IGxvb3BzIGJ5IGNoZWNraW5nIGlmIHdlJ3JlIGFscmVhZHkgYmVpbmcgcmVkaXJlY3RlZFxuICAgIGNvbnN0IGlzUmVkaXJlY3RMb29wID0gcmVxdWVzdC5oZWFkZXJzLmdldCgneC1taWRkbGV3YXJlLXJlZGlyZWN0LWNvdW50Jyk7XG4gICAgaWYgKGlzUmVkaXJlY3RMb29wICYmIHBhcnNlSW50KGlzUmVkaXJlY3RMb29wKSA+IDMpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1JlZGlyZWN0IGxvb3AgZGV0ZWN0ZWQsIGJyZWFraW5nIGNoYWluJyk7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdBdXRoZW50aWNhdGlvbiByZWRpcmVjdCBsb29wIGRldGVjdGVkJyB9LFxuICAgICAgICB7IHN0YXR1czogNTAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgY29uc3QgbG9naW5VcmwgPSBuZXcgVVJMKCcvbG9naW4nLCByZXF1ZXN0LnVybCk7XG4gICAgLy8gSW5jbHVkZSBxdWVyeSBwYXJhbWV0ZXJzIGluIHRoZSByZWRpcmVjdCBVUkxcbiAgICBjb25zdCBmdWxsUGF0aCA9IHBhdGhuYW1lICsgKHJlcXVlc3QubmV4dFVybC5zZWFyY2ggfHwgJycpO1xuICAgIGxvZ2luVXJsLnNlYXJjaFBhcmFtcy5zZXQoJ2NhbGxiYWNrVXJsJywgZnVsbFBhdGgpO1xuXG4gICAgY29uc3QgcmVzcG9uc2UgPSBOZXh0UmVzcG9uc2UucmVkaXJlY3QobG9naW5VcmwpO1xuICAgIC8vIFRyYWNrIHJlZGlyZWN0IGNvdW50IHRvIHByZXZlbnQgbG9vcHNcbiAgICBjb25zdCByZWRpcmVjdENvdW50ID0gcGFyc2VJbnQoaXNSZWRpcmVjdExvb3AgfHwgJzAnKSArIDE7XG4gICAgcmVzcG9uc2UuaGVhZGVycy5zZXQoJ3gtbWlkZGxld2FyZS1yZWRpcmVjdC1jb3VudCcsIHJlZGlyZWN0Q291bnQudG9TdHJpbmcoKSk7XG4gICAgcmV0dXJuIHJlc3BvbnNlO1xuICB9XG5cbiAgLy8gSGFuZGxlIGFkbWluIEFQSSByb3V0ZXMgKHJlcXVpcmUgYWRtaW4gYXV0aGVudGljYXRpb24pXG4gIGlmIChpc0FkbWluQXBpUm91dGUpIHtcbiAgICBpZiAoIXRva2VuKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCcgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMSB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIENoZWNrIGlmIHVzZXIgaGFzIGFkbWluIHJvbGVcbiAgICBpZiAoIXRva2VuLnJvbGUgfHwgdG9rZW4ucm9sZSAhPT0gJ2FkbWluJykge1xuICAgICAgY29uc29sZS53YXJuKGBOb24tYWRtaW4gdXNlciBhdHRlbXB0ZWQgdG8gYWNjZXNzIGFkbWluIEFQSTogJHtwYXRobmFtZX1gLCB7XG4gICAgICAgIHVzZXJJZDogdG9rZW4uc3ViLFxuICAgICAgICBlbWFpbDogdG9rZW4uZW1haWwsXG4gICAgICAgIHJvbGU6IHRva2VuLnJvbGVcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnQWRtaW4gYWNjZXNzIHJlcXVpcmVkJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAzIH1cbiAgICAgICk7XG4gICAgfVxuICB9XG5cbiAgLy8gSGFuZGxlIHByb3RlY3RlZCBBUEkgcm91dGVzXG4gIGlmIChpc1Byb3RlY3RlZEFwaVJvdXRlICYmICF0b2tlbiAmJiAhaXNQdWJsaWNBcGlSb3V0ZSkge1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCcgfSxcbiAgICAgIHsgc3RhdHVzOiA0MDEgfVxuICAgICk7XG4gIH1cblxuICAvLyBSZWRpcmVjdCBhdXRoZW50aWNhdGVkIHVzZXJzIGF3YXkgZnJvbSBhdXRoIHBhZ2VzICh3aXRoIGxvb3AgcHJldmVudGlvbilcbiAgaWYgKHRva2VuICYmIChwYXRobmFtZSA9PT0gJy9sb2dpbicgfHwgcGF0aG5hbWUgPT09ICcvc2lnbnVwJykpIHtcbiAgICAvLyBDaGVjayBmb3IgcmVkaXJlY3QgbG9vcHNcbiAgICBjb25zdCBpc1JlZGlyZWN0TG9vcCA9IHJlcXVlc3QuaGVhZGVycy5nZXQoJ3gtbWlkZGxld2FyZS1yZWRpcmVjdC1jb3VudCcpO1xuICAgIGlmIChpc1JlZGlyZWN0TG9vcCAmJiBwYXJzZUludChpc1JlZGlyZWN0TG9vcCkgPiAzKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdBdXRoIHJlZGlyZWN0IGxvb3AgZGV0ZWN0ZWQsIGFsbG93aW5nIGFjY2VzcyB0byBhdXRoIHBhZ2UnKTtcbiAgICAgIC8vIEFsbG93IGFjY2VzcyB0byBwcmV2ZW50IGluZmluaXRlIGxvb3BzXG4gICAgICBjb25zdCByZXNwb25zZSA9IE5leHRSZXNwb25zZS5uZXh0KCk7XG4gICAgICByZXR1cm4gYWRkU2VjdXJpdHlIZWFkZXJzKHJlc3BvbnNlKTtcbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiB1c2VyIGlzIHRyeWluZyB0byBhY2Nlc3MgbG9naW4gd2l0aCBhIGNhbGxiYWNrIFVSTFxuICAgIGNvbnN0IGNhbGxiYWNrVXJsID0gcmVxdWVzdC5uZXh0VXJsLnNlYXJjaFBhcmFtcy5nZXQoJ2NhbGxiYWNrVXJsJyk7XG4gICAgY29uc3QgcmVkaXJlY3RUbyA9IGNhbGxiYWNrVXJsICYmIGNhbGxiYWNrVXJsICE9PSAnL2xvZ2luJyAmJiBjYWxsYmFja1VybCAhPT0gJy9zaWdudXAnXG4gICAgICA/IGNhbGxiYWNrVXJsXG4gICAgICA6ICcvZGFzaGJvYXJkJztcblxuICAgIGNvbnN0IHJlc3BvbnNlID0gTmV4dFJlc3BvbnNlLnJlZGlyZWN0KG5ldyBVUkwocmVkaXJlY3RUbywgcmVxdWVzdC51cmwpKTtcbiAgICAvLyBUcmFjayByZWRpcmVjdCBjb3VudFxuICAgIGNvbnN0IHJlZGlyZWN0Q291bnQgPSBwYXJzZUludChpc1JlZGlyZWN0TG9vcCB8fCAnMCcpICsgMTtcbiAgICByZXNwb25zZS5oZWFkZXJzLnNldCgneC1taWRkbGV3YXJlLXJlZGlyZWN0LWNvdW50JywgcmVkaXJlY3RDb3VudC50b1N0cmluZygpKTtcbiAgICByZXR1cm4gcmVzcG9uc2U7XG4gIH1cblxuICBjb25zdCByZXNwb25zZSA9IE5leHRSZXNwb25zZS5uZXh0KCk7XG4gIHJldHVybiBhZGRTZWN1cml0eUhlYWRlcnMocmVzcG9uc2UpO1xufVxuXG5leHBvcnQgY29uc3QgY29uZmlnID0ge1xuICBtYXRjaGVyOiBbXG4gICAgLypcbiAgICAgKiBNYXRjaCBhbGwgcmVxdWVzdCBwYXRocyBleGNlcHQgZm9yIHRoZSBvbmVzIHN0YXJ0aW5nIHdpdGg6XG4gICAgICogLSBfbmV4dC9zdGF0aWMgKHN0YXRpYyBmaWxlcylcbiAgICAgKiAtIF9uZXh0L2ltYWdlIChpbWFnZSBvcHRpbWl6YXRpb24gZmlsZXMpXG4gICAgICogLSBmYXZpY29uLmljbyAoZmF2aWNvbiBmaWxlKVxuICAgICAqIC0gcHVibGljIGZvbGRlciBmaWxlc1xuICAgICAqL1xuICAgICcvKCg/IV9uZXh0L3N0YXRpY3xfbmV4dC9pbWFnZXxmYXZpY29uLmljb3xwdWJsaWMvKS4qKScsXG4gIF0sXG59O1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBeUlBQSxPQUFBLENBQUFDLFVBQUEsR0FBQUEsVUFBQTtBQXpJQSxJQUFBQyxRQUFBO0FBQUE7QUFBQSxDQUFBQyxjQUFBLEdBQUFDLENBQUEsUUFBQUMsT0FBQTtBQUNBLElBQUFDLEtBQUE7QUFBQTtBQUFBLENBQUFILGNBQUEsR0FBQUMsQ0FBQSxRQUFBQyxPQUFBO0FBRUE7QUFDQSxJQUFNRSxlQUFlO0FBQUE7QUFBQSxDQUFBSixjQUFBLEdBQUFDLENBQUEsUUFBRyxDQUN0QixZQUFZLEVBQ1osVUFBVSxFQUNWLGFBQWEsRUFDYixRQUFRLEVBQ1IsZUFBZSxFQUNmLFdBQVcsRUFDWCxrQkFBa0IsRUFDbEIscUJBQXFCLEVBQ3JCLGlCQUFpQixFQUNqQixRQUFRLENBQ1Q7QUFFRDtBQUNBLElBQU1JLFdBQVc7QUFBQTtBQUFBLENBQUFMLGNBQUEsR0FBQUMsQ0FBQSxRQUFHLENBQ2xCLFFBQVEsQ0FDVDtBQUVEO0FBQ0EsSUFBTUssa0JBQWtCO0FBQUE7QUFBQSxDQUFBTixjQUFBLEdBQUFDLENBQUEsUUFBRyxDQUN6QixpQkFBaUIsRUFDakIsY0FBYyxFQUNkLG1CQUFtQixFQUNuQix3QkFBd0IsRUFDeEIsNkJBQTZCLEVBQzdCLHVCQUF1QixFQUN2QixzQkFBc0IsRUFDdEIsdUJBQXVCLEVBQ3ZCLHlCQUF5QixFQUN6QixxQkFBcUIsRUFDckIsWUFBWSxDQUNiO0FBRUQ7QUFDQSxJQUFNTSxjQUFjO0FBQUE7QUFBQSxDQUFBUCxjQUFBLEdBQUFDLENBQUEsUUFBRyxDQUNyQixZQUFZLENBQ2I7QUFFRDtBQUNBLElBQU1PLGVBQWU7QUFBQTtBQUFBLENBQUFSLGNBQUEsR0FBQUMsQ0FBQSxRQUFHLENBQ3RCLFdBQVcsRUFDWCxhQUFhLEVBQ2IsbUJBQW1CLEVBQ25CLHlCQUF5QixFQUN6QixjQUFjLEVBQ2QsaUJBQWlCLENBQ2xCO0FBRUQ7QUFDQSxJQUFNUSxjQUFjO0FBQUE7QUFBQSxDQUFBVCxjQUFBLEdBQUFDLENBQUEsUUFBRyxJQUFJUyxHQUFHLEVBQWdEO0FBRTlFLFNBQVNDLFdBQVdBLENBQUNDLE9BQW9CO0VBQUE7RUFBQVosY0FBQSxHQUFBYSxDQUFBO0VBQ3ZDLElBQU1DLFNBQVM7RUFBQTtFQUFBLENBQUFkLGNBQUEsR0FBQUMsQ0FBQSxRQUFHVyxPQUFPLENBQUNHLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGlCQUFpQixDQUFDO0VBQUM7RUFBQWhCLGNBQUEsR0FBQUMsQ0FBQTtFQUN6RCxJQUFJYSxTQUFTLEVBQUU7SUFBQTtJQUFBZCxjQUFBLEdBQUFpQixDQUFBO0lBQUFqQixjQUFBLEdBQUFDLENBQUE7SUFDYixPQUFPYSxTQUFTLENBQUNJLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQ0MsSUFBSSxFQUFFO0VBQ3ZDLENBQUM7RUFBQTtFQUFBO0lBQUFuQixjQUFBLEdBQUFpQixDQUFBO0VBQUE7RUFFRCxJQUFNRyxNQUFNO0VBQUE7RUFBQSxDQUFBcEIsY0FBQSxHQUFBQyxDQUFBLFFBQUdXLE9BQU8sQ0FBQ0csT0FBTyxDQUFDQyxHQUFHLENBQUMsV0FBVyxDQUFDO0VBQUM7RUFBQWhCLGNBQUEsR0FBQUMsQ0FBQTtFQUNoRCxJQUFJbUIsTUFBTSxFQUFFO0lBQUE7SUFBQXBCLGNBQUEsR0FBQWlCLENBQUE7SUFBQWpCLGNBQUEsR0FBQUMsQ0FBQTtJQUNWLE9BQU9tQixNQUFNO0VBQ2YsQ0FBQztFQUFBO0VBQUE7SUFBQXBCLGNBQUEsR0FBQWlCLENBQUE7RUFBQTtFQUFBakIsY0FBQSxHQUFBQyxDQUFBO0VBRUQsT0FBUSwyQkFBQUQsY0FBQSxHQUFBaUIsQ0FBQSxXQUFBTCxPQUFlLENBQUNTLEVBQUU7RUFBQTtFQUFBLENBQUFyQixjQUFBLEdBQUFpQixDQUFBLFdBQUksU0FBUztBQUN6QztBQUVBLFNBQVNLLGFBQWFBLENBQUNWLE9BQW9CLEVBQUVXLFFBQWlDLEVBQUVDLFdBQXlCO0VBQUE7RUFBQXhCLGNBQUEsR0FBQWEsQ0FBQTtFQUFBYixjQUFBLEdBQUFDLENBQUE7RUFBNUQsSUFBQXNCLFFBQUE7SUFBQTtJQUFBdkIsY0FBQSxHQUFBaUIsQ0FBQTtJQUFBakIsY0FBQSxHQUFBQyxDQUFBO0lBQUFzQixRQUFBLEdBQW1CLEVBQUUsR0FBRyxFQUFFLEdBQUcsSUFBSTtFQUFBO0VBQUE7RUFBQTtJQUFBdkIsY0FBQSxHQUFBaUIsQ0FBQTtFQUFBO0VBQUFqQixjQUFBLEdBQUFDLENBQUE7RUFBRSxJQUFBdUIsV0FBQTtJQUFBO0lBQUF4QixjQUFBLEdBQUFpQixDQUFBO0lBQUFqQixjQUFBLEdBQUFDLENBQUE7SUFBQXVCLFdBQUEsTUFBeUI7RUFBQTtFQUFBO0VBQUE7SUFBQXhCLGNBQUEsR0FBQWlCLENBQUE7RUFBQTtFQUN2RyxJQUFNUSxRQUFRO0VBQUE7RUFBQSxDQUFBekIsY0FBQSxHQUFBQyxDQUFBLFFBQUdVLFdBQVcsQ0FBQ0MsT0FBTyxDQUFDO0VBQ3JDLElBQU1jLEdBQUc7RUFBQTtFQUFBLENBQUExQixjQUFBLEdBQUFDLENBQUEsUUFBRzBCLElBQUksQ0FBQ0QsR0FBRyxFQUFFO0VBQ3RCLElBQU1FLFdBQVc7RUFBQTtFQUFBLENBQUE1QixjQUFBLEdBQUFDLENBQUEsUUFBR3lCLEdBQUcsR0FBR0gsUUFBUTtFQUVsQztFQUFBO0VBQUF2QixjQUFBLEdBQUFDLENBQUE7RUFDQTRCLEtBQUssQ0FBQ0MsSUFBSSxDQUFDckIsY0FBYyxDQUFDc0IsT0FBTyxFQUFFLENBQUMsQ0FBQ0MsT0FBTyxDQUFDLFVBQUNDLEVBQVk7SUFBQTtJQUFBakMsY0FBQSxHQUFBYSxDQUFBO1FBQVhxQixHQUFHO01BQUE7TUFBQSxDQUFBbEMsY0FBQSxHQUFBQyxDQUFBLFFBQUFnQyxFQUFBO01BQUVFLEtBQUs7TUFBQTtNQUFBLENBQUFuQyxjQUFBLEdBQUFDLENBQUEsUUFBQWdDLEVBQUE7SUFBQTtJQUFBakMsY0FBQSxHQUFBQyxDQUFBO0lBQ3ZELElBQUlrQyxLQUFLLENBQUNDLFNBQVMsR0FBR1IsV0FBVyxFQUFFO01BQUE7TUFBQTVCLGNBQUEsR0FBQWlCLENBQUE7TUFBQWpCLGNBQUEsR0FBQUMsQ0FBQTtNQUNqQ1EsY0FBYyxDQUFDNEIsTUFBTSxDQUFDSCxHQUFHLENBQUM7SUFDNUIsQ0FBQztJQUFBO0lBQUE7TUFBQWxDLGNBQUEsR0FBQWlCLENBQUE7SUFBQTtFQUNILENBQUMsQ0FBQztFQUVGO0VBQ0EsSUFBTXFCLEtBQUs7RUFBQTtFQUFBLENBQUF0QyxjQUFBLEdBQUFDLENBQUE7RUFBRztFQUFBLENBQUFELGNBQUEsR0FBQWlCLENBQUEsV0FBQVIsY0FBYyxDQUFDTyxHQUFHLENBQUNTLFFBQVEsQ0FBQztFQUFBO0VBQUEsQ0FBQXpCLGNBQUEsR0FBQWlCLENBQUEsV0FBSTtJQUFFc0IsS0FBSyxFQUFFLENBQUM7SUFBRUgsU0FBUyxFQUFFVixHQUFHLEdBQUdIO0VBQVEsQ0FBRTtFQUVyRjtFQUFBO0VBQUF2QixjQUFBLEdBQUFDLENBQUE7RUFDQSxJQUFJcUMsS0FBSyxDQUFDRixTQUFTLEdBQUdWLEdBQUcsRUFBRTtJQUFBO0lBQUExQixjQUFBLEdBQUFpQixDQUFBO0lBQUFqQixjQUFBLEdBQUFDLENBQUE7SUFDekJxQyxLQUFLLENBQUNDLEtBQUssR0FBRyxDQUFDO0lBQUM7SUFBQXZDLGNBQUEsR0FBQUMsQ0FBQTtJQUNoQnFDLEtBQUssQ0FBQ0YsU0FBUyxHQUFHVixHQUFHLEdBQUdILFFBQVE7RUFDbEMsQ0FBQztFQUFBO0VBQUE7SUFBQXZCLGNBQUEsR0FBQWlCLENBQUE7RUFBQTtFQUVEO0VBQUFqQixjQUFBLEdBQUFDLENBQUE7RUFDQXFDLEtBQUssQ0FBQ0MsS0FBSyxFQUFFO0VBQUM7RUFBQXZDLGNBQUEsR0FBQUMsQ0FBQTtFQUNkUSxjQUFjLENBQUMrQixHQUFHLENBQUNmLFFBQVEsRUFBRWEsS0FBSyxDQUFDO0VBQUM7RUFBQXRDLGNBQUEsR0FBQUMsQ0FBQTtFQUVwQyxPQUFPcUMsS0FBSyxDQUFDQyxLQUFLLEdBQUdmLFdBQVc7QUFDbEM7QUFFQSxTQUFTaUIsa0JBQWtCQSxDQUFDQyxRQUFzQjtFQUFBO0VBQUExQyxjQUFBLEdBQUFhLENBQUE7RUFBQWIsY0FBQSxHQUFBQyxDQUFBO0VBQ2hEO0VBQ0F5QyxRQUFRLENBQUMzQixPQUFPLENBQUN5QixHQUFHLENBQUMsaUJBQWlCLEVBQUUsTUFBTSxDQUFDO0VBQUM7RUFBQXhDLGNBQUEsR0FBQUMsQ0FBQTtFQUNoRHlDLFFBQVEsQ0FBQzNCLE9BQU8sQ0FBQ3lCLEdBQUcsQ0FBQyx3QkFBd0IsRUFBRSxTQUFTLENBQUM7RUFBQztFQUFBeEMsY0FBQSxHQUFBQyxDQUFBO0VBQzFEeUMsUUFBUSxDQUFDM0IsT0FBTyxDQUFDeUIsR0FBRyxDQUFDLGlCQUFpQixFQUFFLGlDQUFpQyxDQUFDO0VBQUM7RUFBQXhDLGNBQUEsR0FBQUMsQ0FBQTtFQUMzRXlDLFFBQVEsQ0FBQzNCLE9BQU8sQ0FBQ3lCLEdBQUcsQ0FBQyxrQkFBa0IsRUFBRSxlQUFlLENBQUM7RUFBQztFQUFBeEMsY0FBQSxHQUFBQyxDQUFBO0VBQzFEeUMsUUFBUSxDQUFDM0IsT0FBTyxDQUFDeUIsR0FBRyxDQUFDLG9CQUFvQixFQUFFLDBDQUEwQyxDQUFDO0VBRXRGO0VBQ0EsSUFBTUcsS0FBSztFQUFBO0VBQUEsQ0FBQTNDLGNBQUEsR0FBQUMsQ0FBQSxTQUFHMkMsTUFBTSxDQUFDQyxVQUFVLEVBQUU7RUFFakM7RUFDQSxJQUFNQyxHQUFHO0VBQUE7RUFBQSxDQUFBOUMsY0FBQSxHQUFBQyxDQUFBLFNBQUcsQ0FDVixvQkFBb0IsRUFDcEIsNEJBQUE4QyxNQUFBLENBQTRCSixLQUFLLHFHQUFrRyxFQUNuSSwrREFBK0QsRUFDL0QsbUNBQW1DLEVBQ25DLGlEQUFpRCxFQUNqRCwrRkFBK0YsRUFDL0YseUJBQXlCLEVBQ3pCLHdCQUF3QixFQUN4Qix3QkFBd0IsRUFDeEIsaUJBQWlCLEVBQ2pCLG9CQUFvQixDQUNyQixDQUFDSyxJQUFJLENBQUMsSUFBSSxDQUFDO0VBQUM7RUFBQWhELGNBQUEsR0FBQUMsQ0FBQTtFQUVieUMsUUFBUSxDQUFDM0IsT0FBTyxDQUFDeUIsR0FBRyxDQUFDLHlCQUF5QixFQUFFTSxHQUFHLENBQUM7RUFBQztFQUFBOUMsY0FBQSxHQUFBQyxDQUFBO0VBQ3JEeUMsUUFBUSxDQUFDM0IsT0FBTyxDQUFDeUIsR0FBRyxDQUFDLFNBQVMsRUFBRUcsS0FBSyxDQUFDO0VBRXRDO0VBQUE7RUFBQTNDLGNBQUEsR0FBQUMsQ0FBQTtFQUNBeUMsUUFBUSxDQUFDM0IsT0FBTyxDQUFDeUIsR0FBRyxDQUFDLDJCQUEyQixFQUFFLDhDQUE4QyxDQUFDO0VBRWpHO0VBQUE7RUFBQXhDLGNBQUEsR0FBQUMsQ0FBQTtFQUNBeUMsUUFBUSxDQUFDM0IsT0FBTyxDQUFDeUIsR0FBRyxDQUFDLDhCQUE4QixFQUFFLGNBQWMsQ0FBQztFQUFDO0VBQUF4QyxjQUFBLEdBQUFDLENBQUE7RUFDckV5QyxRQUFRLENBQUMzQixPQUFPLENBQUN5QixHQUFHLENBQUMsNEJBQTRCLEVBQUUsYUFBYSxDQUFDO0VBQUM7RUFBQXhDLGNBQUEsR0FBQUMsQ0FBQTtFQUNsRXlDLFFBQVEsQ0FBQzNCLE9BQU8sQ0FBQ3lCLEdBQUcsQ0FBQyw4QkFBOEIsRUFBRSxhQUFhLENBQUM7RUFBQztFQUFBeEMsY0FBQSxHQUFBQyxDQUFBO0VBRXBFLE9BQU95QyxRQUFRO0FBQ2pCO0FBRUEsU0FBc0I1QyxVQUFVQSxDQUFDYyxPQUFvQjtFQUFBO0VBQUFaLGNBQUEsR0FBQWEsQ0FBQTtFQUFBYixjQUFBLEdBQUFDLENBQUE7Ozs7Ozs7Ozs7Ozs7Ozs7VUFDM0NnRCxRQUFRLEdBQUtyQyxPQUFPLENBQUNzQyxPQUFPLENBQUFELFFBQXBCO1VBQXFCO1VBQUFqRCxjQUFBLEdBQUFDLENBQUE7VUFHL0JrRCx1QkFBdUIsR0FBRyxDQUM5QixLQUFLLEVBQUUsTUFBTSxFQUFFLE1BQU0sRUFBRSxNQUFNLEVBQUUsT0FBTyxFQUFFLE1BQU0sRUFBRSxNQUFNLEVBQUUsTUFBTSxFQUM5RCxPQUFPLEVBQUUsUUFBUSxFQUFFLE1BQU0sRUFBRSxNQUFNLEVBQUUsT0FBTyxFQUFFLE9BQU8sRUFBRSxNQUFNLEVBQUUsT0FBTyxDQUNyRTtVQUFDO1VBQUFuRCxjQUFBLEdBQUFDLENBQUE7VUFHSW1ELGVBQWUsR0FBRyxDQUN0QixNQUFNLEVBQUUsTUFBTSxFQUFFLEtBQUssRUFBRSxTQUFTLEVBQUUsTUFBTSxFQUFFLE1BQU0sRUFBRSxTQUFTLEVBQzNELFNBQVMsRUFBRSxNQUFNLEVBQUUsT0FBTyxFQUFFLE1BQU0sRUFBRSxNQUFNLEVBQUUsTUFBTSxFQUFFLE1BQU0sRUFDMUQsT0FBTyxFQUFFLE9BQU8sRUFBRSxNQUFNLEVBQUUsTUFBTSxFQUFFLEtBQUssRUFBRSxNQUFNLEVBQUUsTUFBTSxDQUN4RDtVQUFDO1VBQUFwRCxjQUFBLEdBQUFDLENBQUE7VUFHSW9ELGlCQUFpQixHQUFHRCxlQUFlLENBQUNFLElBQUksQ0FBQyxVQUFBQyxPQUFPO1lBQUE7WUFBQXZELGNBQUEsR0FBQWEsQ0FBQTtZQUFBYixjQUFBLEdBQUFDLENBQUE7WUFDcEQsT0FBQWdELFFBQVEsQ0FBQ08sV0FBVyxFQUFFLENBQUNDLFFBQVEsQ0FBQ0YsT0FBTyxDQUFDQyxXQUFXLEVBQUUsQ0FBQztVQUF0RCxDQUFzRCxDQUN2RDtVQUFDO1VBQUF4RCxjQUFBLEdBQUFDLENBQUE7VUFFRixJQUFJb0QsaUJBQWlCLEVBQUU7WUFBQTtZQUFBckQsY0FBQSxHQUFBaUIsQ0FBQTtZQUFBakIsY0FBQSxHQUFBQyxDQUFBO1lBQ3JCeUQsT0FBTyxDQUFDQyxJQUFJLENBQUMscUNBQUFaLE1BQUEsQ0FBcUNFLFFBQVEsQ0FBRSxDQUFDO1lBQUM7WUFBQWpELGNBQUEsR0FBQUMsQ0FBQTtZQUM5RCxzQkFBT0YsUUFBQSxDQUFBNkQsWUFBWSxDQUFDQyxJQUFJLENBQ3RCO2NBQUVDLEtBQUssRUFBRTtZQUFlLENBQUUsRUFDMUI7Y0FBRUMsTUFBTSxFQUFFO1lBQUcsQ0FBRSxDQUNoQjtVQUNILENBQUM7VUFBQTtVQUFBO1lBQUEvRCxjQUFBLEdBQUFpQixDQUFBO1VBQUE7VUFBQWpCLGNBQUEsR0FBQUMsQ0FBQTtVQUdLK0QsY0FBYztVQUFHO1VBQUEsQ0FBQWhFLGNBQUEsR0FBQWlCLENBQUEsV0FBQWdDLFFBQVEsQ0FBQ2dCLFVBQVUsQ0FBQyxRQUFRLENBQUM7VUFBQTtVQUFBLENBQUFqRSxjQUFBLEdBQUFpQixDQUFBLFdBQUlnQyxRQUFRLENBQUNnQixVQUFVLENBQUMsWUFBWSxDQUFDO1VBQUM7VUFBQWpFLGNBQUEsR0FBQUMsQ0FBQTtVQUNwRmlFLFNBQVMsR0FBR2pCLFFBQVEsS0FBSyxjQUFjO1VBQUM7VUFBQWpELGNBQUEsR0FBQUMsQ0FBQTtVQUN4Q2tFLGVBQWUsR0FBR2hCLHVCQUF1QixDQUFDRyxJQUFJLENBQUMsVUFBQWMsR0FBRztZQUFBO1lBQUFwRSxjQUFBLEdBQUFhLENBQUE7WUFBQWIsY0FBQSxHQUFBQyxDQUFBO1lBQ3RELE9BQUFnRCxRQUFRLENBQUNPLFdBQVcsRUFBRSxDQUFDYSxRQUFRLENBQUNELEdBQUcsQ0FBQ1osV0FBVyxFQUFFLENBQUM7VUFBbEQsQ0FBa0QsQ0FDbkQ7VUFBQztVQUFBeEQsY0FBQSxHQUFBQyxDQUFBO1VBRUY7VUFBSTtVQUFBLENBQUFELGNBQUEsR0FBQWlCLENBQUEsV0FBQStDLGNBQWM7VUFBQTtVQUFBLENBQUFoRSxjQUFBLEdBQUFpQixDQUFBLFdBQUlpRCxTQUFTO1VBQUs7VUFBQSxDQUFBbEUsY0FBQSxHQUFBaUIsQ0FBQSxXQUFBa0QsZUFBZTtVQUFBO1VBQUEsQ0FBQW5FLGNBQUEsR0FBQWlCLENBQUEsV0FBSWdDLFFBQVEsQ0FBQ2dCLFVBQVUsQ0FBQyxVQUFVLENBQUMsQ0FBQyxFQUFFO1lBQUE7WUFBQWpFLGNBQUEsR0FBQWlCLENBQUE7WUFBQWpCLGNBQUEsR0FBQUMsQ0FBQTtZQUN2RixzQkFBT0YsUUFBQSxDQUFBNkQsWUFBWSxDQUFDVSxJQUFJLEVBQUU7VUFDNUIsQ0FBQztVQUFBO1VBQUE7WUFBQXRFLGNBQUEsR0FBQWlCLENBQUE7VUFBQTtVQUFBakIsY0FBQSxHQUFBQyxDQUFBO1VBR2EscUJBQU0sSUFBQUUsS0FBQSxDQUFBb0UsUUFBUSxFQUFDO1lBQUVDLEdBQUcsRUFBRTVELE9BQU87WUFBRTZELE1BQU0sRUFBRUMsT0FBTyxDQUFDQyxHQUFHLENBQUNDO1VBQWUsQ0FBRSxDQUFDOzs7OztVQUE3RUMsS0FBSyxHQUFHNUMsRUFBQSxDQUFBNkMsSUFBQSxFQUFxRTtVQUFBO1VBQUE5RSxjQUFBLEdBQUFDLENBQUE7VUFHN0U4RSxnQkFBZ0IsR0FBRzNFLGVBQWUsQ0FBQ2tELElBQUksQ0FBQyxVQUFBMEIsS0FBSztZQUFBO1lBQUFoRixjQUFBLEdBQUFhLENBQUE7WUFBQWIsY0FBQSxHQUFBQyxDQUFBO1lBQUksT0FBQWdELFFBQVEsQ0FBQ2dCLFVBQVUsQ0FBQ2UsS0FBSyxDQUFDO1VBQTFCLENBQTBCLENBQUM7VUFBQztVQUFBaEYsY0FBQSxHQUFBQyxDQUFBO1VBQzdFZ0YsWUFBWSxHQUFHNUUsV0FBVyxDQUFDaUQsSUFBSSxDQUFDLFVBQUEwQixLQUFLO1lBQUE7WUFBQWhGLGNBQUEsR0FBQWEsQ0FBQTtZQUFBYixjQUFBLEdBQUFDLENBQUE7WUFBSSxPQUFBZ0QsUUFBUSxDQUFDZ0IsVUFBVSxDQUFDZSxLQUFLLENBQUM7VUFBMUIsQ0FBMEIsQ0FBQztVQUFDO1VBQUFoRixjQUFBLEdBQUFDLENBQUE7VUFDckVpRixtQkFBbUIsR0FBRzVFLGtCQUFrQixDQUFDZ0QsSUFBSSxDQUFDLFVBQUEwQixLQUFLO1lBQUE7WUFBQWhGLGNBQUEsR0FBQWEsQ0FBQTtZQUFBYixjQUFBLEdBQUFDLENBQUE7WUFBSSxPQUFBZ0QsUUFBUSxDQUFDZ0IsVUFBVSxDQUFDZSxLQUFLLENBQUM7VUFBMUIsQ0FBMEIsQ0FBQztVQUFDO1VBQUFoRixjQUFBLEdBQUFDLENBQUE7VUFDbkZrRixlQUFlLEdBQUc1RSxjQUFjLENBQUMrQyxJQUFJLENBQUMsVUFBQTBCLEtBQUs7WUFBQTtZQUFBaEYsY0FBQSxHQUFBYSxDQUFBO1lBQUFiLGNBQUEsR0FBQUMsQ0FBQTtZQUFJLE9BQUFnRCxRQUFRLENBQUNnQixVQUFVLENBQUNlLEtBQUssQ0FBQztVQUExQixDQUEwQixDQUFDO1VBQUM7VUFBQWhGLGNBQUEsR0FBQUMsQ0FBQTtVQUMzRW1GLGdCQUFnQixHQUFHNUUsZUFBZSxDQUFDOEMsSUFBSSxDQUFDLFVBQUEwQixLQUFLO1lBQUE7WUFBQWhGLGNBQUEsR0FBQWEsQ0FBQTtZQUFBYixjQUFBLEdBQUFDLENBQUE7WUFBSSxPQUFBZ0QsUUFBUSxDQUFDZ0IsVUFBVSxDQUFDZSxLQUFLLENBQUM7VUFBMUIsQ0FBMEIsQ0FBQztVQUVsRjtVQUFBO1VBQUFoRixjQUFBLEdBQUFDLENBQUE7VUFDQTtVQUFJO1VBQUEsQ0FBQUQsY0FBQSxHQUFBaUIsQ0FBQSxXQUFBZ0MsUUFBUSxDQUFDZ0IsVUFBVSxDQUFDLE9BQU8sQ0FBQztVQUFBO1VBQUEsQ0FBQWpFLGNBQUEsR0FBQWlCLENBQUEsV0FBSWdFLFlBQVk7VUFBQTtVQUFBLENBQUFqRixjQUFBLEdBQUFpQixDQUFBLFdBQUlrRSxlQUFlLEdBQUU7WUFBQTtZQUFBbkYsY0FBQSxHQUFBaUIsQ0FBQTtZQUFBakIsY0FBQSxHQUFBQyxDQUFBO1lBRTdEdUIsV0FBVztZQUFJO1lBQUEsQ0FBQXhCLGNBQUEsR0FBQWlCLENBQUEsV0FBQWdFLFlBQVk7WUFBQTtZQUFBLENBQUFqRixjQUFBLEdBQUFpQixDQUFBLFdBQUlrRSxlQUFlO1lBQUE7WUFBQSxDQUFBbkYsY0FBQSxHQUFBaUIsQ0FBQSxXQUFJLEVBQUU7WUFBQTtZQUFBLENBQUFqQixjQUFBLEdBQUFpQixDQUFBLFdBQUcsR0FBRztZQUFDO1lBQUFqQixjQUFBLEdBQUFDLENBQUE7WUFDM0RzQixRQUFRO1lBQUk7WUFBQSxDQUFBdkIsY0FBQSxHQUFBaUIsQ0FBQSxXQUFBZ0UsWUFBWTtZQUFBO1lBQUEsQ0FBQWpGLGNBQUEsR0FBQWlCLENBQUEsV0FBSWtFLGVBQWU7WUFBQTtZQUFBLENBQUFuRixjQUFBLEdBQUFpQixDQUFBLFdBQUksRUFBRSxHQUFHLEVBQUUsR0FBRyxJQUFJO1lBQUE7WUFBQSxDQUFBakIsY0FBQSxHQUFBaUIsQ0FBQSxXQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsSUFBSTtZQUFDO1lBQUFqQixjQUFBLEdBQUFDLENBQUE7WUFFckYsSUFBSXFCLGFBQWEsQ0FBQ1YsT0FBTyxFQUFFVyxRQUFRLEVBQUVDLFdBQVcsQ0FBQyxFQUFFO2NBQUE7Y0FBQXhCLGNBQUEsR0FBQWlCLENBQUE7Y0FBQWpCLGNBQUEsR0FBQUMsQ0FBQTtjQUNqRHlELE9BQU8sQ0FBQ0MsSUFBSSxDQUFDLDJCQUFBWixNQUFBLENBQTJCRSxRQUFRLENBQUUsRUFBRTtnQkFDbEQ1QixFQUFFLEVBQUVWLFdBQVcsQ0FBQ0MsT0FBTyxDQUFDO2dCQUN4QnlFLFNBQVMsRUFBRXpFLE9BQU8sQ0FBQ0csT0FBTyxDQUFDQyxHQUFHLENBQUMsWUFBWSxDQUFDO2dCQUM1Q3NFLE9BQU87Z0JBQUU7Z0JBQUEsQ0FBQXRGLGNBQUEsR0FBQWlCLENBQUEsV0FBQWdFLFlBQVk7Z0JBQUE7Z0JBQUEsQ0FBQWpGLGNBQUEsR0FBQWlCLENBQUEsV0FBSWtFLGVBQWU7ZUFDekMsQ0FBQztjQUFDO2NBQUFuRixjQUFBLEdBQUFDLENBQUE7Y0FFSCxzQkFBT0YsUUFBQSxDQUFBNkQsWUFBWSxDQUFDQyxJQUFJLENBQ3RCO2dCQUFFQyxLQUFLLEVBQUU7Y0FBNEMsQ0FBRSxFQUN2RDtnQkFBRUMsTUFBTSxFQUFFO2NBQUcsQ0FBRSxDQUNoQjtZQUNILENBQUM7WUFBQTtZQUFBO2NBQUEvRCxjQUFBLEdBQUFpQixDQUFBO1lBQUE7VUFDSCxDQUFDO1VBQUE7VUFBQTtZQUFBakIsY0FBQSxHQUFBaUIsQ0FBQTtVQUFBO1VBRUQ7VUFBQWpCLGNBQUEsR0FBQUMsQ0FBQTtVQUNBLElBQUlnRixZQUFZLEVBQUU7WUFBQTtZQUFBakYsY0FBQSxHQUFBaUIsQ0FBQTtZQUFBakIsY0FBQSxHQUFBQyxDQUFBO1lBQ2hCLElBQUksQ0FBQzRFLEtBQUssRUFBRTtjQUFBO2NBQUE3RSxjQUFBLEdBQUFpQixDQUFBO2NBQUFqQixjQUFBLEdBQUFDLENBQUE7Y0FDSnNGLFFBQVEsR0FBRyxJQUFJQyxHQUFHLENBQUMsUUFBUSxFQUFFNUUsT0FBTyxDQUFDNkUsR0FBRyxDQUFDO2NBQUM7Y0FBQXpGLGNBQUEsR0FBQUMsQ0FBQTtjQUMxQ3lGLFFBQVEsR0FBR3pDLFFBQVE7Y0FBSTtjQUFBLENBQUFqRCxjQUFBLEdBQUFpQixDQUFBLFdBQUFMLE9BQU8sQ0FBQ3NDLE9BQU8sQ0FBQ3lDLE1BQU07Y0FBQTtjQUFBLENBQUEzRixjQUFBLEdBQUFpQixDQUFBLFdBQUksRUFBRSxFQUFDO2NBQUM7Y0FBQWpCLGNBQUEsR0FBQUMsQ0FBQTtjQUMzRHNGLFFBQVEsQ0FBQ0ssWUFBWSxDQUFDcEQsR0FBRyxDQUFDLGFBQWEsRUFBRWtELFFBQVEsQ0FBQztjQUFDO2NBQUExRixjQUFBLEdBQUFDLENBQUE7Y0FDbkQsc0JBQU9GLFFBQUEsQ0FBQTZELFlBQVksQ0FBQ2lDLFFBQVEsQ0FBQ04sUUFBUSxDQUFDO1lBQ3hDLENBQUM7WUFBQTtZQUFBO2NBQUF2RixjQUFBLEdBQUFpQixDQUFBO1lBQUE7WUFFRDtZQUFBakIsY0FBQSxHQUFBQyxDQUFBO1lBQ0E7WUFBSTtZQUFBLENBQUFELGNBQUEsR0FBQWlCLENBQUEsWUFBQzRELEtBQUssQ0FBQ2lCLElBQUk7WUFBQTtZQUFBLENBQUE5RixjQUFBLEdBQUFpQixDQUFBLFdBQUk0RCxLQUFLLENBQUNpQixJQUFJLEtBQUssT0FBTyxHQUFFO2NBQUE7Y0FBQTlGLGNBQUEsR0FBQWlCLENBQUE7Y0FBQWpCLGNBQUEsR0FBQUMsQ0FBQTtjQUN6Q3lELE9BQU8sQ0FBQ0MsSUFBSSxDQUFDLG1EQUFBWixNQUFBLENBQW1ERSxRQUFRLENBQUUsRUFBRTtnQkFDMUU4QyxNQUFNLEVBQUVsQixLQUFLLENBQUNtQixHQUFHO2dCQUNqQkMsS0FBSyxFQUFFcEIsS0FBSyxDQUFDb0IsS0FBSztnQkFDbEJILElBQUksRUFBRWpCLEtBQUssQ0FBQ2lCO2VBQ2IsQ0FBQztjQUFDO2NBQUE5RixjQUFBLEdBQUFDLENBQUE7Y0FDSCxzQkFBT0YsUUFBQSxDQUFBNkQsWUFBWSxDQUFDQyxJQUFJLENBQ3RCO2dCQUFFQyxLQUFLLEVBQUU7Y0FBdUIsQ0FBRSxFQUNsQztnQkFBRUMsTUFBTSxFQUFFO2NBQUcsQ0FBRSxDQUNoQjtZQUNILENBQUM7WUFBQTtZQUFBO2NBQUEvRCxjQUFBLEdBQUFpQixDQUFBO1lBQUE7VUFDSCxDQUFDO1VBQUE7VUFBQTtZQUFBakIsY0FBQSxHQUFBaUIsQ0FBQTtVQUFBO1VBRUQ7VUFBQWpCLGNBQUEsR0FBQUMsQ0FBQTtVQUNBO1VBQUk7VUFBQSxDQUFBRCxjQUFBLEdBQUFpQixDQUFBLFdBQUE4RCxnQkFBZ0I7VUFBQTtVQUFBLENBQUEvRSxjQUFBLEdBQUFpQixDQUFBLFdBQUksQ0FBQzRELEtBQUssR0FBRTtZQUFBO1lBQUE3RSxjQUFBLEdBQUFpQixDQUFBO1lBQUFqQixjQUFBLEdBQUFDLENBQUE7WUFFeEJpRyxjQUFjLEdBQUd0RixPQUFPLENBQUNHLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDZCQUE2QixDQUFDO1lBQUM7WUFBQWhCLGNBQUEsR0FBQUMsQ0FBQTtZQUMxRTtZQUFJO1lBQUEsQ0FBQUQsY0FBQSxHQUFBaUIsQ0FBQSxXQUFBaUYsY0FBYztZQUFBO1lBQUEsQ0FBQWxHLGNBQUEsR0FBQWlCLENBQUEsV0FBSWtGLFFBQVEsQ0FBQ0QsY0FBYyxDQUFDLEdBQUcsQ0FBQyxHQUFFO2NBQUE7Y0FBQWxHLGNBQUEsR0FBQWlCLENBQUE7Y0FBQWpCLGNBQUEsR0FBQUMsQ0FBQTtjQUNsRHlELE9BQU8sQ0FBQ0ksS0FBSyxDQUFDLHdDQUF3QyxDQUFDO2NBQUM7Y0FBQTlELGNBQUEsR0FBQUMsQ0FBQTtjQUN4RCxzQkFBT0YsUUFBQSxDQUFBNkQsWUFBWSxDQUFDQyxJQUFJLENBQ3RCO2dCQUFFQyxLQUFLLEVBQUU7Y0FBdUMsQ0FBRSxFQUNsRDtnQkFBRUMsTUFBTSxFQUFFO2NBQUcsQ0FBRSxDQUNoQjtZQUNILENBQUM7WUFBQTtZQUFBO2NBQUEvRCxjQUFBLEdBQUFpQixDQUFBO1lBQUE7WUFBQWpCLGNBQUEsR0FBQUMsQ0FBQTtZQUVLc0YsUUFBUSxHQUFHLElBQUlDLEdBQUcsQ0FBQyxRQUFRLEVBQUU1RSxPQUFPLENBQUM2RSxHQUFHLENBQUM7WUFBQztZQUFBekYsY0FBQSxHQUFBQyxDQUFBO1lBRTFDeUYsUUFBUSxHQUFHekMsUUFBUTtZQUFJO1lBQUEsQ0FBQWpELGNBQUEsR0FBQWlCLENBQUEsV0FBQUwsT0FBTyxDQUFDc0MsT0FBTyxDQUFDeUMsTUFBTTtZQUFBO1lBQUEsQ0FBQTNGLGNBQUEsR0FBQWlCLENBQUEsV0FBSSxFQUFFLEVBQUM7WUFBQztZQUFBakIsY0FBQSxHQUFBQyxDQUFBO1lBQzNEc0YsUUFBUSxDQUFDSyxZQUFZLENBQUNwRCxHQUFHLENBQUMsYUFBYSxFQUFFa0QsUUFBUSxDQUFDO1lBQUM7WUFBQTFGLGNBQUEsR0FBQUMsQ0FBQTtZQUU3Q21HLFVBQUEsR0FBV3JHLFFBQUEsQ0FBQTZELFlBQVksQ0FBQ2lDLFFBQVEsQ0FBQ04sUUFBUSxDQUFDO1lBQUM7WUFBQXZGLGNBQUEsR0FBQUMsQ0FBQTtZQUUzQ29HLGFBQWEsR0FBR0YsUUFBUTtZQUFDO1lBQUEsQ0FBQW5HLGNBQUEsR0FBQWlCLENBQUEsV0FBQWlGLGNBQWM7WUFBQTtZQUFBLENBQUFsRyxjQUFBLEdBQUFpQixDQUFBLFdBQUksR0FBRyxFQUFDLEdBQUcsQ0FBQztZQUFDO1lBQUFqQixjQUFBLEdBQUFDLENBQUE7WUFDMURtRyxVQUFRLENBQUNyRixPQUFPLENBQUN5QixHQUFHLENBQUMsNkJBQTZCLEVBQUU2RCxhQUFhLENBQUNDLFFBQVEsRUFBRSxDQUFDO1lBQUM7WUFBQXRHLGNBQUEsR0FBQUMsQ0FBQTtZQUM5RSxzQkFBT21HLFVBQVE7VUFDakIsQ0FBQztVQUFBO1VBQUE7WUFBQXBHLGNBQUEsR0FBQWlCLENBQUE7VUFBQTtVQUVEO1VBQUFqQixjQUFBLEdBQUFDLENBQUE7VUFDQSxJQUFJa0YsZUFBZSxFQUFFO1lBQUE7WUFBQW5GLGNBQUEsR0FBQWlCLENBQUE7WUFBQWpCLGNBQUEsR0FBQUMsQ0FBQTtZQUNuQixJQUFJLENBQUM0RSxLQUFLLEVBQUU7Y0FBQTtjQUFBN0UsY0FBQSxHQUFBaUIsQ0FBQTtjQUFBakIsY0FBQSxHQUFBQyxDQUFBO2NBQ1Ysc0JBQU9GLFFBQUEsQ0FBQTZELFlBQVksQ0FBQ0MsSUFBSSxDQUN0QjtnQkFBRUMsS0FBSyxFQUFFO2NBQXlCLENBQUUsRUFDcEM7Z0JBQUVDLE1BQU0sRUFBRTtjQUFHLENBQUUsQ0FDaEI7WUFDSCxDQUFDO1lBQUE7WUFBQTtjQUFBL0QsY0FBQSxHQUFBaUIsQ0FBQTtZQUFBO1lBRUQ7WUFBQWpCLGNBQUEsR0FBQUMsQ0FBQTtZQUNBO1lBQUk7WUFBQSxDQUFBRCxjQUFBLEdBQUFpQixDQUFBLFlBQUM0RCxLQUFLLENBQUNpQixJQUFJO1lBQUE7WUFBQSxDQUFBOUYsY0FBQSxHQUFBaUIsQ0FBQSxXQUFJNEQsS0FBSyxDQUFDaUIsSUFBSSxLQUFLLE9BQU8sR0FBRTtjQUFBO2NBQUE5RixjQUFBLEdBQUFpQixDQUFBO2NBQUFqQixjQUFBLEdBQUFDLENBQUE7Y0FDekN5RCxPQUFPLENBQUNDLElBQUksQ0FBQyxpREFBQVosTUFBQSxDQUFpREUsUUFBUSxDQUFFLEVBQUU7Z0JBQ3hFOEMsTUFBTSxFQUFFbEIsS0FBSyxDQUFDbUIsR0FBRztnQkFDakJDLEtBQUssRUFBRXBCLEtBQUssQ0FBQ29CLEtBQUs7Z0JBQ2xCSCxJQUFJLEVBQUVqQixLQUFLLENBQUNpQjtlQUNiLENBQUM7Y0FBQztjQUFBOUYsY0FBQSxHQUFBQyxDQUFBO2NBQ0gsc0JBQU9GLFFBQUEsQ0FBQTZELFlBQVksQ0FBQ0MsSUFBSSxDQUN0QjtnQkFBRUMsS0FBSyxFQUFFO2NBQXVCLENBQUUsRUFDbEM7Z0JBQUVDLE1BQU0sRUFBRTtjQUFHLENBQUUsQ0FDaEI7WUFDSCxDQUFDO1lBQUE7WUFBQTtjQUFBL0QsY0FBQSxHQUFBaUIsQ0FBQTtZQUFBO1VBQ0gsQ0FBQztVQUFBO1VBQUE7WUFBQWpCLGNBQUEsR0FBQWlCLENBQUE7VUFBQTtVQUVEO1VBQUFqQixjQUFBLEdBQUFDLENBQUE7VUFDQTtVQUFJO1VBQUEsQ0FBQUQsY0FBQSxHQUFBaUIsQ0FBQSxXQUFBaUUsbUJBQW1CO1VBQUE7VUFBQSxDQUFBbEYsY0FBQSxHQUFBaUIsQ0FBQSxXQUFJLENBQUM0RCxLQUFLO1VBQUE7VUFBQSxDQUFBN0UsY0FBQSxHQUFBaUIsQ0FBQSxXQUFJLENBQUNtRSxnQkFBZ0IsR0FBRTtZQUFBO1lBQUFwRixjQUFBLEdBQUFpQixDQUFBO1lBQUFqQixjQUFBLEdBQUFDLENBQUE7WUFDdEQsc0JBQU9GLFFBQUEsQ0FBQTZELFlBQVksQ0FBQ0MsSUFBSSxDQUN0QjtjQUFFQyxLQUFLLEVBQUU7WUFBeUIsQ0FBRSxFQUNwQztjQUFFQyxNQUFNLEVBQUU7WUFBRyxDQUFFLENBQ2hCO1VBQ0gsQ0FBQztVQUFBO1VBQUE7WUFBQS9ELGNBQUEsR0FBQWlCLENBQUE7VUFBQTtVQUVEO1VBQUFqQixjQUFBLEdBQUFDLENBQUE7VUFDQTtVQUFJO1VBQUEsQ0FBQUQsY0FBQSxHQUFBaUIsQ0FBQSxXQUFBNEQsS0FBSztVQUFLO1VBQUEsQ0FBQTdFLGNBQUEsR0FBQWlCLENBQUEsV0FBQWdDLFFBQVEsS0FBSyxRQUFRO1VBQUE7VUFBQSxDQUFBakQsY0FBQSxHQUFBaUIsQ0FBQSxXQUFJZ0MsUUFBUSxLQUFLLFNBQVMsRUFBQyxFQUFFO1lBQUE7WUFBQWpELGNBQUEsR0FBQWlCLENBQUE7WUFBQWpCLGNBQUEsR0FBQUMsQ0FBQTtZQUV4RGlHLGNBQWMsR0FBR3RGLE9BQU8sQ0FBQ0csT0FBTyxDQUFDQyxHQUFHLENBQUMsNkJBQTZCLENBQUM7WUFBQztZQUFBaEIsY0FBQSxHQUFBQyxDQUFBO1lBQzFFO1lBQUk7WUFBQSxDQUFBRCxjQUFBLEdBQUFpQixDQUFBLFdBQUFpRixjQUFjO1lBQUE7WUFBQSxDQUFBbEcsY0FBQSxHQUFBaUIsQ0FBQSxXQUFJa0YsUUFBUSxDQUFDRCxjQUFjLENBQUMsR0FBRyxDQUFDLEdBQUU7Y0FBQTtjQUFBbEcsY0FBQSxHQUFBaUIsQ0FBQTtjQUFBakIsY0FBQSxHQUFBQyxDQUFBO2NBQ2xEeUQsT0FBTyxDQUFDSSxLQUFLLENBQUMsMkRBQTJELENBQUM7Y0FBQztjQUFBOUQsY0FBQSxHQUFBQyxDQUFBO2NBRXJFc0csVUFBQSxHQUFXeEcsUUFBQSxDQUFBNkQsWUFBWSxDQUFDVSxJQUFJLEVBQUU7Y0FBQztjQUFBdEUsY0FBQSxHQUFBQyxDQUFBO2NBQ3JDLHNCQUFPd0Msa0JBQWtCLENBQUM4RCxVQUFRLENBQUM7WUFDckMsQ0FBQztZQUFBO1lBQUE7Y0FBQXZHLGNBQUEsR0FBQWlCLENBQUE7WUFBQTtZQUFBakIsY0FBQSxHQUFBQyxDQUFBO1lBR0t1RyxXQUFXLEdBQUc1RixPQUFPLENBQUNzQyxPQUFPLENBQUMwQyxZQUFZLENBQUM1RSxHQUFHLENBQUMsYUFBYSxDQUFDO1lBQUM7WUFBQWhCLGNBQUEsR0FBQUMsQ0FBQTtZQUM5RHdHLFVBQVU7WUFBRztZQUFBLENBQUF6RyxjQUFBLEdBQUFpQixDQUFBLFdBQUF1RixXQUFXO1lBQUE7WUFBQSxDQUFBeEcsY0FBQSxHQUFBaUIsQ0FBQSxXQUFJdUYsV0FBVyxLQUFLLFFBQVE7WUFBQTtZQUFBLENBQUF4RyxjQUFBLEdBQUFpQixDQUFBLFdBQUl1RixXQUFXLEtBQUssU0FBUztZQUFBO1lBQUEsQ0FBQXhHLGNBQUEsR0FBQWlCLENBQUEsV0FDbkZ1RixXQUFXO1lBQUE7WUFBQSxDQUFBeEcsY0FBQSxHQUFBaUIsQ0FBQSxXQUNYLFlBQVk7WUFBQztZQUFBakIsY0FBQSxHQUFBQyxDQUFBO1lBRVh5RyxVQUFBLEdBQVczRyxRQUFBLENBQUE2RCxZQUFZLENBQUNpQyxRQUFRLENBQUMsSUFBSUwsR0FBRyxDQUFDaUIsVUFBVSxFQUFFN0YsT0FBTyxDQUFDNkUsR0FBRyxDQUFDLENBQUM7WUFBQztZQUFBekYsY0FBQSxHQUFBQyxDQUFBO1lBRW5Fb0csYUFBYSxHQUFHRixRQUFRO1lBQUM7WUFBQSxDQUFBbkcsY0FBQSxHQUFBaUIsQ0FBQSxXQUFBaUYsY0FBYztZQUFBO1lBQUEsQ0FBQWxHLGNBQUEsR0FBQWlCLENBQUEsV0FBSSxHQUFHLEVBQUMsR0FBRyxDQUFDO1lBQUM7WUFBQWpCLGNBQUEsR0FBQUMsQ0FBQTtZQUMxRHlHLFVBQVEsQ0FBQzNGLE9BQU8sQ0FBQ3lCLEdBQUcsQ0FBQyw2QkFBNkIsRUFBRTZELGFBQWEsQ0FBQ0MsUUFBUSxFQUFFLENBQUM7WUFBQztZQUFBdEcsY0FBQSxHQUFBQyxDQUFBO1lBQzlFLHNCQUFPeUcsVUFBUTtVQUNqQixDQUFDO1VBQUE7VUFBQTtZQUFBMUcsY0FBQSxHQUFBaUIsQ0FBQTtVQUFBO1VBQUFqQixjQUFBLEdBQUFDLENBQUE7VUFFS3lDLFFBQVEsR0FBRzNDLFFBQUEsQ0FBQTZELFlBQVksQ0FBQ1UsSUFBSSxFQUFFO1VBQUM7VUFBQXRFLGNBQUEsR0FBQUMsQ0FBQTtVQUNyQyxzQkFBT3dDLGtCQUFrQixDQUFDQyxRQUFRLENBQUM7Ozs7O0FBQ3BDO0FBQUExQyxjQUFBLEdBQUFDLENBQUE7QUFFWUosT0FBQSxDQUFBOEcsTUFBTSxHQUFHO0VBQ3BCQyxPQUFPLEVBQUU7RUFDUDs7Ozs7OztFQU9BLHVEQUF1RDtDQUUxRCIsImlnbm9yZUxpc3QiOltdfQ==