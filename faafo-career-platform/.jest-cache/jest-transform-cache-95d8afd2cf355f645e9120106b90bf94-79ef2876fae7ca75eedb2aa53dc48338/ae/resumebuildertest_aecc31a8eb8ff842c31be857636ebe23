9cb94f3a8acadb2e51ee44c0adeee766
"use strict";
/**
 * Resume Builder API Tests
 *
 * Tests for the resume builder API endpoints including CRUD operations,
 * authentication, validation, and error handling.
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// Mock dependencies
jest.mock('next-auth/next');
jest.mock('@/lib/prisma', function () { return ({
    user: {
        findUnique: jest.fn(),
    },
    resume: {
        findMany: jest.fn(),
        findFirst: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
    },
}); });
jest.mock('@/lib/errorReporting');
jest.mock('@/lib/logger');
jest.mock('@/lib/errorTracking');
jest.mock('@/lib/csrf');
jest.mock('@/lib/rateLimit');
var server_1 = require("next/server");
var next_1 = require("next-auth/next");
var route_1 = require("@/app/api/resume-builder/route");
var route_2 = require("@/app/api/resume-builder/[id]/route");
var prisma_1 = __importDefault(require("@/lib/prisma"));
var mockGetServerSession = next_1.getServerSession;
var mockPrisma = prisma_1.default;
describe('Resume Builder API', function () {
    beforeEach(function () {
        jest.clearAllMocks();
    });
    describe('GET /api/resume-builder', function () {
        it('should return 401 when user is not authenticated', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockGetServerSession.mockResolvedValue(null);
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder');
                        return [4 /*yield*/, (0, route_1.GET)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(401);
                        expect(data.error).toBe('Not authenticated');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should return user resumes when authenticated', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockSession, mockUser, mockResumes, request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSession = {
                            user: { email: '<EMAIL>' }
                        };
                        mockUser = { id: 'user-1' };
                        mockResumes = [
                            {
                                id: 'resume-1',
                                title: 'Software Engineer Resume',
                                template: 'modern',
                                isPublic: false,
                                lastExported: null,
                                exportCount: 0,
                                createdAt: new Date(),
                                updatedAt: new Date(),
                            }
                        ];
                        mockGetServerSession.mockResolvedValue(mockSession);
                        mockPrisma.user.findUnique.mockResolvedValue(mockUser);
                        mockPrisma.resume.findMany.mockResolvedValue(mockResumes);
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder');
                        return [4 /*yield*/, (0, route_1.GET)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(200);
                        expect(data.success).toBe(true);
                        expect(data.data).toEqual(mockResumes);
                        expect(mockPrisma.resume.findMany).toHaveBeenCalledWith({
                            where: {
                                userId: 'user-1',
                                isActive: true
                            },
                            select: {
                                id: true,
                                title: true,
                                template: true,
                                isPublic: true,
                                lastExported: true,
                                exportCount: true,
                                createdAt: true,
                                updatedAt: true
                            },
                            orderBy: { updatedAt: 'desc' }
                        });
                        return [2 /*return*/];
                }
            });
        }); });
        it('should return 404 when user not found', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockSession, request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSession = {
                            user: { email: '<EMAIL>' }
                        };
                        mockGetServerSession.mockResolvedValue(mockSession);
                        mockPrisma.user.findUnique.mockResolvedValue(null);
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder');
                        return [4 /*yield*/, (0, route_1.GET)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(404);
                        expect(data.error).toBe('User not found');
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('POST /api/resume-builder', function () {
        it('should create a new resume when valid data is provided', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockSession, mockUser, mockResumeData, mockCreatedResume, request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSession = {
                            user: { email: '<EMAIL>' }
                        };
                        mockUser = { id: 'user-1' };
                        mockResumeData = {
                            title: 'My Resume',
                            personalInfo: {
                                firstName: 'John',
                                lastName: 'Doe',
                                email: '<EMAIL>'
                            },
                            summary: 'Experienced developer',
                            experience: [],
                            education: [],
                            skills: [],
                            template: 'modern',
                            isPublic: false
                        };
                        mockCreatedResume = __assign(__assign({ id: 'resume-1', userId: 'user-1' }, mockResumeData), { createdAt: new Date(), updatedAt: new Date() });
                        mockGetServerSession.mockResolvedValue(mockSession);
                        mockPrisma.user.findUnique.mockResolvedValue(mockUser);
                        mockPrisma.resume.create.mockResolvedValue(mockCreatedResume);
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder', {
                            method: 'POST',
                            body: JSON.stringify(mockResumeData),
                            headers: { 'Content-Type': 'application/json' }
                        });
                        return [4 /*yield*/, (0, route_1.POST)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(201);
                        expect(data.success).toBe(true);
                        expect(data.data).toEqual(mockCreatedResume);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should return 400 when validation fails', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockSession, mockUser, invalidResumeData, request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSession = {
                            user: { email: '<EMAIL>' }
                        };
                        mockUser = { id: 'user-1' };
                        invalidResumeData = {
                            // Missing required title
                            personalInfo: {
                                firstName: 'John',
                                lastName: 'Doe',
                                email: 'invalid-email' // Invalid email format
                            }
                        };
                        mockGetServerSession.mockResolvedValue(mockSession);
                        mockPrisma.user.findUnique.mockResolvedValue(mockUser);
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder', {
                            method: 'POST',
                            body: JSON.stringify(invalidResumeData),
                            headers: { 'Content-Type': 'application/json' }
                        });
                        return [4 /*yield*/, (0, route_1.POST)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(400);
                        expect(data.success).toBe(false);
                        expect(data.error).toBe('Validation failed');
                        expect(data.details).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('GET /api/resume-builder/[id]', function () {
        it('should return specific resume when user owns it', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockSession, mockUser, mockResume, request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSession = {
                            user: { email: '<EMAIL>' }
                        };
                        mockUser = { id: 'user-1' };
                        mockResume = {
                            id: 'resume-1',
                            userId: 'user-1',
                            title: 'My Resume',
                            personalInfo: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
                            template: 'modern',
                            isActive: true
                        };
                        mockGetServerSession.mockResolvedValue(mockSession);
                        mockPrisma.user.findUnique.mockResolvedValue(mockUser);
                        mockPrisma.resume.findFirst.mockResolvedValue(mockResume);
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder/resume-1');
                        return [4 /*yield*/, (0, route_2.GET)(request, { params: Promise.resolve({ id: 'resume-1' }) })];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(200);
                        expect(data.success).toBe(true);
                        expect(data.data).toEqual(mockResume);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should return 404 when resume not found or not owned by user', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockSession, mockUser, request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSession = {
                            user: { email: '<EMAIL>' }
                        };
                        mockUser = { id: 'user-1' };
                        mockGetServerSession.mockResolvedValue(mockSession);
                        mockPrisma.user.findUnique.mockResolvedValue(mockUser);
                        mockPrisma.resume.findFirst.mockResolvedValue(null);
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder/resume-1');
                        return [4 /*yield*/, (0, route_2.GET)(request, { params: Promise.resolve({ id: 'resume-1' }) })];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(404);
                        expect(data.success).toBe(false);
                        expect(data.error).toBe('Resume not found');
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('PUT /api/resume-builder/[id]', function () {
        it('should update resume when user owns it', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockSession, mockUser, mockExistingResume, updateData, mockUpdatedResume, request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSession = {
                            user: { email: '<EMAIL>' }
                        };
                        mockUser = { id: 'user-1' };
                        mockExistingResume = {
                            id: 'resume-1',
                            userId: 'user-1',
                            title: 'Old Title',
                            isActive: true
                        };
                        updateData = {
                            title: 'Updated Title',
                            summary: 'Updated summary'
                        };
                        mockUpdatedResume = __assign(__assign(__assign({}, mockExistingResume), updateData), { updatedAt: new Date() });
                        mockGetServerSession.mockResolvedValue(mockSession);
                        mockPrisma.user.findUnique.mockResolvedValue(mockUser);
                        mockPrisma.resume.findFirst.mockResolvedValue(mockExistingResume);
                        mockPrisma.resume.update.mockResolvedValue(mockUpdatedResume);
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder/resume-1', {
                            method: 'PUT',
                            body: JSON.stringify(updateData),
                            headers: { 'Content-Type': 'application/json' }
                        });
                        return [4 /*yield*/, (0, route_2.PUT)(request, { params: Promise.resolve({ id: 'resume-1' }) })];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(200);
                        expect(data.success).toBe(true);
                        expect(data.data).toEqual(mockUpdatedResume);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('DELETE /api/resume-builder/[id]', function () {
        it('should soft delete resume when user owns it', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockSession, mockUser, mockExistingResume, request, response, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockSession = {
                            user: { email: '<EMAIL>' }
                        };
                        mockUser = { id: 'user-1' };
                        mockExistingResume = {
                            id: 'resume-1',
                            userId: 'user-1',
                            title: 'My Resume',
                            isActive: true
                        };
                        mockGetServerSession.mockResolvedValue(mockSession);
                        mockPrisma.user.findUnique.mockResolvedValue(mockUser);
                        mockPrisma.resume.findFirst.mockResolvedValue(mockExistingResume);
                        mockPrisma.resume.update.mockResolvedValue(__assign(__assign({}, mockExistingResume), { isActive: false }));
                        request = new server_1.NextRequest('http://localhost:3000/api/resume-builder/resume-1', {
                            method: 'DELETE'
                        });
                        return [4 /*yield*/, (0, route_2.DELETE)(request, { params: Promise.resolve({ id: 'resume-1' }) })];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        expect(response.status).toBe(200);
                        expect(data.success).toBe(true);
                        expect(data.message).toBe('Resume deleted successfully');
                        expect(mockPrisma.resume.update).toHaveBeenCalledWith({
                            where: { id: 'resume-1' },
                            data: {
                                isActive: false,
                                updatedAt: expect.any(Date)
                            }
                        });
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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