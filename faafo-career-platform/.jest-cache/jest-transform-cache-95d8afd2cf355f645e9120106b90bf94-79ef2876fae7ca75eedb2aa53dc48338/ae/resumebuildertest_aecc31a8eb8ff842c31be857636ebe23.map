{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/api/resume-builder.test.ts", "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQH,oBAAoB;AACpB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAC5B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,cAAM,OAAA,CAAC;IAC/B,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;KACtB;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;QACnB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;QACpB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;KAClB;CACF,CAAC,EAV8B,CAU9B,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;AAClC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAC1B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;AACjC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACxB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAxB7B,sCAA0C;AAC1C,uCAAkD;AAClD,wDAA2D;AAC3D,6DAAoF;AACpF,wDAAkC;AAsBlC,IAAM,oBAAoB,GAAG,uBAAgE,CAAC;AAC9F,IAAM,UAAU,GAAG,gBAAoC,CAAC;AAExD,QAAQ,CAAC,oBAAoB,EAAE;IAC7B,UAAU,CAAC;QACT,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE;QAClC,EAAE,CAAC,kDAAkD,EAAE;;;;;wBACrD,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAEvC,OAAO,GAAG,IAAI,oBAAW,CAAC,0CAA0C,CAAC,CAAC;wBAC3D,qBAAM,IAAA,WAAG,EAAC,OAAO,CAAC,EAAA;;wBAA7B,QAAQ,GAAG,SAAkB;wBACtB,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA5B,IAAI,GAAG,SAAqB;wBAElC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAClC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;;;;aAC9C,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE;;;;;wBAC5C,WAAW,GAAG;4BAClB,IAAI,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE;yBACpC,CAAC;wBACI,QAAQ,GAAG,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC;wBAC5B,WAAW,GAAG;4BAClB;gCACE,EAAE,EAAE,UAAU;gCACd,KAAK,EAAE,0BAA0B;gCACjC,QAAQ,EAAE,QAAQ;gCAClB,QAAQ,EAAE,KAAK;gCACf,YAAY,EAAE,IAAI;gCAClB,WAAW,EAAE,CAAC;gCACd,SAAS,EAAE,IAAI,IAAI,EAAE;gCACrB,SAAS,EAAE,IAAI,IAAI,EAAE;6BACtB;yBACF,CAAC;wBAEF,oBAAoB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;wBACpD,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBACvD,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;wBAEpD,OAAO,GAAG,IAAI,oBAAW,CAAC,0CAA0C,CAAC,CAAC;wBAC3D,qBAAM,IAAA,WAAG,EAAC,OAAO,CAAC,EAAA;;wBAA7B,QAAQ,GAAG,SAAkB;wBACtB,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA5B,IAAI,GAAG,SAAqB;wBAElC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAClC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAChC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;wBACvC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC;4BACtD,KAAK,EAAE;gCACL,MAAM,EAAE,QAAQ;gCAChB,QAAQ,EAAE,IAAI;6BACf;4BACD,MAAM,EAAE;gCACN,EAAE,EAAE,IAAI;gCACR,KAAK,EAAE,IAAI;gCACX,QAAQ,EAAE,IAAI;gCACd,QAAQ,EAAE,IAAI;gCACd,YAAY,EAAE,IAAI;gCAClB,WAAW,EAAE,IAAI;gCACjB,SAAS,EAAE,IAAI;gCACf,SAAS,EAAE,IAAI;6BAChB;4BACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;yBAC/B,CAAC,CAAC;;;;aACJ,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE;;;;;wBACpC,WAAW,GAAG;4BAClB,IAAI,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE;yBACpC,CAAC;wBAEF,oBAAoB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;wBACpD,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAE7C,OAAO,GAAG,IAAI,oBAAW,CAAC,0CAA0C,CAAC,CAAC;wBAC3D,qBAAM,IAAA,WAAG,EAAC,OAAO,CAAC,EAAA;;wBAA7B,QAAQ,GAAG,SAAkB;wBACtB,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA5B,IAAI,GAAG,SAAqB;wBAElC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAClC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;;;;aAC3C,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE;QACnC,EAAE,CAAC,wDAAwD,EAAE;;;;;wBACrD,WAAW,GAAG;4BAClB,IAAI,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE;yBACpC,CAAC;wBACI,QAAQ,GAAG,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC;wBAC5B,cAAc,GAAG;4BACrB,KAAK,EAAE,WAAW;4BAClB,YAAY,EAAE;gCACZ,SAAS,EAAE,MAAM;gCACjB,QAAQ,EAAE,KAAK;gCACf,KAAK,EAAE,kBAAkB;6BAC1B;4BACD,OAAO,EAAE,uBAAuB;4BAChC,UAAU,EAAE,EAAE;4BACd,SAAS,EAAE,EAAE;4BACb,MAAM,EAAE,EAAE;4BACV,QAAQ,EAAE,QAAQ;4BAClB,QAAQ,EAAE,KAAK;yBAChB,CAAC;wBACI,iBAAiB,uBACrB,EAAE,EAAE,UAAU,EACd,MAAM,EAAE,QAAQ,IACb,cAAc,KACjB,SAAS,EAAE,IAAI,IAAI,EAAE,EACrB,SAAS,EAAE,IAAI,IAAI,EAAE,GACtB,CAAC;wBAEF,oBAAoB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;wBACpD,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBACvD,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;wBAExD,OAAO,GAAG,IAAI,oBAAW,CAAC,0CAA0C,EAAE;4BAC1E,MAAM,EAAE,MAAM;4BACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;4BACpC,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;yBAChD,CAAC,CAAC;wBAEc,qBAAM,IAAA,YAAI,EAAC,OAAO,CAAC,EAAA;;wBAA9B,QAAQ,GAAG,SAAmB;wBACvB,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA5B,IAAI,GAAG,SAAqB;wBAElC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAClC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAChC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;;;;aAC9C,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE;;;;;wBACtC,WAAW,GAAG;4BAClB,IAAI,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE;yBACpC,CAAC;wBACI,QAAQ,GAAG,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC;wBAC5B,iBAAiB,GAAG;4BACxB,yBAAyB;4BACzB,YAAY,EAAE;gCACZ,SAAS,EAAE,MAAM;gCACjB,QAAQ,EAAE,KAAK;gCACf,KAAK,EAAE,eAAe,CAAC,uBAAuB;6BAC/C;yBACF,CAAC;wBAEF,oBAAoB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;wBACpD,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBAEjD,OAAO,GAAG,IAAI,oBAAW,CAAC,0CAA0C,EAAE;4BAC1E,MAAM,EAAE,MAAM;4BACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;4BACvC,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;yBAChD,CAAC,CAAC;wBAEc,qBAAM,IAAA,YAAI,EAAC,OAAO,CAAC,EAAA;;wBAA9B,QAAQ,GAAG,SAAmB;wBACvB,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA5B,IAAI,GAAG,SAAqB;wBAElC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAClC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACjC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;wBAC7C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aACpC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE;QACvC,EAAE,CAAC,iDAAiD,EAAE;;;;;wBAC9C,WAAW,GAAG;4BAClB,IAAI,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE;yBACpC,CAAC;wBACI,QAAQ,GAAG,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC;wBAC5B,UAAU,GAAG;4BACjB,EAAE,EAAE,UAAU;4BACd,MAAM,EAAE,QAAQ;4BAChB,KAAK,EAAE,WAAW;4BAClB,YAAY,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,kBAAkB,EAAE;4BAC/E,QAAQ,EAAE,QAAQ;4BAClB,QAAQ,EAAE,IAAI;yBACf,CAAC;wBAEF,oBAAoB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;wBACpD,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBACvD,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;wBAEpD,OAAO,GAAG,IAAI,oBAAW,CAAC,mDAAmD,CAAC,CAAC;wBACpE,qBAAM,IAAA,WAAS,EAAC,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,EAAA;;wBAApF,QAAQ,GAAG,SAAyE;wBAC7E,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA5B,IAAI,GAAG,SAAqB;wBAElC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAClC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAChC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;;;;aACvC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE;;;;;wBAC3D,WAAW,GAAG;4BAClB,IAAI,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE;yBACpC,CAAC;wBACI,QAAQ,GAAG,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC;wBAElC,oBAAoB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;wBACpD,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBACvD,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAE9C,OAAO,GAAG,IAAI,oBAAW,CAAC,mDAAmD,CAAC,CAAC;wBACpE,qBAAM,IAAA,WAAS,EAAC,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,EAAA;;wBAApF,QAAQ,GAAG,SAAyE;wBAC7E,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA5B,IAAI,GAAG,SAAqB;wBAElC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAClC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACjC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;;;;aAC7C,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE;QACvC,EAAE,CAAC,wCAAwC,EAAE;;;;;wBACrC,WAAW,GAAG;4BAClB,IAAI,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE;yBACpC,CAAC;wBACI,QAAQ,GAAG,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC;wBAC5B,kBAAkB,GAAG;4BACzB,EAAE,EAAE,UAAU;4BACd,MAAM,EAAE,QAAQ;4BAChB,KAAK,EAAE,WAAW;4BAClB,QAAQ,EAAE,IAAI;yBACf,CAAC;wBACI,UAAU,GAAG;4BACjB,KAAK,EAAE,eAAe;4BACtB,OAAO,EAAE,iBAAiB;yBAC3B,CAAC;wBACI,iBAAiB,kCAClB,kBAAkB,GAClB,UAAU,KACb,SAAS,EAAE,IAAI,IAAI,EAAE,GACtB,CAAC;wBAEF,oBAAoB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;wBACpD,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBACvD,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;wBAClE,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;wBAExD,OAAO,GAAG,IAAI,oBAAW,CAAC,mDAAmD,EAAE;4BACnF,MAAM,EAAE,KAAK;4BACb,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;4BAChC,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;yBAChD,CAAC,CAAC;wBAEc,qBAAM,IAAA,WAAG,EAAC,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,EAAA;;wBAA9E,QAAQ,GAAG,SAAmE;wBACvE,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA5B,IAAI,GAAG,SAAqB;wBAElC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAClC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAChC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;;;;aAC9C,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE;QAC1C,EAAE,CAAC,6CAA6C,EAAE;;;;;wBAC1C,WAAW,GAAG;4BAClB,IAAI,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE;yBACpC,CAAC;wBACI,QAAQ,GAAG,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC;wBAC5B,kBAAkB,GAAG;4BACzB,EAAE,EAAE,UAAU;4BACd,MAAM,EAAE,QAAQ;4BAChB,KAAK,EAAE,WAAW;4BAClB,QAAQ,EAAE,IAAI;yBACf,CAAC;wBAEF,oBAAoB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;wBACpD,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBACvD,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;wBAClE,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,uBAAM,kBAAkB,KAAE,QAAQ,EAAE,KAAK,IAAG,CAAC;wBAEjF,OAAO,GAAG,IAAI,oBAAW,CAAC,mDAAmD,EAAE;4BACnF,MAAM,EAAE,QAAQ;yBACjB,CAAC,CAAC;wBAEc,qBAAM,IAAA,cAAM,EAAC,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,EAAA;;wBAAjF,QAAQ,GAAG,SAAsE;wBAC1E,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA5B,IAAI,GAAG,SAAqB;wBAElC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAClC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAChC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;wBACzD,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;4BACpD,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;4BACzB,IAAI,EAAE;gCACJ,QAAQ,EAAE,KAAK;gCACf,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;6BAC5B;yBACF,CAAC,CAAC;;;;aACJ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/api/resume-builder.test.ts"], "sourcesContent": ["/**\n * Resume Builder API Tests\n * \n * Tests for the resume builder API endpoints including CRUD operations,\n * authentication, validation, and error handling.\n */\n\nimport { NextRequest } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { GET, POST } from '@/app/api/resume-builder/route';\nimport { GET as GetResume, PUT, DELETE } from '@/app/api/resume-builder/[id]/route';\nimport prisma from '@/lib/prisma';\n\n// Mock dependencies\njest.mock('next-auth/next');\njest.mock('@/lib/prisma', () => ({\n  user: {\n    findUnique: jest.fn(),\n  },\n  resume: {\n    findMany: jest.fn(),\n    findFirst: jest.fn(),\n    create: jest.fn(),\n    update: jest.fn(),\n  },\n}));\n\njest.mock('@/lib/errorReporting');\njest.mock('@/lib/logger');\njest.mock('@/lib/errorTracking');\njest.mock('@/lib/csrf');\njest.mock('@/lib/rateLimit');\n\nconst mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;\nconst mockPrisma = prisma as jest.Mocked<typeof prisma>;\n\ndescribe('Resume Builder API', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('GET /api/resume-builder', () => {\n    it('should return 401 when user is not authenticated', async () => {\n      mockGetServerSession.mockResolvedValue(null);\n\n      const request = new NextRequest('http://localhost:3000/api/resume-builder');\n      const response = await GET(request);\n      const data = await response.json();\n\n      expect(response.status).toBe(401);\n      expect(data.error).toBe('Not authenticated');\n    });\n\n    it('should return user resumes when authenticated', async () => {\n      const mockSession = {\n        user: { email: '<EMAIL>' }\n      };\n      const mockUser = { id: 'user-1' };\n      const mockResumes = [\n        {\n          id: 'resume-1',\n          title: 'Software Engineer Resume',\n          template: 'modern',\n          isPublic: false,\n          lastExported: null,\n          exportCount: 0,\n          createdAt: new Date(),\n          updatedAt: new Date(),\n        }\n      ];\n\n      mockGetServerSession.mockResolvedValue(mockSession);\n      mockPrisma.user.findUnique.mockResolvedValue(mockUser);\n      mockPrisma.resume.findMany.mockResolvedValue(mockResumes);\n\n      const request = new NextRequest('http://localhost:3000/api/resume-builder');\n      const response = await GET(request);\n      const data = await response.json();\n\n      expect(response.status).toBe(200);\n      expect(data.success).toBe(true);\n      expect(data.data).toEqual(mockResumes);\n      expect(mockPrisma.resume.findMany).toHaveBeenCalledWith({\n        where: { \n          userId: 'user-1',\n          isActive: true\n        },\n        select: {\n          id: true,\n          title: true,\n          template: true,\n          isPublic: true,\n          lastExported: true,\n          exportCount: true,\n          createdAt: true,\n          updatedAt: true\n        },\n        orderBy: { updatedAt: 'desc' }\n      });\n    });\n\n    it('should return 404 when user not found', async () => {\n      const mockSession = {\n        user: { email: '<EMAIL>' }\n      };\n\n      mockGetServerSession.mockResolvedValue(mockSession);\n      mockPrisma.user.findUnique.mockResolvedValue(null);\n\n      const request = new NextRequest('http://localhost:3000/api/resume-builder');\n      const response = await GET(request);\n      const data = await response.json();\n\n      expect(response.status).toBe(404);\n      expect(data.error).toBe('User not found');\n    });\n  });\n\n  describe('POST /api/resume-builder', () => {\n    it('should create a new resume when valid data is provided', async () => {\n      const mockSession = {\n        user: { email: '<EMAIL>' }\n      };\n      const mockUser = { id: 'user-1' };\n      const mockResumeData = {\n        title: 'My Resume',\n        personalInfo: {\n          firstName: 'John',\n          lastName: 'Doe',\n          email: '<EMAIL>'\n        },\n        summary: 'Experienced developer',\n        experience: [],\n        education: [],\n        skills: [],\n        template: 'modern',\n        isPublic: false\n      };\n      const mockCreatedResume = {\n        id: 'resume-1',\n        userId: 'user-1',\n        ...mockResumeData,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n\n      mockGetServerSession.mockResolvedValue(mockSession);\n      mockPrisma.user.findUnique.mockResolvedValue(mockUser);\n      mockPrisma.resume.create.mockResolvedValue(mockCreatedResume);\n\n      const request = new NextRequest('http://localhost:3000/api/resume-builder', {\n        method: 'POST',\n        body: JSON.stringify(mockResumeData),\n        headers: { 'Content-Type': 'application/json' }\n      });\n\n      const response = await POST(request);\n      const data = await response.json();\n\n      expect(response.status).toBe(201);\n      expect(data.success).toBe(true);\n      expect(data.data).toEqual(mockCreatedResume);\n    });\n\n    it('should return 400 when validation fails', async () => {\n      const mockSession = {\n        user: { email: '<EMAIL>' }\n      };\n      const mockUser = { id: 'user-1' };\n      const invalidResumeData = {\n        // Missing required title\n        personalInfo: {\n          firstName: 'John',\n          lastName: 'Doe',\n          email: 'invalid-email' // Invalid email format\n        }\n      };\n\n      mockGetServerSession.mockResolvedValue(mockSession);\n      mockPrisma.user.findUnique.mockResolvedValue(mockUser);\n\n      const request = new NextRequest('http://localhost:3000/api/resume-builder', {\n        method: 'POST',\n        body: JSON.stringify(invalidResumeData),\n        headers: { 'Content-Type': 'application/json' }\n      });\n\n      const response = await POST(request);\n      const data = await response.json();\n\n      expect(response.status).toBe(400);\n      expect(data.success).toBe(false);\n      expect(data.error).toBe('Validation failed');\n      expect(data.details).toBeDefined();\n    });\n  });\n\n  describe('GET /api/resume-builder/[id]', () => {\n    it('should return specific resume when user owns it', async () => {\n      const mockSession = {\n        user: { email: '<EMAIL>' }\n      };\n      const mockUser = { id: 'user-1' };\n      const mockResume = {\n        id: 'resume-1',\n        userId: 'user-1',\n        title: 'My Resume',\n        personalInfo: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },\n        template: 'modern',\n        isActive: true\n      };\n\n      mockGetServerSession.mockResolvedValue(mockSession);\n      mockPrisma.user.findUnique.mockResolvedValue(mockUser);\n      mockPrisma.resume.findFirst.mockResolvedValue(mockResume);\n\n      const request = new NextRequest('http://localhost:3000/api/resume-builder/resume-1');\n      const response = await GetResume(request, { params: Promise.resolve({ id: 'resume-1' }) });\n      const data = await response.json();\n\n      expect(response.status).toBe(200);\n      expect(data.success).toBe(true);\n      expect(data.data).toEqual(mockResume);\n    });\n\n    it('should return 404 when resume not found or not owned by user', async () => {\n      const mockSession = {\n        user: { email: '<EMAIL>' }\n      };\n      const mockUser = { id: 'user-1' };\n\n      mockGetServerSession.mockResolvedValue(mockSession);\n      mockPrisma.user.findUnique.mockResolvedValue(mockUser);\n      mockPrisma.resume.findFirst.mockResolvedValue(null);\n\n      const request = new NextRequest('http://localhost:3000/api/resume-builder/resume-1');\n      const response = await GetResume(request, { params: Promise.resolve({ id: 'resume-1' }) });\n      const data = await response.json();\n\n      expect(response.status).toBe(404);\n      expect(data.success).toBe(false);\n      expect(data.error).toBe('Resume not found');\n    });\n  });\n\n  describe('PUT /api/resume-builder/[id]', () => {\n    it('should update resume when user owns it', async () => {\n      const mockSession = {\n        user: { email: '<EMAIL>' }\n      };\n      const mockUser = { id: 'user-1' };\n      const mockExistingResume = {\n        id: 'resume-1',\n        userId: 'user-1',\n        title: 'Old Title',\n        isActive: true\n      };\n      const updateData = {\n        title: 'Updated Title',\n        summary: 'Updated summary'\n      };\n      const mockUpdatedResume = {\n        ...mockExistingResume,\n        ...updateData,\n        updatedAt: new Date()\n      };\n\n      mockGetServerSession.mockResolvedValue(mockSession);\n      mockPrisma.user.findUnique.mockResolvedValue(mockUser);\n      mockPrisma.resume.findFirst.mockResolvedValue(mockExistingResume);\n      mockPrisma.resume.update.mockResolvedValue(mockUpdatedResume);\n\n      const request = new NextRequest('http://localhost:3000/api/resume-builder/resume-1', {\n        method: 'PUT',\n        body: JSON.stringify(updateData),\n        headers: { 'Content-Type': 'application/json' }\n      });\n\n      const response = await PUT(request, { params: Promise.resolve({ id: 'resume-1' }) });\n      const data = await response.json();\n\n      expect(response.status).toBe(200);\n      expect(data.success).toBe(true);\n      expect(data.data).toEqual(mockUpdatedResume);\n    });\n  });\n\n  describe('DELETE /api/resume-builder/[id]', () => {\n    it('should soft delete resume when user owns it', async () => {\n      const mockSession = {\n        user: { email: '<EMAIL>' }\n      };\n      const mockUser = { id: 'user-1' };\n      const mockExistingResume = {\n        id: 'resume-1',\n        userId: 'user-1',\n        title: 'My Resume',\n        isActive: true\n      };\n\n      mockGetServerSession.mockResolvedValue(mockSession);\n      mockPrisma.user.findUnique.mockResolvedValue(mockUser);\n      mockPrisma.resume.findFirst.mockResolvedValue(mockExistingResume);\n      mockPrisma.resume.update.mockResolvedValue({ ...mockExistingResume, isActive: false });\n\n      const request = new NextRequest('http://localhost:3000/api/resume-builder/resume-1', {\n        method: 'DELETE'\n      });\n\n      const response = await DELETE(request, { params: Promise.resolve({ id: 'resume-1' }) });\n      const data = await response.json();\n\n      expect(response.status).toBe(200);\n      expect(data.success).toBe(true);\n      expect(data.message).toBe('Resume deleted successfully');\n      expect(mockPrisma.resume.update).toHaveBeenCalledWith({\n        where: { id: 'resume-1' },\n        data: { \n          isActive: false,\n          updatedAt: expect.any(Date)\n        }\n      });\n    });\n  });\n});\n"], "version": 3}