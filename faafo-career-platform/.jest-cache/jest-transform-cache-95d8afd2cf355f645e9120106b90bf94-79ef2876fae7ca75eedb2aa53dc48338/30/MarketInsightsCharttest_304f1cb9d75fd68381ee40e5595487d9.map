{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/skills/visualizations/MarketInsightsChart.test.tsx", "mappings": ";;;;;;AAKA,2BAA2B;AAC3B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,cAAM,OAAA,CAAC;IAC3B,QAAQ,EAAE,UAAC,EAAiB;YAAf,QAAQ,cAAA;QAAY,OAAA,+CAAiB,WAAW,YAAE,QAAQ,GAAO;IAA7C,CAA6C;IAC9E,GAAG,EAAE,UAAC,EAAsB;YAApB,OAAO,aAAA,EAAE,IAAI,UAAA;QAAY,OAAA,+CAAiB,KAAK,cAAW,OAAO,eAAa,IAAI,GAAI;IAA7D,CAA6D;IAC9F,KAAK,EAAE,UAAC,EAAgB;YAAd,OAAO,aAAA;QAAY,OAAA,+CAAiB,QAAQ,cAAW,OAAO,GAAI;IAA/C,CAA+C;IAC5E,KAAK,EAAE,cAAM,OAAA,+CAAiB,QAAQ,GAAG,EAA5B,CAA4B;IACzC,aAAa,EAAE,cAAM,OAAA,+CAAiB,gBAAgB,GAAG,EAApC,CAAoC;IACzD,OAAO,EAAE,cAAM,OAAA,+CAAiB,SAAS,GAAG,EAA7B,CAA6B;IAC5C,mBAAmB,EAAE,UAAC,EAAiB;YAAf,QAAQ,cAAA;QAAY,OAAA,+CAAiB,sBAAsB,YAAE,QAAQ,GAAO;IAAxD,CAAwD;IACpG,MAAM,EAAE,cAAM,OAAA,+CAAiB,QAAQ,GAAG,EAA5B,CAA4B;CAC3C,CAAC,EAT0B,CAS1B,CAAC,CAAC;AAfJ,gDAA0B;AAC1B,gDAAwD;AACxD,qCAAmC;AACnC,+GAAyF;AAczF,IAAM,cAAc,GAAG;IACrB;QACE,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,EAAE;QACV,MAAM,EAAE,EAAE;QACV,aAAa,EAAE,KAAK;QACpB,MAAM,EAAE,IAAI;QACZ,UAAU,EAAE,CAAC;QACb,WAAW,EAAE,CAAC;QACd,QAAQ,EAAE,aAAa;KACxB;IACD;QACE,KAAK,EAAE,OAAO;QACd,MAAM,EAAE,EAAE;QACV,MAAM,EAAE,EAAE;QACV,aAAa,EAAE,KAAK;QACpB,MAAM,EAAE,IAAI;QACZ,UAAU,EAAE,CAAC;QACb,WAAW,EAAE,EAAE;QACf,QAAQ,EAAE,UAAU;KACrB;IACD;QACE,KAAK,EAAE,QAAQ;QACf,MAAM,EAAE,EAAE;QACV,MAAM,EAAE,EAAE;QACV,aAAa,EAAE,MAAM;QACrB,MAAM,EAAE,IAAI;QACZ,UAAU,EAAE,CAAC;QACb,WAAW,EAAE,CAAC;QACd,QAAQ,EAAE,aAAa;KACxB;CACF,CAAC;AAEF,QAAQ,CAAC,qBAAqB,EAAE;IAC9B,QAAQ,CAAC,qBAAqB,EAAE;QAC9B,EAAE,CAAC,kCAAkC,EAAE;YACrC,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,GAAI,CAAC,CAAC;YAEtD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAChE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,yCAAyC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACxF,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE;YACpD,IAAM,WAAW,GAAG,wBAAwB,CAAC;YAC7C,IAAM,iBAAiB,GAAG,2BAA2B,CAAC;YAEtD,IAAA,cAAM,EACJ,uBAAC,6BAAmB,IAClB,IAAI,EAAE,cAAc,EACpB,KAAK,EAAE,WAAW,EAClB,WAAW,EAAE,iBAAiB,GAC9B,CACH,CAAC;YAEF,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC1D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE;YACxD,IAAA,cAAM,EACJ,uBAAC,6BAAmB,IAClB,IAAI,EAAE,cAAc,EACpB,KAAK,EAAC,YAAY,EAClB,WAAW,EAAC,EAAE,GACd,CACH,CAAC;YAEF,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC3D,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;QAC7E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE;QAC3B,EAAE,CAAC,6CAA6C,EAAE;YAChD,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,GAAI,CAAC,CAAC;YAEtD,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACvE,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC5D,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACjE,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACzD,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACzD,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE;YACxD,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,GAAI,CAAC,CAAC;YAEtD,IAAM,IAAI,GAAG,cAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAE7B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAExD,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE;YAC1C,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,GAAI,CAAC,CAAC;YAEtD,IAAM,KAAK,GAAG,cAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE;YACjD,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,EAAE,UAAU,EAAE,IAAI,GAAI,CAAC,CAAC;YACxE,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE;YACtD,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,EAAE,UAAU,EAAE,KAAK,GAAI,CAAC,CAAC;YACzE,MAAM,CAAC,cAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE;QAC3B,EAAE,CAAC,qDAAqD,EAAE;YACxD,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,GAAI,CAAC,CAAC;YAEtD,MAAM,CAAC,cAAM,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,yBAAyB;QAC5F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE;YAC7C,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,EAAE,MAAM,EAAC,QAAQ,GAAG,CAAC,CAAC;YAEtE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE;YAC7C,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,EAAE,MAAM,EAAC,QAAQ,GAAG,CAAC,CAAC;YAEtE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE;YACjD,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,EAAE,MAAM,EAAC,YAAY,GAAG,CAAC,CAAC;YAE1E,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE;QAC/B,EAAE,CAAC,0CAA0C,EAAE;YAC7C,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,GAAI,CAAC,CAAC;YAEtD,4CAA4C;YAC5C,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACrD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAE3D,4CAA4C;YAC5C,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACrD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAE3D,iCAAiC;YACjC,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACrD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE;YACxC,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,EAAE,GAAI,CAAC,CAAC;YAE1C,MAAM,CAAC,cAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,sCAAsC;QAC5F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE;YACjD,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,EAAE,MAAM,EAAC,QAAQ,GAAG,CAAC,CAAC;YAEtE,uDAAuD;YACvD,MAAM,CAAC,cAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,kCAAkC;YAC1F,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAE3D,yBAAyB;YACzB,MAAM,CAAC,cAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,kCAAkC;YAC3F,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAExD,uBAAuB;YACvB,MAAM,CAAC,cAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,kCAAkC;YAC1F,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE;YACjD,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,EAAE,MAAM,EAAC,QAAQ,GAAG,CAAC,CAAC;YAEtE,mDAAmD;YACnD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACtD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAE3D,wBAAwB;YACxB,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACtD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAExD,uBAAuB;YACvB,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACtD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE;QAChC,EAAE,CAAC,mCAAmC,EAAE;YACtC,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,GAAI,CAAC,CAAC;YAEtD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACnE,wFAAwF;YACxF,MAAM,CAAC,cAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE;YAC3C,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,GAAI,CAAC,CAAC;YAEtD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACrE,+CAA+C;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE;YAC1C,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,EAAE,MAAM,EAAC,QAAQ,GAAG,CAAC,CAAC;YAEtE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAChE,oCAAoC;YACpC,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE;YACzC,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,EAAE,MAAM,EAAC,QAAQ,GAAG,CAAC,CAAC;YAEtE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC/D,qCAAqC;YACrC,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE;QACxB,EAAE,CAAC,uCAAuC,EAAE;YAC1C,IAAM,aAAa,GAAG;gBACpB;oBACE,KAAK,EAAE,gBAAgB;oBACvB,MAAM,EAAE,CAAC;oBACT,MAAM,EAAE,CAAC;oBACT,aAAa,EAAE,CAAC;oBAChB,MAAM,EAAE,CAAC;oBACT,UAAU,EAAE,CAAC;oBACb,WAAW,EAAE,CAAC;oBACd,QAAQ,EAAE,UAAU;iBACrB;aACF,CAAC;YAEF,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,aAAa,GAAI,CAAC,CAAC;YAErD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC/D,MAAM,CAAC,cAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE;YAC1C,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,EAAE,MAAM,EAAC,QAAQ,GAAG,CAAC,CAAC;YAEtE,uEAAuE;YACvE,MAAM,CAAC,cAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,uCAAuC;QAChG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE;YACnD,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,EAAE,cAAc,EAAC,aAAa,GAAG,CAAC,CAAC;YAEnF,MAAM,CAAC,cAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,uCAAuC;YAClG,MAAM,CAAC,cAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,uCAAuC;YAC9F,MAAM,CAAC,cAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,kCAAkC;QAC5F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE;QACxB,EAAE,CAAC,6BAA6B,EAAE;YAChC,IAAM,YAAY,GAAG,GAAG,CAAC;YACzB,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,YAAY,GAAI,CAAC,CAAC;YAE5E,IAAM,SAAS,GAAG,cAAM,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,aAAa,CAAC;YAC3E,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,kBAAW,YAAY,OAAI,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE;YAChC,IAAM,YAAY,GAAG;gBACnB,MAAM,EAAE,SAAS;gBACjB,MAAM,EAAE,SAAS;gBACjB,MAAM,EAAE,SAAS;gBACjB,MAAM,EAAE,SAAS;aAClB,CAAC;YAEF,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,YAAY,GAAI,CAAC,CAAC;YAE5E,IAAM,IAAI,GAAG,cAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE;QACxB,EAAE,CAAC,sCAAsC,EAAE;YACzC,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,GAAI,CAAC,CAAC;YAEtD,IAAM,KAAK,GAAG,cAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YAClD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,2BAA2B;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE;YACpD,IAAA,cAAM,EAAC,uBAAC,6BAAmB,IAAC,IAAI,EAAE,cAAc,GAAI,CAAC,CAAC;YAEtD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,yCAAyC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC1F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/skills/visualizations/MarketInsightsChart.test.tsx"], "sourcesContent": ["import React from 'react';\nimport { render, screen } from '@testing-library/react';\nimport '@testing-library/jest-dom';\nimport MarketInsightsChart from '@/components/skills/visualizations/MarketInsightsChart';\n\n// Mock recharts components\njest.mock('recharts', () => ({\n  BarChart: ({ children }: any) => <div data-testid=\"bar-chart\">{children}</div>,\n  Bar: ({ dataKey, fill }: any) => <div data-testid=\"bar\" data-key={dataKey} data-fill={fill} />,\n  XAxis: ({ dataKey }: any) => <div data-testid=\"x-axis\" data-key={dataKey} />,\n  YAxis: () => <div data-testid=\"y-axis\" />,\n  CartesianGrid: () => <div data-testid=\"cartesian-grid\" />,\n  Tooltip: () => <div data-testid=\"tooltip\" />,\n  ResponsiveContainer: ({ children }: any) => <div data-testid=\"responsive-container\">{children}</div>,\n  Legend: () => <div data-testid=\"legend\" />,\n}));\n\nconst mockMarketData = [\n  {\n    skill: 'JavaScript',\n    demand: 85,\n    supply: 70,\n    averageSalary: 95000,\n    growth: 12.5,\n    difficulty: 6,\n    timeToLearn: 8,\n    category: 'Programming',\n  },\n  {\n    skill: 'React',\n    demand: 80,\n    supply: 65,\n    averageSalary: 90000,\n    growth: 15.2,\n    difficulty: 7,\n    timeToLearn: 10,\n    category: 'Frontend',\n  },\n  {\n    skill: 'Python',\n    demand: 90,\n    supply: 75,\n    averageSalary: 100000,\n    growth: 18.7,\n    difficulty: 5,\n    timeToLearn: 6,\n    category: 'Programming',\n  },\n];\n\ndescribe('MarketInsightsChart', () => {\n  describe('Component Rendering', () => {\n    it('should render with default props', () => {\n      render(<MarketInsightsChart data={mockMarketData} />);\n      \n      expect(screen.getByText('Market Insights')).toBeInTheDocument();\n      expect(screen.getByText('Skill demand, supply, and market trends')).toBeInTheDocument();\n      expect(screen.getByTestId('bar-chart')).toBeInTheDocument();\n    });\n\n    it('should render with custom title and description', () => {\n      const customTitle = 'Custom Market Analysis';\n      const customDescription = 'Custom market description';\n      \n      render(\n        <MarketInsightsChart\n          data={mockMarketData}\n          title={customTitle}\n          description={customDescription}\n        />\n      );\n      \n      expect(screen.getByText(customTitle)).toBeInTheDocument();\n      expect(screen.getByText(customDescription)).toBeInTheDocument();\n    });\n\n    it('should render without description when not provided', () => {\n      render(\n        <MarketInsightsChart\n          data={mockMarketData}\n          title=\"Test Title\"\n          description=\"\"\n        />\n      );\n      \n      expect(screen.getByText('Test Title')).toBeInTheDocument();\n      expect(screen.queryByText('Skill demand, supply')).not.toBeInTheDocument();\n    });\n  });\n\n  describe('Chart Components', () => {\n    it('should render all required chart components', () => {\n      render(<MarketInsightsChart data={mockMarketData} />);\n      \n      expect(screen.getByTestId('responsive-container')).toBeInTheDocument();\n      expect(screen.getByTestId('bar-chart')).toBeInTheDocument();\n      expect(screen.getByTestId('cartesian-grid')).toBeInTheDocument();\n      expect(screen.getByTestId('x-axis')).toBeInTheDocument();\n      expect(screen.getByTestId('y-axis')).toBeInTheDocument();\n      expect(screen.getByTestId('tooltip')).toBeInTheDocument();\n    });\n\n    it('should render bars for demand and supply by default', () => {\n      render(<MarketInsightsChart data={mockMarketData} />);\n      \n      const bars = screen.getAllByTestId('bar');\n      expect(bars).toHaveLength(2);\n      \n      expect(bars[0]).toHaveAttribute('data-key', 'demand');\n      expect(bars[0]).toHaveAttribute('data-fill', '#3b82f6');\n      \n      expect(bars[1]).toHaveAttribute('data-key', 'supply');\n      expect(bars[1]).toHaveAttribute('data-fill', '#10b981');\n    });\n\n    it('should set correct dataKey for x-axis', () => {\n      render(<MarketInsightsChart data={mockMarketData} />);\n      \n      const xAxis = screen.getByTestId('x-axis');\n      expect(xAxis).toHaveAttribute('data-key', 'skill');\n    });\n\n    it('should render legend when showLegend is true', () => {\n      render(<MarketInsightsChart data={mockMarketData} showLegend={true} />);\n      expect(screen.getByTestId('legend')).toBeInTheDocument();\n    });\n\n    it('should not render legend when showLegend is false', () => {\n      render(<MarketInsightsChart data={mockMarketData} showLegend={false} />);\n      expect(screen.queryByTestId('legend')).not.toBeInTheDocument();\n    });\n  });\n\n  describe('Metric Selection', () => {\n    it('should display demand and supply metrics by default', () => {\n      render(<MarketInsightsChart data={mockMarketData} />);\n\n      expect(screen.getAllByText('Demand vs Supply')).toHaveLength(2); // Button and chart title\n    });\n\n    it('should allow switching to salary metrics', () => {\n      render(<MarketInsightsChart data={mockMarketData} metric=\"salary\" />);\n      \n      expect(screen.getByText('Average Salary')).toBeInTheDocument();\n    });\n\n    it('should allow switching to growth metrics', () => {\n      render(<MarketInsightsChart data={mockMarketData} metric=\"growth\" />);\n      \n      expect(screen.getByText('Growth Rate')).toBeInTheDocument();\n    });\n\n    it('should allow switching to difficulty metrics', () => {\n      render(<MarketInsightsChart data={mockMarketData} metric=\"difficulty\" />);\n      \n      expect(screen.getByText('Learning Difficulty')).toBeInTheDocument();\n    });\n  });\n\n  describe('Market Summary Cards', () => {\n    it('should display market summary statistics', () => {\n      render(<MarketInsightsChart data={mockMarketData} />);\n      \n      // Average demand: (85 + 80 + 90) / 3 = 85.0\n      expect(screen.getByText('85.0')).toBeInTheDocument();\n      expect(screen.getByText('Avg Demand')).toBeInTheDocument();\n      \n      // Average supply: (70 + 65 + 75) / 3 = 70.0\n      expect(screen.getByText('70.0')).toBeInTheDocument();\n      expect(screen.getByText('Avg Supply')).toBeInTheDocument();\n      \n      // Market gap: 85.0 - 70.0 = 15.0\n      expect(screen.getByText('15.0')).toBeInTheDocument();\n      expect(screen.getByText('Market Gap')).toBeInTheDocument();\n    });\n\n    it('should handle empty data gracefully', () => {\n      render(<MarketInsightsChart data={[]} />);\n\n      expect(screen.getAllByText('0.0')).toHaveLength(3); // Should show 0 for all three metrics\n    });\n\n    it('should calculate salary statistics correctly', () => {\n      render(<MarketInsightsChart data={mockMarketData} metric=\"salary\" />);\n\n      // Average salary: (95000 + 90000 + 100000) / 3 = 95000\n      expect(screen.getAllByText('$95,000')).toHaveLength(2); // Summary card and recommendation\n      expect(screen.getByText('Avg Salary')).toBeInTheDocument();\n\n      // Highest salary: 100000\n      expect(screen.getAllByText('$100,000')).toHaveLength(2); // Summary card and recommendation\n      expect(screen.getByText('Highest')).toBeInTheDocument();\n\n      // Lowest salary: 90000\n      expect(screen.getAllByText('$90,000')).toHaveLength(2); // Summary card and recommendation\n      expect(screen.getByText('Lowest')).toBeInTheDocument();\n    });\n\n    it('should calculate growth statistics correctly', () => {\n      render(<MarketInsightsChart data={mockMarketData} metric=\"growth\" />);\n      \n      // Average growth: (12.5 + 15.2 + 18.7) / 3 = 15.5%\n      expect(screen.getByText('15.5%')).toBeInTheDocument();\n      expect(screen.getByText('Avg Growth')).toBeInTheDocument();\n      \n      // Highest growth: 18.7%\n      expect(screen.getByText('18.7%')).toBeInTheDocument();\n      expect(screen.getByText('Highest')).toBeInTheDocument();\n      \n      // Lowest growth: 12.5%\n      expect(screen.getByText('12.5%')).toBeInTheDocument();\n      expect(screen.getByText('Lowest')).toBeInTheDocument();\n    });\n  });\n\n  describe('Skill Recommendations', () => {\n    it('should display high-demand skills', () => {\n      render(<MarketInsightsChart data={mockMarketData} />);\n\n      expect(screen.getByText('High Demand Skills')).toBeInTheDocument();\n      // Python has highest demand (90) - appears in both high demand and market opportunities\n      expect(screen.getAllByText('Python')).toHaveLength(2);\n    });\n\n    it('should display skills with market gaps', () => {\n      render(<MarketInsightsChart data={mockMarketData} />);\n      \n      expect(screen.getByText('Market Opportunities')).toBeInTheDocument();\n      // Skills with demand > supply should be listed\n    });\n\n    it('should display fastest growing skills', () => {\n      render(<MarketInsightsChart data={mockMarketData} metric=\"growth\" />);\n      \n      expect(screen.getByText('Fastest Growing')).toBeInTheDocument();\n      // Python has highest growth (18.7%)\n      expect(screen.getByText('Python')).toBeInTheDocument();\n    });\n\n    it('should display highest paying skills', () => {\n      render(<MarketInsightsChart data={mockMarketData} metric=\"salary\" />);\n      \n      expect(screen.getByText('Highest Paying')).toBeInTheDocument();\n      // Python has highest salary (100000)\n      expect(screen.getByText('Python')).toBeInTheDocument();\n    });\n  });\n\n  describe('Data Handling', () => {\n    it('should handle skills with zero values', () => {\n      const dataWithZeros = [\n        {\n          skill: 'New Technology',\n          demand: 0,\n          supply: 0,\n          averageSalary: 0,\n          growth: 0,\n          difficulty: 0,\n          timeToLearn: 0,\n          category: 'Emerging',\n        },\n      ];\n\n      render(<MarketInsightsChart data={dataWithZeros} />);\n\n      expect(screen.getByText('New Technology')).toBeInTheDocument();\n      expect(screen.getAllByText('0.0')).toHaveLength(3); // Three summary cards\n    });\n\n    it('should sort skills by selected metric', () => {\n      render(<MarketInsightsChart data={mockMarketData} sortBy=\"demand\" />);\n\n      // Should be sorted by demand: Python (90), JavaScript (85), React (80)\n      expect(screen.getAllByText('Python')).toHaveLength(2); // High demand and market opportunities\n    });\n\n    it('should filter skills by category when provided', () => {\n      render(<MarketInsightsChart data={mockMarketData} filterCategory=\"Programming\" />);\n\n      expect(screen.getAllByText('JavaScript')).toHaveLength(2); // High demand and market opportunities\n      expect(screen.getAllByText('Python')).toHaveLength(2); // High demand and market opportunities\n      expect(screen.queryAllByText('React')).toHaveLength(0); // Should not appear when filtered\n    });\n  });\n\n  describe('Customization', () => {\n    it('should accept custom height', () => {\n      const customHeight = 500;\n      render(<MarketInsightsChart data={mockMarketData} height={customHeight} />);\n      \n      const container = screen.getByTestId('responsive-container').parentElement;\n      expect(container).toHaveStyle(`height: ${customHeight}px`);\n    });\n\n    it('should accept custom colors', () => {\n      const customColors = {\n        demand: '#ff0000',\n        supply: '#00ff00',\n        salary: '#0000ff',\n        growth: '#ffff00',\n      };\n      \n      render(<MarketInsightsChart data={mockMarketData} colors={customColors} />);\n      \n      const bars = screen.getAllByTestId('bar');\n      expect(bars[0]).toHaveAttribute('data-fill', '#ff0000');\n      expect(bars[1]).toHaveAttribute('data-fill', '#00ff00');\n    });\n  });\n\n  describe('Accessibility', () => {\n    it('should have proper heading structure', () => {\n      render(<MarketInsightsChart data={mockMarketData} />);\n      \n      const title = screen.getByText('Market Insights');\n      expect(title.tagName).toBe('DIV'); // CardTitle renders as div\n    });\n\n    it('should have descriptive text for screen readers', () => {\n      render(<MarketInsightsChart data={mockMarketData} />);\n      \n      expect(screen.getByText('Skill demand, supply, and market trends')).toBeInTheDocument();\n    });\n  });\n});\n"], "version": 3}