f8addf51f42ed1ad6c6b58c7e43d7410
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
// Mock recharts components
jest.mock('recharts', function () { return ({
    BarChart: function (_a) {
        var children = _a.children;
        return (0, jsx_runtime_1.jsx)("div", { "data-testid": "bar-chart", children: children });
    },
    Bar: function (_a) {
        var dataKey = _a.dataKey, fill = _a.fill;
        return (0, jsx_runtime_1.jsx)("div", { "data-testid": "bar", "data-key": dataKey, "data-fill": fill });
    },
    XAxis: function (_a) {
        var dataKey = _a.data<PERSON>ey;
        return (0, jsx_runtime_1.jsx)("div", { "data-testid": "x-axis", "data-key": dataKey });
    },
    YAxis: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "y-axis" }); },
    CartesianGrid: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "cartesian-grid" }); },
    Tooltip: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "tooltip" }); },
    ResponsiveContainer: function (_a) {
        var children = _a.children;
        return (0, jsx_runtime_1.jsx)("div", { "data-testid": "responsive-container", children: children });
    },
    Legend: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "legend" }); },
}); });
var react_1 = __importDefault(require("react"));
var react_2 = require("@testing-library/react");
require("@testing-library/jest-dom");
var MarketInsightsChart_1 = __importDefault(require("@/components/skills/visualizations/MarketInsightsChart"));
var mockMarketData = [
    {
        skill: 'JavaScript',
        demand: 85,
        supply: 70,
        averageSalary: 95000,
        growth: 12.5,
        difficulty: 6,
        timeToLearn: 8,
        category: 'Programming',
    },
    {
        skill: 'React',
        demand: 80,
        supply: 65,
        averageSalary: 90000,
        growth: 15.2,
        difficulty: 7,
        timeToLearn: 10,
        category: 'Frontend',
    },
    {
        skill: 'Python',
        demand: 90,
        supply: 75,
        averageSalary: 100000,
        growth: 18.7,
        difficulty: 5,
        timeToLearn: 6,
        category: 'Programming',
    },
];
describe('MarketInsightsChart', function () {
    describe('Component Rendering', function () {
        it('should render with default props', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData }));
            expect(react_2.screen.getByText('Market Insights')).toBeInTheDocument();
            expect(react_2.screen.getByText('Skill demand, supply, and market trends')).toBeInTheDocument();
            expect(react_2.screen.getByTestId('bar-chart')).toBeInTheDocument();
        });
        it('should render with custom title and description', function () {
            var customTitle = 'Custom Market Analysis';
            var customDescription = 'Custom market description';
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData, title: customTitle, description: customDescription }));
            expect(react_2.screen.getByText(customTitle)).toBeInTheDocument();
            expect(react_2.screen.getByText(customDescription)).toBeInTheDocument();
        });
        it('should render without description when not provided', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData, title: "Test Title", description: "" }));
            expect(react_2.screen.getByText('Test Title')).toBeInTheDocument();
            expect(react_2.screen.queryByText('Skill demand, supply')).not.toBeInTheDocument();
        });
    });
    describe('Chart Components', function () {
        it('should render all required chart components', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData }));
            expect(react_2.screen.getByTestId('responsive-container')).toBeInTheDocument();
            expect(react_2.screen.getByTestId('bar-chart')).toBeInTheDocument();
            expect(react_2.screen.getByTestId('cartesian-grid')).toBeInTheDocument();
            expect(react_2.screen.getByTestId('x-axis')).toBeInTheDocument();
            expect(react_2.screen.getByTestId('y-axis')).toBeInTheDocument();
            expect(react_2.screen.getByTestId('tooltip')).toBeInTheDocument();
        });
        it('should render bars for demand and supply by default', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData }));
            var bars = react_2.screen.getAllByTestId('bar');
            expect(bars).toHaveLength(2);
            expect(bars[0]).toHaveAttribute('data-key', 'demand');
            expect(bars[0]).toHaveAttribute('data-fill', '#3b82f6');
            expect(bars[1]).toHaveAttribute('data-key', 'supply');
            expect(bars[1]).toHaveAttribute('data-fill', '#10b981');
        });
        it('should set correct dataKey for x-axis', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData }));
            var xAxis = react_2.screen.getByTestId('x-axis');
            expect(xAxis).toHaveAttribute('data-key', 'skill');
        });
        it('should render legend when showLegend is true', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData, showLegend: true }));
            expect(react_2.screen.getByTestId('legend')).toBeInTheDocument();
        });
        it('should not render legend when showLegend is false', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData, showLegend: false }));
            expect(react_2.screen.queryByTestId('legend')).not.toBeInTheDocument();
        });
    });
    describe('Metric Selection', function () {
        it('should display demand and supply metrics by default', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData }));
            expect(react_2.screen.getAllByText('Demand vs Supply')).toHaveLength(2); // Button and chart title
        });
        it('should allow switching to salary metrics', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData, metric: "salary" }));
            expect(react_2.screen.getByText('Average Salary')).toBeInTheDocument();
        });
        it('should allow switching to growth metrics', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData, metric: "growth" }));
            expect(react_2.screen.getByText('Growth Rate')).toBeInTheDocument();
        });
        it('should allow switching to difficulty metrics', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData, metric: "difficulty" }));
            expect(react_2.screen.getByText('Learning Difficulty')).toBeInTheDocument();
        });
    });
    describe('Market Summary Cards', function () {
        it('should display market summary statistics', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData }));
            // Average demand: (85 + 80 + 90) / 3 = 85.0
            expect(react_2.screen.getByText('85.0')).toBeInTheDocument();
            expect(react_2.screen.getByText('Avg Demand')).toBeInTheDocument();
            // Average supply: (70 + 65 + 75) / 3 = 70.0
            expect(react_2.screen.getByText('70.0')).toBeInTheDocument();
            expect(react_2.screen.getByText('Avg Supply')).toBeInTheDocument();
            // Market gap: 85.0 - 70.0 = 15.0
            expect(react_2.screen.getByText('15.0')).toBeInTheDocument();
            expect(react_2.screen.getByText('Market Gap')).toBeInTheDocument();
        });
        it('should handle empty data gracefully', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: [] }));
            expect(react_2.screen.getAllByText('0.0')).toHaveLength(3); // Should show 0 for all three metrics
        });
        it('should calculate salary statistics correctly', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData, metric: "salary" }));
            // Average salary: (95000 + 90000 + 100000) / 3 = 95000
            expect(react_2.screen.getAllByText('$95,000')).toHaveLength(2); // Summary card and recommendation
            expect(react_2.screen.getByText('Avg Salary')).toBeInTheDocument();
            // Highest salary: 100000
            expect(react_2.screen.getAllByText('$100,000')).toHaveLength(2); // Summary card and recommendation
            expect(react_2.screen.getByText('Highest')).toBeInTheDocument();
            // Lowest salary: 90000
            expect(react_2.screen.getAllByText('$90,000')).toHaveLength(2); // Summary card and recommendation
            expect(react_2.screen.getByText('Lowest')).toBeInTheDocument();
        });
        it('should calculate growth statistics correctly', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData, metric: "growth" }));
            // Average growth: (12.5 + 15.2 + 18.7) / 3 = 15.5%
            expect(react_2.screen.getByText('15.5%')).toBeInTheDocument();
            expect(react_2.screen.getByText('Avg Growth')).toBeInTheDocument();
            // Highest growth: 18.7%
            expect(react_2.screen.getByText('18.7%')).toBeInTheDocument();
            expect(react_2.screen.getByText('Highest')).toBeInTheDocument();
            // Lowest growth: 12.5%
            expect(react_2.screen.getByText('12.5%')).toBeInTheDocument();
            expect(react_2.screen.getByText('Lowest')).toBeInTheDocument();
        });
    });
    describe('Skill Recommendations', function () {
        it('should display high-demand skills', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData }));
            expect(react_2.screen.getByText('High Demand Skills')).toBeInTheDocument();
            // Python has highest demand (90) - appears in both high demand and market opportunities
            expect(react_2.screen.getAllByText('Python')).toHaveLength(2);
        });
        it('should display skills with market gaps', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData }));
            expect(react_2.screen.getByText('Market Opportunities')).toBeInTheDocument();
            // Skills with demand > supply should be listed
        });
        it('should display fastest growing skills', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData, metric: "growth" }));
            expect(react_2.screen.getByText('Fastest Growing')).toBeInTheDocument();
            // Python has highest growth (18.7%)
            expect(react_2.screen.getByText('Python')).toBeInTheDocument();
        });
        it('should display highest paying skills', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData, metric: "salary" }));
            expect(react_2.screen.getByText('Highest Paying')).toBeInTheDocument();
            // Python has highest salary (100000)
            expect(react_2.screen.getByText('Python')).toBeInTheDocument();
        });
    });
    describe('Data Handling', function () {
        it('should handle skills with zero values', function () {
            var dataWithZeros = [
                {
                    skill: 'New Technology',
                    demand: 0,
                    supply: 0,
                    averageSalary: 0,
                    growth: 0,
                    difficulty: 0,
                    timeToLearn: 0,
                    category: 'Emerging',
                },
            ];
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: dataWithZeros }));
            expect(react_2.screen.getByText('New Technology')).toBeInTheDocument();
            expect(react_2.screen.getAllByText('0.0')).toHaveLength(3); // Three summary cards
        });
        it('should sort skills by selected metric', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData, sortBy: "demand" }));
            // Should be sorted by demand: Python (90), JavaScript (85), React (80)
            expect(react_2.screen.getAllByText('Python')).toHaveLength(2); // High demand and market opportunities
        });
        it('should filter skills by category when provided', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData, filterCategory: "Programming" }));
            expect(react_2.screen.getAllByText('JavaScript')).toHaveLength(2); // High demand and market opportunities
            expect(react_2.screen.getAllByText('Python')).toHaveLength(2); // High demand and market opportunities
            expect(react_2.screen.queryAllByText('React')).toHaveLength(0); // Should not appear when filtered
        });
    });
    describe('Customization', function () {
        it('should accept custom height', function () {
            var customHeight = 500;
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData, height: customHeight }));
            var container = react_2.screen.getByTestId('responsive-container').parentElement;
            expect(container).toHaveStyle("height: ".concat(customHeight, "px"));
        });
        it('should accept custom colors', function () {
            var customColors = {
                demand: '#ff0000',
                supply: '#00ff00',
                salary: '#0000ff',
                growth: '#ffff00',
            };
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData, colors: customColors }));
            var bars = react_2.screen.getAllByTestId('bar');
            expect(bars[0]).toHaveAttribute('data-fill', '#ff0000');
            expect(bars[1]).toHaveAttribute('data-fill', '#00ff00');
        });
    });
    describe('Accessibility', function () {
        it('should have proper heading structure', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData }));
            var title = react_2.screen.getByText('Market Insights');
            expect(title.tagName).toBe('DIV'); // CardTitle renders as div
        });
        it('should have descriptive text for screen readers', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(MarketInsightsChart_1.default, { data: mockMarketData }));
            expect(react_2.screen.getByText('Skill demand, supply, and market trends')).toBeInTheDocument();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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