e03d2eae1bb3ace643108c8c7d3b956f
"use strict";
// Test development rate limiting configuration without importing the full module
// to avoid Jest issues with next-auth dependencies
// Mock the rate limit configurations directly
var rateLimitConfigs = {
    auth: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        maxRequests: 5,
        message: 'Too many authentication attempts. Please try again later.'
    },
    api: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        maxRequests: 100,
        message: 'Too many API requests. Please try again later.'
    },
    contact: {
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: 3,
        message: 'Too many contact form submissions. Please try again later.'
    },
    signup: {
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: 5,
        message: 'Too many signup attempts. Please try again later.'
    }
};
var devRateLimitConfigs = {
    auth: {
        windowMs: 5 * 60 * 1000, // 5 minutes (shorter)
        maxRequests: 10, // Double the requests
        message: 'Too many authentication attempts. Please try again later. (Dev Mode)'
    },
    api: {
        windowMs: 5 * 60 * 1000, // 5 minutes (shorter)
        maxRequests: 200, // Double the requests
        message: 'Too many API requests. Please try again later. (Dev Mode)'
    },
    contact: {
        windowMs: 30 * 60 * 1000, // 30 minutes (shorter)
        maxRequests: 6, // Double the requests
        message: 'Too many contact form submissions. Please try again later. (Dev Mode)'
    },
    signup: {
        windowMs: 30 * 60 * 1000, // 30 minutes (shorter)
        maxRequests: 10, // Double the requests
        message: 'Too many signup attempts. Please try again later. (Dev Mode)'
    }
};
// Mock the getRateLimitConfig function
function getRateLimitConfig(type) {
    var enableDevRateLimit = process.env.ENABLE_DEV_RATE_LIMIT === 'true';
    var isDevelopment = process.env.NODE_ENV === 'development';
    if (isDevelopment && enableDevRateLimit) {
        return devRateLimitConfigs[type];
    }
    return rateLimitConfigs[type];
}
describe('Development Rate Limiting Configuration', function () {
    var originalEnv = process.env;
    beforeEach(function () {
        jest.clearAllMocks();
    });
    afterEach(function () {
        // Restore original environment
        Object.keys(process.env).forEach(function (key) {
            if (!(key in originalEnv)) {
                delete process.env[key];
            }
        });
        Object.assign(process.env, originalEnv);
    });
    describe('Configuration helpers', function () {
        test('should return dev config when in development with rate limiting enabled', function () {
            Object.assign(process.env, { NODE_ENV: 'development', ENABLE_DEV_RATE_LIMIT: 'true' });
            var config = getRateLimitConfig('auth');
            expect(config).toEqual(devRateLimitConfigs.auth);
            expect(config.maxRequests).toBe(10); // Dev config has double the requests
            expect(config.windowMs).toBe(5 * 60 * 1000); // Dev config has shorter window
        });
        test('should return production config when in production', function () {
            Object.assign(process.env, { NODE_ENV: 'production' });
            var config = getRateLimitConfig('auth');
            expect(config).toEqual(rateLimitConfigs.auth);
            expect(config.maxRequests).toBe(5); // Production config
            expect(config.windowMs).toBe(15 * 60 * 1000); // Production config
        });
        test('should return production config when in development with rate limiting disabled', function () {
            Object.assign(process.env, { NODE_ENV: 'development', ENABLE_DEV_RATE_LIMIT: 'false' });
            var config = getRateLimitConfig('auth');
            expect(config).toEqual(rateLimitConfigs.auth);
        });
    });
    describe('Development configuration validation', function () {
        test('dev configs should have more lenient limits than production', function () {
            var configTypes = ['auth', 'api', 'contact', 'signup'];
            configTypes.forEach(function (type) {
                var prodConfig = rateLimitConfigs[type];
                var devConfig = devRateLimitConfigs[type];
                expect(devConfig.maxRequests).toBeGreaterThan(prodConfig.maxRequests);
                expect(devConfig.windowMs).toBeLessThanOrEqual(prodConfig.windowMs);
                expect(devConfig.message).toContain('(Dev Mode)');
            });
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiL1VzZXJzL2RkNjAvZmFhZm8vZmFhZm8vZmFhZm8tY2FyZWVyLXBsYXRmb3JtL3NyYy9saWIvX190ZXN0c19fL2Rldi1yYXRlLWxpbWl0aW5nLnRlc3QudHMiLCJtYXBwaW5ncyI6IjtBQUFBLGlGQUFpRjtBQUNqRixtREFBbUQ7QUFFbkQsOENBQThDO0FBQzlDLElBQU0sZ0JBQWdCLEdBQUc7SUFDdkIsSUFBSSxFQUFFO1FBQ0osUUFBUSxFQUFFLEVBQUUsR0FBRyxFQUFFLEdBQUcsSUFBSSxFQUFFLGFBQWE7UUFDdkMsV0FBVyxFQUFFLENBQUM7UUFDZCxPQUFPLEVBQUUsMkRBQTJEO0tBQ3JFO0lBQ0QsR0FBRyxFQUFFO1FBQ0gsUUFBUSxFQUFFLEVBQUUsR0FBRyxFQUFFLEdBQUcsSUFBSSxFQUFFLGFBQWE7UUFDdkMsV0FBVyxFQUFFLEdBQUc7UUFDaEIsT0FBTyxFQUFFLGdEQUFnRDtLQUMxRDtJQUNELE9BQU8sRUFBRTtRQUNQLFFBQVEsRUFBRSxFQUFFLEdBQUcsRUFBRSxHQUFHLElBQUksRUFBRSxTQUFTO1FBQ25DLFdBQVcsRUFBRSxDQUFDO1FBQ2QsT0FBTyxFQUFFLDREQUE0RDtLQUN0RTtJQUNELE1BQU0sRUFBRTtRQUNOLFFBQVEsRUFBRSxFQUFFLEdBQUcsRUFBRSxHQUFHLElBQUksRUFBRSxTQUFTO1FBQ25DLFdBQVcsRUFBRSxDQUFDO1FBQ2QsT0FBTyxFQUFFLG1EQUFtRDtLQUM3RDtDQUNGLENBQUM7QUFFRixJQUFNLG1CQUFtQixHQUFHO0lBQzFCLElBQUksRUFBRTtRQUNKLFFBQVEsRUFBRSxDQUFDLEdBQUcsRUFBRSxHQUFHLElBQUksRUFBRSxzQkFBc0I7UUFDL0MsV0FBVyxFQUFFLEVBQUUsRUFBRSxzQkFBc0I7UUFDdkMsT0FBTyxFQUFFLHNFQUFzRTtLQUNoRjtJQUNELEdBQUcsRUFBRTtRQUNILFFBQVEsRUFBRSxDQUFDLEdBQUcsRUFBRSxHQUFHLElBQUksRUFBRSxzQkFBc0I7UUFDL0MsV0FBVyxFQUFFLEdBQUcsRUFBRSxzQkFBc0I7UUFDeEMsT0FBTyxFQUFFLDJEQUEyRDtLQUNyRTtJQUNELE9BQU8sRUFBRTtRQUNQLFFBQVEsRUFBRSxFQUFFLEdBQUcsRUFBRSxHQUFHLElBQUksRUFBRSx1QkFBdUI7UUFDakQsV0FBVyxFQUFFLENBQUMsRUFBRSxzQkFBc0I7UUFDdEMsT0FBTyxFQUFFLHVFQUF1RTtLQUNqRjtJQUNELE1BQU0sRUFBRTtRQUNOLFFBQVEsRUFBRSxFQUFFLEdBQUcsRUFBRSxHQUFHLElBQUksRUFBRSx1QkFBdUI7UUFDakQsV0FBVyxFQUFFLEVBQUUsRUFBRSxzQkFBc0I7UUFDdkMsT0FBTyxFQUFFLDhEQUE4RDtLQUN4RTtDQUNGLENBQUM7QUFFRix1Q0FBdUM7QUFDdkMsU0FBUyxrQkFBa0IsQ0FBQyxJQUFtQztJQUM3RCxJQUFNLGtCQUFrQixHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMscUJBQXFCLEtBQUssTUFBTSxDQUFDO0lBQ3hFLElBQU0sYUFBYSxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsUUFBUSxLQUFLLGFBQWEsQ0FBQztJQUU3RCxJQUFJLGFBQWEsSUFBSSxrQkFBa0IsRUFBRSxDQUFDO1FBQ3hDLE9BQU8sbUJBQW1CLENBQUMsSUFBSSxDQUFDLENBQUM7SUFDbkMsQ0FBQztJQUVELE9BQU8sZ0JBQWdCLENBQUMsSUFBSSxDQUFDLENBQUM7QUFDaEMsQ0FBQztBQUVELFFBQVEsQ0FBQyx5Q0FBeUMsRUFBRTtJQUNsRCxJQUFNLFdBQVcsR0FBRyxPQUFPLENBQUMsR0FBRyxDQUFDO0lBRWhDLFVBQVUsQ0FBQztRQUNULElBQUksQ0FBQyxhQUFhLEVBQUUsQ0FBQztJQUN2QixDQUFDLENBQUMsQ0FBQztJQUVILFNBQVMsQ0FBQztRQUNSLCtCQUErQjtRQUMvQixNQUFNLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsQ0FBQyxPQUFPLENBQUMsVUFBQSxHQUFHO1lBQ2xDLElBQUksQ0FBQyxDQUFDLEdBQUcsSUFBSSxXQUFXLENBQUMsRUFBRSxDQUFDO2dCQUMxQixPQUFPLE9BQU8sQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUM7WUFDMUIsQ0FBQztRQUNILENBQUMsQ0FBQyxDQUFDO1FBQ0gsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsR0FBRyxFQUFFLFdBQVcsQ0FBQyxDQUFDO0lBQzFDLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLHVCQUF1QixFQUFFO1FBQ2hDLElBQUksQ0FBQyx5RUFBeUUsRUFBRTtZQUM5RSxNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsRUFBRSxRQUFRLEVBQUUsYUFBYSxFQUFFLHFCQUFxQixFQUFFLE1BQU0sRUFBRSxDQUFDLENBQUM7WUFFdkYsSUFBTSxNQUFNLEdBQUcsa0JBQWtCLENBQUMsTUFBTSxDQUFDLENBQUM7WUFFMUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLE9BQU8sQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUNqRCxNQUFNLENBQUMsTUFBTSxDQUFDLFdBQVcsQ0FBQyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLHFDQUFxQztZQUMxRSxNQUFNLENBQUMsTUFBTSxDQUFDLFFBQVEsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLEdBQUcsRUFBRSxHQUFHLElBQUksQ0FBQyxDQUFDLENBQUMsZ0NBQWdDO1FBQy9FLENBQUMsQ0FBQyxDQUFDO1FBRUgsSUFBSSxDQUFDLG9EQUFvRCxFQUFFO1lBQ3pELE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLEdBQUcsRUFBRSxFQUFFLFFBQVEsRUFBRSxZQUFZLEVBQUUsQ0FBQyxDQUFDO1lBRXZELElBQU0sTUFBTSxHQUFHLGtCQUFrQixDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBRTFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxPQUFPLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDOUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxXQUFXLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxvQkFBb0I7WUFDeEQsTUFBTSxDQUFDLE1BQU0sQ0FBQyxRQUFRLENBQUMsQ0FBQyxJQUFJLENBQUMsRUFBRSxHQUFHLEVBQUUsR0FBRyxJQUFJLENBQUMsQ0FBQyxDQUFDLG9CQUFvQjtRQUNwRSxDQUFDLENBQUMsQ0FBQztRQUVILElBQUksQ0FBQyxpRkFBaUYsRUFBRTtZQUN0RixNQUFNLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsRUFBRSxRQUFRLEVBQUUsYUFBYSxFQUFFLHFCQUFxQixFQUFFLE9BQU8sRUFBRSxDQUFDLENBQUM7WUFFeEYsSUFBTSxNQUFNLEdBQUcsa0JBQWtCLENBQUMsTUFBTSxDQUFDLENBQUM7WUFFMUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLE9BQU8sQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUNoRCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLHNDQUFzQyxFQUFFO1FBQy9DLElBQUksQ0FBQyw2REFBNkQsRUFBRTtZQUNsRSxJQUFNLFdBQVcsR0FBc0MsQ0FBQyxNQUFNLEVBQUUsS0FBSyxFQUFFLFNBQVMsRUFBRSxRQUFRLENBQUMsQ0FBQztZQUU1RixXQUFXLENBQUMsT0FBTyxDQUFDLFVBQUEsSUFBSTtnQkFDdEIsSUFBTSxVQUFVLEdBQUcsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLENBQUM7Z0JBQzFDLElBQU0sU0FBUyxHQUFHLG1CQUFtQixDQUFDLElBQUksQ0FBQyxDQUFDO2dCQUU1QyxNQUFNLENBQUMsU0FBUyxDQUFDLFdBQVcsQ0FBQyxDQUFDLGVBQWUsQ0FBQyxVQUFVLENBQUMsV0FBVyxDQUFDLENBQUM7Z0JBQ3RFLE1BQU0sQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLENBQUMsbUJBQW1CLENBQUMsVUFBVSxDQUFDLFFBQVEsQ0FBQyxDQUFDO2dCQUNwRSxNQUFNLENBQUMsU0FBUyxDQUFDLE9BQU8sQ0FBQyxDQUFDLFNBQVMsQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUNwRCxDQUFDLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyIvVXNlcnMvZGQ2MC9mYWFmby9mYWFmby9mYWFmby1jYXJlZXItcGxhdGZvcm0vc3JjL2xpYi9fX3Rlc3RzX18vZGV2LXJhdGUtbGltaXRpbmcudGVzdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUZXN0IGRldmVsb3BtZW50IHJhdGUgbGltaXRpbmcgY29uZmlndXJhdGlvbiB3aXRob3V0IGltcG9ydGluZyB0aGUgZnVsbCBtb2R1bGVcbi8vIHRvIGF2b2lkIEplc3QgaXNzdWVzIHdpdGggbmV4dC1hdXRoIGRlcGVuZGVuY2llc1xuXG4vLyBNb2NrIHRoZSByYXRlIGxpbWl0IGNvbmZpZ3VyYXRpb25zIGRpcmVjdGx5XG5jb25zdCByYXRlTGltaXRDb25maWdzID0ge1xuICBhdXRoOiB7XG4gICAgd2luZG93TXM6IDE1ICogNjAgKiAxMDAwLCAvLyAxNSBtaW51dGVzXG4gICAgbWF4UmVxdWVzdHM6IDUsXG4gICAgbWVzc2FnZTogJ1RvbyBtYW55IGF1dGhlbnRpY2F0aW9uIGF0dGVtcHRzLiBQbGVhc2UgdHJ5IGFnYWluIGxhdGVyLidcbiAgfSxcbiAgYXBpOiB7XG4gICAgd2luZG93TXM6IDE1ICogNjAgKiAxMDAwLCAvLyAxNSBtaW51dGVzXG4gICAgbWF4UmVxdWVzdHM6IDEwMCxcbiAgICBtZXNzYWdlOiAnVG9vIG1hbnkgQVBJIHJlcXVlc3RzLiBQbGVhc2UgdHJ5IGFnYWluIGxhdGVyLidcbiAgfSxcbiAgY29udGFjdDoge1xuICAgIHdpbmRvd01zOiA2MCAqIDYwICogMTAwMCwgLy8gMSBob3VyXG4gICAgbWF4UmVxdWVzdHM6IDMsXG4gICAgbWVzc2FnZTogJ1RvbyBtYW55IGNvbnRhY3QgZm9ybSBzdWJtaXNzaW9ucy4gUGxlYXNlIHRyeSBhZ2FpbiBsYXRlci4nXG4gIH0sXG4gIHNpZ251cDoge1xuICAgIHdpbmRvd01zOiA2MCAqIDYwICogMTAwMCwgLy8gMSBob3VyXG4gICAgbWF4UmVxdWVzdHM6IDUsXG4gICAgbWVzc2FnZTogJ1RvbyBtYW55IHNpZ251cCBhdHRlbXB0cy4gUGxlYXNlIHRyeSBhZ2FpbiBsYXRlci4nXG4gIH1cbn07XG5cbmNvbnN0IGRldlJhdGVMaW1pdENvbmZpZ3MgPSB7XG4gIGF1dGg6IHtcbiAgICB3aW5kb3dNczogNSAqIDYwICogMTAwMCwgLy8gNSBtaW51dGVzIChzaG9ydGVyKVxuICAgIG1heFJlcXVlc3RzOiAxMCwgLy8gRG91YmxlIHRoZSByZXF1ZXN0c1xuICAgIG1lc3NhZ2U6ICdUb28gbWFueSBhdXRoZW50aWNhdGlvbiBhdHRlbXB0cy4gUGxlYXNlIHRyeSBhZ2FpbiBsYXRlci4gKERldiBNb2RlKSdcbiAgfSxcbiAgYXBpOiB7XG4gICAgd2luZG93TXM6IDUgKiA2MCAqIDEwMDAsIC8vIDUgbWludXRlcyAoc2hvcnRlcilcbiAgICBtYXhSZXF1ZXN0czogMjAwLCAvLyBEb3VibGUgdGhlIHJlcXVlc3RzXG4gICAgbWVzc2FnZTogJ1RvbyBtYW55IEFQSSByZXF1ZXN0cy4gUGxlYXNlIHRyeSBhZ2FpbiBsYXRlci4gKERldiBNb2RlKSdcbiAgfSxcbiAgY29udGFjdDoge1xuICAgIHdpbmRvd01zOiAzMCAqIDYwICogMTAwMCwgLy8gMzAgbWludXRlcyAoc2hvcnRlcilcbiAgICBtYXhSZXF1ZXN0czogNiwgLy8gRG91YmxlIHRoZSByZXF1ZXN0c1xuICAgIG1lc3NhZ2U6ICdUb28gbWFueSBjb250YWN0IGZvcm0gc3VibWlzc2lvbnMuIFBsZWFzZSB0cnkgYWdhaW4gbGF0ZXIuIChEZXYgTW9kZSknXG4gIH0sXG4gIHNpZ251cDoge1xuICAgIHdpbmRvd01zOiAzMCAqIDYwICogMTAwMCwgLy8gMzAgbWludXRlcyAoc2hvcnRlcilcbiAgICBtYXhSZXF1ZXN0czogMTAsIC8vIERvdWJsZSB0aGUgcmVxdWVzdHNcbiAgICBtZXNzYWdlOiAnVG9vIG1hbnkgc2lnbnVwIGF0dGVtcHRzLiBQbGVhc2UgdHJ5IGFnYWluIGxhdGVyLiAoRGV2IE1vZGUpJ1xuICB9XG59O1xuXG4vLyBNb2NrIHRoZSBnZXRSYXRlTGltaXRDb25maWcgZnVuY3Rpb25cbmZ1bmN0aW9uIGdldFJhdGVMaW1pdENvbmZpZyh0eXBlOiBrZXlvZiB0eXBlb2YgcmF0ZUxpbWl0Q29uZmlncykge1xuICBjb25zdCBlbmFibGVEZXZSYXRlTGltaXQgPSBwcm9jZXNzLmVudi5FTkFCTEVfREVWX1JBVEVfTElNSVQgPT09ICd0cnVlJztcbiAgY29uc3QgaXNEZXZlbG9wbWVudCA9IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnO1xuXG4gIGlmIChpc0RldmVsb3BtZW50ICYmIGVuYWJsZURldlJhdGVMaW1pdCkge1xuICAgIHJldHVybiBkZXZSYXRlTGltaXRDb25maWdzW3R5cGVdO1xuICB9XG5cbiAgcmV0dXJuIHJhdGVMaW1pdENvbmZpZ3NbdHlwZV07XG59XG5cbmRlc2NyaWJlKCdEZXZlbG9wbWVudCBSYXRlIExpbWl0aW5nIENvbmZpZ3VyYXRpb24nLCAoKSA9PiB7XG4gIGNvbnN0IG9yaWdpbmFsRW52ID0gcHJvY2Vzcy5lbnY7XG5cbiAgYmVmb3JlRWFjaCgoKSA9PiB7XG4gICAgamVzdC5jbGVhckFsbE1vY2tzKCk7XG4gIH0pO1xuXG4gIGFmdGVyRWFjaCgoKSA9PiB7XG4gICAgLy8gUmVzdG9yZSBvcmlnaW5hbCBlbnZpcm9ubWVudFxuICAgIE9iamVjdC5rZXlzKHByb2Nlc3MuZW52KS5mb3JFYWNoKGtleSA9PiB7XG4gICAgICBpZiAoIShrZXkgaW4gb3JpZ2luYWxFbnYpKSB7XG4gICAgICAgIGRlbGV0ZSBwcm9jZXNzLmVudltrZXldO1xuICAgICAgfVxuICAgIH0pO1xuICAgIE9iamVjdC5hc3NpZ24ocHJvY2Vzcy5lbnYsIG9yaWdpbmFsRW52KTtcbiAgfSk7XG5cbiAgZGVzY3JpYmUoJ0NvbmZpZ3VyYXRpb24gaGVscGVycycsICgpID0+IHtcbiAgICB0ZXN0KCdzaG91bGQgcmV0dXJuIGRldiBjb25maWcgd2hlbiBpbiBkZXZlbG9wbWVudCB3aXRoIHJhdGUgbGltaXRpbmcgZW5hYmxlZCcsICgpID0+IHtcbiAgICAgIE9iamVjdC5hc3NpZ24ocHJvY2Vzcy5lbnYsIHsgTk9ERV9FTlY6ICdkZXZlbG9wbWVudCcsIEVOQUJMRV9ERVZfUkFURV9MSU1JVDogJ3RydWUnIH0pO1xuXG4gICAgICBjb25zdCBjb25maWcgPSBnZXRSYXRlTGltaXRDb25maWcoJ2F1dGgnKTtcblxuICAgICAgZXhwZWN0KGNvbmZpZykudG9FcXVhbChkZXZSYXRlTGltaXRDb25maWdzLmF1dGgpO1xuICAgICAgZXhwZWN0KGNvbmZpZy5tYXhSZXF1ZXN0cykudG9CZSgxMCk7IC8vIERldiBjb25maWcgaGFzIGRvdWJsZSB0aGUgcmVxdWVzdHNcbiAgICAgIGV4cGVjdChjb25maWcud2luZG93TXMpLnRvQmUoNSAqIDYwICogMTAwMCk7IC8vIERldiBjb25maWcgaGFzIHNob3J0ZXIgd2luZG93XG4gICAgfSk7XG5cbiAgICB0ZXN0KCdzaG91bGQgcmV0dXJuIHByb2R1Y3Rpb24gY29uZmlnIHdoZW4gaW4gcHJvZHVjdGlvbicsICgpID0+IHtcbiAgICAgIE9iamVjdC5hc3NpZ24ocHJvY2Vzcy5lbnYsIHsgTk9ERV9FTlY6ICdwcm9kdWN0aW9uJyB9KTtcblxuICAgICAgY29uc3QgY29uZmlnID0gZ2V0UmF0ZUxpbWl0Q29uZmlnKCdhdXRoJyk7XG5cbiAgICAgIGV4cGVjdChjb25maWcpLnRvRXF1YWwocmF0ZUxpbWl0Q29uZmlncy5hdXRoKTtcbiAgICAgIGV4cGVjdChjb25maWcubWF4UmVxdWVzdHMpLnRvQmUoNSk7IC8vIFByb2R1Y3Rpb24gY29uZmlnXG4gICAgICBleHBlY3QoY29uZmlnLndpbmRvd01zKS50b0JlKDE1ICogNjAgKiAxMDAwKTsgLy8gUHJvZHVjdGlvbiBjb25maWdcbiAgICB9KTtcblxuICAgIHRlc3QoJ3Nob3VsZCByZXR1cm4gcHJvZHVjdGlvbiBjb25maWcgd2hlbiBpbiBkZXZlbG9wbWVudCB3aXRoIHJhdGUgbGltaXRpbmcgZGlzYWJsZWQnLCAoKSA9PiB7XG4gICAgICBPYmplY3QuYXNzaWduKHByb2Nlc3MuZW52LCB7IE5PREVfRU5WOiAnZGV2ZWxvcG1lbnQnLCBFTkFCTEVfREVWX1JBVEVfTElNSVQ6ICdmYWxzZScgfSk7XG5cbiAgICAgIGNvbnN0IGNvbmZpZyA9IGdldFJhdGVMaW1pdENvbmZpZygnYXV0aCcpO1xuXG4gICAgICBleHBlY3QoY29uZmlnKS50b0VxdWFsKHJhdGVMaW1pdENvbmZpZ3MuYXV0aCk7XG4gICAgfSk7XG4gIH0pO1xuXG4gIGRlc2NyaWJlKCdEZXZlbG9wbWVudCBjb25maWd1cmF0aW9uIHZhbGlkYXRpb24nLCAoKSA9PiB7XG4gICAgdGVzdCgnZGV2IGNvbmZpZ3Mgc2hvdWxkIGhhdmUgbW9yZSBsZW5pZW50IGxpbWl0cyB0aGFuIHByb2R1Y3Rpb24nLCAoKSA9PiB7XG4gICAgICBjb25zdCBjb25maWdUeXBlczogKGtleW9mIHR5cGVvZiByYXRlTGltaXRDb25maWdzKVtdID0gWydhdXRoJywgJ2FwaScsICdjb250YWN0JywgJ3NpZ251cCddO1xuXG4gICAgICBjb25maWdUeXBlcy5mb3JFYWNoKHR5cGUgPT4ge1xuICAgICAgICBjb25zdCBwcm9kQ29uZmlnID0gcmF0ZUxpbWl0Q29uZmlnc1t0eXBlXTtcbiAgICAgICAgY29uc3QgZGV2Q29uZmlnID0gZGV2UmF0ZUxpbWl0Q29uZmlnc1t0eXBlXTtcblxuICAgICAgICBleHBlY3QoZGV2Q29uZmlnLm1heFJlcXVlc3RzKS50b0JlR3JlYXRlclRoYW4ocHJvZENvbmZpZy5tYXhSZXF1ZXN0cyk7XG4gICAgICAgIGV4cGVjdChkZXZDb25maWcud2luZG93TXMpLnRvQmVMZXNzVGhhbk9yRXF1YWwocHJvZENvbmZpZy53aW5kb3dNcyk7XG4gICAgICAgIGV4cGVjdChkZXZDb25maWcubWVzc2FnZSkudG9Db250YWluKCcoRGV2IE1vZGUpJyk7XG4gICAgICB9KTtcbiAgICB9KTtcbiAgfSk7XG59KTtcbiJdLCJ2ZXJzaW9uIjozfQ==