{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/__tests__/dev-rate-limiting.test.ts", "mappings": ";AAAA,iFAAiF;AACjF,mDAAmD;AAEnD,8CAA8C;AAC9C,IAAM,gBAAgB,GAAG;IACvB,IAAI,EAAE;QACJ,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;QACvC,WAAW,EAAE,CAAC;QACd,OAAO,EAAE,2DAA2D;KACrE;IACD,GAAG,EAAE;QACH,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;QACvC,WAAW,EAAE,GAAG;QAChB,OAAO,EAAE,gDAAgD;KAC1D;IACD,OAAO,EAAE;QACP,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,SAAS;QACnC,WAAW,EAAE,CAAC;QACd,OAAO,EAAE,4DAA4D;KACtE;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,SAAS;QACnC,WAAW,EAAE,CAAC;QACd,OAAO,EAAE,mDAAmD;KAC7D;CACF,CAAC;AAEF,IAAM,mBAAmB,GAAG;IAC1B,IAAI,EAAE;QACJ,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,sBAAsB;QAC/C,WAAW,EAAE,EAAE,EAAE,sBAAsB;QACvC,OAAO,EAAE,sEAAsE;KAChF;IACD,GAAG,EAAE;QACH,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,sBAAsB;QAC/C,WAAW,EAAE,GAAG,EAAE,sBAAsB;QACxC,OAAO,EAAE,2DAA2D;KACrE;IACD,OAAO,EAAE;QACP,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,uBAAuB;QACjD,WAAW,EAAE,CAAC,EAAE,sBAAsB;QACtC,OAAO,EAAE,uEAAuE;KACjF;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,uBAAuB;QACjD,WAAW,EAAE,EAAE,EAAE,sBAAsB;QACvC,OAAO,EAAE,8DAA8D;KACxE;CACF,CAAC;AAEF,uCAAuC;AACvC,SAAS,kBAAkB,CAAC,IAAmC;IAC7D,IAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,KAAK,MAAM,CAAC;IACxE,IAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC;IAE7D,IAAI,aAAa,IAAI,kBAAkB,EAAE,CAAC;QACxC,OAAO,mBAAmB,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAChC,CAAC;AAED,QAAQ,CAAC,yCAAyC,EAAE;IAClD,IAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC;IAEhC,UAAU,CAAC;QACT,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC;QACR,+BAA+B;QAC/B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG;YAClC,IAAI,CAAC,CAAC,GAAG,IAAI,WAAW,CAAC,EAAE,CAAC;gBAC1B,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE;QAChC,IAAI,CAAC,yEAAyE,EAAE;YAC9E,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,aAAa,EAAE,qBAAqB,EAAE,MAAM,EAAE,CAAC,CAAC;YAEvF,IAAM,MAAM,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAE1C,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,qCAAqC;YAC1E,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,gCAAgC;QAC/E,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,oDAAoD,EAAE;YACzD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC;YAEvD,IAAM,MAAM,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAE1C,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB;YACxD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,oBAAoB;QACpE,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,iFAAiF,EAAE;YACtF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,aAAa,EAAE,qBAAqB,EAAE,OAAO,EAAE,CAAC,CAAC;YAExF,IAAM,MAAM,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAE1C,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sCAAsC,EAAE;QAC/C,IAAI,CAAC,6DAA6D,EAAE;YAClE,IAAM,WAAW,GAAsC,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;YAE5F,WAAW,CAAC,OAAO,CAAC,UAAA,IAAI;gBACtB,IAAM,UAAU,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBAC1C,IAAM,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAE5C,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;gBACtE,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACpE,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/__tests__/dev-rate-limiting.test.ts"], "sourcesContent": ["// Test development rate limiting configuration without importing the full module\n// to avoid Jest issues with next-auth dependencies\n\n// Mock the rate limit configurations directly\nconst rateLimitConfigs = {\n  auth: {\n    windowMs: 15 * 60 * 1000, // 15 minutes\n    maxRequests: 5,\n    message: 'Too many authentication attempts. Please try again later.'\n  },\n  api: {\n    windowMs: 15 * 60 * 1000, // 15 minutes\n    maxRequests: 100,\n    message: 'Too many API requests. Please try again later.'\n  },\n  contact: {\n    windowMs: 60 * 60 * 1000, // 1 hour\n    maxRequests: 3,\n    message: 'Too many contact form submissions. Please try again later.'\n  },\n  signup: {\n    windowMs: 60 * 60 * 1000, // 1 hour\n    maxRequests: 5,\n    message: 'Too many signup attempts. Please try again later.'\n  }\n};\n\nconst devRateLimitConfigs = {\n  auth: {\n    windowMs: 5 * 60 * 1000, // 5 minutes (shorter)\n    maxRequests: 10, // Double the requests\n    message: 'Too many authentication attempts. Please try again later. (Dev Mode)'\n  },\n  api: {\n    windowMs: 5 * 60 * 1000, // 5 minutes (shorter)\n    maxRequests: 200, // Double the requests\n    message: 'Too many API requests. Please try again later. (Dev Mode)'\n  },\n  contact: {\n    windowMs: 30 * 60 * 1000, // 30 minutes (shorter)\n    maxRequests: 6, // Double the requests\n    message: 'Too many contact form submissions. Please try again later. (Dev Mode)'\n  },\n  signup: {\n    windowMs: 30 * 60 * 1000, // 30 minutes (shorter)\n    maxRequests: 10, // Double the requests\n    message: 'Too many signup attempts. Please try again later. (Dev Mode)'\n  }\n};\n\n// Mock the getRateLimitConfig function\nfunction getRateLimitConfig(type: keyof typeof rateLimitConfigs) {\n  const enableDevRateLimit = process.env.ENABLE_DEV_RATE_LIMIT === 'true';\n  const isDevelopment = process.env.NODE_ENV === 'development';\n\n  if (isDevelopment && enableDevRateLimit) {\n    return devRateLimitConfigs[type];\n  }\n\n  return rateLimitConfigs[type];\n}\n\ndescribe('Development Rate Limiting Configuration', () => {\n  const originalEnv = process.env;\n\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  afterEach(() => {\n    // Restore original environment\n    Object.keys(process.env).forEach(key => {\n      if (!(key in originalEnv)) {\n        delete process.env[key];\n      }\n    });\n    Object.assign(process.env, originalEnv);\n  });\n\n  describe('Configuration helpers', () => {\n    test('should return dev config when in development with rate limiting enabled', () => {\n      Object.assign(process.env, { NODE_ENV: 'development', ENABLE_DEV_RATE_LIMIT: 'true' });\n\n      const config = getRateLimitConfig('auth');\n\n      expect(config).toEqual(devRateLimitConfigs.auth);\n      expect(config.maxRequests).toBe(10); // Dev config has double the requests\n      expect(config.windowMs).toBe(5 * 60 * 1000); // Dev config has shorter window\n    });\n\n    test('should return production config when in production', () => {\n      Object.assign(process.env, { NODE_ENV: 'production' });\n\n      const config = getRateLimitConfig('auth');\n\n      expect(config).toEqual(rateLimitConfigs.auth);\n      expect(config.maxRequests).toBe(5); // Production config\n      expect(config.windowMs).toBe(15 * 60 * 1000); // Production config\n    });\n\n    test('should return production config when in development with rate limiting disabled', () => {\n      Object.assign(process.env, { NODE_ENV: 'development', ENABLE_DEV_RATE_LIMIT: 'false' });\n\n      const config = getRateLimitConfig('auth');\n\n      expect(config).toEqual(rateLimitConfigs.auth);\n    });\n  });\n\n  describe('Development configuration validation', () => {\n    test('dev configs should have more lenient limits than production', () => {\n      const configTypes: (keyof typeof rateLimitConfigs)[] = ['auth', 'api', 'contact', 'signup'];\n\n      configTypes.forEach(type => {\n        const prodConfig = rateLimitConfigs[type];\n        const devConfig = devRateLimitConfigs[type];\n\n        expect(devConfig.maxRequests).toBeGreaterThan(prodConfig.maxRequests);\n        expect(devConfig.windowMs).toBeLessThanOrEqual(prodConfig.windowMs);\n        expect(devConfig.message).toContain('(Dev Mode)');\n      });\n    });\n  });\n});\n"], "version": 3}