4eabbd8bcb2836aa628cb4ee3912bc6c
"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var globals_1 = require("@jest/globals");
// Mock dependencies
globals_1.jest.mock('@/lib/prisma', function () { return ({
    prisma: {
        skillAssessment: {
            findMany: globals_1.jest.fn(),
        },
        careerPath: {
            findFirst: globals_1.jest.fn(),
        },
        skillGapAnalysis: {
            create: globals_1.jest.fn(),
        },
    },
}); });
globals_1.jest.mock('next-auth', function () { return ({
    getServerSession: globals_1.jest.fn(),
}); });
globals_1.jest.mock('@/lib/services/geminiService', function () { return ({
    geminiService: {
        analyzeComprehensiveSkillGap: globals_1.jest.fn(),
    },
}); });
globals_1.jest.mock('@/lib/services/consolidated-cache-service', function () { return ({
    consolidatedCache: {
        get: globals_1.jest.fn(),
        set: globals_1.jest.fn(),
        generateAIKey: globals_1.jest.fn(),
    },
}); });
globals_1.jest.mock('@/lib/auth', function () { return ({
    authOptions: {},
}); });
globals_1.jest.mock('@/lib/errorHandler', function () { return ({
    withErrorHandler: function (handler) { return handler; },
}); });
globals_1.jest.mock('@/lib/rateLimit', function () { return ({
    withRateLimit: function (request, config, handler) { return handler(); },
}); });
globals_1.jest.mock('@/lib/csrf', function () { return ({
    withCSRFProtection: function (request, handler) { return handler(); },
}); });
var server_1 = require("next/server");
var route_1 = require("@/app/api/ai/skills-analysis/comprehensive/route");
var prisma_1 = require("@/lib/prisma");
var next_auth_1 = require("next-auth");
var geminiService_1 = require("@/lib/services/geminiService");
var consolidated_cache_service_1 = require("@/lib/services/consolidated-cache-service");
var mockPrisma = prisma_1.prisma;
var mockGetServerSession = next_auth_1.getServerSession;
var mockGeminiService = geminiService_1.geminiService;
var mockConsolidatedCache = consolidated_cache_service_1.consolidatedCache;
(0, globals_1.describe)('Comprehensive Skills Analysis API', function () {
    var mockUserId = 'test-user-id';
    (0, globals_1.beforeEach)(function () {
        globals_1.jest.clearAllMocks();
        mockGetServerSession.mockResolvedValue({
            user: { id: mockUserId },
        });
    });
    (0, globals_1.afterEach)(function () {
        globals_1.jest.resetAllMocks();
    });
    (0, globals_1.describe)('POST /api/ai/skills-analysis/comprehensive', function () {
        var validRequestData = {
            currentSkills: [
                {
                    skillName: 'JavaScript',
                    selfRating: 7,
                    confidenceLevel: 8,
                    yearsOfExperience: 3,
                },
                {
                    skillName: 'React',
                    selfRating: 6,
                    confidenceLevel: 7,
                    yearsOfExperience: 2,
                },
            ],
            targetCareerPath: {
                careerPathName: 'Full Stack Developer',
                targetLevel: 'ADVANCED',
            },
            preferences: {
                timeframe: 'ONE_YEAR',
                hoursPerWeek: 10,
                learningStyle: ['VISUAL', 'HANDS_ON'],
                budget: 'FREEMIUM',
                focusAreas: ['Backend Development', 'Database Design'],
            },
            includeMarketData: true,
            includePersonalizedPaths: true,
        };
        (0, globals_1.it)('should perform comprehensive skills analysis successfully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockCareerPathData, mockAIResponse, mockSkillGapAnalysis, request, response, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockCareerPathData = {
                            id: 'fallback-career-path-id',
                            name: 'Full Stack Developer',
                            requiredSkills: [],
                            learningResources: [],
                            learningPaths: [],
                        };
                        mockAIResponse = {
                            success: true,
                            data: {
                                skillGaps: [
                                    {
                                        skillId: 'skill-2',
                                        skillName: 'Node.js',
                                        currentLevel: 3,
                                        targetLevel: 8,
                                        gapSeverity: 'HIGH',
                                        priority: 85,
                                        estimatedLearningTime: 120,
                                        marketDemand: 'VERY_HIGH',
                                        salaryImpact: 15,
                                    },
                                    {
                                        skillId: 'skill-3',
                                        skillName: 'PostgreSQL',
                                        currentLevel: 2,
                                        targetLevel: 7,
                                        gapSeverity: 'CRITICAL',
                                        priority: 95,
                                        estimatedLearningTime: 80,
                                        marketDemand: 'HIGH',
                                        salaryImpact: 12,
                                    },
                                ],
                                learningPlan: {
                                    totalEstimatedHours: 200,
                                    milestones: [
                                        {
                                            month: 3,
                                            skills: ['Node.js Basics'],
                                            estimatedHours: 60,
                                            learningPaths: ['Backend Development Path'],
                                        },
                                        {
                                            month: 6,
                                            skills: ['PostgreSQL Fundamentals'],
                                            estimatedHours: 40,
                                            learningPaths: ['Database Design Path'],
                                        },
                                    ],
                                    recommendedResources: [
                                        {
                                            resourceId: 'resource-1',
                                            resourceType: 'COURSE',
                                            priority: 'HIGH',
                                            skillsAddressed: ['Node.js'],
                                            estimatedHours: 60,
                                        },
                                    ],
                                },
                                careerReadiness: {
                                    currentScore: 65,
                                    targetScore: 85,
                                    improvementPotential: 20,
                                    timeToTarget: 8,
                                },
                                marketInsights: {
                                    industryTrends: [
                                        {
                                            skill: 'Node.js',
                                            trend: 'GROWING',
                                            demandLevel: 'VERY_HIGH',
                                        },
                                    ],
                                    salaryProjections: {
                                        currentEstimate: 75000,
                                        targetEstimate: 95000,
                                        improvementPotential: 26.7,
                                    },
                                },
                            },
                        };
                        mockSkillGapAnalysis = {
                            id: 'analysis-id',
                            userId: mockUserId,
                            targetCareerPathName: 'Full Stack Developer',
                            status: 'ACTIVE',
                            createdAt: new Date(),
                        };
                        mockConsolidatedCache.get.mockResolvedValue(null); // No cache
                        mockPrisma.skillAssessment.findMany.mockResolvedValue([]);
                        mockPrisma.careerPath.findFirst.mockResolvedValue(mockCareerPathData);
                        mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue(mockAIResponse);
                        mockPrisma.skillGapAnalysis.create.mockResolvedValue(mockSkillGapAnalysis);
                        request = new server_1.NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
                            method: 'POST',
                            body: JSON.stringify(validRequestData),
                            headers: { 'Content-Type': 'application/json' },
                        });
                        return [4 /*yield*/, (0, route_1.POST)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(response.status).toBe(200);
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.analysisId).toBe('analysis-id');
                        (0, globals_1.expect)(result.data.skillGaps).toHaveLength(2);
                        (0, globals_1.expect)(result.data.skillGaps[0].skillName).toBe('Node.js');
                        (0, globals_1.expect)(result.data.skillGaps[1].skillName).toBe('PostgreSQL');
                        (0, globals_1.expect)(result.data.learningPlan.totalEstimatedHours).toBe(200);
                        (0, globals_1.expect)(result.data.careerReadiness.currentScore).toBe(65);
                        (0, globals_1.expect)(result.data.marketInsights).toBeDefined();
                        (0, globals_1.expect)(result.cached).toBe(false);
                        // Verify AI service was called with correct parameters
                        (0, globals_1.expect)(mockGeminiService.analyzeComprehensiveSkillGap).toHaveBeenCalledWith(globals_1.expect.arrayContaining([
                            globals_1.expect.objectContaining({ skillName: 'JavaScript' }),
                            globals_1.expect.objectContaining({ skillName: 'React' }),
                        ]), globals_1.expect.objectContaining({
                            careerPathName: 'Full Stack Developer',
                            targetLevel: 'ADVANCED',
                        }), globals_1.expect.objectContaining({
                            timeframe: 'ONE_YEAR',
                            hoursPerWeek: 10,
                        }), globals_1.expect.objectContaining({
                            id: 'fallback-career-path-id',
                            name: 'Full Stack Developer',
                            requiredSkills: [],
                            learningResources: [],
                            learningPaths: [],
                        }), mockUserId);
                        // Verify skill gap analysis was created
                        (0, globals_1.expect)(mockPrisma.skillGapAnalysis.create).toHaveBeenCalledWith({
                            data: globals_1.expect.objectContaining({
                                userId: mockUserId,
                                targetCareerPathName: 'Full Stack Developer',
                                experienceLevel: 'ADVANCED',
                                timeframe: 'ONE_YEAR',
                                status: 'ACTIVE',
                            }),
                        });
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should return cached results when available', function () { return __awaiter(void 0, void 0, void 0, function () {
            var cachedData, request, response, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        cachedData = {
                            analysisId: 'cached-analysis-id',
                            skillGaps: [],
                            learningPlan: { totalEstimatedHours: 0, milestones: [], recommendedResources: [] },
                            careerReadiness: { currentScore: 70, targetScore: 85, improvementPotential: 15, timeToTarget: 6 },
                            generatedAt: '2024-01-01T00:00:00.000Z',
                        };
                        mockConsolidatedCache.get.mockResolvedValue(cachedData);
                        request = new server_1.NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
                            method: 'POST',
                            body: JSON.stringify(validRequestData),
                            headers: { 'Content-Type': 'application/json' },
                        });
                        return [4 /*yield*/, (0, route_1.POST)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(response.status).toBe(200);
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data).toEqual(cachedData);
                        (0, globals_1.expect)(result.cached).toBe(true);
                        (0, globals_1.expect)(result.generatedAt).toBe('2024-01-01T00:00:00.000Z');
                        // Verify AI service was not called
                        (0, globals_1.expect)(mockGeminiService.analyzeComprehensiveSkillGap).not.toHaveBeenCalled();
                        (0, globals_1.expect)(mockPrisma.skillGapAnalysis.create).not.toHaveBeenCalled();
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should fail with invalid request data', function () { return __awaiter(void 0, void 0, void 0, function () {
            var invalidRequestData, request, response, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        invalidRequestData = {
                            currentSkills: [], // Empty array - should fail validation
                            targetCareerPath: {
                                careerPathName: 'A', // Too short
                                targetLevel: 'INVALID_LEVEL', // Invalid enum value
                            },
                            preferences: {
                                timeframe: 'INVALID_TIMEFRAME',
                                hoursPerWeek: -5, // Negative hours
                                learningStyle: [],
                                budget: 'INVALID_BUDGET',
                                focusAreas: [],
                            },
                        };
                        request = new server_1.NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
                            method: 'POST',
                            body: JSON.stringify(invalidRequestData),
                            headers: { 'Content-Type': 'application/json' },
                        });
                        return [4 /*yield*/, (0, route_1.POST)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(response.status).toBe(400);
                        (0, globals_1.expect)(result.success).toBe(false);
                        (0, globals_1.expect)(result.error).toBe('Invalid request data');
                        (0, globals_1.expect)(result.details).toBeDefined();
                        (0, globals_1.expect)(Array.isArray(result.details)).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should handle AI service failures gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, response, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // Arrange
                        mockConsolidatedCache.get.mockResolvedValue(null);
                        mockPrisma.skillAssessment.findMany.mockResolvedValue([]);
                        mockPrisma.careerPath.findFirst.mockResolvedValue(null);
                        mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue({
                            success: false,
                            error: 'AI service temporarily unavailable',
                        });
                        request = new server_1.NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
                            method: 'POST',
                            body: JSON.stringify(validRequestData),
                            headers: { 'Content-Type': 'application/json' },
                        });
                        return [4 /*yield*/, (0, route_1.POST)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(response.status).toBe(500);
                        (0, globals_1.expect)(result.success).toBe(false);
                        (0, globals_1.expect)(result.error).toBe('AI service temporarily unavailable');
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should require authentication', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, response, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // Arrange
                        mockGetServerSession.mockResolvedValue(null);
                        request = new server_1.NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
                            method: 'POST',
                            body: JSON.stringify(validRequestData),
                            headers: { 'Content-Type': 'application/json' },
                        });
                        return [4 /*yield*/, (0, route_1.POST)(request)];
                    case 1:
                        response = _a.sent();
                        return [4 /*yield*/, response.json()];
                    case 2:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(response.status).toBe(401);
                        (0, globals_1.expect)(result.success).toBe(false);
                        (0, globals_1.expect)(result.error).toBe('Authentication required');
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should merge existing skill assessments with current skills', function () { return __awaiter(void 0, void 0, void 0, function () {
            var existingAssessments, request, response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        existingAssessments = [
                            {
                                skillId: 'skill-1',
                                skill: { name: 'TypeScript' },
                                selfRating: 8,
                                confidenceLevel: 9,
                                assessmentDate: new Date(),
                                assessmentType: 'SELF_ASSESSMENT',
                            },
                        ];
                        mockConsolidatedCache.get.mockResolvedValue(null);
                        mockPrisma.skillAssessment.findMany.mockResolvedValue(existingAssessments);
                        mockPrisma.careerPath.findFirst.mockResolvedValue(null);
                        mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue({
                            success: true,
                            data: {
                                skillGaps: [],
                                learningPlan: { totalEstimatedHours: 0, milestones: [], recommendedResources: [] },
                                careerReadiness: { currentScore: 70, targetScore: 85, improvementPotential: 15, timeToTarget: 6 },
                            },
                        });
                        mockPrisma.skillGapAnalysis.create.mockResolvedValue({ id: 'analysis-id' });
                        request = new server_1.NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
                            method: 'POST',
                            body: JSON.stringify(validRequestData),
                            headers: { 'Content-Type': 'application/json' },
                        });
                        return [4 /*yield*/, (0, route_1.POST)(request)];
                    case 1:
                        response = _a.sent();
                        // Assert
                        (0, globals_1.expect)(response.status).toBe(200);
                        // Verify that AI service was called with merged skills (including TypeScript from assessments)
                        (0, globals_1.expect)(mockGeminiService.analyzeComprehensiveSkillGap).toHaveBeenCalledWith(globals_1.expect.arrayContaining([
                            globals_1.expect.objectContaining({ skillName: 'JavaScript' }),
                            globals_1.expect.objectContaining({ skillName: 'React' }),
                            globals_1.expect.objectContaining({ skillName: 'TypeScript' }),
                        ]), globals_1.expect.any(Object), globals_1.expect.any(Object), globals_1.expect.objectContaining({
                            id: 'fallback-career-path-id',
                            name: 'Full Stack Developer',
                        }), mockUserId);
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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