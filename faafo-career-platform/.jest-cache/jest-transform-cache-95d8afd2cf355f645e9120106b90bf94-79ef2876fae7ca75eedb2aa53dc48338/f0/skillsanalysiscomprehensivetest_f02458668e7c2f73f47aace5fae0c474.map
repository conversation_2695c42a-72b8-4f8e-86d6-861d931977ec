{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/api/ai/skills-analysis-comprehensive.test.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yCAAkF;AAQlF,oBAAoB;AACpB,cAAI,CAAC,IAAI,CAAC,cAAc,EAAE,cAAM,OAAA,CAAC;IAC/B,MAAM,EAAE;QACN,eAAe,EAAE;YACf,QAAQ,EAAE,cAAI,CAAC,EAAE,EAAE;SACpB;QACD,UAAU,EAAE;YACV,SAAS,EAAE,cAAI,CAAC,EAAE,EAAE;SACrB;QACD,gBAAgB,EAAE;YAChB,MAAM,EAAE,cAAI,CAAC,EAAE,EAAE;SAClB;KACF;CACF,CAAC,EAZ8B,CAY9B,CAAC,CAAC;AAEJ,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAM,OAAA,CAAC;IAC5B,gBAAgB,EAAE,cAAI,CAAC,EAAE,EAAE;CAC5B,CAAC,EAF2B,CAE3B,CAAC,CAAC;AAEJ,cAAI,CAAC,IAAI,CAAC,8BAA8B,EAAE,cAAM,OAAA,CAAC;IAC/C,aAAa,EAAE;QACb,4BAA4B,EAAE,cAAI,CAAC,EAAE,EAAE;KACxC;CACF,CAAC,EAJ8C,CAI9C,CAAC,CAAC;AAEJ,cAAI,CAAC,IAAI,CAAC,2CAA2C,EAAE,cAAM,OAAA,CAAC;IAC5D,iBAAiB,EAAE;QACjB,GAAG,EAAE,cAAI,CAAC,EAAE,EAAE;QACd,GAAG,EAAE,cAAI,CAAC,EAAE,EAAE;QACd,aAAa,EAAE,cAAI,CAAC,EAAE,EAAE;KACzB;CACF,CAAC,EAN2D,CAM3D,CAAC,CAAC;AAEJ,cAAI,CAAC,IAAI,CAAC,YAAY,EAAE,cAAM,OAAA,CAAC;IAC7B,WAAW,EAAE,EAAE;CAChB,CAAC,EAF4B,CAE5B,CAAC,CAAC;AAEJ,cAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,cAAM,OAAA,CAAC;IACrC,gBAAgB,EAAE,UAAC,OAAY,IAAK,OAAA,OAAO,EAAP,CAAO;CAC5C,CAAC,EAFoC,CAEpC,CAAC,CAAC;AAEJ,cAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,cAAM,OAAA,CAAC;IAClC,aAAa,EAAE,UAAC,OAAY,EAAE,MAAW,EAAE,OAAY,IAAK,OAAA,OAAO,EAAE,EAAT,CAAS;CACtE,CAAC,EAFiC,CAEjC,CAAC,CAAC;AAEJ,cAAI,CAAC,IAAI,CAAC,YAAY,EAAE,cAAM,OAAA,CAAC;IAC7B,kBAAkB,EAAE,UAAC,OAAY,EAAE,OAAY,IAAK,OAAA,OAAO,EAAE,EAAT,CAAS;CAC9D,CAAC,EAF4B,CAE5B,CAAC,CAAC;AAtDJ,sCAA0C;AAC1C,0EAAwE;AACxE,uCAAsC;AACtC,uCAA6C;AAC7C,8DAA6D;AAC7D,wFAA8E;AAmD9E,IAAM,UAAU,GAAG,eAAoC,CAAC;AACxD,IAAM,oBAAoB,GAAG,4BAAgE,CAAC;AAC9F,IAAM,iBAAiB,GAAG,6BAAkD,CAAC;AAC7E,IAAM,qBAAqB,GAAG,8CAA0D,CAAC;AAEzF,IAAA,kBAAQ,EAAC,mCAAmC,EAAE;IAC5C,IAAM,UAAU,GAAG,cAAc,CAAC;IAElC,IAAA,oBAAU,EAAC;QACT,cAAI,CAAC,aAAa,EAAE,CAAC;QACrB,oBAAoB,CAAC,iBAAiB,CAAC;YACrC,IAAI,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;SAClB,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC;IAEH,IAAA,mBAAS,EAAC;QACR,cAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,4CAA4C,EAAE;QACrD,IAAM,gBAAgB,GAAG;YACvB,aAAa,EAAE;gBACb;oBACE,SAAS,EAAE,YAAY;oBACvB,UAAU,EAAE,CAAC;oBACb,eAAe,EAAE,CAAC;oBAClB,iBAAiB,EAAE,CAAC;iBACrB;gBACD;oBACE,SAAS,EAAE,OAAO;oBAClB,UAAU,EAAE,CAAC;oBACb,eAAe,EAAE,CAAC;oBAClB,iBAAiB,EAAE,CAAC;iBACrB;aACF;YACD,gBAAgB,EAAE;gBAChB,cAAc,EAAE,sBAAsB;gBACtC,WAAW,EAAE,UAAmB;aACjC;YACD,WAAW,EAAE;gBACX,SAAS,EAAE,UAAmB;gBAC9B,YAAY,EAAE,EAAE;gBAChB,aAAa,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;gBACrC,MAAM,EAAE,UAAmB;gBAC3B,UAAU,EAAE,CAAC,qBAAqB,EAAE,iBAAiB,CAAC;aACvD;YACD,iBAAiB,EAAE,IAAI;YACvB,wBAAwB,EAAE,IAAI;SAC/B,CAAC;QAEF,IAAA,YAAE,EAAC,2DAA2D,EAAE;;;;;wBAExD,kBAAkB,GAAG;4BACzB,EAAE,EAAE,yBAAyB;4BAC7B,IAAI,EAAE,sBAAsB;4BAC5B,cAAc,EAAE,EAAE;4BAClB,iBAAiB,EAAE,EAAE;4BACrB,aAAa,EAAE,EAAE;yBAClB,CAAC;wBAEI,cAAc,GAAG;4BACrB,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE;gCACJ,SAAS,EAAE;oCACT;wCACE,OAAO,EAAE,SAAS;wCAClB,SAAS,EAAE,SAAS;wCACpB,YAAY,EAAE,CAAC;wCACf,WAAW,EAAE,CAAC;wCACd,WAAW,EAAE,MAAM;wCACnB,QAAQ,EAAE,EAAE;wCACZ,qBAAqB,EAAE,GAAG;wCAC1B,YAAY,EAAE,WAAW;wCACzB,YAAY,EAAE,EAAE;qCACjB;oCACD;wCACE,OAAO,EAAE,SAAS;wCAClB,SAAS,EAAE,YAAY;wCACvB,YAAY,EAAE,CAAC;wCACf,WAAW,EAAE,CAAC;wCACd,WAAW,EAAE,UAAU;wCACvB,QAAQ,EAAE,EAAE;wCACZ,qBAAqB,EAAE,EAAE;wCACzB,YAAY,EAAE,MAAM;wCACpB,YAAY,EAAE,EAAE;qCACjB;iCACF;gCACD,YAAY,EAAE;oCACZ,mBAAmB,EAAE,GAAG;oCACxB,UAAU,EAAE;wCACV;4CACE,KAAK,EAAE,CAAC;4CACR,MAAM,EAAE,CAAC,gBAAgB,CAAC;4CAC1B,cAAc,EAAE,EAAE;4CAClB,aAAa,EAAE,CAAC,0BAA0B,CAAC;yCAC5C;wCACD;4CACE,KAAK,EAAE,CAAC;4CACR,MAAM,EAAE,CAAC,yBAAyB,CAAC;4CACnC,cAAc,EAAE,EAAE;4CAClB,aAAa,EAAE,CAAC,sBAAsB,CAAC;yCACxC;qCACF;oCACD,oBAAoB,EAAE;wCACpB;4CACE,UAAU,EAAE,YAAY;4CACxB,YAAY,EAAE,QAAQ;4CACtB,QAAQ,EAAE,MAAM;4CAChB,eAAe,EAAE,CAAC,SAAS,CAAC;4CAC5B,cAAc,EAAE,EAAE;yCACnB;qCACF;iCACF;gCACD,eAAe,EAAE;oCACf,YAAY,EAAE,EAAE;oCAChB,WAAW,EAAE,EAAE;oCACf,oBAAoB,EAAE,EAAE;oCACxB,YAAY,EAAE,CAAC;iCAChB;gCACD,cAAc,EAAE;oCACd,cAAc,EAAE;wCACd;4CACE,KAAK,EAAE,SAAS;4CAChB,KAAK,EAAE,SAAS;4CAChB,WAAW,EAAE,WAAW;yCACzB;qCACF;oCACD,iBAAiB,EAAE;wCACjB,eAAe,EAAE,KAAK;wCACtB,cAAc,EAAE,KAAK;wCACrB,oBAAoB,EAAE,IAAI;qCAC3B;iCACF;6BACF;yBACF,CAAC;wBAEI,oBAAoB,GAAG;4BAC3B,EAAE,EAAE,aAAa;4BACjB,MAAM,EAAE,UAAU;4BAClB,oBAAoB,EAAE,sBAAsB;4BAC5C,MAAM,EAAE,QAAQ;4BAChB,SAAS,EAAE,IAAI,IAAI,EAAE;yBACtB,CAAC;wBAEF,qBAAqB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;wBAC9D,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;wBAC1D,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,kBAAyB,CAAC,CAAC;wBAC7E,iBAAiB,CAAC,4BAA4B,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;wBACjF,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,iBAAiB,CAAC,oBAA2B,CAAC,CAAC;wBAE5E,OAAO,GAAG,IAAI,oBAAW,CAAC,uDAAuD,EAAE;4BACvF,MAAM,EAAE,MAAM;4BACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;4BACtC,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;yBAChD,CAAC,CAAC;wBAGc,qBAAM,IAAA,YAAI,EAAC,OAAO,CAAC,EAAA;;wBAA9B,QAAQ,GAAG,SAAmB;wBACrB,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA9B,MAAM,GAAG,SAAqB;wBAEpC,SAAS;wBACT,IAAA,gBAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAClC,IAAA,gBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;wBACnD,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBAC9C,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAC3D,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAC9D,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAC/D,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;wBAC1D,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;wBACjD,IAAA,gBAAM,EAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBAElC,uDAAuD;wBACvD,IAAA,gBAAM,EAAC,iBAAiB,CAAC,4BAA4B,CAAC,CAAC,oBAAoB,CACzE,gBAAM,CAAC,eAAe,CAAC;4BACrB,gBAAM,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC;4BACpD,gBAAM,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;yBAChD,CAAC,EACF,gBAAM,CAAC,gBAAgB,CAAC;4BACtB,cAAc,EAAE,sBAAsB;4BACtC,WAAW,EAAE,UAAU;yBACxB,CAAC,EACF,gBAAM,CAAC,gBAAgB,CAAC;4BACtB,SAAS,EAAE,UAAU;4BACrB,YAAY,EAAE,EAAE;yBACjB,CAAC,EACF,gBAAM,CAAC,gBAAgB,CAAC;4BACtB,EAAE,EAAE,yBAAyB;4BAC7B,IAAI,EAAE,sBAAsB;4BAC5B,cAAc,EAAE,EAAE;4BAClB,iBAAiB,EAAE,EAAE;4BACrB,aAAa,EAAE,EAAE;yBAClB,CAAC,EACF,UAAU,CACX,CAAC;wBAEF,wCAAwC;wBACxC,IAAA,gBAAM,EAAC,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC;4BAC9D,IAAI,EAAE,gBAAM,CAAC,gBAAgB,CAAC;gCAC5B,MAAM,EAAE,UAAU;gCAClB,oBAAoB,EAAE,sBAAsB;gCAC5C,eAAe,EAAE,UAAU;gCAC3B,SAAS,EAAE,UAAU;gCACrB,MAAM,EAAE,QAAQ;6BACjB,CAAC;yBACH,CAAC,CAAC;;;;aACJ,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,6CAA6C,EAAE;;;;;wBAE1C,UAAU,GAAG;4BACjB,UAAU,EAAE,oBAAoB;4BAChC,SAAS,EAAE,EAAE;4BACb,YAAY,EAAE,EAAE,mBAAmB,EAAE,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,oBAAoB,EAAE,EAAE,EAAE;4BAClF,eAAe,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,oBAAoB,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC,EAAE;4BACjG,WAAW,EAAE,0BAA0B;yBACxC,CAAC;wBAEF,qBAAqB,CAAC,GAAG,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;wBAElD,OAAO,GAAG,IAAI,oBAAW,CAAC,uDAAuD,EAAE;4BACvF,MAAM,EAAE,MAAM;4BACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;4BACtC,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;yBAChD,CAAC,CAAC;wBAGc,qBAAM,IAAA,YAAI,EAAC,OAAO,CAAC,EAAA;;wBAA9B,QAAQ,GAAG,SAAmB;wBACrB,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA9B,MAAM,GAAG,SAAqB;wBAEpC,SAAS;wBACT,IAAA,gBAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAClC,IAAA,gBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,IAAA,gBAAM,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;wBACxC,IAAA,gBAAM,EAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACjC,IAAA,gBAAM,EAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;wBAE5D,mCAAmC;wBACnC,IAAA,gBAAM,EAAC,iBAAiB,CAAC,4BAA4B,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;wBAC9E,IAAA,gBAAM,EAAC,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;;;;aACnE,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,uCAAuC,EAAE;;;;;wBAEpC,kBAAkB,GAAG;4BACzB,aAAa,EAAE,EAAE,EAAE,uCAAuC;4BAC1D,gBAAgB,EAAE;gCAChB,cAAc,EAAE,GAAG,EAAE,YAAY;gCACjC,WAAW,EAAE,eAAe,EAAE,qBAAqB;6BACpD;4BACD,WAAW,EAAE;gCACX,SAAS,EAAE,mBAAmB;gCAC9B,YAAY,EAAE,CAAC,CAAC,EAAE,iBAAiB;gCACnC,aAAa,EAAE,EAAE;gCACjB,MAAM,EAAE,gBAAgB;gCACxB,UAAU,EAAE,EAAE;6BACf;yBACF,CAAC;wBAEI,OAAO,GAAG,IAAI,oBAAW,CAAC,uDAAuD,EAAE;4BACvF,MAAM,EAAE,MAAM;4BACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;4BACxC,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;yBAChD,CAAC,CAAC;wBAGc,qBAAM,IAAA,YAAI,EAAC,OAAO,CAAC,EAAA;;wBAA9B,QAAQ,GAAG,SAAmB;wBACrB,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA9B,MAAM,GAAG,SAAqB;wBAEpC,SAAS;wBACT,IAAA,gBAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAClC,IAAA,gBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,IAAA,gBAAM,EAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;wBAClD,IAAA,gBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;wBACrC,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aAClD,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,8CAA8C,EAAE;;;;;wBACjD,UAAU;wBACV,qBAAqB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAClD,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;wBAC1D,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBACxD,iBAAiB,CAAC,4BAA4B,CAAC,iBAAiB,CAAC;4BAC/D,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,oCAAoC;yBAC5C,CAAC,CAAC;wBAEG,OAAO,GAAG,IAAI,oBAAW,CAAC,uDAAuD,EAAE;4BACvF,MAAM,EAAE,MAAM;4BACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;4BACtC,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;yBAChD,CAAC,CAAC;wBAGc,qBAAM,IAAA,YAAI,EAAC,OAAO,CAAC,EAAA;;wBAA9B,QAAQ,GAAG,SAAmB;wBACrB,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA9B,MAAM,GAAG,SAAqB;wBAEpC,SAAS;wBACT,IAAA,gBAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAClC,IAAA,gBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,IAAA,gBAAM,EAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;;;;aACjE,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,+BAA+B,EAAE;;;;;wBAClC,UAAU;wBACV,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAEvC,OAAO,GAAG,IAAI,oBAAW,CAAC,uDAAuD,EAAE;4BACvF,MAAM,EAAE,MAAM;4BACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;4BACtC,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;yBAChD,CAAC,CAAC;wBAGc,qBAAM,IAAA,YAAI,EAAC,OAAO,CAAC,EAAA;;wBAA9B,QAAQ,GAAG,SAAmB;wBACrB,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA9B,MAAM,GAAG,SAAqB;wBAEpC,SAAS;wBACT,IAAA,gBAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAClC,IAAA,gBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,IAAA,gBAAM,EAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;;;;aACtD,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,6DAA6D,EAAE;;;;;wBAE1D,mBAAmB,GAAG;4BAC1B;gCACE,OAAO,EAAE,SAAS;gCAClB,KAAK,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE;gCAC7B,UAAU,EAAE,CAAC;gCACb,eAAe,EAAE,CAAC;gCAClB,cAAc,EAAE,IAAI,IAAI,EAAE;gCAC1B,cAAc,EAAE,iBAAiB;6BAClC;yBACF,CAAC;wBAEF,qBAAqB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAClD,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,iBAAiB,CAAC,mBAA0B,CAAC,CAAC;wBAClF,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBACxD,iBAAiB,CAAC,4BAA4B,CAAC,iBAAiB,CAAC;4BAC/D,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE;gCACJ,SAAS,EAAE,EAAE;gCACb,YAAY,EAAE,EAAE,mBAAmB,EAAE,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,oBAAoB,EAAE,EAAE,EAAE;gCAClF,eAAe,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,oBAAoB,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC,EAAE;6BAClG;yBACF,CAAC,CAAC;wBACH,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,aAAa,EAAS,CAAC,CAAC;wBAE7E,OAAO,GAAG,IAAI,oBAAW,CAAC,uDAAuD,EAAE;4BACvF,MAAM,EAAE,MAAM;4BACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;4BACtC,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;yBAChD,CAAC,CAAC;wBAGc,qBAAM,IAAA,YAAI,EAAC,OAAO,CAAC,EAAA;;wBAA9B,QAAQ,GAAG,SAAmB;wBAEpC,SAAS;wBACT,IAAA,gBAAM,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAElC,+FAA+F;wBAC/F,IAAA,gBAAM,EAAC,iBAAiB,CAAC,4BAA4B,CAAC,CAAC,oBAAoB,CACzE,gBAAM,CAAC,eAAe,CAAC;4BACrB,gBAAM,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC;4BACpD,gBAAM,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;4BAC/C,gBAAM,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC;yBACrD,CAAC,EACF,gBAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,gBAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,gBAAM,CAAC,gBAAgB,CAAC;4BACtB,EAAE,EAAE,yBAAyB;4BAC7B,IAAI,EAAE,sBAAsB;yBAC7B,CAAC,EACF,UAAU,CACX,CAAC;;;;aACH,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/api/ai/skills-analysis-comprehensive.test.ts"], "sourcesContent": ["import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';\nimport { NextRequest } from 'next/server';\nimport { POST } from '@/app/api/ai/skills-analysis/comprehensive/route';\nimport { prisma } from '@/lib/prisma';\nimport { getServerSession } from 'next-auth';\nimport { geminiService } from '@/lib/services/geminiService';\nimport { consolidatedCache } from '@/lib/services/consolidated-cache-service';\n\n// Mock dependencies\njest.mock('@/lib/prisma', () => ({\n  prisma: {\n    skillAssessment: {\n      findMany: jest.fn(),\n    },\n    careerPath: {\n      findFirst: jest.fn(),\n    },\n    skillGapAnalysis: {\n      create: jest.fn(),\n    },\n  },\n}));\n\njest.mock('next-auth', () => ({\n  getServerSession: jest.fn(),\n}));\n\njest.mock('@/lib/services/geminiService', () => ({\n  geminiService: {\n    analyzeComprehensiveSkillGap: jest.fn(),\n  },\n}));\n\njest.mock('@/lib/services/consolidated-cache-service', () => ({\n  consolidatedCache: {\n    get: jest.fn(),\n    set: jest.fn(),\n    generateAIKey: jest.fn(),\n  },\n}));\n\njest.mock('@/lib/auth', () => ({\n  authOptions: {},\n}));\n\njest.mock('@/lib/errorHandler', () => ({\n  withErrorHandler: (handler: any) => handler,\n}));\n\njest.mock('@/lib/rateLimit', () => ({\n  withRateLimit: (request: any, config: any, handler: any) => handler(),\n}));\n\njest.mock('@/lib/csrf', () => ({\n  withCSRFProtection: (request: any, handler: any) => handler(),\n}));\n\nconst mockPrisma = prisma as jest.Mocked<typeof prisma>;\nconst mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;\nconst mockGeminiService = geminiService as jest.Mocked<typeof geminiService>;\nconst mockConsolidatedCache = consolidatedCache as jest.Mocked<typeof consolidatedCache>;\n\ndescribe('Comprehensive Skills Analysis API', () => {\n  const mockUserId = 'test-user-id';\n  \n  beforeEach(() => {\n    jest.clearAllMocks();\n    mockGetServerSession.mockResolvedValue({\n      user: { id: mockUserId },\n    } as any);\n  });\n\n  afterEach(() => {\n    jest.resetAllMocks();\n  });\n\n  describe('POST /api/ai/skills-analysis/comprehensive', () => {\n    const validRequestData = {\n      currentSkills: [\n        {\n          skillName: 'JavaScript',\n          selfRating: 7,\n          confidenceLevel: 8,\n          yearsOfExperience: 3,\n        },\n        {\n          skillName: 'React',\n          selfRating: 6,\n          confidenceLevel: 7,\n          yearsOfExperience: 2,\n        },\n      ],\n      targetCareerPath: {\n        careerPathName: 'Full Stack Developer',\n        targetLevel: 'ADVANCED' as const,\n      },\n      preferences: {\n        timeframe: 'ONE_YEAR' as const,\n        hoursPerWeek: 10,\n        learningStyle: ['VISUAL', 'HANDS_ON'],\n        budget: 'FREEMIUM' as const,\n        focusAreas: ['Backend Development', 'Database Design'],\n      },\n      includeMarketData: true,\n      includePersonalizedPaths: true,\n    };\n\n    it('should perform comprehensive skills analysis successfully', async () => {\n      // Arrange\n      const mockCareerPathData = {\n        id: 'fallback-career-path-id',\n        name: 'Full Stack Developer',\n        requiredSkills: [],\n        learningResources: [],\n        learningPaths: [],\n      };\n\n      const mockAIResponse = {\n        success: true,\n        data: {\n          skillGaps: [\n            {\n              skillId: 'skill-2',\n              skillName: 'Node.js',\n              currentLevel: 3,\n              targetLevel: 8,\n              gapSeverity: 'HIGH',\n              priority: 85,\n              estimatedLearningTime: 120,\n              marketDemand: 'VERY_HIGH',\n              salaryImpact: 15,\n            },\n            {\n              skillId: 'skill-3',\n              skillName: 'PostgreSQL',\n              currentLevel: 2,\n              targetLevel: 7,\n              gapSeverity: 'CRITICAL',\n              priority: 95,\n              estimatedLearningTime: 80,\n              marketDemand: 'HIGH',\n              salaryImpact: 12,\n            },\n          ],\n          learningPlan: {\n            totalEstimatedHours: 200,\n            milestones: [\n              {\n                month: 3,\n                skills: ['Node.js Basics'],\n                estimatedHours: 60,\n                learningPaths: ['Backend Development Path'],\n              },\n              {\n                month: 6,\n                skills: ['PostgreSQL Fundamentals'],\n                estimatedHours: 40,\n                learningPaths: ['Database Design Path'],\n              },\n            ],\n            recommendedResources: [\n              {\n                resourceId: 'resource-1',\n                resourceType: 'COURSE',\n                priority: 'HIGH',\n                skillsAddressed: ['Node.js'],\n                estimatedHours: 60,\n              },\n            ],\n          },\n          careerReadiness: {\n            currentScore: 65,\n            targetScore: 85,\n            improvementPotential: 20,\n            timeToTarget: 8,\n          },\n          marketInsights: {\n            industryTrends: [\n              {\n                skill: 'Node.js',\n                trend: 'GROWING',\n                demandLevel: 'VERY_HIGH',\n              },\n            ],\n            salaryProjections: {\n              currentEstimate: 75000,\n              targetEstimate: 95000,\n              improvementPotential: 26.7,\n            },\n          },\n        },\n      };\n\n      const mockSkillGapAnalysis = {\n        id: 'analysis-id',\n        userId: mockUserId,\n        targetCareerPathName: 'Full Stack Developer',\n        status: 'ACTIVE',\n        createdAt: new Date(),\n      };\n\n      mockConsolidatedCache.get.mockResolvedValue(null); // No cache\n      mockPrisma.skillAssessment.findMany.mockResolvedValue([]);\n      mockPrisma.careerPath.findFirst.mockResolvedValue(mockCareerPathData as any);\n      mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue(mockAIResponse);\n      mockPrisma.skillGapAnalysis.create.mockResolvedValue(mockSkillGapAnalysis as any);\n\n      const request = new NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {\n        method: 'POST',\n        body: JSON.stringify(validRequestData),\n        headers: { 'Content-Type': 'application/json' },\n      });\n\n      // Act\n      const response = await POST(request);\n      const result = await response.json();\n\n      // Assert\n      expect(response.status).toBe(200);\n      expect(result.success).toBe(true);\n      expect(result.data.analysisId).toBe('analysis-id');\n      expect(result.data.skillGaps).toHaveLength(2);\n      expect(result.data.skillGaps[0].skillName).toBe('Node.js');\n      expect(result.data.skillGaps[1].skillName).toBe('PostgreSQL');\n      expect(result.data.learningPlan.totalEstimatedHours).toBe(200);\n      expect(result.data.careerReadiness.currentScore).toBe(65);\n      expect(result.data.marketInsights).toBeDefined();\n      expect(result.cached).toBe(false);\n\n      // Verify AI service was called with correct parameters\n      expect(mockGeminiService.analyzeComprehensiveSkillGap).toHaveBeenCalledWith(\n        expect.arrayContaining([\n          expect.objectContaining({ skillName: 'JavaScript' }),\n          expect.objectContaining({ skillName: 'React' }),\n        ]),\n        expect.objectContaining({\n          careerPathName: 'Full Stack Developer',\n          targetLevel: 'ADVANCED',\n        }),\n        expect.objectContaining({\n          timeframe: 'ONE_YEAR',\n          hoursPerWeek: 10,\n        }),\n        expect.objectContaining({\n          id: 'fallback-career-path-id',\n          name: 'Full Stack Developer',\n          requiredSkills: [],\n          learningResources: [],\n          learningPaths: [],\n        }),\n        mockUserId\n      );\n\n      // Verify skill gap analysis was created\n      expect(mockPrisma.skillGapAnalysis.create).toHaveBeenCalledWith({\n        data: expect.objectContaining({\n          userId: mockUserId,\n          targetCareerPathName: 'Full Stack Developer',\n          experienceLevel: 'ADVANCED',\n          timeframe: 'ONE_YEAR',\n          status: 'ACTIVE',\n        }),\n      });\n    });\n\n    it('should return cached results when available', async () => {\n      // Arrange\n      const cachedData = {\n        analysisId: 'cached-analysis-id',\n        skillGaps: [],\n        learningPlan: { totalEstimatedHours: 0, milestones: [], recommendedResources: [] },\n        careerReadiness: { currentScore: 70, targetScore: 85, improvementPotential: 15, timeToTarget: 6 },\n        generatedAt: '2024-01-01T00:00:00.000Z',\n      };\n\n      mockConsolidatedCache.get.mockResolvedValue(cachedData);\n\n      const request = new NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {\n        method: 'POST',\n        body: JSON.stringify(validRequestData),\n        headers: { 'Content-Type': 'application/json' },\n      });\n\n      // Act\n      const response = await POST(request);\n      const result = await response.json();\n\n      // Assert\n      expect(response.status).toBe(200);\n      expect(result.success).toBe(true);\n      expect(result.data).toEqual(cachedData);\n      expect(result.cached).toBe(true);\n      expect(result.generatedAt).toBe('2024-01-01T00:00:00.000Z');\n\n      // Verify AI service was not called\n      expect(mockGeminiService.analyzeComprehensiveSkillGap).not.toHaveBeenCalled();\n      expect(mockPrisma.skillGapAnalysis.create).not.toHaveBeenCalled();\n    });\n\n    it('should fail with invalid request data', async () => {\n      // Arrange\n      const invalidRequestData = {\n        currentSkills: [], // Empty array - should fail validation\n        targetCareerPath: {\n          careerPathName: 'A', // Too short\n          targetLevel: 'INVALID_LEVEL', // Invalid enum value\n        },\n        preferences: {\n          timeframe: 'INVALID_TIMEFRAME',\n          hoursPerWeek: -5, // Negative hours\n          learningStyle: [],\n          budget: 'INVALID_BUDGET',\n          focusAreas: [],\n        },\n      };\n\n      const request = new NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {\n        method: 'POST',\n        body: JSON.stringify(invalidRequestData),\n        headers: { 'Content-Type': 'application/json' },\n      });\n\n      // Act\n      const response = await POST(request);\n      const result = await response.json();\n\n      // Assert\n      expect(response.status).toBe(400);\n      expect(result.success).toBe(false);\n      expect(result.error).toBe('Invalid request data');\n      expect(result.details).toBeDefined();\n      expect(Array.isArray(result.details)).toBe(true);\n    });\n\n    it('should handle AI service failures gracefully', async () => {\n      // Arrange\n      mockConsolidatedCache.get.mockResolvedValue(null);\n      mockPrisma.skillAssessment.findMany.mockResolvedValue([]);\n      mockPrisma.careerPath.findFirst.mockResolvedValue(null);\n      mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue({\n        success: false,\n        error: 'AI service temporarily unavailable',\n      });\n\n      const request = new NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {\n        method: 'POST',\n        body: JSON.stringify(validRequestData),\n        headers: { 'Content-Type': 'application/json' },\n      });\n\n      // Act\n      const response = await POST(request);\n      const result = await response.json();\n\n      // Assert\n      expect(response.status).toBe(500);\n      expect(result.success).toBe(false);\n      expect(result.error).toBe('AI service temporarily unavailable');\n    });\n\n    it('should require authentication', async () => {\n      // Arrange\n      mockGetServerSession.mockResolvedValue(null);\n\n      const request = new NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {\n        method: 'POST',\n        body: JSON.stringify(validRequestData),\n        headers: { 'Content-Type': 'application/json' },\n      });\n\n      // Act\n      const response = await POST(request);\n      const result = await response.json();\n\n      // Assert\n      expect(response.status).toBe(401);\n      expect(result.success).toBe(false);\n      expect(result.error).toBe('Authentication required');\n    });\n\n    it('should merge existing skill assessments with current skills', async () => {\n      // Arrange\n      const existingAssessments = [\n        {\n          skillId: 'skill-1',\n          skill: { name: 'TypeScript' },\n          selfRating: 8,\n          confidenceLevel: 9,\n          assessmentDate: new Date(),\n          assessmentType: 'SELF_ASSESSMENT',\n        },\n      ];\n\n      mockConsolidatedCache.get.mockResolvedValue(null);\n      mockPrisma.skillAssessment.findMany.mockResolvedValue(existingAssessments as any);\n      mockPrisma.careerPath.findFirst.mockResolvedValue(null);\n      mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue({\n        success: true,\n        data: {\n          skillGaps: [],\n          learningPlan: { totalEstimatedHours: 0, milestones: [], recommendedResources: [] },\n          careerReadiness: { currentScore: 70, targetScore: 85, improvementPotential: 15, timeToTarget: 6 },\n        },\n      });\n      mockPrisma.skillGapAnalysis.create.mockResolvedValue({ id: 'analysis-id' } as any);\n\n      const request = new NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {\n        method: 'POST',\n        body: JSON.stringify(validRequestData),\n        headers: { 'Content-Type': 'application/json' },\n      });\n\n      // Act\n      const response = await POST(request);\n\n      // Assert\n      expect(response.status).toBe(200);\n      \n      // Verify that AI service was called with merged skills (including TypeScript from assessments)\n      expect(mockGeminiService.analyzeComprehensiveSkillGap).toHaveBeenCalledWith(\n        expect.arrayContaining([\n          expect.objectContaining({ skillName: 'JavaScript' }),\n          expect.objectContaining({ skillName: 'React' }),\n          expect.objectContaining({ skillName: 'TypeScript' }),\n        ]),\n        expect.any(Object),\n        expect.any(Object),\n        expect.objectContaining({\n          id: 'fallback-career-path-id',\n          name: 'Full Stack Developer',\n        }),\n        mockUserId\n      );\n    });\n  });\n});\n"], "version": 3}