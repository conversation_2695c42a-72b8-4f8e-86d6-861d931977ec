c66663a8ca7e9998adf83f2988ead879
"use strict";

/**
 * Concurrent Database Operations Service
 * Optimizes database operations through concurrent processing, connection pooling,
 * and intelligent query batching for the Skills Analysis API
 */
/* istanbul ignore next */
function cov_1zda1zdg4k() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/concurrent-database-service.ts";
  var hash = "724a9195cf8d5adf3e695fa18ffd38577f9a7fc3";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/concurrent-database-service.ts",
    statementMap: {
      "0": {
        start: {
          line: 7,
          column: 15
        },
        end: {
          line: 17,
          column: 1
        }
      },
      "1": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 15,
          column: 6
        }
      },
      "2": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 13,
          column: 9
        }
      },
      "3": {
        start: {
          line: 9,
          column: 24
        },
        end: {
          line: 9,
          column: 25
        }
      },
      "4": {
        start: {
          line: 9,
          column: 31
        },
        end: {
          line: 9,
          column: 47
        }
      },
      "5": {
        start: {
          line: 10,
          column: 12
        },
        end: {
          line: 10,
          column: 29
        }
      },
      "6": {
        start: {
          line: 11,
          column: 12
        },
        end: {
          line: 12,
          column: 28
        }
      },
      "7": {
        start: {
          line: 11,
          column: 29
        },
        end: {
          line: 12,
          column: 28
        }
      },
      "8": {
        start: {
          line: 12,
          column: 16
        },
        end: {
          line: 12,
          column: 28
        }
      },
      "9": {
        start: {
          line: 14,
          column: 8
        },
        end: {
          line: 14,
          column: 17
        }
      },
      "10": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 43
        }
      },
      "11": {
        start: {
          line: 18,
          column: 16
        },
        end: {
          line: 26,
          column: 1
        }
      },
      "12": {
        start: {
          line: 19,
          column: 28
        },
        end: {
          line: 19,
          column: 110
        }
      },
      "13": {
        start: {
          line: 19,
          column: 91
        },
        end: {
          line: 19,
          column: 106
        }
      },
      "14": {
        start: {
          line: 20,
          column: 4
        },
        end: {
          line: 25,
          column: 7
        }
      },
      "15": {
        start: {
          line: 21,
          column: 36
        },
        end: {
          line: 21,
          column: 97
        }
      },
      "16": {
        start: {
          line: 21,
          column: 42
        },
        end: {
          line: 21,
          column: 70
        }
      },
      "17": {
        start: {
          line: 21,
          column: 85
        },
        end: {
          line: 21,
          column: 95
        }
      },
      "18": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 100
        }
      },
      "19": {
        start: {
          line: 22,
          column: 41
        },
        end: {
          line: 22,
          column: 73
        }
      },
      "20": {
        start: {
          line: 22,
          column: 88
        },
        end: {
          line: 22,
          column: 98
        }
      },
      "21": {
        start: {
          line: 23,
          column: 32
        },
        end: {
          line: 23,
          column: 116
        }
      },
      "22": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 78
        }
      },
      "23": {
        start: {
          line: 27,
          column: 18
        },
        end: {
          line: 53,
          column: 1
        }
      },
      "24": {
        start: {
          line: 28,
          column: 12
        },
        end: {
          line: 28,
          column: 104
        }
      },
      "25": {
        start: {
          line: 28,
          column: 43
        },
        end: {
          line: 28,
          column: 68
        }
      },
      "26": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 68
        }
      },
      "27": {
        start: {
          line: 28,
          column: 69
        },
        end: {
          line: 28,
          column: 81
        }
      },
      "28": {
        start: {
          line: 28,
          column: 119
        },
        end: {
          line: 28,
          column: 196
        }
      },
      "29": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 29,
          column: 160
        }
      },
      "30": {
        start: {
          line: 29,
          column: 141
        },
        end: {
          line: 29,
          column: 153
        }
      },
      "31": {
        start: {
          line: 30,
          column: 23
        },
        end: {
          line: 30,
          column: 68
        }
      },
      "32": {
        start: {
          line: 30,
          column: 45
        },
        end: {
          line: 30,
          column: 65
        }
      },
      "33": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 70
        }
      },
      "34": {
        start: {
          line: 32,
          column: 15
        },
        end: {
          line: 32,
          column: 70
        }
      },
      "35": {
        start: {
          line: 33,
          column: 8
        },
        end: {
          line: 50,
          column: 66
        }
      },
      "36": {
        start: {
          line: 33,
          column: 50
        },
        end: {
          line: 50,
          column: 66
        }
      },
      "37": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 169
        }
      },
      "38": {
        start: {
          line: 34,
          column: 160
        },
        end: {
          line: 34,
          column: 169
        }
      },
      "39": {
        start: {
          line: 35,
          column: 12
        },
        end: {
          line: 35,
          column: 52
        }
      },
      "40": {
        start: {
          line: 35,
          column: 26
        },
        end: {
          line: 35,
          column: 52
        }
      },
      "41": {
        start: {
          line: 36,
          column: 12
        },
        end: {
          line: 48,
          column: 13
        }
      },
      "42": {
        start: {
          line: 37,
          column: 32
        },
        end: {
          line: 37,
          column: 39
        }
      },
      "43": {
        start: {
          line: 37,
          column: 40
        },
        end: {
          line: 37,
          column: 46
        }
      },
      "44": {
        start: {
          line: 38,
          column: 24
        },
        end: {
          line: 38,
          column: 34
        }
      },
      "45": {
        start: {
          line: 38,
          column: 35
        },
        end: {
          line: 38,
          column: 72
        }
      },
      "46": {
        start: {
          line: 39,
          column: 24
        },
        end: {
          line: 39,
          column: 34
        }
      },
      "47": {
        start: {
          line: 39,
          column: 35
        },
        end: {
          line: 39,
          column: 45
        }
      },
      "48": {
        start: {
          line: 39,
          column: 46
        },
        end: {
          line: 39,
          column: 55
        }
      },
      "49": {
        start: {
          line: 39,
          column: 56
        },
        end: {
          line: 39,
          column: 65
        }
      },
      "50": {
        start: {
          line: 40,
          column: 24
        },
        end: {
          line: 40,
          column: 41
        }
      },
      "51": {
        start: {
          line: 40,
          column: 42
        },
        end: {
          line: 40,
          column: 55
        }
      },
      "52": {
        start: {
          line: 40,
          column: 56
        },
        end: {
          line: 40,
          column: 65
        }
      },
      "53": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 128
        }
      },
      "54": {
        start: {
          line: 42,
          column: 110
        },
        end: {
          line: 42,
          column: 116
        }
      },
      "55": {
        start: {
          line: 42,
          column: 117
        },
        end: {
          line: 42,
          column: 126
        }
      },
      "56": {
        start: {
          line: 43,
          column: 20
        },
        end: {
          line: 43,
          column: 106
        }
      },
      "57": {
        start: {
          line: 43,
          column: 81
        },
        end: {
          line: 43,
          column: 97
        }
      },
      "58": {
        start: {
          line: 43,
          column: 98
        },
        end: {
          line: 43,
          column: 104
        }
      },
      "59": {
        start: {
          line: 44,
          column: 20
        },
        end: {
          line: 44,
          column: 89
        }
      },
      "60": {
        start: {
          line: 44,
          column: 57
        },
        end: {
          line: 44,
          column: 72
        }
      },
      "61": {
        start: {
          line: 44,
          column: 73
        },
        end: {
          line: 44,
          column: 80
        }
      },
      "62": {
        start: {
          line: 44,
          column: 81
        },
        end: {
          line: 44,
          column: 87
        }
      },
      "63": {
        start: {
          line: 45,
          column: 20
        },
        end: {
          line: 45,
          column: 87
        }
      },
      "64": {
        start: {
          line: 45,
          column: 47
        },
        end: {
          line: 45,
          column: 62
        }
      },
      "65": {
        start: {
          line: 45,
          column: 63
        },
        end: {
          line: 45,
          column: 78
        }
      },
      "66": {
        start: {
          line: 45,
          column: 79
        },
        end: {
          line: 45,
          column: 85
        }
      },
      "67": {
        start: {
          line: 46,
          column: 20
        },
        end: {
          line: 46,
          column: 42
        }
      },
      "68": {
        start: {
          line: 46,
          column: 30
        },
        end: {
          line: 46,
          column: 42
        }
      },
      "69": {
        start: {
          line: 47,
          column: 20
        },
        end: {
          line: 47,
          column: 33
        }
      },
      "70": {
        start: {
          line: 47,
          column: 34
        },
        end: {
          line: 47,
          column: 43
        }
      },
      "71": {
        start: {
          line: 49,
          column: 12
        },
        end: {
          line: 49,
          column: 39
        }
      },
      "72": {
        start: {
          line: 50,
          column: 22
        },
        end: {
          line: 50,
          column: 34
        }
      },
      "73": {
        start: {
          line: 50,
          column: 35
        },
        end: {
          line: 50,
          column: 41
        }
      },
      "74": {
        start: {
          line: 50,
          column: 54
        },
        end: {
          line: 50,
          column: 64
        }
      },
      "75": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 51,
          column: 35
        }
      },
      "76": {
        start: {
          line: 51,
          column: 23
        },
        end: {
          line: 51,
          column: 35
        }
      },
      "77": {
        start: {
          line: 51,
          column: 36
        },
        end: {
          line: 51,
          column: 89
        }
      },
      "78": {
        start: {
          line: 54,
          column: 20
        },
        end: {
          line: 62,
          column: 1
        }
      },
      "79": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 60,
          column: 5
        }
      },
      "80": {
        start: {
          line: 55,
          column: 40
        },
        end: {
          line: 60,
          column: 5
        }
      },
      "81": {
        start: {
          line: 55,
          column: 53
        },
        end: {
          line: 55,
          column: 54
        }
      },
      "82": {
        start: {
          line: 55,
          column: 60
        },
        end: {
          line: 55,
          column: 71
        }
      },
      "83": {
        start: {
          line: 56,
          column: 8
        },
        end: {
          line: 59,
          column: 9
        }
      },
      "84": {
        start: {
          line: 57,
          column: 12
        },
        end: {
          line: 57,
          column: 65
        }
      },
      "85": {
        start: {
          line: 57,
          column: 21
        },
        end: {
          line: 57,
          column: 65
        }
      },
      "86": {
        start: {
          line: 58,
          column: 12
        },
        end: {
          line: 58,
          column: 28
        }
      },
      "87": {
        start: {
          line: 61,
          column: 4
        },
        end: {
          line: 61,
          column: 61
        }
      },
      "88": {
        start: {
          line: 63,
          column: 0
        },
        end: {
          line: 63,
          column: 62
        }
      },
      "89": {
        start: {
          line: 64,
          column: 0
        },
        end: {
          line: 64,
          column: 79
        }
      },
      "90": {
        start: {
          line: 65,
          column: 15
        },
        end: {
          line: 65,
          column: 38
        }
      },
      "91": {
        start: {
          line: 66,
          column: 35
        },
        end: {
          line: 66,
          column: 74
        }
      },
      "92": {
        start: {
          line: 67,
          column: 47
        },
        end: {
          line: 713,
          column: 3
        }
      },
      "93": {
        start: {
          line: 69,
          column: 20
        },
        end: {
          line: 69,
          column: 24
        }
      },
      "94": {
        start: {
          line: 70,
          column: 8
        },
        end: {
          line: 70,
          column: 40
        }
      },
      "95": {
        start: {
          line: 71,
          column: 8
        },
        end: {
          line: 71,
          column: 45
        }
      },
      "96": {
        start: {
          line: 72,
          column: 8
        },
        end: {
          line: 72,
          column: 45
        }
      },
      "97": {
        start: {
          line: 73,
          column: 8
        },
        end: {
          line: 80,
          column: 10
        }
      },
      "98": {
        start: {
          line: 81,
          column: 8
        },
        end: {
          line: 81,
          column: 90
        }
      },
      "99": {
        start: {
          line: 82,
          column: 8
        },
        end: {
          line: 82,
          column: 84
        }
      },
      "100": {
        start: {
          line: 84,
          column: 8
        },
        end: {
          line: 84,
          column: 80
        }
      },
      "101": {
        start: {
          line: 84,
          column: 34
        },
        end: {
          line: 84,
          column: 71
        }
      },
      "102": {
        start: {
          line: 86,
          column: 8
        },
        end: {
          line: 86,
          column: 73
        }
      },
      "103": {
        start: {
          line: 86,
          column: 34
        },
        end: {
          line: 86,
          column: 63
        }
      },
      "104": {
        start: {
          line: 88,
          column: 4
        },
        end: {
          line: 93,
          column: 6
        }
      },
      "105": {
        start: {
          line: 89,
          column: 8
        },
        end: {
          line: 91,
          column: 9
        }
      },
      "106": {
        start: {
          line: 90,
          column: 12
        },
        end: {
          line: 90,
          column: 81
        }
      },
      "107": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 92,
          column: 50
        }
      },
      "108": {
        start: {
          line: 97,
          column: 4
        },
        end: {
          line: 147,
          column: 6
        }
      },
      "109": {
        start: {
          line: 98,
          column: 8
        },
        end: {
          line: 146,
          column: 11
        }
      },
      "110": {
        start: {
          line: 100,
          column: 24
        },
        end: {
          line: 100,
          column: 28
        }
      },
      "111": {
        start: {
          line: 101,
          column: 12
        },
        end: {
          line: 145,
          column: 15
        }
      },
      "112": {
        start: {
          line: 102,
          column: 16
        },
        end: {
          line: 144,
          column: 17
        }
      },
      "113": {
        start: {
          line: 104,
          column: 24
        },
        end: {
          line: 104,
          column: 47
        }
      },
      "114": {
        start: {
          line: 105,
          column: 24
        },
        end: {
          line: 105,
          column: 87
        }
      },
      "115": {
        start: {
          line: 105,
          column: 70
        },
        end: {
          line: 105,
          column: 83
        }
      },
      "116": {
        start: {
          line: 106,
          column: 24
        },
        end: {
          line: 106,
          column: 37
        }
      },
      "117": {
        start: {
          line: 108,
          column: 24
        },
        end: {
          line: 108,
          column: 50
        }
      },
      "118": {
        start: {
          line: 110,
          column: 24
        },
        end: {
          line: 113,
          column: 27
        }
      },
      "119": {
        start: {
          line: 111,
          column: 28
        },
        end: {
          line: 111,
          column: 64
        }
      },
      "120": {
        start: {
          line: 112,
          column: 28
        },
        end: {
          line: 112,
          column: 60
        }
      },
      "121": {
        start: {
          line: 114,
          column: 24
        },
        end: {
          line: 114,
          column: 138
        }
      },
      "122": {
        start: {
          line: 114,
          column: 95
        },
        end: {
          line: 114,
          column: 132
        }
      },
      "123": {
        start: {
          line: 116,
          column: 24
        },
        end: {
          line: 116,
          column: 44
        }
      },
      "124": {
        start: {
          line: 117,
          column: 24
        },
        end: {
          line: 137,
          column: 27
        }
      },
      "125": {
        start: {
          line: 119,
          column: 46
        },
        end: {
          line: 119,
          column: 65
        }
      },
      "126": {
        start: {
          line: 120,
          column: 48
        },
        end: {
          line: 120,
          column: 70
        }
      },
      "127": {
        start: {
          line: 121,
          column: 28
        },
        end: {
          line: 136,
          column: 29
        }
      },
      "128": {
        start: {
          line: 122,
          column: 32
        },
        end: {
          line: 127,
          column: 34
        }
      },
      "129": {
        start: {
          line: 130,
          column: 32
        },
        end: {
          line: 135,
          column: 34
        }
      },
      "130": {
        start: {
          line: 138,
          column: 24
        },
        end: {
          line: 138,
          column: 60
        }
      },
      "131": {
        start: {
          line: 140,
          column: 24
        },
        end: {
          line: 140,
          column: 44
        }
      },
      "132": {
        start: {
          line: 141,
          column: 24
        },
        end: {
          line: 141,
          column: 88
        }
      },
      "133": {
        start: {
          line: 142,
          column: 24
        },
        end: {
          line: 142,
          column: 38
        }
      },
      "134": {
        start: {
          line: 143,
          column: 28
        },
        end: {
          line: 143,
          column: 50
        }
      },
      "135": {
        start: {
          line: 151,
          column: 4
        },
        end: {
          line: 193,
          column: 6
        }
      },
      "136": {
        start: {
          line: 152,
          column: 8
        },
        end: {
          line: 192,
          column: 11
        }
      },
      "137": {
        start: {
          line: 154,
          column: 24
        },
        end: {
          line: 154,
          column: 28
        }
      },
      "138": {
        start: {
          line: 155,
          column: 12
        },
        end: {
          line: 191,
          column: 15
        }
      },
      "139": {
        start: {
          line: 156,
          column: 16
        },
        end: {
          line: 190,
          column: 17
        }
      },
      "140": {
        start: {
          line: 158,
          column: 24
        },
        end: {
          line: 181,
          column: 26
        }
      },
      "141": {
        start: {
          line: 161,
          column: 57
        },
        end: {
          line: 161,
          column: 126
        }
      },
      "142": {
        start: {
          line: 168,
          column: 57
        },
        end: {
          line: 168,
          column: 95
        }
      },
      "143": {
        start: {
          line: 175,
          column: 57
        },
        end: {
          line: 175,
          column: 102
        }
      },
      "144": {
        start: {
          line: 182,
          column: 24
        },
        end: {
          line: 182,
          column: 81
        }
      },
      "145": {
        start: {
          line: 184,
          column: 24
        },
        end: {
          line: 184,
          column: 44
        }
      },
      "146": {
        start: {
          line: 185,
          column: 24
        },
        end: {
          line: 185,
          column: 116
        }
      },
      "147": {
        start: {
          line: 185,
          column: 69
        },
        end: {
          line: 185,
          column: 112
        }
      },
      "148": {
        start: {
          line: 186,
          column: 24
        },
        end: {
          line: 188,
          column: 25
        }
      },
      "149": {
        start: {
          line: 187,
          column: 28
        },
        end: {
          line: 187,
          column: 173
        }
      },
      "150": {
        start: {
          line: 189,
          column: 24
        },
        end: {
          line: 189,
          column: 67
        }
      },
      "151": {
        start: {
          line: 197,
          column: 4
        },
        end: {
          line: 238,
          column: 6
        }
      },
      "152": {
        start: {
          line: 198,
          column: 8
        },
        end: {
          line: 237,
          column: 11
        }
      },
      "153": {
        start: {
          line: 200,
          column: 24
        },
        end: {
          line: 200,
          column: 28
        }
      },
      "154": {
        start: {
          line: 202,
          column: 12
        },
        end: {
          line: 236,
          column: 15
        }
      },
      "155": {
        start: {
          line: 203,
          column: 16
        },
        end: {
          line: 235,
          column: 17
        }
      },
      "156": {
        start: {
          line: 205,
          column: 24
        },
        end: {
          line: 227,
          column: 26
        }
      },
      "157": {
        start: {
          line: 208,
          column: 57
        },
        end: {
          line: 208,
          column: 163
        }
      },
      "158": {
        start: {
          line: 215,
          column: 57
        },
        end: {
          line: 215,
          column: 168
        }
      },
      "159": {
        start: {
          line: 222,
          column: 57
        },
        end: {
          line: 222,
          column: 100
        }
      },
      "160": {
        start: {
          line: 228,
          column: 24
        },
        end: {
          line: 228,
          column: 81
        }
      },
      "161": {
        start: {
          line: 230,
          column: 24
        },
        end: {
          line: 230,
          column: 44
        }
      },
      "162": {
        start: {
          line: 231,
          column: 24
        },
        end: {
          line: 231,
          column: 196
        }
      },
      "163": {
        start: {
          line: 231,
          column: 78
        },
        end: {
          line: 231,
          column: 139
        }
      },
      "164": {
        start: {
          line: 232,
          column: 24
        },
        end: {
          line: 232,
          column: 198
        }
      },
      "165": {
        start: {
          line: 232,
          column: 79
        },
        end: {
          line: 232,
          column: 141
        }
      },
      "166": {
        start: {
          line: 233,
          column: 24
        },
        end: {
          line: 233,
          column: 186
        }
      },
      "167": {
        start: {
          line: 233,
          column: 78
        },
        end: {
          line: 233,
          column: 129
        }
      },
      "168": {
        start: {
          line: 234,
          column: 24
        },
        end: {
          line: 234,
          column: 168
        }
      },
      "169": {
        start: {
          line: 242,
          column: 4
        },
        end: {
          line: 294,
          column: 6
        }
      },
      "170": {
        start: {
          line: 243,
          column: 8
        },
        end: {
          line: 293,
          column: 11
        }
      },
      "171": {
        start: {
          line: 245,
          column: 24
        },
        end: {
          line: 245,
          column: 28
        }
      },
      "172": {
        start: {
          line: 247,
          column: 12
        },
        end: {
          line: 292,
          column: 15
        }
      },
      "173": {
        start: {
          line: 248,
          column: 16
        },
        end: {
          line: 291,
          column: 17
        }
      },
      "174": {
        start: {
          line: 250,
          column: 24
        },
        end: {
          line: 279,
          column: 26
        }
      },
      "175": {
        start: {
          line: 253,
          column: 57
        },
        end: {
          line: 253,
          column: 109
        }
      },
      "176": {
        start: {
          line: 260,
          column: 57
        },
        end: {
          line: 260,
          column: 106
        }
      },
      "177": {
        start: {
          line: 267,
          column: 57
        },
        end: {
          line: 267,
          column: 113
        }
      },
      "178": {
        start: {
          line: 274,
          column: 57
        },
        end: {
          line: 274,
          column: 110
        }
      },
      "179": {
        start: {
          line: 280,
          column: 24
        },
        end: {
          line: 280,
          column: 81
        }
      },
      "180": {
        start: {
          line: 282,
          column: 24
        },
        end: {
          line: 282,
          column: 44
        }
      },
      "181": {
        start: {
          line: 283,
          column: 24
        },
        end: {
          line: 283,
          column: 176
        }
      },
      "182": {
        start: {
          line: 283,
          column: 71
        },
        end: {
          line: 283,
          column: 126
        }
      },
      "183": {
        start: {
          line: 284,
          column: 24
        },
        end: {
          line: 284,
          column: 173
        }
      },
      "184": {
        start: {
          line: 284,
          column: 68
        },
        end: {
          line: 284,
          column: 116
        }
      },
      "185": {
        start: {
          line: 285,
          column: 24
        },
        end: {
          line: 285,
          column: 180
        }
      },
      "186": {
        start: {
          line: 285,
          column: 75
        },
        end: {
          line: 285,
          column: 123
        }
      },
      "187": {
        start: {
          line: 286,
          column: 24
        },
        end: {
          line: 286,
          column: 166
        }
      },
      "188": {
        start: {
          line: 286,
          column: 71
        },
        end: {
          line: 286,
          column: 116
        }
      },
      "189": {
        start: {
          line: 287,
          column: 24
        },
        end: {
          line: 289,
          column: 25
        }
      },
      "190": {
        start: {
          line: 288,
          column: 28
        },
        end: {
          line: 288,
          column: 69
        }
      },
      "191": {
        start: {
          line: 290,
          column: 24
        },
        end: {
          line: 290,
          column: 163
        }
      },
      "192": {
        start: {
          line: 298,
          column: 4
        },
        end: {
          line: 332,
          column: 6
        }
      },
      "193": {
        start: {
          line: 299,
          column: 8
        },
        end: {
          line: 331,
          column: 11
        }
      },
      "194": {
        start: {
          line: 302,
          column: 12
        },
        end: {
          line: 330,
          column: 15
        }
      },
      "195": {
        start: {
          line: 303,
          column: 16
        },
        end: {
          line: 329,
          column: 17
        }
      },
      "196": {
        start: {
          line: 305,
          column: 24
        },
        end: {
          line: 305,
          column: 47
        }
      },
      "197": {
        start: {
          line: 306,
          column: 24
        },
        end: {
          line: 306,
          column: 69
        }
      },
      "198": {
        start: {
          line: 307,
          column: 24
        },
        end: {
          line: 327,
          column: 32
        }
      },
      "199": {
        start: {
          line: 328,
          column: 28
        },
        end: {
          line: 328,
          column: 61
        }
      },
      "200": {
        start: {
          line: 333,
          column: 4
        },
        end: {
          line: 358,
          column: 6
        }
      },
      "201": {
        start: {
          line: 334,
          column: 8
        },
        end: {
          line: 357,
          column: 11
        }
      },
      "202": {
        start: {
          line: 335,
          column: 12
        },
        end: {
          line: 356,
          column: 15
        }
      },
      "203": {
        start: {
          line: 336,
          column: 16
        },
        end: {
          line: 355,
          column: 17
        }
      },
      "204": {
        start: {
          line: 337,
          column: 28
        },
        end: {
          line: 353,
          column: 28
        }
      },
      "205": {
        start: {
          line: 354,
          column: 28
        },
        end: {
          line: 354,
          column: 61
        }
      },
      "206": {
        start: {
          line: 359,
          column: 4
        },
        end: {
          line: 378,
          column: 6
        }
      },
      "207": {
        start: {
          line: 360,
          column: 8
        },
        end: {
          line: 377,
          column: 11
        }
      },
      "208": {
        start: {
          line: 361,
          column: 12
        },
        end: {
          line: 376,
          column: 15
        }
      },
      "209": {
        start: {
          line: 362,
          column: 16
        },
        end: {
          line: 375,
          column: 17
        }
      },
      "210": {
        start: {
          line: 363,
          column: 28
        },
        end: {
          line: 373,
          column: 28
        }
      },
      "211": {
        start: {
          line: 374,
          column: 28
        },
        end: {
          line: 374,
          column: 61
        }
      },
      "212": {
        start: {
          line: 379,
          column: 4
        },
        end: {
          line: 401,
          column: 6
        }
      },
      "213": {
        start: {
          line: 380,
          column: 8
        },
        end: {
          line: 400,
          column: 11
        }
      },
      "214": {
        start: {
          line: 381,
          column: 12
        },
        end: {
          line: 399,
          column: 15
        }
      },
      "215": {
        start: {
          line: 382,
          column: 16
        },
        end: {
          line: 398,
          column: 17
        }
      },
      "216": {
        start: {
          line: 383,
          column: 28
        },
        end: {
          line: 396,
          column: 28
        }
      },
      "217": {
        start: {
          line: 397,
          column: 28
        },
        end: {
          line: 397,
          column: 61
        }
      },
      "218": {
        start: {
          line: 402,
          column: 4
        },
        end: {
          line: 423,
          column: 6
        }
      },
      "219": {
        start: {
          line: 403,
          column: 8
        },
        end: {
          line: 422,
          column: 11
        }
      },
      "220": {
        start: {
          line: 404,
          column: 12
        },
        end: {
          line: 421,
          column: 15
        }
      },
      "221": {
        start: {
          line: 405,
          column: 16
        },
        end: {
          line: 420,
          column: 17
        }
      },
      "222": {
        start: {
          line: 406,
          column: 28
        },
        end: {
          line: 418,
          column: 28
        }
      },
      "223": {
        start: {
          line: 419,
          column: 28
        },
        end: {
          line: 419,
          column: 61
        }
      },
      "224": {
        start: {
          line: 424,
          column: 4
        },
        end: {
          line: 448,
          column: 6
        }
      },
      "225": {
        start: {
          line: 425,
          column: 8
        },
        end: {
          line: 447,
          column: 11
        }
      },
      "226": {
        start: {
          line: 426,
          column: 12
        },
        end: {
          line: 446,
          column: 15
        }
      },
      "227": {
        start: {
          line: 427,
          column: 16
        },
        end: {
          line: 445,
          column: 17
        }
      },
      "228": {
        start: {
          line: 428,
          column: 28
        },
        end: {
          line: 443,
          column: 28
        }
      },
      "229": {
        start: {
          line: 444,
          column: 28
        },
        end: {
          line: 444,
          column: 61
        }
      },
      "230": {
        start: {
          line: 449,
          column: 4
        },
        end: {
          line: 468,
          column: 6
        }
      },
      "231": {
        start: {
          line: 450,
          column: 8
        },
        end: {
          line: 467,
          column: 11
        }
      },
      "232": {
        start: {
          line: 451,
          column: 12
        },
        end: {
          line: 466,
          column: 15
        }
      },
      "233": {
        start: {
          line: 452,
          column: 16
        },
        end: {
          line: 465,
          column: 17
        }
      },
      "234": {
        start: {
          line: 453,
          column: 28
        },
        end: {
          line: 463,
          column: 28
        }
      },
      "235": {
        start: {
          line: 464,
          column: 28
        },
        end: {
          line: 464,
          column: 61
        }
      },
      "236": {
        start: {
          line: 472,
          column: 4
        },
        end: {
          line: 499,
          column: 6
        }
      },
      "237": {
        start: {
          line: 473,
          column: 8
        },
        end: {
          line: 498,
          column: 11
        }
      },
      "238": {
        start: {
          line: 474,
          column: 12
        },
        end: {
          line: 497,
          column: 15
        }
      },
      "239": {
        start: {
          line: 475,
          column: 16
        },
        end: {
          line: 496,
          column: 17
        }
      },
      "240": {
        start: {
          line: 476,
          column: 28
        },
        end: {
          line: 494,
          column: 28
        }
      },
      "241": {
        start: {
          line: 495,
          column: 28
        },
        end: {
          line: 495,
          column: 61
        }
      },
      "242": {
        start: {
          line: 500,
          column: 4
        },
        end: {
          line: 522,
          column: 6
        }
      },
      "243": {
        start: {
          line: 501,
          column: 8
        },
        end: {
          line: 521,
          column: 11
        }
      },
      "244": {
        start: {
          line: 502,
          column: 12
        },
        end: {
          line: 520,
          column: 15
        }
      },
      "245": {
        start: {
          line: 503,
          column: 16
        },
        end: {
          line: 519,
          column: 17
        }
      },
      "246": {
        start: {
          line: 504,
          column: 28
        },
        end: {
          line: 517,
          column: 28
        }
      },
      "247": {
        start: {
          line: 518,
          column: 28
        },
        end: {
          line: 518,
          column: 61
        }
      },
      "248": {
        start: {
          line: 523,
          column: 4
        },
        end: {
          line: 550,
          column: 6
        }
      },
      "249": {
        start: {
          line: 524,
          column: 8
        },
        end: {
          line: 549,
          column: 11
        }
      },
      "250": {
        start: {
          line: 525,
          column: 12
        },
        end: {
          line: 548,
          column: 15
        }
      },
      "251": {
        start: {
          line: 526,
          column: 16
        },
        end: {
          line: 547,
          column: 17
        }
      },
      "252": {
        start: {
          line: 527,
          column: 28
        },
        end: {
          line: 545,
          column: 28
        }
      },
      "253": {
        start: {
          line: 546,
          column: 28
        },
        end: {
          line: 546,
          column: 61
        }
      },
      "254": {
        start: {
          line: 551,
          column: 4
        },
        end: {
          line: 559,
          column: 6
        }
      },
      "255": {
        start: {
          line: 552,
          column: 8
        },
        end: {
          line: 558,
          column: 11
        }
      },
      "256": {
        start: {
          line: 553,
          column: 12
        },
        end: {
          line: 557,
          column: 15
        }
      },
      "257": {
        start: {
          line: 556,
          column: 16
        },
        end: {
          line: 556,
          column: 44
        }
      },
      "258": {
        start: {
          line: 563,
          column: 4
        },
        end: {
          line: 586,
          column: 6
        }
      },
      "259": {
        start: {
          line: 564,
          column: 8
        },
        end: {
          line: 585,
          column: 11
        }
      },
      "260": {
        start: {
          line: 566,
          column: 24
        },
        end: {
          line: 566,
          column: 28
        }
      },
      "261": {
        start: {
          line: 567,
          column: 12
        },
        end: {
          line: 584,
          column: 15
        }
      },
      "262": {
        start: {
          line: 568,
          column: 16
        },
        end: {
          line: 570,
          column: 17
        }
      },
      "263": {
        start: {
          line: 569,
          column: 20
        },
        end: {
          line: 569,
          column: 42
        }
      },
      "264": {
        start: {
          line: 571,
          column: 16
        },
        end: {
          line: 577,
          column: 19
        }
      },
      "265": {
        start: {
          line: 572,
          column: 44
        },
        end: {
          line: 572,
          column: 89
        }
      },
      "266": {
        start: {
          line: 573,
          column: 44
        },
        end: {
          line: 573,
          column: 80
        }
      },
      "267": {
        start: {
          line: 575,
          column: 40
        },
        end: {
          line: 575,
          column: 70
        }
      },
      "268": {
        start: {
          line: 576,
          column: 20
        },
        end: {
          line: 576,
          column: 81
        }
      },
      "269": {
        start: {
          line: 578,
          column: 16
        },
        end: {
          line: 580,
          column: 17
        }
      },
      "270": {
        start: {
          line: 579,
          column: 20
        },
        end: {
          line: 579,
          column: 42
        }
      },
      "271": {
        start: {
          line: 581,
          column: 16
        },
        end: {
          line: 581,
          column: 51
        }
      },
      "272": {
        start: {
          line: 582,
          column: 16
        },
        end: {
          line: 582,
          column: 49
        }
      },
      "273": {
        start: {
          line: 583,
          column: 16
        },
        end: {
          line: 583,
          column: 38
        }
      },
      "274": {
        start: {
          line: 587,
          column: 4
        },
        end: {
          line: 635,
          column: 6
        }
      },
      "275": {
        start: {
          line: 588,
          column: 8
        },
        end: {
          line: 634,
          column: 11
        }
      },
      "276": {
        start: {
          line: 590,
          column: 12
        },
        end: {
          line: 633,
          column: 15
        }
      },
      "277": {
        start: {
          line: 591,
          column: 16
        },
        end: {
          line: 632,
          column: 17
        }
      },
      "278": {
        start: {
          line: 593,
          column: 24
        },
        end: {
          line: 593,
          column: 67
        }
      },
      "279": {
        start: {
          line: 594,
          column: 24
        },
        end: {
          line: 594,
          column: 90
        }
      },
      "280": {
        start: {
          line: 595,
          column: 24
        },
        end: {
          line: 595,
          column: 47
        }
      },
      "281": {
        start: {
          line: 596,
          column: 24
        },
        end: {
          line: 596,
          column: 37
        }
      },
      "282": {
        start: {
          line: 598,
          column: 24
        },
        end: {
          line: 598,
          column: 51
        }
      },
      "283": {
        start: {
          line: 599,
          column: 24
        },
        end: {
          line: 604,
          column: 32
        }
      },
      "284": {
        start: {
          line: 602,
          column: 36
        },
        end: {
          line: 602,
          column: 137
        }
      },
      "285": {
        start: {
          line: 602,
          column: 68
        },
        end: {
          line: 602,
          column: 114
        }
      },
      "286": {
        start: {
          line: 606,
          column: 24
        },
        end: {
          line: 606,
          column: 43
        }
      },
      "287": {
        start: {
          line: 607,
          column: 24
        },
        end: {
          line: 607,
          column: 75
        }
      },
      "288": {
        start: {
          line: 608,
          column: 24
        },
        end: {
          line: 608,
          column: 65
        }
      },
      "289": {
        start: {
          line: 609,
          column: 24
        },
        end: {
          line: 609,
          column: 63
        }
      },
      "290": {
        start: {
          line: 610,
          column: 24
        },
        end: {
          line: 610,
          column: 73
        }
      },
      "291": {
        start: {
          line: 611,
          column: 24
        },
        end: {
          line: 611,
          column: 48
        }
      },
      "292": {
        start: {
          line: 613,
          column: 24
        },
        end: {
          line: 613,
          column: 44
        }
      },
      "293": {
        start: {
          line: 614,
          column: 24
        },
        end: {
          line: 614,
          column: 103
        }
      },
      "294": {
        start: {
          line: 615,
          column: 24
        },
        end: {
          line: 623,
          column: 25
        }
      },
      "295": {
        start: {
          line: 616,
          column: 28
        },
        end: {
          line: 616,
          column: 48
        }
      },
      "296": {
        start: {
          line: 618,
          column: 28
        },
        end: {
          line: 618,
          column: 55
        }
      },
      "297": {
        start: {
          line: 621,
          column: 28
        },
        end: {
          line: 621,
          column: 99
        }
      },
      "298": {
        start: {
          line: 622,
          column: 28
        },
        end: {
          line: 622,
          column: 69
        }
      },
      "299": {
        start: {
          line: 624,
          column: 24
        },
        end: {
          line: 624,
          column: 63
        }
      },
      "300": {
        start: {
          line: 625,
          column: 24
        },
        end: {
          line: 625,
          column: 74
        }
      },
      "301": {
        start: {
          line: 626,
          column: 24
        },
        end: {
          line: 626,
          column: 48
        }
      },
      "302": {
        start: {
          line: 628,
          column: 24
        },
        end: {
          line: 628,
          column: 70
        }
      },
      "303": {
        start: {
          line: 629,
          column: 24
        },
        end: {
          line: 629,
          column: 90
        }
      },
      "304": {
        start: {
          line: 630,
          column: 24
        },
        end: {
          line: 630,
          column: 50
        }
      },
      "305": {
        start: {
          line: 631,
          column: 28
        },
        end: {
          line: 631,
          column: 50
        }
      },
      "306": {
        start: {
          line: 636,
          column: 4
        },
        end: {
          line: 643,
          column: 6
        }
      },
      "307": {
        start: {
          line: 637,
          column: 20
        },
        end: {
          line: 637,
          column: 24
        }
      },
      "308": {
        start: {
          line: 638,
          column: 8
        },
        end: {
          line: 639,
          column: 24
        }
      },
      "309": {
        start: {
          line: 639,
          column: 12
        },
        end: {
          line: 639,
          column: 24
        }
      },
      "310": {
        start: {
          line: 640,
          column: 8
        },
        end: {
          line: 642,
          column: 11
        }
      },
      "311": {
        start: {
          line: 641,
          column: 12
        },
        end: {
          line: 641,
          column: 56
        }
      },
      "312": {
        start: {
          line: 644,
          column: 4
        },
        end: {
          line: 670,
          column: 6
        }
      },
      "313": {
        start: {
          line: 645,
          column: 8
        },
        end: {
          line: 669,
          column: 11
        }
      },
      "314": {
        start: {
          line: 646,
          column: 24
        },
        end: {
          line: 646,
          column: 28
        }
      },
      "315": {
        start: {
          line: 647,
          column: 12
        },
        end: {
          line: 668,
          column: 15
        }
      },
      "316": {
        start: {
          line: 648,
          column: 16
        },
        end: {
          line: 667,
          column: 24
        }
      },
      "317": {
        start: {
          line: 649,
          column: 46
        },
        end: {
          line: 665,
          column: 25
        }
      },
      "318": {
        start: {
          line: 650,
          column: 28
        },
        end: {
          line: 664,
          column: 29
        }
      },
      "319": {
        start: {
          line: 651,
          column: 45
        },
        end: {
          line: 651,
          column: 87
        }
      },
      "320": {
        start: {
          line: 652,
          column: 32
        },
        end: {
          line: 657,
          column: 33
        }
      },
      "321": {
        start: {
          line: 653,
          column: 36
        },
        end: {
          line: 653,
          column: 68
        }
      },
      "322": {
        start: {
          line: 656,
          column: 36
        },
        end: {
          line: 656,
          column: 52
        }
      },
      "323": {
        start: {
          line: 659,
          column: 33
        },
        end: {
          line: 664,
          column: 29
        }
      },
      "324": {
        start: {
          line: 660,
          column: 32
        },
        end: {
          line: 660,
          column: 73
        }
      },
      "325": {
        start: {
          line: 663,
          column: 32
        },
        end: {
          line: 663,
          column: 64
        }
      },
      "326": {
        start: {
          line: 666,
          column: 24
        },
        end: {
          line: 666,
          column: 42
        }
      },
      "327": {
        start: {
          line: 671,
          column: 4
        },
        end: {
          line: 680,
          column: 6
        }
      },
      "328": {
        start: {
          line: 672,
          column: 24
        },
        end: {
          line: 672,
          column: 94
        }
      },
      "329": {
        start: {
          line: 673,
          column: 8
        },
        end: {
          line: 673,
          column: 103
        }
      },
      "330": {
        start: {
          line: 674,
          column: 8
        },
        end: {
          line: 679,
          column: 9
        }
      },
      "331": {
        start: {
          line: 675,
          column: 12
        },
        end: {
          line: 675,
          column: 140
        }
      },
      "332": {
        start: {
          line: 678,
          column: 12
        },
        end: {
          line: 678,
          column: 134
        }
      },
      "333": {
        start: {
          line: 681,
          column: 4
        },
        end: {
          line: 684,
          column: 6
        }
      },
      "334": {
        start: {
          line: 683,
          column: 8
        },
        end: {
          line: 683,
          column: 118
        }
      },
      "335": {
        start: {
          line: 688,
          column: 4
        },
        end: {
          line: 690,
          column: 6
        }
      },
      "336": {
        start: {
          line: 689,
          column: 8
        },
        end: {
          line: 689,
          column: 197
        }
      },
      "337": {
        start: {
          line: 694,
          column: 4
        },
        end: {
          line: 711,
          column: 6
        }
      },
      "338": {
        start: {
          line: 695,
          column: 8
        },
        end: {
          line: 710,
          column: 11
        }
      },
      "339": {
        start: {
          line: 697,
          column: 12
        },
        end: {
          line: 709,
          column: 15
        }
      },
      "340": {
        start: {
          line: 698,
          column: 16
        },
        end: {
          line: 707,
          column: 17
        }
      },
      "341": {
        start: {
          line: 699,
          column: 20
        },
        end: {
          line: 699,
          column: 48
        }
      },
      "342": {
        start: {
          line: 700,
          column: 20
        },
        end: {
          line: 702,
          column: 55
        }
      },
      "343": {
        start: {
          line: 705,
          column: 20
        },
        end: {
          line: 705,
          column: 93
        }
      },
      "344": {
        start: {
          line: 706,
          column: 20
        },
        end: {
          line: 706,
          column: 49
        }
      },
      "345": {
        start: {
          line: 708,
          column: 16
        },
        end: {
          line: 708,
          column: 38
        }
      },
      "346": {
        start: {
          line: 712,
          column: 4
        },
        end: {
          line: 712,
          column: 37
        }
      },
      "347": {
        start: {
          line: 714,
          column: 0
        },
        end: {
          line: 714,
          column: 62
        }
      },
      "348": {
        start: {
          line: 716,
          column: 0
        },
        end: {
          line: 716,
          column: 76
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 7,
            column: 42
          },
          end: {
            line: 7,
            column: 43
          }
        },
        loc: {
          start: {
            line: 7,
            column: 54
          },
          end: {
            line: 17,
            column: 1
          }
        },
        line: 7
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 8,
            column: 32
          },
          end: {
            line: 8,
            column: 33
          }
        },
        loc: {
          start: {
            line: 8,
            column: 44
          },
          end: {
            line: 15,
            column: 5
          }
        },
        line: 8
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 18,
            column: 44
          },
          end: {
            line: 18,
            column: 45
          }
        },
        loc: {
          start: {
            line: 18,
            column: 89
          },
          end: {
            line: 26,
            column: 1
          }
        },
        line: 18
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 19,
            column: 13
          },
          end: {
            line: 19,
            column: 18
          }
        },
        loc: {
          start: {
            line: 19,
            column: 26
          },
          end: {
            line: 19,
            column: 112
          }
        },
        line: 19
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 19,
            column: 70
          },
          end: {
            line: 19,
            column: 71
          }
        },
        loc: {
          start: {
            line: 19,
            column: 89
          },
          end: {
            line: 19,
            column: 108
          }
        },
        line: 19
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 20,
            column: 36
          },
          end: {
            line: 20,
            column: 37
          }
        },
        loc: {
          start: {
            line: 20,
            column: 63
          },
          end: {
            line: 25,
            column: 5
          }
        },
        line: 20
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 21,
            column: 17
          },
          end: {
            line: 21,
            column: 26
          }
        },
        loc: {
          start: {
            line: 21,
            column: 34
          },
          end: {
            line: 21,
            column: 99
          }
        },
        line: 21
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 22,
            column: 17
          },
          end: {
            line: 22,
            column: 25
          }
        },
        loc: {
          start: {
            line: 22,
            column: 33
          },
          end: {
            line: 22,
            column: 102
          }
        },
        line: 22
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 23,
            column: 17
          },
          end: {
            line: 23,
            column: 21
          }
        },
        loc: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 118
          }
        },
        line: 23
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 27,
            column: 48
          },
          end: {
            line: 27,
            column: 49
          }
        },
        loc: {
          start: {
            line: 27,
            column: 73
          },
          end: {
            line: 53,
            column: 1
          }
        },
        line: 27
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 28,
            column: 30
          },
          end: {
            line: 28,
            column: 31
          }
        },
        loc: {
          start: {
            line: 28,
            column: 41
          },
          end: {
            line: 28,
            column: 83
          }
        },
        line: 28
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 29,
            column: 128
          },
          end: {
            line: 29,
            column: 129
          }
        },
        loc: {
          start: {
            line: 29,
            column: 139
          },
          end: {
            line: 29,
            column: 155
          }
        },
        line: 29
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 30,
            column: 13
          },
          end: {
            line: 30,
            column: 17
          }
        },
        loc: {
          start: {
            line: 30,
            column: 21
          },
          end: {
            line: 30,
            column: 70
          }
        },
        line: 30
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 30,
            column: 30
          },
          end: {
            line: 30,
            column: 31
          }
        },
        loc: {
          start: {
            line: 30,
            column: 43
          },
          end: {
            line: 30,
            column: 67
          }
        },
        line: 30
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 31,
            column: 13
          },
          end: {
            line: 31,
            column: 17
          }
        },
        loc: {
          start: {
            line: 31,
            column: 22
          },
          end: {
            line: 52,
            column: 5
          }
        },
        line: 31
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 54,
            column: 52
          },
          end: {
            line: 54,
            column: 53
          }
        },
        loc: {
          start: {
            line: 54,
            column: 78
          },
          end: {
            line: 62,
            column: 1
          }
        },
        line: 54
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 67,
            column: 47
          },
          end: {
            line: 67,
            column: 48
          }
        },
        loc: {
          start: {
            line: 67,
            column: 59
          },
          end: {
            line: 713,
            column: 1
          }
        },
        line: 67
      },
      "17": {
        name: "ConcurrentDatabaseService",
        decl: {
          start: {
            line: 68,
            column: 13
          },
          end: {
            line: 68,
            column: 38
          }
        },
        loc: {
          start: {
            line: 68,
            column: 41
          },
          end: {
            line: 87,
            column: 5
          }
        },
        line: 68
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 84,
            column: 20
          },
          end: {
            line: 84,
            column: 21
          }
        },
        loc: {
          start: {
            line: 84,
            column: 32
          },
          end: {
            line: 84,
            column: 73
          }
        },
        line: 84
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 86,
            column: 20
          },
          end: {
            line: 86,
            column: 21
          }
        },
        loc: {
          start: {
            line: 86,
            column: 32
          },
          end: {
            line: 86,
            column: 65
          }
        },
        line: 86
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 88,
            column: 44
          },
          end: {
            line: 88,
            column: 45
          }
        },
        loc: {
          start: {
            line: 88,
            column: 56
          },
          end: {
            line: 93,
            column: 5
          }
        },
        line: 88
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 97,
            column: 60
          },
          end: {
            line: 97,
            column: 61
          }
        },
        loc: {
          start: {
            line: 97,
            column: 82
          },
          end: {
            line: 147,
            column: 5
          }
        },
        line: 97
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 98,
            column: 48
          },
          end: {
            line: 98,
            column: 49
          }
        },
        loc: {
          start: {
            line: 98,
            column: 60
          },
          end: {
            line: 146,
            column: 9
          }
        },
        line: 98
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 101,
            column: 37
          },
          end: {
            line: 101,
            column: 38
          }
        },
        loc: {
          start: {
            line: 101,
            column: 51
          },
          end: {
            line: 145,
            column: 13
          }
        },
        line: 101
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 105,
            column: 54
          },
          end: {
            line: 105,
            column: 55
          }
        },
        loc: {
          start: {
            line: 105,
            column: 68
          },
          end: {
            line: 105,
            column: 85
          }
        },
        line: 105
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 110,
            column: 43
          },
          end: {
            line: 110,
            column: 44
          }
        },
        loc: {
          start: {
            line: 110,
            column: 57
          },
          end: {
            line: 113,
            column: 25
          }
        },
        line: 110
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 114,
            column: 79
          },
          end: {
            line: 114,
            column: 80
          }
        },
        loc: {
          start: {
            line: 114,
            column: 93
          },
          end: {
            line: 114,
            column: 134
          }
        },
        line: 114
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 117,
            column: 51
          },
          end: {
            line: 117,
            column: 52
          }
        },
        loc: {
          start: {
            line: 117,
            column: 76
          },
          end: {
            line: 137,
            column: 25
          }
        },
        line: 117
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 151,
            column: 74
          },
          end: {
            line: 151,
            column: 75
          }
        },
        loc: {
          start: {
            line: 151,
            column: 135
          },
          end: {
            line: 193,
            column: 5
          }
        },
        line: 151
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 152,
            column: 48
          },
          end: {
            line: 152,
            column: 49
          }
        },
        loc: {
          start: {
            line: 152,
            column: 60
          },
          end: {
            line: 192,
            column: 9
          }
        },
        line: 152
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 155,
            column: 37
          },
          end: {
            line: 155,
            column: 38
          }
        },
        loc: {
          start: {
            line: 155,
            column: 51
          },
          end: {
            line: 191,
            column: 13
          }
        },
        line: 155
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 161,
            column: 43
          },
          end: {
            line: 161,
            column: 44
          }
        },
        loc: {
          start: {
            line: 161,
            column: 55
          },
          end: {
            line: 161,
            column: 128
          }
        },
        line: 161
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 168,
            column: 43
          },
          end: {
            line: 168,
            column: 44
          }
        },
        loc: {
          start: {
            line: 168,
            column: 55
          },
          end: {
            line: 168,
            column: 97
          }
        },
        line: 168
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 175,
            column: 43
          },
          end: {
            line: 175,
            column: 44
          }
        },
        loc: {
          start: {
            line: 175,
            column: 55
          },
          end: {
            line: 175,
            column: 104
          }
        },
        line: 175
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 185,
            column: 54
          },
          end: {
            line: 185,
            column: 55
          }
        },
        loc: {
          start: {
            line: 185,
            column: 67
          },
          end: {
            line: 185,
            column: 114
          }
        },
        line: 185
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 197,
            column: 72
          },
          end: {
            line: 197,
            column: 73
          }
        },
        loc: {
          start: {
            line: 197,
            column: 90
          },
          end: {
            line: 238,
            column: 5
          }
        },
        line: 197
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 198,
            column: 48
          },
          end: {
            line: 198,
            column: 49
          }
        },
        loc: {
          start: {
            line: 198,
            column: 60
          },
          end: {
            line: 237,
            column: 9
          }
        },
        line: 198
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 202,
            column: 37
          },
          end: {
            line: 202,
            column: 38
          }
        },
        loc: {
          start: {
            line: 202,
            column: 51
          },
          end: {
            line: 236,
            column: 13
          }
        },
        line: 202
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 208,
            column: 43
          },
          end: {
            line: 208,
            column: 44
          }
        },
        loc: {
          start: {
            line: 208,
            column: 55
          },
          end: {
            line: 208,
            column: 165
          }
        },
        line: 208
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 215,
            column: 43
          },
          end: {
            line: 215,
            column: 44
          }
        },
        loc: {
          start: {
            line: 215,
            column: 55
          },
          end: {
            line: 215,
            column: 170
          }
        },
        line: 215
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 222,
            column: 43
          },
          end: {
            line: 222,
            column: 44
          }
        },
        loc: {
          start: {
            line: 222,
            column: 55
          },
          end: {
            line: 222,
            column: 102
          }
        },
        line: 222
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 231,
            column: 63
          },
          end: {
            line: 231,
            column: 64
          }
        },
        loc: {
          start: {
            line: 231,
            column: 76
          },
          end: {
            line: 231,
            column: 141
          }
        },
        line: 231
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 232,
            column: 64
          },
          end: {
            line: 232,
            column: 65
          }
        },
        loc: {
          start: {
            line: 232,
            column: 77
          },
          end: {
            line: 232,
            column: 143
          }
        },
        line: 232
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 233,
            column: 63
          },
          end: {
            line: 233,
            column: 64
          }
        },
        loc: {
          start: {
            line: 233,
            column: 76
          },
          end: {
            line: 233,
            column: 131
          }
        },
        line: 233
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 242,
            column: 71
          },
          end: {
            line: 242,
            column: 72
          }
        },
        loc: {
          start: {
            line: 242,
            column: 95
          },
          end: {
            line: 294,
            column: 5
          }
        },
        line: 242
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 243,
            column: 48
          },
          end: {
            line: 243,
            column: 49
          }
        },
        loc: {
          start: {
            line: 243,
            column: 60
          },
          end: {
            line: 293,
            column: 9
          }
        },
        line: 243
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 247,
            column: 37
          },
          end: {
            line: 247,
            column: 38
          }
        },
        loc: {
          start: {
            line: 247,
            column: 51
          },
          end: {
            line: 292,
            column: 13
          }
        },
        line: 247
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 253,
            column: 43
          },
          end: {
            line: 253,
            column: 44
          }
        },
        loc: {
          start: {
            line: 253,
            column: 55
          },
          end: {
            line: 253,
            column: 111
          }
        },
        line: 253
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 260,
            column: 43
          },
          end: {
            line: 260,
            column: 44
          }
        },
        loc: {
          start: {
            line: 260,
            column: 55
          },
          end: {
            line: 260,
            column: 108
          }
        },
        line: 260
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 267,
            column: 43
          },
          end: {
            line: 267,
            column: 44
          }
        },
        loc: {
          start: {
            line: 267,
            column: 55
          },
          end: {
            line: 267,
            column: 115
          }
        },
        line: 267
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 274,
            column: 43
          },
          end: {
            line: 274,
            column: 44
          }
        },
        loc: {
          start: {
            line: 274,
            column: 55
          },
          end: {
            line: 274,
            column: 112
          }
        },
        line: 274
      },
      "51": {
        name: "(anonymous_51)",
        decl: {
          start: {
            line: 283,
            column: 56
          },
          end: {
            line: 283,
            column: 57
          }
        },
        loc: {
          start: {
            line: 283,
            column: 69
          },
          end: {
            line: 283,
            column: 128
          }
        },
        line: 283
      },
      "52": {
        name: "(anonymous_52)",
        decl: {
          start: {
            line: 284,
            column: 53
          },
          end: {
            line: 284,
            column: 54
          }
        },
        loc: {
          start: {
            line: 284,
            column: 66
          },
          end: {
            line: 284,
            column: 118
          }
        },
        line: 284
      },
      "53": {
        name: "(anonymous_53)",
        decl: {
          start: {
            line: 285,
            column: 60
          },
          end: {
            line: 285,
            column: 61
          }
        },
        loc: {
          start: {
            line: 285,
            column: 73
          },
          end: {
            line: 285,
            column: 125
          }
        },
        line: 285
      },
      "54": {
        name: "(anonymous_54)",
        decl: {
          start: {
            line: 286,
            column: 56
          },
          end: {
            line: 286,
            column: 57
          }
        },
        loc: {
          start: {
            line: 286,
            column: 69
          },
          end: {
            line: 286,
            column: 118
          }
        },
        line: 286
      },
      "55": {
        name: "(anonymous_55)",
        decl: {
          start: {
            line: 298,
            column: 63
          },
          end: {
            line: 298,
            column: 64
          }
        },
        loc: {
          start: {
            line: 298,
            column: 108
          },
          end: {
            line: 332,
            column: 5
          }
        },
        line: 298
      },
      "56": {
        name: "(anonymous_56)",
        decl: {
          start: {
            line: 299,
            column: 48
          },
          end: {
            line: 299,
            column: 49
          }
        },
        loc: {
          start: {
            line: 299,
            column: 60
          },
          end: {
            line: 331,
            column: 9
          }
        },
        line: 299
      },
      "57": {
        name: "(anonymous_57)",
        decl: {
          start: {
            line: 302,
            column: 37
          },
          end: {
            line: 302,
            column: 38
          }
        },
        loc: {
          start: {
            line: 302,
            column: 51
          },
          end: {
            line: 330,
            column: 13
          }
        },
        line: 302
      },
      "58": {
        name: "(anonymous_58)",
        decl: {
          start: {
            line: 333,
            column: 59
          },
          end: {
            line: 333,
            column: 60
          }
        },
        loc: {
          start: {
            line: 333,
            column: 77
          },
          end: {
            line: 358,
            column: 5
          }
        },
        line: 333
      },
      "59": {
        name: "(anonymous_59)",
        decl: {
          start: {
            line: 334,
            column: 48
          },
          end: {
            line: 334,
            column: 49
          }
        },
        loc: {
          start: {
            line: 334,
            column: 60
          },
          end: {
            line: 357,
            column: 9
          }
        },
        line: 334
      },
      "60": {
        name: "(anonymous_60)",
        decl: {
          start: {
            line: 335,
            column: 37
          },
          end: {
            line: 335,
            column: 38
          }
        },
        loc: {
          start: {
            line: 335,
            column: 51
          },
          end: {
            line: 356,
            column: 13
          }
        },
        line: 335
      },
      "61": {
        name: "(anonymous_61)",
        decl: {
          start: {
            line: 359,
            column: 66
          },
          end: {
            line: 359,
            column: 67
          }
        },
        loc: {
          start: {
            line: 359,
            column: 84
          },
          end: {
            line: 378,
            column: 5
          }
        },
        line: 359
      },
      "62": {
        name: "(anonymous_62)",
        decl: {
          start: {
            line: 360,
            column: 48
          },
          end: {
            line: 360,
            column: 49
          }
        },
        loc: {
          start: {
            line: 360,
            column: 60
          },
          end: {
            line: 377,
            column: 9
          }
        },
        line: 360
      },
      "63": {
        name: "(anonymous_63)",
        decl: {
          start: {
            line: 361,
            column: 37
          },
          end: {
            line: 361,
            column: 38
          }
        },
        loc: {
          start: {
            line: 361,
            column: 51
          },
          end: {
            line: 376,
            column: 13
          }
        },
        line: 361
      },
      "64": {
        name: "(anonymous_64)",
        decl: {
          start: {
            line: 379,
            column: 64
          },
          end: {
            line: 379,
            column: 65
          }
        },
        loc: {
          start: {
            line: 379,
            column: 82
          },
          end: {
            line: 401,
            column: 5
          }
        },
        line: 379
      },
      "65": {
        name: "(anonymous_65)",
        decl: {
          start: {
            line: 380,
            column: 48
          },
          end: {
            line: 380,
            column: 49
          }
        },
        loc: {
          start: {
            line: 380,
            column: 60
          },
          end: {
            line: 400,
            column: 9
          }
        },
        line: 380
      },
      "66": {
        name: "(anonymous_66)",
        decl: {
          start: {
            line: 381,
            column: 37
          },
          end: {
            line: 381,
            column: 38
          }
        },
        loc: {
          start: {
            line: 381,
            column: 51
          },
          end: {
            line: 399,
            column: 13
          }
        },
        line: 381
      },
      "67": {
        name: "(anonymous_67)",
        decl: {
          start: {
            line: 402,
            column: 65
          },
          end: {
            line: 402,
            column: 66
          }
        },
        loc: {
          start: {
            line: 402,
            column: 83
          },
          end: {
            line: 423,
            column: 5
          }
        },
        line: 402
      },
      "68": {
        name: "(anonymous_68)",
        decl: {
          start: {
            line: 403,
            column: 48
          },
          end: {
            line: 403,
            column: 49
          }
        },
        loc: {
          start: {
            line: 403,
            column: 60
          },
          end: {
            line: 422,
            column: 9
          }
        },
        line: 403
      },
      "69": {
        name: "(anonymous_69)",
        decl: {
          start: {
            line: 404,
            column: 37
          },
          end: {
            line: 404,
            column: 38
          }
        },
        loc: {
          start: {
            line: 404,
            column: 51
          },
          end: {
            line: 421,
            column: 13
          }
        },
        line: 404
      },
      "70": {
        name: "(anonymous_70)",
        decl: {
          start: {
            line: 424,
            column: 64
          },
          end: {
            line: 424,
            column: 65
          }
        },
        loc: {
          start: {
            line: 424,
            column: 82
          },
          end: {
            line: 448,
            column: 5
          }
        },
        line: 424
      },
      "71": {
        name: "(anonymous_71)",
        decl: {
          start: {
            line: 425,
            column: 48
          },
          end: {
            line: 425,
            column: 49
          }
        },
        loc: {
          start: {
            line: 425,
            column: 60
          },
          end: {
            line: 447,
            column: 9
          }
        },
        line: 425
      },
      "72": {
        name: "(anonymous_72)",
        decl: {
          start: {
            line: 426,
            column: 37
          },
          end: {
            line: 426,
            column: 38
          }
        },
        loc: {
          start: {
            line: 426,
            column: 51
          },
          end: {
            line: 446,
            column: 13
          }
        },
        line: 426
      },
      "73": {
        name: "(anonymous_73)",
        decl: {
          start: {
            line: 449,
            column: 58
          },
          end: {
            line: 449,
            column: 59
          }
        },
        loc: {
          start: {
            line: 449,
            column: 82
          },
          end: {
            line: 468,
            column: 5
          }
        },
        line: 449
      },
      "74": {
        name: "(anonymous_74)",
        decl: {
          start: {
            line: 450,
            column: 48
          },
          end: {
            line: 450,
            column: 49
          }
        },
        loc: {
          start: {
            line: 450,
            column: 60
          },
          end: {
            line: 467,
            column: 9
          }
        },
        line: 450
      },
      "75": {
        name: "(anonymous_75)",
        decl: {
          start: {
            line: 451,
            column: 37
          },
          end: {
            line: 451,
            column: 38
          }
        },
        loc: {
          start: {
            line: 451,
            column: 51
          },
          end: {
            line: 466,
            column: 13
          }
        },
        line: 451
      },
      "76": {
        name: "(anonymous_76)",
        decl: {
          start: {
            line: 472,
            column: 67
          },
          end: {
            line: 472,
            column: 68
          }
        },
        loc: {
          start: {
            line: 472,
            column: 91
          },
          end: {
            line: 499,
            column: 5
          }
        },
        line: 472
      },
      "77": {
        name: "(anonymous_77)",
        decl: {
          start: {
            line: 473,
            column: 48
          },
          end: {
            line: 473,
            column: 49
          }
        },
        loc: {
          start: {
            line: 473,
            column: 60
          },
          end: {
            line: 498,
            column: 9
          }
        },
        line: 473
      },
      "78": {
        name: "(anonymous_78)",
        decl: {
          start: {
            line: 474,
            column: 37
          },
          end: {
            line: 474,
            column: 38
          }
        },
        loc: {
          start: {
            line: 474,
            column: 51
          },
          end: {
            line: 497,
            column: 13
          }
        },
        line: 474
      },
      "79": {
        name: "(anonymous_79)",
        decl: {
          start: {
            line: 500,
            column: 64
          },
          end: {
            line: 500,
            column: 65
          }
        },
        loc: {
          start: {
            line: 500,
            column: 88
          },
          end: {
            line: 522,
            column: 5
          }
        },
        line: 500
      },
      "80": {
        name: "(anonymous_80)",
        decl: {
          start: {
            line: 501,
            column: 48
          },
          end: {
            line: 501,
            column: 49
          }
        },
        loc: {
          start: {
            line: 501,
            column: 60
          },
          end: {
            line: 521,
            column: 9
          }
        },
        line: 501
      },
      "81": {
        name: "(anonymous_81)",
        decl: {
          start: {
            line: 502,
            column: 37
          },
          end: {
            line: 502,
            column: 38
          }
        },
        loc: {
          start: {
            line: 502,
            column: 51
          },
          end: {
            line: 520,
            column: 13
          }
        },
        line: 502
      },
      "82": {
        name: "(anonymous_82)",
        decl: {
          start: {
            line: 523,
            column: 71
          },
          end: {
            line: 523,
            column: 72
          }
        },
        loc: {
          start: {
            line: 523,
            column: 95
          },
          end: {
            line: 550,
            column: 5
          }
        },
        line: 523
      },
      "83": {
        name: "(anonymous_83)",
        decl: {
          start: {
            line: 524,
            column: 48
          },
          end: {
            line: 524,
            column: 49
          }
        },
        loc: {
          start: {
            line: 524,
            column: 60
          },
          end: {
            line: 549,
            column: 9
          }
        },
        line: 524
      },
      "84": {
        name: "(anonymous_84)",
        decl: {
          start: {
            line: 525,
            column: 37
          },
          end: {
            line: 525,
            column: 38
          }
        },
        loc: {
          start: {
            line: 525,
            column: 51
          },
          end: {
            line: 548,
            column: 13
          }
        },
        line: 525
      },
      "85": {
        name: "(anonymous_85)",
        decl: {
          start: {
            line: 551,
            column: 68
          },
          end: {
            line: 551,
            column: 69
          }
        },
        loc: {
          start: {
            line: 551,
            column: 92
          },
          end: {
            line: 559,
            column: 5
          }
        },
        line: 551
      },
      "86": {
        name: "(anonymous_86)",
        decl: {
          start: {
            line: 552,
            column: 48
          },
          end: {
            line: 552,
            column: 49
          }
        },
        loc: {
          start: {
            line: 552,
            column: 60
          },
          end: {
            line: 558,
            column: 9
          }
        },
        line: 552
      },
      "87": {
        name: "(anonymous_87)",
        decl: {
          start: {
            line: 553,
            column: 37
          },
          end: {
            line: 553,
            column: 38
          }
        },
        loc: {
          start: {
            line: 553,
            column: 51
          },
          end: {
            line: 557,
            column: 13
          }
        },
        line: 553
      },
      "88": {
        name: "(anonymous_88)",
        decl: {
          start: {
            line: 563,
            column: 64
          },
          end: {
            line: 563,
            column: 65
          }
        },
        loc: {
          start: {
            line: 563,
            column: 76
          },
          end: {
            line: 586,
            column: 5
          }
        },
        line: 563
      },
      "89": {
        name: "(anonymous_89)",
        decl: {
          start: {
            line: 564,
            column: 48
          },
          end: {
            line: 564,
            column: 49
          }
        },
        loc: {
          start: {
            line: 564,
            column: 60
          },
          end: {
            line: 585,
            column: 9
          }
        },
        line: 564
      },
      "90": {
        name: "(anonymous_90)",
        decl: {
          start: {
            line: 567,
            column: 37
          },
          end: {
            line: 567,
            column: 38
          }
        },
        loc: {
          start: {
            line: 567,
            column: 51
          },
          end: {
            line: 584,
            column: 13
          }
        },
        line: 567
      },
      "91": {
        name: "(anonymous_91)",
        decl: {
          start: {
            line: 572,
            column: 28
          },
          end: {
            line: 572,
            column: 29
          }
        },
        loc: {
          start: {
            line: 572,
            column: 42
          },
          end: {
            line: 572,
            column: 91
          }
        },
        line: 572
      },
      "92": {
        name: "(anonymous_92)",
        decl: {
          start: {
            line: 573,
            column: 28
          },
          end: {
            line: 573,
            column: 29
          }
        },
        loc: {
          start: {
            line: 573,
            column: 42
          },
          end: {
            line: 573,
            column: 82
          }
        },
        line: 573
      },
      "93": {
        name: "(anonymous_93)",
        decl: {
          start: {
            line: 574,
            column: 26
          },
          end: {
            line: 574,
            column: 27
          }
        },
        loc: {
          start: {
            line: 574,
            column: 42
          },
          end: {
            line: 577,
            column: 17
          }
        },
        line: 574
      },
      "94": {
        name: "(anonymous_94)",
        decl: {
          start: {
            line: 587,
            column: 59
          },
          end: {
            line: 587,
            column: 60
          }
        },
        loc: {
          start: {
            line: 587,
            column: 80
          },
          end: {
            line: 635,
            column: 5
          }
        },
        line: 587
      },
      "95": {
        name: "(anonymous_95)",
        decl: {
          start: {
            line: 588,
            column: 48
          },
          end: {
            line: 588,
            column: 49
          }
        },
        loc: {
          start: {
            line: 588,
            column: 60
          },
          end: {
            line: 634,
            column: 9
          }
        },
        line: 588
      },
      "96": {
        name: "(anonymous_96)",
        decl: {
          start: {
            line: 590,
            column: 37
          },
          end: {
            line: 590,
            column: 38
          }
        },
        loc: {
          start: {
            line: 590,
            column: 51
          },
          end: {
            line: 633,
            column: 13
          }
        },
        line: 590
      },
      "97": {
        name: "(anonymous_97)",
        decl: {
          start: {
            line: 601,
            column: 44
          },
          end: {
            line: 601,
            column: 45
          }
        },
        loc: {
          start: {
            line: 601,
            column: 65
          },
          end: {
            line: 603,
            column: 33
          }
        },
        line: 601
      },
      "98": {
        name: "(anonymous_98)",
        decl: {
          start: {
            line: 602,
            column: 54
          },
          end: {
            line: 602,
            column: 55
          }
        },
        loc: {
          start: {
            line: 602,
            column: 66
          },
          end: {
            line: 602,
            column: 116
          }
        },
        line: 602
      },
      "99": {
        name: "(anonymous_99)",
        decl: {
          start: {
            line: 636,
            column: 61
          },
          end: {
            line: 636,
            column: 62
          }
        },
        loc: {
          start: {
            line: 636,
            column: 82
          },
          end: {
            line: 643,
            column: 5
          }
        },
        line: 636
      },
      "100": {
        name: "(anonymous_100)",
        decl: {
          start: {
            line: 640,
            column: 44
          },
          end: {
            line: 640,
            column: 45
          }
        },
        loc: {
          start: {
            line: 640,
            column: 61
          },
          end: {
            line: 642,
            column: 9
          }
        },
        line: 640
      },
      "101": {
        name: "(anonymous_101)",
        decl: {
          start: {
            line: 644,
            column: 59
          },
          end: {
            line: 644,
            column: 60
          }
        },
        loc: {
          start: {
            line: 644,
            column: 82
          },
          end: {
            line: 670,
            column: 5
          }
        },
        line: 644
      },
      "102": {
        name: "(anonymous_102)",
        decl: {
          start: {
            line: 645,
            column: 48
          },
          end: {
            line: 645,
            column: 49
          }
        },
        loc: {
          start: {
            line: 645,
            column: 60
          },
          end: {
            line: 669,
            column: 9
          }
        },
        line: 645
      },
      "103": {
        name: "(anonymous_103)",
        decl: {
          start: {
            line: 647,
            column: 37
          },
          end: {
            line: 647,
            column: 38
          }
        },
        loc: {
          start: {
            line: 647,
            column: 51
          },
          end: {
            line: 668,
            column: 13
          }
        },
        line: 647
      },
      "104": {
        name: "(anonymous_104)",
        decl: {
          start: {
            line: 648,
            column: 50
          },
          end: {
            line: 648,
            column: 51
          }
        },
        loc: {
          start: {
            line: 648,
            column: 77
          },
          end: {
            line: 667,
            column: 21
          }
        },
        line: 648
      },
      "105": {
        name: "(anonymous_105)",
        decl: {
          start: {
            line: 649,
            column: 46
          },
          end: {
            line: 649,
            column: 47
          }
        },
        loc: {
          start: {
            line: 649,
            column: 58
          },
          end: {
            line: 665,
            column: 25
          }
        },
        line: 649
      },
      "106": {
        name: "(anonymous_106)",
        decl: {
          start: {
            line: 671,
            column: 65
          },
          end: {
            line: 671,
            column: 66
          }
        },
        loc: {
          start: {
            line: 671,
            column: 99
          },
          end: {
            line: 680,
            column: 5
          }
        },
        line: 671
      },
      "107": {
        name: "(anonymous_107)",
        decl: {
          start: {
            line: 681,
            column: 56
          },
          end: {
            line: 681,
            column: 57
          }
        },
        loc: {
          start: {
            line: 681,
            column: 68
          },
          end: {
            line: 684,
            column: 5
          }
        },
        line: 681
      },
      "108": {
        name: "(anonymous_108)",
        decl: {
          start: {
            line: 688,
            column: 53
          },
          end: {
            line: 688,
            column: 54
          }
        },
        loc: {
          start: {
            line: 688,
            column: 65
          },
          end: {
            line: 690,
            column: 5
          }
        },
        line: 688
      },
      "109": {
        name: "(anonymous_109)",
        decl: {
          start: {
            line: 694,
            column: 54
          },
          end: {
            line: 694,
            column: 55
          }
        },
        loc: {
          start: {
            line: 694,
            column: 66
          },
          end: {
            line: 711,
            column: 5
          }
        },
        line: 694
      },
      "110": {
        name: "(anonymous_110)",
        decl: {
          start: {
            line: 695,
            column: 48
          },
          end: {
            line: 695,
            column: 49
          }
        },
        loc: {
          start: {
            line: 695,
            column: 60
          },
          end: {
            line: 710,
            column: 9
          }
        },
        line: 695
      },
      "111": {
        name: "(anonymous_111)",
        decl: {
          start: {
            line: 697,
            column: 37
          },
          end: {
            line: 697,
            column: 38
          }
        },
        loc: {
          start: {
            line: 697,
            column: 51
          },
          end: {
            line: 709,
            column: 13
          }
        },
        line: 697
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 17,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 7,
            column: 16
          },
          end: {
            line: 7,
            column: 20
          }
        }, {
          start: {
            line: 7,
            column: 24
          },
          end: {
            line: 7,
            column: 37
          }
        }, {
          start: {
            line: 7,
            column: 42
          },
          end: {
            line: 17,
            column: 1
          }
        }],
        line: 7
      },
      "1": {
        loc: {
          start: {
            line: 8,
            column: 15
          },
          end: {
            line: 15,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 15
          },
          end: {
            line: 8,
            column: 28
          }
        }, {
          start: {
            line: 8,
            column: 32
          },
          end: {
            line: 15,
            column: 5
          }
        }],
        line: 8
      },
      "2": {
        loc: {
          start: {
            line: 11,
            column: 29
          },
          end: {
            line: 12,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 11,
            column: 29
          },
          end: {
            line: 12,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 11
      },
      "3": {
        loc: {
          start: {
            line: 18,
            column: 16
          },
          end: {
            line: 26,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        }, {
          start: {
            line: 18,
            column: 25
          },
          end: {
            line: 18,
            column: 39
          }
        }, {
          start: {
            line: 18,
            column: 44
          },
          end: {
            line: 26,
            column: 1
          }
        }],
        line: 18
      },
      "4": {
        loc: {
          start: {
            line: 19,
            column: 35
          },
          end: {
            line: 19,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 19,
            column: 56
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 64
          },
          end: {
            line: 19,
            column: 109
          }
        }],
        line: 19
      },
      "5": {
        loc: {
          start: {
            line: 20,
            column: 16
          },
          end: {
            line: 20,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 20,
            column: 16
          },
          end: {
            line: 20,
            column: 17
          }
        }, {
          start: {
            line: 20,
            column: 22
          },
          end: {
            line: 20,
            column: 33
          }
        }],
        line: 20
      },
      "6": {
        loc: {
          start: {
            line: 23,
            column: 32
          },
          end: {
            line: 23,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 46
          },
          end: {
            line: 23,
            column: 67
          }
        }, {
          start: {
            line: 23,
            column: 70
          },
          end: {
            line: 23,
            column: 115
          }
        }],
        line: 23
      },
      "7": {
        loc: {
          start: {
            line: 24,
            column: 51
          },
          end: {
            line: 24,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 51
          },
          end: {
            line: 24,
            column: 61
          }
        }, {
          start: {
            line: 24,
            column: 65
          },
          end: {
            line: 24,
            column: 67
          }
        }],
        line: 24
      },
      "8": {
        loc: {
          start: {
            line: 27,
            column: 18
          },
          end: {
            line: 53,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 19
          },
          end: {
            line: 27,
            column: 23
          }
        }, {
          start: {
            line: 27,
            column: 27
          },
          end: {
            line: 27,
            column: 43
          }
        }, {
          start: {
            line: 27,
            column: 48
          },
          end: {
            line: 53,
            column: 1
          }
        }],
        line: 27
      },
      "9": {
        loc: {
          start: {
            line: 28,
            column: 43
          },
          end: {
            line: 28,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 43
          },
          end: {
            line: 28,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "10": {
        loc: {
          start: {
            line: 28,
            column: 134
          },
          end: {
            line: 28,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 28,
            column: 167
          },
          end: {
            line: 28,
            column: 175
          }
        }, {
          start: {
            line: 28,
            column: 178
          },
          end: {
            line: 28,
            column: 184
          }
        }],
        line: 28
      },
      "11": {
        loc: {
          start: {
            line: 29,
            column: 74
          },
          end: {
            line: 29,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 74
          },
          end: {
            line: 29,
            column: 102
          }
        }, {
          start: {
            line: 29,
            column: 107
          },
          end: {
            line: 29,
            column: 155
          }
        }],
        line: 29
      },
      "12": {
        loc: {
          start: {
            line: 32,
            column: 8
          },
          end: {
            line: 32,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 8
          },
          end: {
            line: 32,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "13": {
        loc: {
          start: {
            line: 33,
            column: 15
          },
          end: {
            line: 33,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 15
          },
          end: {
            line: 33,
            column: 16
          }
        }, {
          start: {
            line: 33,
            column: 21
          },
          end: {
            line: 33,
            column: 44
          }
        }],
        line: 33
      },
      "14": {
        loc: {
          start: {
            line: 33,
            column: 28
          },
          end: {
            line: 33,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 28
          },
          end: {
            line: 33,
            column: 33
          }
        }, {
          start: {
            line: 33,
            column: 38
          },
          end: {
            line: 33,
            column: 43
          }
        }],
        line: 33
      },
      "15": {
        loc: {
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "16": {
        loc: {
          start: {
            line: 34,
            column: 23
          },
          end: {
            line: 34,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 34,
            column: 23
          },
          end: {
            line: 34,
            column: 24
          }
        }, {
          start: {
            line: 34,
            column: 29
          },
          end: {
            line: 34,
            column: 125
          }
        }, {
          start: {
            line: 34,
            column: 130
          },
          end: {
            line: 34,
            column: 158
          }
        }],
        line: 34
      },
      "17": {
        loc: {
          start: {
            line: 34,
            column: 33
          },
          end: {
            line: 34,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 34,
            column: 45
          },
          end: {
            line: 34,
            column: 56
          }
        }, {
          start: {
            line: 34,
            column: 59
          },
          end: {
            line: 34,
            column: 125
          }
        }],
        line: 34
      },
      "18": {
        loc: {
          start: {
            line: 34,
            column: 59
          },
          end: {
            line: 34,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 34,
            column: 67
          },
          end: {
            line: 34,
            column: 116
          }
        }, {
          start: {
            line: 34,
            column: 119
          },
          end: {
            line: 34,
            column: 125
          }
        }],
        line: 34
      },
      "19": {
        loc: {
          start: {
            line: 34,
            column: 67
          },
          end: {
            line: 34,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 34,
            column: 67
          },
          end: {
            line: 34,
            column: 77
          }
        }, {
          start: {
            line: 34,
            column: 82
          },
          end: {
            line: 34,
            column: 115
          }
        }],
        line: 34
      },
      "20": {
        loc: {
          start: {
            line: 34,
            column: 82
          },
          end: {
            line: 34,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 34,
            column: 83
          },
          end: {
            line: 34,
            column: 98
          }
        }, {
          start: {
            line: 34,
            column: 103
          },
          end: {
            line: 34,
            column: 112
          }
        }],
        line: 34
      },
      "21": {
        loc: {
          start: {
            line: 35,
            column: 12
          },
          end: {
            line: 35,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 12
          },
          end: {
            line: 35,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "22": {
        loc: {
          start: {
            line: 36,
            column: 12
          },
          end: {
            line: 48,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 37,
            column: 16
          },
          end: {
            line: 37,
            column: 23
          }
        }, {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 46
          }
        }, {
          start: {
            line: 38,
            column: 16
          },
          end: {
            line: 38,
            column: 72
          }
        }, {
          start: {
            line: 39,
            column: 16
          },
          end: {
            line: 39,
            column: 65
          }
        }, {
          start: {
            line: 40,
            column: 16
          },
          end: {
            line: 40,
            column: 65
          }
        }, {
          start: {
            line: 41,
            column: 16
          },
          end: {
            line: 47,
            column: 43
          }
        }],
        line: 36
      },
      "23": {
        loc: {
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "24": {
        loc: {
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 74
          }
        }, {
          start: {
            line: 42,
            column: 79
          },
          end: {
            line: 42,
            column: 90
          }
        }, {
          start: {
            line: 42,
            column: 94
          },
          end: {
            line: 42,
            column: 105
          }
        }],
        line: 42
      },
      "25": {
        loc: {
          start: {
            line: 42,
            column: 42
          },
          end: {
            line: 42,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 42
          },
          end: {
            line: 42,
            column: 54
          }
        }, {
          start: {
            line: 42,
            column: 58
          },
          end: {
            line: 42,
            column: 73
          }
        }],
        line: 42
      },
      "26": {
        loc: {
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "27": {
        loc: {
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 43,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 43,
            column: 35
          }
        }, {
          start: {
            line: 43,
            column: 40
          },
          end: {
            line: 43,
            column: 42
          }
        }, {
          start: {
            line: 43,
            column: 47
          },
          end: {
            line: 43,
            column: 59
          }
        }, {
          start: {
            line: 43,
            column: 63
          },
          end: {
            line: 43,
            column: 75
          }
        }],
        line: 43
      },
      "28": {
        loc: {
          start: {
            line: 44,
            column: 20
          },
          end: {
            line: 44,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 20
          },
          end: {
            line: 44,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "29": {
        loc: {
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 35
          }
        }, {
          start: {
            line: 44,
            column: 39
          },
          end: {
            line: 44,
            column: 53
          }
        }],
        line: 44
      },
      "30": {
        loc: {
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 45
      },
      "31": {
        loc: {
          start: {
            line: 45,
            column: 24
          },
          end: {
            line: 45,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 45,
            column: 24
          },
          end: {
            line: 45,
            column: 25
          }
        }, {
          start: {
            line: 45,
            column: 29
          },
          end: {
            line: 45,
            column: 43
          }
        }],
        line: 45
      },
      "32": {
        loc: {
          start: {
            line: 46,
            column: 20
          },
          end: {
            line: 46,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 20
          },
          end: {
            line: 46,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "33": {
        loc: {
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 51,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 51,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 51
      },
      "34": {
        loc: {
          start: {
            line: 51,
            column: 52
          },
          end: {
            line: 51,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 51,
            column: 60
          },
          end: {
            line: 51,
            column: 65
          }
        }, {
          start: {
            line: 51,
            column: 68
          },
          end: {
            line: 51,
            column: 74
          }
        }],
        line: 51
      },
      "35": {
        loc: {
          start: {
            line: 54,
            column: 20
          },
          end: {
            line: 62,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 54,
            column: 21
          },
          end: {
            line: 54,
            column: 25
          }
        }, {
          start: {
            line: 54,
            column: 29
          },
          end: {
            line: 54,
            column: 47
          }
        }, {
          start: {
            line: 54,
            column: 52
          },
          end: {
            line: 62,
            column: 1
          }
        }],
        line: 54
      },
      "36": {
        loc: {
          start: {
            line: 55,
            column: 4
          },
          end: {
            line: 60,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 55,
            column: 4
          },
          end: {
            line: 60,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 55
      },
      "37": {
        loc: {
          start: {
            line: 55,
            column: 8
          },
          end: {
            line: 55,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 55,
            column: 8
          },
          end: {
            line: 55,
            column: 12
          }
        }, {
          start: {
            line: 55,
            column: 16
          },
          end: {
            line: 55,
            column: 38
          }
        }],
        line: 55
      },
      "38": {
        loc: {
          start: {
            line: 56,
            column: 8
          },
          end: {
            line: 59,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 56,
            column: 8
          },
          end: {
            line: 59,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 56
      },
      "39": {
        loc: {
          start: {
            line: 56,
            column: 12
          },
          end: {
            line: 56,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 56,
            column: 12
          },
          end: {
            line: 56,
            column: 14
          }
        }, {
          start: {
            line: 56,
            column: 18
          },
          end: {
            line: 56,
            column: 30
          }
        }],
        line: 56
      },
      "40": {
        loc: {
          start: {
            line: 57,
            column: 12
          },
          end: {
            line: 57,
            column: 65
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 57,
            column: 12
          },
          end: {
            line: 57,
            column: 65
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 57
      },
      "41": {
        loc: {
          start: {
            line: 61,
            column: 21
          },
          end: {
            line: 61,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 61,
            column: 21
          },
          end: {
            line: 61,
            column: 23
          }
        }, {
          start: {
            line: 61,
            column: 27
          },
          end: {
            line: 61,
            column: 59
          }
        }],
        line: 61
      },
      "42": {
        loc: {
          start: {
            line: 81,
            column: 48
          },
          end: {
            line: 81,
            column: 88
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 81,
            column: 48
          },
          end: {
            line: 81,
            column: 81
          }
        }, {
          start: {
            line: 81,
            column: 85
          },
          end: {
            line: 81,
            column: 88
          }
        }],
        line: 81
      },
      "43": {
        loc: {
          start: {
            line: 82,
            column: 39
          },
          end: {
            line: 82,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 82,
            column: 39
          },
          end: {
            line: 82,
            column: 71
          }
        }, {
          start: {
            line: 82,
            column: 75
          },
          end: {
            line: 82,
            column: 82
          }
        }],
        line: 82
      },
      "44": {
        loc: {
          start: {
            line: 89,
            column: 8
          },
          end: {
            line: 91,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 89,
            column: 8
          },
          end: {
            line: 91,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 89
      },
      "45": {
        loc: {
          start: {
            line: 102,
            column: 16
          },
          end: {
            line: 144,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 103,
            column: 20
          },
          end: {
            line: 106,
            column: 37
          }
        }, {
          start: {
            line: 107,
            column: 20
          },
          end: {
            line: 114,
            column: 138
          }
        }, {
          start: {
            line: 115,
            column: 20
          },
          end: {
            line: 138,
            column: 60
          }
        }, {
          start: {
            line: 139,
            column: 20
          },
          end: {
            line: 142,
            column: 38
          }
        }, {
          start: {
            line: 143,
            column: 20
          },
          end: {
            line: 143,
            column: 50
          }
        }],
        line: 102
      },
      "46": {
        loc: {
          start: {
            line: 121,
            column: 28
          },
          end: {
            line: 136,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 121,
            column: 28
          },
          end: {
            line: 136,
            column: 29
          }
        }, {
          start: {
            line: 129,
            column: 33
          },
          end: {
            line: 136,
            column: 29
          }
        }],
        line: 121
      },
      "47": {
        loc: {
          start: {
            line: 132,
            column: 43
          },
          end: {
            line: 132,
            column: 135
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 132,
            column: 44
          },
          end: {
            line: 132,
            column: 112
          }
        }, {
          start: {
            line: 132,
            column: 117
          },
          end: {
            line: 132,
            column: 135
          }
        }],
        line: 132
      },
      "48": {
        loc: {
          start: {
            line: 132,
            column: 44
          },
          end: {
            line: 132,
            column: 112
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 132,
            column: 93
          },
          end: {
            line: 132,
            column: 99
          }
        }, {
          start: {
            line: 132,
            column: 102
          },
          end: {
            line: 132,
            column: 112
          }
        }],
        line: 132
      },
      "49": {
        loc: {
          start: {
            line: 132,
            column: 44
          },
          end: {
            line: 132,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 132,
            column: 44
          },
          end: {
            line: 132,
            column: 73
          }
        }, {
          start: {
            line: 132,
            column: 77
          },
          end: {
            line: 132,
            column: 90
          }
        }],
        line: 132
      },
      "50": {
        loc: {
          start: {
            line: 156,
            column: 16
          },
          end: {
            line: 190,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 157,
            column: 20
          },
          end: {
            line: 182,
            column: 81
          }
        }, {
          start: {
            line: 183,
            column: 20
          },
          end: {
            line: 189,
            column: 67
          }
        }],
        line: 156
      },
      "51": {
        loc: {
          start: {
            line: 186,
            column: 24
          },
          end: {
            line: 188,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 186,
            column: 24
          },
          end: {
            line: 188,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 186
      },
      "52": {
        loc: {
          start: {
            line: 186,
            column: 30
          },
          end: {
            line: 186,
            column: 116
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 186,
            column: 85
          },
          end: {
            line: 186,
            column: 91
          }
        }, {
          start: {
            line: 186,
            column: 94
          },
          end: {
            line: 186,
            column: 116
          }
        }],
        line: 186
      },
      "53": {
        loc: {
          start: {
            line: 186,
            column: 30
          },
          end: {
            line: 186,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 186,
            column: 30
          },
          end: {
            line: 186,
            column: 53
          }
        }, {
          start: {
            line: 186,
            column: 57
          },
          end: {
            line: 186,
            column: 82
          }
        }],
        line: 186
      },
      "54": {
        loc: {
          start: {
            line: 187,
            column: 44
          },
          end: {
            line: 187,
            column: 171
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 187,
            column: 45
          },
          end: {
            line: 187,
            column: 129
          }
        }, {
          start: {
            line: 187,
            column: 134
          },
          end: {
            line: 187,
            column: 171
          }
        }],
        line: 187
      },
      "55": {
        loc: {
          start: {
            line: 187,
            column: 45
          },
          end: {
            line: 187,
            column: 129
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 187,
            column: 100
          },
          end: {
            line: 187,
            column: 106
          }
        }, {
          start: {
            line: 187,
            column: 109
          },
          end: {
            line: 187,
            column: 129
          }
        }],
        line: 187
      },
      "56": {
        loc: {
          start: {
            line: 187,
            column: 45
          },
          end: {
            line: 187,
            column: 97
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 187,
            column: 45
          },
          end: {
            line: 187,
            column: 68
          }
        }, {
          start: {
            line: 187,
            column: 72
          },
          end: {
            line: 187,
            column: 97
          }
        }],
        line: 187
      },
      "57": {
        loc: {
          start: {
            line: 203,
            column: 16
          },
          end: {
            line: 235,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 204,
            column: 20
          },
          end: {
            line: 228,
            column: 81
          }
        }, {
          start: {
            line: 229,
            column: 20
          },
          end: {
            line: 234,
            column: 168
          }
        }],
        line: 203
      },
      "58": {
        loc: {
          start: {
            line: 231,
            column: 43
          },
          end: {
            line: 231,
            column: 195
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 231,
            column: 44
          },
          end: {
            line: 231,
            column: 188
          }
        }, {
          start: {
            line: 231,
            column: 193
          },
          end: {
            line: 231,
            column: 195
          }
        }],
        line: 231
      },
      "59": {
        loc: {
          start: {
            line: 231,
            column: 44
          },
          end: {
            line: 231,
            column: 188
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 231,
            column: 172
          },
          end: {
            line: 231,
            column: 178
          }
        }, {
          start: {
            line: 231,
            column: 181
          },
          end: {
            line: 231,
            column: 188
          }
        }],
        line: 231
      },
      "60": {
        loc: {
          start: {
            line: 231,
            column: 44
          },
          end: {
            line: 231,
            column: 169
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 231,
            column: 44
          },
          end: {
            line: 231,
            column: 152
          }
        }, {
          start: {
            line: 231,
            column: 156
          },
          end: {
            line: 231,
            column: 169
          }
        }],
        line: 231
      },
      "61": {
        loc: {
          start: {
            line: 232,
            column: 44
          },
          end: {
            line: 232,
            column: 197
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 232,
            column: 45
          },
          end: {
            line: 232,
            column: 190
          }
        }, {
          start: {
            line: 232,
            column: 195
          },
          end: {
            line: 232,
            column: 197
          }
        }],
        line: 232
      },
      "62": {
        loc: {
          start: {
            line: 232,
            column: 45
          },
          end: {
            line: 232,
            column: 190
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 232,
            column: 174
          },
          end: {
            line: 232,
            column: 180
          }
        }, {
          start: {
            line: 232,
            column: 183
          },
          end: {
            line: 232,
            column: 190
          }
        }],
        line: 232
      },
      "63": {
        loc: {
          start: {
            line: 232,
            column: 45
          },
          end: {
            line: 232,
            column: 171
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 232,
            column: 45
          },
          end: {
            line: 232,
            column: 154
          }
        }, {
          start: {
            line: 232,
            column: 158
          },
          end: {
            line: 232,
            column: 171
          }
        }],
        line: 232
      },
      "64": {
        loc: {
          start: {
            line: 233,
            column: 43
          },
          end: {
            line: 233,
            column: 185
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 233,
            column: 44
          },
          end: {
            line: 233,
            column: 178
          }
        }, {
          start: {
            line: 233,
            column: 183
          },
          end: {
            line: 233,
            column: 185
          }
        }],
        line: 233
      },
      "65": {
        loc: {
          start: {
            line: 233,
            column: 44
          },
          end: {
            line: 233,
            column: 178
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 233,
            column: 162
          },
          end: {
            line: 233,
            column: 168
          }
        }, {
          start: {
            line: 233,
            column: 171
          },
          end: {
            line: 233,
            column: 178
          }
        }],
        line: 233
      },
      "66": {
        loc: {
          start: {
            line: 233,
            column: 44
          },
          end: {
            line: 233,
            column: 159
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 233,
            column: 44
          },
          end: {
            line: 233,
            column: 142
          }
        }, {
          start: {
            line: 233,
            column: 146
          },
          end: {
            line: 233,
            column: 159
          }
        }],
        line: 233
      },
      "67": {
        loc: {
          start: {
            line: 248,
            column: 16
          },
          end: {
            line: 291,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 249,
            column: 20
          },
          end: {
            line: 280,
            column: 81
          }
        }, {
          start: {
            line: 281,
            column: 20
          },
          end: {
            line: 290,
            column: 163
          }
        }],
        line: 248
      },
      "68": {
        loc: {
          start: {
            line: 283,
            column: 37
          },
          end: {
            line: 283,
            column: 175
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 283,
            column: 159
          },
          end: {
            line: 283,
            column: 165
          }
        }, {
          start: {
            line: 283,
            column: 168
          },
          end: {
            line: 283,
            column: 175
          }
        }],
        line: 283
      },
      "69": {
        loc: {
          start: {
            line: 283,
            column: 37
          },
          end: {
            line: 283,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 283,
            column: 37
          },
          end: {
            line: 283,
            column: 139
          }
        }, {
          start: {
            line: 283,
            column: 143
          },
          end: {
            line: 283,
            column: 156
          }
        }],
        line: 283
      },
      "70": {
        loc: {
          start: {
            line: 284,
            column: 33
          },
          end: {
            line: 284,
            column: 172
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 284,
            column: 34
          },
          end: {
            line: 284,
            column: 165
          }
        }, {
          start: {
            line: 284,
            column: 170
          },
          end: {
            line: 284,
            column: 172
          }
        }],
        line: 284
      },
      "71": {
        loc: {
          start: {
            line: 284,
            column: 34
          },
          end: {
            line: 284,
            column: 165
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 284,
            column: 149
          },
          end: {
            line: 284,
            column: 155
          }
        }, {
          start: {
            line: 284,
            column: 158
          },
          end: {
            line: 284,
            column: 165
          }
        }],
        line: 284
      },
      "72": {
        loc: {
          start: {
            line: 284,
            column: 34
          },
          end: {
            line: 284,
            column: 146
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 284,
            column: 34
          },
          end: {
            line: 284,
            column: 129
          }
        }, {
          start: {
            line: 284,
            column: 133
          },
          end: {
            line: 284,
            column: 146
          }
        }],
        line: 284
      },
      "73": {
        loc: {
          start: {
            line: 285,
            column: 40
          },
          end: {
            line: 285,
            column: 179
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 285,
            column: 41
          },
          end: {
            line: 285,
            column: 172
          }
        }, {
          start: {
            line: 285,
            column: 177
          },
          end: {
            line: 285,
            column: 179
          }
        }],
        line: 285
      },
      "74": {
        loc: {
          start: {
            line: 285,
            column: 41
          },
          end: {
            line: 285,
            column: 172
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 285,
            column: 156
          },
          end: {
            line: 285,
            column: 162
          }
        }, {
          start: {
            line: 285,
            column: 165
          },
          end: {
            line: 285,
            column: 172
          }
        }],
        line: 285
      },
      "75": {
        loc: {
          start: {
            line: 285,
            column: 41
          },
          end: {
            line: 285,
            column: 153
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 285,
            column: 41
          },
          end: {
            line: 285,
            column: 136
          }
        }, {
          start: {
            line: 285,
            column: 140
          },
          end: {
            line: 285,
            column: 153
          }
        }],
        line: 285
      },
      "76": {
        loc: {
          start: {
            line: 286,
            column: 37
          },
          end: {
            line: 286,
            column: 165
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 286,
            column: 149
          },
          end: {
            line: 286,
            column: 155
          }
        }, {
          start: {
            line: 286,
            column: 158
          },
          end: {
            line: 286,
            column: 165
          }
        }],
        line: 286
      },
      "77": {
        loc: {
          start: {
            line: 286,
            column: 37
          },
          end: {
            line: 286,
            column: 146
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 286,
            column: 37
          },
          end: {
            line: 286,
            column: 129
          }
        }, {
          start: {
            line: 286,
            column: 133
          },
          end: {
            line: 286,
            column: 146
          }
        }],
        line: 286
      },
      "78": {
        loc: {
          start: {
            line: 287,
            column: 24
          },
          end: {
            line: 289,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 287,
            column: 24
          },
          end: {
            line: 289,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 287
      },
      "79": {
        loc: {
          start: {
            line: 303,
            column: 16
          },
          end: {
            line: 329,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 304,
            column: 20
          },
          end: {
            line: 327,
            column: 32
          }
        }, {
          start: {
            line: 328,
            column: 20
          },
          end: {
            line: 328,
            column: 61
          }
        }],
        line: 303
      },
      "80": {
        loc: {
          start: {
            line: 310,
            column: 56
          },
          end: {
            line: 310,
            column: 154
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 310,
            column: 57
          },
          end: {
            line: 310,
            column: 145
          }
        }, {
          start: {
            line: 310,
            column: 150
          },
          end: {
            line: 310,
            column: 154
          }
        }],
        line: 310
      },
      "81": {
        loc: {
          start: {
            line: 310,
            column: 57
          },
          end: {
            line: 310,
            column: 145
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 310,
            column: 121
          },
          end: {
            line: 310,
            column: 127
          }
        }, {
          start: {
            line: 310,
            column: 130
          },
          end: {
            line: 310,
            column: 145
          }
        }],
        line: 310
      },
      "82": {
        loc: {
          start: {
            line: 310,
            column: 57
          },
          end: {
            line: 310,
            column: 118
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 310,
            column: 57
          },
          end: {
            line: 310,
            column: 101
          }
        }, {
          start: {
            line: 310,
            column: 105
          },
          end: {
            line: 310,
            column: 118
          }
        }],
        line: 310
      },
      "83": {
        loc: {
          start: {
            line: 311,
            column: 58
          },
          end: {
            line: 311,
            column: 148
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 311,
            column: 122
          },
          end: {
            line: 311,
            column: 128
          }
        }, {
          start: {
            line: 311,
            column: 131
          },
          end: {
            line: 311,
            column: 148
          }
        }],
        line: 311
      },
      "84": {
        loc: {
          start: {
            line: 311,
            column: 58
          },
          end: {
            line: 311,
            column: 119
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 311,
            column: 58
          },
          end: {
            line: 311,
            column: 102
          }
        }, {
          start: {
            line: 311,
            column: 106
          },
          end: {
            line: 311,
            column: 119
          }
        }],
        line: 311
      },
      "85": {
        loc: {
          start: {
            line: 312,
            column: 53
          },
          end: {
            line: 312,
            column: 140
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 312,
            column: 117
          },
          end: {
            line: 312,
            column: 123
          }
        }, {
          start: {
            line: 312,
            column: 126
          },
          end: {
            line: 312,
            column: 140
          }
        }],
        line: 312
      },
      "86": {
        loc: {
          start: {
            line: 312,
            column: 53
          },
          end: {
            line: 312,
            column: 114
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 312,
            column: 53
          },
          end: {
            line: 312,
            column: 97
          }
        }, {
          start: {
            line: 312,
            column: 101
          },
          end: {
            line: 312,
            column: 114
          }
        }],
        line: 312
      },
      "87": {
        loc: {
          start: {
            line: 313,
            column: 47
          },
          end: {
            line: 313,
            column: 127
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 313,
            column: 106
          },
          end: {
            line: 313,
            column: 112
          }
        }, {
          start: {
            line: 313,
            column: 115
          },
          end: {
            line: 313,
            column: 127
          }
        }],
        line: 313
      },
      "88": {
        loc: {
          start: {
            line: 313,
            column: 47
          },
          end: {
            line: 313,
            column: 103
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 313,
            column: 47
          },
          end: {
            line: 313,
            column: 86
          }
        }, {
          start: {
            line: 313,
            column: 90
          },
          end: {
            line: 313,
            column: 103
          }
        }],
        line: 313
      },
      "89": {
        loc: {
          start: {
            line: 315,
            column: 47
          },
          end: {
            line: 315,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 315,
            column: 47
          },
          end: {
            line: 315,
            column: 69
          }
        }, {
          start: {
            line: 315,
            column: 73
          },
          end: {
            line: 315,
            column: 75
          }
        }],
        line: 315
      },
      "90": {
        loc: {
          start: {
            line: 316,
            column: 50
          },
          end: {
            line: 316,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 316,
            column: 50
          },
          end: {
            line: 316,
            column: 75
          }
        }, {
          start: {
            line: 316,
            column: 79
          },
          end: {
            line: 316,
            column: 81
          }
        }],
        line: 316
      },
      "91": {
        loc: {
          start: {
            line: 317,
            column: 48
          },
          end: {
            line: 317,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 317,
            column: 48
          },
          end: {
            line: 317,
            column: 75
          }
        }, {
          start: {
            line: 317,
            column: 79
          },
          end: {
            line: 317,
            column: 83
          }
        }],
        line: 317
      },
      "92": {
        loc: {
          start: {
            line: 319,
            column: 52
          },
          end: {
            line: 319,
            column: 143
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 319,
            column: 53
          },
          end: {
            line: 319,
            column: 136
          }
        }, {
          start: {
            line: 319,
            column: 141
          },
          end: {
            line: 319,
            column: 143
          }
        }],
        line: 319
      },
      "93": {
        loc: {
          start: {
            line: 319,
            column: 53
          },
          end: {
            line: 319,
            column: 136
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 319,
            column: 114
          },
          end: {
            line: 319,
            column: 120
          }
        }, {
          start: {
            line: 319,
            column: 123
          },
          end: {
            line: 319,
            column: 136
          }
        }],
        line: 319
      },
      "94": {
        loc: {
          start: {
            line: 319,
            column: 53
          },
          end: {
            line: 319,
            column: 111
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 319,
            column: 53
          },
          end: {
            line: 319,
            column: 94
          }
        }, {
          start: {
            line: 319,
            column: 98
          },
          end: {
            line: 319,
            column: 111
          }
        }],
        line: 319
      },
      "95": {
        loc: {
          start: {
            line: 336,
            column: 16
          },
          end: {
            line: 355,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 337,
            column: 20
          },
          end: {
            line: 353,
            column: 28
          }
        }, {
          start: {
            line: 354,
            column: 20
          },
          end: {
            line: 354,
            column: 61
          }
        }],
        line: 336
      },
      "96": {
        loc: {
          start: {
            line: 362,
            column: 16
          },
          end: {
            line: 375,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 363,
            column: 20
          },
          end: {
            line: 373,
            column: 28
          }
        }, {
          start: {
            line: 374,
            column: 20
          },
          end: {
            line: 374,
            column: 61
          }
        }],
        line: 362
      },
      "97": {
        loc: {
          start: {
            line: 382,
            column: 16
          },
          end: {
            line: 398,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 383,
            column: 20
          },
          end: {
            line: 396,
            column: 28
          }
        }, {
          start: {
            line: 397,
            column: 20
          },
          end: {
            line: 397,
            column: 61
          }
        }],
        line: 382
      },
      "98": {
        loc: {
          start: {
            line: 405,
            column: 16
          },
          end: {
            line: 420,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 406,
            column: 20
          },
          end: {
            line: 418,
            column: 28
          }
        }, {
          start: {
            line: 419,
            column: 20
          },
          end: {
            line: 419,
            column: 61
          }
        }],
        line: 405
      },
      "99": {
        loc: {
          start: {
            line: 427,
            column: 16
          },
          end: {
            line: 445,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 428,
            column: 20
          },
          end: {
            line: 443,
            column: 28
          }
        }, {
          start: {
            line: 444,
            column: 20
          },
          end: {
            line: 444,
            column: 61
          }
        }],
        line: 427
      },
      "100": {
        loc: {
          start: {
            line: 452,
            column: 16
          },
          end: {
            line: 465,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 453,
            column: 20
          },
          end: {
            line: 463,
            column: 28
          }
        }, {
          start: {
            line: 464,
            column: 20
          },
          end: {
            line: 464,
            column: 61
          }
        }],
        line: 452
      },
      "101": {
        loc: {
          start: {
            line: 475,
            column: 16
          },
          end: {
            line: 496,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 476,
            column: 20
          },
          end: {
            line: 494,
            column: 28
          }
        }, {
          start: {
            line: 495,
            column: 20
          },
          end: {
            line: 495,
            column: 61
          }
        }],
        line: 475
      },
      "102": {
        loc: {
          start: {
            line: 503,
            column: 16
          },
          end: {
            line: 519,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 504,
            column: 20
          },
          end: {
            line: 517,
            column: 28
          }
        }, {
          start: {
            line: 518,
            column: 20
          },
          end: {
            line: 518,
            column: 61
          }
        }],
        line: 503
      },
      "103": {
        loc: {
          start: {
            line: 526,
            column: 16
          },
          end: {
            line: 547,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 527,
            column: 20
          },
          end: {
            line: 545,
            column: 28
          }
        }, {
          start: {
            line: 546,
            column: 20
          },
          end: {
            line: 546,
            column: 61
          }
        }],
        line: 526
      },
      "104": {
        loc: {
          start: {
            line: 568,
            column: 16
          },
          end: {
            line: 570,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 568,
            column: 16
          },
          end: {
            line: 570,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 568
      },
      "105": {
        loc: {
          start: {
            line: 578,
            column: 16
          },
          end: {
            line: 580,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 578,
            column: 16
          },
          end: {
            line: 580,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 578
      },
      "106": {
        loc: {
          start: {
            line: 591,
            column: 16
          },
          end: {
            line: 632,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 592,
            column: 20
          },
          end: {
            line: 596,
            column: 37
          }
        }, {
          start: {
            line: 597,
            column: 20
          },
          end: {
            line: 604,
            column: 32
          }
        }, {
          start: {
            line: 605,
            column: 20
          },
          end: {
            line: 611,
            column: 48
          }
        }, {
          start: {
            line: 612,
            column: 20
          },
          end: {
            line: 626,
            column: 48
          }
        }, {
          start: {
            line: 627,
            column: 20
          },
          end: {
            line: 630,
            column: 50
          }
        }, {
          start: {
            line: 631,
            column: 20
          },
          end: {
            line: 631,
            column: 50
          }
        }],
        line: 591
      },
      "107": {
        loc: {
          start: {
            line: 615,
            column: 24
          },
          end: {
            line: 623,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 615,
            column: 24
          },
          end: {
            line: 623,
            column: 25
          }
        }, {
          start: {
            line: 620,
            column: 29
          },
          end: {
            line: 623,
            column: 25
          }
        }],
        line: 615
      },
      "108": {
        loc: {
          start: {
            line: 638,
            column: 8
          },
          end: {
            line: 639,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 638,
            column: 8
          },
          end: {
            line: 639,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 638
      },
      "109": {
        loc: {
          start: {
            line: 650,
            column: 28
          },
          end: {
            line: 664,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 650,
            column: 28
          },
          end: {
            line: 664,
            column: 29
          }
        }, {
          start: {
            line: 659,
            column: 33
          },
          end: {
            line: 664,
            column: 29
          }
        }],
        line: 650
      },
      "110": {
        loc: {
          start: {
            line: 652,
            column: 32
          },
          end: {
            line: 657,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 652,
            column: 32
          },
          end: {
            line: 657,
            column: 33
          }
        }, {
          start: {
            line: 655,
            column: 37
          },
          end: {
            line: 657,
            column: 33
          }
        }],
        line: 652
      },
      "111": {
        loc: {
          start: {
            line: 659,
            column: 33
          },
          end: {
            line: 664,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 659,
            column: 33
          },
          end: {
            line: 664,
            column: 29
          }
        }, {
          start: {
            line: 662,
            column: 33
          },
          end: {
            line: 664,
            column: 29
          }
        }],
        line: 659
      },
      "112": {
        loc: {
          start: {
            line: 659,
            column: 37
          },
          end: {
            line: 659,
            column: 122
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 659,
            column: 37
          },
          end: {
            line: 659,
            column: 75
          }
        }, {
          start: {
            line: 659,
            column: 79
          },
          end: {
            line: 659,
            column: 122
          }
        }],
        line: 659
      },
      "113": {
        loc: {
          start: {
            line: 674,
            column: 8
          },
          end: {
            line: 679,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 674,
            column: 8
          },
          end: {
            line: 679,
            column: 9
          }
        }, {
          start: {
            line: 677,
            column: 13
          },
          end: {
            line: 679,
            column: 9
          }
        }],
        line: 674
      },
      "114": {
        loc: {
          start: {
            line: 700,
            column: 42
          },
          end: {
            line: 702,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 700,
            column: 42
          },
          end: {
            line: 700,
            column: 64
          }
        }, {
          start: {
            line: 701,
            column: 28
          },
          end: {
            line: 701,
            column: 86
          }
        }, {
          start: {
            line: 702,
            column: 28
          },
          end: {
            line: 702,
            column: 53
          }
        }],
        line: 700
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0,
      "265": 0,
      "266": 0,
      "267": 0,
      "268": 0,
      "269": 0,
      "270": 0,
      "271": 0,
      "272": 0,
      "273": 0,
      "274": 0,
      "275": 0,
      "276": 0,
      "277": 0,
      "278": 0,
      "279": 0,
      "280": 0,
      "281": 0,
      "282": 0,
      "283": 0,
      "284": 0,
      "285": 0,
      "286": 0,
      "287": 0,
      "288": 0,
      "289": 0,
      "290": 0,
      "291": 0,
      "292": 0,
      "293": 0,
      "294": 0,
      "295": 0,
      "296": 0,
      "297": 0,
      "298": 0,
      "299": 0,
      "300": 0,
      "301": 0,
      "302": 0,
      "303": 0,
      "304": 0,
      "305": 0,
      "306": 0,
      "307": 0,
      "308": 0,
      "309": 0,
      "310": 0,
      "311": 0,
      "312": 0,
      "313": 0,
      "314": 0,
      "315": 0,
      "316": 0,
      "317": 0,
      "318": 0,
      "319": 0,
      "320": 0,
      "321": 0,
      "322": 0,
      "323": 0,
      "324": 0,
      "325": 0,
      "326": 0,
      "327": 0,
      "328": 0,
      "329": 0,
      "330": 0,
      "331": 0,
      "332": 0,
      "333": 0,
      "334": 0,
      "335": 0,
      "336": 0,
      "337": 0,
      "338": 0,
      "339": 0,
      "340": 0,
      "341": 0,
      "342": 0,
      "343": 0,
      "344": 0,
      "345": 0,
      "346": 0,
      "347": 0,
      "348": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0, 0, 0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0],
      "81": [0, 0],
      "82": [0, 0],
      "83": [0, 0],
      "84": [0, 0],
      "85": [0, 0],
      "86": [0, 0],
      "87": [0, 0],
      "88": [0, 0],
      "89": [0, 0],
      "90": [0, 0],
      "91": [0, 0],
      "92": [0, 0],
      "93": [0, 0],
      "94": [0, 0],
      "95": [0, 0],
      "96": [0, 0],
      "97": [0, 0],
      "98": [0, 0],
      "99": [0, 0],
      "100": [0, 0],
      "101": [0, 0],
      "102": [0, 0],
      "103": [0, 0],
      "104": [0, 0],
      "105": [0, 0],
      "106": [0, 0, 0, 0, 0, 0],
      "107": [0, 0],
      "108": [0, 0],
      "109": [0, 0],
      "110": [0, 0],
      "111": [0, 0],
      "112": [0, 0],
      "113": [0, 0],
      "114": [0, 0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/concurrent-database-service.ts",
      mappings: ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,uCAAsC;AAEtC,2EAAwE;AA4BxE;IAiBE;QAAA,iBAMC;QArBO,mBAAc,GAAmC,IAAI,GAAG,EAAE,CAAC;QAC3D,wBAAmB,GAAgB,IAAI,GAAG,EAAE,CAAC;QAC7C,wBAAmB,GAAqB,IAAI,GAAG,EAAE,CAAC;QAClD,YAAO,GAAoB;YACjC,eAAe,EAAE,CAAC;YAClB,oBAAoB,EAAE,CAAC;YACvB,oBAAoB,EAAE,CAAC;YACvB,WAAW,EAAE,CAAC;YACd,yBAAyB,EAAE,CAAC;YAC5B,wBAAwB,EAAE,CAAC;SAC5B,CAAC;QAEe,4BAAuB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,GAAG,CAAC,CAAC;QAC7E,mBAAc,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,OAAO,CAAC,CAAC;QAGtF,iCAAiC;QACjC,WAAW,CAAC,cAAM,OAAA,KAAI,CAAC,qBAAqB,EAAE,EAA5B,CAA4B,EAAE,GAAG,CAAC,CAAC;QAErD,gCAAgC;QAChC,WAAW,CAAC,cAAM,OAAA,KAAI,CAAC,aAAa,EAAE,EAApB,CAAoB,EAAE,IAAI,CAAC,CAAC;IAChD,CAAC;IAEa,qCAAW,GAAzB;QACE,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,CAAC;YACxC,yBAAyB,CAAC,QAAQ,GAAG,IAAI,yBAAyB,EAAE,CAAC;QACvE,CAAC;QACD,OAAO,yBAAyB,CAAC,QAAQ,CAAC;IAC5C,CAAC;IAED;;OAEG;IACG,qDAAiB,GAAvB,UAA2B,UAAkC;uCAAG,OAAO;;;;;;wBAC/D,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBACvB,YAAY,GAAG,UAAU,CAAC,GAAG,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,CAAC,EAAE,EAAL,CAAK,CAAC,CAAC;;;;wBAG/C,0BAA0B;wBAC1B,UAAU,CAAC,OAAO,CAAC,UAAA,EAAE;4BACnB,KAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;4BACnC,KAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;wBACjC,CAAC,CAAC,CAAC;wBAGa,qBAAM,OAAO,CAAC,UAAU,CACtC,UAAU,CAAC,GAAG,CAAC,UAAA,EAAE,IAAI,OAAA,KAAI,CAAC,gBAAgB,CAAI,EAAE,CAAC,EAAE,CAAC,EAA/B,CAA+B,CAAC,CACtD,EAAA;;wBAFK,OAAO,GAAG,SAEf;wBAGK,YAAY,GAA6B,OAAO,CAAC,GAAG,CAAC,UAAC,MAAM,EAAE,KAAK;;4BACvE,IAAM,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;4BACxC,IAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;4BAE7C,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gCAClC,OAAO;oCACL,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,MAAM,CAAC,KAAK;oCAClB,WAAW,aAAA;oCACX,aAAa,eAAA;iCACd,CAAC;4BACJ,CAAC;iCAAM,CAAC;gCACN,OAAO;oCACL,OAAO,EAAE,KAAK;oCACd,KAAK,EAAE,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,OAAO,KAAI,kBAAkB;oCACnD,WAAW,aAAA;oCACX,aAAa,eAAA;iCACd,CAAC;4BACJ,CAAC;wBACH,CAAC,CAAC,CAAC;wBAEH,sBAAO,YAAY,EAAC;;;wBAGpB,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,OAAK,CAAC,CAAC;wBAC9D,MAAM,OAAK,CAAC;;;;;KAEf;IAED;;OAEG;IACG,mEAA+B,GAArC,UACE,MAAc,EACd,WAAgB,EAChB,YAAiB,EACjB,cAAmB;uCAClB,OAAO;;;;;;wBACF,UAAU,GAAwB;4BACtC;gCACE,EAAE,EAAE,iBAAiB;gCACrB,SAAS,EAAE,cAAM,OAAA,KAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,WAAW,EAAE,YAAY,CAAC,EAA5D,CAA4D;gCAC7E,QAAQ,EAAE,MAAM;gCAChB,OAAO,EAAE,IAAI,CAAC,cAAc;gCAC5B,OAAO,EAAE,CAAC;6BACX;4BACD;gCACE,EAAE,EAAE,oBAAoB;gCACxB,SAAS,EAAE,cAAM,OAAA,KAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAA7B,CAA6B;gCAC9C,QAAQ,EAAE,QAAQ;gCAClB,OAAO,EAAE,IAAI;gCACb,OAAO,EAAE,CAAC;6BACX;4BACD;gCACE,EAAE,EAAE,mBAAmB;gCACvB,SAAS,EAAE,cAAM,OAAA,KAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,EAApC,CAAoC;gCACrD,QAAQ,EAAE,KAAK;gCACf,OAAO,EAAE,IAAI;gCACb,OAAO,EAAE,CAAC;gCACV,YAAY,EAAE,CAAC,iBAAiB,CAAC;6BAClC;yBACF,CAAC;wBAEc,qBAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAA;;wBAAlD,OAAO,GAAG,SAAwC;wBAGlD,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,KAAK,iBAAiB,EAAnC,CAAmC,CAAC,CAAC;wBAC9E,IAAI,CAAC,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,OAAO,CAAA,EAAE,CAAC;4BAC7B,MAAM,IAAI,KAAK,CAAC,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,KAAK,KAAI,qCAAqC,CAAC,CAAC;wBAClF,CAAC;wBAED,sBAAO,cAAc,CAAC,IAAI,EAAC;;;;KAC5B;IAED;;OAEG;IACG,iEAA6B,GAAnC,UAAoC,MAAc;uCAAG,OAAO;;;;;;;wBACpD,UAAU,GAAwB;4BACtC;gCACE,EAAE,EAAE,mCAAmC;gCACvC,SAAS,EAAE,cAAM,OAAA,qDAAwB,CAAC,gCAAgC,CAAC,MAAM,EAAE,EAAE,CAAC,EAArE,CAAqE;gCACtF,QAAQ,EAAE,MAAM;gCAChB,OAAO,EAAE,IAAI,EAAE,sCAAsC;gCACrD,OAAO,EAAE,CAAC;6BACX;4BACD;gCACE,EAAE,EAAE,oCAAoC;gCACxC,SAAS,EAAE,cAAM,OAAA,qDAAwB,CAAC,4BAA4B,CAAC,MAAM,EAAE,WAAW,CAAC,EAA1E,CAA0E;gCAC3F,QAAQ,EAAE,MAAM;gCAChB,OAAO,EAAE,IAAI,EAAE,sCAAsC;gCACrD,OAAO,EAAE,CAAC;6BACX;4BACD;gCACE,EAAE,EAAE,yBAAyB;gCAC7B,SAAS,EAAE,cAAM,OAAA,KAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAlC,CAAkC;gCACnD,QAAQ,EAAE,QAAQ;gCAClB,OAAO,EAAE,IAAI;gCACb,OAAO,EAAE,CAAC;6BACX;yBACF,CAAC;wBAEc,qBAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAA;;wBAAlD,OAAO,GAAG,SAAwC;wBAGlD,gBAAgB,GAAG,CAAA,MAAA,OAAO,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,KAAK,mCAAmC,EAArD,CAAqD,CAAC,0CAAE,IAAI,KAAI,EAAE,CAAC;wBACxG,iBAAiB,GAAG,CAAA,MAAA,OAAO,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,KAAK,oCAAoC,EAAtD,CAAsD,CAAC,0CAAE,IAAI,KAAI,EAAE,CAAC;wBAC1G,gBAAgB,GAAG,CAAA,MAAA,OAAO,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,KAAK,yBAAyB,EAA3C,CAA2C,CAAC,0CAAE,IAAI,KAAI,EAAE,CAAC;wBAEpG,oEAAW,gBAAgB,SAAK,iBAAiB,SAAK,gBAAgB,SAAE;;;;KACzE;IAED;;OAEG;IACG,gEAA4B,GAAlC,UAAmC,YAAoB;uCAAG,OAAO;;;;;;;wBACzD,UAAU,GAAwB;4BACtC;gCACE,EAAE,EAAE,6BAA6B;gCACjC,SAAS,EAAE,cAAM,OAAA,KAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,EAA3C,CAA2C;gCAC5D,QAAQ,EAAE,MAAM;gCAChB,OAAO,EAAE,IAAI,EAAE,sCAAsC;gCACrD,OAAO,EAAE,CAAC;6BACX;4BACD;gCACE,EAAE,EAAE,sBAAsB;gCAC1B,SAAS,EAAE,cAAM,OAAA,KAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,EAAxC,CAAwC;gCACzD,QAAQ,EAAE,MAAM;gCAChB,OAAO,EAAE,IAAI;gCACb,OAAO,EAAE,CAAC;6BACX;4BACD;gCACE,EAAE,EAAE,sBAAsB;gCAC1B,SAAS,EAAE,cAAM,OAAA,KAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC,EAA/C,CAA+C;gCAChE,QAAQ,EAAE,QAAQ;gCAClB,OAAO,EAAE,IAAI;gCACb,OAAO,EAAE,CAAC;6BACX;4BACD;gCACE,EAAE,EAAE,mBAAmB;gCACvB,SAAS,EAAE,cAAM,OAAA,KAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,EAA5C,CAA4C;gCAC7D,QAAQ,EAAE,KAAK;gCACf,OAAO,EAAE,KAAK;gCACd,OAAO,EAAE,CAAC;6BACX;yBACF,CAAC;wBAEc,qBAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAA;;wBAAlD,OAAO,GAAG,SAAwC;wBAGlD,UAAU,GAAG,MAAA,OAAO,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,KAAK,6BAA6B,EAA/C,CAA+C,CAAC,0CAAE,IAAI,CAAC;wBACtF,MAAM,GAAG,CAAA,MAAA,OAAO,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,KAAK,sBAAsB,EAAxC,CAAwC,CAAC,0CAAE,IAAI,KAAI,EAAE,CAAC;wBACjF,aAAa,GAAG,CAAA,MAAA,OAAO,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,KAAK,sBAAsB,EAAxC,CAAwC,CAAC,0CAAE,IAAI,KAAI,EAAE,CAAC;wBACxF,UAAU,GAAG,MAAA,OAAO,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,KAAK,mBAAmB,EAArC,CAAqC,CAAC,0CAAE,IAAI,CAAC;wBAElF,IAAI,CAAC,UAAU,EAAE,CAAC;4BAChB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;wBAC3C,CAAC;wBAED,4CACK,UAAU,KACb,aAAa,EAAE,MAAM,EACrB,aAAa,eAAA,EACb,UAAU,YAAA,KACV;;;;KACH;IAED;;OAEG;IACW,wDAAoB,GAAlC,UAAmC,MAAc,EAAE,WAAgB,EAAE,YAAiB;uCAAG,OAAO;;;;;;wBACxF,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;wBAC7B,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;wBAEtC,qBAAM,eAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;gCAC1C,IAAI,EAAE;oCACJ,MAAM,QAAA;oCACN,kBAAkB,EAAE,CAAA,MAAA,WAAW,CAAC,gBAAgB,0CAAE,YAAY,KAAI,IAAI;oCACtE,oBAAoB,EAAE,MAAA,WAAW,CAAC,gBAAgB,0CAAE,cAAc;oCAClE,eAAe,EAAE,MAAA,WAAW,CAAC,gBAAgB,0CAAE,WAAW;oCAC1D,SAAS,EAAE,MAAA,WAAW,CAAC,WAAW,0CAAE,SAAS;oCAC7C,YAAY,EAAE,YAAY;oCAC1B,SAAS,EAAE,YAAY,CAAC,SAAS,IAAI,EAAE;oCACvC,YAAY,EAAE,YAAY,CAAC,YAAY,IAAI,EAAE;oCAC7C,UAAU,EAAE,YAAY,CAAC,cAAc,IAAI,IAAI;oCAC/C,gBAAgB,EAAE;wCAChB,UAAU,EAAE,CAAA,MAAA,YAAY,CAAC,YAAY,0CAAE,UAAU,KAAI,EAAE;wCACvD,mBAAmB,EAAE,EAAE;wCACvB,YAAY,EAAE,UAAU;qCACzB;oCACD,MAAM,EAAE,QAAQ;oCAChB,oBAAoB,EAAE,CAAC;oCACvB,SAAS,WAAA;iCACV;6BACF,CAAC,EAAA;4BApBF,sBAAO,SAoBL,EAAC;;;;KACJ;IAEa,oDAAgB,GAA9B,UAA+B,MAAc;uCAAG,OAAO;;;4BAC9C,qBAAM,eAAM,CAAC,IAAI,CAAC,UAAU,CAAC;4BAClC,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;4BACrB,MAAM,EAAE;gCACN,EAAE,EAAE,IAAI;gCACR,KAAK,EAAE,IAAI;gCACX,OAAO,EAAE;oCACP,MAAM,EAAE;wCACN,SAAS,EAAE,IAAI;wCACf,QAAQ,EAAE,IAAI;wCACd,eAAe,EAAE,IAAI;wCACrB,QAAQ,EAAE,IAAI;wCACd,OAAO,EAAE,IAAI;wCACb,eAAe,EAAE,IAAI;qCACtB;iCACF;6BACF;yBACF,CAAC,EAAA;4BAhBF,sBAAO,SAgBL,EAAC;;;;KACJ;IAEa,2DAAuB,GAArC,UAAsC,MAAc;uCAAG,OAAO;;;4BACrD,qBAAM,eAAM,CAAC,IAAI,CAAC,MAAM,CAAC;4BAC9B,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;4BACrB,IAAI,EAAE;gCACJ,OAAO,EAAE;oCACP,MAAM,EAAE;wCACN,iBAAiB,EAAE,IAAI,IAAI,EAAE;wCAC7B,YAAY,EAAE,IAAI,IAAI,EAAE;qCACzB;iCACF;6BACF;yBACF,CAAC,EAAA;4BAVF,sBAAO,SAUL,EAAC;;;;KACJ;IAEa,yDAAqB,GAAnC,UAAoC,MAAc;uCAAG,OAAO;;;4BACnD,qBAAM,eAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;4BAC3C,KAAK,EAAE,EAAE,MAAM,QAAA,EAAE;4BACjB,OAAO,EAAE;gCACP,KAAK,EAAE;oCACL,MAAM,EAAE;wCACN,EAAE,EAAE,IAAI;wCACR,IAAI,EAAE,IAAI;wCACV,QAAQ,EAAE,IAAI;qCACf;iCACF;6BACF;4BACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;4BAC9B,IAAI,EAAE,EAAE;yBACT,CAAC,EAAA;4BAbF,sBAAO,SAaL,EAAC;;;;KACJ;IAEa,0DAAsB,GAApC,UAAqC,MAAc;uCAAG,OAAO;;;4BACpD,qBAAM,eAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;4BACtC,KAAK,EAAE,EAAE,MAAM,QAAA,EAAE;4BACjB,OAAO,EAAE;gCACP,SAAS,EAAE;oCACT,MAAM,EAAE;wCACN,WAAW,EAAE,IAAI;wCACjB,WAAW,EAAE,IAAI;qCAClB;iCACF;6BACF;4BACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;4BAC9B,IAAI,EAAE,EAAE;yBACT,CAAC,EAAA;4BAZF,sBAAO,SAYL,EAAC;;;;KACJ;IAEa,yDAAqB,GAAnC,UAAoC,MAAc;uCAAG,OAAO;;;4BACnD,qBAAM,eAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC;4BAChD,KAAK,EAAE,EAAE,MAAM,QAAA,EAAE;4BACjB,OAAO,EAAE;gCACP,QAAQ,EAAE;oCACR,MAAM,EAAE;wCACN,EAAE,EAAE,IAAI;wCACR,KAAK,EAAE,IAAI;wCACX,UAAU,EAAE,IAAI;wCAChB,QAAQ,EAAE,IAAI;wCACd,IAAI,EAAE,IAAI;qCACX;iCACF;6BACF;4BACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;4BAC9B,IAAI,EAAE,EAAE;yBACT,CAAC,EAAA;4BAfF,sBAAO,SAeL,EAAC;;;;KACJ;IAEa,mDAAe,GAA7B,UAA8B,YAAoB;uCAAG,OAAO;;;4BACnD,qBAAM,eAAM,CAAC,UAAU,CAAC,UAAU,CAAC;4BACxC,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE;4BAC3B,OAAO,EAAE;gCACP,MAAM,EAAE;oCACN,MAAM,EAAE;wCACN,aAAa,EAAE,IAAI;wCACnB,aAAa,EAAE,IAAI;qCACpB;iCACF;6BACF;yBACF,CAAC,EAAA;4BAVF,sBAAO,SAUL,EAAC;;;;KACJ;IAED;;OAEG;IACW,4DAAwB,GAAtC,UAAuC,YAAoB;uCAAG,OAAO;;;4BAC5D,qBAAM,eAAM,CAAC,UAAU,CAAC,UAAU,CAAC;4BACxC,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE;4BAC3B,MAAM,EAAE;gCACN,EAAE,EAAE,IAAI;gCACR,IAAI,EAAE,IAAI;gCACV,IAAI,EAAE,IAAI;gCACV,QAAQ,EAAE,IAAI;gCACd,IAAI,EAAE,IAAI;gCACV,IAAI,EAAE,IAAI;gCACV,eAAe,EAAE,IAAI;gCACrB,QAAQ,EAAE,IAAI;gCACd,MAAM,EAAE;oCACN,MAAM,EAAE;wCACN,aAAa,EAAE,IAAI;wCACnB,aAAa,EAAE,IAAI;qCACpB;iCACF;6BACF;yBACF,CAAC,EAAA;4BAlBF,sBAAO,SAkBL,EAAC;;;;KACJ;IAEa,yDAAqB,GAAnC,UAAoC,YAAoB;uCAAG,OAAO;;;4BACzD,qBAAM,eAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;4BACjC,KAAK,EAAE;gCACL,WAAW,EAAE;oCACX,IAAI,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE;iCAC3B;6BACF;4BACD,MAAM,EAAE;gCACN,EAAE,EAAE,IAAI;gCACR,IAAI,EAAE,IAAI;gCACV,QAAQ,EAAE,IAAI;gCACd,WAAW,EAAE,IAAI;6BAClB;4BACD,IAAI,EAAE,EAAE;yBACT,CAAC,EAAA;4BAbF,sBAAO,SAaL,EAAC;;;;KACJ;IAEa,gEAA4B,GAA1C,UAA2C,YAAoB;uCAAG,OAAO;;;4BAChE,qBAAM,eAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;4BACxC,KAAK,EAAE;gCACL,WAAW,EAAE;oCACX,IAAI,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE;iCAC3B;6BACF;4BACD,OAAO,EAAE;gCACP,MAAM,EAAE;oCACN,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;iCACxB;gCACD,MAAM,EAAE;oCACN,MAAM,EAAE;wCACN,IAAI,EAAE,IAAI;qCACX;oCACD,IAAI,EAAE,CAAC;iCACR;6BACF;4BACD,IAAI,EAAE,EAAE;yBACT,CAAC,EAAA;4BAlBF,sBAAO,SAkBL,EAAC;;;;KACJ;IAEa,6DAAyB,GAAvC,UAAwC,YAAoB;uCAAG,OAAO;;gBACpE,iEAAiE;gBACjE,yEAAyE;gBACzE,sBAAO,IAAI,EAAC;;;KACb;IAED;;OAEG;IACW,yDAAqB,GAAnC;uCAAuC,OAAO;;;;gBAC5C,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;oBAClE,sBAAO;gBACT,CAAC;gBAGK,mBAAmB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;qBACjE,MAAM,CAAC,UAAA,EAAE,IAAI,OAAA,CAAC,KAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAApC,CAAoC,CAAC;qBAClD,MAAM,CAAC,UAAA,EAAE,IAAI,OAAA,KAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,EAA3B,CAA2B,CAAC;qBACzC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;oBACT,IAAM,aAAa,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;oBACrD,OAAO,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;gBAC/D,CAAC,CAAC,CAAC;gBAEL,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACrC,sBAAO;gBACT,CAAC;gBAEK,SAAS,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;gBACzC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;;;;KAClC;IAEa,oDAAgB,GAA9B,UAA+B,SAA4B;uCAAG,OAAO;;;;;wBACnE,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;wBAC3C,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;wBAE5D,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;;;;wBAGZ,qBAAM,OAAO,CAAC,IAAI,CAAC;gCAChC,SAAS,CAAC,SAAS,EAAE;gCACrB,IAAI,OAAO,CAAC,UAAC,CAAC,EAAE,MAAM;oCACpB,OAAA,UAAU,CAAC,cAAM,OAAA,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,EAAtC,CAAsC,EAAE,SAAS,CAAC,OAAO,CAAC;gCAA3E,CAA2E,CAC5E;6BACF,CAAC,EAAA;;wBALI,MAAM,GAAG,SAKb;wBAEF,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;wBACnD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;wBAEnC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBAC7C,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;;;;wBAGjD,OAAO,CAAC,KAAK,CAAC,6BAAsB,SAAS,CAAC,EAAE,aAAU,EAAE,OAAK,CAAC,CAAC;wBAEnE,IAAI,SAAS,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;4BAC1B,SAAS,CAAC,OAAO,EAAE,CAAC;4BACpB,+BAA+B;4BAC/B,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC;wBAC7B,CAAC;6BAAM,CAAC;4BACN,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,KAAK,EAAG,OAAe,CAAC,OAAO,EAAE,CAAC,CAAC;4BAChF,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;wBAC3C,CAAC;wBAEK,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBAC7C,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;;;wBAGlD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;wBAC9C,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;;;;;;KAErE;IAEO,sDAAkB,GAA1B,UAA2B,SAA4B;QAAvD,iBAMC;QALC,IAAI,CAAC,SAAS,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC;QAEzC,OAAO,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,UAAA,KAAK;YACvC,OAAA,KAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC;QAAnC,CAAmC,CACpC,CAAC;IACJ,CAAC;IAEa,oDAAgB,GAA9B,UAAkC,WAAmB;uCAAG,OAAO;;;gBAC7D,sBAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;wBACjC,IAAM,eAAe,GAAG;4BACtB,IAAI,KAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;gCAC9C,IAAM,MAAM,GAAG,KAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gCACzD,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;oCACjB,MAAM,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gCAClC,CAAC;qCAAM,CAAC;oCACN,OAAO,CAAC,MAAM,CAAC,CAAC;gCAClB,CAAC;4BACH,CAAC;iCAAM,IAAI,CAAC,KAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,KAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;gCAC/F,MAAM,CAAC,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC;4BAC3C,CAAC;iCAAM,CAAC;gCACN,UAAU,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;4BAClC,CAAC;wBACH,CAAC,CAAC;wBACF,eAAe,EAAE,CAAC;oBACpB,CAAC,CAAC,EAAC;;;KACJ;IAEO,0DAAsB,GAA9B,UAA+B,aAAqB,EAAE,OAAgB;QACpE,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;QACzF,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,CAAC,SAAS,GAAG,aAAa,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;QAE/F,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;QAClI,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;QAC5H,CAAC;IACH,CAAC;IAEO,iDAAa,GAArB;QACE,kDAAkD;QAClD,IAAI,CAAC,OAAO,CAAC,yBAAyB,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,GAAG,CAAC;IAChH,CAAC;IAED;;OAEG;IACH,8CAAU,GAAV;QAKE,6BACK,IAAI,CAAC,OAAO,KACf,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EACnC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAClD,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,IAClD;IACJ,CAAC;IAED;;OAEG;IACG,+CAAW,GAAjB;uCAAqB,OAAO;;;gBAC1B,IAAI,CAAC;oBACG,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;oBAClC,sBAAO,OAAO,CAAC,SAAS,GAAG,EAAE;4BACtB,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC,uBAAuB;4BAC1D,OAAO,CAAC,WAAW,GAAG,GAAG,EAAC;gBACnC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;oBACzE,sBAAO,KAAK,EAAC;gBACf,CAAC;;;;KACF;IACH,gCAAC;AAAD,CAAC,AA/iBD,IA+iBC;AA/iBY,8DAAyB;AAijBtC,4BAA4B;AACf,QAAA,yBAAyB,GAAG,yBAAyB,CAAC,WAAW,EAAE,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/concurrent-database-service.ts"],
      sourcesContent: ["/**\n * Concurrent Database Operations Service\n * Optimizes database operations through concurrent processing, connection pooling,\n * and intelligent query batching for the Skills Analysis API\n */\n\nimport { prisma } from '@/lib/prisma';\nimport { Prisma } from '@prisma/client';\nimport { optimizedDatabaseService } from './optimized-database-service';\n\ninterface DatabaseOperation<T = any> {\n  id: string;\n  operation: () => Promise<T>;\n  priority: 'high' | 'medium' | 'low';\n  timeout: number;\n  retries: number;\n  dependencies?: string[];\n}\n\ninterface BatchDatabaseResult<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  operationId: string;\n  executionTime: number;\n}\n\ninterface DatabaseMetrics {\n  totalOperations: number;\n  concurrentOperations: number;\n  averageExecutionTime: number;\n  successRate: number;\n  connectionPoolUtilization: number;\n  queryOptimizationSavings: number;\n}\n\nexport class ConcurrentDatabaseService {\n  private static instance: ConcurrentDatabaseService;\n  private operationQueue: Map<string, DatabaseOperation> = new Map();\n  private executingOperations: Set<string> = new Set();\n  private completedOperations: Map<string, any> = new Map();\n  private metrics: DatabaseMetrics = {\n    totalOperations: 0,\n    concurrentOperations: 0,\n    averageExecutionTime: 0,\n    successRate: 0,\n    connectionPoolUtilization: 0,\n    queryOptimizationSavings: 0,\n  };\n\n  private readonly maxConcurrentOperations = parseInt(process.env.MAX_CONCURRENT_DB_OPS || '5');\n  private readonly defaultTimeout = parseInt(process.env.DB_OPERATION_TIMEOUT || '30000');\n\n  private constructor() {\n    // Initialize operation processor\n    setInterval(() => this.processOperationQueue(), 100);\n    \n    // Initialize metrics collection\n    setInterval(() => this.updateMetrics(), 5000);\n  }\n\n  public static getInstance(): ConcurrentDatabaseService {\n    if (!ConcurrentDatabaseService.instance) {\n      ConcurrentDatabaseService.instance = new ConcurrentDatabaseService();\n    }\n    return ConcurrentDatabaseService.instance;\n  }\n\n  /**\n   * Execute multiple database operations concurrently\n   */\n  async executeConcurrent<T>(operations: DatabaseOperation<T>[]): Promise<BatchDatabaseResult<T>[]> {\n    const startTime = Date.now();\n    const operationIds = operations.map(op => op.id);\n\n    try {\n      // Add operations to queue\n      operations.forEach(op => {\n        this.operationQueue.set(op.id, op);\n        this.metrics.totalOperations++;\n      });\n\n      // Wait for all operations to complete\n      const results = await Promise.allSettled(\n        operations.map(op => this.waitForOperation<T>(op.id))\n      );\n\n      // Process results\n      const batchResults: BatchDatabaseResult<T>[] = results.map((result, index) => {\n        const operationId = operationIds[index];\n        const executionTime = Date.now() - startTime;\n\n        if (result.status === 'fulfilled') {\n          return {\n            success: true,\n            data: result.value,\n            operationId,\n            executionTime,\n          };\n        } else {\n          return {\n            success: false,\n            error: result.reason?.message || 'Operation failed',\n            operationId,\n            executionTime,\n          };\n        }\n      });\n\n      return batchResults;\n\n    } catch (error) {\n      console.error('Concurrent database execution failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Optimized skill gap analysis creation with concurrent operations\n   */\n  async createSkillGapAnalysisOptimized(\n    userId: string,\n    requestData: any,\n    analysisData: any,\n    careerPathData: any\n  ): Promise<any> {\n    const operations: DatabaseOperation[] = [\n      {\n        id: 'create_analysis',\n        operation: () => this.createAnalysisRecord(userId, requestData, analysisData),\n        priority: 'high',\n        timeout: this.defaultTimeout,\n        retries: 2,\n      },\n      {\n        id: 'fetch_user_profile',\n        operation: () => this.fetchUserProfile(userId),\n        priority: 'medium',\n        timeout: 5000,\n        retries: 1,\n      },\n      {\n        id: 'update_user_stats',\n        operation: () => this.updateUserAnalysisStats(userId),\n        priority: 'low',\n        timeout: 5000,\n        retries: 1,\n        dependencies: ['create_analysis'],\n      },\n    ];\n\n    const results = await this.executeConcurrent(operations);\n    \n    // Extract the main analysis result\n    const analysisResult = results.find(r => r.operationId === 'create_analysis');\n    if (!analysisResult?.success) {\n      throw new Error(analysisResult?.error || 'Failed to create skill gap analysis');\n    }\n\n    return analysisResult.data;\n  }\n\n  /**\n   * Batch fetch user assessments with optimization\n   */\n  async fetchUserAssessmentsOptimized(userId: string): Promise<any[]> {\n    const operations: DatabaseOperation[] = [\n      {\n        id: 'fetch_skill_assessments_optimized',\n        operation: () => optimizedDatabaseService.getUserSkillAssessmentsOptimized(userId, 50),\n        priority: 'high',\n        timeout: 5000, // Reduced timeout due to optimization\n        retries: 2,\n      },\n      {\n        id: 'fetch_career_assessments_optimized',\n        operation: () => optimizedDatabaseService.getCareerAssessmentOptimized(userId, 'COMPLETED'),\n        priority: 'high',\n        timeout: 5000, // Reduced timeout due to optimization\n        retries: 2,\n      },\n      {\n        id: 'fetch_learning_progress',\n        operation: () => this.fetchLearningProgress(userId),\n        priority: 'medium',\n        timeout: 5000,\n        retries: 1,\n      },\n    ];\n\n    const results = await this.executeConcurrent(operations);\n    \n    // Combine results\n    const skillAssessments = results.find(r => r.operationId === 'fetch_skill_assessments_optimized')?.data || [];\n    const careerAssessments = results.find(r => r.operationId === 'fetch_career_assessments_optimized')?.data || [];\n    const learningProgress = results.find(r => r.operationId === 'fetch_learning_progress')?.data || [];\n\n    return [...skillAssessments, ...careerAssessments, ...learningProgress];\n  }\n\n  /**\n   * Optimized career path data fetching\n   */\n  async fetchCareerPathDataOptimized(careerPathId: string): Promise<any> {\n    const operations: DatabaseOperation[] = [\n      {\n        id: 'fetch_career_path_optimized',\n        operation: () => this.fetchCareerPathOptimized(careerPathId),\n        priority: 'high',\n        timeout: 3000, // Reduced timeout due to optimization\n        retries: 2,\n      },\n      {\n        id: 'fetch_related_skills',\n        operation: () => this.fetchCareerPathSkills(careerPathId),\n        priority: 'high',\n        timeout: 5000,\n        retries: 2,\n      },\n      {\n        id: 'fetch_learning_paths',\n        operation: () => this.fetchCareerPathLearningPaths(careerPathId),\n        priority: 'medium',\n        timeout: 5000,\n        retries: 1,\n      },\n      {\n        id: 'fetch_market_data',\n        operation: () => this.fetchCareerPathMarketData(careerPathId),\n        priority: 'low',\n        timeout: 10000,\n        retries: 1,\n      },\n    ];\n\n    const results = await this.executeConcurrent(operations);\n    \n    // Combine results into enhanced career path data\n    const careerPath = results.find(r => r.operationId === 'fetch_career_path_optimized')?.data;\n    const skills = results.find(r => r.operationId === 'fetch_related_skills')?.data || [];\n    const learningPaths = results.find(r => r.operationId === 'fetch_learning_paths')?.data || [];\n    const marketData = results.find(r => r.operationId === 'fetch_market_data')?.data;\n\n    if (!careerPath) {\n      throw new Error('Career path not found');\n    }\n\n    return {\n      ...careerPath,\n      relatedSkills: skills,\n      learningPaths,\n      marketData,\n    };\n  }\n\n  /**\n   * Individual database operations\n   */\n  private async createAnalysisRecord(userId: string, requestData: any, analysisData: any): Promise<any> {\n    const expiresAt = new Date();\n    expiresAt.setMonth(expiresAt.getMonth() + 3);\n\n    return await prisma.skillGapAnalysis.create({\n      data: {\n        userId,\n        targetCareerPathId: requestData.targetCareerPath?.careerPathId || null,\n        targetCareerPathName: requestData.targetCareerPath?.careerPathName,\n        experienceLevel: requestData.targetCareerPath?.targetLevel,\n        timeframe: requestData.preferences?.timeframe,\n        analysisData: analysisData,\n        skillGaps: analysisData.skillGaps || [],\n        learningPlan: analysisData.learningPlan || {},\n        marketData: analysisData.marketInsights || null,\n        progressTracking: {\n          milestones: analysisData.learningPlan?.milestones || [],\n          completedMilestones: [],\n          currentPhase: 'planning',\n        },\n        status: 'ACTIVE',\n        completionPercentage: 0,\n        expiresAt,\n      },\n    });\n  }\n\n  private async fetchUserProfile(userId: string): Promise<any> {\n    return await prisma.user.findUnique({\n      where: { id: userId },\n      select: {\n        id: true,\n        email: true,\n        profile: {\n          select: {\n            firstName: true,\n            lastName: true,\n            experienceLevel: true,\n            jobTitle: true,\n            company: true,\n            currentIndustry: true,\n          },\n        },\n      },\n    });\n  }\n\n  private async updateUserAnalysisStats(userId: string): Promise<any> {\n    return await prisma.user.update({\n      where: { id: userId },\n      data: {\n        profile: {\n          update: {\n            lastProfileUpdate: new Date(),\n            lastActiveAt: new Date(),\n          },\n        },\n      },\n    });\n  }\n\n  private async fetchSkillAssessments(userId: string): Promise<any[]> {\n    return await prisma.skillAssessment.findMany({\n      where: { userId },\n      include: {\n        skill: {\n          select: {\n            id: true,\n            name: true,\n            category: true,\n          },\n        },\n      },\n      orderBy: { createdAt: 'desc' },\n      take: 50,\n    });\n  }\n\n  private async fetchCareerAssessments(userId: string): Promise<any[]> {\n    return await prisma.assessment.findMany({\n      where: { userId },\n      include: {\n        responses: {\n          select: {\n            questionKey: true,\n            answerValue: true,\n          },\n        },\n      },\n      orderBy: { createdAt: 'desc' },\n      take: 10,\n    });\n  }\n\n  private async fetchLearningProgress(userId: string): Promise<any[]> {\n    return await prisma.userLearningProgress.findMany({\n      where: { userId },\n      include: {\n        resource: {\n          select: {\n            id: true,\n            title: true,\n            skillLevel: true,\n            category: true,\n            type: true,\n          },\n        },\n      },\n      orderBy: { updatedAt: 'desc' },\n      take: 20,\n    });\n  }\n\n  private async fetchCareerPath(careerPathId: string): Promise<any> {\n    return await prisma.careerPath.findUnique({\n      where: { id: careerPathId },\n      include: {\n        _count: {\n          select: {\n            relatedSkills: true,\n            learningPaths: true,\n          },\n        },\n      },\n    });\n  }\n\n  /**\n   * Optimized career path fetching with selective loading\n   */\n  private async fetchCareerPathOptimized(careerPathId: string): Promise<any> {\n    return await prisma.careerPath.findUnique({\n      where: { id: careerPathId },\n      select: {\n        id: true,\n        name: true,\n        slug: true,\n        overview: true,\n        pros: true,\n        cons: true,\n        actionableSteps: true,\n        isActive: true,\n        _count: {\n          select: {\n            relatedSkills: true,\n            learningPaths: true,\n          },\n        },\n      },\n    });\n  }\n\n  private async fetchCareerPathSkills(careerPathId: string): Promise<any[]> {\n    return await prisma.skill.findMany({\n      where: {\n        careerPaths: {\n          some: { id: careerPathId },\n        },\n      },\n      select: {\n        id: true,\n        name: true,\n        category: true,\n        description: true,\n      },\n      take: 20,\n    });\n  }\n\n  private async fetchCareerPathLearningPaths(careerPathId: string): Promise<any[]> {\n    return await prisma.learningPath.findMany({\n      where: {\n        careerPaths: {\n          some: { id: careerPathId },\n        },\n      },\n      include: {\n        _count: {\n          select: { steps: true },\n        },\n        skills: {\n          select: {\n            name: true,\n          },\n          take: 5,\n        },\n      },\n      take: 10,\n    });\n  }\n\n  private async fetchCareerPathMarketData(careerPathId: string): Promise<any> {\n    // This would fetch market data from external APIs or cached data\n    // For now, return null as this is typically handled by external services\n    return null;\n  }\n\n  /**\n   * Operation queue processing\n   */\n  private async processOperationQueue(): Promise<void> {\n    if (this.executingOperations.size >= this.maxConcurrentOperations) {\n      return;\n    }\n\n    // Find next operation to execute\n    const availableOperations = Array.from(this.operationQueue.values())\n      .filter(op => !this.executingOperations.has(op.id))\n      .filter(op => this.areDependenciesMet(op))\n      .sort((a, b) => {\n        const priorityOrder = { high: 3, medium: 2, low: 1 };\n        return priorityOrder[b.priority] - priorityOrder[a.priority];\n      });\n\n    if (availableOperations.length === 0) {\n      return;\n    }\n\n    const operation = availableOperations[0];\n    this.executeOperation(operation);\n  }\n\n  private async executeOperation(operation: DatabaseOperation): Promise<void> {\n    this.executingOperations.add(operation.id);\n    this.metrics.concurrentOperations = this.executingOperations.size;\n\n    const startTime = Date.now();\n\n    try {\n      const result = await Promise.race([\n        operation.operation(),\n        new Promise((_, reject) => \n          setTimeout(() => reject(new Error('Operation timeout')), operation.timeout)\n        ),\n      ]);\n\n      this.completedOperations.set(operation.id, result);\n      this.operationQueue.delete(operation.id);\n\n      const executionTime = Date.now() - startTime;\n      this.updateExecutionMetrics(executionTime, true);\n\n    } catch (error) {\n      console.error(`Database operation ${operation.id} failed:`, error);\n      \n      if (operation.retries > 0) {\n        operation.retries--;\n        // Re-queue with lower priority\n        operation.priority = 'low';\n      } else {\n        this.completedOperations.set(operation.id, { error: (error as Error).message });\n        this.operationQueue.delete(operation.id);\n      }\n\n      const executionTime = Date.now() - startTime;\n      this.updateExecutionMetrics(executionTime, false);\n\n    } finally {\n      this.executingOperations.delete(operation.id);\n      this.metrics.concurrentOperations = this.executingOperations.size;\n    }\n  }\n\n  private areDependenciesMet(operation: DatabaseOperation): boolean {\n    if (!operation.dependencies) return true;\n    \n    return operation.dependencies.every(depId => \n      this.completedOperations.has(depId)\n    );\n  }\n\n  private async waitForOperation<T>(operationId: string): Promise<T> {\n    return new Promise((resolve, reject) => {\n      const checkCompletion = () => {\n        if (this.completedOperations.has(operationId)) {\n          const result = this.completedOperations.get(operationId);\n          if (result.error) {\n            reject(new Error(result.error));\n          } else {\n            resolve(result);\n          }\n        } else if (!this.operationQueue.has(operationId) && !this.executingOperations.has(operationId)) {\n          reject(new Error('Operation not found'));\n        } else {\n          setTimeout(checkCompletion, 50);\n        }\n      };\n      checkCompletion();\n    });\n  }\n\n  private updateExecutionMetrics(executionTime: number, success: boolean): void {\n    const totalTime = this.metrics.averageExecutionTime * (this.metrics.totalOperations - 1);\n    this.metrics.averageExecutionTime = (totalTime + executionTime) / this.metrics.totalOperations;\n    \n    if (success) {\n      this.metrics.successRate = ((this.metrics.successRate * (this.metrics.totalOperations - 1)) + 1) / this.metrics.totalOperations;\n    } else {\n      this.metrics.successRate = (this.metrics.successRate * (this.metrics.totalOperations - 1)) / this.metrics.totalOperations;\n    }\n  }\n\n  private updateMetrics(): void {\n    // Update connection pool utilization (simplified)\n    this.metrics.connectionPoolUtilization = (this.executingOperations.size / this.maxConcurrentOperations) * 100;\n  }\n\n  /**\n   * Get performance metrics\n   */\n  getMetrics(): DatabaseMetrics & {\n    queueSize: number;\n    executingOperations: number;\n    completedOperations: number;\n  } {\n    return {\n      ...this.metrics,\n      queueSize: this.operationQueue.size,\n      executingOperations: this.executingOperations.size,\n      completedOperations: this.completedOperations.size,\n    };\n  }\n\n  /**\n   * Health check\n   */\n  async healthCheck(): Promise<boolean> {\n    try {\n      const metrics = this.getMetrics();\n      return metrics.queueSize < 50 && \n             metrics.executingOperations < this.maxConcurrentOperations &&\n             metrics.successRate > 0.8;\n    } catch (error) {\n      console.error('Concurrent database service health check failed:', error);\n      return false;\n    }\n  }\n}\n\n// Export singleton instance\nexport const concurrentDatabaseService = ConcurrentDatabaseService.getInstance();\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "724a9195cf8d5adf3e695fa18ffd38577f9a7fc3"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1zda1zdg4k = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1zda1zdg4k();
var __assign =
/* istanbul ignore next */
(cov_1zda1zdg4k().s[0]++,
/* istanbul ignore next */
(cov_1zda1zdg4k().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1zda1zdg4k().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_1zda1zdg4k().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_1zda1zdg4k().f[0]++;
  cov_1zda1zdg4k().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_1zda1zdg4k().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_1zda1zdg4k().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[1]++;
    cov_1zda1zdg4k().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_1zda1zdg4k().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_1zda1zdg4k().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_1zda1zdg4k().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_1zda1zdg4k().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_1zda1zdg4k().b[2][0]++;
          cov_1zda1zdg4k().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_1zda1zdg4k().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_1zda1zdg4k().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_1zda1zdg4k().s[11]++,
/* istanbul ignore next */
(cov_1zda1zdg4k().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_1zda1zdg4k().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1zda1zdg4k().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1zda1zdg4k().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[3]++;
    cov_1zda1zdg4k().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1zda1zdg4k().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_1zda1zdg4k().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[4]++;
      cov_1zda1zdg4k().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_1zda1zdg4k().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_1zda1zdg4k().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[6]++;
      cov_1zda1zdg4k().s[15]++;
      try {
        /* istanbul ignore next */
        cov_1zda1zdg4k().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[7]++;
      cov_1zda1zdg4k().s[18]++;
      try {
        /* istanbul ignore next */
        cov_1zda1zdg4k().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[8]++;
      cov_1zda1zdg4k().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1zda1zdg4k().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1zda1zdg4k().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1zda1zdg4k().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1zda1zdg4k().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1zda1zdg4k().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1zda1zdg4k().s[23]++,
/* istanbul ignore next */
(cov_1zda1zdg4k().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_1zda1zdg4k().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1zda1zdg4k().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1zda1zdg4k().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_1zda1zdg4k().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[10]++;
        cov_1zda1zdg4k().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1zda1zdg4k().b[9][0]++;
          cov_1zda1zdg4k().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1zda1zdg4k().b[9][1]++;
        }
        cov_1zda1zdg4k().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1zda1zdg4k().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1zda1zdg4k().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1zda1zdg4k().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1zda1zdg4k().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1zda1zdg4k().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[11]++;
    cov_1zda1zdg4k().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[12]++;
    cov_1zda1zdg4k().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[13]++;
      cov_1zda1zdg4k().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[14]++;
    cov_1zda1zdg4k().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_1zda1zdg4k().b[12][0]++;
      cov_1zda1zdg4k().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1zda1zdg4k().b[12][1]++;
    }
    cov_1zda1zdg4k().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_1zda1zdg4k().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_1zda1zdg4k().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1zda1zdg4k().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1zda1zdg4k().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1zda1zdg4k().s[36]++;
      try {
        /* istanbul ignore next */
        cov_1zda1zdg4k().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1zda1zdg4k().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_1zda1zdg4k().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1zda1zdg4k().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1zda1zdg4k().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1zda1zdg4k().b[18][0]++,
        /* istanbul ignore next */
        (cov_1zda1zdg4k().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1zda1zdg4k().b[19][1]++,
        /* istanbul ignore next */
        (cov_1zda1zdg4k().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1zda1zdg4k().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1zda1zdg4k().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1zda1zdg4k().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1zda1zdg4k().b[15][0]++;
          cov_1zda1zdg4k().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1zda1zdg4k().b[15][1]++;
        }
        cov_1zda1zdg4k().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1zda1zdg4k().b[21][0]++;
          cov_1zda1zdg4k().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1zda1zdg4k().b[21][1]++;
        }
        cov_1zda1zdg4k().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[22][1]++;
            cov_1zda1zdg4k().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[22][2]++;
            cov_1zda1zdg4k().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[22][3]++;
            cov_1zda1zdg4k().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[22][4]++;
            cov_1zda1zdg4k().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[22][5]++;
            cov_1zda1zdg4k().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().b[23][0]++;
              cov_1zda1zdg4k().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1zda1zdg4k().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1zda1zdg4k().b[23][1]++;
            }
            cov_1zda1zdg4k().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().b[26][0]++;
              cov_1zda1zdg4k().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1zda1zdg4k().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1zda1zdg4k().b[26][1]++;
            }
            cov_1zda1zdg4k().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().b[28][0]++;
              cov_1zda1zdg4k().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1zda1zdg4k().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_1zda1zdg4k().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1zda1zdg4k().b[28][1]++;
            }
            cov_1zda1zdg4k().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().b[30][0]++;
              cov_1zda1zdg4k().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1zda1zdg4k().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1zda1zdg4k().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1zda1zdg4k().b[30][1]++;
            }
            cov_1zda1zdg4k().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().b[32][0]++;
              cov_1zda1zdg4k().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1zda1zdg4k().b[32][1]++;
            }
            cov_1zda1zdg4k().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1zda1zdg4k().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1zda1zdg4k().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1zda1zdg4k().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1zda1zdg4k().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1zda1zdg4k().b[33][0]++;
      cov_1zda1zdg4k().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1zda1zdg4k().b[33][1]++;
    }
    cov_1zda1zdg4k().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1zda1zdg4k().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1zda1zdg4k().b[34][1]++, void 0),
      done: true
    };
  }
}));
var __spreadArray =
/* istanbul ignore next */
(cov_1zda1zdg4k().s[78]++,
/* istanbul ignore next */
(cov_1zda1zdg4k().b[35][0]++, this) &&
/* istanbul ignore next */
(cov_1zda1zdg4k().b[35][1]++, this.__spreadArray) ||
/* istanbul ignore next */
(cov_1zda1zdg4k().b[35][2]++, function (to, from, pack) {
  /* istanbul ignore next */
  cov_1zda1zdg4k().f[15]++;
  cov_1zda1zdg4k().s[79]++;
  if (
  /* istanbul ignore next */
  (cov_1zda1zdg4k().b[37][0]++, pack) ||
  /* istanbul ignore next */
  (cov_1zda1zdg4k().b[37][1]++, arguments.length === 2)) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().b[36][0]++;
    cov_1zda1zdg4k().s[80]++;
    for (var i =
      /* istanbul ignore next */
      (cov_1zda1zdg4k().s[81]++, 0), l =
      /* istanbul ignore next */
      (cov_1zda1zdg4k().s[82]++, from.length), ar; i < l; i++) {
      /* istanbul ignore next */
      cov_1zda1zdg4k().s[83]++;
      if (
      /* istanbul ignore next */
      (cov_1zda1zdg4k().b[39][0]++, ar) ||
      /* istanbul ignore next */
      (cov_1zda1zdg4k().b[39][1]++, !(i in from))) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().b[38][0]++;
        cov_1zda1zdg4k().s[84]++;
        if (!ar) {
          /* istanbul ignore next */
          cov_1zda1zdg4k().b[40][0]++;
          cov_1zda1zdg4k().s[85]++;
          ar = Array.prototype.slice.call(from, 0, i);
        } else
        /* istanbul ignore next */
        {
          cov_1zda1zdg4k().b[40][1]++;
        }
        cov_1zda1zdg4k().s[86]++;
        ar[i] = from[i];
      } else
      /* istanbul ignore next */
      {
        cov_1zda1zdg4k().b[38][1]++;
      }
    }
  } else
  /* istanbul ignore next */
  {
    cov_1zda1zdg4k().b[36][1]++;
  }
  cov_1zda1zdg4k().s[87]++;
  return to.concat(
  /* istanbul ignore next */
  (cov_1zda1zdg4k().b[41][0]++, ar) ||
  /* istanbul ignore next */
  (cov_1zda1zdg4k().b[41][1]++, Array.prototype.slice.call(from)));
}));
/* istanbul ignore next */
cov_1zda1zdg4k().s[88]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1zda1zdg4k().s[89]++;
exports.concurrentDatabaseService = exports.ConcurrentDatabaseService = void 0;
var prisma_1 =
/* istanbul ignore next */
(cov_1zda1zdg4k().s[90]++, require("@/lib/prisma"));
var optimized_database_service_1 =
/* istanbul ignore next */
(cov_1zda1zdg4k().s[91]++, require("./optimized-database-service"));
var ConcurrentDatabaseService =
/* istanbul ignore next */
(/** @class */cov_1zda1zdg4k().s[92]++, function () {
  /* istanbul ignore next */
  cov_1zda1zdg4k().f[16]++;
  function ConcurrentDatabaseService() {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[17]++;
    var _this =
    /* istanbul ignore next */
    (cov_1zda1zdg4k().s[93]++, this);
    /* istanbul ignore next */
    cov_1zda1zdg4k().s[94]++;
    this.operationQueue = new Map();
    /* istanbul ignore next */
    cov_1zda1zdg4k().s[95]++;
    this.executingOperations = new Set();
    /* istanbul ignore next */
    cov_1zda1zdg4k().s[96]++;
    this.completedOperations = new Map();
    /* istanbul ignore next */
    cov_1zda1zdg4k().s[97]++;
    this.metrics = {
      totalOperations: 0,
      concurrentOperations: 0,
      averageExecutionTime: 0,
      successRate: 0,
      connectionPoolUtilization: 0,
      queryOptimizationSavings: 0
    };
    /* istanbul ignore next */
    cov_1zda1zdg4k().s[98]++;
    this.maxConcurrentOperations = parseInt(
    /* istanbul ignore next */
    (cov_1zda1zdg4k().b[42][0]++, process.env.MAX_CONCURRENT_DB_OPS) ||
    /* istanbul ignore next */
    (cov_1zda1zdg4k().b[42][1]++, '5'));
    /* istanbul ignore next */
    cov_1zda1zdg4k().s[99]++;
    this.defaultTimeout = parseInt(
    /* istanbul ignore next */
    (cov_1zda1zdg4k().b[43][0]++, process.env.DB_OPERATION_TIMEOUT) ||
    /* istanbul ignore next */
    (cov_1zda1zdg4k().b[43][1]++, '30000'));
    // Initialize operation processor
    /* istanbul ignore next */
    cov_1zda1zdg4k().s[100]++;
    setInterval(function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[18]++;
      cov_1zda1zdg4k().s[101]++;
      return _this.processOperationQueue();
    }, 100);
    // Initialize metrics collection
    /* istanbul ignore next */
    cov_1zda1zdg4k().s[102]++;
    setInterval(function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[19]++;
      cov_1zda1zdg4k().s[103]++;
      return _this.updateMetrics();
    }, 5000);
  }
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[104]++;
  ConcurrentDatabaseService.getInstance = function () {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[20]++;
    cov_1zda1zdg4k().s[105]++;
    if (!ConcurrentDatabaseService.instance) {
      /* istanbul ignore next */
      cov_1zda1zdg4k().b[44][0]++;
      cov_1zda1zdg4k().s[106]++;
      ConcurrentDatabaseService.instance = new ConcurrentDatabaseService();
    } else
    /* istanbul ignore next */
    {
      cov_1zda1zdg4k().b[44][1]++;
    }
    cov_1zda1zdg4k().s[107]++;
    return ConcurrentDatabaseService.instance;
  };
  /**
   * Execute multiple database operations concurrently
   */
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[108]++;
  ConcurrentDatabaseService.prototype.executeConcurrent = function (operations) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[21]++;
    cov_1zda1zdg4k().s[109]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[22]++;
      var startTime, operationIds, results, batchResults, error_1;
      var _this =
      /* istanbul ignore next */
      (cov_1zda1zdg4k().s[110]++, this);
      /* istanbul ignore next */
      cov_1zda1zdg4k().s[111]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[23]++;
        cov_1zda1zdg4k().s[112]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[45][0]++;
            cov_1zda1zdg4k().s[113]++;
            startTime = Date.now();
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[114]++;
            operationIds = operations.map(function (op) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().f[24]++;
              cov_1zda1zdg4k().s[115]++;
              return op.id;
            });
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[116]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[45][1]++;
            cov_1zda1zdg4k().s[117]++;
            _a.trys.push([1, 3,, 4]);
            // Add operations to queue
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[118]++;
            operations.forEach(function (op) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().f[25]++;
              cov_1zda1zdg4k().s[119]++;
              _this.operationQueue.set(op.id, op);
              /* istanbul ignore next */
              cov_1zda1zdg4k().s[120]++;
              _this.metrics.totalOperations++;
            });
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[121]++;
            return [4 /*yield*/, Promise.allSettled(operations.map(function (op) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().f[26]++;
              cov_1zda1zdg4k().s[122]++;
              return _this.waitForOperation(op.id);
            }))];
          case 2:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[45][2]++;
            cov_1zda1zdg4k().s[123]++;
            results = _a.sent();
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[124]++;
            batchResults = results.map(function (result, index) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().f[27]++;
              var _a;
              var operationId =
              /* istanbul ignore next */
              (cov_1zda1zdg4k().s[125]++, operationIds[index]);
              var executionTime =
              /* istanbul ignore next */
              (cov_1zda1zdg4k().s[126]++, Date.now() - startTime);
              /* istanbul ignore next */
              cov_1zda1zdg4k().s[127]++;
              if (result.status === 'fulfilled') {
                /* istanbul ignore next */
                cov_1zda1zdg4k().b[46][0]++;
                cov_1zda1zdg4k().s[128]++;
                return {
                  success: true,
                  data: result.value,
                  operationId: operationId,
                  executionTime: executionTime
                };
              } else {
                /* istanbul ignore next */
                cov_1zda1zdg4k().b[46][1]++;
                cov_1zda1zdg4k().s[129]++;
                return {
                  success: false,
                  error:
                  /* istanbul ignore next */
                  (cov_1zda1zdg4k().b[47][0]++,
                  /* istanbul ignore next */
                  (cov_1zda1zdg4k().b[49][0]++, (_a = result.reason) === null) ||
                  /* istanbul ignore next */
                  (cov_1zda1zdg4k().b[49][1]++, _a === void 0) ?
                  /* istanbul ignore next */
                  (cov_1zda1zdg4k().b[48][0]++, void 0) :
                  /* istanbul ignore next */
                  (cov_1zda1zdg4k().b[48][1]++, _a.message)) ||
                  /* istanbul ignore next */
                  (cov_1zda1zdg4k().b[47][1]++, 'Operation failed'),
                  operationId: operationId,
                  executionTime: executionTime
                };
              }
            });
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[130]++;
            return [2 /*return*/, batchResults];
          case 3:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[45][3]++;
            cov_1zda1zdg4k().s[131]++;
            error_1 = _a.sent();
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[132]++;
            console.error('Concurrent database execution failed:', error_1);
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[133]++;
            throw error_1;
          case 4:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[45][4]++;
            cov_1zda1zdg4k().s[134]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Optimized skill gap analysis creation with concurrent operations
   */
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[135]++;
  ConcurrentDatabaseService.prototype.createSkillGapAnalysisOptimized = function (userId, requestData, analysisData, careerPathData) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[28]++;
    cov_1zda1zdg4k().s[136]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[29]++;
      var operations, results, analysisResult;
      var _this =
      /* istanbul ignore next */
      (cov_1zda1zdg4k().s[137]++, this);
      /* istanbul ignore next */
      cov_1zda1zdg4k().s[138]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[30]++;
        cov_1zda1zdg4k().s[139]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[50][0]++;
            cov_1zda1zdg4k().s[140]++;
            operations = [{
              id: 'create_analysis',
              operation: function () {
                /* istanbul ignore next */
                cov_1zda1zdg4k().f[31]++;
                cov_1zda1zdg4k().s[141]++;
                return _this.createAnalysisRecord(userId, requestData, analysisData);
              },
              priority: 'high',
              timeout: this.defaultTimeout,
              retries: 2
            }, {
              id: 'fetch_user_profile',
              operation: function () {
                /* istanbul ignore next */
                cov_1zda1zdg4k().f[32]++;
                cov_1zda1zdg4k().s[142]++;
                return _this.fetchUserProfile(userId);
              },
              priority: 'medium',
              timeout: 5000,
              retries: 1
            }, {
              id: 'update_user_stats',
              operation: function () {
                /* istanbul ignore next */
                cov_1zda1zdg4k().f[33]++;
                cov_1zda1zdg4k().s[143]++;
                return _this.updateUserAnalysisStats(userId);
              },
              priority: 'low',
              timeout: 5000,
              retries: 1,
              dependencies: ['create_analysis']
            }];
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[144]++;
            return [4 /*yield*/, this.executeConcurrent(operations)];
          case 1:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[50][1]++;
            cov_1zda1zdg4k().s[145]++;
            results = _a.sent();
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[146]++;
            analysisResult = results.find(function (r) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().f[34]++;
              cov_1zda1zdg4k().s[147]++;
              return r.operationId === 'create_analysis';
            });
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[148]++;
            if (!(
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[53][0]++, analysisResult === null) ||
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[53][1]++, analysisResult === void 0) ?
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[52][0]++, void 0) :
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[52][1]++, analysisResult.success))) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().b[51][0]++;
              cov_1zda1zdg4k().s[149]++;
              throw new Error(
              /* istanbul ignore next */
              (cov_1zda1zdg4k().b[54][0]++,
              /* istanbul ignore next */
              (cov_1zda1zdg4k().b[56][0]++, analysisResult === null) ||
              /* istanbul ignore next */
              (cov_1zda1zdg4k().b[56][1]++, analysisResult === void 0) ?
              /* istanbul ignore next */
              (cov_1zda1zdg4k().b[55][0]++, void 0) :
              /* istanbul ignore next */
              (cov_1zda1zdg4k().b[55][1]++, analysisResult.error)) ||
              /* istanbul ignore next */
              (cov_1zda1zdg4k().b[54][1]++, 'Failed to create skill gap analysis'));
            } else
            /* istanbul ignore next */
            {
              cov_1zda1zdg4k().b[51][1]++;
            }
            cov_1zda1zdg4k().s[150]++;
            return [2 /*return*/, analysisResult.data];
        }
      });
    });
  };
  /**
   * Batch fetch user assessments with optimization
   */
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[151]++;
  ConcurrentDatabaseService.prototype.fetchUserAssessmentsOptimized = function (userId) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[35]++;
    cov_1zda1zdg4k().s[152]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[36]++;
      var operations, results, skillAssessments, careerAssessments, learningProgress;
      var _this =
      /* istanbul ignore next */
      (cov_1zda1zdg4k().s[153]++, this);
      var _a, _b, _c;
      /* istanbul ignore next */
      cov_1zda1zdg4k().s[154]++;
      return __generator(this, function (_d) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[37]++;
        cov_1zda1zdg4k().s[155]++;
        switch (_d.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[57][0]++;
            cov_1zda1zdg4k().s[156]++;
            operations = [{
              id: 'fetch_skill_assessments_optimized',
              operation: function () {
                /* istanbul ignore next */
                cov_1zda1zdg4k().f[38]++;
                cov_1zda1zdg4k().s[157]++;
                return optimized_database_service_1.optimizedDatabaseService.getUserSkillAssessmentsOptimized(userId, 50);
              },
              priority: 'high',
              timeout: 5000,
              // Reduced timeout due to optimization
              retries: 2
            }, {
              id: 'fetch_career_assessments_optimized',
              operation: function () {
                /* istanbul ignore next */
                cov_1zda1zdg4k().f[39]++;
                cov_1zda1zdg4k().s[158]++;
                return optimized_database_service_1.optimizedDatabaseService.getCareerAssessmentOptimized(userId, 'COMPLETED');
              },
              priority: 'high',
              timeout: 5000,
              // Reduced timeout due to optimization
              retries: 2
            }, {
              id: 'fetch_learning_progress',
              operation: function () {
                /* istanbul ignore next */
                cov_1zda1zdg4k().f[40]++;
                cov_1zda1zdg4k().s[159]++;
                return _this.fetchLearningProgress(userId);
              },
              priority: 'medium',
              timeout: 5000,
              retries: 1
            }];
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[160]++;
            return [4 /*yield*/, this.executeConcurrent(operations)];
          case 1:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[57][1]++;
            cov_1zda1zdg4k().s[161]++;
            results = _d.sent();
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[162]++;
            skillAssessments =
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[58][0]++,
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[60][0]++, (_a = results.find(function (r) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().f[41]++;
              cov_1zda1zdg4k().s[163]++;
              return r.operationId === 'fetch_skill_assessments_optimized';
            })) === null) ||
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[60][1]++, _a === void 0) ?
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[59][0]++, void 0) :
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[59][1]++, _a.data)) ||
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[58][1]++, []);
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[164]++;
            careerAssessments =
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[61][0]++,
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[63][0]++, (_b = results.find(function (r) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().f[42]++;
              cov_1zda1zdg4k().s[165]++;
              return r.operationId === 'fetch_career_assessments_optimized';
            })) === null) ||
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[63][1]++, _b === void 0) ?
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[62][0]++, void 0) :
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[62][1]++, _b.data)) ||
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[61][1]++, []);
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[166]++;
            learningProgress =
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[64][0]++,
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[66][0]++, (_c = results.find(function (r) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().f[43]++;
              cov_1zda1zdg4k().s[167]++;
              return r.operationId === 'fetch_learning_progress';
            })) === null) ||
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[66][1]++, _c === void 0) ?
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[65][0]++, void 0) :
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[65][1]++, _c.data)) ||
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[64][1]++, []);
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[168]++;
            return [2 /*return*/, __spreadArray(__spreadArray(__spreadArray([], skillAssessments, true), careerAssessments, true), learningProgress, true)];
        }
      });
    });
  };
  /**
   * Optimized career path data fetching
   */
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[169]++;
  ConcurrentDatabaseService.prototype.fetchCareerPathDataOptimized = function (careerPathId) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[44]++;
    cov_1zda1zdg4k().s[170]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[45]++;
      var operations, results, careerPath, skills, learningPaths, marketData;
      var _this =
      /* istanbul ignore next */
      (cov_1zda1zdg4k().s[171]++, this);
      var _a, _b, _c, _d;
      /* istanbul ignore next */
      cov_1zda1zdg4k().s[172]++;
      return __generator(this, function (_e) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[46]++;
        cov_1zda1zdg4k().s[173]++;
        switch (_e.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[67][0]++;
            cov_1zda1zdg4k().s[174]++;
            operations = [{
              id: 'fetch_career_path_optimized',
              operation: function () {
                /* istanbul ignore next */
                cov_1zda1zdg4k().f[47]++;
                cov_1zda1zdg4k().s[175]++;
                return _this.fetchCareerPathOptimized(careerPathId);
              },
              priority: 'high',
              timeout: 3000,
              // Reduced timeout due to optimization
              retries: 2
            }, {
              id: 'fetch_related_skills',
              operation: function () {
                /* istanbul ignore next */
                cov_1zda1zdg4k().f[48]++;
                cov_1zda1zdg4k().s[176]++;
                return _this.fetchCareerPathSkills(careerPathId);
              },
              priority: 'high',
              timeout: 5000,
              retries: 2
            }, {
              id: 'fetch_learning_paths',
              operation: function () {
                /* istanbul ignore next */
                cov_1zda1zdg4k().f[49]++;
                cov_1zda1zdg4k().s[177]++;
                return _this.fetchCareerPathLearningPaths(careerPathId);
              },
              priority: 'medium',
              timeout: 5000,
              retries: 1
            }, {
              id: 'fetch_market_data',
              operation: function () {
                /* istanbul ignore next */
                cov_1zda1zdg4k().f[50]++;
                cov_1zda1zdg4k().s[178]++;
                return _this.fetchCareerPathMarketData(careerPathId);
              },
              priority: 'low',
              timeout: 10000,
              retries: 1
            }];
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[179]++;
            return [4 /*yield*/, this.executeConcurrent(operations)];
          case 1:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[67][1]++;
            cov_1zda1zdg4k().s[180]++;
            results = _e.sent();
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[181]++;
            careerPath =
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[69][0]++, (_a = results.find(function (r) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().f[51]++;
              cov_1zda1zdg4k().s[182]++;
              return r.operationId === 'fetch_career_path_optimized';
            })) === null) ||
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[69][1]++, _a === void 0) ?
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[68][0]++, void 0) :
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[68][1]++, _a.data);
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[183]++;
            skills =
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[70][0]++,
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[72][0]++, (_b = results.find(function (r) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().f[52]++;
              cov_1zda1zdg4k().s[184]++;
              return r.operationId === 'fetch_related_skills';
            })) === null) ||
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[72][1]++, _b === void 0) ?
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[71][0]++, void 0) :
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[71][1]++, _b.data)) ||
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[70][1]++, []);
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[185]++;
            learningPaths =
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[73][0]++,
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[75][0]++, (_c = results.find(function (r) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().f[53]++;
              cov_1zda1zdg4k().s[186]++;
              return r.operationId === 'fetch_learning_paths';
            })) === null) ||
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[75][1]++, _c === void 0) ?
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[74][0]++, void 0) :
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[74][1]++, _c.data)) ||
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[73][1]++, []);
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[187]++;
            marketData =
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[77][0]++, (_d = results.find(function (r) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().f[54]++;
              cov_1zda1zdg4k().s[188]++;
              return r.operationId === 'fetch_market_data';
            })) === null) ||
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[77][1]++, _d === void 0) ?
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[76][0]++, void 0) :
            /* istanbul ignore next */
            (cov_1zda1zdg4k().b[76][1]++, _d.data);
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[189]++;
            if (!careerPath) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().b[78][0]++;
              cov_1zda1zdg4k().s[190]++;
              throw new Error('Career path not found');
            } else
            /* istanbul ignore next */
            {
              cov_1zda1zdg4k().b[78][1]++;
            }
            cov_1zda1zdg4k().s[191]++;
            return [2 /*return*/, __assign(__assign({}, careerPath), {
              relatedSkills: skills,
              learningPaths: learningPaths,
              marketData: marketData
            })];
        }
      });
    });
  };
  /**
   * Individual database operations
   */
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[192]++;
  ConcurrentDatabaseService.prototype.createAnalysisRecord = function (userId, requestData, analysisData) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[55]++;
    cov_1zda1zdg4k().s[193]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[56]++;
      var expiresAt;
      var _a, _b, _c, _d, _e;
      /* istanbul ignore next */
      cov_1zda1zdg4k().s[194]++;
      return __generator(this, function (_f) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[57]++;
        cov_1zda1zdg4k().s[195]++;
        switch (_f.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[79][0]++;
            cov_1zda1zdg4k().s[196]++;
            expiresAt = new Date();
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[197]++;
            expiresAt.setMonth(expiresAt.getMonth() + 3);
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[198]++;
            return [4 /*yield*/, prisma_1.prisma.skillGapAnalysis.create({
              data: {
                userId: userId,
                targetCareerPathId:
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[80][0]++,
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[82][0]++, (_a = requestData.targetCareerPath) === null) ||
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[82][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[81][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[81][1]++, _a.careerPathId)) ||
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[80][1]++, null),
                targetCareerPathName:
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[84][0]++, (_b = requestData.targetCareerPath) === null) ||
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[84][1]++, _b === void 0) ?
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[83][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[83][1]++, _b.careerPathName),
                experienceLevel:
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[86][0]++, (_c = requestData.targetCareerPath) === null) ||
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[86][1]++, _c === void 0) ?
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[85][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[85][1]++, _c.targetLevel),
                timeframe:
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[88][0]++, (_d = requestData.preferences) === null) ||
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[88][1]++, _d === void 0) ?
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[87][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[87][1]++, _d.timeframe),
                analysisData: analysisData,
                skillGaps:
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[89][0]++, analysisData.skillGaps) ||
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[89][1]++, []),
                learningPlan:
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[90][0]++, analysisData.learningPlan) ||
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[90][1]++, {}),
                marketData:
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[91][0]++, analysisData.marketInsights) ||
                /* istanbul ignore next */
                (cov_1zda1zdg4k().b[91][1]++, null),
                progressTracking: {
                  milestones:
                  /* istanbul ignore next */
                  (cov_1zda1zdg4k().b[92][0]++,
                  /* istanbul ignore next */
                  (cov_1zda1zdg4k().b[94][0]++, (_e = analysisData.learningPlan) === null) ||
                  /* istanbul ignore next */
                  (cov_1zda1zdg4k().b[94][1]++, _e === void 0) ?
                  /* istanbul ignore next */
                  (cov_1zda1zdg4k().b[93][0]++, void 0) :
                  /* istanbul ignore next */
                  (cov_1zda1zdg4k().b[93][1]++, _e.milestones)) ||
                  /* istanbul ignore next */
                  (cov_1zda1zdg4k().b[92][1]++, []),
                  completedMilestones: [],
                  currentPhase: 'planning'
                },
                status: 'ACTIVE',
                completionPercentage: 0,
                expiresAt: expiresAt
              }
            })];
          case 1:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[79][1]++;
            cov_1zda1zdg4k().s[199]++;
            return [2 /*return*/, _f.sent()];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[200]++;
  ConcurrentDatabaseService.prototype.fetchUserProfile = function (userId) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[58]++;
    cov_1zda1zdg4k().s[201]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[59]++;
      cov_1zda1zdg4k().s[202]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[60]++;
        cov_1zda1zdg4k().s[203]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[95][0]++;
            cov_1zda1zdg4k().s[204]++;
            return [4 /*yield*/, prisma_1.prisma.user.findUnique({
              where: {
                id: userId
              },
              select: {
                id: true,
                email: true,
                profile: {
                  select: {
                    firstName: true,
                    lastName: true,
                    experienceLevel: true,
                    jobTitle: true,
                    company: true,
                    currentIndustry: true
                  }
                }
              }
            })];
          case 1:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[95][1]++;
            cov_1zda1zdg4k().s[205]++;
            return [2 /*return*/, _a.sent()];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[206]++;
  ConcurrentDatabaseService.prototype.updateUserAnalysisStats = function (userId) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[61]++;
    cov_1zda1zdg4k().s[207]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[62]++;
      cov_1zda1zdg4k().s[208]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[63]++;
        cov_1zda1zdg4k().s[209]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[96][0]++;
            cov_1zda1zdg4k().s[210]++;
            return [4 /*yield*/, prisma_1.prisma.user.update({
              where: {
                id: userId
              },
              data: {
                profile: {
                  update: {
                    lastProfileUpdate: new Date(),
                    lastActiveAt: new Date()
                  }
                }
              }
            })];
          case 1:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[96][1]++;
            cov_1zda1zdg4k().s[211]++;
            return [2 /*return*/, _a.sent()];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[212]++;
  ConcurrentDatabaseService.prototype.fetchSkillAssessments = function (userId) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[64]++;
    cov_1zda1zdg4k().s[213]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[65]++;
      cov_1zda1zdg4k().s[214]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[66]++;
        cov_1zda1zdg4k().s[215]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[97][0]++;
            cov_1zda1zdg4k().s[216]++;
            return [4 /*yield*/, prisma_1.prisma.skillAssessment.findMany({
              where: {
                userId: userId
              },
              include: {
                skill: {
                  select: {
                    id: true,
                    name: true,
                    category: true
                  }
                }
              },
              orderBy: {
                createdAt: 'desc'
              },
              take: 50
            })];
          case 1:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[97][1]++;
            cov_1zda1zdg4k().s[217]++;
            return [2 /*return*/, _a.sent()];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[218]++;
  ConcurrentDatabaseService.prototype.fetchCareerAssessments = function (userId) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[67]++;
    cov_1zda1zdg4k().s[219]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[68]++;
      cov_1zda1zdg4k().s[220]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[69]++;
        cov_1zda1zdg4k().s[221]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[98][0]++;
            cov_1zda1zdg4k().s[222]++;
            return [4 /*yield*/, prisma_1.prisma.assessment.findMany({
              where: {
                userId: userId
              },
              include: {
                responses: {
                  select: {
                    questionKey: true,
                    answerValue: true
                  }
                }
              },
              orderBy: {
                createdAt: 'desc'
              },
              take: 10
            })];
          case 1:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[98][1]++;
            cov_1zda1zdg4k().s[223]++;
            return [2 /*return*/, _a.sent()];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[224]++;
  ConcurrentDatabaseService.prototype.fetchLearningProgress = function (userId) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[70]++;
    cov_1zda1zdg4k().s[225]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[71]++;
      cov_1zda1zdg4k().s[226]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[72]++;
        cov_1zda1zdg4k().s[227]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[99][0]++;
            cov_1zda1zdg4k().s[228]++;
            return [4 /*yield*/, prisma_1.prisma.userLearningProgress.findMany({
              where: {
                userId: userId
              },
              include: {
                resource: {
                  select: {
                    id: true,
                    title: true,
                    skillLevel: true,
                    category: true,
                    type: true
                  }
                }
              },
              orderBy: {
                updatedAt: 'desc'
              },
              take: 20
            })];
          case 1:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[99][1]++;
            cov_1zda1zdg4k().s[229]++;
            return [2 /*return*/, _a.sent()];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[230]++;
  ConcurrentDatabaseService.prototype.fetchCareerPath = function (careerPathId) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[73]++;
    cov_1zda1zdg4k().s[231]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[74]++;
      cov_1zda1zdg4k().s[232]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[75]++;
        cov_1zda1zdg4k().s[233]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[100][0]++;
            cov_1zda1zdg4k().s[234]++;
            return [4 /*yield*/, prisma_1.prisma.careerPath.findUnique({
              where: {
                id: careerPathId
              },
              include: {
                _count: {
                  select: {
                    relatedSkills: true,
                    learningPaths: true
                  }
                }
              }
            })];
          case 1:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[100][1]++;
            cov_1zda1zdg4k().s[235]++;
            return [2 /*return*/, _a.sent()];
        }
      });
    });
  };
  /**
   * Optimized career path fetching with selective loading
   */
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[236]++;
  ConcurrentDatabaseService.prototype.fetchCareerPathOptimized = function (careerPathId) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[76]++;
    cov_1zda1zdg4k().s[237]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[77]++;
      cov_1zda1zdg4k().s[238]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[78]++;
        cov_1zda1zdg4k().s[239]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[101][0]++;
            cov_1zda1zdg4k().s[240]++;
            return [4 /*yield*/, prisma_1.prisma.careerPath.findUnique({
              where: {
                id: careerPathId
              },
              select: {
                id: true,
                name: true,
                slug: true,
                overview: true,
                pros: true,
                cons: true,
                actionableSteps: true,
                isActive: true,
                _count: {
                  select: {
                    relatedSkills: true,
                    learningPaths: true
                  }
                }
              }
            })];
          case 1:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[101][1]++;
            cov_1zda1zdg4k().s[241]++;
            return [2 /*return*/, _a.sent()];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[242]++;
  ConcurrentDatabaseService.prototype.fetchCareerPathSkills = function (careerPathId) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[79]++;
    cov_1zda1zdg4k().s[243]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[80]++;
      cov_1zda1zdg4k().s[244]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[81]++;
        cov_1zda1zdg4k().s[245]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[102][0]++;
            cov_1zda1zdg4k().s[246]++;
            return [4 /*yield*/, prisma_1.prisma.skill.findMany({
              where: {
                careerPaths: {
                  some: {
                    id: careerPathId
                  }
                }
              },
              select: {
                id: true,
                name: true,
                category: true,
                description: true
              },
              take: 20
            })];
          case 1:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[102][1]++;
            cov_1zda1zdg4k().s[247]++;
            return [2 /*return*/, _a.sent()];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[248]++;
  ConcurrentDatabaseService.prototype.fetchCareerPathLearningPaths = function (careerPathId) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[82]++;
    cov_1zda1zdg4k().s[249]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[83]++;
      cov_1zda1zdg4k().s[250]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[84]++;
        cov_1zda1zdg4k().s[251]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[103][0]++;
            cov_1zda1zdg4k().s[252]++;
            return [4 /*yield*/, prisma_1.prisma.learningPath.findMany({
              where: {
                careerPaths: {
                  some: {
                    id: careerPathId
                  }
                }
              },
              include: {
                _count: {
                  select: {
                    steps: true
                  }
                },
                skills: {
                  select: {
                    name: true
                  },
                  take: 5
                }
              },
              take: 10
            })];
          case 1:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[103][1]++;
            cov_1zda1zdg4k().s[253]++;
            return [2 /*return*/, _a.sent()];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[254]++;
  ConcurrentDatabaseService.prototype.fetchCareerPathMarketData = function (careerPathId) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[85]++;
    cov_1zda1zdg4k().s[255]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[86]++;
      cov_1zda1zdg4k().s[256]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[87]++;
        cov_1zda1zdg4k().s[257]++;
        // This would fetch market data from external APIs or cached data
        // For now, return null as this is typically handled by external services
        return [2 /*return*/, null];
      });
    });
  };
  /**
   * Operation queue processing
   */
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[258]++;
  ConcurrentDatabaseService.prototype.processOperationQueue = function () {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[88]++;
    cov_1zda1zdg4k().s[259]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[89]++;
      var availableOperations, operation;
      var _this =
      /* istanbul ignore next */
      (cov_1zda1zdg4k().s[260]++, this);
      /* istanbul ignore next */
      cov_1zda1zdg4k().s[261]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[90]++;
        cov_1zda1zdg4k().s[262]++;
        if (this.executingOperations.size >= this.maxConcurrentOperations) {
          /* istanbul ignore next */
          cov_1zda1zdg4k().b[104][0]++;
          cov_1zda1zdg4k().s[263]++;
          return [2 /*return*/];
        } else
        /* istanbul ignore next */
        {
          cov_1zda1zdg4k().b[104][1]++;
        }
        cov_1zda1zdg4k().s[264]++;
        availableOperations = Array.from(this.operationQueue.values()).filter(function (op) {
          /* istanbul ignore next */
          cov_1zda1zdg4k().f[91]++;
          cov_1zda1zdg4k().s[265]++;
          return !_this.executingOperations.has(op.id);
        }).filter(function (op) {
          /* istanbul ignore next */
          cov_1zda1zdg4k().f[92]++;
          cov_1zda1zdg4k().s[266]++;
          return _this.areDependenciesMet(op);
        }).sort(function (a, b) {
          /* istanbul ignore next */
          cov_1zda1zdg4k().f[93]++;
          var priorityOrder =
          /* istanbul ignore next */
          (cov_1zda1zdg4k().s[267]++, {
            high: 3,
            medium: 2,
            low: 1
          });
          /* istanbul ignore next */
          cov_1zda1zdg4k().s[268]++;
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        });
        /* istanbul ignore next */
        cov_1zda1zdg4k().s[269]++;
        if (availableOperations.length === 0) {
          /* istanbul ignore next */
          cov_1zda1zdg4k().b[105][0]++;
          cov_1zda1zdg4k().s[270]++;
          return [2 /*return*/];
        } else
        /* istanbul ignore next */
        {
          cov_1zda1zdg4k().b[105][1]++;
        }
        cov_1zda1zdg4k().s[271]++;
        operation = availableOperations[0];
        /* istanbul ignore next */
        cov_1zda1zdg4k().s[272]++;
        this.executeOperation(operation);
        /* istanbul ignore next */
        cov_1zda1zdg4k().s[273]++;
        return [2 /*return*/];
      });
    });
  };
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[274]++;
  ConcurrentDatabaseService.prototype.executeOperation = function (operation) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[94]++;
    cov_1zda1zdg4k().s[275]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[95]++;
      var startTime, result, executionTime, error_2, executionTime;
      /* istanbul ignore next */
      cov_1zda1zdg4k().s[276]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[96]++;
        cov_1zda1zdg4k().s[277]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[106][0]++;
            cov_1zda1zdg4k().s[278]++;
            this.executingOperations.add(operation.id);
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[279]++;
            this.metrics.concurrentOperations = this.executingOperations.size;
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[280]++;
            startTime = Date.now();
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[281]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[106][1]++;
            cov_1zda1zdg4k().s[282]++;
            _a.trys.push([1, 3, 4, 5]);
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[283]++;
            return [4 /*yield*/, Promise.race([operation.operation(), new Promise(function (_, reject) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().f[97]++;
              cov_1zda1zdg4k().s[284]++;
              return setTimeout(function () {
                /* istanbul ignore next */
                cov_1zda1zdg4k().f[98]++;
                cov_1zda1zdg4k().s[285]++;
                return reject(new Error('Operation timeout'));
              }, operation.timeout);
            })])];
          case 2:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[106][2]++;
            cov_1zda1zdg4k().s[286]++;
            result = _a.sent();
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[287]++;
            this.completedOperations.set(operation.id, result);
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[288]++;
            this.operationQueue.delete(operation.id);
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[289]++;
            executionTime = Date.now() - startTime;
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[290]++;
            this.updateExecutionMetrics(executionTime, true);
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[291]++;
            return [3 /*break*/, 5];
          case 3:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[106][3]++;
            cov_1zda1zdg4k().s[292]++;
            error_2 = _a.sent();
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[293]++;
            console.error("Database operation ".concat(operation.id, " failed:"), error_2);
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[294]++;
            if (operation.retries > 0) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().b[107][0]++;
              cov_1zda1zdg4k().s[295]++;
              operation.retries--;
              // Re-queue with lower priority
              /* istanbul ignore next */
              cov_1zda1zdg4k().s[296]++;
              operation.priority = 'low';
            } else {
              /* istanbul ignore next */
              cov_1zda1zdg4k().b[107][1]++;
              cov_1zda1zdg4k().s[297]++;
              this.completedOperations.set(operation.id, {
                error: error_2.message
              });
              /* istanbul ignore next */
              cov_1zda1zdg4k().s[298]++;
              this.operationQueue.delete(operation.id);
            }
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[299]++;
            executionTime = Date.now() - startTime;
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[300]++;
            this.updateExecutionMetrics(executionTime, false);
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[301]++;
            return [3 /*break*/, 5];
          case 4:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[106][4]++;
            cov_1zda1zdg4k().s[302]++;
            this.executingOperations.delete(operation.id);
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[303]++;
            this.metrics.concurrentOperations = this.executingOperations.size;
            /* istanbul ignore next */
            cov_1zda1zdg4k().s[304]++;
            return [7 /*endfinally*/];
          case 5:
            /* istanbul ignore next */
            cov_1zda1zdg4k().b[106][5]++;
            cov_1zda1zdg4k().s[305]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[306]++;
  ConcurrentDatabaseService.prototype.areDependenciesMet = function (operation) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[99]++;
    var _this =
    /* istanbul ignore next */
    (cov_1zda1zdg4k().s[307]++, this);
    /* istanbul ignore next */
    cov_1zda1zdg4k().s[308]++;
    if (!operation.dependencies) {
      /* istanbul ignore next */
      cov_1zda1zdg4k().b[108][0]++;
      cov_1zda1zdg4k().s[309]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_1zda1zdg4k().b[108][1]++;
    }
    cov_1zda1zdg4k().s[310]++;
    return operation.dependencies.every(function (depId) {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[100]++;
      cov_1zda1zdg4k().s[311]++;
      return _this.completedOperations.has(depId);
    });
  };
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[312]++;
  ConcurrentDatabaseService.prototype.waitForOperation = function (operationId) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[101]++;
    cov_1zda1zdg4k().s[313]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[102]++;
      var _this =
      /* istanbul ignore next */
      (cov_1zda1zdg4k().s[314]++, this);
      /* istanbul ignore next */
      cov_1zda1zdg4k().s[315]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[103]++;
        cov_1zda1zdg4k().s[316]++;
        return [2 /*return*/, new Promise(function (resolve, reject) {
          /* istanbul ignore next */
          cov_1zda1zdg4k().f[104]++;
          cov_1zda1zdg4k().s[317]++;
          var checkCompletion = function () {
            /* istanbul ignore next */
            cov_1zda1zdg4k().f[105]++;
            cov_1zda1zdg4k().s[318]++;
            if (_this.completedOperations.has(operationId)) {
              /* istanbul ignore next */
              cov_1zda1zdg4k().b[109][0]++;
              var result =
              /* istanbul ignore next */
              (cov_1zda1zdg4k().s[319]++, _this.completedOperations.get(operationId));
              /* istanbul ignore next */
              cov_1zda1zdg4k().s[320]++;
              if (result.error) {
                /* istanbul ignore next */
                cov_1zda1zdg4k().b[110][0]++;
                cov_1zda1zdg4k().s[321]++;
                reject(new Error(result.error));
              } else {
                /* istanbul ignore next */
                cov_1zda1zdg4k().b[110][1]++;
                cov_1zda1zdg4k().s[322]++;
                resolve(result);
              }
            } else {
              /* istanbul ignore next */
              cov_1zda1zdg4k().b[109][1]++;
              cov_1zda1zdg4k().s[323]++;
              if (
              /* istanbul ignore next */
              (cov_1zda1zdg4k().b[112][0]++, !_this.operationQueue.has(operationId)) &&
              /* istanbul ignore next */
              (cov_1zda1zdg4k().b[112][1]++, !_this.executingOperations.has(operationId))) {
                /* istanbul ignore next */
                cov_1zda1zdg4k().b[111][0]++;
                cov_1zda1zdg4k().s[324]++;
                reject(new Error('Operation not found'));
              } else {
                /* istanbul ignore next */
                cov_1zda1zdg4k().b[111][1]++;
                cov_1zda1zdg4k().s[325]++;
                setTimeout(checkCompletion, 50);
              }
            }
          };
          /* istanbul ignore next */
          cov_1zda1zdg4k().s[326]++;
          checkCompletion();
        })];
      });
    });
  };
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[327]++;
  ConcurrentDatabaseService.prototype.updateExecutionMetrics = function (executionTime, success) {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[106]++;
    var totalTime =
    /* istanbul ignore next */
    (cov_1zda1zdg4k().s[328]++, this.metrics.averageExecutionTime * (this.metrics.totalOperations - 1));
    /* istanbul ignore next */
    cov_1zda1zdg4k().s[329]++;
    this.metrics.averageExecutionTime = (totalTime + executionTime) / this.metrics.totalOperations;
    /* istanbul ignore next */
    cov_1zda1zdg4k().s[330]++;
    if (success) {
      /* istanbul ignore next */
      cov_1zda1zdg4k().b[113][0]++;
      cov_1zda1zdg4k().s[331]++;
      this.metrics.successRate = (this.metrics.successRate * (this.metrics.totalOperations - 1) + 1) / this.metrics.totalOperations;
    } else {
      /* istanbul ignore next */
      cov_1zda1zdg4k().b[113][1]++;
      cov_1zda1zdg4k().s[332]++;
      this.metrics.successRate = this.metrics.successRate * (this.metrics.totalOperations - 1) / this.metrics.totalOperations;
    }
  };
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[333]++;
  ConcurrentDatabaseService.prototype.updateMetrics = function () {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[107]++;
    cov_1zda1zdg4k().s[334]++;
    // Update connection pool utilization (simplified)
    this.metrics.connectionPoolUtilization = this.executingOperations.size / this.maxConcurrentOperations * 100;
  };
  /**
   * Get performance metrics
   */
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[335]++;
  ConcurrentDatabaseService.prototype.getMetrics = function () {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[108]++;
    cov_1zda1zdg4k().s[336]++;
    return __assign(__assign({}, this.metrics), {
      queueSize: this.operationQueue.size,
      executingOperations: this.executingOperations.size,
      completedOperations: this.completedOperations.size
    });
  };
  /**
   * Health check
   */
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[337]++;
  ConcurrentDatabaseService.prototype.healthCheck = function () {
    /* istanbul ignore next */
    cov_1zda1zdg4k().f[109]++;
    cov_1zda1zdg4k().s[338]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zda1zdg4k().f[110]++;
      var metrics;
      /* istanbul ignore next */
      cov_1zda1zdg4k().s[339]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zda1zdg4k().f[111]++;
        cov_1zda1zdg4k().s[340]++;
        try {
          /* istanbul ignore next */
          cov_1zda1zdg4k().s[341]++;
          metrics = this.getMetrics();
          /* istanbul ignore next */
          cov_1zda1zdg4k().s[342]++;
          return [2 /*return*/,
          /* istanbul ignore next */
          (cov_1zda1zdg4k().b[114][0]++, metrics.queueSize < 50) &&
          /* istanbul ignore next */
          (cov_1zda1zdg4k().b[114][1]++, metrics.executingOperations < this.maxConcurrentOperations) &&
          /* istanbul ignore next */
          (cov_1zda1zdg4k().b[114][2]++, metrics.successRate > 0.8)];
        } catch (error) {
          /* istanbul ignore next */
          cov_1zda1zdg4k().s[343]++;
          console.error('Concurrent database service health check failed:', error);
          /* istanbul ignore next */
          cov_1zda1zdg4k().s[344]++;
          return [2 /*return*/, false];
        }
        /* istanbul ignore next */
        cov_1zda1zdg4k().s[345]++;
        return [2 /*return*/];
      });
    });
  };
  /* istanbul ignore next */
  cov_1zda1zdg4k().s[346]++;
  return ConcurrentDatabaseService;
}());
/* istanbul ignore next */
cov_1zda1zdg4k().s[347]++;
exports.ConcurrentDatabaseService = ConcurrentDatabaseService;
// Export singleton instance
/* istanbul ignore next */
cov_1zda1zdg4k().s[348]++;
exports.concurrentDatabaseService = ConcurrentDatabaseService.getInstance();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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