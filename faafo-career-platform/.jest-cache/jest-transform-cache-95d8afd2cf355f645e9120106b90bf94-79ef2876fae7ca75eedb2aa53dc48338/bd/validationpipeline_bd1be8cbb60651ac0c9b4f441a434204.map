{"version": 3, "names": ["zod_1", "cov_k06ael1tm", "s", "require", "isomorphic_dompurify_1", "__importDefault", "DataValidationPipeline", "f", "rules", "b", "prototype", "addRule", "rule", "push", "validate", "data", "Promise", "errors", "sanitizedData", "__assign", "<PERSON><PERSON><PERSON><PERSON>", "fieldValue", "field", "required", "undefined", "concat", "validatedV<PERSON>ue", "schema", "parse", "sanitize", "this_1", "sanitizeString", "error", "z", "ZodError", "apply", "map", "err", "message", "_i", "_a", "length", "securityErrors", "performSecurityChecks", "input", "cleaned", "default", "ALLOWED_TAGS", "ALLOWED_ATTR", "replace", "trim", "sqlPatterns", "xssPatterns", "checkObjectForPatterns", "obj", "patterns", "errorMessage", "_this", "patterns_1", "pattern", "test", "Array", "isArray", "for<PERSON>ach", "item", "Object", "values", "value", "exports", "ValidationPipelines", "createPersonalInfoPipeline", "string", "min", "max", "regex", "email", "url", "createResumePipeline", "createUserPipeline"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/validation-pipeline.ts"], "sourcesContent": ["import { z } from 'zod';\nimport DOMPurify from 'isomorphic-dompurify';\n\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: string[];\n  sanitizedData?: any;\n}\n\nexport interface ValidationRule {\n  field: string;\n  schema: z.ZodSchema;\n  sanitize?: boolean;\n  required?: boolean;\n}\n\nexport class DataValidationPipeline {\n  private rules: ValidationRule[] = [];\n\n  constructor(rules: ValidationRule[] = []) {\n    this.rules = rules;\n  }\n\n  addRule(rule: ValidationRule): void {\n    this.rules.push(rule);\n  }\n\n  /**\n   * Comprehensive validation and sanitization pipeline\n   */\n  async validate(data: any): Promise<ValidationResult> {\n    const errors: string[] = [];\n    const sanitizedData: any = { ...data };\n\n    // Step 1: Basic type and structure validation\n    if (!data || typeof data !== 'object') {\n      return {\n        isValid: false,\n        errors: ['Invalid data structure provided']\n      };\n    }\n\n    // Step 2: Apply field-specific validation rules\n    for (const rule of this.rules) {\n      const fieldValue = data[rule.field];\n\n      // Check required fields\n      if (rule.required && (fieldValue === undefined || fieldValue === null || fieldValue === '')) {\n        errors.push(`${rule.field} is required`);\n        continue;\n      }\n\n      // Skip validation for optional empty fields\n      if (!rule.required && (fieldValue === undefined || fieldValue === null || fieldValue === '')) {\n        continue;\n      }\n\n      // Apply Zod schema validation\n      try {\n        const validatedValue = rule.schema.parse(fieldValue);\n        \n        // Apply sanitization if requested\n        if (rule.sanitize && typeof validatedValue === 'string') {\n          sanitizedData[rule.field] = this.sanitizeString(validatedValue);\n        } else {\n          sanitizedData[rule.field] = validatedValue;\n        }\n      } catch (error) {\n        if (error instanceof z.ZodError) {\n          errors.push(...error.errors.map(err => `${rule.field}: ${err.message}`));\n        } else {\n          errors.push(`${rule.field}: Validation failed`);\n        }\n      }\n    }\n\n    // Step 3: Additional security checks\n    const securityErrors = this.performSecurityChecks(sanitizedData);\n    errors.push(...securityErrors);\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      sanitizedData: errors.length === 0 ? sanitizedData : undefined\n    };\n  }\n\n  /**\n   * Sanitize string input to prevent XSS and other attacks\n   */\n  private sanitizeString(input: string): string {\n    // Remove HTML tags and potentially dangerous content\n    const cleaned = DOMPurify.sanitize(input, { \n      ALLOWED_TAGS: [], \n      ALLOWED_ATTR: [] \n    });\n    \n    // Additional sanitization for common attack patterns\n    return cleaned\n      .replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '') // Remove script tags\n      .replace(/javascript:/gi, '') // Remove javascript: protocols\n      .replace(/on\\w+\\s*=/gi, '') // Remove event handlers\n      .trim();\n  }\n\n  /**\n   * Perform additional security checks\n   */\n  private performSecurityChecks(data: any): string[] {\n    const errors: string[] = [];\n\n    // Check for SQL injection patterns\n    const sqlPatterns = [\n      /(\\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\\b)/i,\n      /(--|\\/\\*|\\*\\/|;)/,\n      /(\\b(OR|AND)\\b.*=.*)/i\n    ];\n\n    // Check for XSS patterns\n    const xssPatterns = [\n      /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\n      /javascript:/gi,\n      /on\\w+\\s*=/gi,\n      /<iframe/gi,\n      /<object/gi,\n      /<embed/gi\n    ];\n\n    // Recursively check all string values\n    this.checkObjectForPatterns(data, sqlPatterns, 'Potential SQL injection detected', errors);\n    this.checkObjectForPatterns(data, xssPatterns, 'Potential XSS attack detected', errors);\n\n    return errors;\n  }\n\n  /**\n   * Recursively check object for dangerous patterns\n   */\n  private checkObjectForPatterns(obj: any, patterns: RegExp[], errorMessage: string, errors: string[]): void {\n    if (typeof obj === 'string') {\n      for (const pattern of patterns) {\n        if (pattern.test(obj)) {\n          errors.push(errorMessage);\n          break;\n        }\n      }\n    } else if (Array.isArray(obj)) {\n      obj.forEach(item => this.checkObjectForPatterns(item, patterns, errorMessage, errors));\n    } else if (obj && typeof obj === 'object') {\n      Object.values(obj).forEach(value => this.checkObjectForPatterns(value, patterns, errorMessage, errors));\n    }\n  }\n}\n\n/**\n * Pre-configured validation pipelines for common use cases\n */\nexport class ValidationPipelines {\n  static createPersonalInfoPipeline(): DataValidationPipeline {\n    return new DataValidationPipeline([\n      {\n        field: 'firstName',\n        schema: z.string()\n          .min(1, 'First name is required')\n          .max(50, 'First name must be less than 50 characters')\n          .regex(/^[a-zA-Z\\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes'),\n        sanitize: true,\n        required: true\n      },\n      {\n        field: 'lastName',\n        schema: z.string()\n          .min(1, 'Last name is required')\n          .max(50, 'Last name must be less than 50 characters')\n          .regex(/^[a-zA-Z\\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes'),\n        sanitize: true,\n        required: true\n      },\n      {\n        field: 'email',\n        schema: z.string()\n          .email('Please enter a valid email address')\n          .max(254, 'Email is too long'),\n        sanitize: true,\n        required: true\n      },\n      {\n        field: 'phone',\n        schema: z.string()\n          .max(20, 'Phone number is too long')\n          .regex(/^[\\+]?[1-9][\\d\\s\\-\\(\\)]{0,15}$/, 'Please enter a valid phone number'),\n        sanitize: true,\n        required: false\n      },\n      {\n        field: 'location',\n        schema: z.string().max(100, 'Location must be less than 100 characters'),\n        sanitize: true,\n        required: false\n      },\n      {\n        field: 'website',\n        schema: z.string()\n          .url('Please enter a valid website URL')\n          .max(500, 'Website URL is too long'),\n        sanitize: true,\n        required: false\n      },\n      {\n        field: 'linkedIn',\n        schema: z.string()\n          .url('Please enter a valid LinkedIn URL')\n          .max(500, 'LinkedIn URL is too long'),\n        sanitize: true,\n        required: false\n      }\n    ]);\n  }\n\n  static createResumePipeline(): DataValidationPipeline {\n    return new DataValidationPipeline([\n      {\n        field: 'title',\n        schema: z.string()\n          .min(1, 'Resume title is required')\n          .max(200, 'Resume title must be less than 200 characters'),\n        sanitize: true,\n        required: true\n      },\n      {\n        field: 'summary',\n        schema: z.string()\n          .max(2000, 'Summary must be less than 2000 characters'),\n        sanitize: true,\n        required: false\n      },\n      {\n        field: 'template',\n        schema: z.string()\n          .max(50, 'Template name is too long'),\n        sanitize: true,\n        required: false\n      }\n    ]);\n  }\n\n  static createUserPipeline(): DataValidationPipeline {\n    return new DataValidationPipeline([\n      {\n        field: 'email',\n        schema: z.string()\n          .email('Please enter a valid email address')\n          .max(254, 'Email is too long'),\n        sanitize: true,\n        required: true\n      },\n      {\n        field: 'password',\n        schema: z.string()\n          .min(8, 'Password must be at least 8 characters')\n          .max(128, 'Password is too long')\n          .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),\n        sanitize: false, // Don't sanitize passwords\n        required: true\n      },\n      {\n        field: 'name',\n        schema: z.string()\n          .max(100, 'Name must be less than 100 characters'),\n        sanitize: true,\n        required: false\n      }\n    ]);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,KAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAC,sBAAA;AAAA;AAAA,CAAAH,aAAA,GAAAC,CAAA,QAAAG,eAAA,CAAAF,OAAA;AAeA,IAAAG,sBAAA;AAAA;AAAA,cAAAL,aAAA,GAAAC,CAAA;EAAA;EAAAD,aAAA,GAAAM,CAAA;EAGE,SAAAD,uBAAYE,KAA4B;IAAA;IAAAP,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAC,CAAA;IAA5B,IAAAM,KAAA;MAAA;MAAAP,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAC,CAAA;MAAAM,KAAA,KAA4B;IAAA;IAAA;IAAA;MAAAP,aAAA,GAAAQ,CAAA;IAAA;IAAAR,aAAA,GAAAC,CAAA;IAFhC,KAAAM,KAAK,GAAqB,EAAE;IAAC;IAAAP,aAAA,GAAAC,CAAA;IAGnC,IAAI,CAACM,KAAK,GAAGA,KAAK;EACpB;EAAC;EAAAP,aAAA,GAAAC,CAAA;EAEDI,sBAAA,CAAAI,SAAA,CAAAC,OAAO,GAAP,UAAQC,IAAoB;IAAA;IAAAX,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAC,CAAA;IAC1B,IAAI,CAACM,KAAK,CAACK,IAAI,CAACD,IAAI,CAAC;EACvB,CAAC;EAED;;;EAAA;EAAAX,aAAA,GAAAC,CAAA;EAGMI,sBAAA,CAAAI,SAAA,CAAAI,QAAQ,GAAd,UAAeC,IAAS;IAAA;IAAAd,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAC,CAAA;mCAAGc,OAAO;MAAA;MAAAf,aAAA,GAAAM,CAAA;;;;;;;;QAC1BU,MAAM,GAAa,EAAE;QAAC;QAAAhB,aAAA,GAAAC,CAAA;QACtBgB,aAAa,GAAAC,QAAA,KAAaJ,IAAI,CAAE;QAEtC;QAAA;QAAAd,aAAA,GAAAC,CAAA;QACA;QAAI;QAAA,CAAAD,aAAA,GAAAQ,CAAA,YAACM,IAAI;QAAA;QAAA,CAAAd,aAAA,GAAAQ,CAAA,WAAI,OAAOM,IAAI,KAAK,QAAQ,GAAE;UAAA;UAAAd,aAAA,GAAAQ,CAAA;UAAAR,aAAA,GAAAC,CAAA;UACrC,sBAAO;YACLkB,OAAO,EAAE,KAAK;YACdH,MAAM,EAAE,CAAC,iCAAiC;WAC3C;QACH,CAAC;QAAA;QAAA;UAAAhB,aAAA,GAAAQ,CAAA;QAAA;QAAAR,aAAA,GAAAC,CAAA;4BAGUU,IAAI;UAAA;UAAAX,aAAA,GAAAM,CAAA;UACb,IAAMc,UAAU;UAAA;UAAA,CAAApB,aAAA,GAAAC,CAAA,QAAGa,IAAI,CAACH,IAAI,CAACU,KAAK,CAAC;UAEnC;UAAA;UAAArB,aAAA,GAAAC,CAAA;UACA;UAAI;UAAA,CAAAD,aAAA,GAAAQ,CAAA,WAAAG,IAAI,CAACW,QAAQ;UAAK;UAAA,CAAAtB,aAAA,GAAAQ,CAAA,WAAAY,UAAU,KAAKG,SAAS;UAAA;UAAA,CAAAvB,aAAA,GAAAQ,CAAA,WAAIY,UAAU,KAAK,IAAI;UAAA;UAAA,CAAApB,aAAA,GAAAQ,CAAA,WAAIY,UAAU,KAAK,EAAE,EAAC,EAAE;YAAA;YAAApB,aAAA,GAAAQ,CAAA;YAAAR,aAAA,GAAAC,CAAA;YAC3Fe,MAAM,CAACJ,IAAI,CAAC,GAAAY,MAAA,CAAGb,IAAI,CAACU,KAAK,iBAAc,CAAC;YAAC;YAAArB,aAAA,GAAAC,CAAA;;UAE3C,CAAC;UAAA;UAAA;YAAAD,aAAA,GAAAQ,CAAA;UAAA;UAED;UAAAR,aAAA,GAAAC,CAAA;UACA;UAAI;UAAA,CAAAD,aAAA,GAAAQ,CAAA,YAACG,IAAI,CAACW,QAAQ;UAAK;UAAA,CAAAtB,aAAA,GAAAQ,CAAA,WAAAY,UAAU,KAAKG,SAAS;UAAA;UAAA,CAAAvB,aAAA,GAAAQ,CAAA,WAAIY,UAAU,KAAK,IAAI;UAAA;UAAA,CAAApB,aAAA,GAAAQ,CAAA,WAAIY,UAAU,KAAK,EAAE,EAAC,EAAE;YAAA;YAAApB,aAAA,GAAAQ,CAAA;YAAAR,aAAA,GAAAC,CAAA;;UAE9F,CAAC;UAAA;UAAA;YAAAD,aAAA,GAAAQ,CAAA;UAAA;UAED;UAAAR,aAAA,GAAAC,CAAA;UACA,IAAI;YACF,IAAMwB,cAAc;YAAA;YAAA,CAAAzB,aAAA,GAAAC,CAAA,SAAGU,IAAI,CAACe,MAAM,CAACC,KAAK,CAACP,UAAU,CAAC;YAEpD;YAAA;YAAApB,aAAA,GAAAC,CAAA;YACA;YAAI;YAAA,CAAAD,aAAA,GAAAQ,CAAA,WAAAG,IAAI,CAACiB,QAAQ;YAAA;YAAA,CAAA5B,aAAA,GAAAQ,CAAA,WAAI,OAAOiB,cAAc,KAAK,QAAQ,GAAE;cAAA;cAAAzB,aAAA,GAAAQ,CAAA;cAAAR,aAAA,GAAAC,CAAA;cACvDgB,aAAa,CAACN,IAAI,CAACU,KAAK,CAAC,GAAGQ,MAAA,CAAKC,cAAc,CAACL,cAAc,CAAC;YACjE,CAAC,MAAM;cAAA;cAAAzB,aAAA,GAAAQ,CAAA;cAAAR,aAAA,GAAAC,CAAA;cACLgB,aAAa,CAACN,IAAI,CAACU,KAAK,CAAC,GAAGI,cAAc;YAC5C;UACF,CAAC,CAAC,OAAOM,KAAK,EAAE;YAAA;YAAA/B,aAAA,GAAAC,CAAA;YACd,IAAI8B,KAAK,YAAYhC,KAAA,CAAAiC,CAAC,CAACC,QAAQ,EAAE;cAAA;cAAAjC,aAAA,GAAAQ,CAAA;cAAAR,aAAA,GAAAC,CAAA;cAC/Be,MAAM,CAACJ,IAAI,CAAAsB,KAAA,CAAXlB,MAAM,EAASe,KAAK,CAACf,MAAM,CAACmB,GAAG,CAAC,UAAAC,GAAG;gBAAA;gBAAApC,aAAA,GAAAM,CAAA;gBAAAN,aAAA,GAAAC,CAAA;gBAAI,UAAAuB,MAAA,CAAGb,IAAI,CAACU,KAAK,QAAAG,MAAA,CAAKY,GAAG,CAACC,OAAO,CAAE;cAA/B,CAA+B,CAAC;YACzE,CAAC,MAAM;cAAA;cAAArC,aAAA,GAAAQ,CAAA;cAAAR,aAAA,GAAAC,CAAA;cACLe,MAAM,CAACJ,IAAI,CAAC,GAAAY,MAAA,CAAGb,IAAI,CAACU,KAAK,wBAAqB,CAAC;YACjD;UACF;;;;;QA/BF;QAAA;QAAArB,aAAA,GAAAC,CAAA;QACA,KAAAqC,EAAA,IAA6B,EAAVC,EAAA,OAAI,CAAChC,KAAK,EAAV+B,EAAA,GAAAC,EAAA,CAAAC,MAAU,EAAVF,EAAA,EAAU;UAAA;UAAAtC,aAAA,GAAAC,CAAA;UAAlBU,IAAI,GAAA4B,EAAA,CAAAD,EAAA;UAAA;UAAAtC,aAAA,GAAAC,CAAA;kBAAJU,IAAI;;QA+Bd;QAAAX,aAAA,GAAAC,CAAA;QAGKwC,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAACzB,aAAa,CAAC;QAAC;QAAAjB,aAAA,GAAAC,CAAA;QACjEe,MAAM,CAACJ,IAAI,CAAAsB,KAAA,CAAXlB,MAAM,EAASyB,cAAc;QAAE;QAAAzC,aAAA,GAAAC,CAAA;QAE/B,sBAAO;UACLkB,OAAO,EAAEH,MAAM,CAACwB,MAAM,KAAK,CAAC;UAC5BxB,MAAM,EAAAA,MAAA;UACNC,aAAa,EAAED,MAAM,CAACwB,MAAM,KAAK,CAAC;UAAA;UAAA,CAAAxC,aAAA,GAAAQ,CAAA,WAAGS,aAAa;UAAA;UAAA,CAAAjB,aAAA,GAAAQ,CAAA,WAAGe,SAAS;SAC/D;;;GACF;EAED;;;EAAA;EAAAvB,aAAA,GAAAC,CAAA;EAGQI,sBAAA,CAAAI,SAAA,CAAAqB,cAAc,GAAtB,UAAuBa,KAAa;IAAA;IAAA3C,aAAA,GAAAM,CAAA;IAClC;IACA,IAAMsC,OAAO;IAAA;IAAA,CAAA5C,aAAA,GAAAC,CAAA,SAAGE,sBAAA,CAAA0C,OAAS,CAACjB,QAAQ,CAACe,KAAK,EAAE;MACxCG,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE;KACf,CAAC;IAEF;IAAA;IAAA/C,aAAA,GAAAC,CAAA;IACA,OAAO2C,OAAO,CACXI,OAAO,CAAC,qDAAqD,EAAE,EAAE,CAAC,CAAC;IAAA,CACnEA,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;IAAA,CAC7BA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;IAAA,CAC3BC,IAAI,EAAE;EACX,CAAC;EAED;;;EAAA;EAAAjD,aAAA,GAAAC,CAAA;EAGQI,sBAAA,CAAAI,SAAA,CAAAiC,qBAAqB,GAA7B,UAA8B5B,IAAS;IAAA;IAAAd,aAAA,GAAAM,CAAA;IACrC,IAAMU,MAAM;IAAA;IAAA,CAAAhB,aAAA,GAAAC,CAAA,SAAa,EAAE;IAE3B;IACA,IAAMiD,WAAW;IAAA;IAAA,CAAAlD,aAAA,GAAAC,CAAA,SAAG,CAClB,mEAAmE,EACnE,kBAAkB,EAClB,sBAAsB,CACvB;IAED;IACA,IAAMkD,WAAW;IAAA;IAAA,CAAAnD,aAAA,GAAAC,CAAA,SAAG,CAClB,qDAAqD,EACrD,eAAe,EACf,aAAa,EACb,WAAW,EACX,WAAW,EACX,UAAU,CACX;IAED;IAAA;IAAAD,aAAA,GAAAC,CAAA;IACA,IAAI,CAACmD,sBAAsB,CAACtC,IAAI,EAAEoC,WAAW,EAAE,kCAAkC,EAAElC,MAAM,CAAC;IAAC;IAAAhB,aAAA,GAAAC,CAAA;IAC3F,IAAI,CAACmD,sBAAsB,CAACtC,IAAI,EAAEqC,WAAW,EAAE,+BAA+B,EAAEnC,MAAM,CAAC;IAAC;IAAAhB,aAAA,GAAAC,CAAA;IAExF,OAAOe,MAAM;EACf,CAAC;EAED;;;EAAA;EAAAhB,aAAA,GAAAC,CAAA;EAGQI,sBAAA,CAAAI,SAAA,CAAA2C,sBAAsB,GAA9B,UAA+BC,GAAQ,EAAEC,QAAkB,EAAEC,YAAoB,EAAEvC,MAAgB;IAAA;IAAAhB,aAAA,GAAAM,CAAA;IAAnG,IAAAkD,KAAA;IAAA;IAAA,CAAAxD,aAAA,GAAAC,CAAA;IAaC;IAAAD,aAAA,GAAAC,CAAA;IAZC,IAAI,OAAOoD,GAAG,KAAK,QAAQ,EAAE;MAAA;MAAArD,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAC,CAAA;MAC3B,KAAsB,IAAAqC,EAAA;QAAA;QAAA,CAAAtC,aAAA,GAAAC,CAAA,UAAQ,GAARwD,UAAA;QAAA;QAAA,CAAAzD,aAAA,GAAAC,CAAA,SAAAqD,QAAQ,GAARhB,EAAA,GAAAmB,UAAA,CAAAjB,MAAQ,EAARF,EAAA,EAAQ,EAAE;QAA3B,IAAMoB,OAAO;QAAA;QAAA,CAAA1D,aAAA,GAAAC,CAAA,SAAAwD,UAAA,CAAAnB,EAAA;QAAA;QAAAtC,aAAA,GAAAC,CAAA;QAChB,IAAIyD,OAAO,CAACC,IAAI,CAACN,GAAG,CAAC,EAAE;UAAA;UAAArD,aAAA,GAAAQ,CAAA;UAAAR,aAAA,GAAAC,CAAA;UACrBe,MAAM,CAACJ,IAAI,CAAC2C,YAAY,CAAC;UAAC;UAAAvD,aAAA,GAAAC,CAAA;UAC1B;QACF,CAAC;QAAA;QAAA;UAAAD,aAAA,GAAAQ,CAAA;QAAA;MACH;IACF,CAAC,MAAM;MAAA;MAAAR,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAC,CAAA;MAAA,IAAI2D,KAAK,CAACC,OAAO,CAACR,GAAG,CAAC,EAAE;QAAA;QAAArD,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAC,CAAA;QAC7BoD,GAAG,CAACS,OAAO,CAAC,UAAAC,IAAI;UAAA;UAAA/D,aAAA,GAAAM,CAAA;UAAAN,aAAA,GAAAC,CAAA;UAAI,OAAAuD,KAAI,CAACJ,sBAAsB,CAACW,IAAI,EAAET,QAAQ,EAAEC,YAAY,EAAEvC,MAAM,CAAC;QAAjE,CAAiE,CAAC;MACxF,CAAC,MAAM;QAAA;QAAAhB,aAAA,GAAAQ,CAAA;QAAAR,aAAA,GAAAC,CAAA;QAAA;QAAI;QAAA,CAAAD,aAAA,GAAAQ,CAAA,WAAA6C,GAAG;QAAA;QAAA,CAAArD,aAAA,GAAAQ,CAAA,WAAI,OAAO6C,GAAG,KAAK,QAAQ,GAAE;UAAA;UAAArD,aAAA,GAAAQ,CAAA;UAAAR,aAAA,GAAAC,CAAA;UACzC+D,MAAM,CAACC,MAAM,CAACZ,GAAG,CAAC,CAACS,OAAO,CAAC,UAAAI,KAAK;YAAA;YAAAlE,aAAA,GAAAM,CAAA;YAAAN,aAAA,GAAAC,CAAA;YAAI,OAAAuD,KAAI,CAACJ,sBAAsB,CAACc,KAAK,EAAEZ,QAAQ,EAAEC,YAAY,EAAEvC,MAAM,CAAC;UAAlE,CAAkE,CAAC;QACzG,CAAC;QAAA;QAAA;UAAAhB,aAAA,GAAAQ,CAAA;QAAA;MAAD;IAAA;EACF,CAAC;EAAA;EAAAR,aAAA,GAAAC,CAAA;EACH,OAAAI,sBAAC;AAAD,CAAC,CAxID;AAwIC;AAAAL,aAAA,GAAAC,CAAA;AAxIYkE,OAAA,CAAA9D,sBAAA,GAAAA,sBAAA;AA0Ib;;;AAGA,IAAA+D,mBAAA;AAAA;AAAA,cAAApE,aAAA,GAAAC,CAAA;EAAA;EAAAD,aAAA,GAAAM,CAAA;EAAA,SAAA8D,oBAAA;IAAA;IAAApE,aAAA,GAAAM,CAAA;EAqHA;EAAC;EAAAN,aAAA,GAAAC,CAAA;EApHQmE,mBAAA,CAAAC,0BAA0B,GAAjC;IAAA;IAAArE,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAC,CAAA;IACE,OAAO,IAAII,sBAAsB,CAAC,CAChC;MACEgB,KAAK,EAAE,WAAW;MAClBK,MAAM,EAAE3B,KAAA,CAAAiC,CAAC,CAACsC,MAAM,EAAE,CACfC,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC,CAChCC,GAAG,CAAC,EAAE,EAAE,4CAA4C,CAAC,CACrDC,KAAK,CAAC,iBAAiB,EAAE,uEAAuE,CAAC;MACpG7C,QAAQ,EAAE,IAAI;MACdN,QAAQ,EAAE;KACX,EACD;MACED,KAAK,EAAE,UAAU;MACjBK,MAAM,EAAE3B,KAAA,CAAAiC,CAAC,CAACsC,MAAM,EAAE,CACfC,GAAG,CAAC,CAAC,EAAE,uBAAuB,CAAC,CAC/BC,GAAG,CAAC,EAAE,EAAE,2CAA2C,CAAC,CACpDC,KAAK,CAAC,iBAAiB,EAAE,sEAAsE,CAAC;MACnG7C,QAAQ,EAAE,IAAI;MACdN,QAAQ,EAAE;KACX,EACD;MACED,KAAK,EAAE,OAAO;MACdK,MAAM,EAAE3B,KAAA,CAAAiC,CAAC,CAACsC,MAAM,EAAE,CACfI,KAAK,CAAC,oCAAoC,CAAC,CAC3CF,GAAG,CAAC,GAAG,EAAE,mBAAmB,CAAC;MAChC5C,QAAQ,EAAE,IAAI;MACdN,QAAQ,EAAE;KACX,EACD;MACED,KAAK,EAAE,OAAO;MACdK,MAAM,EAAE3B,KAAA,CAAAiC,CAAC,CAACsC,MAAM,EAAE,CACfE,GAAG,CAAC,EAAE,EAAE,0BAA0B,CAAC,CACnCC,KAAK,CAAC,gCAAgC,EAAE,mCAAmC,CAAC;MAC/E7C,QAAQ,EAAE,IAAI;MACdN,QAAQ,EAAE;KACX,EACD;MACED,KAAK,EAAE,UAAU;MACjBK,MAAM,EAAE3B,KAAA,CAAAiC,CAAC,CAACsC,MAAM,EAAE,CAACE,GAAG,CAAC,GAAG,EAAE,2CAA2C,CAAC;MACxE5C,QAAQ,EAAE,IAAI;MACdN,QAAQ,EAAE;KACX,EACD;MACED,KAAK,EAAE,SAAS;MAChBK,MAAM,EAAE3B,KAAA,CAAAiC,CAAC,CAACsC,MAAM,EAAE,CACfK,GAAG,CAAC,kCAAkC,CAAC,CACvCH,GAAG,CAAC,GAAG,EAAE,yBAAyB,CAAC;MACtC5C,QAAQ,EAAE,IAAI;MACdN,QAAQ,EAAE;KACX,EACD;MACED,KAAK,EAAE,UAAU;MACjBK,MAAM,EAAE3B,KAAA,CAAAiC,CAAC,CAACsC,MAAM,EAAE,CACfK,GAAG,CAAC,mCAAmC,CAAC,CACxCH,GAAG,CAAC,GAAG,EAAE,0BAA0B,CAAC;MACvC5C,QAAQ,EAAE,IAAI;MACdN,QAAQ,EAAE;KACX,CACF,CAAC;EACJ,CAAC;EAAA;EAAAtB,aAAA,GAAAC,CAAA;EAEMmE,mBAAA,CAAAQ,oBAAoB,GAA3B;IAAA;IAAA5E,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAC,CAAA;IACE,OAAO,IAAII,sBAAsB,CAAC,CAChC;MACEgB,KAAK,EAAE,OAAO;MACdK,MAAM,EAAE3B,KAAA,CAAAiC,CAAC,CAACsC,MAAM,EAAE,CACfC,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC,CAClCC,GAAG,CAAC,GAAG,EAAE,+CAA+C,CAAC;MAC5D5C,QAAQ,EAAE,IAAI;MACdN,QAAQ,EAAE;KACX,EACD;MACED,KAAK,EAAE,SAAS;MAChBK,MAAM,EAAE3B,KAAA,CAAAiC,CAAC,CAACsC,MAAM,EAAE,CACfE,GAAG,CAAC,IAAI,EAAE,2CAA2C,CAAC;MACzD5C,QAAQ,EAAE,IAAI;MACdN,QAAQ,EAAE;KACX,EACD;MACED,KAAK,EAAE,UAAU;MACjBK,MAAM,EAAE3B,KAAA,CAAAiC,CAAC,CAACsC,MAAM,EAAE,CACfE,GAAG,CAAC,EAAE,EAAE,2BAA2B,CAAC;MACvC5C,QAAQ,EAAE,IAAI;MACdN,QAAQ,EAAE;KACX,CACF,CAAC;EACJ,CAAC;EAAA;EAAAtB,aAAA,GAAAC,CAAA;EAEMmE,mBAAA,CAAAS,kBAAkB,GAAzB;IAAA;IAAA7E,aAAA,GAAAM,CAAA;IAAAN,aAAA,GAAAC,CAAA;IACE,OAAO,IAAII,sBAAsB,CAAC,CAChC;MACEgB,KAAK,EAAE,OAAO;MACdK,MAAM,EAAE3B,KAAA,CAAAiC,CAAC,CAACsC,MAAM,EAAE,CACfI,KAAK,CAAC,oCAAoC,CAAC,CAC3CF,GAAG,CAAC,GAAG,EAAE,mBAAmB,CAAC;MAChC5C,QAAQ,EAAE,IAAI;MACdN,QAAQ,EAAE;KACX,EACD;MACED,KAAK,EAAE,UAAU;MACjBK,MAAM,EAAE3B,KAAA,CAAAiC,CAAC,CAACsC,MAAM,EAAE,CACfC,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC,CAChDC,GAAG,CAAC,GAAG,EAAE,sBAAsB,CAAC,CAChCC,KAAK,CAAC,iCAAiC,EAAE,2FAA2F,CAAC;MACxI7C,QAAQ,EAAE,KAAK;MAAE;MACjBN,QAAQ,EAAE;KACX,EACD;MACED,KAAK,EAAE,MAAM;MACbK,MAAM,EAAE3B,KAAA,CAAAiC,CAAC,CAACsC,MAAM,EAAE,CACfE,GAAG,CAAC,GAAG,EAAE,uCAAuC,CAAC;MACpD5C,QAAQ,EAAE,IAAI;MACdN,QAAQ,EAAE;KACX,CACF,CAAC;EACJ,CAAC;EAAA;EAAAtB,aAAA,GAAAC,CAAA;EACH,OAAAmE,mBAAC;AAAD,CAAC,CArHD;AAqHC;AAAApE,aAAA,GAAAC,CAAA;AArHYkE,OAAA,CAAAC,mBAAA,GAAAA,mBAAA", "ignoreList": []}