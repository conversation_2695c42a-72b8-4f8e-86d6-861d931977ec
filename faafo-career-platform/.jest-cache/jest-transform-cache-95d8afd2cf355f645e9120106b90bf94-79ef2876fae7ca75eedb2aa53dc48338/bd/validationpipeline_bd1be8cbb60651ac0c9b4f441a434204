6aab6a34ea1f6c984fe77b1f5c47a08a
"use strict";

/* istanbul ignore next */
function cov_k06ael1tm() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/validation-pipeline.ts";
  var hash = "200d69141f38a96f851b6d9af63e71d977d4bda8";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/validation-pipeline.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 22
        },
        end: {
          line: 51,
          column: 1
        }
      },
      "79": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 50,
          column: 62
        }
      },
      "80": {
        start: {
          line: 52,
          column: 0
        },
        end: {
          line: 52,
          column: 62
        }
      },
      "81": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 70
        }
      },
      "82": {
        start: {
          line: 54,
          column: 12
        },
        end: {
          line: 54,
          column: 26
        }
      },
      "83": {
        start: {
          line: 55,
          column: 29
        },
        end: {
          line: 55,
          column: 77
        }
      },
      "84": {
        start: {
          line: 56,
          column: 44
        },
        end: {
          line: 191,
          column: 3
        }
      },
      "85": {
        start: {
          line: 58,
          column: 8
        },
        end: {
          line: 58,
          column: 45
        }
      },
      "86": {
        start: {
          line: 58,
          column: 32
        },
        end: {
          line: 58,
          column: 43
        }
      },
      "87": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 59,
          column: 24
        }
      },
      "88": {
        start: {
          line: 60,
          column: 8
        },
        end: {
          line: 60,
          column: 27
        }
      },
      "89": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 64,
          column: 6
        }
      },
      "90": {
        start: {
          line: 63,
          column: 8
        },
        end: {
          line: 63,
          column: 30
        }
      },
      "91": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 127,
          column: 6
        }
      },
      "92": {
        start: {
          line: 69,
          column: 8
        },
        end: {
          line: 126,
          column: 11
        }
      },
      "93": {
        start: {
          line: 71,
          column: 12
        },
        end: {
          line: 125,
          column: 15
        }
      },
      "94": {
        start: {
          line: 72,
          column: 16
        },
        end: {
          line: 72,
          column: 28
        }
      },
      "95": {
        start: {
          line: 73,
          column: 16
        },
        end: {
          line: 73,
          column: 51
        }
      },
      "96": {
        start: {
          line: 75,
          column: 16
        },
        end: {
          line: 80,
          column: 17
        }
      },
      "97": {
        start: {
          line: 76,
          column: 20
        },
        end: {
          line: 79,
          column: 27
        }
      },
      "98": {
        start: {
          line: 81,
          column: 16
        },
        end: {
          line: 111,
          column: 18
        }
      },
      "99": {
        start: {
          line: 82,
          column: 37
        },
        end: {
          line: 82,
          column: 53
        }
      },
      "100": {
        start: {
          line: 84,
          column: 20
        },
        end: {
          line: 87,
          column: 21
        }
      },
      "101": {
        start: {
          line: 85,
          column: 24
        },
        end: {
          line: 85,
          column: 75
        }
      },
      "102": {
        start: {
          line: 86,
          column: 24
        },
        end: {
          line: 86,
          column: 42
        }
      },
      "103": {
        start: {
          line: 89,
          column: 20
        },
        end: {
          line: 91,
          column: 21
        }
      },
      "104": {
        start: {
          line: 90,
          column: 24
        },
        end: {
          line: 90,
          column: 42
        }
      },
      "105": {
        start: {
          line: 93,
          column: 20
        },
        end: {
          line: 110,
          column: 21
        }
      },
      "106": {
        start: {
          line: 94,
          column: 45
        },
        end: {
          line: 94,
          column: 74
        }
      },
      "107": {
        start: {
          line: 96,
          column: 24
        },
        end: {
          line: 101,
          column: 25
        }
      },
      "108": {
        start: {
          line: 97,
          column: 28
        },
        end: {
          line: 97,
          column: 94
        }
      },
      "109": {
        start: {
          line: 100,
          column: 28
        },
        end: {
          line: 100,
          column: 71
        }
      },
      "110": {
        start: {
          line: 104,
          column: 24
        },
        end: {
          line: 109,
          column: 25
        }
      },
      "111": {
        start: {
          line: 105,
          column: 28
        },
        end: {
          line: 105,
          column: 148
        }
      },
      "112": {
        start: {
          line: 105,
          column: 88
        },
        end: {
          line: 105,
          column: 143
        }
      },
      "113": {
        start: {
          line: 108,
          column: 28
        },
        end: {
          line: 108,
          column: 86
        }
      },
      "114": {
        start: {
          line: 112,
          column: 16
        },
        end: {
          line: 112,
          column: 30
        }
      },
      "115": {
        start: {
          line: 114,
          column: 16
        },
        end: {
          line: 117,
          column: 17
        }
      },
      "116": {
        start: {
          line: 115,
          column: 20
        },
        end: {
          line: 115,
          column: 34
        }
      },
      "117": {
        start: {
          line: 116,
          column: 20
        },
        end: {
          line: 116,
          column: 34
        }
      },
      "118": {
        start: {
          line: 118,
          column: 16
        },
        end: {
          line: 118,
          column: 75
        }
      },
      "119": {
        start: {
          line: 119,
          column: 16
        },
        end: {
          line: 119,
          column: 58
        }
      },
      "120": {
        start: {
          line: 120,
          column: 16
        },
        end: {
          line: 124,
          column: 23
        }
      },
      "121": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 143,
          column: 6
        }
      },
      "122": {
        start: {
          line: 133,
          column: 22
        },
        end: {
          line: 136,
          column: 10
        }
      },
      "123": {
        start: {
          line: 138,
          column: 8
        },
        end: {
          line: 142,
          column: 20
        }
      },
      "124": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 168,
          column: 6
        }
      },
      "125": {
        start: {
          line: 148,
          column: 21
        },
        end: {
          line: 148,
          column: 23
        }
      },
      "126": {
        start: {
          line: 150,
          column: 26
        },
        end: {
          line: 154,
          column: 9
        }
      },
      "127": {
        start: {
          line: 156,
          column: 26
        },
        end: {
          line: 163,
          column: 9
        }
      },
      "128": {
        start: {
          line: 165,
          column: 8
        },
        end: {
          line: 165,
          column: 99
        }
      },
      "129": {
        start: {
          line: 166,
          column: 8
        },
        end: {
          line: 166,
          column: 96
        }
      },
      "130": {
        start: {
          line: 167,
          column: 8
        },
        end: {
          line: 167,
          column: 22
        }
      },
      "131": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 189,
          column: 6
        }
      },
      "132": {
        start: {
          line: 173,
          column: 20
        },
        end: {
          line: 173,
          column: 24
        }
      },
      "133": {
        start: {
          line: 174,
          column: 8
        },
        end: {
          line: 188,
          column: 9
        }
      },
      "134": {
        start: {
          line: 175,
          column: 12
        },
        end: {
          line: 181,
          column: 13
        }
      },
      "135": {
        start: {
          line: 175,
          column: 26
        },
        end: {
          line: 175,
          column: 27
        }
      },
      "136": {
        start: {
          line: 175,
          column: 42
        },
        end: {
          line: 175,
          column: 50
        }
      },
      "137": {
        start: {
          line: 176,
          column: 30
        },
        end: {
          line: 176,
          column: 44
        }
      },
      "138": {
        start: {
          line: 177,
          column: 16
        },
        end: {
          line: 180,
          column: 17
        }
      },
      "139": {
        start: {
          line: 178,
          column: 20
        },
        end: {
          line: 178,
          column: 46
        }
      },
      "140": {
        start: {
          line: 179,
          column: 20
        },
        end: {
          line: 179,
          column: 26
        }
      },
      "141": {
        start: {
          line: 183,
          column: 13
        },
        end: {
          line: 188,
          column: 9
        }
      },
      "142": {
        start: {
          line: 184,
          column: 12
        },
        end: {
          line: 184,
          column: 120
        }
      },
      "143": {
        start: {
          line: 184,
          column: 42
        },
        end: {
          line: 184,
          column: 116
        }
      },
      "144": {
        start: {
          line: 186,
          column: 13
        },
        end: {
          line: 188,
          column: 9
        }
      },
      "145": {
        start: {
          line: 187,
          column: 12
        },
        end: {
          line: 187,
          column: 137
        }
      },
      "146": {
        start: {
          line: 187,
          column: 58
        },
        end: {
          line: 187,
          column: 133
        }
      },
      "147": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 190,
          column: 34
        }
      },
      "148": {
        start: {
          line: 192,
          column: 0
        },
        end: {
          line: 192,
          column: 56
        }
      },
      "149": {
        start: {
          line: 196,
          column: 41
        },
        end: {
          line: 314,
          column: 3
        }
      },
      "150": {
        start: {
          line: 199,
          column: 4
        },
        end: {
          line: 258,
          column: 6
        }
      },
      "151": {
        start: {
          line: 200,
          column: 8
        },
        end: {
          line: 257,
          column: 11
        }
      },
      "152": {
        start: {
          line: 259,
          column: 4
        },
        end: {
          line: 284,
          column: 6
        }
      },
      "153": {
        start: {
          line: 260,
          column: 8
        },
        end: {
          line: 283,
          column: 11
        }
      },
      "154": {
        start: {
          line: 285,
          column: 4
        },
        end: {
          line: 312,
          column: 6
        }
      },
      "155": {
        start: {
          line: 286,
          column: 8
        },
        end: {
          line: 311,
          column: 11
        }
      },
      "156": {
        start: {
          line: 313,
          column: 4
        },
        end: {
          line: 313,
          column: 31
        }
      },
      "157": {
        start: {
          line: 315,
          column: 0
        },
        end: {
          line: 315,
          column: 50
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 49,
            column: 57
          }
        },
        loc: {
          start: {
            line: 49,
            column: 71
          },
          end: {
            line: 51,
            column: 1
          }
        },
        line: 49
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 56,
            column: 44
          },
          end: {
            line: 56,
            column: 45
          }
        },
        loc: {
          start: {
            line: 56,
            column: 56
          },
          end: {
            line: 191,
            column: 1
          }
        },
        line: 56
      },
      "17": {
        name: "DataValidationPipeline",
        decl: {
          start: {
            line: 57,
            column: 13
          },
          end: {
            line: 57,
            column: 35
          }
        },
        loc: {
          start: {
            line: 57,
            column: 43
          },
          end: {
            line: 61,
            column: 5
          }
        },
        line: 57
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 62,
            column: 47
          },
          end: {
            line: 62,
            column: 48
          }
        },
        loc: {
          start: {
            line: 62,
            column: 63
          },
          end: {
            line: 64,
            column: 5
          }
        },
        line: 62
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 68,
            column: 48
          },
          end: {
            line: 68,
            column: 49
          }
        },
        loc: {
          start: {
            line: 68,
            column: 64
          },
          end: {
            line: 127,
            column: 5
          }
        },
        line: 68
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 69,
            column: 48
          },
          end: {
            line: 69,
            column: 49
          }
        },
        loc: {
          start: {
            line: 69,
            column: 60
          },
          end: {
            line: 126,
            column: 9
          }
        },
        line: 69
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 71,
            column: 37
          },
          end: {
            line: 71,
            column: 38
          }
        },
        loc: {
          start: {
            line: 71,
            column: 51
          },
          end: {
            line: 125,
            column: 13
          }
        },
        line: 71
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 81,
            column: 26
          },
          end: {
            line: 81,
            column: 27
          }
        },
        loc: {
          start: {
            line: 81,
            column: 42
          },
          end: {
            line: 111,
            column: 17
          }
        },
        line: 81
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 105,
            column: 71
          },
          end: {
            line: 105,
            column: 72
          }
        },
        loc: {
          start: {
            line: 105,
            column: 86
          },
          end: {
            line: 105,
            column: 145
          }
        },
        line: 105
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 131,
            column: 54
          },
          end: {
            line: 131,
            column: 55
          }
        },
        loc: {
          start: {
            line: 131,
            column: 71
          },
          end: {
            line: 143,
            column: 5
          }
        },
        line: 131
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 147,
            column: 61
          },
          end: {
            line: 147,
            column: 62
          }
        },
        loc: {
          start: {
            line: 147,
            column: 77
          },
          end: {
            line: 168,
            column: 5
          }
        },
        line: 147
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 172,
            column: 62
          },
          end: {
            line: 172,
            column: 63
          }
        },
        loc: {
          start: {
            line: 172,
            column: 109
          },
          end: {
            line: 189,
            column: 5
          }
        },
        line: 172
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 184,
            column: 24
          },
          end: {
            line: 184,
            column: 25
          }
        },
        loc: {
          start: {
            line: 184,
            column: 40
          },
          end: {
            line: 184,
            column: 118
          }
        },
        line: 184
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 187,
            column: 39
          },
          end: {
            line: 187,
            column: 40
          }
        },
        loc: {
          start: {
            line: 187,
            column: 56
          },
          end: {
            line: 187,
            column: 135
          }
        },
        line: 187
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 196,
            column: 41
          },
          end: {
            line: 196,
            column: 42
          }
        },
        loc: {
          start: {
            line: 196,
            column: 53
          },
          end: {
            line: 314,
            column: 1
          }
        },
        line: 196
      },
      "30": {
        name: "ValidationPipelines",
        decl: {
          start: {
            line: 197,
            column: 13
          },
          end: {
            line: 197,
            column: 32
          }
        },
        loc: {
          start: {
            line: 197,
            column: 35
          },
          end: {
            line: 198,
            column: 5
          }
        },
        line: 197
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 199,
            column: 53
          },
          end: {
            line: 199,
            column: 54
          }
        },
        loc: {
          start: {
            line: 199,
            column: 65
          },
          end: {
            line: 258,
            column: 5
          }
        },
        line: 199
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 259,
            column: 47
          },
          end: {
            line: 259,
            column: 48
          }
        },
        loc: {
          start: {
            line: 259,
            column: 59
          },
          end: {
            line: 284,
            column: 5
          }
        },
        line: 259
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 285,
            column: 45
          },
          end: {
            line: 285,
            column: 46
          }
        },
        loc: {
          start: {
            line: 285,
            column: 57
          },
          end: {
            line: 312,
            column: 5
          }
        },
        line: 285
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 49,
            column: 22
          },
          end: {
            line: 51,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 23
          },
          end: {
            line: 49,
            column: 27
          }
        }, {
          start: {
            line: 49,
            column: 31
          },
          end: {
            line: 49,
            column: 51
          }
        }, {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 51,
            column: 1
          }
        }],
        line: 49
      },
      "36": {
        loc: {
          start: {
            line: 50,
            column: 11
          },
          end: {
            line: 50,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 50,
            column: 37
          },
          end: {
            line: 50,
            column: 40
          }
        }, {
          start: {
            line: 50,
            column: 43
          },
          end: {
            line: 50,
            column: 61
          }
        }],
        line: 50
      },
      "37": {
        loc: {
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 15
          }
        }, {
          start: {
            line: 50,
            column: 19
          },
          end: {
            line: 50,
            column: 33
          }
        }],
        line: 50
      },
      "38": {
        loc: {
          start: {
            line: 58,
            column: 8
          },
          end: {
            line: 58,
            column: 45
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 58,
            column: 8
          },
          end: {
            line: 58,
            column: 45
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 58
      },
      "39": {
        loc: {
          start: {
            line: 75,
            column: 16
          },
          end: {
            line: 80,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 75,
            column: 16
          },
          end: {
            line: 80,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 75
      },
      "40": {
        loc: {
          start: {
            line: 75,
            column: 20
          },
          end: {
            line: 75,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 75,
            column: 20
          },
          end: {
            line: 75,
            column: 25
          }
        }, {
          start: {
            line: 75,
            column: 29
          },
          end: {
            line: 75,
            column: 53
          }
        }],
        line: 75
      },
      "41": {
        loc: {
          start: {
            line: 84,
            column: 20
          },
          end: {
            line: 87,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 84,
            column: 20
          },
          end: {
            line: 87,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 84
      },
      "42": {
        loc: {
          start: {
            line: 84,
            column: 24
          },
          end: {
            line: 84,
            column: 111
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 84,
            column: 24
          },
          end: {
            line: 84,
            column: 37
          }
        }, {
          start: {
            line: 84,
            column: 42
          },
          end: {
            line: 84,
            column: 66
          }
        }, {
          start: {
            line: 84,
            column: 70
          },
          end: {
            line: 84,
            column: 89
          }
        }, {
          start: {
            line: 84,
            column: 93
          },
          end: {
            line: 84,
            column: 110
          }
        }],
        line: 84
      },
      "43": {
        loc: {
          start: {
            line: 89,
            column: 20
          },
          end: {
            line: 91,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 89,
            column: 20
          },
          end: {
            line: 91,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 89
      },
      "44": {
        loc: {
          start: {
            line: 89,
            column: 24
          },
          end: {
            line: 89,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 89,
            column: 24
          },
          end: {
            line: 89,
            column: 38
          }
        }, {
          start: {
            line: 89,
            column: 43
          },
          end: {
            line: 89,
            column: 67
          }
        }, {
          start: {
            line: 89,
            column: 71
          },
          end: {
            line: 89,
            column: 90
          }
        }, {
          start: {
            line: 89,
            column: 94
          },
          end: {
            line: 89,
            column: 111
          }
        }],
        line: 89
      },
      "45": {
        loc: {
          start: {
            line: 96,
            column: 24
          },
          end: {
            line: 101,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 96,
            column: 24
          },
          end: {
            line: 101,
            column: 25
          }
        }, {
          start: {
            line: 99,
            column: 29
          },
          end: {
            line: 101,
            column: 25
          }
        }],
        line: 96
      },
      "46": {
        loc: {
          start: {
            line: 96,
            column: 28
          },
          end: {
            line: 96,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 96,
            column: 28
          },
          end: {
            line: 96,
            column: 41
          }
        }, {
          start: {
            line: 96,
            column: 45
          },
          end: {
            line: 96,
            column: 79
          }
        }],
        line: 96
      },
      "47": {
        loc: {
          start: {
            line: 104,
            column: 24
          },
          end: {
            line: 109,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 104,
            column: 24
          },
          end: {
            line: 109,
            column: 25
          }
        }, {
          start: {
            line: 107,
            column: 29
          },
          end: {
            line: 109,
            column: 25
          }
        }],
        line: 104
      },
      "48": {
        loc: {
          start: {
            line: 123,
            column: 39
          },
          end: {
            line: 123,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 123,
            column: 61
          },
          end: {
            line: 123,
            column: 74
          }
        }, {
          start: {
            line: 123,
            column: 77
          },
          end: {
            line: 123,
            column: 86
          }
        }],
        line: 123
      },
      "49": {
        loc: {
          start: {
            line: 174,
            column: 8
          },
          end: {
            line: 188,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 174,
            column: 8
          },
          end: {
            line: 188,
            column: 9
          }
        }, {
          start: {
            line: 183,
            column: 13
          },
          end: {
            line: 188,
            column: 9
          }
        }],
        line: 174
      },
      "50": {
        loc: {
          start: {
            line: 177,
            column: 16
          },
          end: {
            line: 180,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 177,
            column: 16
          },
          end: {
            line: 180,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 177
      },
      "51": {
        loc: {
          start: {
            line: 183,
            column: 13
          },
          end: {
            line: 188,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 183,
            column: 13
          },
          end: {
            line: 188,
            column: 9
          }
        }, {
          start: {
            line: 186,
            column: 13
          },
          end: {
            line: 188,
            column: 9
          }
        }],
        line: 183
      },
      "52": {
        loc: {
          start: {
            line: 186,
            column: 13
          },
          end: {
            line: 188,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 186,
            column: 13
          },
          end: {
            line: 188,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 186
      },
      "53": {
        loc: {
          start: {
            line: 186,
            column: 17
          },
          end: {
            line: 186,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 186,
            column: 17
          },
          end: {
            line: 186,
            column: 20
          }
        }, {
          start: {
            line: 186,
            column: 24
          },
          end: {
            line: 186,
            column: 47
          }
        }],
        line: 186
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0, 0, 0],
      "43": [0, 0],
      "44": [0, 0, 0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/validation-pipeline.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2BAAwB;AACxB,8EAA6C;AAe7C;IAGE,gCAAY,KAA4B;QAA5B,sBAAA,EAAA,UAA4B;QAFhC,UAAK,GAAqB,EAAE,CAAC;QAGnC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAED,wCAAO,GAAP,UAAQ,IAAoB;QAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC;IAED;;OAEG;IACG,yCAAQ,GAAd,UAAe,IAAS;uCAAG,OAAO;;;gBAC1B,MAAM,GAAa,EAAE,CAAC;gBACtB,aAAa,gBAAa,IAAI,CAAE,CAAC;gBAEvC,8CAA8C;gBAC9C,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACtC,sBAAO;4BACL,OAAO,EAAE,KAAK;4BACd,MAAM,EAAE,CAAC,iCAAiC,CAAC;yBAC5C,EAAC;gBACJ,CAAC;oCAGU,IAAI;oBACb,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAEpC,wBAAwB;oBACxB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,UAAU,KAAK,SAAS,IAAI,UAAU,KAAK,IAAI,IAAI,UAAU,KAAK,EAAE,CAAC,EAAE,CAAC;wBAC5F,MAAM,CAAC,IAAI,CAAC,UAAG,IAAI,CAAC,KAAK,iBAAc,CAAC,CAAC;;oBAE3C,CAAC;oBAED,4CAA4C;oBAC5C,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,UAAU,KAAK,SAAS,IAAI,UAAU,KAAK,IAAI,IAAI,UAAU,KAAK,EAAE,CAAC,EAAE,CAAC;;oBAE/F,CAAC;oBAED,8BAA8B;oBAC9B,IAAI,CAAC;wBACH,IAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;wBAErD,kCAAkC;wBAClC,IAAI,IAAI,CAAC,QAAQ,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE,CAAC;4BACxD,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,OAAK,cAAc,CAAC,cAAc,CAAC,CAAC;wBAClE,CAAC;6BAAM,CAAC;4BACN,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC;wBAC7C,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,IAAI,KAAK,YAAY,OAAC,CAAC,QAAQ,EAAE,CAAC;4BAChC,MAAM,CAAC,IAAI,OAAX,MAAM,EAAS,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,UAAG,IAAI,CAAC,KAAK,eAAK,GAAG,CAAC,OAAO,CAAE,EAA/B,CAA+B,CAAC,EAAE;wBAC3E,CAAC;6BAAM,CAAC;4BACN,MAAM,CAAC,IAAI,CAAC,UAAG,IAAI,CAAC,KAAK,wBAAqB,CAAC,CAAC;wBAClD,CAAC;oBACH,CAAC;;;gBA/BH,gDAAgD;gBAChD,WAA6B,EAAV,KAAA,IAAI,CAAC,KAAK,EAAV,cAAU,EAAV,IAAU;oBAAlB,IAAI;4BAAJ,IAAI;iBA+Bd;gBAGK,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;gBACjE,MAAM,CAAC,IAAI,OAAX,MAAM,EAAS,cAAc,EAAE;gBAE/B,sBAAO;wBACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;wBAC5B,MAAM,QAAA;wBACN,aAAa,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS;qBAC/D,EAAC;;;KACH;IAED;;OAEG;IACK,+CAAc,GAAtB,UAAuB,KAAa;QAClC,qDAAqD;QACrD,IAAM,OAAO,GAAG,8BAAS,CAAC,QAAQ,CAAC,KAAK,EAAE;YACxC,YAAY,EAAE,EAAE;YAChB,YAAY,EAAE,EAAE;SACjB,CAAC,CAAC;QAEH,qDAAqD;QACrD,OAAO,OAAO;aACX,OAAO,CAAC,qDAAqD,EAAE,EAAE,CAAC,CAAC,qBAAqB;aACxF,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,+BAA+B;aAC5D,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,wBAAwB;aACnD,IAAI,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,sDAAqB,GAA7B,UAA8B,IAAS;QACrC,IAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,mCAAmC;QACnC,IAAM,WAAW,GAAG;YAClB,mEAAmE;YACnE,kBAAkB;YAClB,sBAAsB;SACvB,CAAC;QAEF,yBAAyB;QACzB,IAAM,WAAW,GAAG;YAClB,qDAAqD;YACrD,eAAe;YACf,aAAa;YACb,WAAW;YACX,WAAW;YACX,UAAU;SACX,CAAC;QAEF,sCAAsC;QACtC,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,WAAW,EAAE,kCAAkC,EAAE,MAAM,CAAC,CAAC;QAC3F,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,WAAW,EAAE,+BAA+B,EAAE,MAAM,CAAC,CAAC;QAExF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,uDAAsB,GAA9B,UAA+B,GAAQ,EAAE,QAAkB,EAAE,YAAoB,EAAE,MAAgB;QAAnG,iBAaC;QAZC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC5B,KAAsB,UAAQ,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ,EAAE,CAAC;gBAA5B,IAAM,OAAO,iBAAA;gBAChB,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;oBACtB,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAC1B,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9B,GAAG,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,KAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,CAAC,EAAjE,CAAiE,CAAC,CAAC;QACzF,CAAC;aAAM,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAA,KAAK,IAAI,OAAA,KAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,CAAC,EAAlE,CAAkE,CAAC,CAAC;QAC1G,CAAC;IACH,CAAC;IACH,6BAAC;AAAD,CAAC,AAxID,IAwIC;AAxIY,wDAAsB;AA0InC;;GAEG;AACH;IAAA;IAqHA,CAAC;IApHQ,8CAA0B,GAAjC;QACE,OAAO,IAAI,sBAAsB,CAAC;YAChC;gBACE,KAAK,EAAE,WAAW;gBAClB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;qBACf,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC;qBAChC,GAAG,CAAC,EAAE,EAAE,4CAA4C,CAAC;qBACrD,KAAK,CAAC,iBAAiB,EAAE,uEAAuE,CAAC;gBACpG,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;aACf;YACD;gBACE,KAAK,EAAE,UAAU;gBACjB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;qBACf,GAAG,CAAC,CAAC,EAAE,uBAAuB,CAAC;qBAC/B,GAAG,CAAC,EAAE,EAAE,2CAA2C,CAAC;qBACpD,KAAK,CAAC,iBAAiB,EAAE,sEAAsE,CAAC;gBACnG,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;aACf;YACD;gBACE,KAAK,EAAE,OAAO;gBACd,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;qBACf,KAAK,CAAC,oCAAoC,CAAC;qBAC3C,GAAG,CAAC,GAAG,EAAE,mBAAmB,CAAC;gBAChC,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;aACf;YACD;gBACE,KAAK,EAAE,OAAO;gBACd,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;qBACf,GAAG,CAAC,EAAE,EAAE,0BAA0B,CAAC;qBACnC,KAAK,CAAC,gCAAgC,EAAE,mCAAmC,CAAC;gBAC/E,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,KAAK;aAChB;YACD;gBACE,KAAK,EAAE,UAAU;gBACjB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,2CAA2C,CAAC;gBACxE,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,KAAK;aAChB;YACD;gBACE,KAAK,EAAE,SAAS;gBAChB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;qBACf,GAAG,CAAC,kCAAkC,CAAC;qBACvC,GAAG,CAAC,GAAG,EAAE,yBAAyB,CAAC;gBACtC,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,KAAK;aAChB;YACD;gBACE,KAAK,EAAE,UAAU;gBACjB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;qBACf,GAAG,CAAC,mCAAmC,CAAC;qBACxC,GAAG,CAAC,GAAG,EAAE,0BAA0B,CAAC;gBACvC,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,KAAK;aAChB;SACF,CAAC,CAAC;IACL,CAAC;IAEM,wCAAoB,GAA3B;QACE,OAAO,IAAI,sBAAsB,CAAC;YAChC;gBACE,KAAK,EAAE,OAAO;gBACd,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;qBACf,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC;qBAClC,GAAG,CAAC,GAAG,EAAE,+CAA+C,CAAC;gBAC5D,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;aACf;YACD;gBACE,KAAK,EAAE,SAAS;gBAChB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;qBACf,GAAG,CAAC,IAAI,EAAE,2CAA2C,CAAC;gBACzD,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,KAAK;aAChB;YACD;gBACE,KAAK,EAAE,UAAU;gBACjB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;qBACf,GAAG,CAAC,EAAE,EAAE,2BAA2B,CAAC;gBACvC,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,KAAK;aAChB;SACF,CAAC,CAAC;IACL,CAAC;IAEM,sCAAkB,GAAzB;QACE,OAAO,IAAI,sBAAsB,CAAC;YAChC;gBACE,KAAK,EAAE,OAAO;gBACd,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;qBACf,KAAK,CAAC,oCAAoC,CAAC;qBAC3C,GAAG,CAAC,GAAG,EAAE,mBAAmB,CAAC;gBAChC,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;aACf;YACD;gBACE,KAAK,EAAE,UAAU;gBACjB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;qBACf,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC;qBAChD,GAAG,CAAC,GAAG,EAAE,sBAAsB,CAAC;qBAChC,KAAK,CAAC,iCAAiC,EAAE,2FAA2F,CAAC;gBACxI,QAAQ,EAAE,KAAK,EAAE,2BAA2B;gBAC5C,QAAQ,EAAE,IAAI;aACf;YACD;gBACE,KAAK,EAAE,MAAM;gBACb,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;qBACf,GAAG,CAAC,GAAG,EAAE,uCAAuC,CAAC;gBACpD,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,KAAK;aAChB;SACF,CAAC,CAAC;IACL,CAAC;IACH,0BAAC;AAAD,CAAC,AArHD,IAqHC;AArHY,kDAAmB",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/validation-pipeline.ts"],
      sourcesContent: ["import { z } from 'zod';\nimport DOMPurify from 'isomorphic-dompurify';\n\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: string[];\n  sanitizedData?: any;\n}\n\nexport interface ValidationRule {\n  field: string;\n  schema: z.ZodSchema;\n  sanitize?: boolean;\n  required?: boolean;\n}\n\nexport class DataValidationPipeline {\n  private rules: ValidationRule[] = [];\n\n  constructor(rules: ValidationRule[] = []) {\n    this.rules = rules;\n  }\n\n  addRule(rule: ValidationRule): void {\n    this.rules.push(rule);\n  }\n\n  /**\n   * Comprehensive validation and sanitization pipeline\n   */\n  async validate(data: any): Promise<ValidationResult> {\n    const errors: string[] = [];\n    const sanitizedData: any = { ...data };\n\n    // Step 1: Basic type and structure validation\n    if (!data || typeof data !== 'object') {\n      return {\n        isValid: false,\n        errors: ['Invalid data structure provided']\n      };\n    }\n\n    // Step 2: Apply field-specific validation rules\n    for (const rule of this.rules) {\n      const fieldValue = data[rule.field];\n\n      // Check required fields\n      if (rule.required && (fieldValue === undefined || fieldValue === null || fieldValue === '')) {\n        errors.push(`${rule.field} is required`);\n        continue;\n      }\n\n      // Skip validation for optional empty fields\n      if (!rule.required && (fieldValue === undefined || fieldValue === null || fieldValue === '')) {\n        continue;\n      }\n\n      // Apply Zod schema validation\n      try {\n        const validatedValue = rule.schema.parse(fieldValue);\n        \n        // Apply sanitization if requested\n        if (rule.sanitize && typeof validatedValue === 'string') {\n          sanitizedData[rule.field] = this.sanitizeString(validatedValue);\n        } else {\n          sanitizedData[rule.field] = validatedValue;\n        }\n      } catch (error) {\n        if (error instanceof z.ZodError) {\n          errors.push(...error.errors.map(err => `${rule.field}: ${err.message}`));\n        } else {\n          errors.push(`${rule.field}: Validation failed`);\n        }\n      }\n    }\n\n    // Step 3: Additional security checks\n    const securityErrors = this.performSecurityChecks(sanitizedData);\n    errors.push(...securityErrors);\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      sanitizedData: errors.length === 0 ? sanitizedData : undefined\n    };\n  }\n\n  /**\n   * Sanitize string input to prevent XSS and other attacks\n   */\n  private sanitizeString(input: string): string {\n    // Remove HTML tags and potentially dangerous content\n    const cleaned = DOMPurify.sanitize(input, { \n      ALLOWED_TAGS: [], \n      ALLOWED_ATTR: [] \n    });\n    \n    // Additional sanitization for common attack patterns\n    return cleaned\n      .replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '') // Remove script tags\n      .replace(/javascript:/gi, '') // Remove javascript: protocols\n      .replace(/on\\w+\\s*=/gi, '') // Remove event handlers\n      .trim();\n  }\n\n  /**\n   * Perform additional security checks\n   */\n  private performSecurityChecks(data: any): string[] {\n    const errors: string[] = [];\n\n    // Check for SQL injection patterns\n    const sqlPatterns = [\n      /(\\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\\b)/i,\n      /(--|\\/\\*|\\*\\/|;)/,\n      /(\\b(OR|AND)\\b.*=.*)/i\n    ];\n\n    // Check for XSS patterns\n    const xssPatterns = [\n      /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\n      /javascript:/gi,\n      /on\\w+\\s*=/gi,\n      /<iframe/gi,\n      /<object/gi,\n      /<embed/gi\n    ];\n\n    // Recursively check all string values\n    this.checkObjectForPatterns(data, sqlPatterns, 'Potential SQL injection detected', errors);\n    this.checkObjectForPatterns(data, xssPatterns, 'Potential XSS attack detected', errors);\n\n    return errors;\n  }\n\n  /**\n   * Recursively check object for dangerous patterns\n   */\n  private checkObjectForPatterns(obj: any, patterns: RegExp[], errorMessage: string, errors: string[]): void {\n    if (typeof obj === 'string') {\n      for (const pattern of patterns) {\n        if (pattern.test(obj)) {\n          errors.push(errorMessage);\n          break;\n        }\n      }\n    } else if (Array.isArray(obj)) {\n      obj.forEach(item => this.checkObjectForPatterns(item, patterns, errorMessage, errors));\n    } else if (obj && typeof obj === 'object') {\n      Object.values(obj).forEach(value => this.checkObjectForPatterns(value, patterns, errorMessage, errors));\n    }\n  }\n}\n\n/**\n * Pre-configured validation pipelines for common use cases\n */\nexport class ValidationPipelines {\n  static createPersonalInfoPipeline(): DataValidationPipeline {\n    return new DataValidationPipeline([\n      {\n        field: 'firstName',\n        schema: z.string()\n          .min(1, 'First name is required')\n          .max(50, 'First name must be less than 50 characters')\n          .regex(/^[a-zA-Z\\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes'),\n        sanitize: true,\n        required: true\n      },\n      {\n        field: 'lastName',\n        schema: z.string()\n          .min(1, 'Last name is required')\n          .max(50, 'Last name must be less than 50 characters')\n          .regex(/^[a-zA-Z\\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes'),\n        sanitize: true,\n        required: true\n      },\n      {\n        field: 'email',\n        schema: z.string()\n          .email('Please enter a valid email address')\n          .max(254, 'Email is too long'),\n        sanitize: true,\n        required: true\n      },\n      {\n        field: 'phone',\n        schema: z.string()\n          .max(20, 'Phone number is too long')\n          .regex(/^[\\+]?[1-9][\\d\\s\\-\\(\\)]{0,15}$/, 'Please enter a valid phone number'),\n        sanitize: true,\n        required: false\n      },\n      {\n        field: 'location',\n        schema: z.string().max(100, 'Location must be less than 100 characters'),\n        sanitize: true,\n        required: false\n      },\n      {\n        field: 'website',\n        schema: z.string()\n          .url('Please enter a valid website URL')\n          .max(500, 'Website URL is too long'),\n        sanitize: true,\n        required: false\n      },\n      {\n        field: 'linkedIn',\n        schema: z.string()\n          .url('Please enter a valid LinkedIn URL')\n          .max(500, 'LinkedIn URL is too long'),\n        sanitize: true,\n        required: false\n      }\n    ]);\n  }\n\n  static createResumePipeline(): DataValidationPipeline {\n    return new DataValidationPipeline([\n      {\n        field: 'title',\n        schema: z.string()\n          .min(1, 'Resume title is required')\n          .max(200, 'Resume title must be less than 200 characters'),\n        sanitize: true,\n        required: true\n      },\n      {\n        field: 'summary',\n        schema: z.string()\n          .max(2000, 'Summary must be less than 2000 characters'),\n        sanitize: true,\n        required: false\n      },\n      {\n        field: 'template',\n        schema: z.string()\n          .max(50, 'Template name is too long'),\n        sanitize: true,\n        required: false\n      }\n    ]);\n  }\n\n  static createUserPipeline(): DataValidationPipeline {\n    return new DataValidationPipeline([\n      {\n        field: 'email',\n        schema: z.string()\n          .email('Please enter a valid email address')\n          .max(254, 'Email is too long'),\n        sanitize: true,\n        required: true\n      },\n      {\n        field: 'password',\n        schema: z.string()\n          .min(8, 'Password must be at least 8 characters')\n          .max(128, 'Password is too long')\n          .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),\n        sanitize: false, // Don't sanitize passwords\n        required: true\n      },\n      {\n        field: 'name',\n        schema: z.string()\n          .max(100, 'Name must be less than 100 characters'),\n        sanitize: true,\n        required: false\n      }\n    ]);\n  }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "200d69141f38a96f851b6d9af63e71d977d4bda8"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_k06ael1tm = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_k06ael1tm();
var __assign =
/* istanbul ignore next */
(cov_k06ael1tm().s[0]++,
/* istanbul ignore next */
(cov_k06ael1tm().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_k06ael1tm().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_k06ael1tm().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_k06ael1tm().f[0]++;
  cov_k06ael1tm().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_k06ael1tm().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_k06ael1tm().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_k06ael1tm().f[1]++;
    cov_k06ael1tm().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_k06ael1tm().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_k06ael1tm().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_k06ael1tm().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_k06ael1tm().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_k06ael1tm().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_k06ael1tm().b[2][0]++;
          cov_k06ael1tm().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_k06ael1tm().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_k06ael1tm().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_k06ael1tm().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_k06ael1tm().s[11]++,
/* istanbul ignore next */
(cov_k06ael1tm().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_k06ael1tm().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_k06ael1tm().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_k06ael1tm().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_k06ael1tm().f[3]++;
    cov_k06ael1tm().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_k06ael1tm().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_k06ael1tm().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_k06ael1tm().f[4]++;
      cov_k06ael1tm().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_k06ael1tm().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_k06ael1tm().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_k06ael1tm().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_k06ael1tm().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_k06ael1tm().f[6]++;
      cov_k06ael1tm().s[15]++;
      try {
        /* istanbul ignore next */
        cov_k06ael1tm().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_k06ael1tm().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_k06ael1tm().f[7]++;
      cov_k06ael1tm().s[18]++;
      try {
        /* istanbul ignore next */
        cov_k06ael1tm().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_k06ael1tm().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_k06ael1tm().f[8]++;
      cov_k06ael1tm().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_k06ael1tm().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_k06ael1tm().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_k06ael1tm().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_k06ael1tm().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_k06ael1tm().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_k06ael1tm().s[23]++,
/* istanbul ignore next */
(cov_k06ael1tm().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_k06ael1tm().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_k06ael1tm().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_k06ael1tm().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_k06ael1tm().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_k06ael1tm().f[10]++;
        cov_k06ael1tm().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_k06ael1tm().b[9][0]++;
          cov_k06ael1tm().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_k06ael1tm().b[9][1]++;
        }
        cov_k06ael1tm().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_k06ael1tm().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_k06ael1tm().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_k06ael1tm().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_k06ael1tm().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_k06ael1tm().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_k06ael1tm().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_k06ael1tm().f[11]++;
    cov_k06ael1tm().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_k06ael1tm().f[12]++;
    cov_k06ael1tm().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_k06ael1tm().f[13]++;
      cov_k06ael1tm().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_k06ael1tm().f[14]++;
    cov_k06ael1tm().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_k06ael1tm().b[12][0]++;
      cov_k06ael1tm().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_k06ael1tm().b[12][1]++;
    }
    cov_k06ael1tm().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_k06ael1tm().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_k06ael1tm().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_k06ael1tm().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_k06ael1tm().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_k06ael1tm().s[36]++;
      try {
        /* istanbul ignore next */
        cov_k06ael1tm().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_k06ael1tm().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_k06ael1tm().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_k06ael1tm().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_k06ael1tm().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_k06ael1tm().b[18][0]++,
        /* istanbul ignore next */
        (cov_k06ael1tm().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_k06ael1tm().b[19][1]++,
        /* istanbul ignore next */
        (cov_k06ael1tm().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_k06ael1tm().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_k06ael1tm().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_k06ael1tm().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_k06ael1tm().b[15][0]++;
          cov_k06ael1tm().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_k06ael1tm().b[15][1]++;
        }
        cov_k06ael1tm().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_k06ael1tm().b[21][0]++;
          cov_k06ael1tm().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_k06ael1tm().b[21][1]++;
        }
        cov_k06ael1tm().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_k06ael1tm().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_k06ael1tm().b[22][1]++;
            cov_k06ael1tm().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_k06ael1tm().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_k06ael1tm().b[22][2]++;
            cov_k06ael1tm().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_k06ael1tm().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_k06ael1tm().b[22][3]++;
            cov_k06ael1tm().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_k06ael1tm().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_k06ael1tm().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_k06ael1tm().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_k06ael1tm().b[22][4]++;
            cov_k06ael1tm().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_k06ael1tm().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_k06ael1tm().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_k06ael1tm().b[22][5]++;
            cov_k06ael1tm().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_k06ael1tm().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_k06ael1tm().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_k06ael1tm().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_k06ael1tm().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_k06ael1tm().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_k06ael1tm().b[23][0]++;
              cov_k06ael1tm().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_k06ael1tm().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_k06ael1tm().b[23][1]++;
            }
            cov_k06ael1tm().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_k06ael1tm().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_k06ael1tm().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_k06ael1tm().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_k06ael1tm().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_k06ael1tm().b[26][0]++;
              cov_k06ael1tm().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_k06ael1tm().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_k06ael1tm().b[26][1]++;
            }
            cov_k06ael1tm().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_k06ael1tm().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_k06ael1tm().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_k06ael1tm().b[28][0]++;
              cov_k06ael1tm().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_k06ael1tm().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_k06ael1tm().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_k06ael1tm().b[28][1]++;
            }
            cov_k06ael1tm().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_k06ael1tm().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_k06ael1tm().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_k06ael1tm().b[30][0]++;
              cov_k06ael1tm().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_k06ael1tm().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_k06ael1tm().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_k06ael1tm().b[30][1]++;
            }
            cov_k06ael1tm().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_k06ael1tm().b[32][0]++;
              cov_k06ael1tm().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_k06ael1tm().b[32][1]++;
            }
            cov_k06ael1tm().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_k06ael1tm().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_k06ael1tm().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_k06ael1tm().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_k06ael1tm().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_k06ael1tm().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_k06ael1tm().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_k06ael1tm().b[33][0]++;
      cov_k06ael1tm().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_k06ael1tm().b[33][1]++;
    }
    cov_k06ael1tm().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_k06ael1tm().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_k06ael1tm().b[34][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_k06ael1tm().s[78]++,
/* istanbul ignore next */
(cov_k06ael1tm().b[35][0]++, this) &&
/* istanbul ignore next */
(cov_k06ael1tm().b[35][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_k06ael1tm().b[35][2]++, function (mod) {
  /* istanbul ignore next */
  cov_k06ael1tm().f[15]++;
  cov_k06ael1tm().s[79]++;
  return /* istanbul ignore next */(cov_k06ael1tm().b[37][0]++, mod) &&
  /* istanbul ignore next */
  (cov_k06ael1tm().b[37][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_k06ael1tm().b[36][0]++, mod) :
  /* istanbul ignore next */
  (cov_k06ael1tm().b[36][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_k06ael1tm().s[80]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_k06ael1tm().s[81]++;
exports.ValidationPipelines = exports.DataValidationPipeline = void 0;
var zod_1 =
/* istanbul ignore next */
(cov_k06ael1tm().s[82]++, require("zod"));
var isomorphic_dompurify_1 =
/* istanbul ignore next */
(cov_k06ael1tm().s[83]++, __importDefault(require("isomorphic-dompurify")));
var DataValidationPipeline =
/* istanbul ignore next */
(/** @class */cov_k06ael1tm().s[84]++, function () {
  /* istanbul ignore next */
  cov_k06ael1tm().f[16]++;
  function DataValidationPipeline(rules) {
    /* istanbul ignore next */
    cov_k06ael1tm().f[17]++;
    cov_k06ael1tm().s[85]++;
    if (rules === void 0) {
      /* istanbul ignore next */
      cov_k06ael1tm().b[38][0]++;
      cov_k06ael1tm().s[86]++;
      rules = [];
    } else
    /* istanbul ignore next */
    {
      cov_k06ael1tm().b[38][1]++;
    }
    cov_k06ael1tm().s[87]++;
    this.rules = [];
    /* istanbul ignore next */
    cov_k06ael1tm().s[88]++;
    this.rules = rules;
  }
  /* istanbul ignore next */
  cov_k06ael1tm().s[89]++;
  DataValidationPipeline.prototype.addRule = function (rule) {
    /* istanbul ignore next */
    cov_k06ael1tm().f[18]++;
    cov_k06ael1tm().s[90]++;
    this.rules.push(rule);
  };
  /**
   * Comprehensive validation and sanitization pipeline
   */
  /* istanbul ignore next */
  cov_k06ael1tm().s[91]++;
  DataValidationPipeline.prototype.validate = function (data) {
    /* istanbul ignore next */
    cov_k06ael1tm().f[19]++;
    cov_k06ael1tm().s[92]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_k06ael1tm().f[20]++;
      var errors, sanitizedData, _loop_1, this_1, _i, _a, rule, securityErrors;
      /* istanbul ignore next */
      cov_k06ael1tm().s[93]++;
      return __generator(this, function (_b) {
        /* istanbul ignore next */
        cov_k06ael1tm().f[21]++;
        cov_k06ael1tm().s[94]++;
        errors = [];
        /* istanbul ignore next */
        cov_k06ael1tm().s[95]++;
        sanitizedData = __assign({}, data);
        // Step 1: Basic type and structure validation
        /* istanbul ignore next */
        cov_k06ael1tm().s[96]++;
        if (
        /* istanbul ignore next */
        (cov_k06ael1tm().b[40][0]++, !data) ||
        /* istanbul ignore next */
        (cov_k06ael1tm().b[40][1]++, typeof data !== 'object')) {
          /* istanbul ignore next */
          cov_k06ael1tm().b[39][0]++;
          cov_k06ael1tm().s[97]++;
          return [2 /*return*/, {
            isValid: false,
            errors: ['Invalid data structure provided']
          }];
        } else
        /* istanbul ignore next */
        {
          cov_k06ael1tm().b[39][1]++;
        }
        cov_k06ael1tm().s[98]++;
        _loop_1 = function (rule) {
          /* istanbul ignore next */
          cov_k06ael1tm().f[22]++;
          var fieldValue =
          /* istanbul ignore next */
          (cov_k06ael1tm().s[99]++, data[rule.field]);
          // Check required fields
          /* istanbul ignore next */
          cov_k06ael1tm().s[100]++;
          if (
          /* istanbul ignore next */
          (cov_k06ael1tm().b[42][0]++, rule.required) && (
          /* istanbul ignore next */
          (cov_k06ael1tm().b[42][1]++, fieldValue === undefined) ||
          /* istanbul ignore next */
          (cov_k06ael1tm().b[42][2]++, fieldValue === null) ||
          /* istanbul ignore next */
          (cov_k06ael1tm().b[42][3]++, fieldValue === ''))) {
            /* istanbul ignore next */
            cov_k06ael1tm().b[41][0]++;
            cov_k06ael1tm().s[101]++;
            errors.push("".concat(rule.field, " is required"));
            /* istanbul ignore next */
            cov_k06ael1tm().s[102]++;
            return "continue";
          } else
          /* istanbul ignore next */
          {
            cov_k06ael1tm().b[41][1]++;
          }
          // Skip validation for optional empty fields
          cov_k06ael1tm().s[103]++;
          if (
          /* istanbul ignore next */
          (cov_k06ael1tm().b[44][0]++, !rule.required) && (
          /* istanbul ignore next */
          (cov_k06ael1tm().b[44][1]++, fieldValue === undefined) ||
          /* istanbul ignore next */
          (cov_k06ael1tm().b[44][2]++, fieldValue === null) ||
          /* istanbul ignore next */
          (cov_k06ael1tm().b[44][3]++, fieldValue === ''))) {
            /* istanbul ignore next */
            cov_k06ael1tm().b[43][0]++;
            cov_k06ael1tm().s[104]++;
            return "continue";
          } else
          /* istanbul ignore next */
          {
            cov_k06ael1tm().b[43][1]++;
          }
          // Apply Zod schema validation
          cov_k06ael1tm().s[105]++;
          try {
            var validatedValue =
            /* istanbul ignore next */
            (cov_k06ael1tm().s[106]++, rule.schema.parse(fieldValue));
            // Apply sanitization if requested
            /* istanbul ignore next */
            cov_k06ael1tm().s[107]++;
            if (
            /* istanbul ignore next */
            (cov_k06ael1tm().b[46][0]++, rule.sanitize) &&
            /* istanbul ignore next */
            (cov_k06ael1tm().b[46][1]++, typeof validatedValue === 'string')) {
              /* istanbul ignore next */
              cov_k06ael1tm().b[45][0]++;
              cov_k06ael1tm().s[108]++;
              sanitizedData[rule.field] = this_1.sanitizeString(validatedValue);
            } else {
              /* istanbul ignore next */
              cov_k06ael1tm().b[45][1]++;
              cov_k06ael1tm().s[109]++;
              sanitizedData[rule.field] = validatedValue;
            }
          } catch (error) {
            /* istanbul ignore next */
            cov_k06ael1tm().s[110]++;
            if (error instanceof zod_1.z.ZodError) {
              /* istanbul ignore next */
              cov_k06ael1tm().b[47][0]++;
              cov_k06ael1tm().s[111]++;
              errors.push.apply(errors, error.errors.map(function (err) {
                /* istanbul ignore next */
                cov_k06ael1tm().f[23]++;
                cov_k06ael1tm().s[112]++;
                return "".concat(rule.field, ": ").concat(err.message);
              }));
            } else {
              /* istanbul ignore next */
              cov_k06ael1tm().b[47][1]++;
              cov_k06ael1tm().s[113]++;
              errors.push("".concat(rule.field, ": Validation failed"));
            }
          }
        };
        /* istanbul ignore next */
        cov_k06ael1tm().s[114]++;
        this_1 = this;
        // Step 2: Apply field-specific validation rules
        /* istanbul ignore next */
        cov_k06ael1tm().s[115]++;
        for (_i = 0, _a = this.rules; _i < _a.length; _i++) {
          /* istanbul ignore next */
          cov_k06ael1tm().s[116]++;
          rule = _a[_i];
          /* istanbul ignore next */
          cov_k06ael1tm().s[117]++;
          _loop_1(rule);
        }
        /* istanbul ignore next */
        cov_k06ael1tm().s[118]++;
        securityErrors = this.performSecurityChecks(sanitizedData);
        /* istanbul ignore next */
        cov_k06ael1tm().s[119]++;
        errors.push.apply(errors, securityErrors);
        /* istanbul ignore next */
        cov_k06ael1tm().s[120]++;
        return [2 /*return*/, {
          isValid: errors.length === 0,
          errors: errors,
          sanitizedData: errors.length === 0 ?
          /* istanbul ignore next */
          (cov_k06ael1tm().b[48][0]++, sanitizedData) :
          /* istanbul ignore next */
          (cov_k06ael1tm().b[48][1]++, undefined)
        }];
      });
    });
  };
  /**
   * Sanitize string input to prevent XSS and other attacks
   */
  /* istanbul ignore next */
  cov_k06ael1tm().s[121]++;
  DataValidationPipeline.prototype.sanitizeString = function (input) {
    /* istanbul ignore next */
    cov_k06ael1tm().f[24]++;
    // Remove HTML tags and potentially dangerous content
    var cleaned =
    /* istanbul ignore next */
    (cov_k06ael1tm().s[122]++, isomorphic_dompurify_1.default.sanitize(input, {
      ALLOWED_TAGS: [],
      ALLOWED_ATTR: []
    }));
    // Additional sanitization for common attack patterns
    /* istanbul ignore next */
    cov_k06ael1tm().s[123]++;
    return cleaned.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim();
  };
  /**
   * Perform additional security checks
   */
  /* istanbul ignore next */
  cov_k06ael1tm().s[124]++;
  DataValidationPipeline.prototype.performSecurityChecks = function (data) {
    /* istanbul ignore next */
    cov_k06ael1tm().f[25]++;
    var errors =
    /* istanbul ignore next */
    (cov_k06ael1tm().s[125]++, []);
    // Check for SQL injection patterns
    var sqlPatterns =
    /* istanbul ignore next */
    (cov_k06ael1tm().s[126]++, [/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i, /(--|\/\*|\*\/|;)/, /(\b(OR|AND)\b.*=.*)/i]);
    // Check for XSS patterns
    var xssPatterns =
    /* istanbul ignore next */
    (cov_k06ael1tm().s[127]++, [/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, /javascript:/gi, /on\w+\s*=/gi, /<iframe/gi, /<object/gi, /<embed/gi]);
    // Recursively check all string values
    /* istanbul ignore next */
    cov_k06ael1tm().s[128]++;
    this.checkObjectForPatterns(data, sqlPatterns, 'Potential SQL injection detected', errors);
    /* istanbul ignore next */
    cov_k06ael1tm().s[129]++;
    this.checkObjectForPatterns(data, xssPatterns, 'Potential XSS attack detected', errors);
    /* istanbul ignore next */
    cov_k06ael1tm().s[130]++;
    return errors;
  };
  /**
   * Recursively check object for dangerous patterns
   */
  /* istanbul ignore next */
  cov_k06ael1tm().s[131]++;
  DataValidationPipeline.prototype.checkObjectForPatterns = function (obj, patterns, errorMessage, errors) {
    /* istanbul ignore next */
    cov_k06ael1tm().f[26]++;
    var _this =
    /* istanbul ignore next */
    (cov_k06ael1tm().s[132]++, this);
    /* istanbul ignore next */
    cov_k06ael1tm().s[133]++;
    if (typeof obj === 'string') {
      /* istanbul ignore next */
      cov_k06ael1tm().b[49][0]++;
      cov_k06ael1tm().s[134]++;
      for (var _i =
        /* istanbul ignore next */
        (cov_k06ael1tm().s[135]++, 0), patterns_1 =
        /* istanbul ignore next */
        (cov_k06ael1tm().s[136]++, patterns); _i < patterns_1.length; _i++) {
        var pattern =
        /* istanbul ignore next */
        (cov_k06ael1tm().s[137]++, patterns_1[_i]);
        /* istanbul ignore next */
        cov_k06ael1tm().s[138]++;
        if (pattern.test(obj)) {
          /* istanbul ignore next */
          cov_k06ael1tm().b[50][0]++;
          cov_k06ael1tm().s[139]++;
          errors.push(errorMessage);
          /* istanbul ignore next */
          cov_k06ael1tm().s[140]++;
          break;
        } else
        /* istanbul ignore next */
        {
          cov_k06ael1tm().b[50][1]++;
        }
      }
    } else {
      /* istanbul ignore next */
      cov_k06ael1tm().b[49][1]++;
      cov_k06ael1tm().s[141]++;
      if (Array.isArray(obj)) {
        /* istanbul ignore next */
        cov_k06ael1tm().b[51][0]++;
        cov_k06ael1tm().s[142]++;
        obj.forEach(function (item) {
          /* istanbul ignore next */
          cov_k06ael1tm().f[27]++;
          cov_k06ael1tm().s[143]++;
          return _this.checkObjectForPatterns(item, patterns, errorMessage, errors);
        });
      } else {
        /* istanbul ignore next */
        cov_k06ael1tm().b[51][1]++;
        cov_k06ael1tm().s[144]++;
        if (
        /* istanbul ignore next */
        (cov_k06ael1tm().b[53][0]++, obj) &&
        /* istanbul ignore next */
        (cov_k06ael1tm().b[53][1]++, typeof obj === 'object')) {
          /* istanbul ignore next */
          cov_k06ael1tm().b[52][0]++;
          cov_k06ael1tm().s[145]++;
          Object.values(obj).forEach(function (value) {
            /* istanbul ignore next */
            cov_k06ael1tm().f[28]++;
            cov_k06ael1tm().s[146]++;
            return _this.checkObjectForPatterns(value, patterns, errorMessage, errors);
          });
        } else
        /* istanbul ignore next */
        {
          cov_k06ael1tm().b[52][1]++;
        }
      }
    }
  };
  /* istanbul ignore next */
  cov_k06ael1tm().s[147]++;
  return DataValidationPipeline;
}());
/* istanbul ignore next */
cov_k06ael1tm().s[148]++;
exports.DataValidationPipeline = DataValidationPipeline;
/**
 * Pre-configured validation pipelines for common use cases
 */
var ValidationPipelines =
/* istanbul ignore next */
(/** @class */cov_k06ael1tm().s[149]++, function () {
  /* istanbul ignore next */
  cov_k06ael1tm().f[29]++;
  function ValidationPipelines() {
    /* istanbul ignore next */
    cov_k06ael1tm().f[30]++;
  }
  /* istanbul ignore next */
  cov_k06ael1tm().s[150]++;
  ValidationPipelines.createPersonalInfoPipeline = function () {
    /* istanbul ignore next */
    cov_k06ael1tm().f[31]++;
    cov_k06ael1tm().s[151]++;
    return new DataValidationPipeline([{
      field: 'firstName',
      schema: zod_1.z.string().min(1, 'First name is required').max(50, 'First name must be less than 50 characters').regex(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes'),
      sanitize: true,
      required: true
    }, {
      field: 'lastName',
      schema: zod_1.z.string().min(1, 'Last name is required').max(50, 'Last name must be less than 50 characters').regex(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes'),
      sanitize: true,
      required: true
    }, {
      field: 'email',
      schema: zod_1.z.string().email('Please enter a valid email address').max(254, 'Email is too long'),
      sanitize: true,
      required: true
    }, {
      field: 'phone',
      schema: zod_1.z.string().max(20, 'Phone number is too long').regex(/^[\+]?[1-9][\d\s\-\(\)]{0,15}$/, 'Please enter a valid phone number'),
      sanitize: true,
      required: false
    }, {
      field: 'location',
      schema: zod_1.z.string().max(100, 'Location must be less than 100 characters'),
      sanitize: true,
      required: false
    }, {
      field: 'website',
      schema: zod_1.z.string().url('Please enter a valid website URL').max(500, 'Website URL is too long'),
      sanitize: true,
      required: false
    }, {
      field: 'linkedIn',
      schema: zod_1.z.string().url('Please enter a valid LinkedIn URL').max(500, 'LinkedIn URL is too long'),
      sanitize: true,
      required: false
    }]);
  };
  /* istanbul ignore next */
  cov_k06ael1tm().s[152]++;
  ValidationPipelines.createResumePipeline = function () {
    /* istanbul ignore next */
    cov_k06ael1tm().f[32]++;
    cov_k06ael1tm().s[153]++;
    return new DataValidationPipeline([{
      field: 'title',
      schema: zod_1.z.string().min(1, 'Resume title is required').max(200, 'Resume title must be less than 200 characters'),
      sanitize: true,
      required: true
    }, {
      field: 'summary',
      schema: zod_1.z.string().max(2000, 'Summary must be less than 2000 characters'),
      sanitize: true,
      required: false
    }, {
      field: 'template',
      schema: zod_1.z.string().max(50, 'Template name is too long'),
      sanitize: true,
      required: false
    }]);
  };
  /* istanbul ignore next */
  cov_k06ael1tm().s[154]++;
  ValidationPipelines.createUserPipeline = function () {
    /* istanbul ignore next */
    cov_k06ael1tm().f[33]++;
    cov_k06ael1tm().s[155]++;
    return new DataValidationPipeline([{
      field: 'email',
      schema: zod_1.z.string().email('Please enter a valid email address').max(254, 'Email is too long'),
      sanitize: true,
      required: true
    }, {
      field: 'password',
      schema: zod_1.z.string().min(8, 'Password must be at least 8 characters').max(128, 'Password is too long').regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
      sanitize: false,
      // Don't sanitize passwords
      required: true
    }, {
      field: 'name',
      schema: zod_1.z.string().max(100, 'Name must be less than 100 characters'),
      sanitize: true,
      required: false
    }]);
  };
  /* istanbul ignore next */
  cov_k06ael1tm().s[156]++;
  return ValidationPipelines;
}());
/* istanbul ignore next */
cov_k06ael1tm().s[157]++;
exports.ValidationPipelines = ValidationPipelines;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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