{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/__tests__/csrf-security.test.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,uBAAuB;AACvB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,cAAM,OAAA,CAAC;IAC1B,WAAW,EAAE,EAAE;CAChB,CAAC,EAFyB,CAEzB,CAAC,CAAC;AAEJ,wBAAwB;AACxB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,cAAM,OAAA,CAAC;IACjC,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;CAC5B,CAAC,EAFgC,CAEhC,CAAC,CAAC;AAXJ,sCAA0C;AAC1C,gCAA6E;AAYrE,IAAA,gBAAgB,GAAK,OAAO,CAAC,gBAAgB,CAAC,iBAA9B,CAA+B;AAEvD,QAAQ,CAAC,4BAA4B,EAAE;IACrC,UAAU,CAAC;QACT,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,2BAA2B;QAC3B,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;YAC5B,UAAU,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAClC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,6EAA6E,EAAE;QAClF,IAAM,MAAM,GAAG,IAAA,wBAAiB,GAAE,CAAC;QACnC,IAAM,MAAM,GAAG,IAAA,wBAAiB,GAAE,CAAC;QAEnC,6BAA6B;QAC7B,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEhC,mEAAmE;QACnE,IAAM,YAAY,GAAG,8FAA8F,CAAC;QACpH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACrC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAErC,kDAAkD;QAClD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,2EAA2E,EAAE;;;;;oBAChF,gBAAgB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB;oBAG5D,QAAQ,GAAG,IAAI,oBAAW,CAAC,4BAA4B,EAAE;wBAC7D,OAAO,EAAE;4BACP,iBAAiB,EAAE,aAAa;4BAChC,YAAY,EAAE,8DAA8D;4BAC5E,iBAAiB,EAAE,gBAAgB;4BACnC,iBAAiB,EAAE,mBAAmB;yBACvC;qBACF,CAAC,CAAC;oBAEG,QAAQ,GAAG,IAAI,oBAAW,CAAC,4BAA4B,EAAE;wBAC7D,OAAO,EAAE;4BACP,iBAAiB,EAAE,aAAa,EAAE,UAAU;4BAC5C,YAAY,EAAE,oEAAoE,EAAE,eAAe;4BACnG,iBAAiB,EAAE,gBAAgB;4BACnC,iBAAiB,EAAE,mBAAmB;yBACvC;qBACF,CAAC,CAAC;oBAEY,qBAAM,IAAA,mBAAY,EAAC,QAAQ,CAAC,EAAA;;oBAArC,MAAM,GAAG,SAA4B;oBAC5B,qBAAM,IAAA,mBAAY,EAAC,QAAQ,CAAC,EAAA;;oBAArC,MAAM,GAAG,SAA4B;oBAE3C,2DAA2D;oBAC3D,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;;;SACjC,CAAC,CAAC;IAEH,IAAI,CAAC,4DAA4D,EAAE;;;;;oBACjE,gBAAgB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBAEnC,OAAO,GAAG;wBACd,iBAAiB,EAAE,aAAa;wBAChC,YAAY,EAAE,8DAA8D;wBAC5E,iBAAiB,EAAE,gBAAgB;wBACnC,iBAAiB,EAAE,mBAAmB;qBACvC,CAAC;oBAEI,QAAQ,GAAG,IAAI,oBAAW,CAAC,4BAA4B,EAAE,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;oBACtE,QAAQ,GAAG,IAAI,oBAAW,CAAC,4BAA4B,EAAE,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;oBAE7D,qBAAM,IAAA,mBAAY,EAAC,QAAQ,CAAC,EAAA;;oBAArC,MAAM,GAAG,SAA4B;oBAC5B,qBAAM,IAAA,mBAAY,EAAC,QAAQ,CAAC,EAAA;;oBAArC,MAAM,GAAG,SAA4B;oBAE3C,yCAAyC;oBACzC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;;;SAC7B,CAAC,CAAC;IAEH,IAAI,CAAC,wDAAwD,EAAE;;;;;oBAC7D,gBAAgB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBAEnC,OAAO,GAAG,IAAI,oBAAW,CAAC,4BAA4B,EAAE;wBAC5D,OAAO,EAAE;4BACP,iBAAiB,EAAE,aAAa;4BAChC,YAAY,EAAE,8DAA8D;4BAC5E,iBAAiB,EAAE,gBAAgB;4BACnC,iBAAiB,EAAE,mBAAmB;yBACvC;qBACF,CAAC,CAAC;oBAGW,qBAAM,IAAA,mBAAY,EAAC,OAAO,CAAC,EAAA;;oBAAnC,KAAK,GAAG,SAA2B;oBAGzB,qBAAM,IAAA,wBAAiB,EAAC,OAAO,EAAE,KAAK,CAAC,EAAA;;oBAAjD,OAAO,GAAG,SAAuC;oBACvD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAGrB,gBAAgB,GAAG,IAAI,oBAAW,CAAC,4BAA4B,EAAE;wBACrE,OAAO,EAAE;4BACP,iBAAiB,EAAE,aAAa;4BAChC,YAAY,EAAE,sBAAsB,EAAE,wBAAwB;4BAC9D,iBAAiB,EAAE,gBAAgB;4BACnC,iBAAiB,EAAE,mBAAmB;yBACvC;qBACF,CAAC,CAAC;oBAEsB,qBAAM,IAAA,wBAAiB,EAAC,gBAAgB,EAAE,KAAK,CAAC,EAAA;;oBAAnE,gBAAgB,GAAG,SAAgD;oBACzE,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;;;SACtC,CAAC,CAAC;IAEH,IAAI,CAAC,6DAA6D,EAAE;;;;;oBAC5D,WAAW,GAAG;wBAClB,IAAI,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE;qBACnD,CAAC;oBACF,gBAAgB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;oBAE1C,OAAO,GAAG,IAAI,oBAAW,CAAC,4BAA4B,EAAE;wBAC5D,OAAO,EAAE;4BACP,iBAAiB,EAAE,aAAa;4BAChC,YAAY,EAAE,aAAa;yBAC5B;qBACF,CAAC,CAAC;oBAEW,qBAAM,IAAA,mBAAY,EAAC,OAAO,CAAC,EAAA;;oBAAnC,KAAK,GAAG,SAA2B;oBACzB,qBAAM,IAAA,wBAAiB,EAAC,OAAO,EAAE,KAAK,CAAC,EAAA;;oBAAjD,OAAO,GAAG,SAAuC;oBAEvD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;SAC5B,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/__tests__/csrf-security.test.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\nimport { generateCSRFToken, getCSRFToken, validateCSRFToken } from '../csrf';\n\n// Mock the auth module\njest.mock('../auth', () => ({\n  authOptions: {}\n}));\n\n// Mock getServerSession\njest.mock('next-auth/next', () => ({\n  getServerSession: jest.fn()\n}));\n\nconst { getServerSession } = require('next-auth/next');\n\ndescribe('CSRF Security Improvements', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n    // Clear global CSRF tokens\n    if (globalThis.__csrfTokens) {\n      globalThis.__csrfTokens.clear();\n    }\n  });\n\n  test('generateCSRFToken should create secure tokens with multiple entropy sources', () => {\n    const token1 = generateCSRFToken();\n    const token2 = generateCSRFToken();\n    \n    // Tokens should be different\n    expect(token1).not.toBe(token2);\n    \n    // Tokens should have the expected format: uuid-timestamp-randomhex\n    const tokenPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}-[0-9a-z]+-[0-9a-f]{32}$/;\n    expect(token1).toMatch(tokenPattern);\n    expect(token2).toMatch(tokenPattern);\n    \n    // Tokens should be sufficiently long for security\n    expect(token1.length).toBeGreaterThan(70);\n  });\n\n  test('guest users should get tokens based on secure fingerprinting, not just IP', async () => {\n    getServerSession.mockResolvedValue(null); // No authenticated user\n\n    // Create two requests with same IP but different user agents\n    const request1 = new NextRequest('http://localhost:3000/test', {\n      headers: {\n        'x-forwarded-for': '***********',\n        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n        'accept-language': 'en-US,en;q=0.9',\n        'accept-encoding': 'gzip, deflate, br'\n      }\n    });\n\n    const request2 = new NextRequest('http://localhost:3000/test', {\n      headers: {\n        'x-forwarded-for': '***********', // Same IP\n        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', // Different UA\n        'accept-language': 'en-US,en;q=0.9',\n        'accept-encoding': 'gzip, deflate, br'\n      }\n    });\n\n    const token1 = await getCSRFToken(request1);\n    const token2 = await getCSRFToken(request2);\n\n    // Different fingerprints should result in different tokens\n    expect(token1).not.toBe(token2);\n  });\n\n  test('same guest fingerprint should get same token within expiry', async () => {\n    getServerSession.mockResolvedValue(null);\n\n    const headers = {\n      'x-forwarded-for': '***********',\n      'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n      'accept-language': 'en-US,en;q=0.9',\n      'accept-encoding': 'gzip, deflate, br'\n    };\n\n    const request1 = new NextRequest('http://localhost:3000/test', { headers });\n    const request2 = new NextRequest('http://localhost:3000/test', { headers });\n\n    const token1 = await getCSRFToken(request1);\n    const token2 = await getCSRFToken(request2);\n\n    // Same fingerprint should get same token\n    expect(token1).toBe(token2);\n  });\n\n  test('CSRF validation should work with secure fingerprinting', async () => {\n    getServerSession.mockResolvedValue(null);\n\n    const request = new NextRequest('http://localhost:3000/test', {\n      headers: {\n        'x-forwarded-for': '***********',\n        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n        'accept-language': 'en-US,en;q=0.9',\n        'accept-encoding': 'gzip, deflate, br'\n      }\n    });\n\n    // Get a token\n    const token = await getCSRFToken(request);\n\n    // Validation should succeed with same request fingerprint\n    const isValid = await validateCSRFToken(request, token);\n    expect(isValid).toBe(true);\n\n    // Validation should fail with different fingerprint\n    const differentRequest = new NextRequest('http://localhost:3000/test', {\n      headers: {\n        'x-forwarded-for': '***********',\n        'user-agent': 'Different User Agent', // Different fingerprint\n        'accept-language': 'en-US,en;q=0.9',\n        'accept-encoding': 'gzip, deflate, br'\n      }\n    });\n\n    const isValidDifferent = await validateCSRFToken(differentRequest, token);\n    expect(isValidDifferent).toBe(false);\n  });\n\n  test('authenticated users should use user ID for token generation', async () => {\n    const mockSession = {\n      user: { id: 'user123', email: '<EMAIL>' }\n    };\n    getServerSession.mockResolvedValue(mockSession);\n\n    const request = new NextRequest('http://localhost:3000/test', {\n      headers: {\n        'x-forwarded-for': '***********',\n        'user-agent': 'Mozilla/5.0'\n      }\n    });\n\n    const token = await getCSRFToken(request);\n    const isValid = await validateCSRFToken(request, token);\n\n    expect(isValid).toBe(true);\n  });\n});\n"], "version": 3}