ef6686daf2d88d0bb4da5a5c7f18f63e
"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
// Mock the auth module
jest.mock('../auth', function () { return ({
    authOptions: {}
}); });
// Mock getServerSession
jest.mock('next-auth/next', function () { return ({
    getServerSession: jest.fn()
}); });
var server_1 = require("next/server");
var csrf_1 = require("../csrf");
var getServerSession = require('next-auth/next').getServerSession;
describe('CSRF Security Improvements', function () {
    beforeEach(function () {
        jest.clearAllMocks();
        // Clear global CSRF tokens
        if (globalThis.__csrfTokens) {
            globalThis.__csrfTokens.clear();
        }
    });
    test('generateCSRFToken should create secure tokens with multiple entropy sources', function () {
        var token1 = (0, csrf_1.generateCSRFToken)();
        var token2 = (0, csrf_1.generateCSRFToken)();
        // Tokens should be different
        expect(token1).not.toBe(token2);
        // Tokens should have the expected format: uuid-timestamp-randomhex
        var tokenPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}-[0-9a-z]+-[0-9a-f]{32}$/;
        expect(token1).toMatch(tokenPattern);
        expect(token2).toMatch(tokenPattern);
        // Tokens should be sufficiently long for security
        expect(token1.length).toBeGreaterThan(70);
    });
    test('guest users should get tokens based on secure fingerprinting, not just IP', function () { return __awaiter(void 0, void 0, void 0, function () {
        var request1, request2, token1, token2;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    getServerSession.mockResolvedValue(null); // No authenticated user
                    request1 = new server_1.NextRequest('http://localhost:3000/test', {
                        headers: {
                            'x-forwarded-for': '***********',
                            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            'accept-language': 'en-US,en;q=0.9',
                            'accept-encoding': 'gzip, deflate, br'
                        }
                    });
                    request2 = new server_1.NextRequest('http://localhost:3000/test', {
                        headers: {
                            'x-forwarded-for': '***********', // Same IP
                            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', // Different UA
                            'accept-language': 'en-US,en;q=0.9',
                            'accept-encoding': 'gzip, deflate, br'
                        }
                    });
                    return [4 /*yield*/, (0, csrf_1.getCSRFToken)(request1)];
                case 1:
                    token1 = _a.sent();
                    return [4 /*yield*/, (0, csrf_1.getCSRFToken)(request2)];
                case 2:
                    token2 = _a.sent();
                    // Different fingerprints should result in different tokens
                    expect(token1).not.toBe(token2);
                    return [2 /*return*/];
            }
        });
    }); });
    test('same guest fingerprint should get same token within expiry', function () { return __awaiter(void 0, void 0, void 0, function () {
        var headers, request1, request2, token1, token2;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    getServerSession.mockResolvedValue(null);
                    headers = {
                        'x-forwarded-for': '***********',
                        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'accept-language': 'en-US,en;q=0.9',
                        'accept-encoding': 'gzip, deflate, br'
                    };
                    request1 = new server_1.NextRequest('http://localhost:3000/test', { headers: headers });
                    request2 = new server_1.NextRequest('http://localhost:3000/test', { headers: headers });
                    return [4 /*yield*/, (0, csrf_1.getCSRFToken)(request1)];
                case 1:
                    token1 = _a.sent();
                    return [4 /*yield*/, (0, csrf_1.getCSRFToken)(request2)];
                case 2:
                    token2 = _a.sent();
                    // Same fingerprint should get same token
                    expect(token1).toBe(token2);
                    return [2 /*return*/];
            }
        });
    }); });
    test('CSRF validation should work with secure fingerprinting', function () { return __awaiter(void 0, void 0, void 0, function () {
        var request, token, isValid, differentRequest, isValidDifferent;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    getServerSession.mockResolvedValue(null);
                    request = new server_1.NextRequest('http://localhost:3000/test', {
                        headers: {
                            'x-forwarded-for': '***********',
                            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            'accept-language': 'en-US,en;q=0.9',
                            'accept-encoding': 'gzip, deflate, br'
                        }
                    });
                    return [4 /*yield*/, (0, csrf_1.getCSRFToken)(request)];
                case 1:
                    token = _a.sent();
                    return [4 /*yield*/, (0, csrf_1.validateCSRFToken)(request, token)];
                case 2:
                    isValid = _a.sent();
                    expect(isValid).toBe(true);
                    differentRequest = new server_1.NextRequest('http://localhost:3000/test', {
                        headers: {
                            'x-forwarded-for': '***********',
                            'user-agent': 'Different User Agent', // Different fingerprint
                            'accept-language': 'en-US,en;q=0.9',
                            'accept-encoding': 'gzip, deflate, br'
                        }
                    });
                    return [4 /*yield*/, (0, csrf_1.validateCSRFToken)(differentRequest, token)];
                case 3:
                    isValidDifferent = _a.sent();
                    expect(isValidDifferent).toBe(false);
                    return [2 /*return*/];
            }
        });
    }); });
    test('authenticated users should use user ID for token generation', function () { return __awaiter(void 0, void 0, void 0, function () {
        var mockSession, request, token, isValid;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    mockSession = {
                        user: { id: 'user123', email: '<EMAIL>' }
                    };
                    getServerSession.mockResolvedValue(mockSession);
                    request = new server_1.NextRequest('http://localhost:3000/test', {
                        headers: {
                            'x-forwarded-for': '***********',
                            'user-agent': 'Mozilla/5.0'
                        }
                    });
                    return [4 /*yield*/, (0, csrf_1.getCSRFToken)(request)];
                case 1:
                    token = _a.sent();
                    return [4 /*yield*/, (0, csrf_1.validateCSRFToken)(request, token)];
                case 2:
                    isValid = _a.sent();
                    expect(isValid).toBe(true);
                    return [2 /*return*/];
            }
        });
    }); });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************