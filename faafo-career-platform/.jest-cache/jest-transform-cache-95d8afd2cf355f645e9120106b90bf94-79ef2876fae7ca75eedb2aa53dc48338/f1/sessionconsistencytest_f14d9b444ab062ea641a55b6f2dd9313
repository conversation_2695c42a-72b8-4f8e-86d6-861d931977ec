2fd6316278ad3466fce2534be0990453
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var auth_1 = require("../auth");
describe('Session Management Consistency', function () {
    test('session and JWT maxAge should be consistent', function () {
        var _a, _b;
        var sessionMaxAge = (_a = auth_1.authOptions.session) === null || _a === void 0 ? void 0 : _a.maxAge;
        var jwtMaxAge = (_b = auth_1.authOptions.jwt) === null || _b === void 0 ? void 0 : _b.maxAge;
        expect(sessionMaxAge).toBeDefined();
        expect(jwtMaxAge).toBeDefined();
        expect(sessionMaxAge).toBe(jwtMaxAge);
        expect(sessionMaxAge).toBe(30 * 24 * 60 * 60); // 30 days
    });
    test('cookie maxAge should match session maxAge', function () {
        var _a, _b, _c, _d;
        var sessionMaxAge = (_a = auth_1.authOptions.session) === null || _a === void 0 ? void 0 : _a.maxAge;
        var cookieMaxAge = (_d = (_c = (_b = auth_1.authOptions.cookies) === null || _b === void 0 ? void 0 : _b.sessionToken) === null || _c === void 0 ? void 0 : _c.options) === null || _d === void 0 ? void 0 : _d.maxAge;
        expect(cookieMaxAge).toBeDefined();
        expect(cookieMaxAge).toBe(sessionMaxAge);
    });
    test('session configuration should be properly structured', function () {
        var _a, _b;
        expect((_a = auth_1.authOptions.session) === null || _a === void 0 ? void 0 : _a.strategy).toBe('jwt');
        expect((_b = auth_1.authOptions.session) === null || _b === void 0 ? void 0 : _b.updateAge).toBe(24 * 60 * 60); // 24 hours
    });
    test('cookie security settings should be appropriate', function () {
        var _a, _b;
        var sessionCookie = (_b = (_a = auth_1.authOptions.cookies) === null || _a === void 0 ? void 0 : _a.sessionToken) === null || _b === void 0 ? void 0 : _b.options;
        expect(sessionCookie === null || sessionCookie === void 0 ? void 0 : sessionCookie.httpOnly).toBe(true);
        expect(sessionCookie === null || sessionCookie === void 0 ? void 0 : sessionCookie.sameSite).toBe('lax');
        expect(sessionCookie === null || sessionCookie === void 0 ? void 0 : sessionCookie.path).toBe('/');
        // In development, secure should be false; in production, true
        if (process.env.NODE_ENV === 'production') {
            expect(sessionCookie === null || sessionCookie === void 0 ? void 0 : sessionCookie.secure).toBe(true);
        }
        else {
            expect(sessionCookie === null || sessionCookie === void 0 ? void 0 : sessionCookie.secure).toBe(false);
        }
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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