{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/__tests__/session-consistency.test.ts", "mappings": ";;AAAA,gCAAsC;AAEtC,QAAQ,CAAC,gCAAgC,EAAE;IACzC,IAAI,CAAC,6CAA6C,EAAE;;QAClD,IAAM,aAAa,GAAG,MAAA,kBAAW,CAAC,OAAO,0CAAE,MAAM,CAAC;QAClD,IAAM,SAAS,GAAG,MAAA,kBAAW,CAAC,GAAG,0CAAE,MAAM,CAAC;QAE1C,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;QACpC,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QAChC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACtC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,UAAU;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,2CAA2C,EAAE;;QAChD,IAAM,aAAa,GAAG,MAAA,kBAAW,CAAC,OAAO,0CAAE,MAAM,CAAC;QAClD,IAAM,YAAY,GAAG,MAAA,MAAA,MAAA,kBAAW,CAAC,OAAO,0CAAE,YAAY,0CAAE,OAAO,0CAAE,MAAM,CAAC;QAExE,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;QACnC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,qDAAqD,EAAE;;QAC1D,MAAM,CAAC,MAAA,kBAAW,CAAC,OAAO,0CAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,MAAM,CAAC,MAAA,kBAAW,CAAC,OAAO,0CAAE,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,WAAW;IACxE,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,gDAAgD,EAAE;;QACrD,IAAM,aAAa,GAAG,MAAA,MAAA,kBAAW,CAAC,OAAO,0CAAE,YAAY,0CAAE,OAAO,CAAC;QAEjE,MAAM,CAAC,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,CAAC,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,MAAM,CAAC,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEtC,8DAA8D;QAC9D,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC1C,MAAM,CAAC,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/__tests__/session-consistency.test.ts"], "sourcesContent": ["import { authOptions } from '../auth';\n\ndescribe('Session Management Consistency', () => {\n  test('session and JWT maxAge should be consistent', () => {\n    const sessionMaxAge = authOptions.session?.maxAge;\n    const jwtMaxAge = authOptions.jwt?.maxAge;\n    \n    expect(sessionMaxAge).toBeDefined();\n    expect(jwtMaxAge).toBeDefined();\n    expect(sessionMaxAge).toBe(jwtMaxAge);\n    expect(sessionMaxAge).toBe(30 * 24 * 60 * 60); // 30 days\n  });\n\n  test('cookie maxAge should match session maxAge', () => {\n    const sessionMaxAge = authOptions.session?.maxAge;\n    const cookieMaxAge = authOptions.cookies?.sessionToken?.options?.maxAge;\n    \n    expect(cookieMaxAge).toBeDefined();\n    expect(cookieMaxAge).toBe(sessionMaxAge);\n  });\n\n  test('session configuration should be properly structured', () => {\n    expect(authOptions.session?.strategy).toBe('jwt');\n    expect(authOptions.session?.updateAge).toBe(24 * 60 * 60); // 24 hours\n  });\n\n  test('cookie security settings should be appropriate', () => {\n    const sessionCookie = authOptions.cookies?.sessionToken?.options;\n    \n    expect(sessionCookie?.httpOnly).toBe(true);\n    expect(sessionCookie?.sameSite).toBe('lax');\n    expect(sessionCookie?.path).toBe('/');\n    \n    // In development, secure should be false; in production, true\n    if (process.env.NODE_ENV === 'production') {\n      expect(sessionCookie?.secure).toBe(true);\n    } else {\n      expect(sessionCookie?.secure).toBe(false);\n    }\n  });\n});\n"], "version": 3}