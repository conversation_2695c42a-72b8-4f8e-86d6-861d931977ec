bf23815a1974d4551cdc86c242f15f6a
"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var PersonalizedLearningPathService_1 = require("@/lib/skills/PersonalizedLearningPathService");
describe('PersonalizedLearningPathService', function () {
    var service;
    beforeEach(function () {
        service = new PersonalizedLearningPathService_1.PersonalizedLearningPathService();
    });
    describe('Learning Path Generation', function () {
        it('should generate a personalized learning path', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, learningPath;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            currentSkills: [
                                { skill: 'javascript', level: 7, confidence: 8 },
                                { skill: 'html', level: 9, confidence: 9 },
                                { skill: 'css', level: 8, confidence: 7 },
                            ],
                            targetRole: 'Full Stack Developer',
                            timeframe: 6, // months
                            learningStyle: 'hands-on',
                            availability: 10, // hours per week
                            budget: 500, // dollars
                            preferences: {
                                preferredFormats: ['video', 'interactive'],
                                difficulty: 'intermediate',
                                certificationRequired: true,
                            },
                        };
                        return [4 /*yield*/, service.generateLearningPath(request)];
                    case 1:
                        learningPath = _a.sent();
                        expect(learningPath).toBeDefined();
                        expect(learningPath.id).toBeDefined();
                        expect(learningPath.userId).toBe('user-123');
                        expect(learningPath.targetRole).toBe('Full Stack Developer');
                        expect(learningPath.estimatedDuration).toBeDefined();
                        expect(learningPath.totalCost).toBeLessThanOrEqual(500);
                        expect(learningPath.phases).toBeDefined();
                        expect(learningPath.phases.length).toBeGreaterThan(0);
                        expect(learningPath.skillGaps).toBeDefined();
                        expect(learningPath.recommendations).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should identify skill gaps for target role', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, learningPath, reactGap;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'html', level: 5, confidence: 6 }],
                            targetRole: 'React Developer',
                            timeframe: 3,
                            learningStyle: 'visual',
                            availability: 15,
                            budget: 1000,
                        };
                        return [4 /*yield*/, service.generateLearningPath(request)];
                    case 1:
                        learningPath = _a.sent();
                        expect(learningPath.skillGaps).toBeDefined();
                        expect(learningPath.skillGaps.length).toBeGreaterThan(0);
                        reactGap = learningPath.skillGaps.find(function (gap) { return gap.skill === 'react'; });
                        expect(reactGap).toBeDefined();
                        expect(reactGap === null || reactGap === void 0 ? void 0 : reactGap.currentLevel).toBe(0);
                        expect(reactGap === null || reactGap === void 0 ? void 0 : reactGap.targetLevel).toBeGreaterThan(0);
                        expect(reactGap === null || reactGap === void 0 ? void 0 : reactGap.priority).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should create learning phases in logical order', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, learningPath, i, currentPhase, previousPhase;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'html', level: 3, confidence: 4 }],
                            targetRole: 'Frontend Developer',
                            timeframe: 12,
                            learningStyle: 'structured',
                            availability: 8,
                            budget: 2000,
                        };
                        return [4 /*yield*/, service.generateLearningPath(request)];
                    case 1:
                        learningPath = _a.sent();
                        expect(learningPath.phases.length).toBeGreaterThan(1);
                        // Check that phases have proper dependencies
                        for (i = 1; i < learningPath.phases.length; i++) {
                            currentPhase = learningPath.phases[i];
                            previousPhase = learningPath.phases[i - 1];
                            expect(currentPhase.startDate.getTime()).toBeGreaterThanOrEqual(previousPhase.endDate.getTime());
                        }
                        return [2 /*return*/];
                }
            });
        }); });
        it('should respect budget constraints', function () { return __awaiter(void 0, void 0, void 0, function () {
            var lowBudgetRequest, learningPath;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        lowBudgetRequest = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
                            targetRole: 'Full Stack Developer',
                            timeframe: 6,
                            learningStyle: 'self-paced',
                            availability: 10,
                            budget: 100, // Low budget
                        };
                        return [4 /*yield*/, service.generateLearningPath(lowBudgetRequest)];
                    case 1:
                        learningPath = _a.sent();
                        expect(learningPath.totalCost).toBeLessThanOrEqual(100);
                        expect(learningPath.resources.some(function (r) { return r.cost === 0; })).toBe(true); // Should include free resources
                        return [2 /*return*/];
                }
            });
        }); });
        it('should adapt to learning style preferences', function () { return __awaiter(void 0, void 0, void 0, function () {
            var visualLearnerRequest, learningPath, visualResources;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        visualLearnerRequest = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 6, confidence: 7 }],
                            targetRole: 'Frontend Developer',
                            timeframe: 4,
                            learningStyle: 'visual',
                            availability: 12,
                            budget: 500,
                        };
                        return [4 /*yield*/, service.generateLearningPath(visualLearnerRequest)];
                    case 1:
                        learningPath = _a.sent();
                        visualResources = learningPath.resources.filter(function (r) {
                            return r.format === 'video' || r.format === 'interactive';
                        });
                        expect(visualResources.length).toBeGreaterThan(0);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Learning Path Optimization', function () {
        it('should optimize learning path for time constraints', function () { return __awaiter(void 0, void 0, void 0, function () {
            var timeConstrainedRequest, learningPath;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        timeConstrainedRequest = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 7, confidence: 8 }],
                            targetRole: 'React Developer',
                            timeframe: 2, // Very short timeframe
                            learningStyle: 'intensive',
                            availability: 20, // High availability
                            budget: 1000,
                        };
                        return [4 /*yield*/, service.generateLearningPath(timeConstrainedRequest)];
                    case 1:
                        learningPath = _a.sent();
                        expect(learningPath.estimatedDuration).toBeLessThanOrEqual(2 * 4); // 2 months in weeks
                        expect(learningPath.phases.every(function (phase) { return phase.intensity === 'high'; })).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should suggest alternative paths when constraints are unrealistic', function () { return __awaiter(void 0, void 0, void 0, function () {
            var unrealisticRequest, learningPath;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        unrealisticRequest = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'html', level: 2, confidence: 3 }],
                            targetRole: 'Senior Full Stack Developer',
                            timeframe: 1, // Unrealistic timeframe
                            learningStyle: 'casual',
                            availability: 2, // Very low availability
                            budget: 50, // Very low budget
                        };
                        return [4 /*yield*/, service.generateLearningPath(unrealisticRequest)];
                    case 1:
                        learningPath = _a.sent();
                        expect(learningPath.alternatives).toBeDefined();
                        expect(learningPath.alternatives.length).toBeGreaterThan(0);
                        expect(learningPath.feasibilityScore).toBeLessThan(0.7); // Low feasibility
                        return [2 /*return*/];
                }
            });
        }); });
        it('should prioritize skills based on market demand and role requirements', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, learningPath, highPrioritySkills, firstPhaseSkills, hasHighPriorityInFirstPhase;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 6, confidence: 7 }],
                            targetRole: 'Full Stack Developer',
                            timeframe: 8,
                            learningStyle: 'balanced',
                            availability: 12,
                            budget: 800,
                        };
                        return [4 /*yield*/, service.generateLearningPath(request)];
                    case 1:
                        learningPath = _a.sent();
                        highPrioritySkills = learningPath.skillGaps.filter(function (gap) { return gap.priority === 'high' || gap.priority === 'critical'; });
                        expect(highPrioritySkills.length).toBeGreaterThan(0);
                        firstPhaseSkills = learningPath.phases[0].skills;
                        hasHighPriorityInFirstPhase = firstPhaseSkills.some(function (skill) {
                            return highPrioritySkills.some(function (gap) { return gap.skill === skill.name; });
                        });
                        expect(hasHighPriorityInFirstPhase).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Progress Tracking Integration', function () {
        it.skip('should track learning progress and update path accordingly', function () { return __awaiter(void 0, void 0, void 0, function () {
            var learningPath, progressUpdate, updatedPath;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.generateLearningPath({
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
                            targetRole: 'React Developer',
                            timeframe: 6,
                            learningStyle: 'structured',
                            availability: 10,
                            budget: 600,
                        })];
                    case 1:
                        learningPath = _a.sent();
                        // Debug: Check what resources are available
                        console.log('Learning path resources:', learningPath.resources.length);
                        console.log('Resource IDs:', learningPath.resources.map(function (r) { return r.id; }));
                        // Ensure we have at least one resource to complete
                        expect(learningPath.resources.length).toBeGreaterThan(0);
                        progressUpdate = {
                            userId: 'user-123',
                            pathId: learningPath.id,
                            completedResources: [learningPath.resources[0].id], // Complete at least one resource
                            skillUpdates: [
                                { skill: 'react', newLevel: 4, confidence: 6 },
                            ],
                            timeSpent: 20, // hours
                        };
                        return [4 /*yield*/, service.updateProgress(progressUpdate)];
                    case 2:
                        updatedPath = _a.sent();
                        // Debug: Check progress after update
                        console.log('Completed resources:', updatedPath.progress.completedResources);
                        console.log('Total resources:', updatedPath.resources.length);
                        console.log('Completion percentage:', updatedPath.progress.completionPercentage);
                        expect(updatedPath).toBeDefined();
                        expect(updatedPath.progress.completionPercentage).toBeGreaterThan(0);
                        expect(updatedPath.progress.timeSpent).toBe(20);
                        expect(updatedPath.estimatedTimeRemaining).toBeLessThan(learningPath.estimatedDuration);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should suggest path adjustments based on progress', function () { return __awaiter(void 0, void 0, void 0, function () {
            var learningPath, progressUpdate, updatedPath;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.generateLearningPath({
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 6, confidence: 7 }],
                            targetRole: 'Frontend Developer',
                            timeframe: 4,
                            learningStyle: 'adaptive',
                            availability: 15,
                            budget: 500,
                        })];
                    case 1:
                        learningPath = _a.sent();
                        progressUpdate = {
                            userId: 'user-123',
                            pathId: learningPath.id,
                            completedResources: learningPath.resources.slice(0, 3).map(function (r) { return r.id; }),
                            skillUpdates: [
                                { skill: 'react', newLevel: 7, confidence: 8 },
                            ],
                            timeSpent: 15, // Less time than estimated
                        };
                        return [4 /*yield*/, service.updateProgress(progressUpdate)];
                    case 2:
                        updatedPath = _a.sent();
                        expect(updatedPath.adjustments).toBeDefined();
                        expect(updatedPath.adjustments.length).toBeGreaterThan(0);
                        expect(updatedPath.adjustments.some(function (adj) { return adj.type === 'accelerate'; })).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Resource Recommendation', function () {
        it('should recommend diverse learning resources', function () { return __awaiter(void 0, void 0, void 0, function () {
            var learningPath, resourceTypes, costLevels;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.generateLearningPath({
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
                            targetRole: 'Full Stack Developer',
                            timeframe: 8,
                            learningStyle: 'mixed',
                            availability: 12,
                            budget: 1000,
                        })];
                    case 1:
                        learningPath = _a.sent();
                        resourceTypes = new Set(learningPath.resources.map(function (r) { return r.format; }));
                        expect(resourceTypes.size).toBeGreaterThan(2); // Should have multiple formats
                        costLevels = new Set(learningPath.resources.map(function (r) { return r.cost > 0 ? 'paid' : 'free'; }));
                        expect(costLevels.has('free')).toBe(true); // Should include free resources
                        return [2 /*return*/];
                }
            });
        }); });
        it('should match resources to learning preferences', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, learningPath, interactiveResources, certificationResources;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'html', level: 6, confidence: 7 }],
                            targetRole: 'Frontend Developer',
                            timeframe: 6,
                            learningStyle: 'hands-on',
                            availability: 10,
                            budget: 500,
                            preferences: {
                                preferredFormats: ['interactive', 'project'],
                                difficulty: 'intermediate',
                                certificationRequired: true,
                            },
                        };
                        return [4 /*yield*/, service.generateLearningPath(request)];
                    case 1:
                        learningPath = _a.sent();
                        interactiveResources = learningPath.resources.filter(function (r) {
                            return r.format === 'interactive' || r.format === 'project';
                        });
                        expect(interactiveResources.length).toBeGreaterThan(0);
                        certificationResources = learningPath.resources.filter(function (r) { return r.providesCertification; });
                        expect(certificationResources.length).toBeGreaterThan(0);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Milestone and Achievement System', function () {
        it('should define clear milestones for learning path', function () { return __awaiter(void 0, void 0, void 0, function () {
            var learningPath;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.generateLearningPath({
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 4, confidence: 5 }],
                            targetRole: 'React Developer',
                            timeframe: 5,
                            learningStyle: 'goal-oriented',
                            availability: 12,
                            budget: 600,
                        })];
                    case 1:
                        learningPath = _a.sent();
                        expect(learningPath.milestones).toBeDefined();
                        expect(learningPath.milestones.length).toBeGreaterThan(0);
                        learningPath.milestones.forEach(function (milestone) {
                            expect(milestone.id).toBeDefined();
                            expect(milestone.title).toBeDefined();
                            expect(milestone.description).toBeDefined();
                            expect(milestone.targetDate).toBeInstanceOf(Date);
                            expect(milestone.criteria).toBeDefined();
                            expect(milestone.criteria.length).toBeGreaterThan(0);
                        });
                        return [2 /*return*/];
                }
            });
        }); });
        it('should track milestone completion', function () { return __awaiter(void 0, void 0, void 0, function () {
            var learningPath, milestoneCompletion, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.generateLearningPath({
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
                            targetRole: 'Frontend Developer',
                            timeframe: 4,
                            learningStyle: 'structured',
                            availability: 15,
                            budget: 500,
                        })];
                    case 1:
                        learningPath = _a.sent();
                        milestoneCompletion = {
                            userId: 'user-123',
                            pathId: learningPath.id,
                            milestoneId: learningPath.milestones[0].id,
                            completedCriteria: learningPath.milestones[0].criteria.slice(0, 2),
                            evidence: ['completed-project-url', 'assessment-score-85'],
                        };
                        return [4 /*yield*/, service.completeMilestone(milestoneCompletion)];
                    case 2:
                        result = _a.sent();
                        expect(result.success).toBe(true);
                        expect(result.milestone.completed).toBe(true);
                        expect(result.milestone.completedDate).toBeInstanceOf(Date);
                        expect(result.achievements).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Adaptive Learning', function () {
        it('should adapt path based on learning velocity', function () { return __awaiter(void 0, void 0, void 0, function () {
            var learningPath, slowProgressUpdate, adaptedPath;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.generateLearningPath({
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 6, confidence: 7 }],
                            targetRole: 'Full Stack Developer',
                            timeframe: 6,
                            learningStyle: 'adaptive',
                            availability: 12,
                            budget: 800,
                        })];
                    case 1:
                        learningPath = _a.sent();
                        slowProgressUpdate = {
                            userId: 'user-123',
                            pathId: learningPath.id,
                            completedResources: [learningPath.resources[0].id],
                            skillUpdates: [
                                { skill: 'react', newLevel: 2, confidence: 4 }, // Lower than expected
                            ],
                            timeSpent: 40, // More time than estimated
                        };
                        return [4 /*yield*/, service.updateProgress(slowProgressUpdate)];
                    case 2:
                        adaptedPath = _a.sent();
                        expect(adaptedPath.adjustments).toBeDefined();
                        expect(adaptedPath.adjustments.some(function (adj) { return adj.type === 'simplify'; })).toBe(true);
                        expect(adaptedPath.estimatedTimeRemaining).toBeGreaterThan(learningPath.estimatedDuration);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should suggest additional resources when struggling', function () { return __awaiter(void 0, void 0, void 0, function () {
            var learningPath, strugglingUpdate, supportedPath, additionalResources;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.generateLearningPath({
                            userId: 'user-123',
                            currentSkills: [{ skill: 'html', level: 4, confidence: 5 }],
                            targetRole: 'Frontend Developer',
                            timeframe: 8,
                            learningStyle: 'supportive',
                            availability: 10,
                            budget: 600,
                        })];
                    case 1:
                        learningPath = _a.sent();
                        strugglingUpdate = {
                            userId: 'user-123',
                            pathId: learningPath.id,
                            completedResources: [],
                            skillUpdates: [
                                { skill: 'css', newLevel: 3, confidence: 2 }, // Low confidence
                            ],
                            timeSpent: 30,
                            difficulties: ['css-flexbox', 'css-grid'],
                        };
                        return [4 /*yield*/, service.updateProgress(strugglingUpdate)];
                    case 2:
                        supportedPath = _a.sent();
                        expect(supportedPath.adjustments).toBeDefined();
                        additionalResources = supportedPath.adjustments.filter(function (adj) { return adj.type === 'add_resource'; });
                        expect(additionalResources.length).toBeGreaterThan(0);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Integration with External Services', function () {
        it('should integrate with skill market data for relevance', function () { return __awaiter(void 0, void 0, void 0, function () {
            var learningPath;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.generateLearningPath({
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
                            targetRole: 'Full Stack Developer',
                            timeframe: 6,
                            learningStyle: 'market-driven',
                            availability: 12,
                            budget: 800,
                        })];
                    case 1:
                        learningPath = _a.sent();
                        expect(learningPath.marketRelevance).toBeDefined();
                        expect(learningPath.marketRelevance.demandScore).toBeGreaterThan(0);
                        expect(learningPath.marketRelevance.salaryImpact).toBeDefined();
                        expect(learningPath.marketRelevance.jobOpportunities).toBeGreaterThan(0);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should validate path feasibility', function () { return __awaiter(void 0, void 0, void 0, function () {
            var ambitiousRequest, learningPath;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        ambitiousRequest = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'html', level: 3, confidence: 4 }],
                            targetRole: 'Senior Full Stack Developer',
                            timeframe: 3, // Very ambitious
                            learningStyle: 'intensive',
                            availability: 40, // Full time
                            budget: 2000,
                        };
                        return [4 /*yield*/, service.generateLearningPath(ambitiousRequest)];
                    case 1:
                        learningPath = _a.sent();
                        expect(learningPath.feasibilityAnalysis).toBeDefined();
                        expect(learningPath.feasibilityAnalysis.overallScore).toBeLessThan(0.8); // Should flag as challenging
                        expect(learningPath.feasibilityAnalysis.risks).toBeDefined();
                        expect(learningPath.feasibilityAnalysis.risks.length).toBeGreaterThan(0);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Error Handling and Edge Cases', function () {
        it('should handle invalid input gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var invalidRequest;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        invalidRequest = {
                            userId: '',
                            currentSkills: [],
                            targetRole: '',
                            timeframe: -1,
                            learningStyle: 'invalid',
                            availability: -5,
                            budget: -100,
                        };
                        return [4 /*yield*/, expect(service.generateLearningPath(invalidRequest))
                                .rejects.toThrow('Invalid learning path request')];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle unknown target roles', function () { return __awaiter(void 0, void 0, void 0, function () {
            var unknownRoleRequest, learningPath;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        unknownRoleRequest = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
                            targetRole: 'Quantum Developer', // Non-existent role
                            timeframe: 6,
                            learningStyle: 'structured',
                            availability: 10,
                            budget: 500,
                        };
                        return [4 /*yield*/, service.generateLearningPath(unknownRoleRequest)];
                    case 1:
                        learningPath = _a.sent();
                        expect(learningPath).toBeDefined();
                        expect(learningPath.warnings).toBeDefined();
                        expect(learningPath.warnings.some(function (w) { return w.includes('unknown role'); })).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle service unavailability gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var originalFetch, request, learningPath;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        originalFetch = global.fetch;
                        global.fetch = jest.fn().mockRejectedValue(new Error('Service unavailable'));
                        request = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
                            targetRole: 'Frontend Developer',
                            timeframe: 6,
                            learningStyle: 'structured',
                            availability: 10,
                            budget: 500,
                        };
                        return [4 /*yield*/, service.generateLearningPath(request)];
                    case 1:
                        learningPath = _a.sent();
                        expect(learningPath).toBeDefined();
                        expect(learningPath.isOffline).toBe(true);
                        expect(learningPath.resources.length).toBeGreaterThan(0); // Should use cached/default resources
                        global.fetch = originalFetch;
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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