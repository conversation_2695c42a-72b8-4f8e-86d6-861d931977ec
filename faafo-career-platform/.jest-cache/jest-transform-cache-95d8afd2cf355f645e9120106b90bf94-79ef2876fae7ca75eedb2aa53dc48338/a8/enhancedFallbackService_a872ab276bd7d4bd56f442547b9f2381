be146630b3a212e289a0db434a99cb3d
"use strict";

/**
 * Enhanced Fallback Service
 * Provides sophisticated algorithmic fallbacks when AI services are unavailable
 */
/* istanbul ignore next */
function cov_v2h67dk8h() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/enhancedFallbackService.ts";
  var hash = "3be850a743b4224e752f9076e260c06346fd4f10";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/enhancedFallbackService.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 16
        },
        end: {
          line: 14,
          column: 1
        }
      },
      "1": {
        start: {
          line: 7,
          column: 28
        },
        end: {
          line: 7,
          column: 110
        }
      },
      "2": {
        start: {
          line: 7,
          column: 91
        },
        end: {
          line: 7,
          column: 106
        }
      },
      "3": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 13,
          column: 7
        }
      },
      "4": {
        start: {
          line: 9,
          column: 36
        },
        end: {
          line: 9,
          column: 97
        }
      },
      "5": {
        start: {
          line: 9,
          column: 42
        },
        end: {
          line: 9,
          column: 70
        }
      },
      "6": {
        start: {
          line: 9,
          column: 85
        },
        end: {
          line: 9,
          column: 95
        }
      },
      "7": {
        start: {
          line: 10,
          column: 35
        },
        end: {
          line: 10,
          column: 100
        }
      },
      "8": {
        start: {
          line: 10,
          column: 41
        },
        end: {
          line: 10,
          column: 73
        }
      },
      "9": {
        start: {
          line: 10,
          column: 88
        },
        end: {
          line: 10,
          column: 98
        }
      },
      "10": {
        start: {
          line: 11,
          column: 32
        },
        end: {
          line: 11,
          column: 116
        }
      },
      "11": {
        start: {
          line: 12,
          column: 8
        },
        end: {
          line: 12,
          column: 78
        }
      },
      "12": {
        start: {
          line: 15,
          column: 18
        },
        end: {
          line: 41,
          column: 1
        }
      },
      "13": {
        start: {
          line: 16,
          column: 12
        },
        end: {
          line: 16,
          column: 104
        }
      },
      "14": {
        start: {
          line: 16,
          column: 43
        },
        end: {
          line: 16,
          column: 68
        }
      },
      "15": {
        start: {
          line: 16,
          column: 57
        },
        end: {
          line: 16,
          column: 68
        }
      },
      "16": {
        start: {
          line: 16,
          column: 69
        },
        end: {
          line: 16,
          column: 81
        }
      },
      "17": {
        start: {
          line: 16,
          column: 119
        },
        end: {
          line: 16,
          column: 196
        }
      },
      "18": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 160
        }
      },
      "19": {
        start: {
          line: 17,
          column: 141
        },
        end: {
          line: 17,
          column: 153
        }
      },
      "20": {
        start: {
          line: 18,
          column: 23
        },
        end: {
          line: 18,
          column: 68
        }
      },
      "21": {
        start: {
          line: 18,
          column: 45
        },
        end: {
          line: 18,
          column: 65
        }
      },
      "22": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "23": {
        start: {
          line: 20,
          column: 15
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "24": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 38,
          column: 66
        }
      },
      "25": {
        start: {
          line: 21,
          column: 50
        },
        end: {
          line: 38,
          column: 66
        }
      },
      "26": {
        start: {
          line: 22,
          column: 12
        },
        end: {
          line: 22,
          column: 169
        }
      },
      "27": {
        start: {
          line: 22,
          column: 160
        },
        end: {
          line: 22,
          column: 169
        }
      },
      "28": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 52
        }
      },
      "29": {
        start: {
          line: 23,
          column: 26
        },
        end: {
          line: 23,
          column: 52
        }
      },
      "30": {
        start: {
          line: 24,
          column: 12
        },
        end: {
          line: 36,
          column: 13
        }
      },
      "31": {
        start: {
          line: 25,
          column: 32
        },
        end: {
          line: 25,
          column: 39
        }
      },
      "32": {
        start: {
          line: 25,
          column: 40
        },
        end: {
          line: 25,
          column: 46
        }
      },
      "33": {
        start: {
          line: 26,
          column: 24
        },
        end: {
          line: 26,
          column: 34
        }
      },
      "34": {
        start: {
          line: 26,
          column: 35
        },
        end: {
          line: 26,
          column: 72
        }
      },
      "35": {
        start: {
          line: 27,
          column: 24
        },
        end: {
          line: 27,
          column: 34
        }
      },
      "36": {
        start: {
          line: 27,
          column: 35
        },
        end: {
          line: 27,
          column: 45
        }
      },
      "37": {
        start: {
          line: 27,
          column: 46
        },
        end: {
          line: 27,
          column: 55
        }
      },
      "38": {
        start: {
          line: 27,
          column: 56
        },
        end: {
          line: 27,
          column: 65
        }
      },
      "39": {
        start: {
          line: 28,
          column: 24
        },
        end: {
          line: 28,
          column: 41
        }
      },
      "40": {
        start: {
          line: 28,
          column: 42
        },
        end: {
          line: 28,
          column: 55
        }
      },
      "41": {
        start: {
          line: 28,
          column: 56
        },
        end: {
          line: 28,
          column: 65
        }
      },
      "42": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 128
        }
      },
      "43": {
        start: {
          line: 30,
          column: 110
        },
        end: {
          line: 30,
          column: 116
        }
      },
      "44": {
        start: {
          line: 30,
          column: 117
        },
        end: {
          line: 30,
          column: 126
        }
      },
      "45": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 106
        }
      },
      "46": {
        start: {
          line: 31,
          column: 81
        },
        end: {
          line: 31,
          column: 97
        }
      },
      "47": {
        start: {
          line: 31,
          column: 98
        },
        end: {
          line: 31,
          column: 104
        }
      },
      "48": {
        start: {
          line: 32,
          column: 20
        },
        end: {
          line: 32,
          column: 89
        }
      },
      "49": {
        start: {
          line: 32,
          column: 57
        },
        end: {
          line: 32,
          column: 72
        }
      },
      "50": {
        start: {
          line: 32,
          column: 73
        },
        end: {
          line: 32,
          column: 80
        }
      },
      "51": {
        start: {
          line: 32,
          column: 81
        },
        end: {
          line: 32,
          column: 87
        }
      },
      "52": {
        start: {
          line: 33,
          column: 20
        },
        end: {
          line: 33,
          column: 87
        }
      },
      "53": {
        start: {
          line: 33,
          column: 47
        },
        end: {
          line: 33,
          column: 62
        }
      },
      "54": {
        start: {
          line: 33,
          column: 63
        },
        end: {
          line: 33,
          column: 78
        }
      },
      "55": {
        start: {
          line: 33,
          column: 79
        },
        end: {
          line: 33,
          column: 85
        }
      },
      "56": {
        start: {
          line: 34,
          column: 20
        },
        end: {
          line: 34,
          column: 42
        }
      },
      "57": {
        start: {
          line: 34,
          column: 30
        },
        end: {
          line: 34,
          column: 42
        }
      },
      "58": {
        start: {
          line: 35,
          column: 20
        },
        end: {
          line: 35,
          column: 33
        }
      },
      "59": {
        start: {
          line: 35,
          column: 34
        },
        end: {
          line: 35,
          column: 43
        }
      },
      "60": {
        start: {
          line: 37,
          column: 12
        },
        end: {
          line: 37,
          column: 39
        }
      },
      "61": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 38,
          column: 34
        }
      },
      "62": {
        start: {
          line: 38,
          column: 35
        },
        end: {
          line: 38,
          column: 41
        }
      },
      "63": {
        start: {
          line: 38,
          column: 54
        },
        end: {
          line: 38,
          column: 64
        }
      },
      "64": {
        start: {
          line: 39,
          column: 8
        },
        end: {
          line: 39,
          column: 35
        }
      },
      "65": {
        start: {
          line: 39,
          column: 23
        },
        end: {
          line: 39,
          column: 35
        }
      },
      "66": {
        start: {
          line: 39,
          column: 36
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "67": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 62
        }
      },
      "68": {
        start: {
          line: 43,
          column: 0
        },
        end: {
          line: 43,
          column: 41
        }
      },
      "69": {
        start: {
          line: 44,
          column: 37
        },
        end: {
          line: 44,
          column: 78
        }
      },
      "70": {
        start: {
          line: 45,
          column: 45
        },
        end: {
          line: 341,
          column: 3
        }
      },
      "71": {
        start: {
          line: 51,
          column: 4
        },
        end: {
          line: 79,
          column: 6
        }
      },
      "72": {
        start: {
          line: 52,
          column: 8
        },
        end: {
          line: 78,
          column: 11
        }
      },
      "73": {
        start: {
          line: 54,
          column: 12
        },
        end: {
          line: 54,
          column: 89
        }
      },
      "74": {
        start: {
          line: 54,
          column: 45
        },
        end: {
          line: 54,
          column: 87
        }
      },
      "75": {
        start: {
          line: 55,
          column: 12
        },
        end: {
          line: 77,
          column: 15
        }
      },
      "76": {
        start: {
          line: 56,
          column: 16
        },
        end: {
          line: 76,
          column: 17
        }
      },
      "77": {
        start: {
          line: 57,
          column: 28
        },
        end: {
          line: 57,
          column: 161
        }
      },
      "78": {
        start: {
          line: 59,
          column: 24
        },
        end: {
          line: 59,
          column: 55
        }
      },
      "79": {
        start: {
          line: 60,
          column: 24
        },
        end: {
          line: 60,
          column: 106
        }
      },
      "80": {
        start: {
          line: 61,
          column: 24
        },
        end: {
          line: 61,
          column: 103
        }
      },
      "81": {
        start: {
          line: 62,
          column: 24
        },
        end: {
          line: 62,
          column: 136
        }
      },
      "82": {
        start: {
          line: 63,
          column: 24
        },
        end: {
          line: 63,
          column: 99
        }
      },
      "83": {
        start: {
          line: 64,
          column: 24
        },
        end: {
          line: 64,
          column: 118
        }
      },
      "84": {
        start: {
          line: 65,
          column: 24
        },
        end: {
          line: 65,
          column: 105
        }
      },
      "85": {
        start: {
          line: 66,
          column: 24
        },
        end: {
          line: 75,
          column: 31
        }
      },
      "86": {
        start: {
          line: 83,
          column: 4
        },
        end: {
          line: 101,
          column: 6
        }
      },
      "87": {
        start: {
          line: 84,
          column: 20
        },
        end: {
          line: 84,
          column: 24
        }
      },
      "88": {
        start: {
          line: 85,
          column: 8
        },
        end: {
          line: 100,
          column: 15
        }
      },
      "89": {
        start: {
          line: 85,
          column: 69
        },
        end: {
          line: 100,
          column: 11
        }
      },
      "90": {
        start: {
          line: 97,
          column: 78
        },
        end: {
          line: 97,
          column: 95
        }
      },
      "91": {
        start: {
          line: 105,
          column: 4
        },
        end: {
          line: 131,
          column: 6
        }
      },
      "92": {
        start: {
          line: 106,
          column: 20
        },
        end: {
          line: 106,
          column: 24
        }
      },
      "93": {
        start: {
          line: 107,
          column: 27
        },
        end: {
          line: 107,
          column: 36
        }
      },
      "94": {
        start: {
          line: 109,
          column: 8
        },
        end: {
          line: 124,
          column: 11
        }
      },
      "95": {
        start: {
          line: 110,
          column: 12
        },
        end: {
          line: 123,
          column: 15
        }
      },
      "96": {
        start: {
          line: 111,
          column: 16
        },
        end: {
          line: 122,
          column: 17
        }
      },
      "97": {
        start: {
          line: 112,
          column: 38
        },
        end: {
          line: 112,
          column: 80
        }
      },
      "98": {
        start: {
          line: 113,
          column: 20
        },
        end: {
          line: 121,
          column: 23
        }
      },
      "99": {
        start: {
          line: 125,
          column: 8
        },
        end: {
          line: 130,
          column: 26
        }
      },
      "100": {
        start: {
          line: 127,
          column: 32
        },
        end: {
          line: 127,
          column: 62
        }
      },
      "101": {
        start: {
          line: 128,
          column: 12
        },
        end: {
          line: 128,
          column: 73
        }
      },
      "102": {
        start: {
          line: 135,
          column: 4
        },
        end: {
          line: 150,
          column: 6
        }
      },
      "103": {
        start: {
          line: 136,
          column: 23
        },
        end: {
          line: 136,
          column: 44
        }
      },
      "104": {
        start: {
          line: 137,
          column: 8
        },
        end: {
          line: 139,
          column: 9
        }
      },
      "105": {
        start: {
          line: 138,
          column: 12
        },
        end: {
          line: 138,
          column: 49
        }
      },
      "106": {
        start: {
          line: 140,
          column: 21
        },
        end: {
          line: 140,
          column: 66
        }
      },
      "107": {
        start: {
          line: 141,
          column: 25
        },
        end: {
          line: 141,
          column: 62
        }
      },
      "108": {
        start: {
          line: 142,
          column: 31
        },
        end: {
          line: 142,
          column: 82
        }
      },
      "109": {
        start: {
          line: 143,
          column: 8
        },
        end: {
          line: 149,
          column: 10
        }
      },
      "110": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 164,
          column: 6
        }
      },
      "111": {
        start: {
          line: 155,
          column: 25
        },
        end: {
          line: 155,
          column: 55
        }
      },
      "112": {
        start: {
          line: 156,
          column: 8
        },
        end: {
          line: 163,
          column: 10
        }
      },
      "113": {
        start: {
          line: 168,
          column: 4
        },
        end: {
          line: 203,
          column: 6
        }
      },
      "114": {
        start: {
          line: 169,
          column: 21
        },
        end: {
          line: 169,
          column: 23
        }
      },
      "115": {
        start: {
          line: 170,
          column: 23
        },
        end: {
          line: 170,
          column: 44
        }
      },
      "116": {
        start: {
          line: 172,
          column: 8
        },
        end: {
          line: 175,
          column: 9
        }
      },
      "117": {
        start: {
          line: 173,
          column: 12
        },
        end: {
          line: 173,
          column: 117
        }
      },
      "118": {
        start: {
          line: 174,
          column: 12
        },
        end: {
          line: 174,
          column: 113
        }
      },
      "119": {
        start: {
          line: 177,
          column: 8
        },
        end: {
          line: 180,
          column: 9
        }
      },
      "120": {
        start: {
          line: 178,
          column: 12
        },
        end: {
          line: 178,
          column: 136
        }
      },
      "121": {
        start: {
          line: 179,
          column: 12
        },
        end: {
          line: 179,
          column: 115
        }
      },
      "122": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 184,
          column: 9
        }
      },
      "123": {
        start: {
          line: 183,
          column: 12
        },
        end: {
          line: 183,
          column: 137
        }
      },
      "124": {
        start: {
          line: 186,
          column: 8
        },
        end: {
          line: 189,
          column: 9
        }
      },
      "125": {
        start: {
          line: 187,
          column: 12
        },
        end: {
          line: 187,
          column: 132
        }
      },
      "126": {
        start: {
          line: 188,
          column: 12
        },
        end: {
          line: 188,
          column: 99
        }
      },
      "127": {
        start: {
          line: 191,
          column: 8
        },
        end: {
          line: 194,
          column: 9
        }
      },
      "128": {
        start: {
          line: 192,
          column: 12
        },
        end: {
          line: 192,
          column: 104
        }
      },
      "129": {
        start: {
          line: 193,
          column: 12
        },
        end: {
          line: 193,
          column: 101
        }
      },
      "130": {
        start: {
          line: 196,
          column: 8
        },
        end: {
          line: 201,
          column: 9
        }
      },
      "131": {
        start: {
          line: 197,
          column: 12
        },
        end: {
          line: 197,
          column: 99
        }
      },
      "132": {
        start: {
          line: 198,
          column: 12
        },
        end: {
          line: 198,
          column: 113
        }
      },
      "133": {
        start: {
          line: 199,
          column: 12
        },
        end: {
          line: 199,
          column: 100
        }
      },
      "134": {
        start: {
          line: 200,
          column: 12
        },
        end: {
          line: 200,
          column: 102
        }
      },
      "135": {
        start: {
          line: 202,
          column: 8
        },
        end: {
          line: 202,
          column: 22
        }
      },
      "136": {
        start: {
          line: 207,
          column: 4
        },
        end: {
          line: 221,
          column: 6
        }
      },
      "137": {
        start: {
          line: 208,
          column: 8
        },
        end: {
          line: 209,
          column: 22
        }
      },
      "138": {
        start: {
          line: 209,
          column: 12
        },
        end: {
          line: 209,
          column: 22
        }
      },
      "139": {
        start: {
          line: 210,
          column: 23
        },
        end: {
          line: 210,
          column: 44
        }
      },
      "140": {
        start: {
          line: 211,
          column: 29
        },
        end: {
          line: 211,
          column: 53
        }
      },
      "141": {
        start: {
          line: 213,
          column: 33
        },
        end: {
          line: 213,
          column: 47
        }
      },
      "142": {
        start: {
          line: 214,
          column: 8
        },
        end: {
          line: 215,
          column: 36
        }
      },
      "143": {
        start: {
          line: 215,
          column: 12
        },
        end: {
          line: 215,
          column: 36
        }
      },
      "144": {
        start: {
          line: 216,
          column: 8
        },
        end: {
          line: 217,
          column: 37
        }
      },
      "145": {
        start: {
          line: 217,
          column: 12
        },
        end: {
          line: 217,
          column: 37
        }
      },
      "146": {
        start: {
          line: 218,
          column: 8
        },
        end: {
          line: 219,
          column: 36
        }
      },
      "147": {
        start: {
          line: 219,
          column: 12
        },
        end: {
          line: 219,
          column: 36
        }
      },
      "148": {
        start: {
          line: 220,
          column: 8
        },
        end: {
          line: 220,
          column: 62
        }
      },
      "149": {
        start: {
          line: 223,
          column: 4
        },
        end: {
          line: 231,
          column: 6
        }
      },
      "150": {
        start: {
          line: 224,
          column: 8
        },
        end: {
          line: 225,
          column: 73
        }
      },
      "151": {
        start: {
          line: 225,
          column: 12
        },
        end: {
          line: 225,
          column: 73
        }
      },
      "152": {
        start: {
          line: 226,
          column: 8
        },
        end: {
          line: 227,
          column: 68
        }
      },
      "153": {
        start: {
          line: 227,
          column: 12
        },
        end: {
          line: 227,
          column: 68
        }
      },
      "154": {
        start: {
          line: 228,
          column: 8
        },
        end: {
          line: 229,
          column: 67
        }
      },
      "155": {
        start: {
          line: 229,
          column: 12
        },
        end: {
          line: 229,
          column: 67
        }
      },
      "156": {
        start: {
          line: 230,
          column: 8
        },
        end: {
          line: 230,
          column: 64
        }
      },
      "157": {
        start: {
          line: 232,
          column: 4
        },
        end: {
          line: 238,
          column: 6
        }
      },
      "158": {
        start: {
          line: 233,
          column: 8
        },
        end: {
          line: 234,
          column: 25
        }
      },
      "159": {
        start: {
          line: 234,
          column: 12
        },
        end: {
          line: 234,
          column: 25
        }
      },
      "160": {
        start: {
          line: 235,
          column: 8
        },
        end: {
          line: 236,
          column: 28
        }
      },
      "161": {
        start: {
          line: 236,
          column: 12
        },
        end: {
          line: 236,
          column: 28
        }
      },
      "162": {
        start: {
          line: 237,
          column: 8
        },
        end: {
          line: 237,
          column: 22
        }
      },
      "163": {
        start: {
          line: 239,
          column: 4
        },
        end: {
          line: 248,
          column: 6
        }
      },
      "164": {
        start: {
          line: 240,
          column: 22
        },
        end: {
          line: 240,
          column: 83
        }
      },
      "165": {
        start: {
          line: 241,
          column: 8
        },
        end: {
          line: 243,
          column: 9
        }
      },
      "166": {
        start: {
          line: 242,
          column: 12
        },
        end: {
          line: 242,
          column: 63
        }
      },
      "167": {
        start: {
          line: 244,
          column: 8
        },
        end: {
          line: 246,
          column: 9
        }
      },
      "168": {
        start: {
          line: 245,
          column: 12
        },
        end: {
          line: 245,
          column: 60
        }
      },
      "169": {
        start: {
          line: 247,
          column: 8
        },
        end: {
          line: 247,
          column: 23
        }
      },
      "170": {
        start: {
          line: 249,
          column: 4
        },
        end: {
          line: 261,
          column: 6
        }
      },
      "171": {
        start: {
          line: 250,
          column: 25
        },
        end: {
          line: 250,
          column: 27
        }
      },
      "172": {
        start: {
          line: 251,
          column: 8
        },
        end: {
          line: 253,
          column: 9
        }
      },
      "173": {
        start: {
          line: 252,
          column: 12
        },
        end: {
          line: 252,
          column: 70
        }
      },
      "174": {
        start: {
          line: 254,
          column: 8
        },
        end: {
          line: 256,
          column: 9
        }
      },
      "175": {
        start: {
          line: 255,
          column: 12
        },
        end: {
          line: 255,
          column: 59
        }
      },
      "176": {
        start: {
          line: 257,
          column: 8
        },
        end: {
          line: 259,
          column: 9
        }
      },
      "177": {
        start: {
          line: 258,
          column: 12
        },
        end: {
          line: 258,
          column: 54
        }
      },
      "178": {
        start: {
          line: 260,
          column: 8
        },
        end: {
          line: 260,
          column: 101
        }
      },
      "179": {
        start: {
          line: 262,
          column: 4
        },
        end: {
          line: 271,
          column: 6
        }
      },
      "180": {
        start: {
          line: 264,
          column: 30
        },
        end: {
          line: 264,
          column: 91
        }
      },
      "181": {
        start: {
          line: 265,
          column: 32
        },
        end: {
          line: 265,
          column: 95
        }
      },
      "182": {
        start: {
          line: 266,
          column: 8
        },
        end: {
          line: 267,
          column: 21
        }
      },
      "183": {
        start: {
          line: 266,
          column: 50
        },
        end: {
          line: 266,
          column: 105
        }
      },
      "184": {
        start: {
          line: 267,
          column: 12
        },
        end: {
          line: 267,
          column: 21
        }
      },
      "185": {
        start: {
          line: 268,
          column: 8
        },
        end: {
          line: 269,
          column: 21
        }
      },
      "186": {
        start: {
          line: 268,
          column: 52
        },
        end: {
          line: 268,
          column: 107
        }
      },
      "187": {
        start: {
          line: 269,
          column: 12
        },
        end: {
          line: 269,
          column: 21
        }
      },
      "188": {
        start: {
          line: 270,
          column: 8
        },
        end: {
          line: 270,
          column: 17
        }
      },
      "189": {
        start: {
          line: 272,
          column: 4
        },
        end: {
          line: 280,
          column: 6
        }
      },
      "190": {
        start: {
          line: 273,
          column: 8
        },
        end: {
          line: 275,
          column: 9
        }
      },
      "191": {
        start: {
          line: 274,
          column: 12
        },
        end: {
          line: 274,
          column: 84
        }
      },
      "192": {
        start: {
          line: 276,
          column: 8
        },
        end: {
          line: 278,
          column: 9
        }
      },
      "193": {
        start: {
          line: 277,
          column: 12
        },
        end: {
          line: 277,
          column: 72
        }
      },
      "194": {
        start: {
          line: 279,
          column: 8
        },
        end: {
          line: 279,
          column: 69
        }
      },
      "195": {
        start: {
          line: 281,
          column: 4
        },
        end: {
          line: 305,
          column: 6
        }
      },
      "196": {
        start: {
          line: 282,
          column: 8
        },
        end: {
          line: 304,
          column: 10
        }
      },
      "197": {
        start: {
          line: 307,
          column: 4
        },
        end: {
          line: 310,
          column: 6
        }
      },
      "198": {
        start: {
          line: 309,
          column: 8
        },
        end: {
          line: 309,
          column: 18
        }
      },
      "199": {
        start: {
          line: 311,
          column: 4
        },
        end: {
          line: 314,
          column: 6
        }
      },
      "200": {
        start: {
          line: 313,
          column: 8
        },
        end: {
          line: 313,
          column: 18
        }
      },
      "201": {
        start: {
          line: 315,
          column: 4
        },
        end: {
          line: 318,
          column: 6
        }
      },
      "202": {
        start: {
          line: 317,
          column: 8
        },
        end: {
          line: 317,
          column: 29
        }
      },
      "203": {
        start: {
          line: 319,
          column: 4
        },
        end: {
          line: 321,
          column: 6
        }
      },
      "204": {
        start: {
          line: 320,
          column: 8
        },
        end: {
          line: 320,
          column: 106
        }
      },
      "205": {
        start: {
          line: 322,
          column: 4
        },
        end: {
          line: 324,
          column: 6
        }
      },
      "206": {
        start: {
          line: 323,
          column: 8
        },
        end: {
          line: 323,
          column: 82
        }
      },
      "207": {
        start: {
          line: 325,
          column: 4
        },
        end: {
          line: 327,
          column: 6
        }
      },
      "208": {
        start: {
          line: 326,
          column: 8
        },
        end: {
          line: 326,
          column: 74
        }
      },
      "209": {
        start: {
          line: 328,
          column: 4
        },
        end: {
          line: 330,
          column: 6
        }
      },
      "210": {
        start: {
          line: 329,
          column: 8
        },
        end: {
          line: 329,
          column: 64
        }
      },
      "211": {
        start: {
          line: 331,
          column: 4
        },
        end: {
          line: 333,
          column: 6
        }
      },
      "212": {
        start: {
          line: 332,
          column: 8
        },
        end: {
          line: 332,
          column: 22
        }
      },
      "213": {
        start: {
          line: 334,
          column: 4
        },
        end: {
          line: 336,
          column: 6
        }
      },
      "214": {
        start: {
          line: 335,
          column: 8
        },
        end: {
          line: 335,
          column: 79
        }
      },
      "215": {
        start: {
          line: 337,
          column: 4
        },
        end: {
          line: 339,
          column: 6
        }
      },
      "216": {
        start: {
          line: 338,
          column: 8
        },
        end: {
          line: 338,
          column: 86
        }
      },
      "217": {
        start: {
          line: 340,
          column: 4
        },
        end: {
          line: 340,
          column: 35
        }
      },
      "218": {
        start: {
          line: 342,
          column: 0
        },
        end: {
          line: 342,
          column: 58
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 44
          },
          end: {
            line: 6,
            column: 45
          }
        },
        loc: {
          start: {
            line: 6,
            column: 89
          },
          end: {
            line: 14,
            column: 1
          }
        },
        line: 6
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 7,
            column: 13
          },
          end: {
            line: 7,
            column: 18
          }
        },
        loc: {
          start: {
            line: 7,
            column: 26
          },
          end: {
            line: 7,
            column: 112
          }
        },
        line: 7
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 71
          }
        },
        loc: {
          start: {
            line: 7,
            column: 89
          },
          end: {
            line: 7,
            column: 108
          }
        },
        line: 7
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 8,
            column: 36
          },
          end: {
            line: 8,
            column: 37
          }
        },
        loc: {
          start: {
            line: 8,
            column: 63
          },
          end: {
            line: 13,
            column: 5
          }
        },
        line: 8
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 9,
            column: 17
          },
          end: {
            line: 9,
            column: 26
          }
        },
        loc: {
          start: {
            line: 9,
            column: 34
          },
          end: {
            line: 9,
            column: 99
          }
        },
        line: 9
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 10,
            column: 17
          },
          end: {
            line: 10,
            column: 25
          }
        },
        loc: {
          start: {
            line: 10,
            column: 33
          },
          end: {
            line: 10,
            column: 102
          }
        },
        line: 10
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 11,
            column: 17
          },
          end: {
            line: 11,
            column: 21
          }
        },
        loc: {
          start: {
            line: 11,
            column: 30
          },
          end: {
            line: 11,
            column: 118
          }
        },
        line: 11
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 15,
            column: 48
          },
          end: {
            line: 15,
            column: 49
          }
        },
        loc: {
          start: {
            line: 15,
            column: 73
          },
          end: {
            line: 41,
            column: 1
          }
        },
        line: 15
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 16,
            column: 30
          },
          end: {
            line: 16,
            column: 31
          }
        },
        loc: {
          start: {
            line: 16,
            column: 41
          },
          end: {
            line: 16,
            column: 83
          }
        },
        line: 16
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 17,
            column: 128
          },
          end: {
            line: 17,
            column: 129
          }
        },
        loc: {
          start: {
            line: 17,
            column: 139
          },
          end: {
            line: 17,
            column: 155
          }
        },
        line: 17
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 18,
            column: 13
          },
          end: {
            line: 18,
            column: 17
          }
        },
        loc: {
          start: {
            line: 18,
            column: 21
          },
          end: {
            line: 18,
            column: 70
          }
        },
        line: 18
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 31
          }
        },
        loc: {
          start: {
            line: 18,
            column: 43
          },
          end: {
            line: 18,
            column: 67
          }
        },
        line: 18
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 19,
            column: 13
          },
          end: {
            line: 19,
            column: 17
          }
        },
        loc: {
          start: {
            line: 19,
            column: 22
          },
          end: {
            line: 40,
            column: 5
          }
        },
        line: 19
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 45,
            column: 45
          },
          end: {
            line: 45,
            column: 46
          }
        },
        loc: {
          start: {
            line: 45,
            column: 57
          },
          end: {
            line: 341,
            column: 1
          }
        },
        line: 45
      },
      "14": {
        name: "EnhancedFallbackService",
        decl: {
          start: {
            line: 46,
            column: 13
          },
          end: {
            line: 46,
            column: 36
          }
        },
        loc: {
          start: {
            line: 46,
            column: 39
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 46
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 51,
            column: 55
          },
          end: {
            line: 51,
            column: 56
          }
        },
        loc: {
          start: {
            line: 51,
            column: 90
          },
          end: {
            line: 79,
            column: 5
          }
        },
        line: 51
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 52,
            column: 51
          },
          end: {
            line: 52,
            column: 52
          }
        },
        loc: {
          start: {
            line: 52,
            column: 98
          },
          end: {
            line: 78,
            column: 9
          }
        },
        line: 52
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 55,
            column: 37
          },
          end: {
            line: 55,
            column: 38
          }
        },
        loc: {
          start: {
            line: 55,
            column: 51
          },
          end: {
            line: 77,
            column: 13
          }
        },
        line: 55
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 83,
            column: 63
          },
          end: {
            line: 83,
            column: 64
          }
        },
        loc: {
          start: {
            line: 83,
            column: 93
          },
          end: {
            line: 101,
            column: 5
          }
        },
        line: 83
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 85,
            column: 50
          },
          end: {
            line: 85,
            column: 51
          }
        },
        loc: {
          start: {
            line: 85,
            column: 67
          },
          end: {
            line: 100,
            column: 13
          }
        },
        line: 85
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 97,
            column: 61
          },
          end: {
            line: 97,
            column: 62
          }
        },
        loc: {
          start: {
            line: 97,
            column: 76
          },
          end: {
            line: 97,
            column: 97
          }
        },
        line: 97
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 105,
            column: 55
          },
          end: {
            line: 105,
            column: 56
          }
        },
        loc: {
          start: {
            line: 105,
            column: 95
          },
          end: {
            line: 131,
            column: 5
          }
        },
        line: 105
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 109,
            column: 47
          },
          end: {
            line: 109,
            column: 48
          }
        },
        loc: {
          start: {
            line: 109,
            column: 64
          },
          end: {
            line: 124,
            column: 9
          }
        },
        line: 109
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 110,
            column: 36
          },
          end: {
            line: 110,
            column: 37
          }
        },
        loc: {
          start: {
            line: 110,
            column: 51
          },
          end: {
            line: 123,
            column: 13
          }
        },
        line: 110
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 126,
            column: 18
          },
          end: {
            line: 126,
            column: 19
          }
        },
        loc: {
          start: {
            line: 126,
            column: 34
          },
          end: {
            line: 129,
            column: 9
          }
        },
        line: 126
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 135,
            column: 66
          },
          end: {
            line: 135,
            column: 67
          }
        },
        loc: {
          start: {
            line: 135,
            column: 117
          },
          end: {
            line: 150,
            column: 5
          }
        },
        line: 135
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 154,
            column: 53
          },
          end: {
            line: 154,
            column: 54
          }
        },
        loc: {
          start: {
            line: 154,
            column: 93
          },
          end: {
            line: 164,
            column: 5
          }
        },
        line: 154
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 168,
            column: 57
          },
          end: {
            line: 168,
            column: 58
          }
        },
        loc: {
          start: {
            line: 168,
            column: 108
          },
          end: {
            line: 203,
            column: 5
          }
        },
        line: 168
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 207,
            column: 58
          },
          end: {
            line: 207,
            column: 59
          }
        },
        loc: {
          start: {
            line: 207,
            column: 98
          },
          end: {
            line: 221,
            column: 5
          }
        },
        line: 207
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 223,
            column: 50
          },
          end: {
            line: 223,
            column: 51
          }
        },
        loc: {
          start: {
            line: 223,
            column: 72
          },
          end: {
            line: 231,
            column: 5
          }
        },
        line: 223
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 232,
            column: 49
          },
          end: {
            line: 232,
            column: 50
          }
        },
        loc: {
          start: {
            line: 232,
            column: 76
          },
          end: {
            line: 238,
            column: 5
          }
        },
        line: 232
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 239,
            column: 53
          },
          end: {
            line: 239,
            column: 54
          }
        },
        loc: {
          start: {
            line: 239,
            column: 70
          },
          end: {
            line: 248,
            column: 5
          }
        },
        line: 239
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 249,
            column: 58
          },
          end: {
            line: 249,
            column: 59
          }
        },
        loc: {
          start: {
            line: 249,
            column: 75
          },
          end: {
            line: 261,
            column: 5
          }
        },
        line: 249
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 262,
            column: 56
          },
          end: {
            line: 262,
            column: 57
          }
        },
        loc: {
          start: {
            line: 262,
            column: 73
          },
          end: {
            line: 271,
            column: 5
          }
        },
        line: 262
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 266,
            column: 33
          },
          end: {
            line: 266,
            column: 34
          }
        },
        loc: {
          start: {
            line: 266,
            column: 48
          },
          end: {
            line: 266,
            column: 107
          }
        },
        line: 266
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 268,
            column: 35
          },
          end: {
            line: 268,
            column: 36
          }
        },
        loc: {
          start: {
            line: 268,
            column: 50
          },
          end: {
            line: 268,
            column: 109
          }
        },
        line: 268
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 272,
            column: 61
          },
          end: {
            line: 272,
            column: 62
          }
        },
        loc: {
          start: {
            line: 272,
            column: 88
          },
          end: {
            line: 280,
            column: 5
          }
        },
        line: 272
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 281,
            column: 53
          },
          end: {
            line: 281,
            column: 54
          }
        },
        loc: {
          start: {
            line: 281,
            column: 65
          },
          end: {
            line: 305,
            column: 5
          }
        },
        line: 281
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 307,
            column: 51
          },
          end: {
            line: 307,
            column: 52
          }
        },
        loc: {
          start: {
            line: 307,
            column: 78
          },
          end: {
            line: 310,
            column: 5
          }
        },
        line: 307
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 311,
            column: 55
          },
          end: {
            line: 311,
            column: 56
          }
        },
        loc: {
          start: {
            line: 311,
            column: 73
          },
          end: {
            line: 314,
            column: 5
          }
        },
        line: 311
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 315,
            column: 56
          },
          end: {
            line: 315,
            column: 57
          }
        },
        loc: {
          start: {
            line: 315,
            column: 87
          },
          end: {
            line: 318,
            column: 5
          }
        },
        line: 315
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 319,
            column: 57
          },
          end: {
            line: 319,
            column: 58
          }
        },
        loc: {
          start: {
            line: 319,
            column: 77
          },
          end: {
            line: 321,
            column: 5
          }
        },
        line: 319
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 322,
            column: 52
          },
          end: {
            line: 322,
            column: 53
          }
        },
        loc: {
          start: {
            line: 322,
            column: 74
          },
          end: {
            line: 324,
            column: 5
          }
        },
        line: 322
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 325,
            column: 53
          },
          end: {
            line: 325,
            column: 54
          }
        },
        loc: {
          start: {
            line: 325,
            column: 75
          },
          end: {
            line: 327,
            column: 5
          }
        },
        line: 325
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 328,
            column: 50
          },
          end: {
            line: 328,
            column: 51
          }
        },
        loc: {
          start: {
            line: 328,
            column: 72
          },
          end: {
            line: 330,
            column: 5
          }
        },
        line: 328
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 331,
            column: 52
          },
          end: {
            line: 331,
            column: 53
          }
        },
        loc: {
          start: {
            line: 331,
            column: 74
          },
          end: {
            line: 333,
            column: 5
          }
        },
        line: 331
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 334,
            column: 62
          },
          end: {
            line: 334,
            column: 63
          }
        },
        loc: {
          start: {
            line: 334,
            column: 84
          },
          end: {
            line: 336,
            column: 5
          }
        },
        line: 334
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 337,
            column: 53
          },
          end: {
            line: 337,
            column: 54
          }
        },
        loc: {
          start: {
            line: 337,
            column: 75
          },
          end: {
            line: 339,
            column: 5
          }
        },
        line: 337
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 16
          },
          end: {
            line: 14,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 21
          }
        }, {
          start: {
            line: 6,
            column: 25
          },
          end: {
            line: 6,
            column: 39
          }
        }, {
          start: {
            line: 6,
            column: 44
          },
          end: {
            line: 14,
            column: 1
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 7,
            column: 35
          },
          end: {
            line: 7,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 56
          },
          end: {
            line: 7,
            column: 61
          }
        }, {
          start: {
            line: 7,
            column: 64
          },
          end: {
            line: 7,
            column: 109
          }
        }],
        line: 7
      },
      "2": {
        loc: {
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 17
          }
        }, {
          start: {
            line: 8,
            column: 22
          },
          end: {
            line: 8,
            column: 33
          }
        }],
        line: 8
      },
      "3": {
        loc: {
          start: {
            line: 11,
            column: 32
          },
          end: {
            line: 11,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 11,
            column: 46
          },
          end: {
            line: 11,
            column: 67
          }
        }, {
          start: {
            line: 11,
            column: 70
          },
          end: {
            line: 11,
            column: 115
          }
        }],
        line: 11
      },
      "4": {
        loc: {
          start: {
            line: 12,
            column: 51
          },
          end: {
            line: 12,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 12,
            column: 51
          },
          end: {
            line: 12,
            column: 61
          }
        }, {
          start: {
            line: 12,
            column: 65
          },
          end: {
            line: 12,
            column: 67
          }
        }],
        line: 12
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 18
          },
          end: {
            line: 41,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 19
          },
          end: {
            line: 15,
            column: 23
          }
        }, {
          start: {
            line: 15,
            column: 27
          },
          end: {
            line: 15,
            column: 43
          }
        }, {
          start: {
            line: 15,
            column: 48
          },
          end: {
            line: 41,
            column: 1
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 16,
            column: 43
          },
          end: {
            line: 16,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 43
          },
          end: {
            line: 16,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "7": {
        loc: {
          start: {
            line: 16,
            column: 134
          },
          end: {
            line: 16,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 16,
            column: 167
          },
          end: {
            line: 16,
            column: 175
          }
        }, {
          start: {
            line: 16,
            column: 178
          },
          end: {
            line: 16,
            column: 184
          }
        }],
        line: 16
      },
      "8": {
        loc: {
          start: {
            line: 17,
            column: 74
          },
          end: {
            line: 17,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 74
          },
          end: {
            line: 17,
            column: 102
          }
        }, {
          start: {
            line: 17,
            column: 107
          },
          end: {
            line: 17,
            column: 155
          }
        }],
        line: 17
      },
      "9": {
        loc: {
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 20
      },
      "10": {
        loc: {
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 21,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 21,
            column: 16
          }
        }, {
          start: {
            line: 21,
            column: 21
          },
          end: {
            line: 21,
            column: 44
          }
        }],
        line: 21
      },
      "11": {
        loc: {
          start: {
            line: 21,
            column: 28
          },
          end: {
            line: 21,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 28
          },
          end: {
            line: 21,
            column: 33
          }
        }, {
          start: {
            line: 21,
            column: 38
          },
          end: {
            line: 21,
            column: 43
          }
        }],
        line: 21
      },
      "12": {
        loc: {
          start: {
            line: 22,
            column: 12
          },
          end: {
            line: 22,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 12
          },
          end: {
            line: 22,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "13": {
        loc: {
          start: {
            line: 22,
            column: 23
          },
          end: {
            line: 22,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 23
          },
          end: {
            line: 22,
            column: 24
          }
        }, {
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 125
          }
        }, {
          start: {
            line: 22,
            column: 130
          },
          end: {
            line: 22,
            column: 158
          }
        }],
        line: 22
      },
      "14": {
        loc: {
          start: {
            line: 22,
            column: 33
          },
          end: {
            line: 22,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 45
          },
          end: {
            line: 22,
            column: 56
          }
        }, {
          start: {
            line: 22,
            column: 59
          },
          end: {
            line: 22,
            column: 125
          }
        }],
        line: 22
      },
      "15": {
        loc: {
          start: {
            line: 22,
            column: 59
          },
          end: {
            line: 22,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 116
          }
        }, {
          start: {
            line: 22,
            column: 119
          },
          end: {
            line: 22,
            column: 125
          }
        }],
        line: 22
      },
      "16": {
        loc: {
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 77
          }
        }, {
          start: {
            line: 22,
            column: 82
          },
          end: {
            line: 22,
            column: 115
          }
        }],
        line: 22
      },
      "17": {
        loc: {
          start: {
            line: 22,
            column: 82
          },
          end: {
            line: 22,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 83
          },
          end: {
            line: 22,
            column: 98
          }
        }, {
          start: {
            line: 22,
            column: 103
          },
          end: {
            line: 22,
            column: 112
          }
        }],
        line: 22
      },
      "18": {
        loc: {
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 23,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 23,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "19": {
        loc: {
          start: {
            line: 24,
            column: 12
          },
          end: {
            line: 36,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 25,
            column: 23
          }
        }, {
          start: {
            line: 25,
            column: 24
          },
          end: {
            line: 25,
            column: 46
          }
        }, {
          start: {
            line: 26,
            column: 16
          },
          end: {
            line: 26,
            column: 72
          }
        }, {
          start: {
            line: 27,
            column: 16
          },
          end: {
            line: 27,
            column: 65
          }
        }, {
          start: {
            line: 28,
            column: 16
          },
          end: {
            line: 28,
            column: 65
          }
        }, {
          start: {
            line: 29,
            column: 16
          },
          end: {
            line: 35,
            column: 43
          }
        }],
        line: 24
      },
      "20": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 74
          }
        }, {
          start: {
            line: 30,
            column: 79
          },
          end: {
            line: 30,
            column: 90
          }
        }, {
          start: {
            line: 30,
            column: 94
          },
          end: {
            line: 30,
            column: 105
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 30,
            column: 42
          },
          end: {
            line: 30,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 42
          },
          end: {
            line: 30,
            column: 54
          }
        }, {
          start: {
            line: 30,
            column: 58
          },
          end: {
            line: 30,
            column: 73
          }
        }],
        line: 30
      },
      "23": {
        loc: {
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "24": {
        loc: {
          start: {
            line: 31,
            column: 24
          },
          end: {
            line: 31,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 31,
            column: 24
          },
          end: {
            line: 31,
            column: 35
          }
        }, {
          start: {
            line: 31,
            column: 40
          },
          end: {
            line: 31,
            column: 42
          }
        }, {
          start: {
            line: 31,
            column: 47
          },
          end: {
            line: 31,
            column: 59
          }
        }, {
          start: {
            line: 31,
            column: 63
          },
          end: {
            line: 31,
            column: 75
          }
        }],
        line: 31
      },
      "25": {
        loc: {
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "26": {
        loc: {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 35
          }
        }, {
          start: {
            line: 32,
            column: 39
          },
          end: {
            line: 32,
            column: 53
          }
        }],
        line: 32
      },
      "27": {
        loc: {
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "28": {
        loc: {
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 25
          }
        }, {
          start: {
            line: 33,
            column: 29
          },
          end: {
            line: 33,
            column: 43
          }
        }],
        line: 33
      },
      "29": {
        loc: {
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "30": {
        loc: {
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "31": {
        loc: {
          start: {
            line: 39,
            column: 52
          },
          end: {
            line: 39,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 60
          },
          end: {
            line: 39,
            column: 65
          }
        }, {
          start: {
            line: 39,
            column: 68
          },
          end: {
            line: 39,
            column: 74
          }
        }],
        line: 39
      },
      "32": {
        loc: {
          start: {
            line: 54,
            column: 12
          },
          end: {
            line: 54,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 54,
            column: 12
          },
          end: {
            line: 54,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 54
      },
      "33": {
        loc: {
          start: {
            line: 56,
            column: 16
          },
          end: {
            line: 76,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 57,
            column: 20
          },
          end: {
            line: 57,
            column: 161
          }
        }, {
          start: {
            line: 58,
            column: 20
          },
          end: {
            line: 75,
            column: 31
          }
        }],
        line: 56
      },
      "34": {
        loc: {
          start: {
            line: 111,
            column: 16
          },
          end: {
            line: 122,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 111,
            column: 16
          },
          end: {
            line: 122,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 111
      },
      "35": {
        loc: {
          start: {
            line: 111,
            column: 20
          },
          end: {
            line: 111,
            column: 98
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 111,
            column: 20
          },
          end: {
            line: 111,
            column: 48
          }
        }, {
          start: {
            line: 111,
            column: 52
          },
          end: {
            line: 111,
            column: 98
          }
        }],
        line: 111
      },
      "36": {
        loc: {
          start: {
            line: 137,
            column: 8
          },
          end: {
            line: 139,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 137,
            column: 8
          },
          end: {
            line: 139,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 137
      },
      "37": {
        loc: {
          start: {
            line: 172,
            column: 8
          },
          end: {
            line: 175,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 172,
            column: 8
          },
          end: {
            line: 175,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 172
      },
      "38": {
        loc: {
          start: {
            line: 177,
            column: 8
          },
          end: {
            line: 180,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 177,
            column: 8
          },
          end: {
            line: 180,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 177
      },
      "39": {
        loc: {
          start: {
            line: 177,
            column: 12
          },
          end: {
            line: 177,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 177,
            column: 12
          },
          end: {
            line: 177,
            column: 20
          }
        }, {
          start: {
            line: 177,
            column: 24
          },
          end: {
            line: 177,
            column: 53
          }
        }],
        line: 177
      },
      "40": {
        loc: {
          start: {
            line: 182,
            column: 8
          },
          end: {
            line: 184,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 182,
            column: 8
          },
          end: {
            line: 184,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 182
      },
      "41": {
        loc: {
          start: {
            line: 182,
            column: 12
          },
          end: {
            line: 182,
            column: 127
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 182,
            column: 12
          },
          end: {
            line: 182,
            column: 45
          }
        }, {
          start: {
            line: 182,
            column: 49
          },
          end: {
            line: 182,
            column: 127
          }
        }],
        line: 182
      },
      "42": {
        loc: {
          start: {
            line: 182,
            column: 50
          },
          end: {
            line: 182,
            column: 121
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 182,
            column: 93
          },
          end: {
            line: 182,
            column: 99
          }
        }, {
          start: {
            line: 182,
            column: 102
          },
          end: {
            line: 182,
            column: 121
          }
        }],
        line: 182
      },
      "43": {
        loc: {
          start: {
            line: 182,
            column: 50
          },
          end: {
            line: 182,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 182,
            column: 50
          },
          end: {
            line: 182,
            column: 67
          }
        }, {
          start: {
            line: 182,
            column: 71
          },
          end: {
            line: 182,
            column: 90
          }
        }],
        line: 182
      },
      "44": {
        loc: {
          start: {
            line: 186,
            column: 8
          },
          end: {
            line: 189,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 186,
            column: 8
          },
          end: {
            line: 189,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 186
      },
      "45": {
        loc: {
          start: {
            line: 191,
            column: 8
          },
          end: {
            line: 194,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 191,
            column: 8
          },
          end: {
            line: 194,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 191
      },
      "46": {
        loc: {
          start: {
            line: 196,
            column: 8
          },
          end: {
            line: 201,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 196,
            column: 8
          },
          end: {
            line: 201,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 196
      },
      "47": {
        loc: {
          start: {
            line: 208,
            column: 8
          },
          end: {
            line: 209,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 208,
            column: 8
          },
          end: {
            line: 209,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 208
      },
      "48": {
        loc: {
          start: {
            line: 214,
            column: 8
          },
          end: {
            line: 215,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 214,
            column: 8
          },
          end: {
            line: 215,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 214
      },
      "49": {
        loc: {
          start: {
            line: 216,
            column: 8
          },
          end: {
            line: 217,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 216,
            column: 8
          },
          end: {
            line: 217,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 216
      },
      "50": {
        loc: {
          start: {
            line: 218,
            column: 8
          },
          end: {
            line: 219,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 218,
            column: 8
          },
          end: {
            line: 219,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 218
      },
      "51": {
        loc: {
          start: {
            line: 224,
            column: 8
          },
          end: {
            line: 225,
            column: 73
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 224,
            column: 8
          },
          end: {
            line: 225,
            column: 73
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 224
      },
      "52": {
        loc: {
          start: {
            line: 226,
            column: 8
          },
          end: {
            line: 227,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 226,
            column: 8
          },
          end: {
            line: 227,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 226
      },
      "53": {
        loc: {
          start: {
            line: 228,
            column: 8
          },
          end: {
            line: 229,
            column: 67
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 228,
            column: 8
          },
          end: {
            line: 229,
            column: 67
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 228
      },
      "54": {
        loc: {
          start: {
            line: 233,
            column: 8
          },
          end: {
            line: 234,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 233,
            column: 8
          },
          end: {
            line: 234,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 233
      },
      "55": {
        loc: {
          start: {
            line: 235,
            column: 8
          },
          end: {
            line: 236,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 235,
            column: 8
          },
          end: {
            line: 236,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 235
      },
      "56": {
        loc: {
          start: {
            line: 241,
            column: 8
          },
          end: {
            line: 243,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 241,
            column: 8
          },
          end: {
            line: 243,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 241
      },
      "57": {
        loc: {
          start: {
            line: 244,
            column: 8
          },
          end: {
            line: 246,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 244,
            column: 8
          },
          end: {
            line: 246,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 244
      },
      "58": {
        loc: {
          start: {
            line: 251,
            column: 8
          },
          end: {
            line: 253,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 251,
            column: 8
          },
          end: {
            line: 253,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 251
      },
      "59": {
        loc: {
          start: {
            line: 254,
            column: 8
          },
          end: {
            line: 256,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 254,
            column: 8
          },
          end: {
            line: 256,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 254
      },
      "60": {
        loc: {
          start: {
            line: 257,
            column: 8
          },
          end: {
            line: 259,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 257,
            column: 8
          },
          end: {
            line: 259,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 257
      },
      "61": {
        loc: {
          start: {
            line: 260,
            column: 15
          },
          end: {
            line: 260,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 260,
            column: 39
          },
          end: {
            line: 260,
            column: 49
          }
        }, {
          start: {
            line: 260,
            column: 52
          },
          end: {
            line: 260,
            column: 100
          }
        }],
        line: 260
      },
      "62": {
        loc: {
          start: {
            line: 266,
            column: 8
          },
          end: {
            line: 267,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 266,
            column: 8
          },
          end: {
            line: 267,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 266
      },
      "63": {
        loc: {
          start: {
            line: 268,
            column: 8
          },
          end: {
            line: 269,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 268,
            column: 8
          },
          end: {
            line: 269,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 268
      },
      "64": {
        loc: {
          start: {
            line: 273,
            column: 8
          },
          end: {
            line: 275,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 273,
            column: 8
          },
          end: {
            line: 275,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 273
      },
      "65": {
        loc: {
          start: {
            line: 276,
            column: 8
          },
          end: {
            line: 278,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 276,
            column: 8
          },
          end: {
            line: 278,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 276
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/enhancedFallbackService.ts",
      mappings: ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGH,+EAAsG;AAqEtG;IAAA;IA8WA,CAAC;IA7WC;;OAEG;IACU,gDAAwB,GAArC;0CAIG,OAAO,YAHR,SAA6B,EAC7B,QAA4B,EAC5B,cAAiD;;YAAjD,+BAAA,EAAA,yCAAiD;;;4BAGtB,qBAAM,2DAA4B,CAAC,6BAA6B,CACzF,SAAS,EACT,QAAQ,CACT,EAAA;;wBAHK,kBAAkB,GAAG,SAG1B;wBAGK,qBAAqB,GAAG,IAAI,CAAC,gCAAgC,CAAC,kBAAkB,CAAC,CAAC;wBAGlF,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;wBAG/E,2BAA2B,GAAG,IAAI,CAAC,mCAAmC,CAC1E,kBAAkB,EAClB,QAAQ,EACR,SAAS,CACV,CAAC;wBAGI,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;wBAG3E,kBAAkB,GAAG,IAAI,CAAC,0BAA0B,CACxD,kBAAkB,EAClB,QAAQ,EACR,SAAS,CACV,CAAC;wBAGI,eAAe,GAAG,IAAI,CAAC,2BAA2B,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;wBAEvF,sBAAO;gCACL,qBAAqB,uBAAA;gCACrB,gBAAgB,kBAAA;gCAChB,2BAA2B,6BAAA;gCAC3B,cAAc,gBAAA;gCACd,kBAAkB,oBAAA;gCAClB,eAAe,iBAAA;gCACf,cAAc,gBAAA;gCACd,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;6BACtC,EAAC;;;;KACH;IAED;;OAEG;IACY,wDAAgC,GAA/C,UACE,kBAA4C;QAD9C,iBAmBC;QAhBC,OAAO,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,CAAC;YAClD,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,IAAI;YACjC,eAAe,EAAE,KAAK,CAAC,UAAU;YACjC,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,WAAW,EAAE;gBACX,GAAG,EAAE,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK;gBACvC,GAAG,EAAE,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM;gBACxC,QAAQ,EAAE,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ;aAChD;YACD,aAAa,EAAE,KAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC;YAClF,oBAAoB,EAAE,KAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,eAAe,CAAC;YACjG,gBAAgB,EAAE,KAAK,CAAC,iBAAiB;YACzC,eAAe,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,KAAK,EAAT,CAAS,CAAC;YAClE,cAAc,EAAE,KAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC;YAClD,mBAAmB,EAAE,KAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC;SAC7D,CAAC,EAfiD,CAejD,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACY,gDAAwB,GAAvC,UACE,kBAA4C,EAC5C,QAA4B;QAF9B,iBA+BC;QA3BC,IAAM,YAAY,GAAG,IAAI,GAAG,EAA4B,CAAC;QAEzD,+CAA+C;QAC/C,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,UAAA,KAAK;YAC1C,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,GAAG;gBACzB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;oBACpF,IAAM,WAAW,GAAG,KAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBAE9D,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE;wBAC1B,KAAK,EAAE,GAAG,CAAC,KAAK;wBAChB,YAAY,EAAE,GAAG,CAAC,YAAY;wBAC9B,WAAW,EAAE,GAAG,CAAC,aAAa;wBAC9B,QAAQ,EAAE,GAAG,CAAC,QAAQ;wBACtB,YAAY,EAAE,GAAG,CAAC,qBAAqB;wBACvC,mBAAmB,EAAE,KAAI,CAAC,8BAA8B,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,QAAQ,CAAC;wBACjF,WAAW,aAAA;qBACZ,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;aACrC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;YACT,IAAM,aAAa,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;YACrD,OAAO,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QAC/D,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,oBAAoB;IACvC,CAAC;IAED;;OAEG;IACY,2DAAmC,GAAlD,UACE,kBAA4C,EAC5C,QAA4B,EAC5B,SAA6B;QAE7B,IAAM,QAAQ,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACvC,CAAC;QAED,IAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC7D,IAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;QACzD,IAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAE7E,OAAO;YACL,aAAa,EAAE,QAAQ,CAAC,iBAAiB;YACzC,MAAM,QAAA;YACN,gBAAgB,kBAAA;YAChB,UAAU,YAAA;YACV,kBAAkB,EAAE,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC;SAC9D,CAAC;IACJ,CAAC;IAED;;OAEG;IACY,8CAAsB,GAArC,UACE,kBAA4C,EAC5C,QAA4B;QAE5B,IAAM,UAAU,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAElD,OAAO;YACL,cAAc,EAAE,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC;YACtD,cAAc,EAAE,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC;YACvD,YAAY,EAAE,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC;YAClD,eAAe,EAAE,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC;YACvD,uBAAuB,EAAE,IAAI,CAAC,+BAA+B,CAAC,UAAU,CAAC;YACzE,gBAAgB,EAAE,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC;SAC1D,CAAC;IACJ,CAAC;IAED;;OAEG;IACY,kDAA0B,GAAzC,UACE,kBAA4C,EAC5C,QAA4B,EAC5B,SAA6B;QAE7B,IAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAM,QAAQ,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;QAEvC,6BAA6B;QAC7B,IAAI,QAAQ,CAAC,MAAM,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,2FAA2F,CAAC,CAAC;YACzG,MAAM,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;QACvG,CAAC;QAED,2BAA2B;QAC3B,IAAI,QAAQ,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,8GAA8G,CAAC,CAAC;YAC5H,MAAM,CAAC,IAAI,CAAC,yFAAyF,CAAC,CAAC;QACzG,CAAC;QAED,kBAAkB;QAClB,IAAI,QAAQ,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,IAAI,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,UAAU,IAAG,EAAE,EAAE,CAAC;YACnE,MAAM,CAAC,IAAI,CAAC,+GAA+G,CAAC,CAAC;QAC/H,CAAC;QAED,wBAAwB;QACxB,IAAI,QAAQ,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,0GAA0G,CAAC,CAAC;YACxH,MAAM,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;QACzF,CAAC;QAED,6BAA6B;QAC7B,IAAI,QAAQ,CAAC,MAAM,CAAC,gBAAgB,GAAG,EAAE,EAAE,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAC;YAC5F,MAAM,CAAC,IAAI,CAAC,2EAA2E,CAAC,CAAC;QAC3F,CAAC;QAED,oCAAoC;QACpC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;YACvF,MAAM,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;YACrG,MAAM,CAAC,IAAI,CAAC,0EAA0E,CAAC,CAAC;YACxF,MAAM,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC;QAC5F,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACY,mDAA2B,GAA1C,UACE,kBAA4C,EAC5C,QAA4B;QAE5B,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAE/C,IAAM,QAAQ,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;QACvC,IAAM,cAAc,GAAG,QAAQ,CAAC,eAAe,CAAC;QAEhD,+BAA+B;QAC/B,IAAI,kBAAkB,GAAG,cAAc,CAAC;QAExC,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC;YAAE,kBAAkB,IAAI,CAAC,CAAC;QAC5D,IAAI,QAAQ,CAAC,MAAM,CAAC,cAAc,IAAI,EAAE;YAAE,kBAAkB,IAAI,EAAE,CAAC;QACnE,IAAI,kBAAkB,CAAC,MAAM,IAAI,CAAC;YAAE,kBAAkB,IAAI,CAAC,CAAC;QAE5D,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC,sBAAsB;IAC/E,CAAC;IAED,iBAAiB;IACF,2CAAmB,GAAlC,UAAmC,UAAkB;QACnD,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,UAAG,UAAU,iCAA8B,CAAC;QACzE,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,UAAG,UAAU,4BAAyB,CAAC;QACpE,IAAI,UAAU,IAAI,CAAC;YAAE,OAAO,UAAG,UAAU,2BAAwB,CAAC;QAClE,OAAO,UAAG,UAAU,4BAAyB,CAAC;IAChD,CAAC;IAEc,0CAAkB,GAAjC,UAAkC,eAAuB;QACvD,IAAI,eAAe,IAAI,CAAC;YAAE,OAAO,KAAK,CAAC;QACvC,IAAI,eAAe,IAAI,CAAC;YAAE,OAAO,QAAQ,CAAC;QAC1C,OAAO,MAAM,CAAC;IAChB,CAAC;IAEc,8CAAsB,GAArC,UAAsC,KAA6B;QACjE,IAAM,OAAO,GAAG,CAAC,4BAA4B,EAAE,6BAA6B,CAAC,CAAC;QAE9E,IAAI,KAAK,CAAC,YAAY,CAAC,cAAc,IAAI,EAAE,EAAE,CAAC;YAC5C,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,KAAK,CAAC,YAAY,CAAC,qBAAqB,IAAI,EAAE,EAAE,CAAC;YACnD,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAClD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEc,mDAA2B,GAA1C,UAA2C,KAA6B;QACtE,IAAM,UAAU,GAAG,EAAE,CAAC;QAEtB,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,UAAU,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,KAAK,CAAC,YAAY,CAAC,qBAAqB,GAAG,EAAE,EAAE,CAAC;YAClD,UAAU,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,gBAAgB,IAAI,CAAC,EAAE,CAAC;YACzD,UAAU,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,oBAAoB,EAAE,wBAAwB,CAAC,CAAC;IAC/F,CAAC;IAEc,iDAAyB,GAAxC,UAAyC,KAAa;QACpD,sCAAsC;QACtC,IAAM,eAAe,GAAG,CAAC,OAAO,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;QACtF,IAAM,iBAAiB,GAAG,CAAC,iBAAiB,EAAE,oBAAoB,EAAE,oBAAoB,CAAC,CAAC;QAE1F,IAAI,eAAe,CAAC,IAAI,CAAC,UAAA,GAAG,IAAI,OAAA,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,EAA/C,CAA+C,CAAC;YAAE,OAAO,CAAC,CAAC;QAC3F,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAA,GAAG,IAAI,OAAA,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,EAA/C,CAA+C,CAAC;YAAE,OAAO,CAAC,CAAC;QAC7F,OAAO,CAAC,CAAC;IACX,CAAC;IAEc,sDAA8B,GAA7C,UAA8C,KAAa,EAAE,QAAmC;QAC9F,IAAI,QAAQ,KAAK,MAAM,EAAE,CAAC;YACxB,OAAO,gEAAgE,CAAC;QAC1E,CAAC;QACD,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC1B,OAAO,oDAAoD,CAAC;QAC9D,CAAC;QACD,OAAO,qDAAqD,CAAC;IAC/D,CAAC;IAEc,8CAAsB,GAArC;QACE,OAAO;YACL,aAAa,EAAE,aAAa;YAC5B,MAAM,EAAE;gBACN;oBACE,KAAK,EAAE,CAAC;oBACR,IAAI,EAAE,qBAAqB;oBAC3B,QAAQ,EAAE,WAAW;oBACrB,KAAK,EAAE,CAAC,eAAe,EAAE,aAAa,CAAC;oBACvC,YAAY,EAAE,CAAC,8BAA8B,EAAE,qBAAqB,CAAC;oBACrE,eAAe,EAAE,CAAC,uBAAuB,EAAE,8BAA8B,CAAC;iBAC3E;aACF;YACD,gBAAgB,EAAE,aAAa;YAC/B,UAAU,EAAE;gBACV;oBACE,IAAI,EAAE,CAAC;oBACP,KAAK,EAAE,qBAAqB;oBAC5B,WAAW,EAAE,0BAA0B;oBACvC,iBAAiB,EAAE,gCAAgC;iBACpD;aACF;YACD,kBAAkB,EAAE,gDAAgD;SACrE,CAAC;IACJ,CAAC;IAED,yDAAyD;IAC1C,4CAAoB,GAAnC,UAAoC,KAA6B,EAAE,QAA4B;QAC7F,uDAAuD;QACvD,OAAO,EAAE,CAAC;IACZ,CAAC;IAEc,gDAAwB,GAAvC,UAAwC,MAA+B;QACrE,qDAAqD;QACrD,OAAO,EAAE,CAAC;IACZ,CAAC;IAEc,iDAAyB,GAAxC,UAAyC,QAA4B,EAAE,SAA6B;QAClG,mDAAmD;QACnD,OAAO,aAAa,CAAC;IACvB,CAAC;IAEc,kDAA0B,GAAzC,UAA0C,QAA4B;QACpE,OAAO,0FAA0F,CAAC;IACpG,CAAC;IAEc,6CAAqB,GAApC,UAAqC,UAAoC;QACvE,OAAO,CAAC,sBAAsB,EAAE,gBAAgB,EAAE,sBAAsB,CAAC,CAAC;IAC5E,CAAC;IAEc,8CAAsB,GAArC,UAAsC,UAAoC;QACxE,OAAO,CAAC,oBAAoB,EAAE,iBAAiB,EAAE,eAAe,CAAC,CAAC;IACpE,CAAC;IAEc,2CAAmB,GAAlC,UAAmC,UAAoC;QACrE,OAAO,gDAAgD,CAAC;IAC1D,CAAC;IAEc,6CAAqB,GAApC,UAAqC,UAAoC;QACvE,OAAO,MAAM,CAAC;IAChB,CAAC;IAEc,uDAA+B,GAA9C,UAA+C,UAAoC;QACjF,OAAO,CAAC,iBAAiB,EAAE,sBAAsB,EAAE,kBAAkB,CAAC,CAAC;IACzE,CAAC;IAEc,8CAAsB,GAArC,UAAsC,UAAoC;QACxE,OAAO,sEAAsE,CAAC;IAChF,CAAC;IACH,8BAAC;AAAD,CAAC,AA9WD,IA8WC;AA9WY,0DAAuB",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/enhancedFallbackService.ts"],
      sourcesContent: ["/**\n * Enhanced Fallback Service\n * Provides sophisticated algorithmic fallbacks when AI services are unavailable\n */\n\nimport { AssessmentResponse, AssessmentInsights } from './assessmentScoring';\nimport { AlgorithmicAssessmentService, AlgorithmicMatchResult } from './algorithmicAssessmentService';\n\nexport interface FallbackInsights {\n  careerRecommendations: FallbackCareerRecommendation[];\n  skillGapAnalysis: FallbackSkillGap[];\n  learningPathRecommendations: FallbackLearningPath;\n  marketInsights: FallbackMarketInsights;\n  personalizedAdvice: string[];\n  confidenceScore: number;\n  fallbackReason: string;\n  generatedAt: string;\n}\n\nexport interface FallbackCareerRecommendation {\n  careerPath: string;\n  matchPercentage: number;\n  reasoning: string[];\n  salaryRange: { min: number; max: number; currency: string };\n  marketOutlook: string;\n  transitionDifficulty: 'LOW' | 'MEDIUM' | 'HIGH';\n  timeToTransition: string;\n  keySkillsNeeded: string[];\n  successFactors: string[];\n  potentialChallenges: string[];\n}\n\nexport interface FallbackSkillGap {\n  skill: string;\n  currentLevel: number;\n  targetLevel: number;\n  priority: 'HIGH' | 'MEDIUM' | 'LOW';\n  learningTime: string;\n  recommendedApproach: string;\n  marketValue: number; // 1-10\n}\n\nexport interface FallbackLearningPath {\n  totalDuration: string;\n  phases: FallbackLearningPhase[];\n  weeklyCommitment: string;\n  milestones: FallbackMilestone[];\n  adaptationStrategy: string;\n}\n\nexport interface FallbackLearningPhase {\n  phase: number;\n  name: string;\n  duration: string;\n  focus: string[];\n  deliverables: string[];\n  successCriteria: string[];\n}\n\nexport interface FallbackMilestone {\n  week: number;\n  title: string;\n  description: string;\n  measurableOutcome: string;\n}\n\nexport interface FallbackMarketInsights {\n  industryTrends: string[];\n  emergingSkills: string[];\n  salaryTrends: string;\n  jobMarketHealth: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'CHALLENGING';\n  geographicOpportunities: string[];\n  automationImpact: string;\n}\n\nexport class EnhancedFallbackService {\n  /**\n   * Generate comprehensive fallback insights using algorithmic approaches\n   */\n  static async generateFallbackInsights(\n    responses: AssessmentResponse,\n    insights: AssessmentInsights,\n    fallbackReason: string = 'AI service unavailable'\n  ): Promise<FallbackInsights> {\n    // Use algorithmic assessment service for sophisticated matching\n    const algorithmicMatches = await AlgorithmicAssessmentService.generateCareerRecommendations(\n      responses,\n      insights\n    );\n\n    // Convert algorithmic matches to fallback format\n    const careerRecommendations = this.convertToFallbackRecommendations(algorithmicMatches);\n\n    // Generate skill gap analysis\n    const skillGapAnalysis = this.generateSkillGapAnalysis(algorithmicMatches, insights);\n\n    // Create learning path recommendations\n    const learningPathRecommendations = this.generateLearningPathRecommendations(\n      algorithmicMatches,\n      insights,\n      responses\n    );\n\n    // Generate market insights\n    const marketInsights = this.generateMarketInsights(algorithmicMatches, insights);\n\n    // Create personalized advice\n    const personalizedAdvice = this.generatePersonalizedAdvice(\n      algorithmicMatches,\n      insights,\n      responses\n    );\n\n    // Calculate confidence score\n    const confidenceScore = this.calculateFallbackConfidence(algorithmicMatches, insights);\n\n    return {\n      careerRecommendations,\n      skillGapAnalysis,\n      learningPathRecommendations,\n      marketInsights,\n      personalizedAdvice,\n      confidenceScore,\n      fallbackReason,\n      generatedAt: new Date().toISOString()\n    };\n  }\n\n  /**\n   * Convert algorithmic matches to fallback recommendation format\n   */\n  private static convertToFallbackRecommendations(\n    algorithmicMatches: AlgorithmicMatchResult[]\n  ): FallbackCareerRecommendation[] {\n    return algorithmicMatches.slice(0, 5).map(match => ({\n      careerPath: match.careerPath.name,\n      matchPercentage: match.matchScore,\n      reasoning: match.reasoning,\n      salaryRange: {\n        min: match.careerPath.salaryRange.entry,\n        max: match.careerPath.salaryRange.senior,\n        currency: match.careerPath.salaryRange.currency\n      },\n      marketOutlook: this.formatMarketOutlook(match.careerPath.marketMetrics.growthRate),\n      transitionDifficulty: this.mapDifficultyLevel(match.careerPath.transitionFactors.difficultyScore),\n      timeToTransition: match.estimatedTimeline,\n      keySkillsNeeded: match.skillGaps.slice(0, 5).map(gap => gap.skill),\n      successFactors: this.generateSuccessFactors(match),\n      potentialChallenges: this.generatePotentialChallenges(match)\n    }));\n  }\n\n  /**\n   * Generate comprehensive skill gap analysis\n   */\n  private static generateSkillGapAnalysis(\n    algorithmicMatches: AlgorithmicMatchResult[],\n    insights: AssessmentInsights\n  ): FallbackSkillGap[] {\n    const allSkillGaps = new Map<string, FallbackSkillGap>();\n\n    // Aggregate skill gaps from top career matches\n    algorithmicMatches.slice(0, 3).forEach(match => {\n      match.skillGaps.forEach(gap => {\n        if (!allSkillGaps.has(gap.skill) || allSkillGaps.get(gap.skill)!.priority === 'LOW') {\n          const marketValue = this.calculateSkillMarketValue(gap.skill);\n          \n          allSkillGaps.set(gap.skill, {\n            skill: gap.skill,\n            currentLevel: gap.currentLevel,\n            targetLevel: gap.requiredLevel,\n            priority: gap.priority,\n            learningTime: gap.estimatedLearningTime,\n            recommendedApproach: this.getRecommendedLearningApproach(gap.skill, gap.priority),\n            marketValue\n          });\n        }\n      });\n    });\n\n    return Array.from(allSkillGaps.values())\n      .sort((a, b) => {\n        const priorityOrder = { HIGH: 3, MEDIUM: 2, LOW: 1 };\n        return priorityOrder[b.priority] - priorityOrder[a.priority];\n      })\n      .slice(0, 10); // Top 10 skill gaps\n  }\n\n  /**\n   * Generate learning path recommendations\n   */\n  private static generateLearningPathRecommendations(\n    algorithmicMatches: AlgorithmicMatchResult[],\n    insights: AssessmentInsights,\n    responses: AssessmentResponse\n  ): FallbackLearningPath {\n    const topMatch = algorithmicMatches[0];\n    if (!topMatch) {\n      return this.getDefaultLearningPath();\n    }\n\n    const phases = this.createLearningPhases(topMatch, insights);\n    const milestones = this.createLearningMilestones(phases);\n    const weeklyCommitment = this.calculateWeeklyCommitment(insights, responses);\n\n    return {\n      totalDuration: topMatch.estimatedTimeline,\n      phases,\n      weeklyCommitment,\n      milestones,\n      adaptationStrategy: this.generateAdaptationStrategy(insights)\n    };\n  }\n\n  /**\n   * Generate market insights\n   */\n  private static generateMarketInsights(\n    algorithmicMatches: AlgorithmicMatchResult[],\n    insights: AssessmentInsights\n  ): FallbackMarketInsights {\n    const topCareers = algorithmicMatches.slice(0, 3);\n    \n    return {\n      industryTrends: this.extractIndustryTrends(topCareers),\n      emergingSkills: this.identifyEmergingSkills(topCareers),\n      salaryTrends: this.analyzeSalaryTrends(topCareers),\n      jobMarketHealth: this.assessJobMarketHealth(topCareers),\n      geographicOpportunities: this.identifyGeographicOpportunities(topCareers),\n      automationImpact: this.assessAutomationImpact(topCareers)\n    };\n  }\n\n  /**\n   * Generate personalized advice\n   */\n  private static generatePersonalizedAdvice(\n    algorithmicMatches: AlgorithmicMatchResult[],\n    insights: AssessmentInsights,\n    responses: AssessmentResponse\n  ): string[] {\n    const advice: string[] = [];\n    const topMatch = algorithmicMatches[0];\n\n    // Financial readiness advice\n    if (insights.scores.financialReadiness < 3) {\n      advice.push('Build an emergency fund covering 3-6 months of expenses before making a career transition');\n      advice.push('Consider part-time learning while maintaining current income to reduce financial risk');\n    }\n\n    // Skill development advice\n    if (topMatch && topMatch.skillGaps.length > 3) {\n      advice.push('Focus on developing 1-2 high-priority skills at a time rather than trying to learn everything simultaneously');\n      advice.push('Create a portfolio project that demonstrates your growing skills to potential employers');\n    }\n\n    // Timeline advice\n    if (insights.scores.urgencyLevel >= 4 && topMatch?.matchScore < 70) {\n      advice.push('Consider interim roles or freelance opportunities to gain experience while building toward your target career');\n    }\n\n    // Support system advice\n    if (insights.scores.supportLevel < 3) {\n      advice.push('Join professional communities and online forums related to your target career for networking and support');\n      advice.push('Consider finding a mentor in your target field to guide your transition');\n    }\n\n    // Confidence building advice\n    if (insights.scores.skillsConfidence < 60) {\n      advice.push('Start with small, achievable learning goals to build confidence and momentum');\n      advice.push('Document your learning progress to track improvement and boost confidence');\n    }\n\n    // Ensure we always have some advice\n    if (advice.length === 0) {\n      advice.push('Focus on continuous learning and skill development in your chosen field');\n      advice.push('Network with professionals in your target industry to gain insights and opportunities');\n      advice.push('Create a structured learning plan with specific milestones and deadlines');\n      advice.push('Consider taking on projects or volunteer work to gain practical experience');\n    }\n\n    return advice;\n  }\n\n  /**\n   * Calculate fallback confidence score\n   */\n  private static calculateFallbackConfidence(\n    algorithmicMatches: AlgorithmicMatchResult[],\n    insights: AssessmentInsights\n  ): number {\n    if (algorithmicMatches.length === 0) return 50;\n\n    const topMatch = algorithmicMatches[0];\n    const baseConfidence = topMatch.confidenceLevel;\n    \n    // Adjust based on data quality\n    let adjustedConfidence = baseConfidence;\n    \n    if (insights.topSkills.length >= 3) adjustedConfidence += 5;\n    if (insights.scores.readinessScore >= 70) adjustedConfidence += 10;\n    if (algorithmicMatches.length >= 3) adjustedConfidence += 5;\n    \n    return Math.min(95, Math.max(60, adjustedConfidence)); // Clamp between 60-95\n  }\n\n  // Helper methods\n  private static formatMarketOutlook(growthRate: number): string {\n    if (growthRate >= 20) return `${growthRate}% (Much faster than average)`;\n    if (growthRate >= 10) return `${growthRate}% (Faster than average)`;\n    if (growthRate >= 5) return `${growthRate}% (As fast as average)`;\n    return `${growthRate}% (Slower than average)`;\n  }\n\n  private static mapDifficultyLevel(difficultyScore: number): 'LOW' | 'MEDIUM' | 'HIGH' {\n    if (difficultyScore <= 3) return 'LOW';\n    if (difficultyScore <= 6) return 'MEDIUM';\n    return 'HIGH';\n  }\n\n  private static generateSuccessFactors(match: AlgorithmicMatchResult): string[] {\n    const factors = ['Strong analytical thinking', 'Continuous learning mindset'];\n    \n    if (match.matchFactors.skillAlignment >= 70) {\n      factors.push('Good foundation in required skills');\n    }\n    \n    if (match.matchFactors.transitionFeasibility >= 70) {\n      factors.push('Favorable transition conditions');\n    }\n    \n    return factors;\n  }\n\n  private static generatePotentialChallenges(match: AlgorithmicMatchResult): string[] {\n    const challenges = [];\n    \n    if (match.skillGaps.length > 3) {\n      challenges.push('Significant skill development required');\n    }\n    \n    if (match.matchFactors.transitionFeasibility < 60) {\n      challenges.push('Complex transition timeline');\n    }\n    \n    if (match.careerPath.marketMetrics.competitionLevel >= 7) {\n      challenges.push('Competitive job market');\n    }\n    \n    return challenges.length > 0 ? challenges : ['Market competition', 'Keeping skills current'];\n  }\n\n  private static calculateSkillMarketValue(skill: string): number {\n    // Simplified market value calculation\n    const highValueSkills = ['AI/ML', 'Cloud Computing', 'Cybersecurity', 'Data Science'];\n    const mediumValueSkills = ['Web Development', 'Mobile Development', 'Project Management'];\n    \n    if (highValueSkills.some(hvs => skill.toLowerCase().includes(hvs.toLowerCase()))) return 9;\n    if (mediumValueSkills.some(mvs => skill.toLowerCase().includes(mvs.toLowerCase()))) return 7;\n    return 5;\n  }\n\n  private static getRecommendedLearningApproach(skill: string, priority: 'HIGH' | 'MEDIUM' | 'LOW'): string {\n    if (priority === 'HIGH') {\n      return 'Intensive bootcamp or structured course with hands-on projects';\n    }\n    if (priority === 'MEDIUM') {\n      return 'Online courses combined with practical application';\n    }\n    return 'Self-paced learning with periodic skill assessments';\n  }\n\n  private static getDefaultLearningPath(): FallbackLearningPath {\n    return {\n      totalDuration: '6-12 months',\n      phases: [\n        {\n          phase: 1,\n          name: 'Foundation Building',\n          duration: '4-6 weeks',\n          focus: ['Core concepts', 'Basic tools'],\n          deliverables: ['Complete introductory course', 'Build first project'],\n          successCriteria: ['Pass skill assessment', 'Demonstrate basic competency']\n        }\n      ],\n      weeklyCommitment: '10-15 hours',\n      milestones: [\n        {\n          week: 4,\n          title: 'Foundation Complete',\n          description: 'Basic skills established',\n          measurableOutcome: 'Score 70%+ on skill assessment'\n        }\n      ],\n      adaptationStrategy: 'Regular progress reviews with plan adjustments'\n    };\n  }\n\n  // Additional helper methods would be implemented here...\n  private static createLearningPhases(match: AlgorithmicMatchResult, insights: AssessmentInsights): FallbackLearningPhase[] {\n    // Implementation would create detailed learning phases\n    return [];\n  }\n\n  private static createLearningMilestones(phases: FallbackLearningPhase[]): FallbackMilestone[] {\n    // Implementation would create milestones from phases\n    return [];\n  }\n\n  private static calculateWeeklyCommitment(insights: AssessmentInsights, responses: AssessmentResponse): string {\n    // Calculate based on user availability and urgency\n    return '10-15 hours';\n  }\n\n  private static generateAdaptationStrategy(insights: AssessmentInsights): string {\n    return 'Regular progress reviews with plan adjustments based on learning pace and market changes';\n  }\n\n  private static extractIndustryTrends(topCareers: AlgorithmicMatchResult[]): string[] {\n    return ['Remote work adoption', 'AI integration', 'Sustainability focus'];\n  }\n\n  private static identifyEmergingSkills(topCareers: AlgorithmicMatchResult[]): string[] {\n    return ['AI/ML fundamentals', 'Cloud platforms', 'Data analysis'];\n  }\n\n  private static analyzeSalaryTrends(topCareers: AlgorithmicMatchResult[]): string {\n    return 'Salaries growing 5-15% annually in tech fields';\n  }\n\n  private static assessJobMarketHealth(topCareers: AlgorithmicMatchResult[]): 'EXCELLENT' | 'GOOD' | 'FAIR' | 'CHALLENGING' {\n    return 'GOOD';\n  }\n\n  private static identifyGeographicOpportunities(topCareers: AlgorithmicMatchResult[]): string[] {\n    return ['Major tech hubs', 'Remote opportunities', 'Emerging markets'];\n  }\n\n  private static assessAutomationImpact(topCareers: AlgorithmicMatchResult[]): string {\n    return 'Low to moderate automation risk with focus on human-AI collaboration';\n  }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "3be850a743b4224e752f9076e260c06346fd4f10"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_v2h67dk8h = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_v2h67dk8h();
var __awaiter =
/* istanbul ignore next */
(cov_v2h67dk8h().s[0]++,
/* istanbul ignore next */
(cov_v2h67dk8h().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_v2h67dk8h().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_v2h67dk8h().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_v2h67dk8h().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[1]++;
    cov_v2h67dk8h().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_v2h67dk8h().f[2]++;
      cov_v2h67dk8h().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_v2h67dk8h().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_v2h67dk8h().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_v2h67dk8h().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_v2h67dk8h().f[4]++;
      cov_v2h67dk8h().s[4]++;
      try {
        /* istanbul ignore next */
        cov_v2h67dk8h().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_v2h67dk8h().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_v2h67dk8h().f[5]++;
      cov_v2h67dk8h().s[7]++;
      try {
        /* istanbul ignore next */
        cov_v2h67dk8h().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_v2h67dk8h().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_v2h67dk8h().f[6]++;
      cov_v2h67dk8h().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_v2h67dk8h().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_v2h67dk8h().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_v2h67dk8h().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_v2h67dk8h().s[12]++,
/* istanbul ignore next */
(cov_v2h67dk8h().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_v2h67dk8h().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_v2h67dk8h().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_v2h67dk8h().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_v2h67dk8h().f[8]++;
        cov_v2h67dk8h().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_v2h67dk8h().b[6][0]++;
          cov_v2h67dk8h().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_v2h67dk8h().b[6][1]++;
        }
        cov_v2h67dk8h().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_v2h67dk8h().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_v2h67dk8h().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_v2h67dk8h().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[9]++;
    cov_v2h67dk8h().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[10]++;
    cov_v2h67dk8h().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_v2h67dk8h().f[11]++;
      cov_v2h67dk8h().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[12]++;
    cov_v2h67dk8h().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[9][0]++;
      cov_v2h67dk8h().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[9][1]++;
    }
    cov_v2h67dk8h().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_v2h67dk8h().s[25]++;
      try {
        /* istanbul ignore next */
        cov_v2h67dk8h().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_v2h67dk8h().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_v2h67dk8h().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_v2h67dk8h().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_v2h67dk8h().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_v2h67dk8h().b[15][0]++,
        /* istanbul ignore next */
        (cov_v2h67dk8h().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_v2h67dk8h().b[16][1]++,
        /* istanbul ignore next */
        (cov_v2h67dk8h().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_v2h67dk8h().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_v2h67dk8h().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_v2h67dk8h().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_v2h67dk8h().b[12][0]++;
          cov_v2h67dk8h().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_v2h67dk8h().b[12][1]++;
        }
        cov_v2h67dk8h().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_v2h67dk8h().b[18][0]++;
          cov_v2h67dk8h().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_v2h67dk8h().b[18][1]++;
        }
        cov_v2h67dk8h().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_v2h67dk8h().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_v2h67dk8h().b[19][1]++;
            cov_v2h67dk8h().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_v2h67dk8h().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_v2h67dk8h().b[19][2]++;
            cov_v2h67dk8h().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_v2h67dk8h().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_v2h67dk8h().b[19][3]++;
            cov_v2h67dk8h().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_v2h67dk8h().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_v2h67dk8h().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_v2h67dk8h().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_v2h67dk8h().b[19][4]++;
            cov_v2h67dk8h().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_v2h67dk8h().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_v2h67dk8h().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_v2h67dk8h().b[19][5]++;
            cov_v2h67dk8h().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_v2h67dk8h().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_v2h67dk8h().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_v2h67dk8h().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_v2h67dk8h().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_v2h67dk8h().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_v2h67dk8h().b[20][0]++;
              cov_v2h67dk8h().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_v2h67dk8h().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_v2h67dk8h().b[20][1]++;
            }
            cov_v2h67dk8h().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_v2h67dk8h().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_v2h67dk8h().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_v2h67dk8h().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_v2h67dk8h().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_v2h67dk8h().b[23][0]++;
              cov_v2h67dk8h().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_v2h67dk8h().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_v2h67dk8h().b[23][1]++;
            }
            cov_v2h67dk8h().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_v2h67dk8h().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_v2h67dk8h().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_v2h67dk8h().b[25][0]++;
              cov_v2h67dk8h().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_v2h67dk8h().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_v2h67dk8h().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_v2h67dk8h().b[25][1]++;
            }
            cov_v2h67dk8h().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_v2h67dk8h().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_v2h67dk8h().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_v2h67dk8h().b[27][0]++;
              cov_v2h67dk8h().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_v2h67dk8h().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_v2h67dk8h().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_v2h67dk8h().b[27][1]++;
            }
            cov_v2h67dk8h().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_v2h67dk8h().b[29][0]++;
              cov_v2h67dk8h().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_v2h67dk8h().b[29][1]++;
            }
            cov_v2h67dk8h().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_v2h67dk8h().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_v2h67dk8h().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_v2h67dk8h().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_v2h67dk8h().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_v2h67dk8h().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_v2h67dk8h().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[30][0]++;
      cov_v2h67dk8h().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[30][1]++;
    }
    cov_v2h67dk8h().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_v2h67dk8h().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_v2h67dk8h().b[31][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_v2h67dk8h().s[67]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_v2h67dk8h().s[68]++;
exports.EnhancedFallbackService = void 0;
var algorithmicAssessmentService_1 =
/* istanbul ignore next */
(cov_v2h67dk8h().s[69]++, require("./algorithmicAssessmentService"));
var EnhancedFallbackService =
/* istanbul ignore next */
(/** @class */cov_v2h67dk8h().s[70]++, function () {
  /* istanbul ignore next */
  cov_v2h67dk8h().f[13]++;
  function EnhancedFallbackService() {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[14]++;
  }
  /**
   * Generate comprehensive fallback insights using algorithmic approaches
   */
  /* istanbul ignore next */
  cov_v2h67dk8h().s[71]++;
  EnhancedFallbackService.generateFallbackInsights = function (responses_1, insights_1) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[15]++;
    cov_v2h67dk8h().s[72]++;
    return __awaiter(this, arguments, Promise, function (responses, insights, fallbackReason) {
      /* istanbul ignore next */
      cov_v2h67dk8h().f[16]++;
      var algorithmicMatches, careerRecommendations, skillGapAnalysis, learningPathRecommendations, marketInsights, personalizedAdvice, confidenceScore;
      /* istanbul ignore next */
      cov_v2h67dk8h().s[73]++;
      if (fallbackReason === void 0) {
        /* istanbul ignore next */
        cov_v2h67dk8h().b[32][0]++;
        cov_v2h67dk8h().s[74]++;
        fallbackReason = 'AI service unavailable';
      } else
      /* istanbul ignore next */
      {
        cov_v2h67dk8h().b[32][1]++;
      }
      cov_v2h67dk8h().s[75]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_v2h67dk8h().f[17]++;
        cov_v2h67dk8h().s[76]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_v2h67dk8h().b[33][0]++;
            cov_v2h67dk8h().s[77]++;
            return [4 /*yield*/, algorithmicAssessmentService_1.AlgorithmicAssessmentService.generateCareerRecommendations(responses, insights)];
          case 1:
            /* istanbul ignore next */
            cov_v2h67dk8h().b[33][1]++;
            cov_v2h67dk8h().s[78]++;
            algorithmicMatches = _a.sent();
            /* istanbul ignore next */
            cov_v2h67dk8h().s[79]++;
            careerRecommendations = this.convertToFallbackRecommendations(algorithmicMatches);
            /* istanbul ignore next */
            cov_v2h67dk8h().s[80]++;
            skillGapAnalysis = this.generateSkillGapAnalysis(algorithmicMatches, insights);
            /* istanbul ignore next */
            cov_v2h67dk8h().s[81]++;
            learningPathRecommendations = this.generateLearningPathRecommendations(algorithmicMatches, insights, responses);
            /* istanbul ignore next */
            cov_v2h67dk8h().s[82]++;
            marketInsights = this.generateMarketInsights(algorithmicMatches, insights);
            /* istanbul ignore next */
            cov_v2h67dk8h().s[83]++;
            personalizedAdvice = this.generatePersonalizedAdvice(algorithmicMatches, insights, responses);
            /* istanbul ignore next */
            cov_v2h67dk8h().s[84]++;
            confidenceScore = this.calculateFallbackConfidence(algorithmicMatches, insights);
            /* istanbul ignore next */
            cov_v2h67dk8h().s[85]++;
            return [2 /*return*/, {
              careerRecommendations: careerRecommendations,
              skillGapAnalysis: skillGapAnalysis,
              learningPathRecommendations: learningPathRecommendations,
              marketInsights: marketInsights,
              personalizedAdvice: personalizedAdvice,
              confidenceScore: confidenceScore,
              fallbackReason: fallbackReason,
              generatedAt: new Date().toISOString()
            }];
        }
      });
    });
  };
  /**
   * Convert algorithmic matches to fallback recommendation format
   */
  /* istanbul ignore next */
  cov_v2h67dk8h().s[86]++;
  EnhancedFallbackService.convertToFallbackRecommendations = function (algorithmicMatches) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[18]++;
    var _this =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[87]++, this);
    /* istanbul ignore next */
    cov_v2h67dk8h().s[88]++;
    return algorithmicMatches.slice(0, 5).map(function (match) {
      /* istanbul ignore next */
      cov_v2h67dk8h().f[19]++;
      cov_v2h67dk8h().s[89]++;
      return {
        careerPath: match.careerPath.name,
        matchPercentage: match.matchScore,
        reasoning: match.reasoning,
        salaryRange: {
          min: match.careerPath.salaryRange.entry,
          max: match.careerPath.salaryRange.senior,
          currency: match.careerPath.salaryRange.currency
        },
        marketOutlook: _this.formatMarketOutlook(match.careerPath.marketMetrics.growthRate),
        transitionDifficulty: _this.mapDifficultyLevel(match.careerPath.transitionFactors.difficultyScore),
        timeToTransition: match.estimatedTimeline,
        keySkillsNeeded: match.skillGaps.slice(0, 5).map(function (gap) {
          /* istanbul ignore next */
          cov_v2h67dk8h().f[20]++;
          cov_v2h67dk8h().s[90]++;
          return gap.skill;
        }),
        successFactors: _this.generateSuccessFactors(match),
        potentialChallenges: _this.generatePotentialChallenges(match)
      };
    });
  };
  /**
   * Generate comprehensive skill gap analysis
   */
  /* istanbul ignore next */
  cov_v2h67dk8h().s[91]++;
  EnhancedFallbackService.generateSkillGapAnalysis = function (algorithmicMatches, insights) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[21]++;
    var _this =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[92]++, this);
    var allSkillGaps =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[93]++, new Map());
    // Aggregate skill gaps from top career matches
    /* istanbul ignore next */
    cov_v2h67dk8h().s[94]++;
    algorithmicMatches.slice(0, 3).forEach(function (match) {
      /* istanbul ignore next */
      cov_v2h67dk8h().f[22]++;
      cov_v2h67dk8h().s[95]++;
      match.skillGaps.forEach(function (gap) {
        /* istanbul ignore next */
        cov_v2h67dk8h().f[23]++;
        cov_v2h67dk8h().s[96]++;
        if (
        /* istanbul ignore next */
        (cov_v2h67dk8h().b[35][0]++, !allSkillGaps.has(gap.skill)) ||
        /* istanbul ignore next */
        (cov_v2h67dk8h().b[35][1]++, allSkillGaps.get(gap.skill).priority === 'LOW')) {
          /* istanbul ignore next */
          cov_v2h67dk8h().b[34][0]++;
          var marketValue =
          /* istanbul ignore next */
          (cov_v2h67dk8h().s[97]++, _this.calculateSkillMarketValue(gap.skill));
          /* istanbul ignore next */
          cov_v2h67dk8h().s[98]++;
          allSkillGaps.set(gap.skill, {
            skill: gap.skill,
            currentLevel: gap.currentLevel,
            targetLevel: gap.requiredLevel,
            priority: gap.priority,
            learningTime: gap.estimatedLearningTime,
            recommendedApproach: _this.getRecommendedLearningApproach(gap.skill, gap.priority),
            marketValue: marketValue
          });
        } else
        /* istanbul ignore next */
        {
          cov_v2h67dk8h().b[34][1]++;
        }
      });
    });
    /* istanbul ignore next */
    cov_v2h67dk8h().s[99]++;
    return Array.from(allSkillGaps.values()).sort(function (a, b) {
      /* istanbul ignore next */
      cov_v2h67dk8h().f[24]++;
      var priorityOrder =
      /* istanbul ignore next */
      (cov_v2h67dk8h().s[100]++, {
        HIGH: 3,
        MEDIUM: 2,
        LOW: 1
      });
      /* istanbul ignore next */
      cov_v2h67dk8h().s[101]++;
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    }).slice(0, 10); // Top 10 skill gaps
  };
  /**
   * Generate learning path recommendations
   */
  /* istanbul ignore next */
  cov_v2h67dk8h().s[102]++;
  EnhancedFallbackService.generateLearningPathRecommendations = function (algorithmicMatches, insights, responses) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[25]++;
    var topMatch =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[103]++, algorithmicMatches[0]);
    /* istanbul ignore next */
    cov_v2h67dk8h().s[104]++;
    if (!topMatch) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[36][0]++;
      cov_v2h67dk8h().s[105]++;
      return this.getDefaultLearningPath();
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[36][1]++;
    }
    var phases =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[106]++, this.createLearningPhases(topMatch, insights));
    var milestones =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[107]++, this.createLearningMilestones(phases));
    var weeklyCommitment =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[108]++, this.calculateWeeklyCommitment(insights, responses));
    /* istanbul ignore next */
    cov_v2h67dk8h().s[109]++;
    return {
      totalDuration: topMatch.estimatedTimeline,
      phases: phases,
      weeklyCommitment: weeklyCommitment,
      milestones: milestones,
      adaptationStrategy: this.generateAdaptationStrategy(insights)
    };
  };
  /**
   * Generate market insights
   */
  /* istanbul ignore next */
  cov_v2h67dk8h().s[110]++;
  EnhancedFallbackService.generateMarketInsights = function (algorithmicMatches, insights) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[26]++;
    var topCareers =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[111]++, algorithmicMatches.slice(0, 3));
    /* istanbul ignore next */
    cov_v2h67dk8h().s[112]++;
    return {
      industryTrends: this.extractIndustryTrends(topCareers),
      emergingSkills: this.identifyEmergingSkills(topCareers),
      salaryTrends: this.analyzeSalaryTrends(topCareers),
      jobMarketHealth: this.assessJobMarketHealth(topCareers),
      geographicOpportunities: this.identifyGeographicOpportunities(topCareers),
      automationImpact: this.assessAutomationImpact(topCareers)
    };
  };
  /**
   * Generate personalized advice
   */
  /* istanbul ignore next */
  cov_v2h67dk8h().s[113]++;
  EnhancedFallbackService.generatePersonalizedAdvice = function (algorithmicMatches, insights, responses) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[27]++;
    var advice =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[114]++, []);
    var topMatch =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[115]++, algorithmicMatches[0]);
    // Financial readiness advice
    /* istanbul ignore next */
    cov_v2h67dk8h().s[116]++;
    if (insights.scores.financialReadiness < 3) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[37][0]++;
      cov_v2h67dk8h().s[117]++;
      advice.push('Build an emergency fund covering 3-6 months of expenses before making a career transition');
      /* istanbul ignore next */
      cov_v2h67dk8h().s[118]++;
      advice.push('Consider part-time learning while maintaining current income to reduce financial risk');
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[37][1]++;
    }
    // Skill development advice
    cov_v2h67dk8h().s[119]++;
    if (
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[39][0]++, topMatch) &&
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[39][1]++, topMatch.skillGaps.length > 3)) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[38][0]++;
      cov_v2h67dk8h().s[120]++;
      advice.push('Focus on developing 1-2 high-priority skills at a time rather than trying to learn everything simultaneously');
      /* istanbul ignore next */
      cov_v2h67dk8h().s[121]++;
      advice.push('Create a portfolio project that demonstrates your growing skills to potential employers');
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[38][1]++;
    }
    // Timeline advice
    cov_v2h67dk8h().s[122]++;
    if (
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[41][0]++, insights.scores.urgencyLevel >= 4) &&
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[41][1]++, (
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[43][0]++, topMatch === null) ||
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[43][1]++, topMatch === void 0) ?
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[42][0]++, void 0) :
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[42][1]++, topMatch.matchScore)) < 70)) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[40][0]++;
      cov_v2h67dk8h().s[123]++;
      advice.push('Consider interim roles or freelance opportunities to gain experience while building toward your target career');
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[40][1]++;
    }
    // Support system advice
    cov_v2h67dk8h().s[124]++;
    if (insights.scores.supportLevel < 3) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[44][0]++;
      cov_v2h67dk8h().s[125]++;
      advice.push('Join professional communities and online forums related to your target career for networking and support');
      /* istanbul ignore next */
      cov_v2h67dk8h().s[126]++;
      advice.push('Consider finding a mentor in your target field to guide your transition');
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[44][1]++;
    }
    // Confidence building advice
    cov_v2h67dk8h().s[127]++;
    if (insights.scores.skillsConfidence < 60) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[45][0]++;
      cov_v2h67dk8h().s[128]++;
      advice.push('Start with small, achievable learning goals to build confidence and momentum');
      /* istanbul ignore next */
      cov_v2h67dk8h().s[129]++;
      advice.push('Document your learning progress to track improvement and boost confidence');
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[45][1]++;
    }
    // Ensure we always have some advice
    cov_v2h67dk8h().s[130]++;
    if (advice.length === 0) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[46][0]++;
      cov_v2h67dk8h().s[131]++;
      advice.push('Focus on continuous learning and skill development in your chosen field');
      /* istanbul ignore next */
      cov_v2h67dk8h().s[132]++;
      advice.push('Network with professionals in your target industry to gain insights and opportunities');
      /* istanbul ignore next */
      cov_v2h67dk8h().s[133]++;
      advice.push('Create a structured learning plan with specific milestones and deadlines');
      /* istanbul ignore next */
      cov_v2h67dk8h().s[134]++;
      advice.push('Consider taking on projects or volunteer work to gain practical experience');
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[46][1]++;
    }
    cov_v2h67dk8h().s[135]++;
    return advice;
  };
  /**
   * Calculate fallback confidence score
   */
  /* istanbul ignore next */
  cov_v2h67dk8h().s[136]++;
  EnhancedFallbackService.calculateFallbackConfidence = function (algorithmicMatches, insights) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[28]++;
    cov_v2h67dk8h().s[137]++;
    if (algorithmicMatches.length === 0) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[47][0]++;
      cov_v2h67dk8h().s[138]++;
      return 50;
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[47][1]++;
    }
    var topMatch =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[139]++, algorithmicMatches[0]);
    var baseConfidence =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[140]++, topMatch.confidenceLevel);
    // Adjust based on data quality
    var adjustedConfidence =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[141]++, baseConfidence);
    /* istanbul ignore next */
    cov_v2h67dk8h().s[142]++;
    if (insights.topSkills.length >= 3) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[48][0]++;
      cov_v2h67dk8h().s[143]++;
      adjustedConfidence += 5;
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[48][1]++;
    }
    cov_v2h67dk8h().s[144]++;
    if (insights.scores.readinessScore >= 70) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[49][0]++;
      cov_v2h67dk8h().s[145]++;
      adjustedConfidence += 10;
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[49][1]++;
    }
    cov_v2h67dk8h().s[146]++;
    if (algorithmicMatches.length >= 3) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[50][0]++;
      cov_v2h67dk8h().s[147]++;
      adjustedConfidence += 5;
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[50][1]++;
    }
    cov_v2h67dk8h().s[148]++;
    return Math.min(95, Math.max(60, adjustedConfidence)); // Clamp between 60-95
  };
  // Helper methods
  /* istanbul ignore next */
  cov_v2h67dk8h().s[149]++;
  EnhancedFallbackService.formatMarketOutlook = function (growthRate) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[29]++;
    cov_v2h67dk8h().s[150]++;
    if (growthRate >= 20) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[51][0]++;
      cov_v2h67dk8h().s[151]++;
      return "".concat(growthRate, "% (Much faster than average)");
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[51][1]++;
    }
    cov_v2h67dk8h().s[152]++;
    if (growthRate >= 10) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[52][0]++;
      cov_v2h67dk8h().s[153]++;
      return "".concat(growthRate, "% (Faster than average)");
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[52][1]++;
    }
    cov_v2h67dk8h().s[154]++;
    if (growthRate >= 5) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[53][0]++;
      cov_v2h67dk8h().s[155]++;
      return "".concat(growthRate, "% (As fast as average)");
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[53][1]++;
    }
    cov_v2h67dk8h().s[156]++;
    return "".concat(growthRate, "% (Slower than average)");
  };
  /* istanbul ignore next */
  cov_v2h67dk8h().s[157]++;
  EnhancedFallbackService.mapDifficultyLevel = function (difficultyScore) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[30]++;
    cov_v2h67dk8h().s[158]++;
    if (difficultyScore <= 3) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[54][0]++;
      cov_v2h67dk8h().s[159]++;
      return 'LOW';
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[54][1]++;
    }
    cov_v2h67dk8h().s[160]++;
    if (difficultyScore <= 6) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[55][0]++;
      cov_v2h67dk8h().s[161]++;
      return 'MEDIUM';
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[55][1]++;
    }
    cov_v2h67dk8h().s[162]++;
    return 'HIGH';
  };
  /* istanbul ignore next */
  cov_v2h67dk8h().s[163]++;
  EnhancedFallbackService.generateSuccessFactors = function (match) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[31]++;
    var factors =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[164]++, ['Strong analytical thinking', 'Continuous learning mindset']);
    /* istanbul ignore next */
    cov_v2h67dk8h().s[165]++;
    if (match.matchFactors.skillAlignment >= 70) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[56][0]++;
      cov_v2h67dk8h().s[166]++;
      factors.push('Good foundation in required skills');
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[56][1]++;
    }
    cov_v2h67dk8h().s[167]++;
    if (match.matchFactors.transitionFeasibility >= 70) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[57][0]++;
      cov_v2h67dk8h().s[168]++;
      factors.push('Favorable transition conditions');
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[57][1]++;
    }
    cov_v2h67dk8h().s[169]++;
    return factors;
  };
  /* istanbul ignore next */
  cov_v2h67dk8h().s[170]++;
  EnhancedFallbackService.generatePotentialChallenges = function (match) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[32]++;
    var challenges =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[171]++, []);
    /* istanbul ignore next */
    cov_v2h67dk8h().s[172]++;
    if (match.skillGaps.length > 3) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[58][0]++;
      cov_v2h67dk8h().s[173]++;
      challenges.push('Significant skill development required');
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[58][1]++;
    }
    cov_v2h67dk8h().s[174]++;
    if (match.matchFactors.transitionFeasibility < 60) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[59][0]++;
      cov_v2h67dk8h().s[175]++;
      challenges.push('Complex transition timeline');
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[59][1]++;
    }
    cov_v2h67dk8h().s[176]++;
    if (match.careerPath.marketMetrics.competitionLevel >= 7) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[60][0]++;
      cov_v2h67dk8h().s[177]++;
      challenges.push('Competitive job market');
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[60][1]++;
    }
    cov_v2h67dk8h().s[178]++;
    return challenges.length > 0 ?
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[61][0]++, challenges) :
    /* istanbul ignore next */
    (cov_v2h67dk8h().b[61][1]++, ['Market competition', 'Keeping skills current']);
  };
  /* istanbul ignore next */
  cov_v2h67dk8h().s[179]++;
  EnhancedFallbackService.calculateSkillMarketValue = function (skill) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[33]++;
    // Simplified market value calculation
    var highValueSkills =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[180]++, ['AI/ML', 'Cloud Computing', 'Cybersecurity', 'Data Science']);
    var mediumValueSkills =
    /* istanbul ignore next */
    (cov_v2h67dk8h().s[181]++, ['Web Development', 'Mobile Development', 'Project Management']);
    /* istanbul ignore next */
    cov_v2h67dk8h().s[182]++;
    if (highValueSkills.some(function (hvs) {
      /* istanbul ignore next */
      cov_v2h67dk8h().f[34]++;
      cov_v2h67dk8h().s[183]++;
      return skill.toLowerCase().includes(hvs.toLowerCase());
    })) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[62][0]++;
      cov_v2h67dk8h().s[184]++;
      return 9;
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[62][1]++;
    }
    cov_v2h67dk8h().s[185]++;
    if (mediumValueSkills.some(function (mvs) {
      /* istanbul ignore next */
      cov_v2h67dk8h().f[35]++;
      cov_v2h67dk8h().s[186]++;
      return skill.toLowerCase().includes(mvs.toLowerCase());
    })) {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[63][0]++;
      cov_v2h67dk8h().s[187]++;
      return 7;
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[63][1]++;
    }
    cov_v2h67dk8h().s[188]++;
    return 5;
  };
  /* istanbul ignore next */
  cov_v2h67dk8h().s[189]++;
  EnhancedFallbackService.getRecommendedLearningApproach = function (skill, priority) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[36]++;
    cov_v2h67dk8h().s[190]++;
    if (priority === 'HIGH') {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[64][0]++;
      cov_v2h67dk8h().s[191]++;
      return 'Intensive bootcamp or structured course with hands-on projects';
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[64][1]++;
    }
    cov_v2h67dk8h().s[192]++;
    if (priority === 'MEDIUM') {
      /* istanbul ignore next */
      cov_v2h67dk8h().b[65][0]++;
      cov_v2h67dk8h().s[193]++;
      return 'Online courses combined with practical application';
    } else
    /* istanbul ignore next */
    {
      cov_v2h67dk8h().b[65][1]++;
    }
    cov_v2h67dk8h().s[194]++;
    return 'Self-paced learning with periodic skill assessments';
  };
  /* istanbul ignore next */
  cov_v2h67dk8h().s[195]++;
  EnhancedFallbackService.getDefaultLearningPath = function () {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[37]++;
    cov_v2h67dk8h().s[196]++;
    return {
      totalDuration: '6-12 months',
      phases: [{
        phase: 1,
        name: 'Foundation Building',
        duration: '4-6 weeks',
        focus: ['Core concepts', 'Basic tools'],
        deliverables: ['Complete introductory course', 'Build first project'],
        successCriteria: ['Pass skill assessment', 'Demonstrate basic competency']
      }],
      weeklyCommitment: '10-15 hours',
      milestones: [{
        week: 4,
        title: 'Foundation Complete',
        description: 'Basic skills established',
        measurableOutcome: 'Score 70%+ on skill assessment'
      }],
      adaptationStrategy: 'Regular progress reviews with plan adjustments'
    };
  };
  // Additional helper methods would be implemented here...
  /* istanbul ignore next */
  cov_v2h67dk8h().s[197]++;
  EnhancedFallbackService.createLearningPhases = function (match, insights) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[38]++;
    cov_v2h67dk8h().s[198]++;
    // Implementation would create detailed learning phases
    return [];
  };
  /* istanbul ignore next */
  cov_v2h67dk8h().s[199]++;
  EnhancedFallbackService.createLearningMilestones = function (phases) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[39]++;
    cov_v2h67dk8h().s[200]++;
    // Implementation would create milestones from phases
    return [];
  };
  /* istanbul ignore next */
  cov_v2h67dk8h().s[201]++;
  EnhancedFallbackService.calculateWeeklyCommitment = function (insights, responses) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[40]++;
    cov_v2h67dk8h().s[202]++;
    // Calculate based on user availability and urgency
    return '10-15 hours';
  };
  /* istanbul ignore next */
  cov_v2h67dk8h().s[203]++;
  EnhancedFallbackService.generateAdaptationStrategy = function (insights) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[41]++;
    cov_v2h67dk8h().s[204]++;
    return 'Regular progress reviews with plan adjustments based on learning pace and market changes';
  };
  /* istanbul ignore next */
  cov_v2h67dk8h().s[205]++;
  EnhancedFallbackService.extractIndustryTrends = function (topCareers) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[42]++;
    cov_v2h67dk8h().s[206]++;
    return ['Remote work adoption', 'AI integration', 'Sustainability focus'];
  };
  /* istanbul ignore next */
  cov_v2h67dk8h().s[207]++;
  EnhancedFallbackService.identifyEmergingSkills = function (topCareers) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[43]++;
    cov_v2h67dk8h().s[208]++;
    return ['AI/ML fundamentals', 'Cloud platforms', 'Data analysis'];
  };
  /* istanbul ignore next */
  cov_v2h67dk8h().s[209]++;
  EnhancedFallbackService.analyzeSalaryTrends = function (topCareers) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[44]++;
    cov_v2h67dk8h().s[210]++;
    return 'Salaries growing 5-15% annually in tech fields';
  };
  /* istanbul ignore next */
  cov_v2h67dk8h().s[211]++;
  EnhancedFallbackService.assessJobMarketHealth = function (topCareers) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[45]++;
    cov_v2h67dk8h().s[212]++;
    return 'GOOD';
  };
  /* istanbul ignore next */
  cov_v2h67dk8h().s[213]++;
  EnhancedFallbackService.identifyGeographicOpportunities = function (topCareers) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[46]++;
    cov_v2h67dk8h().s[214]++;
    return ['Major tech hubs', 'Remote opportunities', 'Emerging markets'];
  };
  /* istanbul ignore next */
  cov_v2h67dk8h().s[215]++;
  EnhancedFallbackService.assessAutomationImpact = function (topCareers) {
    /* istanbul ignore next */
    cov_v2h67dk8h().f[47]++;
    cov_v2h67dk8h().s[216]++;
    return 'Low to moderate automation risk with focus on human-AI collaboration';
  };
  /* istanbul ignore next */
  cov_v2h67dk8h().s[217]++;
  return EnhancedFallbackService;
}());
/* istanbul ignore next */
cov_v2h67dk8h().s[218]++;
exports.EnhancedFallbackService = EnhancedFallbackService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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