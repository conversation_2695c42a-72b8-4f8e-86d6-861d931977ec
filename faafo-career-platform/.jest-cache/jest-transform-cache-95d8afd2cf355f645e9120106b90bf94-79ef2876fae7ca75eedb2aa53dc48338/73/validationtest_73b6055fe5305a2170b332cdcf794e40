837aa9c7f4f6fc3d2bdc61029b4ad099
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var validation_1 = require("../validation");
describe('Validation Schemas', function () {
    describe('signupSchema', function () {
        it('should validate correct signup data', function () {
            var validData = {
                email: '<EMAIL>',
                password: 'Password123!',
            };
            var result = (0, validation_1.validateRequestBody)(validation_1.signupSchema, validData);
            expect(result.success).toBe(true);
            if (result.success) {
                expect(result.data).toEqual(validData);
            }
        });
        it('should reject invalid email', function () {
            var invalidData = {
                email: 'invalid-email',
                password: 'Password123!',
            };
            var result = (0, validation_1.validateRequestBody)(validation_1.signupSchema, invalidData);
            expect(result.success).toBe(false);
            if (!result.success) {
                expect(result.error).toContain('Invalid email address');
            }
        });
        it('should reject weak password', function () {
            var invalidData = {
                email: '<EMAIL>',
                password: 'weak',
            };
            var result = (0, validation_1.validateRequestBody)(validation_1.signupSchema, invalidData);
            expect(result.success).toBe(false);
            if (!result.success) {
                expect(result.error).toContain('Password must be at least 8 characters');
            }
        });
    });
    describe('loginSchema', function () {
        it('should validate correct login data', function () {
            var validData = {
                email: '<EMAIL>',
                password: 'anypassword',
            };
            var result = (0, validation_1.validateRequestBody)(validation_1.loginSchema, validData);
            expect(result.success).toBe(true);
        });
        it('should reject missing password', function () {
            var invalidData = {
                email: '<EMAIL>',
                password: '',
            };
            var result = (0, validation_1.validateRequestBody)(validation_1.loginSchema, invalidData);
            expect(result.success).toBe(false);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************