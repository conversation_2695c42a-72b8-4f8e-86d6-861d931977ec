{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/__tests__/validation.test.ts", "mappings": ";;AAAA,4CAIuB;AAEvB,QAAQ,CAAC,oBAAoB,EAAE;IAC7B,QAAQ,CAAC,cAAc,EAAE;QACvB,EAAE,CAAC,qCAAqC,EAAE;YACxC,IAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,cAAc;aACzB,CAAC;YAEF,IAAM,MAAM,GAAG,IAAA,gCAAmB,EAAC,yBAAY,EAAE,SAAS,CAAC,CAAC;YAC5D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACzC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE;YAChC,IAAM,WAAW,GAAG;gBAClB,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE,cAAc;aACzB,CAAC;YAEF,IAAM,MAAM,GAAG,IAAA,gCAAmB,EAAC,yBAAY,EAAE,WAAW,CAAC,CAAC;YAC9D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE;YAChC,IAAM,WAAW,GAAG;gBAClB,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,MAAM;aACjB,CAAC;YAEF,IAAM,MAAM,GAAG,IAAA,gCAAmB,EAAC,yBAAY,EAAE,WAAW,CAAC,CAAC;YAC9D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE;QACtB,EAAE,CAAC,oCAAoC,EAAE;YACvC,IAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,aAAa;aACxB,CAAC;YAEF,IAAM,MAAM,GAAG,IAAA,gCAAmB,EAAC,wBAAW,EAAE,SAAS,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE;YACnC,IAAM,WAAW,GAAG;gBAClB,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,EAAE;aACb,CAAC;YAEF,IAAM,MAAM,GAAG,IAAA,gCAAmB,EAAC,wBAAW,EAAE,WAAW,CAAC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/__tests__/validation.test.ts"], "sourcesContent": ["import {\n  signupSchema,\n  loginSchema,\n  validateRequestBody,\n} from '../validation';\n\ndescribe('Validation Schemas', () => {\n  describe('signupSchema', () => {\n    it('should validate correct signup data', () => {\n      const validData = {\n        email: '<EMAIL>',\n        password: 'Password123!',\n      };\n\n      const result = validateRequestBody(signupSchema, validData);\n      expect(result.success).toBe(true);\n      if (result.success) {\n        expect(result.data).toEqual(validData);\n      }\n    });\n\n    it('should reject invalid email', () => {\n      const invalidData = {\n        email: 'invalid-email',\n        password: 'Password123!',\n      };\n\n      const result = validateRequestBody(signupSchema, invalidData);\n      expect(result.success).toBe(false);\n      if (!result.success) {\n        expect(result.error).toContain('Invalid email address');\n      }\n    });\n\n    it('should reject weak password', () => {\n      const invalidData = {\n        email: '<EMAIL>',\n        password: 'weak',\n      };\n\n      const result = validateRequestBody(signupSchema, invalidData);\n      expect(result.success).toBe(false);\n      if (!result.success) {\n        expect(result.error).toContain('Password must be at least 8 characters');\n      }\n    });\n  });\n\n  describe('loginSchema', () => {\n    it('should validate correct login data', () => {\n      const validData = {\n        email: '<EMAIL>',\n        password: 'anypassword',\n      };\n\n      const result = validateRequestBody(loginSchema, validData);\n      expect(result.success).toBe(true);\n    });\n\n    it('should reject missing password', () => {\n      const invalidData = {\n        email: '<EMAIL>',\n        password: '',\n      };\n\n      const result = validateRequestBody(loginSchema, invalidData);\n      expect(result.success).toBe(false);\n    });\n  });\n});\n"], "version": 3}