4e175fb09aca8c2e431f243cc66a59d2
"use strict";
/**
 * Comprehensive Authentication Middleware Tests
 * Tests route protection, admin access control, and security headers
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
// Mock next-auth/jwt
jest.mock('next-auth/jwt', function () { return ({
    getToken: jest.fn(),
}); });
var jwt_1 = require("next-auth/jwt");
var mockGetToken = jwt_1.getToken;
// Import middleware after mocking
var middleware_1 = require("../../../middleware");
describe('Authentication Middleware', function () {
    var mockRequest;
    beforeEach(function () {
        jest.clearAllMocks();
        mockRequest = {
            nextUrl: {
                pathname: '/dashboard',
                search: '',
                searchParams: new URLSearchParams(),
            },
            url: 'http://localhost:3000/dashboard',
            headers: new Headers(),
        };
    });
    describe('Public Routes', function () {
        it('should allow access to public routes without authentication', function () { return __awaiter(void 0, void 0, void 0, function () {
            var response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockRequest.nextUrl.pathname = '/';
                        mockGetToken.mockResolvedValue(null);
                        return [4 /*yield*/, (0, middleware_1.middleware)(mockRequest)];
                    case 1:
                        response = _a.sent();
                        expect(response).toBeDefined();
                        // Should not redirect to login
                        expect(response.status).not.toBe(307);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should allow access to auth pages when not authenticated', function () { return __awaiter(void 0, void 0, void 0, function () {
            var response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockRequest.nextUrl.pathname = '/login';
                        mockGetToken.mockResolvedValue(null);
                        return [4 /*yield*/, (0, middleware_1.middleware)(mockRequest)];
                    case 1:
                        response = _a.sent();
                        expect(response).toBeDefined();
                        expect(response.status).not.toBe(307);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should allow access to static assets', function () { return __awaiter(void 0, void 0, void 0, function () {
            var response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockRequest.nextUrl.pathname = '/favicon.ico';
                        mockGetToken.mockResolvedValue(null);
                        return [4 /*yield*/, (0, middleware_1.middleware)(mockRequest)];
                    case 1:
                        response = _a.sent();
                        expect(response).toBeDefined();
                        expect(response.status).not.toBe(307);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Protected Routes', function () {
        it('should redirect unauthenticated users to login', function () { return __awaiter(void 0, void 0, void 0, function () {
            var response, location;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockRequest.nextUrl.pathname = '/dashboard';
                        mockGetToken.mockResolvedValue(null);
                        return [4 /*yield*/, (0, middleware_1.middleware)(mockRequest)];
                    case 1:
                        response = _a.sent();
                        expect(response).toBeDefined();
                        expect(response.status).toBe(307); // Redirect status
                        location = response.headers.get('location');
                        expect(location).toContain('/login');
                        expect(location).toContain('callbackUrl');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should allow authenticated users to access protected routes', function () { return __awaiter(void 0, void 0, void 0, function () {
            var response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockRequest.nextUrl.pathname = '/dashboard';
                        mockGetToken.mockResolvedValue({
                            sub: 'user-id',
                            email: '<EMAIL>',
                            role: 'user'
                        });
                        return [4 /*yield*/, (0, middleware_1.middleware)(mockRequest)];
                    case 1:
                        response = _a.sent();
                        expect(response).toBeDefined();
                        expect(response.status).not.toBe(307);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should preserve callback URL in login redirect', function () { return __awaiter(void 0, void 0, void 0, function () {
            var response, location;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockRequest.nextUrl.pathname = '/profile';
                        mockRequest.nextUrl.search = '?tab=settings';
                        mockGetToken.mockResolvedValue(null);
                        return [4 /*yield*/, (0, middleware_1.middleware)(mockRequest)];
                    case 1:
                        response = _a.sent();
                        location = response.headers.get('location');
                        expect(location).toContain('callbackUrl=%2Fprofile%3Ftab%3Dsettings');
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Admin Routes', function () {
        it('should redirect non-admin users from admin routes', function () { return __awaiter(void 0, void 0, void 0, function () {
            var response, body;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockRequest.nextUrl.pathname = '/admin/dashboard';
                        mockGetToken.mockResolvedValue({
                            sub: 'user-id',
                            email: '<EMAIL>',
                            role: 'user'
                        });
                        return [4 /*yield*/, (0, middleware_1.middleware)(mockRequest)];
                    case 1:
                        response = _a.sent();
                        expect(response).toBeDefined();
                        expect(response.status).toBe(403);
                        return [4 /*yield*/, response.json()];
                    case 2:
                        body = _a.sent();
                        expect(body.error).toBe('Admin access required');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should allow admin users to access admin routes', function () { return __awaiter(void 0, void 0, void 0, function () {
            var response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockRequest.nextUrl.pathname = '/admin/dashboard';
                        mockGetToken.mockResolvedValue({
                            sub: 'admin-id',
                            email: '<EMAIL>',
                            role: 'admin'
                        });
                        return [4 /*yield*/, (0, middleware_1.middleware)(mockRequest)];
                    case 1:
                        response = _a.sent();
                        expect(response).toBeDefined();
                        expect(response.status).not.toBe(403);
                        expect(response.status).not.toBe(307);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should redirect unauthenticated users from admin routes to login', function () { return __awaiter(void 0, void 0, void 0, function () {
            var response, location;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockRequest.nextUrl.pathname = '/admin/users';
                        mockGetToken.mockResolvedValue(null);
                        return [4 /*yield*/, (0, middleware_1.middleware)(mockRequest)];
                    case 1:
                        response = _a.sent();
                        expect(response.status).toBe(307);
                        location = response.headers.get('location');
                        expect(location).toContain('/login');
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('API Route Protection', function () {
        it('should block unauthenticated access to protected API routes', function () { return __awaiter(void 0, void 0, void 0, function () {
            var response, body;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockRequest.nextUrl.pathname = '/api/profile';
                        mockGetToken.mockResolvedValue(null);
                        return [4 /*yield*/, (0, middleware_1.middleware)(mockRequest)];
                    case 1:
                        response = _a.sent();
                        expect(response.status).toBe(401);
                        return [4 /*yield*/, response.json()];
                    case 2:
                        body = _a.sent();
                        expect(body.error).toBe('Authentication required');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should allow authenticated access to protected API routes', function () { return __awaiter(void 0, void 0, void 0, function () {
            var response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockRequest.nextUrl.pathname = '/api/profile';
                        mockGetToken.mockResolvedValue({
                            sub: 'user-id',
                            email: '<EMAIL>',
                            role: 'user'
                        });
                        return [4 /*yield*/, (0, middleware_1.middleware)(mockRequest)];
                    case 1:
                        response = _a.sent();
                        expect(response.status).not.toBe(401);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should block non-admin access to admin API routes', function () { return __awaiter(void 0, void 0, void 0, function () {
            var response, body;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockRequest.nextUrl.pathname = '/api/admin/database';
                        mockGetToken.mockResolvedValue({
                            sub: 'user-id',
                            email: '<EMAIL>',
                            role: 'user'
                        });
                        return [4 /*yield*/, (0, middleware_1.middleware)(mockRequest)];
                    case 1:
                        response = _a.sent();
                        expect(response.status).toBe(403);
                        return [4 /*yield*/, response.json()];
                    case 2:
                        body = _a.sent();
                        expect(body.error).toBe('Admin access required');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should allow admin access to admin API routes', function () { return __awaiter(void 0, void 0, void 0, function () {
            var response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockRequest.nextUrl.pathname = '/api/admin/database';
                        mockGetToken.mockResolvedValue({
                            sub: 'admin-id',
                            email: '<EMAIL>',
                            role: 'admin'
                        });
                        return [4 /*yield*/, (0, middleware_1.middleware)(mockRequest)];
                    case 1:
                        response = _a.sent();
                        expect(response.status).not.toBe(403);
                        expect(response.status).not.toBe(401);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Auth Page Redirects', function () {
        it('should redirect authenticated users away from login page', function () { return __awaiter(void 0, void 0, void 0, function () {
            var response, location;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockRequest.nextUrl.pathname = '/login';
                        mockGetToken.mockResolvedValue({
                            sub: 'user-id',
                            email: '<EMAIL>',
                            role: 'user'
                        });
                        return [4 /*yield*/, (0, middleware_1.middleware)(mockRequest)];
                    case 1:
                        response = _a.sent();
                        expect(response.status).toBe(307);
                        location = response.headers.get('location');
                        expect(location).toContain('/dashboard');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should redirect authenticated users away from signup page', function () { return __awaiter(void 0, void 0, void 0, function () {
            var response, location;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockRequest.nextUrl.pathname = '/signup';
                        mockGetToken.mockResolvedValue({
                            sub: 'user-id',
                            email: '<EMAIL>',
                            role: 'user'
                        });
                        return [4 /*yield*/, (0, middleware_1.middleware)(mockRequest)];
                    case 1:
                        response = _a.sent();
                        expect(response.status).toBe(307);
                        location = response.headers.get('location');
                        expect(location).toContain('/dashboard');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should respect callback URL when redirecting from auth pages', function () { return __awaiter(void 0, void 0, void 0, function () {
            var response, location;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockRequest.nextUrl.pathname = '/login';
                        mockRequest.nextUrl.searchParams.set('callbackUrl', '/profile');
                        mockGetToken.mockResolvedValue({
                            sub: 'user-id',
                            email: '<EMAIL>',
                            role: 'user'
                        });
                        return [4 /*yield*/, (0, middleware_1.middleware)(mockRequest)];
                    case 1:
                        response = _a.sent();
                        location = response.headers.get('location');
                        expect(location).toContain('/profile');
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Security Headers', function () {
        it('should add security headers to responses', function () { return __awaiter(void 0, void 0, void 0, function () {
            var response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockRequest.nextUrl.pathname = '/dashboard';
                        mockGetToken.mockResolvedValue({
                            sub: 'user-id',
                            email: '<EMAIL>',
                            role: 'user'
                        });
                        return [4 /*yield*/, (0, middleware_1.middleware)(mockRequest)];
                    case 1:
                        response = _a.sent();
                        // Check for common security headers
                        expect(response.headers.get('X-Frame-Options')).toBeDefined();
                        expect(response.headers.get('X-Content-Type-Options')).toBeDefined();
                        expect(response.headers.get('Referrer-Policy')).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************