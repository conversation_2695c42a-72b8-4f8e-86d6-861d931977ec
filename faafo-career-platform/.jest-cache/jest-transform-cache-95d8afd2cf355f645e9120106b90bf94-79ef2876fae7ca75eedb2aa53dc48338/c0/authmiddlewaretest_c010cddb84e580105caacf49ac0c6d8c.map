{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/auth/auth-middleware.test.ts", "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKH,qBAAqB;AACrB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,cAAM,OAAA,CAAC;IAChC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;CACpB,CAAC,EAF+B,CAE/B,CAAC,CAAC;AALJ,qCAAyC;AAOzC,IAAM,YAAY,GAAG,cAAgD,CAAC;AAEtE,kCAAkC;AAClC,kDAAiD;AAEjD,QAAQ,CAAC,2BAA2B,EAAE;IACpC,IAAI,WAAiC,CAAC;IAEtC,UAAU,CAAC;QACT,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,WAAW,GAAG;YACZ,OAAO,EAAE;gBACP,QAAQ,EAAE,YAAY;gBACtB,MAAM,EAAE,EAAE;gBACV,YAAY,EAAE,IAAI,eAAe,EAAE;aAC7B;YACR,GAAG,EAAE,iCAAiC;YACtC,OAAO,EAAE,IAAI,OAAO,EAAE;SACvB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE;QACxB,EAAE,CAAC,6DAA6D,EAAE;;;;;wBAChE,WAAW,CAAC,OAAQ,CAAC,QAAQ,GAAG,GAAG,CAAC;wBACpC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAEpB,qBAAM,IAAA,uBAAU,EAAC,WAA0B,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAE7D,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC/B,+BAA+B;wBAC/B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;;;aACvC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE;;;;;wBAC7D,WAAW,CAAC,OAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;wBACzC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAEpB,qBAAM,IAAA,uBAAU,EAAC,WAA0B,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAE7D,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC/B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;;;aACvC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE;;;;;wBACzC,WAAW,CAAC,OAAQ,CAAC,QAAQ,GAAG,cAAc,CAAC;wBAC/C,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAEpB,qBAAM,IAAA,uBAAU,EAAC,WAA0B,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAE7D,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC/B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;;;aACvC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE;QAC3B,EAAE,CAAC,gDAAgD,EAAE;;;;;wBACnD,WAAW,CAAC,OAAQ,CAAC,QAAQ,GAAG,YAAY,CAAC;wBAC7C,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAEpB,qBAAM,IAAA,uBAAU,EAAC,WAA0B,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAE7D,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC/B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,kBAAkB;wBAE/C,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;wBAClD,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;wBACrC,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;;;;aAC3C,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE;;;;;wBAChE,WAAW,CAAC,OAAQ,CAAC,QAAQ,GAAG,YAAY,CAAC;wBAC7C,YAAY,CAAC,iBAAiB,CAAC;4BAC7B,GAAG,EAAE,SAAS;4BACd,KAAK,EAAE,kBAAkB;4BACzB,IAAI,EAAE,MAAM;yBACb,CAAC,CAAC;wBAEc,qBAAM,IAAA,uBAAU,EAAC,WAA0B,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAE7D,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC/B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;;;aACvC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE;;;;;wBACnD,WAAW,CAAC,OAAQ,CAAC,QAAQ,GAAG,UAAU,CAAC;wBAC3C,WAAW,CAAC,OAAQ,CAAC,MAAM,GAAG,eAAe,CAAC;wBAC9C,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAEpB,qBAAM,IAAA,uBAAU,EAAC,WAA0B,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAEvD,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;wBAClD,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,yCAAyC,CAAC,CAAC;;;;aACvE,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE;QACvB,EAAE,CAAC,mDAAmD,EAAE;;;;;wBACtD,WAAW,CAAC,OAAQ,CAAC,QAAQ,GAAG,kBAAkB,CAAC;wBACnD,YAAY,CAAC,iBAAiB,CAAC;4BAC7B,GAAG,EAAE,SAAS;4BACd,KAAK,EAAE,kBAAkB;4BACzB,IAAI,EAAE,MAAM;yBACb,CAAC,CAAC;wBAEc,qBAAM,IAAA,uBAAU,EAAC,WAA0B,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAE7D,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC/B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAErB,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA5B,IAAI,GAAG,SAAqB;wBAClC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;;;;aAClD,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE;;;;;wBACpD,WAAW,CAAC,OAAQ,CAAC,QAAQ,GAAG,kBAAkB,CAAC;wBACnD,YAAY,CAAC,iBAAiB,CAAC;4BAC7B,GAAG,EAAE,UAAU;4BACf,KAAK,EAAE,mBAAmB;4BAC1B,IAAI,EAAE,OAAO;yBACd,CAAC,CAAC;wBAEc,qBAAM,IAAA,uBAAU,EAAC,WAA0B,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAE7D,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC/B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBACtC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;;;aACvC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE;;;;;wBACrE,WAAW,CAAC,OAAQ,CAAC,QAAQ,GAAG,cAAc,CAAC;wBAC/C,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAEpB,qBAAM,IAAA,uBAAU,EAAC,WAA0B,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAE7D,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAC5B,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;wBAClD,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;;;;aACtC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE;QAC/B,EAAE,CAAC,6DAA6D,EAAE;;;;;wBAChE,WAAW,CAAC,OAAQ,CAAC,QAAQ,GAAG,cAAc,CAAC;wBAC/C,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAEpB,qBAAM,IAAA,uBAAU,EAAC,WAA0B,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAE7D,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBACrB,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA5B,IAAI,GAAG,SAAqB;wBAClC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;;;;aACpD,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE;;;;;wBAC9D,WAAW,CAAC,OAAQ,CAAC,QAAQ,GAAG,cAAc,CAAC;wBAC/C,YAAY,CAAC,iBAAiB,CAAC;4BAC7B,GAAG,EAAE,SAAS;4BACd,KAAK,EAAE,kBAAkB;4BACzB,IAAI,EAAE,MAAM;yBACb,CAAC,CAAC;wBAEc,qBAAM,IAAA,uBAAU,EAAC,WAA0B,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAE7D,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;;;aACvC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE;;;;;wBACtD,WAAW,CAAC,OAAQ,CAAC,QAAQ,GAAG,qBAAqB,CAAC;wBACtD,YAAY,CAAC,iBAAiB,CAAC;4BAC7B,GAAG,EAAE,SAAS;4BACd,KAAK,EAAE,kBAAkB;4BACzB,IAAI,EAAE,MAAM;yBACb,CAAC,CAAC;wBAEc,qBAAM,IAAA,uBAAU,EAAC,WAA0B,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAE7D,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBACrB,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA5B,IAAI,GAAG,SAAqB;wBAClC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;;;;aAClD,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE;;;;;wBAClD,WAAW,CAAC,OAAQ,CAAC,QAAQ,GAAG,qBAAqB,CAAC;wBACtD,YAAY,CAAC,iBAAiB,CAAC;4BAC7B,GAAG,EAAE,UAAU;4BACf,KAAK,EAAE,mBAAmB;4BAC1B,IAAI,EAAE,OAAO;yBACd,CAAC,CAAC;wBAEc,qBAAM,IAAA,uBAAU,EAAC,WAA0B,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAE7D,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBACtC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;;;aACvC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE;QAC9B,EAAE,CAAC,0DAA0D,EAAE;;;;;wBAC7D,WAAW,CAAC,OAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;wBACzC,YAAY,CAAC,iBAAiB,CAAC;4BAC7B,GAAG,EAAE,SAAS;4BACd,KAAK,EAAE,kBAAkB;4BACzB,IAAI,EAAE,MAAM;yBACb,CAAC,CAAC;wBAEc,qBAAM,IAAA,uBAAU,EAAC,WAA0B,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAE7D,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAC5B,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;wBAClD,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;;;;aAC1C,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE;;;;;wBAC9D,WAAW,CAAC,OAAQ,CAAC,QAAQ,GAAG,SAAS,CAAC;wBAC1C,YAAY,CAAC,iBAAiB,CAAC;4BAC7B,GAAG,EAAE,SAAS;4BACd,KAAK,EAAE,kBAAkB;4BACzB,IAAI,EAAE,MAAM;yBACb,CAAC,CAAC;wBAEc,qBAAM,IAAA,uBAAU,EAAC,WAA0B,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAE7D,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAC5B,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;wBAClD,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;;;;aAC1C,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE;;;;;wBACjE,WAAW,CAAC,OAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;wBACzC,WAAW,CAAC,OAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;wBACjE,YAAY,CAAC,iBAAiB,CAAC;4BAC7B,GAAG,EAAE,SAAS;4BACd,KAAK,EAAE,kBAAkB;4BACzB,IAAI,EAAE,MAAM;yBACb,CAAC,CAAC;wBAEc,qBAAM,IAAA,uBAAU,EAAC,WAA0B,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAEvD,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;wBAClD,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;;;;aACxC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE;QAC3B,EAAE,CAAC,0CAA0C,EAAE;;;;;wBAC7C,WAAW,CAAC,OAAQ,CAAC,QAAQ,GAAG,YAAY,CAAC;wBAC7C,YAAY,CAAC,iBAAiB,CAAC;4BAC7B,GAAG,EAAE,SAAS;4BACd,KAAK,EAAE,kBAAkB;4BACzB,IAAI,EAAE,MAAM;yBACb,CAAC,CAAC;wBAEc,qBAAM,IAAA,uBAAU,EAAC,WAA0B,CAAC,EAAA;;wBAAvD,QAAQ,GAAG,SAA4C;wBAE7D,oCAAoC;wBACpC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC9D,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;wBACrE,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC/D,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/auth/auth-middleware.test.ts"], "sourcesContent": ["/**\n * Comprehensive Authentication Middleware Tests\n * Tests route protection, admin access control, and security headers\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { getToken } from 'next-auth/jwt';\n\n// Mock next-auth/jwt\njest.mock('next-auth/jwt', () => ({\n  getToken: jest.fn(),\n}));\n\nconst mockGetToken = getToken as jest.MockedFunction<typeof getToken>;\n\n// Import middleware after mocking\nimport { middleware } from '../../../middleware';\n\ndescribe('Authentication Middleware', () => {\n  let mockRequest: Partial<NextRequest>;\n  \n  beforeEach(() => {\n    jest.clearAllMocks();\n    \n    mockRequest = {\n      nextUrl: {\n        pathname: '/dashboard',\n        search: '',\n        searchParams: new URLSearchParams(),\n      } as any,\n      url: 'http://localhost:3000/dashboard',\n      headers: new Headers(),\n    };\n  });\n\n  describe('Public Routes', () => {\n    it('should allow access to public routes without authentication', async () => {\n      mockRequest.nextUrl!.pathname = '/';\n      mockGetToken.mockResolvedValue(null);\n\n      const response = await middleware(mockRequest as NextRequest);\n      \n      expect(response).toBeDefined();\n      // Should not redirect to login\n      expect(response.status).not.toBe(307);\n    });\n\n    it('should allow access to auth pages when not authenticated', async () => {\n      mockRequest.nextUrl!.pathname = '/login';\n      mockGetToken.mockResolvedValue(null);\n\n      const response = await middleware(mockRequest as NextRequest);\n      \n      expect(response).toBeDefined();\n      expect(response.status).not.toBe(307);\n    });\n\n    it('should allow access to static assets', async () => {\n      mockRequest.nextUrl!.pathname = '/favicon.ico';\n      mockGetToken.mockResolvedValue(null);\n\n      const response = await middleware(mockRequest as NextRequest);\n      \n      expect(response).toBeDefined();\n      expect(response.status).not.toBe(307);\n    });\n  });\n\n  describe('Protected Routes', () => {\n    it('should redirect unauthenticated users to login', async () => {\n      mockRequest.nextUrl!.pathname = '/dashboard';\n      mockGetToken.mockResolvedValue(null);\n\n      const response = await middleware(mockRequest as NextRequest);\n      \n      expect(response).toBeDefined();\n      expect(response.status).toBe(307); // Redirect status\n      \n      const location = response.headers.get('location');\n      expect(location).toContain('/login');\n      expect(location).toContain('callbackUrl');\n    });\n\n    it('should allow authenticated users to access protected routes', async () => {\n      mockRequest.nextUrl!.pathname = '/dashboard';\n      mockGetToken.mockResolvedValue({\n        sub: 'user-id',\n        email: '<EMAIL>',\n        role: 'user'\n      });\n\n      const response = await middleware(mockRequest as NextRequest);\n      \n      expect(response).toBeDefined();\n      expect(response.status).not.toBe(307);\n    });\n\n    it('should preserve callback URL in login redirect', async () => {\n      mockRequest.nextUrl!.pathname = '/profile';\n      mockRequest.nextUrl!.search = '?tab=settings';\n      mockGetToken.mockResolvedValue(null);\n\n      const response = await middleware(mockRequest as NextRequest);\n      \n      const location = response.headers.get('location');\n      expect(location).toContain('callbackUrl=%2Fprofile%3Ftab%3Dsettings');\n    });\n  });\n\n  describe('Admin Routes', () => {\n    it('should redirect non-admin users from admin routes', async () => {\n      mockRequest.nextUrl!.pathname = '/admin/dashboard';\n      mockGetToken.mockResolvedValue({\n        sub: 'user-id',\n        email: '<EMAIL>',\n        role: 'user'\n      });\n\n      const response = await middleware(mockRequest as NextRequest);\n      \n      expect(response).toBeDefined();\n      expect(response.status).toBe(403);\n      \n      const body = await response.json();\n      expect(body.error).toBe('Admin access required');\n    });\n\n    it('should allow admin users to access admin routes', async () => {\n      mockRequest.nextUrl!.pathname = '/admin/dashboard';\n      mockGetToken.mockResolvedValue({\n        sub: 'admin-id',\n        email: '<EMAIL>',\n        role: 'admin'\n      });\n\n      const response = await middleware(mockRequest as NextRequest);\n      \n      expect(response).toBeDefined();\n      expect(response.status).not.toBe(403);\n      expect(response.status).not.toBe(307);\n    });\n\n    it('should redirect unauthenticated users from admin routes to login', async () => {\n      mockRequest.nextUrl!.pathname = '/admin/users';\n      mockGetToken.mockResolvedValue(null);\n\n      const response = await middleware(mockRequest as NextRequest);\n      \n      expect(response.status).toBe(307);\n      const location = response.headers.get('location');\n      expect(location).toContain('/login');\n    });\n  });\n\n  describe('API Route Protection', () => {\n    it('should block unauthenticated access to protected API routes', async () => {\n      mockRequest.nextUrl!.pathname = '/api/profile';\n      mockGetToken.mockResolvedValue(null);\n\n      const response = await middleware(mockRequest as NextRequest);\n\n      expect(response.status).toBe(401);\n      const body = await response.json();\n      expect(body.error).toBe('Authentication required');\n    });\n\n    it('should allow authenticated access to protected API routes', async () => {\n      mockRequest.nextUrl!.pathname = '/api/profile';\n      mockGetToken.mockResolvedValue({\n        sub: 'user-id',\n        email: '<EMAIL>',\n        role: 'user'\n      });\n\n      const response = await middleware(mockRequest as NextRequest);\n      \n      expect(response.status).not.toBe(401);\n    });\n\n    it('should block non-admin access to admin API routes', async () => {\n      mockRequest.nextUrl!.pathname = '/api/admin/database';\n      mockGetToken.mockResolvedValue({\n        sub: 'user-id',\n        email: '<EMAIL>',\n        role: 'user'\n      });\n\n      const response = await middleware(mockRequest as NextRequest);\n      \n      expect(response.status).toBe(403);\n      const body = await response.json();\n      expect(body.error).toBe('Admin access required');\n    });\n\n    it('should allow admin access to admin API routes', async () => {\n      mockRequest.nextUrl!.pathname = '/api/admin/database';\n      mockGetToken.mockResolvedValue({\n        sub: 'admin-id',\n        email: '<EMAIL>',\n        role: 'admin'\n      });\n\n      const response = await middleware(mockRequest as NextRequest);\n      \n      expect(response.status).not.toBe(403);\n      expect(response.status).not.toBe(401);\n    });\n  });\n\n  describe('Auth Page Redirects', () => {\n    it('should redirect authenticated users away from login page', async () => {\n      mockRequest.nextUrl!.pathname = '/login';\n      mockGetToken.mockResolvedValue({\n        sub: 'user-id',\n        email: '<EMAIL>',\n        role: 'user'\n      });\n\n      const response = await middleware(mockRequest as NextRequest);\n      \n      expect(response.status).toBe(307);\n      const location = response.headers.get('location');\n      expect(location).toContain('/dashboard');\n    });\n\n    it('should redirect authenticated users away from signup page', async () => {\n      mockRequest.nextUrl!.pathname = '/signup';\n      mockGetToken.mockResolvedValue({\n        sub: 'user-id',\n        email: '<EMAIL>',\n        role: 'user'\n      });\n\n      const response = await middleware(mockRequest as NextRequest);\n      \n      expect(response.status).toBe(307);\n      const location = response.headers.get('location');\n      expect(location).toContain('/dashboard');\n    });\n\n    it('should respect callback URL when redirecting from auth pages', async () => {\n      mockRequest.nextUrl!.pathname = '/login';\n      mockRequest.nextUrl!.searchParams.set('callbackUrl', '/profile');\n      mockGetToken.mockResolvedValue({\n        sub: 'user-id',\n        email: '<EMAIL>',\n        role: 'user'\n      });\n\n      const response = await middleware(mockRequest as NextRequest);\n      \n      const location = response.headers.get('location');\n      expect(location).toContain('/profile');\n    });\n  });\n\n  describe('Security Headers', () => {\n    it('should add security headers to responses', async () => {\n      mockRequest.nextUrl!.pathname = '/dashboard';\n      mockGetToken.mockResolvedValue({\n        sub: 'user-id',\n        email: '<EMAIL>',\n        role: 'user'\n      });\n\n      const response = await middleware(mockRequest as NextRequest);\n      \n      // Check for common security headers\n      expect(response.headers.get('X-Frame-Options')).toBeDefined();\n      expect(response.headers.get('X-Content-Type-Options')).toBeDefined();\n      expect(response.headers.get('Referrer-Policy')).toBeDefined();\n    });\n  });\n});\n"], "version": 3}