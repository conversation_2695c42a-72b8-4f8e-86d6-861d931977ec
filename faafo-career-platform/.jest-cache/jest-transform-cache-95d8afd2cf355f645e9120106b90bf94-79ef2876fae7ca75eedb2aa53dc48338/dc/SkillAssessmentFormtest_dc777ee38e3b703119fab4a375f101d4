475522801330ad084a1839fa8360b58e
"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
// Mock toast
jest.mock('sonner', function () { return ({
    toast: {
        error: jest.fn(),
        success: jest.fn(),
    },
}); });
// Mock UI components that cause issues in tests
jest.mock('@/components/ui/slider', function () { return ({
    Slider: function (_a) {
        var value = _a.value, onValueChange = _a.onValueChange, props = __rest(_a, ["value", "onValueChange"]);
        return ((0, jsx_runtime_1.jsx)("input", __assign({ type: "range", value: value[0], onChange: function (e) { return onValueChange([parseInt(e.target.value)]); }, "data-testid": "slider" }, props)));
    },
}); });
jest.mock('@/components/ui/card', function () { return ({
    Card: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("div", __assign({ "data-testid": "card" }, props, { children: children }));
    },
    CardContent: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("div", __assign({ "data-testid": "card-content" }, props, { children: children }));
    },
    CardDescription: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("div", __assign({ "data-testid": "card-description" }, props, { children: children }));
    },
    CardHeader: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("div", __assign({ "data-testid": "card-header" }, props, { children: children }));
    },
    CardTitle: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("div", __assign({ "data-testid": "card-title" }, props, { children: children }));
    },
}); });
jest.mock('@/components/ui/button', function () { return ({
    Button: function (_a) {
        var children = _a.children, onClick = _a.onClick, disabled = _a.disabled, props = __rest(_a, ["children", "onClick", "disabled"]);
        return ((0, jsx_runtime_1.jsx)("button", __assign({ onClick: onClick, disabled: disabled, "data-testid": "button" }, props, { children: children })));
    },
}); });
jest.mock('@/components/ui/input', function () { return ({
    Input: function (props) { return (0, jsx_runtime_1.jsx)("input", __assign({ "data-testid": "input" }, props)); },
}); });
jest.mock('@/components/ui/label', function () { return ({
    Label: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("label", __assign({ "data-testid": "label" }, props, { children: children }));
    },
}); });
jest.mock('@/components/ui/textarea', function () { return ({
    Textarea: function (props) { return (0, jsx_runtime_1.jsx)("textarea", __assign({ "data-testid": "textarea" }, props)); },
}); });
jest.mock('@/components/ui/badge', function () { return ({
    Badge: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("span", __assign({ "data-testid": "badge" }, props, { children: children }));
    },
}); });
jest.mock('@/components/ui/progress', function () { return ({
    Progress: function (_a) {
        var value = _a.value, props = __rest(_a, ["value"]);
        return (0, jsx_runtime_1.jsx)("div", __assign({ "data-testid": "progress", "data-value": value }, props));
    },
}); });
jest.mock('@/components/ui/alert', function () { return ({
    Alert: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("div", __assign({ "data-testid": "alert" }, props, { children: children }));
    },
    AlertDescription: function (_a) {
        var children = _a.children, props = __rest(_a, ["children"]);
        return (0, jsx_runtime_1.jsx)("div", __assign({ "data-testid": "alert-description" }, props, { children: children }));
    },
}); });
// Mock Lucide icons
jest.mock('lucide-react', function () { return ({
    Loader2: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "loader2-icon" }); },
    Star: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "star-icon" }); },
    TrendingUp: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "trending-up-icon" }); },
    BookOpen: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "book-open-icon" }); },
    Award: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "award-icon" }); },
}); });
var react_1 = __importDefault(require("react"));
var react_2 = require("@testing-library/react");
var user_event_1 = __importDefault(require("@testing-library/user-event"));
require("@testing-library/jest-dom");
var SkillAssessmentForm_1 = __importDefault(require("@/components/skills/SkillAssessmentForm"));
// Mock skill data
var mockSkills = [
    {
        id: 'skill-1',
        name: 'JavaScript',
        category: 'Programming',
        description: 'JavaScript programming language',
    },
    {
        id: 'skill-2',
        name: 'React',
        category: 'Frontend',
        description: 'React framework',
    },
    {
        id: 'common-1',
        name: 'Python',
        category: 'Programming',
        description: 'Python programming language',
    },
];
var mockInitialAssessments = [
    {
        skillId: 'skill-1',
        skillName: 'JavaScript',
        selfRating: 7,
        confidenceLevel: 8,
        assessmentType: 'SELF_ASSESSMENT',
        yearsOfExperience: 3,
        lastUsed: 'Currently using',
        notes: 'Strong in ES6+',
    },
];
describe('SkillAssessmentForm - TDD State Synchronization Fix', function () {
    var mockOnSubmit = jest.fn();
    var mockOnSkillSearch = jest.fn();
    var mockOnAssessmentsChange = jest.fn();
    beforeEach(function () {
        jest.clearAllMocks();
        mockOnSkillSearch.mockResolvedValue(mockSkills);
    });
    describe('State Synchronization (Critical Bug Fix)', function () {
        it('should sync internal state with initialAssessments prop on mount', function () { return __awaiter(void 0, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch, initialAssessments: mockInitialAssessments, onAssessmentsChange: mockOnAssessmentsChange }));
                        // Verify initial assessment data is displayed
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByDisplayValue('JavaScript')).toBeInTheDocument();
                            })];
                    case 1:
                        // Verify initial assessment data is displayed
                        _a.sent();
                        expect(react_2.screen.getByDisplayValue('Currently using')).toBeInTheDocument();
                        expect(react_2.screen.getByDisplayValue('Strong in ES6+')).toBeInTheDocument();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should update internal state when initialAssessments prop changes', function () { return __awaiter(void 0, void 0, void 0, function () {
            var rerender;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        rerender = (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch, initialAssessments: [], onAssessmentsChange: mockOnAssessmentsChange })).rerender;
                        // Initially should show empty form
                        expect(react_2.screen.getByPlaceholderText('e.g., JavaScript, React, Python')).toHaveValue('');
                        // Update with initial assessments
                        rerender((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch, initialAssessments: mockInitialAssessments, onAssessmentsChange: mockOnAssessmentsChange }));
                        // Should now show the assessment data
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByDisplayValue('JavaScript')).toBeInTheDocument();
                            })];
                    case 1:
                        // Should now show the assessment data
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should call onAssessmentsChange when internal state updates', function () { return __awaiter(void 0, void 0, void 0, function () {
            var skillInput;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch, initialAssessments: [], onAssessmentsChange: mockOnAssessmentsChange }));
                        skillInput = react_2.screen.getByPlaceholderText('e.g., JavaScript, React, Python');
                        return [4 /*yield*/, user_event_1.default.type(skillInput, 'TypeScript')];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockOnAssessmentsChange).toHaveBeenCalled();
                            })];
                    case 2:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Search Result Selection', function () {
        it('should populate form fields when selecting a skill from search results', function () { return __awaiter(void 0, void 0, void 0, function () {
            var searchInput, searchResult;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch, onAssessmentsChange: mockOnAssessmentsChange }));
                        searchInput = react_2.screen.getByPlaceholderText('Search for skills to assess...');
                        // Type to trigger search
                        return [4 /*yield*/, user_event_1.default.type(searchInput, 'Java')];
                    case 1:
                        // Type to trigger search
                        _a.sent();
                        // Wait for search results
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockOnSkillSearch).toHaveBeenCalledWith('Java');
                            })];
                    case 2:
                        // Wait for search results
                        _a.sent();
                        return [4 /*yield*/, react_2.screen.findByText('JavaScript')];
                    case 3:
                        searchResult = _a.sent();
                        return [4 /*yield*/, user_event_1.default.click(searchResult)];
                    case 4:
                        _a.sent();
                        // Verify form field is populated
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                var skillNameInput = react_2.screen.getByPlaceholderText('e.g., JavaScript, React, Python');
                                expect(skillNameInput).toHaveValue('JavaScript');
                            })];
                    case 5:
                        // Verify form field is populated
                        _a.sent();
                        // Verify search is cleared
                        expect(searchInput).toHaveValue('');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle fallback skills (common-* ids) correctly', function () { return __awaiter(void 0, void 0, void 0, function () {
            var searchInput, searchResult;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch, onAssessmentsChange: mockOnAssessmentsChange }));
                        searchInput = react_2.screen.getByPlaceholderText('Search for skills to assess...');
                        return [4 /*yield*/, user_event_1.default.type(searchInput, 'Python')];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockOnSkillSearch).toHaveBeenCalledWith('Python');
                            })];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, react_2.screen.findByText('Python')];
                    case 3:
                        searchResult = _a.sent();
                        return [4 /*yield*/, user_event_1.default.click(searchResult)];
                    case 4:
                        _a.sent();
                        // Verify form field is populated
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                var skillNameInput = react_2.screen.getByPlaceholderText('e.g., JavaScript, React, Python');
                                expect(skillNameInput).toHaveValue('Python');
                            })];
                    case 5:
                        // Verify form field is populated
                        _a.sent();
                        // Verify onAssessmentsChange was called with updated data
                        expect(mockOnAssessmentsChange).toHaveBeenCalled();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Form Validation', function () {
        it('should validate required skill name', function () { return __awaiter(void 0, void 0, void 0, function () {
            var submitButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch }));
                        submitButton = react_2.screen.getByText('Submit Assessment');
                        return [4 /*yield*/, user_event_1.default.click(submitButton)];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(react_2.screen.getByText('Skill name is required')).toBeInTheDocument();
                            })];
                    case 2:
                        _a.sent();
                        expect(mockOnSubmit).not.toHaveBeenCalled();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should validate rating ranges', function () { return __awaiter(void 0, void 0, void 0, function () {
            var skillInput, submitButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch }));
                        skillInput = react_2.screen.getByPlaceholderText('e.g., JavaScript, React, Python');
                        return [4 /*yield*/, user_event_1.default.type(skillInput, 'JavaScript')];
                    case 1:
                        _a.sent();
                        submitButton = react_2.screen.getByText('Submit Assessment');
                        return [4 /*yield*/, user_event_1.default.click(submitButton)];
                    case 2:
                        _a.sent();
                        // Should pass validation with default values (5/10)
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockOnSubmit).toHaveBeenCalled();
                            })];
                    case 3:
                        // Should pass validation with default values (5/10)
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Bulk Mode', function () {
        it('should allow adding multiple assessments in bulk mode', function () { return __awaiter(void 0, void 0, void 0, function () {
            var addButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch, mode: "bulk", maxAssessments: 5 }));
                        // Should start with one assessment
                        expect(react_2.screen.getByText('Assessment 1')).toBeInTheDocument();
                        addButton = react_2.screen.getByText('Add Another Skill Assessment');
                        return [4 /*yield*/, user_event_1.default.click(addButton)];
                    case 1:
                        _a.sent();
                        // Should now have two assessments
                        expect(react_2.screen.getByText('Assessment 2')).toBeInTheDocument();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should preserve state on submit in bulk mode', function () { return __awaiter(void 0, void 0, void 0, function () {
            var skillInput, submitButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearch, mode: "bulk", preserveStateOnSubmit: true }));
                        skillInput = react_2.screen.getByPlaceholderText('e.g., JavaScript, React, Python');
                        return [4 /*yield*/, user_event_1.default.type(skillInput, 'JavaScript')];
                    case 1:
                        _a.sent();
                        submitButton = react_2.screen.getByText('Submit Assessment');
                        return [4 /*yield*/, user_event_1.default.click(submitButton)];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockOnSubmit).toHaveBeenCalled();
                            })];
                    case 3:
                        _a.sent();
                        // Form should still have the data
                        expect(skillInput).toHaveValue('JavaScript');
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Error Handling', function () {
        it('should handle search errors gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockOnSkillSearchError, searchInput;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockOnSkillSearchError = jest.fn().mockRejectedValue(new Error('Search failed'));
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmit, onSkillSearch: mockOnSkillSearchError }));
                        searchInput = react_2.screen.getByPlaceholderText('Search for skills to assess...');
                        return [4 /*yield*/, user_event_1.default.type(searchInput, 'Java')];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockOnSkillSearchError).toHaveBeenCalledWith('Java');
                            })];
                    case 2:
                        _a.sent();
                        // Should not crash and should handle error gracefully
                        expect(searchInput).toBeInTheDocument();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle submit errors gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockOnSubmitError, skillInput, submitButton;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockOnSubmitError = jest.fn().mockRejectedValue(new Error('Submit failed'));
                        (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillAssessmentForm_1.default, { onSubmit: mockOnSubmitError, onSkillSearch: mockOnSkillSearch }));
                        skillInput = react_2.screen.getByPlaceholderText('e.g., JavaScript, React, Python');
                        return [4 /*yield*/, user_event_1.default.type(skillInput, 'JavaScript')];
                    case 1:
                        _a.sent();
                        submitButton = react_2.screen.getByText('Submit Assessment');
                        return [4 /*yield*/, user_event_1.default.click(submitButton)];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, (0, react_2.waitFor)(function () {
                                expect(mockOnSubmitError).toHaveBeenCalled();
                            })];
                    case 3:
                        _a.sent();
                        // Button should not be stuck in loading state
                        expect(react_2.screen.getByText('Submit Assessment')).toBeInTheDocument();
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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