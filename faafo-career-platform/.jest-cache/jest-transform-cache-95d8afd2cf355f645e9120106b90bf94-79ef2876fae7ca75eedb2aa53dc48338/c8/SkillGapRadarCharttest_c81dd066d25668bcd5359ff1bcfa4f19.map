{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/skills/visualizations/SkillGapRadarChart.test.tsx", "mappings": ";;;;;;AAKA,2BAA2B;AAC3B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,cAAM,OAAA,CAAC;IAC3B,UAAU,EAAE,UAAC,EAAiB;YAAf,QAAQ,cAAA;QAAY,OAAA,+CAAiB,aAAa,YAAE,QAAQ,GAAO;IAA/C,CAA+C;IAClF,SAAS,EAAE,cAAM,OAAA,+CAAiB,YAAY,GAAG,EAAhC,CAAgC;IACjD,cAAc,EAAE,UAAC,EAAgB;YAAd,OAAO,aAAA;QAAY,OAAA,+CAAiB,kBAAkB,cAAW,OAAO,GAAI;IAAzD,CAAyD;IAC/F,eAAe,EAAE,cAAM,OAAA,+CAAiB,mBAAmB,GAAG,EAAvC,CAAuC;IAC9D,KAAK,EAAE,UAAC,EAA8B;YAA5B,IAAI,UAAA,EAAE,OAAO,aAAA,EAAE,MAAM,YAAA;QAAY,OAAA,CACzC,+CAAiB,OAAO,eAAY,IAAI,cAAY,OAAO,iBAAe,MAAM,GAAI,CACrF;IAF0C,CAE1C;IACD,mBAAmB,EAAE,UAAC,EAAiB;YAAf,QAAQ,cAAA;QAAY,OAAA,+CAAiB,sBAAsB,YAAE,QAAQ,GAAO;IAAxD,CAAwD;IACpG,MAAM,EAAE,cAAM,OAAA,+CAAiB,QAAQ,GAAG,EAA5B,CAA4B;IAC1C,OAAO,EAAE,cAAM,OAAA,+CAAiB,SAAS,GAAG,EAA7B,CAA6B;CAC7C,CAAC,EAX0B,CAW1B,CAAC,CAAC;AAjBJ,gDAA0B;AAC1B,gDAAwD;AACxD,qCAAmC;AACnC,6GAAuF;AAgBvF,IAAM,aAAa,GAAG;IACpB;QACE,KAAK,EAAE,YAAY;QACnB,OAAO,EAAE,CAAC;QACV,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,CAAC;QACT,QAAQ,EAAE,aAAa;KACxB;IACD;QACE,KAAK,EAAE,OAAO;QACd,OAAO,EAAE,CAAC;QACV,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,CAAC;QACT,QAAQ,EAAE,UAAU;KACrB;IACD;QACE,KAAK,EAAE,SAAS;QAChB,OAAO,EAAE,CAAC;QACV,MAAM,EAAE,CAAC;QACT,MAAM,EAAE,CAAC;QACT,QAAQ,EAAE,SAAS;KACpB;CACF,CAAC;AAEF,QAAQ,CAAC,oBAAoB,EAAE;IAC7B,QAAQ,CAAC,qBAAqB,EAAE;QAC9B,EAAE,CAAC,kCAAkC,EAAE;YACrC,IAAA,cAAM,EAAC,uBAAC,4BAAkB,IAAC,IAAI,EAAE,aAAa,GAAI,CAAC,CAAC;YAEpD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACnE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iEAAiE,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAChH,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE;YACpD,IAAM,WAAW,GAAG,uBAAuB,CAAC;YAC5C,IAAM,iBAAiB,GAAG,gCAAgC,CAAC;YAE3D,IAAA,cAAM,EACJ,uBAAC,4BAAkB,IACjB,IAAI,EAAE,aAAa,EACnB,KAAK,EAAE,WAAW,EAClB,WAAW,EAAE,iBAAiB,GAC9B,CACH,CAAC;YAEF,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC1D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE;YACxD,IAAA,cAAM,EACJ,uBAAC,4BAAkB,IACjB,IAAI,EAAE,aAAa,EACnB,KAAK,EAAC,YAAY,EAClB,WAAW,EAAC,EAAE,GACd,CACH,CAAC;YAEF,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC3D,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,6BAA6B,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;QACpF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE;QAC3B,EAAE,CAAC,6CAA6C,EAAE;YAChD,IAAA,cAAM,EAAC,uBAAC,4BAAkB,IAAC,IAAI,EAAE,aAAa,GAAI,CAAC,CAAC;YAEpD,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACvE,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC9D,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC7D,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACnE,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACpE,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE;YACpE,IAAA,cAAM,EAAC,uBAAC,4BAAkB,IAAC,IAAI,EAAE,aAAa,GAAI,CAAC,CAAC;YAEpD,IAAM,aAAa,GAAG,cAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEtC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YACvE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAChE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YAEnE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YACtE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC/D,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YAEnE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;YACxE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC/D,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE;YACjD,IAAA,cAAM,EAAC,uBAAC,4BAAkB,IAAC,IAAI,EAAE,aAAa,EAAE,UAAU,EAAE,IAAI,GAAI,CAAC,CAAC;YACtE,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE;YACtD,IAAA,cAAM,EAAC,uBAAC,4BAAkB,IAAC,IAAI,EAAE,aAAa,EAAE,UAAU,EAAE,KAAK,GAAI,CAAC,CAAC;YACvE,MAAM,CAAC,cAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE;QAC7B,EAAE,CAAC,8DAA8D,EAAE;YACjE,IAAA,cAAM,EAAC,uBAAC,4BAAkB,IAAC,IAAI,EAAE,aAAa,GAAI,CAAC,CAAC;YAEpD,yCAAyC;YACzC,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACvD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE;YAChE,IAAA,cAAM,EAAC,uBAAC,4BAAkB,IAAC,IAAI,EAAE,aAAa,GAAI,CAAC,CAAC;YAEpD,wCAAwC;YACxC,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACvD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE;YACxD,IAAA,cAAM,EAAC,uBAAC,4BAAkB,IAAC,IAAI,EAAE,aAAa,GAAI,CAAC,CAAC;YAEpD,oEAAoE;YACpE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACpD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE;YACxC,IAAA,cAAM,EAAC,uBAAC,4BAAkB,IAAC,IAAI,EAAE,EAAE,GAAI,CAAC,CAAC;YAEzC,MAAM,CAAC,cAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,8BAA8B;YACrF,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC,CAAC,eAAe;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE;YAC9C,IAAM,WAAW,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,IAAA,cAAM,EAAC,uBAAC,4BAAkB,IAAC,IAAI,EAAE,WAAW,GAAI,CAAC,CAAC;YAElD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC,CAAC,UAAU;YAClE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC,CAAC,SAAS;YACjE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC,CAAC,MAAM;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE;QACxB,EAAE,CAAC,uCAAuC,EAAE;YAC1C,IAAM,aAAa,GAAG;gBACpB;oBACE,KAAK,EAAE,WAAW;oBAClB,OAAO,EAAE,CAAC;oBACV,MAAM,EAAE,CAAC;oBACT,MAAM,EAAE,CAAC;oBACT,QAAQ,EAAE,UAAU;iBACrB;aACF,CAAC;YAEF,IAAA,cAAM,EAAC,uBAAC,4BAAkB,IAAC,IAAI,EAAE,aAAa,GAAI,CAAC,CAAC;YAEpD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACvD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACvD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC,CAAC,eAAe;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE;YACtD,IAAM,cAAc,GAAG;gBACrB;oBACE,KAAK,EAAE,cAAc;oBACrB,OAAO,EAAE,CAAC;oBACV,MAAM,EAAE,CAAC;oBACT,MAAM,EAAE,CAAC;oBACT,QAAQ,EAAE,UAAU;iBACrB;aACF,CAAC;YAEF,IAAA,cAAM,EAAC,uBAAC,4BAAkB,IAAC,IAAI,EAAE,cAAc,GAAI,CAAC,CAAC;YAErD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACvD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACvD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC,CAAC,uBAAuB;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE;YACpD,IAAA,cAAM,EAAC,uBAAC,4BAAkB,IAAC,IAAI,EAAE,aAAa,GAAI,CAAC,CAAC;YAEpD,IAAM,cAAc,GAAG,cAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;YAC9D,MAAM,CAAC,cAAc,CAAC,CAAC,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE;QACxB,EAAE,CAAC,sCAAsC,EAAE;YACzC,IAAA,cAAM,EAAC,uBAAC,4BAAkB,IAAC,IAAI,EAAE,aAAa,GAAI,CAAC,CAAC;YAEpD,IAAM,KAAK,GAAG,cAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,+CAA+C;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE;YACpD,IAAA,cAAM,EAAC,uBAAC,4BAAkB,IAAC,IAAI,EAAE,aAAa,GAAI,CAAC,CAAC;YAEpD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iEAAiE,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAClH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE;QACxB,EAAE,CAAC,6BAA6B,EAAE;YAChC,IAAM,YAAY,GAAG,GAAG,CAAC;YACzB,IAAA,cAAM,EAAC,uBAAC,4BAAkB,IAAC,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,GAAI,CAAC,CAAC;YAE1E,IAAM,SAAS,GAAG,cAAM,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,aAAa,CAAC;YAC3E,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,kBAAW,YAAY,OAAI,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE;YAClC,sFAAsF;YACtF,qEAAqE;YACrE,6EAA6E;YAC7E,IAAA,cAAM,EAAC,uBAAC,4BAAkB,IAAC,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,CAAC,GAAI,CAAC,CAAC;YAEjE,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/skills/visualizations/SkillGapRadarChart.test.tsx"], "sourcesContent": ["import React from 'react';\nimport { render, screen } from '@testing-library/react';\nimport '@testing-library/jest-dom';\nimport SkillGapRadar<PERSON>hart from '@/components/skills/visualizations/SkillGapRadarChart';\n\n// Mock recharts components\njest.mock('recharts', () => ({\n  RadarChart: ({ children }: any) => <div data-testid=\"radar-chart\">{children}</div>,\n  PolarGrid: () => <div data-testid=\"polar-grid\" />,\n  PolarAngleAxis: ({ dataKey }: any) => <div data-testid=\"polar-angle-axis\" data-key={dataKey} />,\n  PolarRadiusAxis: () => <div data-testid=\"polar-radius-axis\" />,\n  Radar: ({ name, dataKey, stroke }: any) => (\n    <div data-testid=\"radar\" data-name={name} data-key={dataKey} data-stroke={stroke} />\n  ),\n  ResponsiveContainer: ({ children }: any) => <div data-testid=\"responsive-container\">{children}</div>,\n  Legend: () => <div data-testid=\"legend\" />,\n  Tooltip: () => <div data-testid=\"tooltip\" />,\n}));\n\nconst mockSkillData = [\n  {\n    skill: 'JavaScript',\n    current: 7,\n    target: 9,\n    market: 8,\n    category: 'Programming',\n  },\n  {\n    skill: 'React',\n    current: 6,\n    target: 8,\n    market: 7,\n    category: 'Frontend',\n  },\n  {\n    skill: 'Node.js',\n    current: 5,\n    target: 7,\n    market: 6,\n    category: 'Backend',\n  },\n];\n\ndescribe('SkillGapRadarChart', () => {\n  describe('Component Rendering', () => {\n    it('should render with default props', () => {\n      render(<SkillGapRadarChart data={mockSkillData} />);\n      \n      expect(screen.getByText('Skill Gap Analysis')).toBeInTheDocument();\n      expect(screen.getByText('Compare your current skills with target and market requirements')).toBeInTheDocument();\n      expect(screen.getByTestId('radar-chart')).toBeInTheDocument();\n    });\n\n    it('should render with custom title and description', () => {\n      const customTitle = 'Custom Skill Analysis';\n      const customDescription = 'Custom description for testing';\n      \n      render(\n        <SkillGapRadarChart\n          data={mockSkillData}\n          title={customTitle}\n          description={customDescription}\n        />\n      );\n      \n      expect(screen.getByText(customTitle)).toBeInTheDocument();\n      expect(screen.getByText(customDescription)).toBeInTheDocument();\n    });\n\n    it('should render without description when not provided', () => {\n      render(\n        <SkillGapRadarChart\n          data={mockSkillData}\n          title=\"Test Title\"\n          description=\"\"\n        />\n      );\n      \n      expect(screen.getByText('Test Title')).toBeInTheDocument();\n      expect(screen.queryByText('Compare your current skills')).not.toBeInTheDocument();\n    });\n  });\n\n  describe('Chart Components', () => {\n    it('should render all required chart components', () => {\n      render(<SkillGapRadarChart data={mockSkillData} />);\n      \n      expect(screen.getByTestId('responsive-container')).toBeInTheDocument();\n      expect(screen.getByTestId('radar-chart')).toBeInTheDocument();\n      expect(screen.getByTestId('polar-grid')).toBeInTheDocument();\n      expect(screen.getByTestId('polar-angle-axis')).toBeInTheDocument();\n      expect(screen.getByTestId('polar-radius-axis')).toBeInTheDocument();\n      expect(screen.getByTestId('tooltip')).toBeInTheDocument();\n    });\n\n    it('should render three radar lines for current, target, and market', () => {\n      render(<SkillGapRadarChart data={mockSkillData} />);\n      \n      const radarElements = screen.getAllByTestId('radar');\n      expect(radarElements).toHaveLength(3);\n      \n      expect(radarElements[0]).toHaveAttribute('data-name', 'Current Level');\n      expect(radarElements[0]).toHaveAttribute('data-key', 'current');\n      expect(radarElements[0]).toHaveAttribute('data-stroke', '#3b82f6');\n      \n      expect(radarElements[1]).toHaveAttribute('data-name', 'Target Level');\n      expect(radarElements[1]).toHaveAttribute('data-key', 'target');\n      expect(radarElements[1]).toHaveAttribute('data-stroke', '#10b981');\n      \n      expect(radarElements[2]).toHaveAttribute('data-name', 'Market Average');\n      expect(radarElements[2]).toHaveAttribute('data-key', 'market');\n      expect(radarElements[2]).toHaveAttribute('data-stroke', '#f59e0b');\n    });\n\n    it('should render legend when showLegend is true', () => {\n      render(<SkillGapRadarChart data={mockSkillData} showLegend={true} />);\n      expect(screen.getByTestId('legend')).toBeInTheDocument();\n    });\n\n    it('should not render legend when showLegend is false', () => {\n      render(<SkillGapRadarChart data={mockSkillData} showLegend={false} />);\n      expect(screen.queryByTestId('legend')).not.toBeInTheDocument();\n    });\n  });\n\n  describe('Summary Statistics', () => {\n    it('should calculate and display average current level correctly', () => {\n      render(<SkillGapRadarChart data={mockSkillData} />);\n      \n      // Average current: (7 + 6 + 5) / 3 = 6.0\n      expect(screen.getByText('6.0/10')).toBeInTheDocument();\n      expect(screen.getByText('Average Current')).toBeInTheDocument();\n    });\n\n    it('should calculate and display average target level correctly', () => {\n      render(<SkillGapRadarChart data={mockSkillData} />);\n      \n      // Average target: (9 + 8 + 7) / 3 = 8.0\n      expect(screen.getByText('8.0/10')).toBeInTheDocument();\n      expect(screen.getByText('Average Target')).toBeInTheDocument();\n    });\n\n    it('should calculate and display gap to close correctly', () => {\n      render(<SkillGapRadarChart data={mockSkillData} />);\n      \n      // Gap to close: ((9-7) + (8-6) + (7-5)) / 3 = (2 + 2 + 2) / 3 = 2.0\n      expect(screen.getByText('2.0')).toBeInTheDocument();\n      expect(screen.getByText('Gap to Close')).toBeInTheDocument();\n    });\n\n    it('should handle empty data gracefully', () => {\n      render(<SkillGapRadarChart data={[]} />);\n\n      expect(screen.getAllByText('0.0/10')).toHaveLength(2); // Current and Target averages\n      expect(screen.getByText('0.0')).toBeInTheDocument(); // Gap to close\n    });\n\n    it('should handle single skill data correctly', () => {\n      const singleSkill = [mockSkillData[0]];\n      render(<SkillGapRadarChart data={singleSkill} />);\n      \n      expect(screen.getByText('7.0/10')).toBeInTheDocument(); // Current\n      expect(screen.getByText('9.0/10')).toBeInTheDocument(); // Target\n      expect(screen.getByText('2.0')).toBeInTheDocument(); // Gap\n    });\n  });\n\n  describe('Data Handling', () => {\n    it('should handle skills with zero values', () => {\n      const dataWithZeros = [\n        {\n          skill: 'New Skill',\n          current: 0,\n          target: 5,\n          market: 3,\n          category: 'Learning',\n        },\n      ];\n      \n      render(<SkillGapRadarChart data={dataWithZeros} />);\n      \n      expect(screen.getByText('0.0/10')).toBeInTheDocument();\n      expect(screen.getByText('5.0/10')).toBeInTheDocument();\n      expect(screen.getByText('5.0')).toBeInTheDocument(); // Gap: 5-0 = 5\n    });\n\n    it('should handle skills where current exceeds target', () => {\n      const dataWithExcess = [\n        {\n          skill: 'Expert Skill',\n          current: 9,\n          target: 7,\n          market: 8,\n          category: 'Advanced',\n        },\n      ];\n      \n      render(<SkillGapRadarChart data={dataWithExcess} />);\n      \n      expect(screen.getByText('9.0/10')).toBeInTheDocument();\n      expect(screen.getByText('7.0/10')).toBeInTheDocument();\n      expect(screen.getByText('0.0')).toBeInTheDocument(); // Gap: max(0, 7-9) = 0\n    });\n\n    it('should set correct dataKey for polar angle axis', () => {\n      render(<SkillGapRadarChart data={mockSkillData} />);\n      \n      const polarAngleAxis = screen.getByTestId('polar-angle-axis');\n      expect(polarAngleAxis).toHaveAttribute('data-key', 'skill');\n    });\n  });\n\n  describe('Accessibility', () => {\n    it('should have proper heading structure', () => {\n      render(<SkillGapRadarChart data={mockSkillData} />);\n\n      const title = screen.getByText('Skill Gap Analysis');\n      expect(title.tagName).toBe('DIV'); // CardTitle renders as div with proper styling\n    });\n\n    it('should have descriptive text for screen readers', () => {\n      render(<SkillGapRadarChart data={mockSkillData} />);\n      \n      expect(screen.getByText('Compare your current skills with target and market requirements')).toBeInTheDocument();\n    });\n  });\n\n  describe('Customization', () => {\n    it('should accept custom height', () => {\n      const customHeight = 500;\n      render(<SkillGapRadarChart data={mockSkillData} height={customHeight} />);\n      \n      const container = screen.getByTestId('responsive-container').parentElement;\n      expect(container).toHaveStyle(`height: ${customHeight}px`);\n    });\n\n    it('should accept custom maxValue', () => {\n      // This would be tested by checking if the PolarRadiusAxis receives the correct domain\n      // Since we're mocking recharts, we can't test the actual domain prop\n      // In a real implementation, this would be verified through integration tests\n      render(<SkillGapRadarChart data={mockSkillData} maxValue={5} />);\n      \n      expect(screen.getByTestId('polar-radius-axis')).toBeInTheDocument();\n    });\n  });\n});\n"], "version": 3}