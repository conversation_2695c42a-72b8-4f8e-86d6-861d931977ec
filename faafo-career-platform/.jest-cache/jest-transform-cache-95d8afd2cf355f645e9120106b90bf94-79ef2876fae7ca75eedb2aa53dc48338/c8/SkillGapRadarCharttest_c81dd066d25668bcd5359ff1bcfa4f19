884ca7c9baf5885260bf45c429cfc7bf
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
// Mock recharts components
jest.mock('recharts', function () { return ({
    RadarChart: function (_a) {
        var children = _a.children;
        return (0, jsx_runtime_1.jsx)("div", { "data-testid": "radar-chart", children: children });
    },
    PolarGrid: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "polar-grid" }); },
    PolarAngleAxis: function (_a) {
        var dataKey = _a.dataKey;
        return (0, jsx_runtime_1.jsx)("div", { "data-testid": "polar-angle-axis", "data-key": dataKey });
    },
    PolarRadiusAxis: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "polar-radius-axis" }); },
    Radar: function (_a) {
        var name = _a.name, dataKey = _a.dataKey, stroke = _a.stroke;
        return ((0, jsx_runtime_1.jsx)("div", { "data-testid": "radar", "data-name": name, "data-key": dataKey, "data-stroke": stroke }));
    },
    ResponsiveContainer: function (_a) {
        var children = _a.children;
        return (0, jsx_runtime_1.jsx)("div", { "data-testid": "responsive-container", children: children });
    },
    Legend: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "legend" }); },
    Tooltip: function () { return (0, jsx_runtime_1.jsx)("div", { "data-testid": "tooltip" }); },
}); });
var react_1 = __importDefault(require("react"));
var react_2 = require("@testing-library/react");
require("@testing-library/jest-dom");
var SkillGapRadarChart_1 = __importDefault(require("@/components/skills/visualizations/SkillGapRadarChart"));
var mockSkillData = [
    {
        skill: 'JavaScript',
        current: 7,
        target: 9,
        market: 8,
        category: 'Programming',
    },
    {
        skill: 'React',
        current: 6,
        target: 8,
        market: 7,
        category: 'Frontend',
    },
    {
        skill: 'Node.js',
        current: 5,
        target: 7,
        market: 6,
        category: 'Backend',
    },
];
describe('SkillGapRadarChart', function () {
    describe('Component Rendering', function () {
        it('should render with default props', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: mockSkillData }));
            expect(react_2.screen.getByText('Skill Gap Analysis')).toBeInTheDocument();
            expect(react_2.screen.getByText('Compare your current skills with target and market requirements')).toBeInTheDocument();
            expect(react_2.screen.getByTestId('radar-chart')).toBeInTheDocument();
        });
        it('should render with custom title and description', function () {
            var customTitle = 'Custom Skill Analysis';
            var customDescription = 'Custom description for testing';
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: mockSkillData, title: customTitle, description: customDescription }));
            expect(react_2.screen.getByText(customTitle)).toBeInTheDocument();
            expect(react_2.screen.getByText(customDescription)).toBeInTheDocument();
        });
        it('should render without description when not provided', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: mockSkillData, title: "Test Title", description: "" }));
            expect(react_2.screen.getByText('Test Title')).toBeInTheDocument();
            expect(react_2.screen.queryByText('Compare your current skills')).not.toBeInTheDocument();
        });
    });
    describe('Chart Components', function () {
        it('should render all required chart components', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: mockSkillData }));
            expect(react_2.screen.getByTestId('responsive-container')).toBeInTheDocument();
            expect(react_2.screen.getByTestId('radar-chart')).toBeInTheDocument();
            expect(react_2.screen.getByTestId('polar-grid')).toBeInTheDocument();
            expect(react_2.screen.getByTestId('polar-angle-axis')).toBeInTheDocument();
            expect(react_2.screen.getByTestId('polar-radius-axis')).toBeInTheDocument();
            expect(react_2.screen.getByTestId('tooltip')).toBeInTheDocument();
        });
        it('should render three radar lines for current, target, and market', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: mockSkillData }));
            var radarElements = react_2.screen.getAllByTestId('radar');
            expect(radarElements).toHaveLength(3);
            expect(radarElements[0]).toHaveAttribute('data-name', 'Current Level');
            expect(radarElements[0]).toHaveAttribute('data-key', 'current');
            expect(radarElements[0]).toHaveAttribute('data-stroke', '#3b82f6');
            expect(radarElements[1]).toHaveAttribute('data-name', 'Target Level');
            expect(radarElements[1]).toHaveAttribute('data-key', 'target');
            expect(radarElements[1]).toHaveAttribute('data-stroke', '#10b981');
            expect(radarElements[2]).toHaveAttribute('data-name', 'Market Average');
            expect(radarElements[2]).toHaveAttribute('data-key', 'market');
            expect(radarElements[2]).toHaveAttribute('data-stroke', '#f59e0b');
        });
        it('should render legend when showLegend is true', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: mockSkillData, showLegend: true }));
            expect(react_2.screen.getByTestId('legend')).toBeInTheDocument();
        });
        it('should not render legend when showLegend is false', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: mockSkillData, showLegend: false }));
            expect(react_2.screen.queryByTestId('legend')).not.toBeInTheDocument();
        });
    });
    describe('Summary Statistics', function () {
        it('should calculate and display average current level correctly', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: mockSkillData }));
            // Average current: (7 + 6 + 5) / 3 = 6.0
            expect(react_2.screen.getByText('6.0/10')).toBeInTheDocument();
            expect(react_2.screen.getByText('Average Current')).toBeInTheDocument();
        });
        it('should calculate and display average target level correctly', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: mockSkillData }));
            // Average target: (9 + 8 + 7) / 3 = 8.0
            expect(react_2.screen.getByText('8.0/10')).toBeInTheDocument();
            expect(react_2.screen.getByText('Average Target')).toBeInTheDocument();
        });
        it('should calculate and display gap to close correctly', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: mockSkillData }));
            // Gap to close: ((9-7) + (8-6) + (7-5)) / 3 = (2 + 2 + 2) / 3 = 2.0
            expect(react_2.screen.getByText('2.0')).toBeInTheDocument();
            expect(react_2.screen.getByText('Gap to Close')).toBeInTheDocument();
        });
        it('should handle empty data gracefully', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: [] }));
            expect(react_2.screen.getAllByText('0.0/10')).toHaveLength(2); // Current and Target averages
            expect(react_2.screen.getByText('0.0')).toBeInTheDocument(); // Gap to close
        });
        it('should handle single skill data correctly', function () {
            var singleSkill = [mockSkillData[0]];
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: singleSkill }));
            expect(react_2.screen.getByText('7.0/10')).toBeInTheDocument(); // Current
            expect(react_2.screen.getByText('9.0/10')).toBeInTheDocument(); // Target
            expect(react_2.screen.getByText('2.0')).toBeInTheDocument(); // Gap
        });
    });
    describe('Data Handling', function () {
        it('should handle skills with zero values', function () {
            var dataWithZeros = [
                {
                    skill: 'New Skill',
                    current: 0,
                    target: 5,
                    market: 3,
                    category: 'Learning',
                },
            ];
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: dataWithZeros }));
            expect(react_2.screen.getByText('0.0/10')).toBeInTheDocument();
            expect(react_2.screen.getByText('5.0/10')).toBeInTheDocument();
            expect(react_2.screen.getByText('5.0')).toBeInTheDocument(); // Gap: 5-0 = 5
        });
        it('should handle skills where current exceeds target', function () {
            var dataWithExcess = [
                {
                    skill: 'Expert Skill',
                    current: 9,
                    target: 7,
                    market: 8,
                    category: 'Advanced',
                },
            ];
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: dataWithExcess }));
            expect(react_2.screen.getByText('9.0/10')).toBeInTheDocument();
            expect(react_2.screen.getByText('7.0/10')).toBeInTheDocument();
            expect(react_2.screen.getByText('0.0')).toBeInTheDocument(); // Gap: max(0, 7-9) = 0
        });
        it('should set correct dataKey for polar angle axis', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: mockSkillData }));
            var polarAngleAxis = react_2.screen.getByTestId('polar-angle-axis');
            expect(polarAngleAxis).toHaveAttribute('data-key', 'skill');
        });
    });
    describe('Accessibility', function () {
        it('should have proper heading structure', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: mockSkillData }));
            var title = react_2.screen.getByText('Skill Gap Analysis');
            expect(title.tagName).toBe('DIV'); // CardTitle renders as div with proper styling
        });
        it('should have descriptive text for screen readers', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: mockSkillData }));
            expect(react_2.screen.getByText('Compare your current skills with target and market requirements')).toBeInTheDocument();
        });
    });
    describe('Customization', function () {
        it('should accept custom height', function () {
            var customHeight = 500;
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: mockSkillData, height: customHeight }));
            var container = react_2.screen.getByTestId('responsive-container').parentElement;
            expect(container).toHaveStyle("height: ".concat(customHeight, "px"));
        });
        it('should accept custom maxValue', function () {
            // This would be tested by checking if the PolarRadiusAxis receives the correct domain
            // Since we're mocking recharts, we can't test the actual domain prop
            // In a real implementation, this would be verified through integration tests
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillGapRadarChart_1.default, { data: mockSkillData, maxValue: 5 }));
            expect(react_2.screen.getByTestId('polar-radius-axis')).toBeInTheDocument();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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