d35e92487929fc244a79785a80c4510f
"use strict";

/* istanbul ignore next */
function cov_2410vbosi2() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/resume-builder/[id]/route.ts";
  var hash = "216070d388250565c3639160a8fb299a10005312";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/resume-builder/[id]/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 22
        },
        end: {
          line: 51,
          column: 1
        }
      },
      "79": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 50,
          column: 62
        }
      },
      "80": {
        start: {
          line: 52,
          column: 0
        },
        end: {
          line: 52,
          column: 62
        }
      },
      "81": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 52
        }
      },
      "82": {
        start: {
          line: 54,
          column: 15
        },
        end: {
          line: 54,
          column: 37
        }
      },
      "83": {
        start: {
          line: 55,
          column: 13
        },
        end: {
          line: 55,
          column: 38
        }
      },
      "84": {
        start: {
          line: 56,
          column: 13
        },
        end: {
          line: 56,
          column: 34
        }
      },
      "85": {
        start: {
          line: 57,
          column: 15
        },
        end: {
          line: 57,
          column: 55
        }
      },
      "86": {
        start: {
          line: 58,
          column: 15
        },
        end: {
          line: 58,
          column: 38
        }
      },
      "87": {
        start: {
          line: 59,
          column: 13
        },
        end: {
          line: 59,
          column: 34
        }
      },
      "88": {
        start: {
          line: 60,
          column: 18
        },
        end: {
          line: 60,
          column: 44
        }
      },
      "89": {
        start: {
          line: 61,
          column: 34
        },
        end: {
          line: 61,
          column: 76
        }
      },
      "90": {
        start: {
          line: 62,
          column: 12
        },
        end: {
          line: 62,
          column: 26
        }
      },
      "91": {
        start: {
          line: 63,
          column: 28
        },
        end: {
          line: 63,
          column: 64
        }
      },
      "92": {
        start: {
          line: 65,
          column: 25
        },
        end: {
          line: 95,
          column: 2
        }
      },
      "93": {
        start: {
          line: 96,
          column: 23
        },
        end: {
          line: 103,
          column: 2
        }
      },
      "94": {
        start: {
          line: 104,
          column: 22
        },
        end: {
          line: 112,
          column: 2
        }
      },
      "95": {
        start: {
          line: 113,
          column: 18
        },
        end: {
          line: 117,
          column: 2
        }
      },
      "96": {
        start: {
          line: 118,
          column: 25
        },
        end: {
          line: 128,
          column: 2
        }
      },
      "97": {
        start: {
          line: 130,
          column: 0
        },
        end: {
          line: 204,
          column: 7
        }
      },
      "98": {
        start: {
          line: 130,
          column: 99
        },
        end: {
          line: 204,
          column: 3
        }
      },
      "99": {
        start: {
          line: 131,
          column: 17
        },
        end: {
          line: 131,
          column: 26
        }
      },
      "100": {
        start: {
          line: 132,
          column: 4
        },
        end: {
          line: 203,
          column: 7
        }
      },
      "101": {
        start: {
          line: 133,
          column: 8
        },
        end: {
          line: 202,
          column: 20
        }
      },
      "102": {
        start: {
          line: 134,
          column: 26
        },
        end: {
          line: 202,
          column: 15
        }
      },
      "103": {
        start: {
          line: 137,
          column: 16
        },
        end: {
          line: 201,
          column: 19
        }
      },
      "104": {
        start: {
          line: 138,
          column: 20
        },
        end: {
          line: 200,
          column: 21
        }
      },
      "105": {
        start: {
          line: 140,
          column: 28
        },
        end: {
          line: 140,
          column: 51
        }
      },
      "106": {
        start: {
          line: 141,
          column: 28
        },
        end: {
          line: 141,
          column: 99
        }
      },
      "107": {
        start: {
          line: 143,
          column: 28
        },
        end: {
          line: 143,
          column: 48
        }
      },
      "108": {
        start: {
          line: 144,
          column: 28
        },
        end: {
          line: 144,
          column: 57
        }
      },
      "109": {
        start: {
          line: 146,
          column: 28
        },
        end: {
          line: 146,
          column: 48
        }
      },
      "110": {
        start: {
          line: 147,
          column: 28
        },
        end: {
          line: 155,
          column: 29
        }
      },
      "111": {
        start: {
          line: 148,
          column: 32
        },
        end: {
          line: 151,
          column: 35
        }
      },
      "112": {
        start: {
          line: 152,
          column: 32
        },
        end: {
          line: 152,
          column: 71
        }
      },
      "113": {
        start: {
          line: 153,
          column: 32
        },
        end: {
          line: 153,
          column: 55
        }
      },
      "114": {
        start: {
          line: 154,
          column: 32
        },
        end: {
          line: 154,
          column: 44
        }
      },
      "115": {
        start: {
          line: 156,
          column: 28
        },
        end: {
          line: 160,
          column: 31
        }
      },
      "116": {
        start: {
          line: 161,
          column: 28
        },
        end: {
          line: 161,
          column: 53
        }
      },
      "117": {
        start: {
          line: 162,
          column: 28
        },
        end: {
          line: 165,
          column: 36
        }
      },
      "118": {
        start: {
          line: 167,
          column: 28
        },
        end: {
          line: 167,
          column: 45
        }
      },
      "119": {
        start: {
          line: 168,
          column: 28
        },
        end: {
          line: 172,
          column: 29
        }
      },
      "120": {
        start: {
          line: 169,
          column: 32
        },
        end: {
          line: 169,
          column: 68
        }
      },
      "121": {
        start: {
          line: 170,
          column: 32
        },
        end: {
          line: 170,
          column: 55
        }
      },
      "122": {
        start: {
          line: 171,
          column: 32
        },
        end: {
          line: 171,
          column: 44
        }
      },
      "123": {
        start: {
          line: 173,
          column: 28
        },
        end: {
          line: 179,
          column: 36
        }
      },
      "124": {
        start: {
          line: 181,
          column: 28
        },
        end: {
          line: 181,
          column: 47
        }
      },
      "125": {
        start: {
          line: 182,
          column: 28
        },
        end: {
          line: 182,
          column: 66
        }
      },
      "126": {
        start: {
          line: 183,
          column: 28
        },
        end: {
          line: 185,
          column: 31
        }
      },
      "127": {
        start: {
          line: 186,
          column: 28
        },
        end: {
          line: 190,
          column: 29
        }
      },
      "128": {
        start: {
          line: 187,
          column: 32
        },
        end: {
          line: 187,
          column: 70
        }
      },
      "129": {
        start: {
          line: 188,
          column: 32
        },
        end: {
          line: 188,
          column: 55
        }
      },
      "130": {
        start: {
          line: 189,
          column: 32
        },
        end: {
          line: 189,
          column: 44
        }
      },
      "131": {
        start: {
          line: 191,
          column: 28
        },
        end: {
          line: 191,
          column: 67
        }
      },
      "132": {
        start: {
          line: 192,
          column: 28
        },
        end: {
          line: 195,
          column: 31
        }
      },
      "133": {
        start: {
          line: 196,
          column: 28
        },
        end: {
          line: 199,
          column: 36
        }
      },
      "134": {
        start: {
          line: 206,
          column: 0
        },
        end: {
          line: 314,
          column: 7
        }
      },
      "135": {
        start: {
          line: 206,
          column: 99
        },
        end: {
          line: 314,
          column: 3
        }
      },
      "136": {
        start: {
          line: 207,
          column: 17
        },
        end: {
          line: 207,
          column: 26
        }
      },
      "137": {
        start: {
          line: 208,
          column: 4
        },
        end: {
          line: 313,
          column: 7
        }
      },
      "138": {
        start: {
          line: 209,
          column: 8
        },
        end: {
          line: 312,
          column: 20
        }
      },
      "139": {
        start: {
          line: 209,
          column: 84
        },
        end: {
          line: 312,
          column: 15
        }
      },
      "140": {
        start: {
          line: 210,
          column: 16
        },
        end: {
          line: 311,
          column: 19
        }
      },
      "141": {
        start: {
          line: 211,
          column: 20
        },
        end: {
          line: 310,
          column: 32
        }
      },
      "142": {
        start: {
          line: 212,
          column: 38
        },
        end: {
          line: 310,
          column: 27
        }
      },
      "143": {
        start: {
          line: 215,
          column: 28
        },
        end: {
          line: 309,
          column: 31
        }
      },
      "144": {
        start: {
          line: 216,
          column: 32
        },
        end: {
          line: 308,
          column: 33
        }
      },
      "145": {
        start: {
          line: 218,
          column: 40
        },
        end: {
          line: 218,
          column: 63
        }
      },
      "146": {
        start: {
          line: 219,
          column: 40
        },
        end: {
          line: 219,
          column: 111
        }
      },
      "147": {
        start: {
          line: 221,
          column: 40
        },
        end: {
          line: 221,
          column: 60
        }
      },
      "148": {
        start: {
          line: 222,
          column: 40
        },
        end: {
          line: 222,
          column: 69
        }
      },
      "149": {
        start: {
          line: 224,
          column: 40
        },
        end: {
          line: 224,
          column: 60
        }
      },
      "150": {
        start: {
          line: 225,
          column: 40
        },
        end: {
          line: 229,
          column: 41
        }
      },
      "151": {
        start: {
          line: 226,
          column: 44
        },
        end: {
          line: 226,
          column: 83
        }
      },
      "152": {
        start: {
          line: 227,
          column: 44
        },
        end: {
          line: 227,
          column: 67
        }
      },
      "153": {
        start: {
          line: 228,
          column: 44
        },
        end: {
          line: 228,
          column: 56
        }
      },
      "154": {
        start: {
          line: 230,
          column: 40
        },
        end: {
          line: 233,
          column: 48
        }
      },
      "155": {
        start: {
          line: 235,
          column: 40
        },
        end: {
          line: 235,
          column: 57
        }
      },
      "156": {
        start: {
          line: 236,
          column: 40
        },
        end: {
          line: 240,
          column: 41
        }
      },
      "157": {
        start: {
          line: 237,
          column: 44
        },
        end: {
          line: 237,
          column: 80
        }
      },
      "158": {
        start: {
          line: 238,
          column: 44
        },
        end: {
          line: 238,
          column: 67
        }
      },
      "159": {
        start: {
          line: 239,
          column: 44
        },
        end: {
          line: 239,
          column: 56
        }
      },
      "160": {
        start: {
          line: 241,
          column: 40
        },
        end: {
          line: 247,
          column: 48
        }
      },
      "161": {
        start: {
          line: 249,
          column: 40
        },
        end: {
          line: 249,
          column: 67
        }
      },
      "162": {
        start: {
          line: 250,
          column: 40
        },
        end: {
          line: 254,
          column: 41
        }
      },
      "163": {
        start: {
          line: 251,
          column: 44
        },
        end: {
          line: 251,
          column: 82
        }
      },
      "164": {
        start: {
          line: 252,
          column: 44
        },
        end: {
          line: 252,
          column: 67
        }
      },
      "165": {
        start: {
          line: 253,
          column: 44
        },
        end: {
          line: 253,
          column: 56
        }
      },
      "166": {
        start: {
          line: 255,
          column: 40
        },
        end: {
          line: 255,
          column: 77
        }
      },
      "167": {
        start: {
          line: 257,
          column: 40
        },
        end: {
          line: 257,
          column: 57
        }
      },
      "168": {
        start: {
          line: 258,
          column: 40
        },
        end: {
          line: 258,
          column: 134
        }
      },
      "169": {
        start: {
          line: 259,
          column: 40
        },
        end: {
          line: 259,
          column: 122
        }
      },
      "170": {
        start: {
          line: 260,
          column: 40
        },
        end: {
          line: 260,
          column: 88
        }
      },
      "171": {
        start: {
          line: 260,
          column: 64
        },
        end: {
          line: 260,
          column: 88
        }
      },
      "172": {
        start: {
          line: 261,
          column: 40
        },
        end: {
          line: 261,
          column: 111
        }
      },
      "173": {
        start: {
          line: 263,
          column: 40
        },
        end: {
          line: 263,
          column: 71
        }
      },
      "174": {
        start: {
          line: 264,
          column: 40
        },
        end: {
          line: 269,
          column: 41
        }
      },
      "175": {
        start: {
          line: 265,
          column: 44
        },
        end: {
          line: 265,
          column: 104
        }
      },
      "176": {
        start: {
          line: 266,
          column: 44
        },
        end: {
          line: 266,
          column: 67
        }
      },
      "177": {
        start: {
          line: 267,
          column: 44
        },
        end: {
          line: 267,
          column: 86
        }
      },
      "178": {
        start: {
          line: 268,
          column: 44
        },
        end: {
          line: 268,
          column: 56
        }
      },
      "179": {
        start: {
          line: 270,
          column: 40
        },
        end: {
          line: 270,
          column: 93
        }
      },
      "180": {
        start: {
          line: 271,
          column: 40
        },
        end: {
          line: 271,
          column: 53
        }
      },
      "181": {
        start: {
          line: 272,
          column: 44
        },
        end: {
          line: 272,
          column: 96
        }
      },
      "182": {
        start: {
          line: 274,
          column: 40
        },
        end: {
          line: 274,
          column: 65
        }
      },
      "183": {
        start: {
          line: 275,
          column: 40
        },
        end: {
          line: 280,
          column: 41
        }
      },
      "184": {
        start: {
          line: 276,
          column: 44
        },
        end: {
          line: 276,
          column: 95
        }
      },
      "185": {
        start: {
          line: 277,
          column: 44
        },
        end: {
          line: 277,
          column: 67
        }
      },
      "186": {
        start: {
          line: 278,
          column: 44
        },
        end: {
          line: 278,
          column: 80
        }
      },
      "187": {
        start: {
          line: 279,
          column: 44
        },
        end: {
          line: 279,
          column: 56
        }
      },
      "188": {
        start: {
          line: 281,
          column: 40
        },
        end: {
          line: 281,
          column: 83
        }
      },
      "189": {
        start: {
          line: 282,
          column: 40
        },
        end: {
          line: 282,
          column: 96
        }
      },
      "190": {
        start: {
          line: 283,
          column: 40
        },
        end: {
          line: 287,
          column: 43
        }
      },
      "191": {
        start: {
          line: 288,
          column: 40
        },
        end: {
          line: 288,
          column: 65
        }
      },
      "192": {
        start: {
          line: 289,
          column: 40
        },
        end: {
          line: 292,
          column: 48
        }
      },
      "193": {
        start: {
          line: 294,
          column: 40
        },
        end: {
          line: 294,
          column: 66
        }
      },
      "194": {
        start: {
          line: 295,
          column: 40
        },
        end: {
          line: 295,
          column: 78
        }
      },
      "195": {
        start: {
          line: 296,
          column: 40
        },
        end: {
          line: 298,
          column: 43
        }
      },
      "196": {
        start: {
          line: 299,
          column: 40
        },
        end: {
          line: 299,
          column: 79
        }
      },
      "197": {
        start: {
          line: 300,
          column: 40
        },
        end: {
          line: 303,
          column: 43
        }
      },
      "198": {
        start: {
          line: 304,
          column: 40
        },
        end: {
          line: 307,
          column: 48
        }
      },
      "199": {
        start: {
          line: 316,
          column: 0
        },
        end: {
          line: 402,
          column: 7
        }
      },
      "200": {
        start: {
          line: 316,
          column: 102
        },
        end: {
          line: 402,
          column: 3
        }
      },
      "201": {
        start: {
          line: 317,
          column: 17
        },
        end: {
          line: 317,
          column: 26
        }
      },
      "202": {
        start: {
          line: 318,
          column: 4
        },
        end: {
          line: 401,
          column: 7
        }
      },
      "203": {
        start: {
          line: 319,
          column: 8
        },
        end: {
          line: 400,
          column: 20
        }
      },
      "204": {
        start: {
          line: 319,
          column: 84
        },
        end: {
          line: 400,
          column: 15
        }
      },
      "205": {
        start: {
          line: 320,
          column: 16
        },
        end: {
          line: 399,
          column: 19
        }
      },
      "206": {
        start: {
          line: 321,
          column: 20
        },
        end: {
          line: 398,
          column: 32
        }
      },
      "207": {
        start: {
          line: 322,
          column: 38
        },
        end: {
          line: 398,
          column: 27
        }
      },
      "208": {
        start: {
          line: 325,
          column: 28
        },
        end: {
          line: 397,
          column: 31
        }
      },
      "209": {
        start: {
          line: 326,
          column: 32
        },
        end: {
          line: 396,
          column: 33
        }
      },
      "210": {
        start: {
          line: 328,
          column: 40
        },
        end: {
          line: 328,
          column: 63
        }
      },
      "211": {
        start: {
          line: 329,
          column: 40
        },
        end: {
          line: 329,
          column: 111
        }
      },
      "212": {
        start: {
          line: 331,
          column: 40
        },
        end: {
          line: 331,
          column: 60
        }
      },
      "213": {
        start: {
          line: 332,
          column: 40
        },
        end: {
          line: 332,
          column: 69
        }
      },
      "214": {
        start: {
          line: 334,
          column: 40
        },
        end: {
          line: 334,
          column: 60
        }
      },
      "215": {
        start: {
          line: 335,
          column: 40
        },
        end: {
          line: 339,
          column: 41
        }
      },
      "216": {
        start: {
          line: 336,
          column: 44
        },
        end: {
          line: 336,
          column: 83
        }
      },
      "217": {
        start: {
          line: 337,
          column: 44
        },
        end: {
          line: 337,
          column: 67
        }
      },
      "218": {
        start: {
          line: 338,
          column: 44
        },
        end: {
          line: 338,
          column: 56
        }
      },
      "219": {
        start: {
          line: 340,
          column: 40
        },
        end: {
          line: 343,
          column: 48
        }
      },
      "220": {
        start: {
          line: 345,
          column: 40
        },
        end: {
          line: 345,
          column: 57
        }
      },
      "221": {
        start: {
          line: 346,
          column: 40
        },
        end: {
          line: 350,
          column: 41
        }
      },
      "222": {
        start: {
          line: 347,
          column: 44
        },
        end: {
          line: 347,
          column: 80
        }
      },
      "223": {
        start: {
          line: 348,
          column: 44
        },
        end: {
          line: 348,
          column: 67
        }
      },
      "224": {
        start: {
          line: 349,
          column: 44
        },
        end: {
          line: 349,
          column: 56
        }
      },
      "225": {
        start: {
          line: 351,
          column: 40
        },
        end: {
          line: 357,
          column: 48
        }
      },
      "226": {
        start: {
          line: 359,
          column: 40
        },
        end: {
          line: 359,
          column: 67
        }
      },
      "227": {
        start: {
          line: 360,
          column: 40
        },
        end: {
          line: 364,
          column: 41
        }
      },
      "228": {
        start: {
          line: 361,
          column: 44
        },
        end: {
          line: 361,
          column: 82
        }
      },
      "229": {
        start: {
          line: 362,
          column: 44
        },
        end: {
          line: 362,
          column: 67
        }
      },
      "230": {
        start: {
          line: 363,
          column: 44
        },
        end: {
          line: 363,
          column: 56
        }
      },
      "231": {
        start: {
          line: 365,
          column: 40
        },
        end: {
          line: 369,
          column: 43
        }
      },
      "232": {
        start: {
          line: 370,
          column: 40
        },
        end: {
          line: 370,
          column: 65
        }
      },
      "233": {
        start: {
          line: 372,
          column: 40
        },
        end: {
          line: 378,
          column: 48
        }
      },
      "234": {
        start: {
          line: 381,
          column: 40
        },
        end: {
          line: 381,
          column: 50
        }
      },
      "235": {
        start: {
          line: 382,
          column: 40
        },
        end: {
          line: 382,
          column: 78
        }
      },
      "236": {
        start: {
          line: 383,
          column: 40
        },
        end: {
          line: 386,
          column: 43
        }
      },
      "237": {
        start: {
          line: 387,
          column: 40
        },
        end: {
          line: 387,
          column: 79
        }
      },
      "238": {
        start: {
          line: 388,
          column: 40
        },
        end: {
          line: 391,
          column: 43
        }
      },
      "239": {
        start: {
          line: 392,
          column: 40
        },
        end: {
          line: 395,
          column: 48
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 49,
            column: 57
          }
        },
        loc: {
          start: {
            line: 49,
            column: 71
          },
          end: {
            line: 51,
            column: 1
          }
        },
        line: 49
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 130,
            column: 72
          },
          end: {
            line: 130,
            column: 73
          }
        },
        loc: {
          start: {
            line: 130,
            column: 97
          },
          end: {
            line: 204,
            column: 5
          }
        },
        line: 130
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 130,
            column: 149
          },
          end: {
            line: 130,
            column: 150
          }
        },
        loc: {
          start: {
            line: 130,
            column: 172
          },
          end: {
            line: 204,
            column: 1
          }
        },
        line: 130
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 132,
            column: 29
          },
          end: {
            line: 132,
            column: 30
          }
        },
        loc: {
          start: {
            line: 132,
            column: 43
          },
          end: {
            line: 203,
            column: 5
          }
        },
        line: 132
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 134,
            column: 12
          },
          end: {
            line: 134,
            column: 13
          }
        },
        loc: {
          start: {
            line: 134,
            column: 24
          },
          end: {
            line: 202,
            column: 17
          }
        },
        line: 134
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 134,
            column: 67
          },
          end: {
            line: 134,
            column: 68
          }
        },
        loc: {
          start: {
            line: 134,
            column: 79
          },
          end: {
            line: 202,
            column: 13
          }
        },
        line: 134
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 137,
            column: 41
          },
          end: {
            line: 137,
            column: 42
          }
        },
        loc: {
          start: {
            line: 137,
            column: 55
          },
          end: {
            line: 201,
            column: 17
          }
        },
        line: 137
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 206,
            column: 72
          },
          end: {
            line: 206,
            column: 73
          }
        },
        loc: {
          start: {
            line: 206,
            column: 97
          },
          end: {
            line: 314,
            column: 5
          }
        },
        line: 206
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 206,
            column: 149
          },
          end: {
            line: 206,
            column: 150
          }
        },
        loc: {
          start: {
            line: 206,
            column: 172
          },
          end: {
            line: 314,
            column: 1
          }
        },
        line: 206
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 208,
            column: 29
          },
          end: {
            line: 208,
            column: 30
          }
        },
        loc: {
          start: {
            line: 208,
            column: 43
          },
          end: {
            line: 313,
            column: 5
          }
        },
        line: 208
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 209,
            column: 70
          },
          end: {
            line: 209,
            column: 71
          }
        },
        loc: {
          start: {
            line: 209,
            column: 82
          },
          end: {
            line: 312,
            column: 17
          }
        },
        line: 209
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 209,
            column: 125
          },
          end: {
            line: 209,
            column: 126
          }
        },
        loc: {
          start: {
            line: 209,
            column: 137
          },
          end: {
            line: 312,
            column: 13
          }
        },
        line: 209
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 210,
            column: 41
          },
          end: {
            line: 210,
            column: 42
          }
        },
        loc: {
          start: {
            line: 210,
            column: 55
          },
          end: {
            line: 311,
            column: 17
          }
        },
        line: 210
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 212,
            column: 24
          },
          end: {
            line: 212,
            column: 25
          }
        },
        loc: {
          start: {
            line: 212,
            column: 36
          },
          end: {
            line: 310,
            column: 29
          }
        },
        line: 212
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 212,
            column: 79
          },
          end: {
            line: 212,
            column: 80
          }
        },
        loc: {
          start: {
            line: 212,
            column: 91
          },
          end: {
            line: 310,
            column: 25
          }
        },
        line: 212
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 215,
            column: 53
          },
          end: {
            line: 215,
            column: 54
          }
        },
        loc: {
          start: {
            line: 215,
            column: 67
          },
          end: {
            line: 309,
            column: 29
          }
        },
        line: 215
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 316,
            column: 75
          },
          end: {
            line: 316,
            column: 76
          }
        },
        loc: {
          start: {
            line: 316,
            column: 100
          },
          end: {
            line: 402,
            column: 5
          }
        },
        line: 316
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 316,
            column: 152
          },
          end: {
            line: 316,
            column: 153
          }
        },
        loc: {
          start: {
            line: 316,
            column: 175
          },
          end: {
            line: 402,
            column: 1
          }
        },
        line: 316
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 318,
            column: 29
          },
          end: {
            line: 318,
            column: 30
          }
        },
        loc: {
          start: {
            line: 318,
            column: 43
          },
          end: {
            line: 401,
            column: 5
          }
        },
        line: 318
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 319,
            column: 70
          },
          end: {
            line: 319,
            column: 71
          }
        },
        loc: {
          start: {
            line: 319,
            column: 82
          },
          end: {
            line: 400,
            column: 17
          }
        },
        line: 319
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 319,
            column: 125
          },
          end: {
            line: 319,
            column: 126
          }
        },
        loc: {
          start: {
            line: 319,
            column: 137
          },
          end: {
            line: 400,
            column: 13
          }
        },
        line: 319
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 320,
            column: 41
          },
          end: {
            line: 320,
            column: 42
          }
        },
        loc: {
          start: {
            line: 320,
            column: 55
          },
          end: {
            line: 399,
            column: 17
          }
        },
        line: 320
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 322,
            column: 24
          },
          end: {
            line: 322,
            column: 25
          }
        },
        loc: {
          start: {
            line: 322,
            column: 36
          },
          end: {
            line: 398,
            column: 29
          }
        },
        line: 322
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 322,
            column: 79
          },
          end: {
            line: 322,
            column: 80
          }
        },
        loc: {
          start: {
            line: 322,
            column: 91
          },
          end: {
            line: 398,
            column: 25
          }
        },
        line: 322
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 325,
            column: 53
          },
          end: {
            line: 325,
            column: 54
          }
        },
        loc: {
          start: {
            line: 325,
            column: 67
          },
          end: {
            line: 397,
            column: 29
          }
        },
        line: 325
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 49,
            column: 22
          },
          end: {
            line: 51,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 23
          },
          end: {
            line: 49,
            column: 27
          }
        }, {
          start: {
            line: 49,
            column: 31
          },
          end: {
            line: 49,
            column: 51
          }
        }, {
          start: {
            line: 49,
            column: 56
          },
          end: {
            line: 51,
            column: 1
          }
        }],
        line: 49
      },
      "36": {
        loc: {
          start: {
            line: 50,
            column: 11
          },
          end: {
            line: 50,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 50,
            column: 37
          },
          end: {
            line: 50,
            column: 40
          }
        }, {
          start: {
            line: 50,
            column: 43
          },
          end: {
            line: 50,
            column: 61
          }
        }],
        line: 50
      },
      "37": {
        loc: {
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 50,
            column: 15
          }
        }, {
          start: {
            line: 50,
            column: 19
          },
          end: {
            line: 50,
            column: 33
          }
        }],
        line: 50
      },
      "38": {
        loc: {
          start: {
            line: 138,
            column: 20
          },
          end: {
            line: 200,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 139,
            column: 24
          },
          end: {
            line: 141,
            column: 99
          }
        }, {
          start: {
            line: 142,
            column: 24
          },
          end: {
            line: 144,
            column: 57
          }
        }, {
          start: {
            line: 145,
            column: 24
          },
          end: {
            line: 165,
            column: 36
          }
        }, {
          start: {
            line: 166,
            column: 24
          },
          end: {
            line: 179,
            column: 36
          }
        }, {
          start: {
            line: 180,
            column: 24
          },
          end: {
            line: 199,
            column: 36
          }
        }],
        line: 138
      },
      "39": {
        loc: {
          start: {
            line: 147,
            column: 28
          },
          end: {
            line: 155,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 147,
            column: 28
          },
          end: {
            line: 155,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 147
      },
      "40": {
        loc: {
          start: {
            line: 147,
            column: 34
          },
          end: {
            line: 147,
            column: 149
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 147,
            column: 132
          },
          end: {
            line: 147,
            column: 138
          }
        }, {
          start: {
            line: 147,
            column: 141
          },
          end: {
            line: 147,
            column: 149
          }
        }],
        line: 147
      },
      "41": {
        loc: {
          start: {
            line: 147,
            column: 34
          },
          end: {
            line: 147,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 147,
            column: 34
          },
          end: {
            line: 147,
            column: 112
          }
        }, {
          start: {
            line: 147,
            column: 116
          },
          end: {
            line: 147,
            column: 129
          }
        }],
        line: 147
      },
      "42": {
        loc: {
          start: {
            line: 147,
            column: 40
          },
          end: {
            line: 147,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 147,
            column: 81
          },
          end: {
            line: 147,
            column: 87
          }
        }, {
          start: {
            line: 147,
            column: 90
          },
          end: {
            line: 147,
            column: 102
          }
        }],
        line: 147
      },
      "43": {
        loc: {
          start: {
            line: 147,
            column: 40
          },
          end: {
            line: 147,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 147,
            column: 40
          },
          end: {
            line: 147,
            column: 56
          }
        }, {
          start: {
            line: 147,
            column: 60
          },
          end: {
            line: 147,
            column: 78
          }
        }],
        line: 147
      },
      "44": {
        loc: {
          start: {
            line: 168,
            column: 28
          },
          end: {
            line: 172,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 168,
            column: 28
          },
          end: {
            line: 172,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 168
      },
      "45": {
        loc: {
          start: {
            line: 186,
            column: 28
          },
          end: {
            line: 190,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 186,
            column: 28
          },
          end: {
            line: 190,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 186
      },
      "46": {
        loc: {
          start: {
            line: 216,
            column: 32
          },
          end: {
            line: 308,
            column: 33
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 217,
            column: 36
          },
          end: {
            line: 219,
            column: 111
          }
        }, {
          start: {
            line: 220,
            column: 36
          },
          end: {
            line: 222,
            column: 69
          }
        }, {
          start: {
            line: 223,
            column: 36
          },
          end: {
            line: 233,
            column: 48
          }
        }, {
          start: {
            line: 234,
            column: 36
          },
          end: {
            line: 247,
            column: 48
          }
        }, {
          start: {
            line: 248,
            column: 36
          },
          end: {
            line: 255,
            column: 77
          }
        }, {
          start: {
            line: 256,
            column: 36
          },
          end: {
            line: 261,
            column: 111
          }
        }, {
          start: {
            line: 262,
            column: 36
          },
          end: {
            line: 271,
            column: 53
          }
        }, {
          start: {
            line: 272,
            column: 36
          },
          end: {
            line: 272,
            column: 96
          }
        }, {
          start: {
            line: 273,
            column: 36
          },
          end: {
            line: 292,
            column: 48
          }
        }, {
          start: {
            line: 293,
            column: 36
          },
          end: {
            line: 307,
            column: 48
          }
        }],
        line: 216
      },
      "47": {
        loc: {
          start: {
            line: 225,
            column: 40
          },
          end: {
            line: 229,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 225,
            column: 40
          },
          end: {
            line: 229,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 225
      },
      "48": {
        loc: {
          start: {
            line: 225,
            column: 46
          },
          end: {
            line: 225,
            column: 161
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 225,
            column: 144
          },
          end: {
            line: 225,
            column: 150
          }
        }, {
          start: {
            line: 225,
            column: 153
          },
          end: {
            line: 225,
            column: 161
          }
        }],
        line: 225
      },
      "49": {
        loc: {
          start: {
            line: 225,
            column: 46
          },
          end: {
            line: 225,
            column: 141
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 225,
            column: 46
          },
          end: {
            line: 225,
            column: 124
          }
        }, {
          start: {
            line: 225,
            column: 128
          },
          end: {
            line: 225,
            column: 141
          }
        }],
        line: 225
      },
      "50": {
        loc: {
          start: {
            line: 225,
            column: 52
          },
          end: {
            line: 225,
            column: 114
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 225,
            column: 93
          },
          end: {
            line: 225,
            column: 99
          }
        }, {
          start: {
            line: 225,
            column: 102
          },
          end: {
            line: 225,
            column: 114
          }
        }],
        line: 225
      },
      "51": {
        loc: {
          start: {
            line: 225,
            column: 52
          },
          end: {
            line: 225,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 225,
            column: 52
          },
          end: {
            line: 225,
            column: 68
          }
        }, {
          start: {
            line: 225,
            column: 72
          },
          end: {
            line: 225,
            column: 90
          }
        }],
        line: 225
      },
      "52": {
        loc: {
          start: {
            line: 236,
            column: 40
          },
          end: {
            line: 240,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 236,
            column: 40
          },
          end: {
            line: 240,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 236
      },
      "53": {
        loc: {
          start: {
            line: 250,
            column: 40
          },
          end: {
            line: 254,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 250,
            column: 40
          },
          end: {
            line: 254,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 250
      },
      "54": {
        loc: {
          start: {
            line: 260,
            column: 40
          },
          end: {
            line: 260,
            column: 88
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 260,
            column: 40
          },
          end: {
            line: 260,
            column: 88
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 260
      },
      "55": {
        loc: {
          start: {
            line: 264,
            column: 40
          },
          end: {
            line: 269,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 264,
            column: 40
          },
          end: {
            line: 269,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 264
      },
      "56": {
        loc: {
          start: {
            line: 275,
            column: 40
          },
          end: {
            line: 280,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 275,
            column: 40
          },
          end: {
            line: 280,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 275
      },
      "57": {
        loc: {
          start: {
            line: 326,
            column: 32
          },
          end: {
            line: 396,
            column: 33
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 327,
            column: 36
          },
          end: {
            line: 329,
            column: 111
          }
        }, {
          start: {
            line: 330,
            column: 36
          },
          end: {
            line: 332,
            column: 69
          }
        }, {
          start: {
            line: 333,
            column: 36
          },
          end: {
            line: 343,
            column: 48
          }
        }, {
          start: {
            line: 344,
            column: 36
          },
          end: {
            line: 357,
            column: 48
          }
        }, {
          start: {
            line: 358,
            column: 36
          },
          end: {
            line: 378,
            column: 48
          }
        }, {
          start: {
            line: 379,
            column: 36
          },
          end: {
            line: 395,
            column: 48
          }
        }],
        line: 326
      },
      "58": {
        loc: {
          start: {
            line: 335,
            column: 40
          },
          end: {
            line: 339,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 335,
            column: 40
          },
          end: {
            line: 339,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 335
      },
      "59": {
        loc: {
          start: {
            line: 335,
            column: 46
          },
          end: {
            line: 335,
            column: 161
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 335,
            column: 144
          },
          end: {
            line: 335,
            column: 150
          }
        }, {
          start: {
            line: 335,
            column: 153
          },
          end: {
            line: 335,
            column: 161
          }
        }],
        line: 335
      },
      "60": {
        loc: {
          start: {
            line: 335,
            column: 46
          },
          end: {
            line: 335,
            column: 141
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 335,
            column: 46
          },
          end: {
            line: 335,
            column: 124
          }
        }, {
          start: {
            line: 335,
            column: 128
          },
          end: {
            line: 335,
            column: 141
          }
        }],
        line: 335
      },
      "61": {
        loc: {
          start: {
            line: 335,
            column: 52
          },
          end: {
            line: 335,
            column: 114
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 335,
            column: 93
          },
          end: {
            line: 335,
            column: 99
          }
        }, {
          start: {
            line: 335,
            column: 102
          },
          end: {
            line: 335,
            column: 114
          }
        }],
        line: 335
      },
      "62": {
        loc: {
          start: {
            line: 335,
            column: 52
          },
          end: {
            line: 335,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 335,
            column: 52
          },
          end: {
            line: 335,
            column: 68
          }
        }, {
          start: {
            line: 335,
            column: 72
          },
          end: {
            line: 335,
            column: 90
          }
        }],
        line: 335
      },
      "63": {
        loc: {
          start: {
            line: 346,
            column: 40
          },
          end: {
            line: 350,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 346,
            column: 40
          },
          end: {
            line: 350,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 346
      },
      "64": {
        loc: {
          start: {
            line: 360,
            column: 40
          },
          end: {
            line: 364,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 360,
            column: 40
          },
          end: {
            line: 364,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 360
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0, 0, 0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0, 0, 0, 0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/resume-builder/[id]/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAAkD;AAClD,mCAAyC;AACzC,wDAAkC;AAElC,uCAAmC;AAEnC,mCAAgD;AAChD,6CAAgD;AAChD,6EAAwF;AACxF,2BAAwB;AACxB,iEAAgE;AAEhE,uEAAuE;AACvE,IAAM,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IAClC,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;SAClB,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC;SAChC,GAAG,CAAC,EAAE,EAAE,4CAA4C,CAAC;SACrD,KAAK,CAAC,iBAAiB,EAAE,uEAAuE,CAAC;IACpG,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;SACjB,GAAG,CAAC,CAAC,EAAE,uBAAuB,CAAC;SAC/B,GAAG,CAAC,EAAE,EAAE,2CAA2C,CAAC;SACpD,KAAK,CAAC,iBAAiB,EAAE,sEAAsE,CAAC;IACnG,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;SACd,KAAK,CAAC,yBAAyB,CAAC;SAChC,GAAG,CAAC,GAAG,EAAE,mBAAmB,CAAC;IAChC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;SACd,GAAG,CAAC,EAAE,EAAE,0BAA0B,CAAC;SACnC,KAAK,CAAC,gCAAgC,EAAE,mCAAmC,CAAC;SAC5E,QAAQ,EAAE;SACV,EAAE,CAAC,OAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;SACjB,GAAG,CAAC,GAAG,EAAE,2CAA2C,CAAC;SACrD,QAAQ,EAAE;IACb,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;SAChB,GAAG,CAAC,kCAAkC,CAAC;SACvC,GAAG,CAAC,GAAG,EAAE,yBAAyB,CAAC;SACnC,QAAQ,EAAE;SACV,EAAE,CAAC,OAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IACpB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;SACjB,GAAG,CAAC,mCAAmC,CAAC;SACxC,GAAG,CAAC,GAAG,EAAE,0BAA0B,CAAC;SACpC,QAAQ,EAAE;SACV,EAAE,CAAC,OAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;CACrB,CAAC,CAAC;AAEH,IAAM,gBAAgB,GAAG,OAAC,CAAC,MAAM,CAAC;IAChC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC;IACtD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,sBAAsB,CAAC;IACnD,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC;IACtD,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC9B,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAClC,YAAY,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;CAC7C,CAAC,CAAC;AAEH,IAAM,eAAe,GAAG,OAAC,CAAC,MAAM,CAAC;IAC/B,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,yBAAyB,CAAC;IACzD,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,oBAAoB,CAAC;IAC/C,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAChC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC9B,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC1B,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAC9B,CAAC,CAAC;AAEH,IAAM,WAAW,GAAG,OAAC,CAAC,MAAM,CAAC;IAC3B,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC;IACjD,KAAK,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC5E,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAChC,CAAC,CAAC;AAEH,IAAM,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IAClC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC,CAAC,QAAQ,EAAE;IAC/D,YAAY,EAAE,kBAAkB,CAAC,QAAQ,EAAE;IAC3C,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC9B,UAAU,EAAE,OAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,QAAQ,EAAE;IAChD,SAAS,EAAE,OAAC,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,QAAQ,EAAE;IAC9C,MAAM,EAAE,OAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE;IACvC,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;IACtC,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC/B,QAAQ,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;CACjC,CAAC,CAAC;AAEH,iCAAiC;AACpB,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,uFAC1C,OAAoB,EACpB,EAA+C;QAA7C,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,EAAE,8BAA8B;YAC9E;;;;;;4BACQ,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;4BACb,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BACpC,qBAAM,MAAM,EAAA;;4BAAnB,EAAE,GAAK,CAAA,SAAY,CAAA,GAAjB;4BAEV,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,KAAK,CAAA,EAAE,CAAC;gCAC1B,YAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,SAAS,EAAE,KAAK,EAAE;oCACjD,SAAS,EAAE,oBAAoB;oCAC/B,MAAM,EAAE,YAAY;iCACrB,CAAC,CAAC;gCACG,KAAK,GAAG,IAAI,KAAK,CAAC,mBAAmB,CAAQ,CAAC;gCACpD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAED,YAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE;gCAC1B,SAAS,EAAE,oBAAoB;gCAC/B,MAAM,EAAE,YAAY;gCACpB,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK;6BAC3B,CAAC,CAAC;4BAEG,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;4BAClB,qBAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oCACxC,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE;oCACpC,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;iCACrB,CAAC,EAAA;;4BAHI,IAAI,GAAG,SAGX;4BAEF,IAAI,CAAC,IAAI,EAAE,CAAC;gCACJ,KAAK,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAQ,CAAC;gCACjD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAEc,qBAAM,gBAAM,CAAC,MAAM,CAAC,SAAS,CAAC;oCAC3C,KAAK,EAAE;wCACL,EAAE,IAAA;wCACF,MAAM,EAAE,IAAI,CAAC,EAAE;wCACf,QAAQ,EAAE,IAAI;qCACf;iCACF,CAAC,EAAA;;4BANI,MAAM,GAAG,SAMb;4BAEI,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC;4BAC5C,YAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE;gCAC9C,MAAM,EAAE,IAAI,CAAC,EAAE;6BAChB,CAAC,CAAC;4BAEH,IAAI,CAAC,MAAM,EAAE,CAAC;gCACN,KAAK,GAAG,IAAI,KAAK,CAAC,kBAAkB,CAAQ,CAAC;gCACnD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAEK,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;4BAC7C,YAAG,CAAC,GAAG,CAAC,KAAK,EAAE,8BAAuB,EAAE,CAAE,EAAE,GAAG,EAAE,aAAa,EAAE;gCAC9D,SAAS,EAAE,oBAAoB;gCAC/B,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK;6BAC3B,CAAC,CAAC;4BAEH,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,MAAM;iCACb,CAAC,EAAC;;;iBACJ,CACF,EAAC;;KACH,CAAC,CAAC;AAEH,sBAAsB;AACT,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,uFAC1C,OAAoB,EACpB,EAA+C;QAA7C,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAkB,EAAC,OAAO,EAAE;;oBACjC,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,4BAA4B;wBAC3E;;;;;;wCACQ,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wCACb,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;wCAA7C,OAAO,GAAG,SAAmC;wCACpC,qBAAM,MAAM,EAAA;;wCAAnB,EAAE,GAAK,CAAA,SAAY,CAAA,GAAjB;wCAEV,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,KAAK,CAAA,EAAE,CAAC;4CACpB,KAAK,GAAG,IAAI,KAAK,CAAC,mBAAmB,CAAQ,CAAC;4CACpD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAEY,qBAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gDACxC,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE;gDACpC,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;6CACrB,CAAC,EAAA;;wCAHI,IAAI,GAAG,SAGX;wCAEF,IAAI,CAAC,IAAI,EAAE,CAAC;4CACJ,KAAK,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAQ,CAAC;4CACjD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAGsB,qBAAM,gBAAM,CAAC,MAAM,CAAC,SAAS,CAAC;gDACnD,KAAK,EAAE;oDACL,EAAE,IAAA;oDACF,MAAM,EAAE,IAAI,CAAC,EAAE;oDACf,QAAQ,EAAE,IAAI;iDACf;6CACF,CAAC,EAAA;;wCANI,cAAc,GAAG,SAMrB;wCAEF,IAAI,CAAC,cAAc,EAAE,CAAC;4CACd,KAAK,GAAG,IAAI,KAAK,CAAC,kBAAkB,CAAQ,CAAC;4CACnD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAEY,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;wCAA3B,IAAI,GAAG,SAAoB;wCAG3B,oBAAoB,GAAG,yCAAmB,CAAC,0BAA0B,EAAE,CAAC;wCACxE,cAAc,GAAG,yCAAmB,CAAC,oBAAoB,EAAE,CAAC;6CAG9D,IAAI,CAAC,YAAY,EAAjB,wBAAiB;wCACQ,qBAAM,oBAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,EAAA;;wCAA3E,kBAAkB,GAAG,SAAsD;wCACjF,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;4CAC1B,KAAK,GAAG,IAAI,KAAK,CAAC,wCAAwC,CAAQ,CAAC;4CACzE,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,KAAK,CAAC,OAAO,GAAG,kBAAkB,CAAC,MAAM,CAAC;4CAC1C,MAAM,KAAK,CAAC;wCACd,CAAC;wCACD,IAAI,CAAC,YAAY,GAAG,kBAAkB,CAAC,aAAa,CAAC;;4CAIlC,qBAAM,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAA;;wCAAlD,YAAY,GAAG,SAAmC;wCACxD,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;4CACpB,KAAK,GAAG,IAAI,KAAK,CAAC,+BAA+B,CAAQ,CAAC;4CAChE,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,KAAK,CAAC,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC;4CACpC,MAAM,KAAK,CAAC;wCACd,CAAC;wCAGK,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC;wCAG3C,aAAa,GAAG,kBAAkB,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;wCAE5D,YAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE;4CAC1B,SAAS,EAAE,oBAAoB;4CAC/B,MAAM,EAAE,eAAe;4CACvB,MAAM,EAAE,IAAI,CAAC,EAAE;yCAChB,CAAC,CAAC;wCAEG,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wCACT,qBAAM,gBAAM,CAAC,MAAM,CAAC,MAAM,CAAC;gDAC/C,KAAK,EAAE,EAAE,EAAE,IAAA,EAAE;gDACb,IAAI,wBACC,aAAa,KAChB,SAAS,EAAE,IAAI,IAAI,EAAE,GACtB;6CACF,CAAC,EAAA;;wCANI,aAAa,GAAG,SAMpB;wCAEI,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC;wCAC5C,YAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE;4CAC3C,MAAM,EAAE,IAAI,CAAC,EAAE;yCAChB,CAAC,CAAC;wCAEG,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wCAC7C,YAAG,CAAC,GAAG,CAAC,KAAK,EAAE,8BAAuB,EAAE,CAAE,EAAE,GAAG,EAAE,aAAa,EAAE;4CAC9D,SAAS,EAAE,oBAAoB;4CAC/B,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK;yCAC3B,CAAC,CAAC;wCAEL,sBAAO,qBAAY,CAAC,IAAI,CAAC;gDACvB,OAAO,EAAE,IAAI;gDACb,IAAI,EAAE,aAAa;6CACpB,CAAC,EAAC;;;6BACJ,CACF,EAAC;;iBACH,CAAC,EAAC;;KACJ,CAAC,CAAC;AAEH,uCAAuC;AAC1B,QAAA,MAAM,GAAG,IAAA,oDAAwB,EAAC,uFAC7C,OAAoB,EACpB,EAA+C;QAA7C,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAkB,EAAC,OAAO,EAAE;;oBACjC,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,4BAA4B;wBAC3E;;;;;;wCACQ,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wCACb,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;wCAA7C,OAAO,GAAG,SAAmC;wCACpC,qBAAM,MAAM,EAAA;;wCAAnB,EAAE,GAAK,CAAA,SAAY,CAAA,GAAjB;wCAEV,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,KAAK,CAAA,EAAE,CAAC;4CACpB,KAAK,GAAG,IAAI,KAAK,CAAC,mBAAmB,CAAQ,CAAC;4CACpD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAEY,qBAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gDACxC,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE;gDACpC,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;6CACrB,CAAC,EAAA;;wCAHI,IAAI,GAAG,SAGX;wCAEF,IAAI,CAAC,IAAI,EAAE,CAAC;4CACJ,KAAK,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAQ,CAAC;4CACjD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAGsB,qBAAM,gBAAM,CAAC,MAAM,CAAC,SAAS,CAAC;gDACnD,KAAK,EAAE;oDACL,EAAE,IAAA;oDACF,MAAM,EAAE,IAAI,CAAC,EAAE;oDACf,QAAQ,EAAE,IAAI;iDACf;6CACF,CAAC,EAAA;;wCANI,cAAc,GAAG,SAMrB;wCAEF,IAAI,CAAC,cAAc,EAAE,CAAC;4CACd,KAAK,GAAG,IAAI,KAAK,CAAC,kBAAkB,CAAQ,CAAC;4CACnD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAED,YAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE;4CAC1B,SAAS,EAAE,oBAAoB;4CAC/B,MAAM,EAAE,eAAe;4CACvB,MAAM,EAAE,IAAI,CAAC,EAAE;yCAChB,CAAC,CAAC;wCAEG,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wCAC/B,2CAA2C;wCAC3C,qBAAM,gBAAM,CAAC,MAAM,CAAC,MAAM,CAAC;gDACzB,KAAK,EAAE,EAAE,EAAE,IAAA,EAAE;gDACb,IAAI,EAAE;oDACJ,QAAQ,EAAE,KAAK;oDACf,SAAS,EAAE,IAAI,IAAI,EAAE;iDACtB;6CACF,CAAC,EAAA;;wCAPF,2CAA2C;wCAC3C,SAME,CAAC;wCAEG,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC;wCAC5C,YAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE;4CAC3C,MAAM,EAAE,IAAI,CAAC,EAAE;4CACf,MAAM,EAAE,aAAa;yCACtB,CAAC,CAAC;wCAEG,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wCAC7C,YAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,8BAAuB,EAAE,CAAE,EAAE,GAAG,EAAE,aAAa,EAAE;4CACjE,SAAS,EAAE,oBAAoB;4CAC/B,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK;yCAC3B,CAAC,CAAC;wCAEH,sBAAO,qBAAY,CAAC,IAAI,CAAC;gDACvB,OAAO,EAAE,IAAI;gDACb,OAAO,EAAE,6BAA6B;6CACvC,CAAC,EAAC;;;6BACJ,CACF,EAAC;;iBACH,CAAC,EAAC;;KACJ,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/resume-builder/[id]/route.ts"],
      sourcesContent: ["import { NextResponse, NextRequest } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport prisma from '@/lib/prisma';\nimport { ErrorReporter } from '@/lib/errorReporting';\nimport { log } from '@/lib/logger';\nimport { trackError } from '@/lib/errorTracking';\nimport { withCSRFProtection } from '@/lib/csrf';\nimport { withRateLimit } from '@/lib/rateLimit';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\nimport { z } from 'zod';\nimport { ValidationPipelines } from '@/lib/validation-pipeline';\n\n// Validation schemas with proper length limits (same as in main route)\nconst personalInfoSchema = z.object({\n  firstName: z.string()\n    .min(1, 'First name is required')\n    .max(50, 'First name must be less than 50 characters')\n    .regex(/^[a-zA-Z\\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes'),\n  lastName: z.string()\n    .min(1, 'Last name is required')\n    .max(50, 'Last name must be less than 50 characters')\n    .regex(/^[a-zA-Z\\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes'),\n  email: z.string()\n    .email('Valid email is required')\n    .max(254, 'Email is too long'),\n  phone: z.string()\n    .max(20, 'Phone number is too long')\n    .regex(/^[\\+]?[1-9][\\d\\s\\-\\(\\)]{0,15}$/, 'Please enter a valid phone number')\n    .optional()\n    .or(z.literal('')),\n  location: z.string()\n    .max(100, 'Location must be less than 100 characters')\n    .optional(),\n  website: z.string()\n    .url('Please enter a valid website URL')\n    .max(500, 'Website URL is too long')\n    .optional()\n    .or(z.literal('')),\n  linkedIn: z.string()\n    .url('Please enter a valid LinkedIn URL')\n    .max(500, 'LinkedIn URL is too long')\n    .optional()\n    .or(z.literal(''))\n});\n\nconst experienceSchema = z.object({\n  company: z.string().min(1, 'Company name is required'),\n  position: z.string().min(1, 'Position is required'),\n  startDate: z.string().min(1, 'Start date is required'),\n  endDate: z.string().optional(),\n  description: z.string().optional(),\n  achievements: z.array(z.string()).optional()\n});\n\nconst educationSchema = z.object({\n  institution: z.string().min(1, 'Institution is required'),\n  degree: z.string().min(1, 'Degree is required'),\n  field: z.string().optional(),\n  startDate: z.string().optional(),\n  endDate: z.string().optional(),\n  gpa: z.string().optional(),\n  honors: z.string().optional()\n});\n\nconst skillSchema = z.object({\n  name: z.string().min(1, 'Skill name is required'),\n  level: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),\n  category: z.string().optional()\n});\n\nconst resumeUpdateSchema = z.object({\n  title: z.string().min(1, 'Resume title is required').optional(),\n  personalInfo: personalInfoSchema.optional(),\n  summary: z.string().optional(),\n  experience: z.array(experienceSchema).optional(),\n  education: z.array(educationSchema).optional(),\n  skills: z.array(skillSchema).optional(),\n  sections: z.record(z.any()).optional(),\n  template: z.string().optional(),\n  isPublic: z.boolean().optional()\n});\n\n// GET - Retrieve specific resume\nexport const GET = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) => {\n  return withRateLimit(\n    request,\n    { windowMs: 15 * 60 * 1000, maxRequests: 200 }, // 200 requests per 15 minutes\n    async () => {\n      const startTime = Date.now();\n      const session = await getServerSession(authOptions);\n      const { id } = await params;\n\n      if (!session?.user?.email) {\n        log.auth('resume_access_denied', undefined, false, {\n          component: 'resume_builder_api',\n          action: 'get_resume'\n        });\n        const error = new Error('Not authenticated') as any;\n        error.statusCode = 401;\n        throw error;\n      }\n\n      log.info('Fetching resume', {\n        component: 'resume_builder_api',\n        action: 'get_resume',\n        userId: session.user.email\n      });\n\n      const dbStartTime = Date.now();\n      const user = await prisma.user.findUnique({\n        where: { email: session.user.email },\n        select: { id: true }\n      });\n\n      if (!user) {\n        const error = new Error('User not found') as any;\n        error.statusCode = 404;\n        throw error;\n      }\n\n      const resume = await prisma.resume.findFirst({\n        where: {\n          id,\n          userId: user.id,\n          isActive: true\n        }\n      });\n\n      const dbDuration = Date.now() - dbStartTime;\n      log.database('findFirst', 'resume', dbDuration, {\n        userId: user.id\n      });\n\n      if (!resume) {\n        const error = new Error('Resume not found') as any;\n        error.statusCode = 404;\n        throw error;\n      }\n\n      const totalDuration = Date.now() - startTime;\n      log.api('GET', `/api/resume-builder/${id}`, 200, totalDuration, {\n        component: 'resume_builder_api',\n        userId: session.user.email\n      });\n\n      return NextResponse.json({\n        success: true,\n        data: resume\n      });\n    }\n  );\n});\n\n// PUT - Update resume\nexport const PUT = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) => {\n  return withCSRFProtection(request, async () => {\n    return withRateLimit(\n      request,\n      { windowMs: 15 * 60 * 1000, maxRequests: 50 }, // 50 updates per 15 minutes\n      async () => {\n        const startTime = Date.now();\n        const session = await getServerSession(authOptions);\n        const { id } = await params;\n\n        if (!session?.user?.email) {\n          const error = new Error('Not authenticated') as any;\n          error.statusCode = 401;\n          throw error;\n        }\n\n        const user = await prisma.user.findUnique({\n          where: { email: session.user.email },\n          select: { id: true }\n        });\n\n        if (!user) {\n          const error = new Error('User not found') as any;\n          error.statusCode = 404;\n          throw error;\n        }\n\n        // Verify resume ownership\n        const existingResume = await prisma.resume.findFirst({\n          where: {\n            id,\n            userId: user.id,\n            isActive: true\n          }\n        });\n\n        if (!existingResume) {\n          const error = new Error('Resume not found') as any;\n          error.statusCode = 404;\n          throw error;\n        }\n\n        const body = await request.json();\n\n        // Step 1: Use validation pipeline for comprehensive validation and sanitization\n        const personalInfoPipeline = ValidationPipelines.createPersonalInfoPipeline();\n        const resumePipeline = ValidationPipelines.createResumePipeline();\n\n        // Validate personal info if provided\n        if (body.personalInfo) {\n          const personalInfoResult = await personalInfoPipeline.validate(body.personalInfo);\n          if (!personalInfoResult.isValid) {\n            const error = new Error('Personal information validation failed') as any;\n            error.statusCode = 400;\n            error.details = personalInfoResult.errors;\n            throw error;\n          }\n          body.personalInfo = personalInfoResult.sanitizedData;\n        }\n\n        // Validate resume data\n        const resumeResult = await resumePipeline.validate(body);\n        if (!resumeResult.isValid) {\n          const error = new Error('Resume data validation failed') as any;\n          error.statusCode = 400;\n          error.details = resumeResult.errors;\n          throw error;\n        }\n\n        // Step 2: Use sanitized data from validation pipeline\n        const sanitizedBody = resumeResult.sanitizedData;\n\n        // Step 3: Additional Zod validation for complex structures\n        const validatedData = resumeUpdateSchema.parse(sanitizedBody);\n\n          log.info('Updating resume', {\n            component: 'resume_builder_api',\n            action: 'update_resume',\n            userId: user.id\n          });\n\n          const dbStartTime = Date.now();\n          const updatedResume = await prisma.resume.update({\n            where: { id },\n            data: {\n              ...validatedData,\n              updatedAt: new Date()\n            }\n          });\n\n          const dbDuration = Date.now() - dbStartTime;\n          log.database('update', 'resume', dbDuration, {\n            userId: user.id\n          });\n\n          const totalDuration = Date.now() - startTime;\n          log.api('PUT', `/api/resume-builder/${id}`, 200, totalDuration, {\n            component: 'resume_builder_api',\n            userId: session.user.email\n          });\n\n        return NextResponse.json({\n          success: true,\n          data: updatedResume\n        });\n      }\n    );\n  });\n});\n\n// DELETE - Delete resume (soft delete)\nexport const DELETE = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) => {\n  return withCSRFProtection(request, async () => {\n    return withRateLimit(\n      request,\n      { windowMs: 15 * 60 * 1000, maxRequests: 20 }, // 20 deletes per 15 minutes\n      async () => {\n        const startTime = Date.now();\n        const session = await getServerSession(authOptions);\n        const { id } = await params;\n\n        if (!session?.user?.email) {\n          const error = new Error('Not authenticated') as any;\n          error.statusCode = 401;\n          throw error;\n        }\n\n        const user = await prisma.user.findUnique({\n          where: { email: session.user.email },\n          select: { id: true }\n        });\n\n        if (!user) {\n          const error = new Error('User not found') as any;\n          error.statusCode = 404;\n          throw error;\n        }\n\n        // Verify resume ownership\n        const existingResume = await prisma.resume.findFirst({\n          where: {\n            id,\n            userId: user.id,\n            isActive: true\n          }\n        });\n\n        if (!existingResume) {\n          const error = new Error('Resume not found') as any;\n          error.statusCode = 404;\n          throw error;\n        }\n\n        log.info('Deleting resume', {\n          component: 'resume_builder_api',\n          action: 'delete_resume',\n          userId: user.id\n        });\n\n        const dbStartTime = Date.now();\n        // Soft delete by setting isActive to false\n        await prisma.resume.update({\n          where: { id },\n          data: {\n            isActive: false,\n            updatedAt: new Date()\n          }\n        });\n\n        const dbDuration = Date.now() - dbStartTime;\n        log.database('update', 'resume', dbDuration, {\n          userId: user.id,\n          action: 'soft_delete'\n        });\n\n        const totalDuration = Date.now() - startTime;\n        log.api('DELETE', `/api/resume-builder/${id}`, 200, totalDuration, {\n          component: 'resume_builder_api',\n          userId: session.user.email\n        });\n\n        return NextResponse.json({\n          success: true,\n          message: 'Resume deleted successfully'\n        });\n      }\n    );\n  });\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "216070d388250565c3639160a8fb299a10005312"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2410vbosi2 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2410vbosi2();
var __assign =
/* istanbul ignore next */
(cov_2410vbosi2().s[0]++,
/* istanbul ignore next */
(cov_2410vbosi2().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2410vbosi2().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_2410vbosi2().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_2410vbosi2().f[0]++;
  cov_2410vbosi2().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_2410vbosi2().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_2410vbosi2().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_2410vbosi2().f[1]++;
    cov_2410vbosi2().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_2410vbosi2().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_2410vbosi2().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_2410vbosi2().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_2410vbosi2().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_2410vbosi2().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_2410vbosi2().b[2][0]++;
          cov_2410vbosi2().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_2410vbosi2().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_2410vbosi2().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_2410vbosi2().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_2410vbosi2().s[11]++,
/* istanbul ignore next */
(cov_2410vbosi2().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_2410vbosi2().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_2410vbosi2().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_2410vbosi2().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_2410vbosi2().f[3]++;
    cov_2410vbosi2().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_2410vbosi2().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_2410vbosi2().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_2410vbosi2().f[4]++;
      cov_2410vbosi2().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_2410vbosi2().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_2410vbosi2().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_2410vbosi2().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_2410vbosi2().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_2410vbosi2().f[6]++;
      cov_2410vbosi2().s[15]++;
      try {
        /* istanbul ignore next */
        cov_2410vbosi2().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2410vbosi2().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_2410vbosi2().f[7]++;
      cov_2410vbosi2().s[18]++;
      try {
        /* istanbul ignore next */
        cov_2410vbosi2().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2410vbosi2().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_2410vbosi2().f[8]++;
      cov_2410vbosi2().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_2410vbosi2().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_2410vbosi2().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_2410vbosi2().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_2410vbosi2().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_2410vbosi2().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_2410vbosi2().s[23]++,
/* istanbul ignore next */
(cov_2410vbosi2().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_2410vbosi2().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_2410vbosi2().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_2410vbosi2().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_2410vbosi2().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_2410vbosi2().f[10]++;
        cov_2410vbosi2().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_2410vbosi2().b[9][0]++;
          cov_2410vbosi2().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_2410vbosi2().b[9][1]++;
        }
        cov_2410vbosi2().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_2410vbosi2().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_2410vbosi2().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_2410vbosi2().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_2410vbosi2().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_2410vbosi2().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_2410vbosi2().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_2410vbosi2().f[11]++;
    cov_2410vbosi2().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_2410vbosi2().f[12]++;
    cov_2410vbosi2().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_2410vbosi2().f[13]++;
      cov_2410vbosi2().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_2410vbosi2().f[14]++;
    cov_2410vbosi2().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_2410vbosi2().b[12][0]++;
      cov_2410vbosi2().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_2410vbosi2().b[12][1]++;
    }
    cov_2410vbosi2().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_2410vbosi2().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_2410vbosi2().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_2410vbosi2().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_2410vbosi2().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_2410vbosi2().s[36]++;
      try {
        /* istanbul ignore next */
        cov_2410vbosi2().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_2410vbosi2().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_2410vbosi2().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_2410vbosi2().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_2410vbosi2().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_2410vbosi2().b[18][0]++,
        /* istanbul ignore next */
        (cov_2410vbosi2().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_2410vbosi2().b[19][1]++,
        /* istanbul ignore next */
        (cov_2410vbosi2().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_2410vbosi2().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_2410vbosi2().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_2410vbosi2().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_2410vbosi2().b[15][0]++;
          cov_2410vbosi2().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_2410vbosi2().b[15][1]++;
        }
        cov_2410vbosi2().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_2410vbosi2().b[21][0]++;
          cov_2410vbosi2().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_2410vbosi2().b[21][1]++;
        }
        cov_2410vbosi2().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_2410vbosi2().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_2410vbosi2().b[22][1]++;
            cov_2410vbosi2().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_2410vbosi2().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_2410vbosi2().b[22][2]++;
            cov_2410vbosi2().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_2410vbosi2().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_2410vbosi2().b[22][3]++;
            cov_2410vbosi2().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_2410vbosi2().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_2410vbosi2().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_2410vbosi2().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_2410vbosi2().b[22][4]++;
            cov_2410vbosi2().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_2410vbosi2().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2410vbosi2().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_2410vbosi2().b[22][5]++;
            cov_2410vbosi2().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_2410vbosi2().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_2410vbosi2().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_2410vbosi2().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_2410vbosi2().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_2410vbosi2().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_2410vbosi2().b[23][0]++;
              cov_2410vbosi2().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_2410vbosi2().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_2410vbosi2().b[23][1]++;
            }
            cov_2410vbosi2().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_2410vbosi2().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_2410vbosi2().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_2410vbosi2().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_2410vbosi2().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_2410vbosi2().b[26][0]++;
              cov_2410vbosi2().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_2410vbosi2().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2410vbosi2().b[26][1]++;
            }
            cov_2410vbosi2().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_2410vbosi2().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_2410vbosi2().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_2410vbosi2().b[28][0]++;
              cov_2410vbosi2().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_2410vbosi2().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_2410vbosi2().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2410vbosi2().b[28][1]++;
            }
            cov_2410vbosi2().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_2410vbosi2().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_2410vbosi2().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_2410vbosi2().b[30][0]++;
              cov_2410vbosi2().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_2410vbosi2().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_2410vbosi2().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2410vbosi2().b[30][1]++;
            }
            cov_2410vbosi2().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_2410vbosi2().b[32][0]++;
              cov_2410vbosi2().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_2410vbosi2().b[32][1]++;
            }
            cov_2410vbosi2().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2410vbosi2().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_2410vbosi2().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_2410vbosi2().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_2410vbosi2().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_2410vbosi2().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_2410vbosi2().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_2410vbosi2().b[33][0]++;
      cov_2410vbosi2().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_2410vbosi2().b[33][1]++;
    }
    cov_2410vbosi2().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_2410vbosi2().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_2410vbosi2().b[34][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_2410vbosi2().s[78]++,
/* istanbul ignore next */
(cov_2410vbosi2().b[35][0]++, this) &&
/* istanbul ignore next */
(cov_2410vbosi2().b[35][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_2410vbosi2().b[35][2]++, function (mod) {
  /* istanbul ignore next */
  cov_2410vbosi2().f[15]++;
  cov_2410vbosi2().s[79]++;
  return /* istanbul ignore next */(cov_2410vbosi2().b[37][0]++, mod) &&
  /* istanbul ignore next */
  (cov_2410vbosi2().b[37][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_2410vbosi2().b[36][0]++, mod) :
  /* istanbul ignore next */
  (cov_2410vbosi2().b[36][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_2410vbosi2().s[80]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2410vbosi2().s[81]++;
exports.DELETE = exports.PUT = exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_2410vbosi2().s[82]++, require("next/server"));
var next_1 =
/* istanbul ignore next */
(cov_2410vbosi2().s[83]++, require("next-auth/next"));
var auth_1 =
/* istanbul ignore next */
(cov_2410vbosi2().s[84]++, require("@/lib/auth"));
var prisma_1 =
/* istanbul ignore next */
(cov_2410vbosi2().s[85]++, __importDefault(require("@/lib/prisma")));
var logger_1 =
/* istanbul ignore next */
(cov_2410vbosi2().s[86]++, require("@/lib/logger"));
var csrf_1 =
/* istanbul ignore next */
(cov_2410vbosi2().s[87]++, require("@/lib/csrf"));
var rateLimit_1 =
/* istanbul ignore next */
(cov_2410vbosi2().s[88]++, require("@/lib/rateLimit"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_2410vbosi2().s[89]++, require("@/lib/unified-api-error-handler"));
var zod_1 =
/* istanbul ignore next */
(cov_2410vbosi2().s[90]++, require("zod"));
var validation_pipeline_1 =
/* istanbul ignore next */
(cov_2410vbosi2().s[91]++, require("@/lib/validation-pipeline"));
// Validation schemas with proper length limits (same as in main route)
var personalInfoSchema =
/* istanbul ignore next */
(cov_2410vbosi2().s[92]++, zod_1.z.object({
  firstName: zod_1.z.string().min(1, 'First name is required').max(50, 'First name must be less than 50 characters').regex(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes'),
  lastName: zod_1.z.string().min(1, 'Last name is required').max(50, 'Last name must be less than 50 characters').regex(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes'),
  email: zod_1.z.string().email('Valid email is required').max(254, 'Email is too long'),
  phone: zod_1.z.string().max(20, 'Phone number is too long').regex(/^[\+]?[1-9][\d\s\-\(\)]{0,15}$/, 'Please enter a valid phone number').optional().or(zod_1.z.literal('')),
  location: zod_1.z.string().max(100, 'Location must be less than 100 characters').optional(),
  website: zod_1.z.string().url('Please enter a valid website URL').max(500, 'Website URL is too long').optional().or(zod_1.z.literal('')),
  linkedIn: zod_1.z.string().url('Please enter a valid LinkedIn URL').max(500, 'LinkedIn URL is too long').optional().or(zod_1.z.literal(''))
}));
var experienceSchema =
/* istanbul ignore next */
(cov_2410vbosi2().s[93]++, zod_1.z.object({
  company: zod_1.z.string().min(1, 'Company name is required'),
  position: zod_1.z.string().min(1, 'Position is required'),
  startDate: zod_1.z.string().min(1, 'Start date is required'),
  endDate: zod_1.z.string().optional(),
  description: zod_1.z.string().optional(),
  achievements: zod_1.z.array(zod_1.z.string()).optional()
}));
var educationSchema =
/* istanbul ignore next */
(cov_2410vbosi2().s[94]++, zod_1.z.object({
  institution: zod_1.z.string().min(1, 'Institution is required'),
  degree: zod_1.z.string().min(1, 'Degree is required'),
  field: zod_1.z.string().optional(),
  startDate: zod_1.z.string().optional(),
  endDate: zod_1.z.string().optional(),
  gpa: zod_1.z.string().optional(),
  honors: zod_1.z.string().optional()
}));
var skillSchema =
/* istanbul ignore next */
(cov_2410vbosi2().s[95]++, zod_1.z.object({
  name: zod_1.z.string().min(1, 'Skill name is required'),
  level: zod_1.z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
  category: zod_1.z.string().optional()
}));
var resumeUpdateSchema =
/* istanbul ignore next */
(cov_2410vbosi2().s[96]++, zod_1.z.object({
  title: zod_1.z.string().min(1, 'Resume title is required').optional(),
  personalInfo: personalInfoSchema.optional(),
  summary: zod_1.z.string().optional(),
  experience: zod_1.z.array(experienceSchema).optional(),
  education: zod_1.z.array(educationSchema).optional(),
  skills: zod_1.z.array(skillSchema).optional(),
  sections: zod_1.z.record(zod_1.z.any()).optional(),
  template: zod_1.z.string().optional(),
  isPublic: zod_1.z.boolean().optional()
}));
// GET - Retrieve specific resume
/* istanbul ignore next */
cov_2410vbosi2().s[97]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_2410vbosi2().f[16]++;
  cov_2410vbosi2().s[98]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_2410vbosi2().f[17]++;
    var params =
    /* istanbul ignore next */
    (cov_2410vbosi2().s[99]++, _b.params);
    /* istanbul ignore next */
    cov_2410vbosi2().s[100]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_2410vbosi2().f[18]++;
      cov_2410vbosi2().s[101]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: 200
      },
      // 200 requests per 15 minutes
      function () {
        /* istanbul ignore next */
        cov_2410vbosi2().f[19]++;
        cov_2410vbosi2().s[102]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_2410vbosi2().f[20]++;
          var startTime, session, id, error, dbStartTime, user, error, resume, dbDuration, error, totalDuration;
          var _a;
          /* istanbul ignore next */
          cov_2410vbosi2().s[103]++;
          return __generator(this, function (_b) {
            /* istanbul ignore next */
            cov_2410vbosi2().f[21]++;
            cov_2410vbosi2().s[104]++;
            switch (_b.label) {
              case 0:
                /* istanbul ignore next */
                cov_2410vbosi2().b[38][0]++;
                cov_2410vbosi2().s[105]++;
                startTime = Date.now();
                /* istanbul ignore next */
                cov_2410vbosi2().s[106]++;
                return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_2410vbosi2().b[38][1]++;
                cov_2410vbosi2().s[107]++;
                session = _b.sent();
                /* istanbul ignore next */
                cov_2410vbosi2().s[108]++;
                return [4 /*yield*/, params];
              case 2:
                /* istanbul ignore next */
                cov_2410vbosi2().b[38][2]++;
                cov_2410vbosi2().s[109]++;
                id = _b.sent().id;
                /* istanbul ignore next */
                cov_2410vbosi2().s[110]++;
                if (!(
                /* istanbul ignore next */
                (cov_2410vbosi2().b[41][0]++, (_a =
                /* istanbul ignore next */
                (cov_2410vbosi2().b[43][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_2410vbosi2().b[43][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_2410vbosi2().b[42][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2410vbosi2().b[42][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_2410vbosi2().b[41][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_2410vbosi2().b[40][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2410vbosi2().b[40][1]++, _a.email))) {
                  /* istanbul ignore next */
                  cov_2410vbosi2().b[39][0]++;
                  cov_2410vbosi2().s[111]++;
                  logger_1.log.auth('resume_access_denied', undefined, false, {
                    component: 'resume_builder_api',
                    action: 'get_resume'
                  });
                  /* istanbul ignore next */
                  cov_2410vbosi2().s[112]++;
                  error = new Error('Not authenticated');
                  /* istanbul ignore next */
                  cov_2410vbosi2().s[113]++;
                  error.statusCode = 401;
                  /* istanbul ignore next */
                  cov_2410vbosi2().s[114]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_2410vbosi2().b[39][1]++;
                }
                cov_2410vbosi2().s[115]++;
                logger_1.log.info('Fetching resume', {
                  component: 'resume_builder_api',
                  action: 'get_resume',
                  userId: session.user.email
                });
                /* istanbul ignore next */
                cov_2410vbosi2().s[116]++;
                dbStartTime = Date.now();
                /* istanbul ignore next */
                cov_2410vbosi2().s[117]++;
                return [4 /*yield*/, prisma_1.default.user.findUnique({
                  where: {
                    email: session.user.email
                  },
                  select: {
                    id: true
                  }
                })];
              case 3:
                /* istanbul ignore next */
                cov_2410vbosi2().b[38][3]++;
                cov_2410vbosi2().s[118]++;
                user = _b.sent();
                /* istanbul ignore next */
                cov_2410vbosi2().s[119]++;
                if (!user) {
                  /* istanbul ignore next */
                  cov_2410vbosi2().b[44][0]++;
                  cov_2410vbosi2().s[120]++;
                  error = new Error('User not found');
                  /* istanbul ignore next */
                  cov_2410vbosi2().s[121]++;
                  error.statusCode = 404;
                  /* istanbul ignore next */
                  cov_2410vbosi2().s[122]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_2410vbosi2().b[44][1]++;
                }
                cov_2410vbosi2().s[123]++;
                return [4 /*yield*/, prisma_1.default.resume.findFirst({
                  where: {
                    id: id,
                    userId: user.id,
                    isActive: true
                  }
                })];
              case 4:
                /* istanbul ignore next */
                cov_2410vbosi2().b[38][4]++;
                cov_2410vbosi2().s[124]++;
                resume = _b.sent();
                /* istanbul ignore next */
                cov_2410vbosi2().s[125]++;
                dbDuration = Date.now() - dbStartTime;
                /* istanbul ignore next */
                cov_2410vbosi2().s[126]++;
                logger_1.log.database('findFirst', 'resume', dbDuration, {
                  userId: user.id
                });
                /* istanbul ignore next */
                cov_2410vbosi2().s[127]++;
                if (!resume) {
                  /* istanbul ignore next */
                  cov_2410vbosi2().b[45][0]++;
                  cov_2410vbosi2().s[128]++;
                  error = new Error('Resume not found');
                  /* istanbul ignore next */
                  cov_2410vbosi2().s[129]++;
                  error.statusCode = 404;
                  /* istanbul ignore next */
                  cov_2410vbosi2().s[130]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_2410vbosi2().b[45][1]++;
                }
                cov_2410vbosi2().s[131]++;
                totalDuration = Date.now() - startTime;
                /* istanbul ignore next */
                cov_2410vbosi2().s[132]++;
                logger_1.log.api('GET', "/api/resume-builder/".concat(id), 200, totalDuration, {
                  component: 'resume_builder_api',
                  userId: session.user.email
                });
                /* istanbul ignore next */
                cov_2410vbosi2().s[133]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: resume
                })];
            }
          });
        });
      })];
    });
  });
});
// PUT - Update resume
/* istanbul ignore next */
cov_2410vbosi2().s[134]++;
exports.PUT = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_2410vbosi2().f[22]++;
  cov_2410vbosi2().s[135]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_2410vbosi2().f[23]++;
    var params =
    /* istanbul ignore next */
    (cov_2410vbosi2().s[136]++, _b.params);
    /* istanbul ignore next */
    cov_2410vbosi2().s[137]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_2410vbosi2().f[24]++;
      cov_2410vbosi2().s[138]++;
      return [2 /*return*/, (0, csrf_1.withCSRFProtection)(request, function () {
        /* istanbul ignore next */
        cov_2410vbosi2().f[25]++;
        cov_2410vbosi2().s[139]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_2410vbosi2().f[26]++;
          cov_2410vbosi2().s[140]++;
          return __generator(this, function (_a) {
            /* istanbul ignore next */
            cov_2410vbosi2().f[27]++;
            cov_2410vbosi2().s[141]++;
            return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
              windowMs: 15 * 60 * 1000,
              maxRequests: 50
            },
            // 50 updates per 15 minutes
            function () {
              /* istanbul ignore next */
              cov_2410vbosi2().f[28]++;
              cov_2410vbosi2().s[142]++;
              return __awaiter(void 0, void 0, void 0, function () {
                /* istanbul ignore next */
                cov_2410vbosi2().f[29]++;
                var startTime, session, id, error, user, error, existingResume, error, body, personalInfoPipeline, resumePipeline, personalInfoResult, error, resumeResult, error, sanitizedBody, validatedData, dbStartTime, updatedResume, dbDuration, totalDuration;
                var _a;
                /* istanbul ignore next */
                cov_2410vbosi2().s[143]++;
                return __generator(this, function (_b) {
                  /* istanbul ignore next */
                  cov_2410vbosi2().f[30]++;
                  cov_2410vbosi2().s[144]++;
                  switch (_b.label) {
                    case 0:
                      /* istanbul ignore next */
                      cov_2410vbosi2().b[46][0]++;
                      cov_2410vbosi2().s[145]++;
                      startTime = Date.now();
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[146]++;
                      return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
                    case 1:
                      /* istanbul ignore next */
                      cov_2410vbosi2().b[46][1]++;
                      cov_2410vbosi2().s[147]++;
                      session = _b.sent();
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[148]++;
                      return [4 /*yield*/, params];
                    case 2:
                      /* istanbul ignore next */
                      cov_2410vbosi2().b[46][2]++;
                      cov_2410vbosi2().s[149]++;
                      id = _b.sent().id;
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[150]++;
                      if (!(
                      /* istanbul ignore next */
                      (cov_2410vbosi2().b[49][0]++, (_a =
                      /* istanbul ignore next */
                      (cov_2410vbosi2().b[51][0]++, session === null) ||
                      /* istanbul ignore next */
                      (cov_2410vbosi2().b[51][1]++, session === void 0) ?
                      /* istanbul ignore next */
                      (cov_2410vbosi2().b[50][0]++, void 0) :
                      /* istanbul ignore next */
                      (cov_2410vbosi2().b[50][1]++, session.user)) === null) ||
                      /* istanbul ignore next */
                      (cov_2410vbosi2().b[49][1]++, _a === void 0) ?
                      /* istanbul ignore next */
                      (cov_2410vbosi2().b[48][0]++, void 0) :
                      /* istanbul ignore next */
                      (cov_2410vbosi2().b[48][1]++, _a.email))) {
                        /* istanbul ignore next */
                        cov_2410vbosi2().b[47][0]++;
                        cov_2410vbosi2().s[151]++;
                        error = new Error('Not authenticated');
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[152]++;
                        error.statusCode = 401;
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[153]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_2410vbosi2().b[47][1]++;
                      }
                      cov_2410vbosi2().s[154]++;
                      return [4 /*yield*/, prisma_1.default.user.findUnique({
                        where: {
                          email: session.user.email
                        },
                        select: {
                          id: true
                        }
                      })];
                    case 3:
                      /* istanbul ignore next */
                      cov_2410vbosi2().b[46][3]++;
                      cov_2410vbosi2().s[155]++;
                      user = _b.sent();
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[156]++;
                      if (!user) {
                        /* istanbul ignore next */
                        cov_2410vbosi2().b[52][0]++;
                        cov_2410vbosi2().s[157]++;
                        error = new Error('User not found');
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[158]++;
                        error.statusCode = 404;
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[159]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_2410vbosi2().b[52][1]++;
                      }
                      cov_2410vbosi2().s[160]++;
                      return [4 /*yield*/, prisma_1.default.resume.findFirst({
                        where: {
                          id: id,
                          userId: user.id,
                          isActive: true
                        }
                      })];
                    case 4:
                      /* istanbul ignore next */
                      cov_2410vbosi2().b[46][4]++;
                      cov_2410vbosi2().s[161]++;
                      existingResume = _b.sent();
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[162]++;
                      if (!existingResume) {
                        /* istanbul ignore next */
                        cov_2410vbosi2().b[53][0]++;
                        cov_2410vbosi2().s[163]++;
                        error = new Error('Resume not found');
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[164]++;
                        error.statusCode = 404;
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[165]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_2410vbosi2().b[53][1]++;
                      }
                      cov_2410vbosi2().s[166]++;
                      return [4 /*yield*/, request.json()];
                    case 5:
                      /* istanbul ignore next */
                      cov_2410vbosi2().b[46][5]++;
                      cov_2410vbosi2().s[167]++;
                      body = _b.sent();
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[168]++;
                      personalInfoPipeline = validation_pipeline_1.ValidationPipelines.createPersonalInfoPipeline();
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[169]++;
                      resumePipeline = validation_pipeline_1.ValidationPipelines.createResumePipeline();
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[170]++;
                      if (!body.personalInfo) {
                        /* istanbul ignore next */
                        cov_2410vbosi2().b[54][0]++;
                        cov_2410vbosi2().s[171]++;
                        return [3 /*break*/, 7];
                      } else
                      /* istanbul ignore next */
                      {
                        cov_2410vbosi2().b[54][1]++;
                      }
                      cov_2410vbosi2().s[172]++;
                      return [4 /*yield*/, personalInfoPipeline.validate(body.personalInfo)];
                    case 6:
                      /* istanbul ignore next */
                      cov_2410vbosi2().b[46][6]++;
                      cov_2410vbosi2().s[173]++;
                      personalInfoResult = _b.sent();
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[174]++;
                      if (!personalInfoResult.isValid) {
                        /* istanbul ignore next */
                        cov_2410vbosi2().b[55][0]++;
                        cov_2410vbosi2().s[175]++;
                        error = new Error('Personal information validation failed');
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[176]++;
                        error.statusCode = 400;
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[177]++;
                        error.details = personalInfoResult.errors;
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[178]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_2410vbosi2().b[55][1]++;
                      }
                      cov_2410vbosi2().s[179]++;
                      body.personalInfo = personalInfoResult.sanitizedData;
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[180]++;
                      _b.label = 7;
                    case 7:
                      /* istanbul ignore next */
                      cov_2410vbosi2().b[46][7]++;
                      cov_2410vbosi2().s[181]++;
                      return [4 /*yield*/, resumePipeline.validate(body)];
                    case 8:
                      /* istanbul ignore next */
                      cov_2410vbosi2().b[46][8]++;
                      cov_2410vbosi2().s[182]++;
                      resumeResult = _b.sent();
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[183]++;
                      if (!resumeResult.isValid) {
                        /* istanbul ignore next */
                        cov_2410vbosi2().b[56][0]++;
                        cov_2410vbosi2().s[184]++;
                        error = new Error('Resume data validation failed');
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[185]++;
                        error.statusCode = 400;
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[186]++;
                        error.details = resumeResult.errors;
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[187]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_2410vbosi2().b[56][1]++;
                      }
                      cov_2410vbosi2().s[188]++;
                      sanitizedBody = resumeResult.sanitizedData;
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[189]++;
                      validatedData = resumeUpdateSchema.parse(sanitizedBody);
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[190]++;
                      logger_1.log.info('Updating resume', {
                        component: 'resume_builder_api',
                        action: 'update_resume',
                        userId: user.id
                      });
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[191]++;
                      dbStartTime = Date.now();
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[192]++;
                      return [4 /*yield*/, prisma_1.default.resume.update({
                        where: {
                          id: id
                        },
                        data: __assign(__assign({}, validatedData), {
                          updatedAt: new Date()
                        })
                      })];
                    case 9:
                      /* istanbul ignore next */
                      cov_2410vbosi2().b[46][9]++;
                      cov_2410vbosi2().s[193]++;
                      updatedResume = _b.sent();
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[194]++;
                      dbDuration = Date.now() - dbStartTime;
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[195]++;
                      logger_1.log.database('update', 'resume', dbDuration, {
                        userId: user.id
                      });
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[196]++;
                      totalDuration = Date.now() - startTime;
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[197]++;
                      logger_1.log.api('PUT', "/api/resume-builder/".concat(id), 200, totalDuration, {
                        component: 'resume_builder_api',
                        userId: session.user.email
                      });
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[198]++;
                      return [2 /*return*/, server_1.NextResponse.json({
                        success: true,
                        data: updatedResume
                      })];
                  }
                });
              });
            })];
          });
        });
      })];
    });
  });
});
// DELETE - Delete resume (soft delete)
/* istanbul ignore next */
cov_2410vbosi2().s[199]++;
exports.DELETE = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_2410vbosi2().f[31]++;
  cov_2410vbosi2().s[200]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_2410vbosi2().f[32]++;
    var params =
    /* istanbul ignore next */
    (cov_2410vbosi2().s[201]++, _b.params);
    /* istanbul ignore next */
    cov_2410vbosi2().s[202]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_2410vbosi2().f[33]++;
      cov_2410vbosi2().s[203]++;
      return [2 /*return*/, (0, csrf_1.withCSRFProtection)(request, function () {
        /* istanbul ignore next */
        cov_2410vbosi2().f[34]++;
        cov_2410vbosi2().s[204]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_2410vbosi2().f[35]++;
          cov_2410vbosi2().s[205]++;
          return __generator(this, function (_a) {
            /* istanbul ignore next */
            cov_2410vbosi2().f[36]++;
            cov_2410vbosi2().s[206]++;
            return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
              windowMs: 15 * 60 * 1000,
              maxRequests: 20
            },
            // 20 deletes per 15 minutes
            function () {
              /* istanbul ignore next */
              cov_2410vbosi2().f[37]++;
              cov_2410vbosi2().s[207]++;
              return __awaiter(void 0, void 0, void 0, function () {
                /* istanbul ignore next */
                cov_2410vbosi2().f[38]++;
                var startTime, session, id, error, user, error, existingResume, error, dbStartTime, dbDuration, totalDuration;
                var _a;
                /* istanbul ignore next */
                cov_2410vbosi2().s[208]++;
                return __generator(this, function (_b) {
                  /* istanbul ignore next */
                  cov_2410vbosi2().f[39]++;
                  cov_2410vbosi2().s[209]++;
                  switch (_b.label) {
                    case 0:
                      /* istanbul ignore next */
                      cov_2410vbosi2().b[57][0]++;
                      cov_2410vbosi2().s[210]++;
                      startTime = Date.now();
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[211]++;
                      return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
                    case 1:
                      /* istanbul ignore next */
                      cov_2410vbosi2().b[57][1]++;
                      cov_2410vbosi2().s[212]++;
                      session = _b.sent();
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[213]++;
                      return [4 /*yield*/, params];
                    case 2:
                      /* istanbul ignore next */
                      cov_2410vbosi2().b[57][2]++;
                      cov_2410vbosi2().s[214]++;
                      id = _b.sent().id;
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[215]++;
                      if (!(
                      /* istanbul ignore next */
                      (cov_2410vbosi2().b[60][0]++, (_a =
                      /* istanbul ignore next */
                      (cov_2410vbosi2().b[62][0]++, session === null) ||
                      /* istanbul ignore next */
                      (cov_2410vbosi2().b[62][1]++, session === void 0) ?
                      /* istanbul ignore next */
                      (cov_2410vbosi2().b[61][0]++, void 0) :
                      /* istanbul ignore next */
                      (cov_2410vbosi2().b[61][1]++, session.user)) === null) ||
                      /* istanbul ignore next */
                      (cov_2410vbosi2().b[60][1]++, _a === void 0) ?
                      /* istanbul ignore next */
                      (cov_2410vbosi2().b[59][0]++, void 0) :
                      /* istanbul ignore next */
                      (cov_2410vbosi2().b[59][1]++, _a.email))) {
                        /* istanbul ignore next */
                        cov_2410vbosi2().b[58][0]++;
                        cov_2410vbosi2().s[216]++;
                        error = new Error('Not authenticated');
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[217]++;
                        error.statusCode = 401;
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[218]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_2410vbosi2().b[58][1]++;
                      }
                      cov_2410vbosi2().s[219]++;
                      return [4 /*yield*/, prisma_1.default.user.findUnique({
                        where: {
                          email: session.user.email
                        },
                        select: {
                          id: true
                        }
                      })];
                    case 3:
                      /* istanbul ignore next */
                      cov_2410vbosi2().b[57][3]++;
                      cov_2410vbosi2().s[220]++;
                      user = _b.sent();
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[221]++;
                      if (!user) {
                        /* istanbul ignore next */
                        cov_2410vbosi2().b[63][0]++;
                        cov_2410vbosi2().s[222]++;
                        error = new Error('User not found');
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[223]++;
                        error.statusCode = 404;
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[224]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_2410vbosi2().b[63][1]++;
                      }
                      cov_2410vbosi2().s[225]++;
                      return [4 /*yield*/, prisma_1.default.resume.findFirst({
                        where: {
                          id: id,
                          userId: user.id,
                          isActive: true
                        }
                      })];
                    case 4:
                      /* istanbul ignore next */
                      cov_2410vbosi2().b[57][4]++;
                      cov_2410vbosi2().s[226]++;
                      existingResume = _b.sent();
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[227]++;
                      if (!existingResume) {
                        /* istanbul ignore next */
                        cov_2410vbosi2().b[64][0]++;
                        cov_2410vbosi2().s[228]++;
                        error = new Error('Resume not found');
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[229]++;
                        error.statusCode = 404;
                        /* istanbul ignore next */
                        cov_2410vbosi2().s[230]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_2410vbosi2().b[64][1]++;
                      }
                      cov_2410vbosi2().s[231]++;
                      logger_1.log.info('Deleting resume', {
                        component: 'resume_builder_api',
                        action: 'delete_resume',
                        userId: user.id
                      });
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[232]++;
                      dbStartTime = Date.now();
                      // Soft delete by setting isActive to false
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[233]++;
                      return [4 /*yield*/, prisma_1.default.resume.update({
                        where: {
                          id: id
                        },
                        data: {
                          isActive: false,
                          updatedAt: new Date()
                        }
                      })];
                    case 5:
                      /* istanbul ignore next */
                      cov_2410vbosi2().b[57][5]++;
                      cov_2410vbosi2().s[234]++;
                      // Soft delete by setting isActive to false
                      _b.sent();
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[235]++;
                      dbDuration = Date.now() - dbStartTime;
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[236]++;
                      logger_1.log.database('update', 'resume', dbDuration, {
                        userId: user.id,
                        action: 'soft_delete'
                      });
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[237]++;
                      totalDuration = Date.now() - startTime;
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[238]++;
                      logger_1.log.api('DELETE', "/api/resume-builder/".concat(id), 200, totalDuration, {
                        component: 'resume_builder_api',
                        userId: session.user.email
                      });
                      /* istanbul ignore next */
                      cov_2410vbosi2().s[239]++;
                      return [2 /*return*/, server_1.NextResponse.json({
                        success: true,
                        message: 'Resume deleted successfully'
                      })];
                  }
                });
              });
            })];
          });
        });
      })];
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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