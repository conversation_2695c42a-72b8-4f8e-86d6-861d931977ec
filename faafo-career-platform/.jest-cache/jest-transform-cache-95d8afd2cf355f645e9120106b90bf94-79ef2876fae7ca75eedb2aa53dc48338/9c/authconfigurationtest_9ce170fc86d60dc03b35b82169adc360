2140e706709f5315fb86254d2bd12e05
"use strict";
/**
 * Comprehensive Authentication Configuration Tests
 * Tests NextAuth configuration, providers, callbacks, and security settings
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
// Mock NextAuth providers before importing
jest.mock('next-auth/providers/credentials', function () {
    var mockCredentialsProvider = jest.fn(function () { return ({
        id: 'credentials',
        name: 'Credentials',
        type: 'credentials',
        credentials: {
            email: { label: 'Email', type: 'email' },
            password: { label: 'Password', type: 'password' }
        },
        authorize: jest.fn()
    }); });
    return {
        __esModule: true,
        default: mockCredentialsProvider
    };
});
jest.mock('next-auth/providers/email', function () {
    var mockEmailProvider = jest.fn(function () { return ({
        id: 'email',
        name: 'Email',
        type: 'email'
    }); });
    return {
        __esModule: true,
        default: mockEmailProvider
    };
});
jest.mock('@auth/prisma-adapter', function () { return ({
    PrismaAdapter: jest.fn(function () { return ({
        createUser: jest.fn(),
        getUser: jest.fn(),
        getUserByEmail: jest.fn(),
        getUserByAccount: jest.fn(),
        updateUser: jest.fn(),
        deleteUser: jest.fn(),
        linkAccount: jest.fn(),
        unlinkAccount: jest.fn(),
        createSession: jest.fn(),
        getSessionAndUser: jest.fn(),
        updateSession: jest.fn(),
        deleteSession: jest.fn(),
        createVerificationToken: jest.fn(),
        useVerificationToken: jest.fn(),
    }); })
}); });
var auth_1 = require("@/lib/auth");
describe('Authentication Configuration', function () {
    describe('NextAuth Configuration', function () {
        it('should have valid authOptions configuration', function () {
            expect(auth_1.authOptions).toBeDefined();
            expect(auth_1.authOptions).toBeInstanceOf(Object);
        });
        it('should have Prisma adapter configured', function () {
            // In test environment, adapter might be mocked, so check if it exists or is a function
            expect(auth_1.authOptions.adapter).toBeDefined();
            expect(typeof auth_1.authOptions.adapter === 'object' || typeof auth_1.authOptions.adapter === 'function').toBe(true);
        });
        it('should have correct session strategy', function () {
            var _a;
            expect((_a = auth_1.authOptions.session) === null || _a === void 0 ? void 0 : _a.strategy).toBe('jwt');
        });
        it('should have appropriate session timeouts', function () {
            var _a, _b;
            expect((_a = auth_1.authOptions.session) === null || _a === void 0 ? void 0 : _a.maxAge).toBeGreaterThan(0);
            expect((_b = auth_1.authOptions.jwt) === null || _b === void 0 ? void 0 : _b.maxAge).toBeGreaterThan(0);
        });
    });
    describe('Authentication Providers', function () {
        it('should have credentials provider configured', function () {
            expect(auth_1.authOptions.providers).toBeDefined();
            expect(Array.isArray(auth_1.authOptions.providers)).toBe(true);
            expect(auth_1.authOptions.providers.length).toBeGreaterThan(0);
            var credentialsProvider = auth_1.authOptions.providers.find(function (provider) { return provider.type === 'credentials'; });
            expect(credentialsProvider).toBeDefined();
        });
        it('should have email provider configured', function () {
            var emailProvider = auth_1.authOptions.providers.find(function (provider) { return provider.type === 'email'; });
            expect(emailProvider).toBeDefined();
        });
        it('should have proper credentials configuration', function () {
            var credentialsProvider = auth_1.authOptions.providers.find(function (provider) { return provider.type === 'credentials'; });
            expect(credentialsProvider).toBeDefined();
            // In test environment, credentials might be mocked differently
            if (credentialsProvider.credentials) {
                expect(credentialsProvider.credentials.email).toBeDefined();
                expect(credentialsProvider.credentials.password).toBeDefined();
            }
            else {
                // Accept that credentials provider exists even if structure is mocked
                expect(credentialsProvider.type).toBe('credentials');
            }
        });
    });
    describe('Security Configuration', function () {
        it('should have secure cookie settings', function () {
            var _a, _b;
            expect(auth_1.authOptions.cookies).toBeDefined();
            expect((_a = auth_1.authOptions.cookies) === null || _a === void 0 ? void 0 : _a.sessionToken).toBeDefined();
            expect((_b = auth_1.authOptions.cookies) === null || _b === void 0 ? void 0 : _b.csrfToken).toBeDefined();
        });
        it('should have httpOnly cookies in production', function () {
            var _a, _b;
            var sessionCookie = (_a = auth_1.authOptions.cookies) === null || _a === void 0 ? void 0 : _a.sessionToken;
            expect((_b = sessionCookie === null || sessionCookie === void 0 ? void 0 : sessionCookie.options) === null || _b === void 0 ? void 0 : _b.httpOnly).toBe(true);
        });
        it('should have proper sameSite settings', function () {
            var _a, _b;
            var sessionCookie = (_a = auth_1.authOptions.cookies) === null || _a === void 0 ? void 0 : _a.sessionToken;
            expect((_b = sessionCookie === null || sessionCookie === void 0 ? void 0 : sessionCookie.options) === null || _b === void 0 ? void 0 : _b.sameSite).toBe('lax');
        });
        it('should have secure cookies in production', function () {
            var _a, _b;
            var originalEnv = process.env.NODE_ENV;
            process.env.NODE_ENV = 'production';
            // Re-import to get updated config
            jest.resetModules();
            var prodAuthOptions = require('@/lib/auth').authOptions;
            var sessionCookie = (_a = prodAuthOptions.cookies) === null || _a === void 0 ? void 0 : _a.sessionToken;
            expect((_b = sessionCookie === null || sessionCookie === void 0 ? void 0 : sessionCookie.options) === null || _b === void 0 ? void 0 : _b.secure).toBe(true);
            process.env.NODE_ENV = originalEnv;
        });
    });
    describe('JWT Callbacks', function () {
        it('should have jwt callback defined', function () {
            var _a, _b;
            expect((_a = auth_1.authOptions.callbacks) === null || _a === void 0 ? void 0 : _a.jwt).toBeDefined();
            expect(typeof ((_b = auth_1.authOptions.callbacks) === null || _b === void 0 ? void 0 : _b.jwt)).toBe('function');
        });
        it('should have session callback defined', function () {
            var _a, _b;
            expect((_a = auth_1.authOptions.callbacks) === null || _a === void 0 ? void 0 : _a.session).toBeDefined();
            expect(typeof ((_b = auth_1.authOptions.callbacks) === null || _b === void 0 ? void 0 : _b.session)).toBe('function');
        });
        it('should handle JWT token creation properly', function () { return __awaiter(void 0, void 0, void 0, function () {
            var jwtCallback, mockToken, mockUser, result;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        jwtCallback = (_a = auth_1.authOptions.callbacks) === null || _a === void 0 ? void 0 : _a.jwt;
                        if (!jwtCallback) {
                            throw new Error('JWT callback not defined');
                        }
                        mockToken = { sub: 'test-user-id' };
                        mockUser = { id: 'test-user-id', email: '<EMAIL>' };
                        return [4 /*yield*/, jwtCallback({
                                token: mockToken,
                                user: mockUser,
                                trigger: 'signIn'
                            })];
                    case 1:
                        result = _b.sent();
                        expect(result).toBeDefined();
                        expect(result.sub).toBe('test-user-id');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle session creation properly', function () { return __awaiter(void 0, void 0, void 0, function () {
            var sessionCallback, mockSession, mockToken, result;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        sessionCallback = (_a = auth_1.authOptions.callbacks) === null || _a === void 0 ? void 0 : _a.session;
                        if (!sessionCallback) {
                            throw new Error('Session callback not defined');
                        }
                        mockSession = {
                            user: { id: 'test-user-id', email: '<EMAIL>' },
                            expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
                        };
                        mockToken = { sub: 'test-user-id', email: '<EMAIL>' };
                        return [4 /*yield*/, sessionCallback({
                                session: mockSession,
                                token: mockToken
                            })];
                    case 1:
                        result = _b.sent();
                        expect(result).toBeDefined();
                        expect(result.user).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Pages Configuration', function () {
        it('should have custom pages configured', function () {
            expect(auth_1.authOptions.pages).toBeDefined();
        });
        it('should have custom sign in page', function () {
            var _a, _b;
            expect((_a = auth_1.authOptions.pages) === null || _a === void 0 ? void 0 : _a.signIn).toBeDefined();
            expect((_b = auth_1.authOptions.pages) === null || _b === void 0 ? void 0 : _b.signIn).toBe('/login');
        });
        it('should have custom error page or use default', function () {
            var _a, _b;
            // Error page is optional - if not defined, NextAuth uses default
            if ((_a = auth_1.authOptions.pages) === null || _a === void 0 ? void 0 : _a.error) {
                expect(auth_1.authOptions.pages.error).toBe('/auth/error');
            }
            else {
                // Accept that error page is not configured (uses NextAuth default)
                expect((_b = auth_1.authOptions.pages) === null || _b === void 0 ? void 0 : _b.error).toBeUndefined();
            }
        });
    });
    describe('Environment Variables', function () {
        it('should have NEXTAUTH_SECRET configured', function () {
            expect(process.env.NEXTAUTH_SECRET).toBeDefined();
            expect(process.env.NEXTAUTH_SECRET).not.toBe('');
        });
        it('should have NEXTAUTH_URL configured', function () {
            expect(process.env.NEXTAUTH_URL).toBeDefined();
            expect(process.env.NEXTAUTH_URL).not.toBe('');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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