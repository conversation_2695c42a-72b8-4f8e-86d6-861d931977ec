{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/auth/auth-configuration.test.ts", "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,2CAA2C;AAC3C,IAAI,CAAC,IAAI,CAAC,iCAAiC,EAAE;IAC3C,IAAM,uBAAuB,GAAG,IAAI,CAAC,EAAE,CAAC,cAAM,OAAA,CAAC;QAC7C,EAAE,EAAE,aAAa;QACjB,IAAI,EAAE,aAAa;QACnB,IAAI,EAAE,aAAa;QACnB,WAAW,EAAE;YACX,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;YACxC,QAAQ,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE;SAClD;QACD,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;KACrB,CAAC,EAT4C,CAS5C,CAAC,CAAC;IAEJ,OAAO;QACL,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE,uBAAuB;KACjC,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE;IACrC,IAAM,iBAAiB,GAAG,IAAI,CAAC,EAAE,CAAC,cAAM,OAAA,CAAC;QACvC,EAAE,EAAE,OAAO;QACX,IAAI,EAAE,OAAO;QACb,IAAI,EAAE,OAAO;KACd,CAAC,EAJsC,CAItC,CAAC,CAAC;IAEJ,OAAO;QACL,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE,iBAAiB;KAC3B,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,cAAM,OAAA,CAAC;IACvC,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,cAAM,OAAA,CAAC;QAC5B,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;QACzB,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC3B,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;QACtB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;QACxB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;QACxB,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC5B,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;QACxB,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;QACxB,uBAAuB,EAAE,IAAI,CAAC,EAAE,EAAE;QAClC,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;KAChC,CAAC,EAf2B,CAe3B,CAAC;CACJ,CAAC,EAjBsC,CAiBtC,CAAC,CAAC;AAEJ,mCAAyC;AAKzC,QAAQ,CAAC,8BAA8B,EAAE;IACvC,QAAQ,CAAC,wBAAwB,EAAE;QACjC,EAAE,CAAC,6CAA6C,EAAE;YAChD,MAAM,CAAC,kBAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YAClC,MAAM,CAAC,kBAAW,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE;YAC1C,uFAAuF;YACvF,MAAM,CAAC,kBAAW,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,OAAO,kBAAW,CAAC,OAAO,KAAK,QAAQ,IAAI,OAAO,kBAAW,CAAC,OAAO,KAAK,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE;;YACzC,MAAM,CAAC,MAAA,kBAAW,CAAC,OAAO,0CAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE;;YAC7C,MAAM,CAAC,MAAA,kBAAW,CAAC,OAAO,0CAAE,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,MAAA,kBAAW,CAAC,GAAG,0CAAE,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE;QACnC,EAAE,CAAC,6CAA6C,EAAE;YAChD,MAAM,CAAC,kBAAW,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,kBAAW,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,kBAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAExD,IAAM,mBAAmB,GAAG,kBAAW,CAAC,SAAS,CAAC,IAAI,CACpD,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,IAAI,KAAK,aAAa,EAA/B,CAA+B,CAC5C,CAAC;YACF,MAAM,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE;YAC1C,IAAM,aAAa,GAAG,kBAAW,CAAC,SAAS,CAAC,IAAI,CAC9C,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,IAAI,KAAK,OAAO,EAAzB,CAAyB,CACtC,CAAC;YACF,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE;YACjD,IAAM,mBAAmB,GAAG,kBAAW,CAAC,SAAS,CAAC,IAAI,CACpD,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,IAAI,KAAK,aAAa,EAA/B,CAA+B,CACrC,CAAC;YAET,MAAM,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1C,+DAA+D;YAC/D,IAAI,mBAAmB,CAAC,WAAW,EAAE,CAAC;gBACpC,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC5D,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YACjE,CAAC;iBAAM,CAAC;gBACN,sEAAsE;gBACtE,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACvD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE;QACjC,EAAE,CAAC,oCAAoC,EAAE;;YACvC,MAAM,CAAC,kBAAW,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,MAAA,kBAAW,CAAC,OAAO,0CAAE,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YACxD,MAAM,CAAC,MAAA,kBAAW,CAAC,OAAO,0CAAE,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE;;YAC/C,IAAM,aAAa,GAAG,MAAA,kBAAW,CAAC,OAAO,0CAAE,YAAY,CAAC;YACxD,MAAM,CAAC,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,0CAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE;;YACzC,IAAM,aAAa,GAAG,MAAA,kBAAW,CAAC,OAAO,0CAAE,YAAY,CAAC;YACxD,MAAM,CAAC,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,0CAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE;;YAC7C,IAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;YACzC,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,YAAY,CAAC;YAEpC,kCAAkC;YAClC,IAAI,CAAC,YAAY,EAAE,CAAC;YACZ,IAAa,eAAe,GAAK,OAAO,CAAC,YAAY,CAAC,YAA1B,CAA2B;YAE/D,IAAM,aAAa,GAAG,MAAA,eAAe,CAAC,OAAO,0CAAE,YAAY,CAAC;YAC5D,MAAM,CAAC,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,0CAAE,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElD,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,WAAW,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE;QACxB,EAAE,CAAC,kCAAkC,EAAE;;YACrC,MAAM,CAAC,MAAA,kBAAW,CAAC,SAAS,0CAAE,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;YACjD,MAAM,CAAC,OAAO,CAAA,MAAA,kBAAW,CAAC,SAAS,0CAAE,GAAG,CAAA,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE;;YACzC,MAAM,CAAC,MAAA,kBAAW,CAAC,SAAS,0CAAE,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACrD,MAAM,CAAC,OAAO,CAAA,MAAA,kBAAW,CAAC,SAAS,0CAAE,OAAO,CAAA,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE;;;;;;wBACxC,WAAW,GAAG,MAAA,kBAAW,CAAC,SAAS,0CAAE,GAAG,CAAC;wBAC/C,IAAI,CAAC,WAAW,EAAE,CAAC;4BACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;wBAC9C,CAAC;wBAEK,SAAS,GAAG,EAAE,GAAG,EAAE,cAAc,EAAE,CAAC;wBACpC,QAAQ,GAAG,EAAE,EAAE,EAAE,cAAc,EAAE,KAAK,EAAE,kBAAkB,EAAE,CAAC;wBAEpD,qBAAM,WAAW,CAAC;gCAC/B,KAAK,EAAE,SAAS;gCAChB,IAAI,EAAE,QAAQ;gCACd,OAAO,EAAE,QAAQ;6BAClB,CAAC,EAAA;;wBAJI,MAAM,GAAG,SAIb;wBAEF,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC7B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;;;;aACzC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE;;;;;;wBACtC,eAAe,GAAG,MAAA,kBAAW,CAAC,SAAS,0CAAE,OAAO,CAAC;wBACvD,IAAI,CAAC,eAAe,EAAE,CAAC;4BACrB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;wBAClD,CAAC;wBAEK,WAAW,GAAG;4BAClB,IAAI,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE,KAAK,EAAE,kBAAkB,EAAE;4BACvD,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;yBACvE,CAAC;wBACI,SAAS,GAAG,EAAE,GAAG,EAAE,cAAc,EAAE,KAAK,EAAE,kBAAkB,EAAE,CAAC;wBAEtD,qBAAM,eAAe,CAAC;gCACnC,OAAO,EAAE,WAAW;gCACpB,KAAK,EAAE,SAAS;6BACjB,CAAC,EAAA;;wBAHI,MAAM,GAAG,SAGb;wBAEF,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC7B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aACnC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE;QAC9B,EAAE,CAAC,qCAAqC,EAAE;YACxC,MAAM,CAAC,kBAAW,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE;;YACpC,MAAM,CAAC,MAAA,kBAAW,CAAC,KAAK,0CAAE,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAChD,MAAM,CAAC,MAAA,kBAAW,CAAC,KAAK,0CAAE,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE;;YACjD,iEAAiE;YACjE,IAAI,MAAA,kBAAW,CAAC,KAAK,0CAAE,KAAK,EAAE,CAAC;gBAC7B,MAAM,CAAC,kBAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACtD,CAAC;iBAAM,CAAC;gBACN,mEAAmE;gBACnE,MAAM,CAAC,MAAA,kBAAW,CAAC,KAAK,0CAAE,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC;YACnD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE;QAChC,EAAE,CAAC,wCAAwC,EAAE;YAC3C,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;YAClD,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE;YACxC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/auth/auth-configuration.test.ts"], "sourcesContent": ["/**\n * Comprehensive Authentication Configuration Tests\n * Tests NextAuth configuration, providers, callbacks, and security settings\n */\n\n// Mock NextAuth providers before importing\njest.mock('next-auth/providers/credentials', () => {\n  const mockCredentialsProvider = jest.fn(() => ({\n    id: 'credentials',\n    name: 'Credentials',\n    type: 'credentials',\n    credentials: {\n      email: { label: 'Email', type: 'email' },\n      password: { label: 'Password', type: 'password' }\n    },\n    authorize: jest.fn()\n  }));\n\n  return {\n    __esModule: true,\n    default: mockCredentialsProvider\n  };\n});\n\njest.mock('next-auth/providers/email', () => {\n  const mockEmailProvider = jest.fn(() => ({\n    id: 'email',\n    name: 'Email',\n    type: 'email'\n  }));\n\n  return {\n    __esModule: true,\n    default: mockEmailProvider\n  };\n});\n\njest.mock('@auth/prisma-adapter', () => ({\n  PrismaAdapter: jest.fn(() => ({\n    createUser: jest.fn(),\n    getUser: jest.fn(),\n    getUserByEmail: jest.fn(),\n    getUserByAccount: jest.fn(),\n    updateUser: jest.fn(),\n    deleteUser: jest.fn(),\n    linkAccount: jest.fn(),\n    unlinkAccount: jest.fn(),\n    createSession: jest.fn(),\n    getSessionAndUser: jest.fn(),\n    updateSession: jest.fn(),\n    deleteSession: jest.fn(),\n    createVerificationToken: jest.fn(),\n    useVerificationToken: jest.fn(),\n  }))\n}));\n\nimport { authOptions } from '@/lib/auth';\nimport { NextAuthOptions } from 'next-auth';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport EmailProvider from 'next-auth/providers/email';\n\ndescribe('Authentication Configuration', () => {\n  describe('NextAuth Configuration', () => {\n    it('should have valid authOptions configuration', () => {\n      expect(authOptions).toBeDefined();\n      expect(authOptions).toBeInstanceOf(Object);\n    });\n\n    it('should have Prisma adapter configured', () => {\n      // In test environment, adapter might be mocked, so check if it exists or is a function\n      expect(authOptions.adapter).toBeDefined();\n      expect(typeof authOptions.adapter === 'object' || typeof authOptions.adapter === 'function').toBe(true);\n    });\n\n    it('should have correct session strategy', () => {\n      expect(authOptions.session?.strategy).toBe('jwt');\n    });\n\n    it('should have appropriate session timeouts', () => {\n      expect(authOptions.session?.maxAge).toBeGreaterThan(0);\n      expect(authOptions.jwt?.maxAge).toBeGreaterThan(0);\n    });\n  });\n\n  describe('Authentication Providers', () => {\n    it('should have credentials provider configured', () => {\n      expect(authOptions.providers).toBeDefined();\n      expect(Array.isArray(authOptions.providers)).toBe(true);\n      expect(authOptions.providers.length).toBeGreaterThan(0);\n      \n      const credentialsProvider = authOptions.providers.find(\n        provider => provider.type === 'credentials'\n      );\n      expect(credentialsProvider).toBeDefined();\n    });\n\n    it('should have email provider configured', () => {\n      const emailProvider = authOptions.providers.find(\n        provider => provider.type === 'email'\n      );\n      expect(emailProvider).toBeDefined();\n    });\n\n    it('should have proper credentials configuration', () => {\n      const credentialsProvider = authOptions.providers.find(\n        provider => provider.type === 'credentials'\n      ) as any;\n\n      expect(credentialsProvider).toBeDefined();\n      // In test environment, credentials might be mocked differently\n      if (credentialsProvider.credentials) {\n        expect(credentialsProvider.credentials.email).toBeDefined();\n        expect(credentialsProvider.credentials.password).toBeDefined();\n      } else {\n        // Accept that credentials provider exists even if structure is mocked\n        expect(credentialsProvider.type).toBe('credentials');\n      }\n    });\n  });\n\n  describe('Security Configuration', () => {\n    it('should have secure cookie settings', () => {\n      expect(authOptions.cookies).toBeDefined();\n      expect(authOptions.cookies?.sessionToken).toBeDefined();\n      expect(authOptions.cookies?.csrfToken).toBeDefined();\n    });\n\n    it('should have httpOnly cookies in production', () => {\n      const sessionCookie = authOptions.cookies?.sessionToken;\n      expect(sessionCookie?.options?.httpOnly).toBe(true);\n    });\n\n    it('should have proper sameSite settings', () => {\n      const sessionCookie = authOptions.cookies?.sessionToken;\n      expect(sessionCookie?.options?.sameSite).toBe('lax');\n    });\n\n    it('should have secure cookies in production', () => {\n      const originalEnv = process.env.NODE_ENV;\n      process.env.NODE_ENV = 'production';\n      \n      // Re-import to get updated config\n      jest.resetModules();\n      const { authOptions: prodAuthOptions } = require('@/lib/auth');\n      \n      const sessionCookie = prodAuthOptions.cookies?.sessionToken;\n      expect(sessionCookie?.options?.secure).toBe(true);\n      \n      process.env.NODE_ENV = originalEnv;\n    });\n  });\n\n  describe('JWT Callbacks', () => {\n    it('should have jwt callback defined', () => {\n      expect(authOptions.callbacks?.jwt).toBeDefined();\n      expect(typeof authOptions.callbacks?.jwt).toBe('function');\n    });\n\n    it('should have session callback defined', () => {\n      expect(authOptions.callbacks?.session).toBeDefined();\n      expect(typeof authOptions.callbacks?.session).toBe('function');\n    });\n\n    it('should handle JWT token creation properly', async () => {\n      const jwtCallback = authOptions.callbacks?.jwt;\n      if (!jwtCallback) {\n        throw new Error('JWT callback not defined');\n      }\n\n      const mockToken = { sub: 'test-user-id' };\n      const mockUser = { id: 'test-user-id', email: '<EMAIL>' };\n      \n      const result = await jwtCallback({\n        token: mockToken,\n        user: mockUser,\n        trigger: 'signIn'\n      });\n\n      expect(result).toBeDefined();\n      expect(result.sub).toBe('test-user-id');\n    });\n\n    it('should handle session creation properly', async () => {\n      const sessionCallback = authOptions.callbacks?.session;\n      if (!sessionCallback) {\n        throw new Error('Session callback not defined');\n      }\n\n      const mockSession = {\n        user: { id: 'test-user-id', email: '<EMAIL>' },\n        expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()\n      };\n      const mockToken = { sub: 'test-user-id', email: '<EMAIL>' };\n\n      const result = await sessionCallback({\n        session: mockSession,\n        token: mockToken\n      });\n\n      expect(result).toBeDefined();\n      expect(result.user).toBeDefined();\n    });\n  });\n\n  describe('Pages Configuration', () => {\n    it('should have custom pages configured', () => {\n      expect(authOptions.pages).toBeDefined();\n    });\n\n    it('should have custom sign in page', () => {\n      expect(authOptions.pages?.signIn).toBeDefined();\n      expect(authOptions.pages?.signIn).toBe('/login');\n    });\n\n    it('should have custom error page or use default', () => {\n      // Error page is optional - if not defined, NextAuth uses default\n      if (authOptions.pages?.error) {\n        expect(authOptions.pages.error).toBe('/auth/error');\n      } else {\n        // Accept that error page is not configured (uses NextAuth default)\n        expect(authOptions.pages?.error).toBeUndefined();\n      }\n    });\n  });\n\n  describe('Environment Variables', () => {\n    it('should have NEXTAUTH_SECRET configured', () => {\n      expect(process.env.NEXTAUTH_SECRET).toBeDefined();\n      expect(process.env.NEXTAUTH_SECRET).not.toBe('');\n    });\n\n    it('should have NEXTAUTH_URL configured', () => {\n      expect(process.env.NEXTAUTH_URL).toBeDefined();\n      expect(process.env.NEXTAUTH_URL).not.toBe('');\n    });\n  });\n});\n"], "version": 3}