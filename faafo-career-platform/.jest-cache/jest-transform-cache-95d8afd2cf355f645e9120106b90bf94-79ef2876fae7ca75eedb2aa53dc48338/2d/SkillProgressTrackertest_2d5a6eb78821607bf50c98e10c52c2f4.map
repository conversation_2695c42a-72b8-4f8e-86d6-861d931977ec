{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/skills/visualizations/SkillProgressTracker.test.tsx", "mappings": ";;;;;;;;;;;;;;;;;AAKA,8BAA8B;AAC9B,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE,cAAM,OAAA,CAAC;IAC3C,QAAQ,EAAE,UAAC,EAAyB;YAAvB,KAAK,WAAA,EAAE,SAAS,eAAA;QAAY,OAAA,CACvC,+CAAiB,cAAc,gBAAa,KAAK,EAAE,SAAS,EAAE,SAAS,GAAI,CAC5E;IAFwC,CAExC;CACF,CAAC,EAJ0C,CAI1C,CAAC,CAAC;AAVJ,gDAA0B;AAC1B,gDAAwD;AACxD,qCAAmC;AACnC,iHAA2F;AAS3F,IAAM,qBAAqB,GAAG;IAC5B;QACE,EAAE,EAAE,SAAS;QACb,IAAI,EAAE,YAAY;QAClB,QAAQ,EAAE,aAAa;QACvB,YAAY,EAAE,CAAC;QACf,WAAW,EAAE,CAAC;QACd,QAAQ,EAAE,EAAE;QACZ,MAAM,EAAE,aAAsB;QAC9B,uBAAuB,EAAE,CAAC;QAC1B,QAAQ,EAAE,MAAe;QACzB,WAAW,EAAE,sBAAsB;QACnC,UAAU,EAAE;YACV;gBACE,EAAE,EAAE,aAAa;gBACjB,KAAK,EAAE,sBAAsB;gBAC7B,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE,sBAAsB;aAChC;YACD;gBACE,EAAE,EAAE,aAAa;gBACjB,KAAK,EAAE,qBAAqB;gBAC5B,SAAS,EAAE,KAAK;gBAChB,OAAO,EAAE,sBAAsB;aAChC;SACF;KACF;IACD;QACE,EAAE,EAAE,SAAS;QACb,IAAI,EAAE,OAAO;QACb,QAAQ,EAAE,UAAU;QACpB,YAAY,EAAE,CAAC;QACf,WAAW,EAAE,CAAC;QACd,QAAQ,EAAE,EAAE;QACZ,MAAM,EAAE,SAAkB;QAC1B,uBAAuB,EAAE,CAAC;QAC1B,QAAQ,EAAE,UAAmB;QAC7B,WAAW,EAAE,sBAAsB;QACnC,UAAU,EAAE;YACV;gBACE,EAAE,EAAE,aAAa;gBACjB,KAAK,EAAE,aAAa;gBACpB,SAAS,EAAE,IAAI;aAChB;YACD;gBACE,EAAE,EAAE,aAAa;gBACjB,KAAK,EAAE,kBAAkB;gBACzB,SAAS,EAAE,KAAK;aACjB;SACF;KACF;IACD;QACE,EAAE,EAAE,SAAS;QACb,IAAI,EAAE,SAAS;QACf,QAAQ,EAAE,SAAS;QACnB,YAAY,EAAE,CAAC;QACf,WAAW,EAAE,CAAC;QACd,QAAQ,EAAE,GAAG;QACb,MAAM,EAAE,WAAoB;QAC5B,uBAAuB,EAAE,CAAC;QAC1B,QAAQ,EAAE,QAAiB;QAC3B,WAAW,EAAE,sBAAsB;QACnC,UAAU,EAAE;YACV;gBACE,EAAE,EAAE,aAAa;gBACjB,KAAK,EAAE,oBAAoB;gBAC3B,SAAS,EAAE,IAAI;aAChB;SACF;KACF;CACF,CAAC;AAEF,QAAQ,CAAC,sBAAsB,EAAE;IAC/B,QAAQ,CAAC,qBAAqB,EAAE;QAC9B,EAAE,CAAC,kCAAkC,EAAE;YACrC,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,qBAAqB,GAAI,CAAC,CAAC;YAEhE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC3E,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,0DAA0D,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC3G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE;YACpD,IAAM,WAAW,GAAG,yBAAyB,CAAC;YAC9C,IAAM,iBAAiB,GAAG,gCAAgC,CAAC;YAE3D,IAAA,cAAM,EACJ,uBAAC,8BAAoB,IACnB,MAAM,EAAE,qBAAqB,EAC7B,KAAK,EAAE,WAAW,EAClB,WAAW,EAAE,iBAAiB,GAC9B,CACH,CAAC;YAEF,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC1D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAClE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE;YACxD,IAAA,cAAM,EACJ,uBAAC,8BAAoB,IACnB,MAAM,EAAE,qBAAqB,EAC7B,KAAK,EAAC,YAAY,EAClB,WAAW,EAAC,EAAE,GACd,CACH,CAAC;YAEF,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC3D,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;QAC5E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE;QACnC,EAAE,CAAC,yDAAyD,EAAE;YAC5D,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,qBAAqB,GAAI,CAAC,CAAC;YAEhE,gDAAgD;YAChD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACtD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE;YAC5C,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,qBAAqB,GAAI,CAAC,CAAC;YAEhE,4BAA4B;YAC5B,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACpD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE;YAC1C,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,qBAAqB,GAAI,CAAC,CAAC;YAEhE,wBAAwB;YACxB,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAClD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE;YACrC,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,EAAE,GAAI,CAAC,CAAC;YAE7C,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC,CAAC,mBAAmB;YACzE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC,CAAC,mBAAmB;YACxE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC,CAAC,iBAAiB;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE;QAC9B,EAAE,CAAC,oDAAoD,EAAE;YACvD,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,qBAAqB,GAAI,CAAC,CAAC;YAEhE,oBAAoB;YACpB,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC3D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACtD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAExD,0BAA0B;YAC1B,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC5D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC5D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE;YAChD,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,qBAAqB,GAAI,CAAC,CAAC;YAEhE,IAAM,YAAY,GAAG,cAAM,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC3D,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAErC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAC5D,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE;YACzC,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,qBAAqB,GAAI,CAAC,CAAC;YAEhE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC5D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACxD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE;YAC3C,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,qBAAqB,GAAI,CAAC,CAAC;YAEhE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACrD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACzD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE;YAC9C,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,qBAAqB,GAAI,CAAC,CAAC;YAEhE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC7D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC7D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE;QAC7B,EAAE,CAAC,uDAAuD,EAAE;YAC1D,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,qBAAqB,EAAE,cAAc,EAAE,IAAI,GAAI,CAAC,CAAC;YAEtF,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACrE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACpE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC5D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACjE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE;YAC/D,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,qBAAqB,EAAE,cAAc,EAAE,KAAK,GAAI,CAAC,CAAC;YAEvF,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;YAC3E,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE;YACtD,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,qBAAqB,EAAE,cAAc,EAAE,IAAI,GAAI,CAAC,CAAC;YAEtF,4BAA4B;YAC5B,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC1D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE;YAC/C,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,qBAAqB,EAAE,cAAc,EAAE,IAAI,GAAI,CAAC,CAAC;YAEtF,yDAAyD;YACzD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC5D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE;QAC5B,EAAE,CAAC,8DAA8D,EAAE;YACjE,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,qBAAqB,EAAE,eAAe,EAAE,IAAI,GAAI,CAAC,CAAC;YAEvF,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC5D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACzD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE;YACnE,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,qBAAqB,EAAE,eAAe,EAAE,KAAK,GAAI,CAAC,CAAC;YAExF,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;YAClE,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;YAC/D,MAAM,CAAC,cAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE;QACvB,EAAE,CAAC,qCAAqC,EAAE;YACxC,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,qBAAqB,GAAI,CAAC,CAAC;YAEhE,iFAAiF;YACjF,+EAA+E;YAC/E,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC3D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACtD,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE;QAC/B,EAAE,CAAC,mCAAmC,EAAE;YACtC,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,qBAAqB,GAAI,CAAC,CAAC;YAEhE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACxE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACxE,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE;QACrB,EAAE,CAAC,yCAAyC,EAAE;YAC5C,IAAM,uBAAuB,GAAG;sCAEzB,qBAAqB,CAAC,CAAC,CAAC,KAC3B,UAAU,EAAE,EAAE;aAEjB,CAAC;YAEF,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,uBAAuB,EAAE,cAAc,EAAE,IAAI,GAAI,CAAC,CAAC;YAExF,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC3D,4DAA4D;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE;YACjD,IAAM,eAAe,GAAG;sCAEjB,qBAAqB,CAAC,CAAC,CAAC,KAC3B,MAAM,EAAE,aAAsB,EAC9B,QAAQ,EAAE,CAAC;aAEd,CAAC;YAEF,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,eAAe,GAAI,CAAC,CAAC;YAE1D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE;YAC3C,IAAM,gBAAgB,GAAG;sCAElB,qBAAqB,CAAC,CAAC,CAAC,KAC3B,QAAQ,EAAE,KAAc;aAE3B,CAAC;YAEF,IAAA,cAAM,EAAC,uBAAC,8BAAoB,IAAC,MAAM,EAAE,gBAAgB,GAAI,CAAC,CAAC;YAE3D,MAAM,CAAC,cAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/components/skills/visualizations/SkillProgressTracker.test.tsx"], "sourcesContent": ["import React from 'react';\nimport { render, screen } from '@testing-library/react';\nimport '@testing-library/jest-dom';\nimport SkillProgressTracker from '@/components/skills/visualizations/SkillProgressTracker';\n\n// Mock the Progress component\njest.mock('@/components/ui/progress', () => ({\n  Progress: ({ value, className }: any) => (\n    <div data-testid=\"progress-bar\" data-value={value} className={className} />\n  ),\n}));\n\nconst mockSkillProgressData = [\n  {\n    id: 'skill-1',\n    name: 'JavaScript',\n    category: 'Programming',\n    currentLevel: 7,\n    targetLevel: 9,\n    progress: 75,\n    status: 'in_progress' as const,\n    estimatedTimeToComplete: 4,\n    priority: 'high' as const,\n    lastUpdated: '2025-06-21T10:00:00Z',\n    milestones: [\n      {\n        id: 'milestone-1',\n        title: 'Complete ES6 modules',\n        completed: true,\n        dueDate: '2025-06-15T00:00:00Z',\n      },\n      {\n        id: 'milestone-2',\n        title: 'Build React project',\n        completed: false,\n        dueDate: '2025-07-01T00:00:00Z',\n      },\n    ],\n  },\n  {\n    id: 'skill-2',\n    name: 'React',\n    category: 'Frontend',\n    currentLevel: 6,\n    targetLevel: 8,\n    progress: 50,\n    status: 'at_risk' as const,\n    estimatedTimeToComplete: 6,\n    priority: 'critical' as const,\n    lastUpdated: '2025-06-20T15:30:00Z',\n    milestones: [\n      {\n        id: 'milestone-3',\n        title: 'Learn hooks',\n        completed: true,\n      },\n      {\n        id: 'milestone-4',\n        title: 'State management',\n        completed: false,\n      },\n    ],\n  },\n  {\n    id: 'skill-3',\n    name: 'Node.js',\n    category: 'Backend',\n    currentLevel: 8,\n    targetLevel: 8,\n    progress: 100,\n    status: 'completed' as const,\n    estimatedTimeToComplete: 0,\n    priority: 'medium' as const,\n    lastUpdated: '2025-06-19T12:00:00Z',\n    milestones: [\n      {\n        id: 'milestone-5',\n        title: 'Express.js mastery',\n        completed: true,\n      },\n    ],\n  },\n];\n\ndescribe('SkillProgressTracker', () => {\n  describe('Component Rendering', () => {\n    it('should render with default props', () => {\n      render(<SkillProgressTracker skills={mockSkillProgressData} />);\n      \n      expect(screen.getByText('Skill Development Progress')).toBeInTheDocument();\n      expect(screen.getByText('Track your progress towards your skill development goals')).toBeInTheDocument();\n    });\n\n    it('should render with custom title and description', () => {\n      const customTitle = 'Custom Progress Tracker';\n      const customDescription = 'Custom description for testing';\n      \n      render(\n        <SkillProgressTracker\n          skills={mockSkillProgressData}\n          title={customTitle}\n          description={customDescription}\n        />\n      );\n      \n      expect(screen.getByText(customTitle)).toBeInTheDocument();\n      expect(screen.getByText(customDescription)).toBeInTheDocument();\n    });\n\n    it('should render without description when not provided', () => {\n      render(\n        <SkillProgressTracker\n          skills={mockSkillProgressData}\n          title=\"Test Title\"\n          description=\"\"\n        />\n      );\n      \n      expect(screen.getByText('Test Title')).toBeInTheDocument();\n      expect(screen.queryByText('Track your progress')).not.toBeInTheDocument();\n    });\n  });\n\n  describe('Overall Progress Summary', () => {\n    it('should calculate and display overall progress correctly', () => {\n      render(<SkillProgressTracker skills={mockSkillProgressData} />);\n      \n      // Overall progress: (75 + 50 + 100) / 3 = 75.0%\n      expect(screen.getByText('75.0%')).toBeInTheDocument();\n      expect(screen.getByText('Overall Progress')).toBeInTheDocument();\n    });\n\n    it('should count completed skills correctly', () => {\n      render(<SkillProgressTracker skills={mockSkillProgressData} />);\n      \n      // Only Node.js is completed\n      expect(screen.getByText('1/3')).toBeInTheDocument();\n      expect(screen.getByText('Completed Skills')).toBeInTheDocument();\n    });\n\n    it('should count at-risk skills correctly', () => {\n      render(<SkillProgressTracker skills={mockSkillProgressData} />);\n      \n      // Only React is at risk\n      expect(screen.getByText('1')).toBeInTheDocument();\n      expect(screen.getByText('At Risk')).toBeInTheDocument();\n    });\n\n    it('should handle empty skills array', () => {\n      render(<SkillProgressTracker skills={[]} />);\n      \n      expect(screen.getByText('0.0%')).toBeInTheDocument(); // Overall progress\n      expect(screen.getByText('0/0')).toBeInTheDocument(); // Completed skills\n      expect(screen.getByText('0')).toBeInTheDocument(); // At risk skills\n    });\n  });\n\n  describe('Skill Items Display', () => {\n    it('should display all skills with correct information', () => {\n      render(<SkillProgressTracker skills={mockSkillProgressData} />);\n      \n      // Check skill names\n      expect(screen.getByText('JavaScript')).toBeInTheDocument();\n      expect(screen.getByText('React')).toBeInTheDocument();\n      expect(screen.getByText('Node.js')).toBeInTheDocument();\n      \n      // Check level progression\n      expect(screen.getByText('Level 7 → 9')).toBeInTheDocument();\n      expect(screen.getByText('Level 6 → 8')).toBeInTheDocument();\n      expect(screen.getByText('Level 8 → 8')).toBeInTheDocument();\n    });\n\n    it('should display progress bars for each skill', () => {\n      render(<SkillProgressTracker skills={mockSkillProgressData} />);\n      \n      const progressBars = screen.getAllByTestId('progress-bar');\n      expect(progressBars).toHaveLength(3);\n      \n      expect(progressBars[0]).toHaveAttribute('data-value', '75');\n      expect(progressBars[1]).toHaveAttribute('data-value', '50');\n      expect(progressBars[2]).toHaveAttribute('data-value', '100');\n    });\n\n    it('should display correct status badges', () => {\n      render(<SkillProgressTracker skills={mockSkillProgressData} />);\n      \n      expect(screen.getByText('in progress')).toBeInTheDocument();\n      expect(screen.getByText('at risk')).toBeInTheDocument();\n      expect(screen.getByText('completed')).toBeInTheDocument();\n    });\n\n    it('should display correct priority badges', () => {\n      render(<SkillProgressTracker skills={mockSkillProgressData} />);\n      \n      expect(screen.getByText('high')).toBeInTheDocument();\n      expect(screen.getByText('critical')).toBeInTheDocument();\n      expect(screen.getByText('medium')).toBeInTheDocument();\n    });\n\n    it('should display estimated time to complete', () => {\n      render(<SkillProgressTracker skills={mockSkillProgressData} />);\n      \n      expect(screen.getByText('Est. 4 weeks')).toBeInTheDocument();\n      expect(screen.getByText('Est. 6 weeks')).toBeInTheDocument();\n      expect(screen.getByText('Est. 0 weeks')).toBeInTheDocument();\n    });\n  });\n\n  describe('Milestones Display', () => {\n    it('should display milestones when showMilestones is true', () => {\n      render(<SkillProgressTracker skills={mockSkillProgressData} showMilestones={true} />);\n      \n      expect(screen.getByText('Complete ES6 modules')).toBeInTheDocument();\n      expect(screen.getByText('Build React project')).toBeInTheDocument();\n      expect(screen.getByText('Learn hooks')).toBeInTheDocument();\n      expect(screen.getByText('State management')).toBeInTheDocument();\n      expect(screen.getByText('Express.js mastery')).toBeInTheDocument();\n    });\n\n    it('should not display milestones when showMilestones is false', () => {\n      render(<SkillProgressTracker skills={mockSkillProgressData} showMilestones={false} />);\n      \n      expect(screen.queryByText('Complete ES6 modules')).not.toBeInTheDocument();\n      expect(screen.queryByText('Milestones')).not.toBeInTheDocument();\n    });\n\n    it('should display milestone due dates when available', () => {\n      render(<SkillProgressTracker skills={mockSkillProgressData} showMilestones={true} />);\n      \n      // Check for formatted dates\n      expect(screen.getByText('6/15/2025')).toBeInTheDocument();\n      expect(screen.getByText('7/1/2025')).toBeInTheDocument();\n    });\n\n    it('should handle milestones without due dates', () => {\n      render(<SkillProgressTracker skills={mockSkillProgressData} showMilestones={true} />);\n      \n      // Milestones without due dates should still be displayed\n      expect(screen.getByText('Learn hooks')).toBeInTheDocument();\n      expect(screen.getByText('State management')).toBeInTheDocument();\n    });\n  });\n\n  describe('Category Grouping', () => {\n    it('should group skills by category when groupByCategory is true', () => {\n      render(<SkillProgressTracker skills={mockSkillProgressData} groupByCategory={true} />);\n      \n      expect(screen.getByText('Programming')).toBeInTheDocument();\n      expect(screen.getByText('Frontend')).toBeInTheDocument();\n      expect(screen.getByText('Backend')).toBeInTheDocument();\n    });\n\n    it('should not show category headers when groupByCategory is false', () => {\n      render(<SkillProgressTracker skills={mockSkillProgressData} groupByCategory={false} />);\n      \n      expect(screen.queryByText('Programming')).not.toBeInTheDocument();\n      expect(screen.queryByText('Frontend')).not.toBeInTheDocument();\n      expect(screen.queryByText('Backend')).not.toBeInTheDocument();\n    });\n  });\n\n  describe('Status Icons', () => {\n    it('should display correct status icons', () => {\n      render(<SkillProgressTracker skills={mockSkillProgressData} />);\n      \n      // We can't easily test the actual icons, but we can verify the component renders\n      // In a real test, you might check for specific icon classes or data attributes\n      expect(screen.getByText('JavaScript')).toBeInTheDocument();\n      expect(screen.getByText('React')).toBeInTheDocument();\n      expect(screen.getByText('Node.js')).toBeInTheDocument();\n    });\n  });\n\n  describe('Last Updated Display', () => {\n    it('should display last updated dates', () => {\n      render(<SkillProgressTracker skills={mockSkillProgressData} />);\n      \n      expect(screen.getByText('Last updated: 6/21/2025')).toBeInTheDocument();\n      expect(screen.getByText('Last updated: 6/20/2025')).toBeInTheDocument();\n      expect(screen.getByText('Last updated: 6/19/2025')).toBeInTheDocument();\n    });\n  });\n\n  describe('Edge Cases', () => {\n    it('should handle skills with no milestones', () => {\n      const skillsWithoutMilestones = [\n        {\n          ...mockSkillProgressData[0],\n          milestones: [],\n        },\n      ];\n      \n      render(<SkillProgressTracker skills={skillsWithoutMilestones} showMilestones={true} />);\n      \n      expect(screen.getByText('JavaScript')).toBeInTheDocument();\n      // Should not show milestones section if no milestones exist\n    });\n\n    it('should handle skills with not_started status', () => {\n      const notStartedSkill = [\n        {\n          ...mockSkillProgressData[0],\n          status: 'not_started' as const,\n          progress: 0,\n        },\n      ];\n      \n      render(<SkillProgressTracker skills={notStartedSkill} />);\n      \n      expect(screen.getByText('not started')).toBeInTheDocument();\n    });\n\n    it('should handle skills with low priority', () => {\n      const lowPrioritySkill = [\n        {\n          ...mockSkillProgressData[0],\n          priority: 'low' as const,\n        },\n      ];\n      \n      render(<SkillProgressTracker skills={lowPrioritySkill} />);\n      \n      expect(screen.getByText('low')).toBeInTheDocument();\n    });\n  });\n});\n"], "version": 3}