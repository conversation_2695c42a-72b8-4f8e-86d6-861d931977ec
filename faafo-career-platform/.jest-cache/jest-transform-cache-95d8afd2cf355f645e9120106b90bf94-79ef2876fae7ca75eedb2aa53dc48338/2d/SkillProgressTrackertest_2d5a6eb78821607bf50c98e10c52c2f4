82418bc0fbf0e80fc75b890d473c44ec
"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
// Mock the Progress component
jest.mock('@/components/ui/progress', function () { return ({
    Progress: function (_a) {
        var value = _a.value, className = _a.className;
        return ((0, jsx_runtime_1.jsx)("div", { "data-testid": "progress-bar", "data-value": value, className: className }));
    },
}); });
var react_1 = __importDefault(require("react"));
var react_2 = require("@testing-library/react");
require("@testing-library/jest-dom");
var SkillProgressTracker_1 = __importDefault(require("@/components/skills/visualizations/SkillProgressTracker"));
var mockSkillProgressData = [
    {
        id: 'skill-1',
        name: 'JavaScript',
        category: 'Programming',
        currentLevel: 7,
        targetLevel: 9,
        progress: 75,
        status: 'in_progress',
        estimatedTimeToComplete: 4,
        priority: 'high',
        lastUpdated: '2025-06-21T10:00:00Z',
        milestones: [
            {
                id: 'milestone-1',
                title: 'Complete ES6 modules',
                completed: true,
                dueDate: '2025-06-15T00:00:00Z',
            },
            {
                id: 'milestone-2',
                title: 'Build React project',
                completed: false,
                dueDate: '2025-07-01T00:00:00Z',
            },
        ],
    },
    {
        id: 'skill-2',
        name: 'React',
        category: 'Frontend',
        currentLevel: 6,
        targetLevel: 8,
        progress: 50,
        status: 'at_risk',
        estimatedTimeToComplete: 6,
        priority: 'critical',
        lastUpdated: '2025-06-20T15:30:00Z',
        milestones: [
            {
                id: 'milestone-3',
                title: 'Learn hooks',
                completed: true,
            },
            {
                id: 'milestone-4',
                title: 'State management',
                completed: false,
            },
        ],
    },
    {
        id: 'skill-3',
        name: 'Node.js',
        category: 'Backend',
        currentLevel: 8,
        targetLevel: 8,
        progress: 100,
        status: 'completed',
        estimatedTimeToComplete: 0,
        priority: 'medium',
        lastUpdated: '2025-06-19T12:00:00Z',
        milestones: [
            {
                id: 'milestone-5',
                title: 'Express.js mastery',
                completed: true,
            },
        ],
    },
];
describe('SkillProgressTracker', function () {
    describe('Component Rendering', function () {
        it('should render with default props', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            expect(react_2.screen.getByText('Skill Development Progress')).toBeInTheDocument();
            expect(react_2.screen.getByText('Track your progress towards your skill development goals')).toBeInTheDocument();
        });
        it('should render with custom title and description', function () {
            var customTitle = 'Custom Progress Tracker';
            var customDescription = 'Custom description for testing';
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData, title: customTitle, description: customDescription }));
            expect(react_2.screen.getByText(customTitle)).toBeInTheDocument();
            expect(react_2.screen.getByText(customDescription)).toBeInTheDocument();
        });
        it('should render without description when not provided', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData, title: "Test Title", description: "" }));
            expect(react_2.screen.getByText('Test Title')).toBeInTheDocument();
            expect(react_2.screen.queryByText('Track your progress')).not.toBeInTheDocument();
        });
    });
    describe('Overall Progress Summary', function () {
        it('should calculate and display overall progress correctly', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            // Overall progress: (75 + 50 + 100) / 3 = 75.0%
            expect(react_2.screen.getByText('75.0%')).toBeInTheDocument();
            expect(react_2.screen.getByText('Overall Progress')).toBeInTheDocument();
        });
        it('should count completed skills correctly', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            // Only Node.js is completed
            expect(react_2.screen.getByText('1/3')).toBeInTheDocument();
            expect(react_2.screen.getByText('Completed Skills')).toBeInTheDocument();
        });
        it('should count at-risk skills correctly', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            // Only React is at risk
            expect(react_2.screen.getByText('1')).toBeInTheDocument();
            expect(react_2.screen.getByText('At Risk')).toBeInTheDocument();
        });
        it('should handle empty skills array', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: [] }));
            expect(react_2.screen.getByText('0.0%')).toBeInTheDocument(); // Overall progress
            expect(react_2.screen.getByText('0/0')).toBeInTheDocument(); // Completed skills
            expect(react_2.screen.getByText('0')).toBeInTheDocument(); // At risk skills
        });
    });
    describe('Skill Items Display', function () {
        it('should display all skills with correct information', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            // Check skill names
            expect(react_2.screen.getByText('JavaScript')).toBeInTheDocument();
            expect(react_2.screen.getByText('React')).toBeInTheDocument();
            expect(react_2.screen.getByText('Node.js')).toBeInTheDocument();
            // Check level progression
            expect(react_2.screen.getByText('Level 7 → 9')).toBeInTheDocument();
            expect(react_2.screen.getByText('Level 6 → 8')).toBeInTheDocument();
            expect(react_2.screen.getByText('Level 8 → 8')).toBeInTheDocument();
        });
        it('should display progress bars for each skill', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            var progressBars = react_2.screen.getAllByTestId('progress-bar');
            expect(progressBars).toHaveLength(3);
            expect(progressBars[0]).toHaveAttribute('data-value', '75');
            expect(progressBars[1]).toHaveAttribute('data-value', '50');
            expect(progressBars[2]).toHaveAttribute('data-value', '100');
        });
        it('should display correct status badges', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            expect(react_2.screen.getByText('in progress')).toBeInTheDocument();
            expect(react_2.screen.getByText('at risk')).toBeInTheDocument();
            expect(react_2.screen.getByText('completed')).toBeInTheDocument();
        });
        it('should display correct priority badges', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            expect(react_2.screen.getByText('high')).toBeInTheDocument();
            expect(react_2.screen.getByText('critical')).toBeInTheDocument();
            expect(react_2.screen.getByText('medium')).toBeInTheDocument();
        });
        it('should display estimated time to complete', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            expect(react_2.screen.getByText('Est. 4 weeks')).toBeInTheDocument();
            expect(react_2.screen.getByText('Est. 6 weeks')).toBeInTheDocument();
            expect(react_2.screen.getByText('Est. 0 weeks')).toBeInTheDocument();
        });
    });
    describe('Milestones Display', function () {
        it('should display milestones when showMilestones is true', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData, showMilestones: true }));
            expect(react_2.screen.getByText('Complete ES6 modules')).toBeInTheDocument();
            expect(react_2.screen.getByText('Build React project')).toBeInTheDocument();
            expect(react_2.screen.getByText('Learn hooks')).toBeInTheDocument();
            expect(react_2.screen.getByText('State management')).toBeInTheDocument();
            expect(react_2.screen.getByText('Express.js mastery')).toBeInTheDocument();
        });
        it('should not display milestones when showMilestones is false', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData, showMilestones: false }));
            expect(react_2.screen.queryByText('Complete ES6 modules')).not.toBeInTheDocument();
            expect(react_2.screen.queryByText('Milestones')).not.toBeInTheDocument();
        });
        it('should display milestone due dates when available', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData, showMilestones: true }));
            // Check for formatted dates
            expect(react_2.screen.getByText('6/15/2025')).toBeInTheDocument();
            expect(react_2.screen.getByText('7/1/2025')).toBeInTheDocument();
        });
        it('should handle milestones without due dates', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData, showMilestones: true }));
            // Milestones without due dates should still be displayed
            expect(react_2.screen.getByText('Learn hooks')).toBeInTheDocument();
            expect(react_2.screen.getByText('State management')).toBeInTheDocument();
        });
    });
    describe('Category Grouping', function () {
        it('should group skills by category when groupByCategory is true', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData, groupByCategory: true }));
            expect(react_2.screen.getByText('Programming')).toBeInTheDocument();
            expect(react_2.screen.getByText('Frontend')).toBeInTheDocument();
            expect(react_2.screen.getByText('Backend')).toBeInTheDocument();
        });
        it('should not show category headers when groupByCategory is false', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData, groupByCategory: false }));
            expect(react_2.screen.queryByText('Programming')).not.toBeInTheDocument();
            expect(react_2.screen.queryByText('Frontend')).not.toBeInTheDocument();
            expect(react_2.screen.queryByText('Backend')).not.toBeInTheDocument();
        });
    });
    describe('Status Icons', function () {
        it('should display correct status icons', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            // We can't easily test the actual icons, but we can verify the component renders
            // In a real test, you might check for specific icon classes or data attributes
            expect(react_2.screen.getByText('JavaScript')).toBeInTheDocument();
            expect(react_2.screen.getByText('React')).toBeInTheDocument();
            expect(react_2.screen.getByText('Node.js')).toBeInTheDocument();
        });
    });
    describe('Last Updated Display', function () {
        it('should display last updated dates', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            expect(react_2.screen.getByText('Last updated: 6/21/2025')).toBeInTheDocument();
            expect(react_2.screen.getByText('Last updated: 6/20/2025')).toBeInTheDocument();
            expect(react_2.screen.getByText('Last updated: 6/19/2025')).toBeInTheDocument();
        });
    });
    describe('Edge Cases', function () {
        it('should handle skills with no milestones', function () {
            var skillsWithoutMilestones = [
                __assign(__assign({}, mockSkillProgressData[0]), { milestones: [] }),
            ];
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: skillsWithoutMilestones, showMilestones: true }));
            expect(react_2.screen.getByText('JavaScript')).toBeInTheDocument();
            // Should not show milestones section if no milestones exist
        });
        it('should handle skills with not_started status', function () {
            var notStartedSkill = [
                __assign(__assign({}, mockSkillProgressData[0]), { status: 'not_started', progress: 0 }),
            ];
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: notStartedSkill }));
            expect(react_2.screen.getByText('not started')).toBeInTheDocument();
        });
        it('should handle skills with low priority', function () {
            var lowPrioritySkill = [
                __assign(__assign({}, mockSkillProgressData[0]), { priority: 'low' }),
            ];
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: lowPrioritySkill }));
            expect(react_2.screen.getByText('low')).toBeInTheDocument();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiL1VzZXJzL2RkNjAvZmFhZm8vZmFhZm8vZmFhZm8tY2FyZWVyLXBsYXRmb3JtL19fdGVzdHNfXy9jb21wb25lbnRzL3NraWxscy92aXN1YWxpemF0aW9ucy9Ta2lsbFByb2dyZXNzVHJhY2tlci50ZXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtBLDhCQUE4QjtBQUM5QixJQUFJLENBQUMsSUFBSSxDQUFDLDBCQUEwQixFQUFFLGNBQU0sT0FBQSxDQUFDO0lBQzNDLFFBQVEsRUFBRSxVQUFDLEVBQXlCO1lBQXZCLEtBQUssV0FBQSxFQUFFLFNBQVMsZUFBQTtRQUFZLE9BQUEsQ0FDdkMsK0NBQWlCLGNBQWMsZ0JBQWEsS0FBSyxFQUFFLFNBQVMsRUFBRSxTQUFTLEdBQUksQ0FDNUU7SUFGd0MsQ0FFeEM7Q0FDRixDQUFDLEVBSjBDLENBSTFDLENBQUMsQ0FBQztBQVZKLGdEQUEwQjtBQUMxQixnREFBd0Q7QUFDeEQscUNBQW1DO0FBQ25DLGlIQUEyRjtBQVMzRixJQUFNLHFCQUFxQixHQUFHO0lBQzVCO1FBQ0UsRUFBRSxFQUFFLFNBQVM7UUFDYixJQUFJLEVBQUUsWUFBWTtRQUNsQixRQUFRLEVBQUUsYUFBYTtRQUN2QixZQUFZLEVBQUUsQ0FBQztRQUNmLFdBQVcsRUFBRSxDQUFDO1FBQ2QsUUFBUSxFQUFFLEVBQUU7UUFDWixNQUFNLEVBQUUsYUFBc0I7UUFDOUIsdUJBQXVCLEVBQUUsQ0FBQztRQUMxQixRQUFRLEVBQUUsTUFBZTtRQUN6QixXQUFXLEVBQUUsc0JBQXNCO1FBQ25DLFVBQVUsRUFBRTtZQUNWO2dCQUNFLEVBQUUsRUFBRSxhQUFhO2dCQUNqQixLQUFLLEVBQUUsc0JBQXNCO2dCQUM3QixTQUFTLEVBQUUsSUFBSTtnQkFDZixPQUFPLEVBQUUsc0JBQXNCO2FBQ2hDO1lBQ0Q7Z0JBQ0UsRUFBRSxFQUFFLGFBQWE7Z0JBQ2pCLEtBQUssRUFBRSxxQkFBcUI7Z0JBQzVCLFNBQVMsRUFBRSxLQUFLO2dCQUNoQixPQUFPLEVBQUUsc0JBQXNCO2FBQ2hDO1NBQ0Y7S0FDRjtJQUNEO1FBQ0UsRUFBRSxFQUFFLFNBQVM7UUFDYixJQUFJLEVBQUUsT0FBTztRQUNiLFFBQVEsRUFBRSxVQUFVO1FBQ3BCLFlBQVksRUFBRSxDQUFDO1FBQ2YsV0FBVyxFQUFFLENBQUM7UUFDZCxRQUFRLEVBQUUsRUFBRTtRQUNaLE1BQU0sRUFBRSxTQUFrQjtRQUMxQix1QkFBdUIsRUFBRSxDQUFDO1FBQzFCLFFBQVEsRUFBRSxVQUFtQjtRQUM3QixXQUFXLEVBQUUsc0JBQXNCO1FBQ25DLFVBQVUsRUFBRTtZQUNWO2dCQUNFLEVBQUUsRUFBRSxhQUFhO2dCQUNqQixLQUFLLEVBQUUsYUFBYTtnQkFDcEIsU0FBUyxFQUFFLElBQUk7YUFDaEI7WUFDRDtnQkFDRSxFQUFFLEVBQUUsYUFBYTtnQkFDakIsS0FBSyxFQUFFLGtCQUFrQjtnQkFDekIsU0FBUyxFQUFFLEtBQUs7YUFDakI7U0FDRjtLQUNGO0lBQ0Q7UUFDRSxFQUFFLEVBQUUsU0FBUztRQUNiLElBQUksRUFBRSxTQUFTO1FBQ2YsUUFBUSxFQUFFLFNBQVM7UUFDbkIsWUFBWSxFQUFFLENBQUM7UUFDZixXQUFXLEVBQUUsQ0FBQztRQUNkLFFBQVEsRUFBRSxHQUFHO1FBQ2IsTUFBTSxFQUFFLFdBQW9CO1FBQzVCLHVCQUF1QixFQUFFLENBQUM7UUFDMUIsUUFBUSxFQUFFLFFBQWlCO1FBQzNCLFdBQVcsRUFBRSxzQkFBc0I7UUFDbkMsVUFBVSxFQUFFO1lBQ1Y7Z0JBQ0UsRUFBRSxFQUFFLGFBQWE7Z0JBQ2pCLEtBQUssRUFBRSxvQkFBb0I7Z0JBQzNCLFNBQVMsRUFBRSxJQUFJO2FBQ2hCO1NBQ0Y7S0FDRjtDQUNGLENBQUM7QUFFRixRQUFRLENBQUMsc0JBQXNCLEVBQUU7SUFDL0IsUUFBUSxDQUFDLHFCQUFxQixFQUFFO1FBQzlCLEVBQUUsQ0FBQyxrQ0FBa0MsRUFBRTtZQUNyQyxJQUFBLGNBQU0sRUFBQyx1QkFBQyw4QkFBb0IsSUFBQyxNQUFNLEVBQUUscUJBQXFCLEdBQUksQ0FBQyxDQUFDO1lBRWhFLE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLDRCQUE0QixDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQzNFLE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLDBEQUEwRCxDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1FBQzNHLENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLGlEQUFpRCxFQUFFO1lBQ3BELElBQU0sV0FBVyxHQUFHLHlCQUF5QixDQUFDO1lBQzlDLElBQU0saUJBQWlCLEdBQUcsZ0NBQWdDLENBQUM7WUFFM0QsSUFBQSxjQUFNLEVBQ0osdUJBQUMsOEJBQW9CLElBQ25CLE1BQU0sRUFBRSxxQkFBcUIsRUFDN0IsS0FBSyxFQUFFLFdBQVcsRUFDbEIsV0FBVyxFQUFFLGlCQUFpQixHQUM5QixDQUNILENBQUM7WUFFRixNQUFNLENBQUMsY0FBTSxDQUFDLFNBQVMsQ0FBQyxXQUFXLENBQUMsQ0FBQyxDQUFDLGlCQUFpQixFQUFFLENBQUM7WUFDMUQsTUFBTSxDQUFDLGNBQU0sQ0FBQyxTQUFTLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxDQUFDLGlCQUFpQixFQUFFLENBQUM7UUFDbEUsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMscURBQXFELEVBQUU7WUFDeEQsSUFBQSxjQUFNLEVBQ0osdUJBQUMsOEJBQW9CLElBQ25CLE1BQU0sRUFBRSxxQkFBcUIsRUFDN0IsS0FBSyxFQUFDLFlBQVksRUFDbEIsV0FBVyxFQUFDLEVBQUUsR0FDZCxDQUNILENBQUM7WUFFRixNQUFNLENBQUMsY0FBTSxDQUFDLFNBQVMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLGlCQUFpQixFQUFFLENBQUM7WUFDM0QsTUFBTSxDQUFDLGNBQU0sQ0FBQyxXQUFXLENBQUMscUJBQXFCLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1FBQzVFLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsMEJBQTBCLEVBQUU7UUFDbkMsRUFBRSxDQUFDLHlEQUF5RCxFQUFFO1lBQzVELElBQUEsY0FBTSxFQUFDLHVCQUFDLDhCQUFvQixJQUFDLE1BQU0sRUFBRSxxQkFBcUIsR0FBSSxDQUFDLENBQUM7WUFFaEUsZ0RBQWdEO1lBQ2hELE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztZQUN0RCxNQUFNLENBQUMsY0FBTSxDQUFDLFNBQVMsQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztRQUNuRSxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyx5Q0FBeUMsRUFBRTtZQUM1QyxJQUFBLGNBQU0sRUFBQyx1QkFBQyw4QkFBb0IsSUFBQyxNQUFNLEVBQUUscUJBQXFCLEdBQUksQ0FBQyxDQUFDO1lBRWhFLDRCQUE0QjtZQUM1QixNQUFNLENBQUMsY0FBTSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLGlCQUFpQixFQUFFLENBQUM7WUFDcEQsTUFBTSxDQUFDLGNBQU0sQ0FBQyxTQUFTLENBQUMsa0JBQWtCLENBQUMsQ0FBQyxDQUFDLGlCQUFpQixFQUFFLENBQUM7UUFDbkUsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsdUNBQXVDLEVBQUU7WUFDMUMsSUFBQSxjQUFNLEVBQUMsdUJBQUMsOEJBQW9CLElBQUMsTUFBTSxFQUFFLHFCQUFxQixHQUFJLENBQUMsQ0FBQztZQUVoRSx3QkFBd0I7WUFDeEIsTUFBTSxDQUFDLGNBQU0sQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQ2xELE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztRQUMxRCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxrQ0FBa0MsRUFBRTtZQUNyQyxJQUFBLGNBQU0sRUFBQyx1QkFBQyw4QkFBb0IsSUFBQyxNQUFNLEVBQUUsRUFBRSxHQUFJLENBQUMsQ0FBQztZQUU3QyxNQUFNLENBQUMsY0FBTSxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLGlCQUFpQixFQUFFLENBQUMsQ0FBQyxtQkFBbUI7WUFDekUsTUFBTSxDQUFDLGNBQU0sQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDLENBQUMsbUJBQW1CO1lBQ3hFLE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsaUJBQWlCLEVBQUUsQ0FBQyxDQUFDLGlCQUFpQjtRQUN0RSxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLHFCQUFxQixFQUFFO1FBQzlCLEVBQUUsQ0FBQyxvREFBb0QsRUFBRTtZQUN2RCxJQUFBLGNBQU0sRUFBQyx1QkFBQyw4QkFBb0IsSUFBQyxNQUFNLEVBQUUscUJBQXFCLEdBQUksQ0FBQyxDQUFDO1lBRWhFLG9CQUFvQjtZQUNwQixNQUFNLENBQUMsY0FBTSxDQUFDLFNBQVMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLGlCQUFpQixFQUFFLENBQUM7WUFDM0QsTUFBTSxDQUFDLGNBQU0sQ0FBQyxTQUFTLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQ3RELE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztZQUV4RCwwQkFBMEI7WUFDMUIsTUFBTSxDQUFDLGNBQU0sQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQzVELE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztZQUM1RCxNQUFNLENBQUMsY0FBTSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLGlCQUFpQixFQUFFLENBQUM7UUFDOUQsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsNkNBQTZDLEVBQUU7WUFDaEQsSUFBQSxjQUFNLEVBQUMsdUJBQUMsOEJBQW9CLElBQUMsTUFBTSxFQUFFLHFCQUFxQixHQUFJLENBQUMsQ0FBQztZQUVoRSxJQUFNLFlBQVksR0FBRyxjQUFNLENBQUMsY0FBYyxDQUFDLGNBQWMsQ0FBQyxDQUFDO1lBQzNELE1BQU0sQ0FBQyxZQUFZLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFFckMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLGVBQWUsQ0FBQyxZQUFZLEVBQUUsSUFBSSxDQUFDLENBQUM7WUFDNUQsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLGVBQWUsQ0FBQyxZQUFZLEVBQUUsSUFBSSxDQUFDLENBQUM7WUFDNUQsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLGVBQWUsQ0FBQyxZQUFZLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFDL0QsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsc0NBQXNDLEVBQUU7WUFDekMsSUFBQSxjQUFNLEVBQUMsdUJBQUMsOEJBQW9CLElBQUMsTUFBTSxFQUFFLHFCQUFxQixHQUFJLENBQUMsQ0FBQztZQUVoRSxNQUFNLENBQUMsY0FBTSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLGlCQUFpQixFQUFFLENBQUM7WUFDNUQsTUFBTSxDQUFDLGNBQU0sQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQ3hELE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztRQUM1RCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyx3Q0FBd0MsRUFBRTtZQUMzQyxJQUFBLGNBQU0sRUFBQyx1QkFBQyw4QkFBb0IsSUFBQyxNQUFNLEVBQUUscUJBQXFCLEdBQUksQ0FBQyxDQUFDO1lBRWhFLE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztZQUNyRCxNQUFNLENBQUMsY0FBTSxDQUFDLFNBQVMsQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFDLGlCQUFpQixFQUFFLENBQUM7WUFDekQsTUFBTSxDQUFDLGNBQU0sQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1FBQ3pELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDJDQUEyQyxFQUFFO1lBQzlDLElBQUEsY0FBTSxFQUFDLHVCQUFDLDhCQUFvQixJQUFDLE1BQU0sRUFBRSxxQkFBcUIsR0FBSSxDQUFDLENBQUM7WUFFaEUsTUFBTSxDQUFDLGNBQU0sQ0FBQyxTQUFTLENBQUMsY0FBYyxDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQzdELE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLGNBQWMsQ0FBQyxDQUFDLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztZQUM3RCxNQUFNLENBQUMsY0FBTSxDQUFDLFNBQVMsQ0FBQyxjQUFjLENBQUMsQ0FBQyxDQUFDLGlCQUFpQixFQUFFLENBQUM7UUFDL0QsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxvQkFBb0IsRUFBRTtRQUM3QixFQUFFLENBQUMsdURBQXVELEVBQUU7WUFDMUQsSUFBQSxjQUFNLEVBQUMsdUJBQUMsOEJBQW9CLElBQUMsTUFBTSxFQUFFLHFCQUFxQixFQUFFLGNBQWMsRUFBRSxJQUFJLEdBQUksQ0FBQyxDQUFDO1lBRXRGLE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLHNCQUFzQixDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQ3JFLE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLHFCQUFxQixDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQ3BFLE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztZQUM1RCxNQUFNLENBQUMsY0FBTSxDQUFDLFNBQVMsQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztZQUNqRSxNQUFNLENBQUMsY0FBTSxDQUFDLFNBQVMsQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztRQUNyRSxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyw0REFBNEQsRUFBRTtZQUMvRCxJQUFBLGNBQU0sRUFBQyx1QkFBQyw4QkFBb0IsSUFBQyxNQUFNLEVBQUUscUJBQXFCLEVBQUUsY0FBYyxFQUFFLEtBQUssR0FBSSxDQUFDLENBQUM7WUFFdkYsTUFBTSxDQUFDLGNBQU0sQ0FBQyxXQUFXLENBQUMsc0JBQXNCLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQzNFLE1BQU0sQ0FBQyxjQUFNLENBQUMsV0FBVyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLGlCQUFpQixFQUFFLENBQUM7UUFDbkUsQ0FBQyxDQUFDLENBQUM7UUFFSCxFQUFFLENBQUMsbURBQW1ELEVBQUU7WUFDdEQsSUFBQSxjQUFNLEVBQUMsdUJBQUMsOEJBQW9CLElBQUMsTUFBTSxFQUFFLHFCQUFxQixFQUFFLGNBQWMsRUFBRSxJQUFJLEdBQUksQ0FBQyxDQUFDO1lBRXRGLDRCQUE0QjtZQUM1QixNQUFNLENBQUMsY0FBTSxDQUFDLFNBQVMsQ0FBQyxXQUFXLENBQUMsQ0FBQyxDQUFDLGlCQUFpQixFQUFFLENBQUM7WUFDMUQsTUFBTSxDQUFDLGNBQU0sQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1FBQzNELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDRDQUE0QyxFQUFFO1lBQy9DLElBQUEsY0FBTSxFQUFDLHVCQUFDLDhCQUFvQixJQUFDLE1BQU0sRUFBRSxxQkFBcUIsRUFBRSxjQUFjLEVBQUUsSUFBSSxHQUFJLENBQUMsQ0FBQztZQUV0Rix5REFBeUQ7WUFDekQsTUFBTSxDQUFDLGNBQU0sQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQzVELE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLGtCQUFrQixDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1FBQ25FLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsbUJBQW1CLEVBQUU7UUFDNUIsRUFBRSxDQUFDLDhEQUE4RCxFQUFFO1lBQ2pFLElBQUEsY0FBTSxFQUFDLHVCQUFDLDhCQUFvQixJQUFDLE1BQU0sRUFBRSxxQkFBcUIsRUFBRSxlQUFlLEVBQUUsSUFBSSxHQUFJLENBQUMsQ0FBQztZQUV2RixNQUFNLENBQUMsY0FBTSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLGlCQUFpQixFQUFFLENBQUM7WUFDNUQsTUFBTSxDQUFDLGNBQU0sQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQ3pELE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztRQUMxRCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyxnRUFBZ0UsRUFBRTtZQUNuRSxJQUFBLGNBQU0sRUFBQyx1QkFBQyw4QkFBb0IsSUFBQyxNQUFNLEVBQUUscUJBQXFCLEVBQUUsZUFBZSxFQUFFLEtBQUssR0FBSSxDQUFDLENBQUM7WUFFeEYsTUFBTSxDQUFDLGNBQU0sQ0FBQyxXQUFXLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztZQUNsRSxNQUFNLENBQUMsY0FBTSxDQUFDLFdBQVcsQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQy9ELE1BQU0sQ0FBQyxjQUFNLENBQUMsV0FBVyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLGlCQUFpQixFQUFFLENBQUM7UUFDaEUsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxjQUFjLEVBQUU7UUFDdkIsRUFBRSxDQUFDLHFDQUFxQyxFQUFFO1lBQ3hDLElBQUEsY0FBTSxFQUFDLHVCQUFDLDhCQUFvQixJQUFDLE1BQU0sRUFBRSxxQkFBcUIsR0FBSSxDQUFDLENBQUM7WUFFaEUsaUZBQWlGO1lBQ2pGLCtFQUErRTtZQUMvRSxNQUFNLENBQUMsY0FBTSxDQUFDLFNBQVMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLGlCQUFpQixFQUFFLENBQUM7WUFDM0QsTUFBTSxDQUFDLGNBQU0sQ0FBQyxTQUFTLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQ3RELE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztRQUMxRCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsUUFBUSxDQUFDLHNCQUFzQixFQUFFO1FBQy9CLEVBQUUsQ0FBQyxtQ0FBbUMsRUFBRTtZQUN0QyxJQUFBLGNBQU0sRUFBQyx1QkFBQyw4QkFBb0IsSUFBQyxNQUFNLEVBQUUscUJBQXFCLEdBQUksQ0FBQyxDQUFDO1lBRWhFLE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLHlCQUF5QixDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQ3hFLE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLHlCQUF5QixDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQ3hFLE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLHlCQUF5QixDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1FBQzFFLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxRQUFRLENBQUMsWUFBWSxFQUFFO1FBQ3JCLEVBQUUsQ0FBQyx5Q0FBeUMsRUFBRTtZQUM1QyxJQUFNLHVCQUF1QixHQUFHO3NDQUV6QixxQkFBcUIsQ0FBQyxDQUFDLENBQUMsS0FDM0IsVUFBVSxFQUFFLEVBQUU7YUFFakIsQ0FBQztZQUVGLElBQUEsY0FBTSxFQUFDLHVCQUFDLDhCQUFvQixJQUFDLE1BQU0sRUFBRSx1QkFBdUIsRUFBRSxjQUFjLEVBQUUsSUFBSSxHQUFJLENBQUMsQ0FBQztZQUV4RixNQUFNLENBQUMsY0FBTSxDQUFDLFNBQVMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLGlCQUFpQixFQUFFLENBQUM7WUFDM0QsNERBQTREO1FBQzlELENBQUMsQ0FBQyxDQUFDO1FBRUgsRUFBRSxDQUFDLDhDQUE4QyxFQUFFO1lBQ2pELElBQU0sZUFBZSxHQUFHO3NDQUVqQixxQkFBcUIsQ0FBQyxDQUFDLENBQUMsS0FDM0IsTUFBTSxFQUFFLGFBQXNCLEVBQzlCLFFBQVEsRUFBRSxDQUFDO2FBRWQsQ0FBQztZQUVGLElBQUEsY0FBTSxFQUFDLHVCQUFDLDhCQUFvQixJQUFDLE1BQU0sRUFBRSxlQUFlLEdBQUksQ0FBQyxDQUFDO1lBRTFELE1BQU0sQ0FBQyxjQUFNLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsaUJBQWlCLEVBQUUsQ0FBQztRQUM5RCxDQUFDLENBQUMsQ0FBQztRQUVILEVBQUUsQ0FBQyx3Q0FBd0MsRUFBRTtZQUMzQyxJQUFNLGdCQUFnQixHQUFHO3NDQUVsQixxQkFBcUIsQ0FBQyxDQUFDLENBQUMsS0FDM0IsUUFBUSxFQUFFLEtBQWM7YUFFM0IsQ0FBQztZQUVGLElBQUEsY0FBTSxFQUFDLHVCQUFDLDhCQUFvQixJQUFDLE1BQU0sRUFBRSxnQkFBZ0IsR0FBSSxDQUFDLENBQUM7WUFFM0QsTUFBTSxDQUFDLGNBQU0sQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1FBQ3RELENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUMsQ0FBQyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyIvVXNlcnMvZGQ2MC9mYWFmby9mYWFmby9mYWFmby1jYXJlZXItcGxhdGZvcm0vX190ZXN0c19fL2NvbXBvbmVudHMvc2tpbGxzL3Zpc3VhbGl6YXRpb25zL1NraWxsUHJvZ3Jlc3NUcmFja2VyLnRlc3QudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyByZW5kZXIsIHNjcmVlbiB9IGZyb20gJ0B0ZXN0aW5nLWxpYnJhcnkvcmVhY3QnO1xuaW1wb3J0ICdAdGVzdGluZy1saWJyYXJ5L2plc3QtZG9tJztcbmltcG9ydCBTa2lsbFByb2dyZXNzVHJhY2tlciBmcm9tICdAL2NvbXBvbmVudHMvc2tpbGxzL3Zpc3VhbGl6YXRpb25zL1NraWxsUHJvZ3Jlc3NUcmFja2VyJztcblxuLy8gTW9jayB0aGUgUHJvZ3Jlc3MgY29tcG9uZW50XG5qZXN0Lm1vY2soJ0AvY29tcG9uZW50cy91aS9wcm9ncmVzcycsICgpID0+ICh7XG4gIFByb2dyZXNzOiAoeyB2YWx1ZSwgY2xhc3NOYW1lIH06IGFueSkgPT4gKFxuICAgIDxkaXYgZGF0YS10ZXN0aWQ9XCJwcm9ncmVzcy1iYXJcIiBkYXRhLXZhbHVlPXt2YWx1ZX0gY2xhc3NOYW1lPXtjbGFzc05hbWV9IC8+XG4gICksXG59KSk7XG5cbmNvbnN0IG1vY2tTa2lsbFByb2dyZXNzRGF0YSA9IFtcbiAge1xuICAgIGlkOiAnc2tpbGwtMScsXG4gICAgbmFtZTogJ0phdmFTY3JpcHQnLFxuICAgIGNhdGVnb3J5OiAnUHJvZ3JhbW1pbmcnLFxuICAgIGN1cnJlbnRMZXZlbDogNyxcbiAgICB0YXJnZXRMZXZlbDogOSxcbiAgICBwcm9ncmVzczogNzUsXG4gICAgc3RhdHVzOiAnaW5fcHJvZ3Jlc3MnIGFzIGNvbnN0LFxuICAgIGVzdGltYXRlZFRpbWVUb0NvbXBsZXRlOiA0LFxuICAgIHByaW9yaXR5OiAnaGlnaCcgYXMgY29uc3QsXG4gICAgbGFzdFVwZGF0ZWQ6ICcyMDI1LTA2LTIxVDEwOjAwOjAwWicsXG4gICAgbWlsZXN0b25lczogW1xuICAgICAge1xuICAgICAgICBpZDogJ21pbGVzdG9uZS0xJyxcbiAgICAgICAgdGl0bGU6ICdDb21wbGV0ZSBFUzYgbW9kdWxlcycsXG4gICAgICAgIGNvbXBsZXRlZDogdHJ1ZSxcbiAgICAgICAgZHVlRGF0ZTogJzIwMjUtMDYtMTVUMDA6MDA6MDBaJyxcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnbWlsZXN0b25lLTInLFxuICAgICAgICB0aXRsZTogJ0J1aWxkIFJlYWN0IHByb2plY3QnLFxuICAgICAgICBjb21wbGV0ZWQ6IGZhbHNlLFxuICAgICAgICBkdWVEYXRlOiAnMjAyNS0wNy0wMVQwMDowMDowMFonLFxuICAgICAgfSxcbiAgICBdLFxuICB9LFxuICB7XG4gICAgaWQ6ICdza2lsbC0yJyxcbiAgICBuYW1lOiAnUmVhY3QnLFxuICAgIGNhdGVnb3J5OiAnRnJvbnRlbmQnLFxuICAgIGN1cnJlbnRMZXZlbDogNixcbiAgICB0YXJnZXRMZXZlbDogOCxcbiAgICBwcm9ncmVzczogNTAsXG4gICAgc3RhdHVzOiAnYXRfcmlzaycgYXMgY29uc3QsXG4gICAgZXN0aW1hdGVkVGltZVRvQ29tcGxldGU6IDYsXG4gICAgcHJpb3JpdHk6ICdjcml0aWNhbCcgYXMgY29uc3QsXG4gICAgbGFzdFVwZGF0ZWQ6ICcyMDI1LTA2LTIwVDE1OjMwOjAwWicsXG4gICAgbWlsZXN0b25lczogW1xuICAgICAge1xuICAgICAgICBpZDogJ21pbGVzdG9uZS0zJyxcbiAgICAgICAgdGl0bGU6ICdMZWFybiBob29rcycsXG4gICAgICAgIGNvbXBsZXRlZDogdHJ1ZSxcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnbWlsZXN0b25lLTQnLFxuICAgICAgICB0aXRsZTogJ1N0YXRlIG1hbmFnZW1lbnQnLFxuICAgICAgICBjb21wbGV0ZWQ6IGZhbHNlLFxuICAgICAgfSxcbiAgICBdLFxuICB9LFxuICB7XG4gICAgaWQ6ICdza2lsbC0zJyxcbiAgICBuYW1lOiAnTm9kZS5qcycsXG4gICAgY2F0ZWdvcnk6ICdCYWNrZW5kJyxcbiAgICBjdXJyZW50TGV2ZWw6IDgsXG4gICAgdGFyZ2V0TGV2ZWw6IDgsXG4gICAgcHJvZ3Jlc3M6IDEwMCxcbiAgICBzdGF0dXM6ICdjb21wbGV0ZWQnIGFzIGNvbnN0LFxuICAgIGVzdGltYXRlZFRpbWVUb0NvbXBsZXRlOiAwLFxuICAgIHByaW9yaXR5OiAnbWVkaXVtJyBhcyBjb25zdCxcbiAgICBsYXN0VXBkYXRlZDogJzIwMjUtMDYtMTlUMTI6MDA6MDBaJyxcbiAgICBtaWxlc3RvbmVzOiBbXG4gICAgICB7XG4gICAgICAgIGlkOiAnbWlsZXN0b25lLTUnLFxuICAgICAgICB0aXRsZTogJ0V4cHJlc3MuanMgbWFzdGVyeScsXG4gICAgICAgIGNvbXBsZXRlZDogdHJ1ZSxcbiAgICAgIH0sXG4gICAgXSxcbiAgfSxcbl07XG5cbmRlc2NyaWJlKCdTa2lsbFByb2dyZXNzVHJhY2tlcicsICgpID0+IHtcbiAgZGVzY3JpYmUoJ0NvbXBvbmVudCBSZW5kZXJpbmcnLCAoKSA9PiB7XG4gICAgaXQoJ3Nob3VsZCByZW5kZXIgd2l0aCBkZWZhdWx0IHByb3BzJywgKCkgPT4ge1xuICAgICAgcmVuZGVyKDxTa2lsbFByb2dyZXNzVHJhY2tlciBza2lsbHM9e21vY2tTa2lsbFByb2dyZXNzRGF0YX0gLz4pO1xuICAgICAgXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnU2tpbGwgRGV2ZWxvcG1lbnQgUHJvZ3Jlc3MnKSkudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdUcmFjayB5b3VyIHByb2dyZXNzIHRvd2FyZHMgeW91ciBza2lsbCBkZXZlbG9wbWVudCBnb2FscycpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCByZW5kZXIgd2l0aCBjdXN0b20gdGl0bGUgYW5kIGRlc2NyaXB0aW9uJywgKCkgPT4ge1xuICAgICAgY29uc3QgY3VzdG9tVGl0bGUgPSAnQ3VzdG9tIFByb2dyZXNzIFRyYWNrZXInO1xuICAgICAgY29uc3QgY3VzdG9tRGVzY3JpcHRpb24gPSAnQ3VzdG9tIGRlc2NyaXB0aW9uIGZvciB0ZXN0aW5nJztcbiAgICAgIFxuICAgICAgcmVuZGVyKFxuICAgICAgICA8U2tpbGxQcm9ncmVzc1RyYWNrZXJcbiAgICAgICAgICBza2lsbHM9e21vY2tTa2lsbFByb2dyZXNzRGF0YX1cbiAgICAgICAgICB0aXRsZT17Y3VzdG9tVGl0bGV9XG4gICAgICAgICAgZGVzY3JpcHRpb249e2N1c3RvbURlc2NyaXB0aW9ufVxuICAgICAgICAvPlxuICAgICAgKTtcbiAgICAgIFxuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoY3VzdG9tVGl0bGUpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoY3VzdG9tRGVzY3JpcHRpb24pKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCByZW5kZXIgd2l0aG91dCBkZXNjcmlwdGlvbiB3aGVuIG5vdCBwcm92aWRlZCcsICgpID0+IHtcbiAgICAgIHJlbmRlcihcbiAgICAgICAgPFNraWxsUHJvZ3Jlc3NUcmFja2VyXG4gICAgICAgICAgc2tpbGxzPXttb2NrU2tpbGxQcm9ncmVzc0RhdGF9XG4gICAgICAgICAgdGl0bGU9XCJUZXN0IFRpdGxlXCJcbiAgICAgICAgICBkZXNjcmlwdGlvbj1cIlwiXG4gICAgICAgIC8+XG4gICAgICApO1xuICAgICAgXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnVGVzdCBUaXRsZScpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgICAgZXhwZWN0KHNjcmVlbi5xdWVyeUJ5VGV4dCgnVHJhY2sgeW91ciBwcm9ncmVzcycpKS5ub3QudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICB9KTtcbiAgfSk7XG5cbiAgZGVzY3JpYmUoJ092ZXJhbGwgUHJvZ3Jlc3MgU3VtbWFyeScsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIGNhbGN1bGF0ZSBhbmQgZGlzcGxheSBvdmVyYWxsIHByb2dyZXNzIGNvcnJlY3RseScsICgpID0+IHtcbiAgICAgIHJlbmRlcig8U2tpbGxQcm9ncmVzc1RyYWNrZXIgc2tpbGxzPXttb2NrU2tpbGxQcm9ncmVzc0RhdGF9IC8+KTtcbiAgICAgIFxuICAgICAgLy8gT3ZlcmFsbCBwcm9ncmVzczogKDc1ICsgNTAgKyAxMDApIC8gMyA9IDc1LjAlXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnNzUuMCUnKSkudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdPdmVyYWxsIFByb2dyZXNzJykpLnRvQmVJblRoZURvY3VtZW50KCk7XG4gICAgfSk7XG5cbiAgICBpdCgnc2hvdWxkIGNvdW50IGNvbXBsZXRlZCBza2lsbHMgY29ycmVjdGx5JywgKCkgPT4ge1xuICAgICAgcmVuZGVyKDxTa2lsbFByb2dyZXNzVHJhY2tlciBza2lsbHM9e21vY2tTa2lsbFByb2dyZXNzRGF0YX0gLz4pO1xuICAgICAgXG4gICAgICAvLyBPbmx5IE5vZGUuanMgaXMgY29tcGxldGVkXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnMS8zJykpLnRvQmVJblRoZURvY3VtZW50KCk7XG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnQ29tcGxldGVkIFNraWxscycpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCBjb3VudCBhdC1yaXNrIHNraWxscyBjb3JyZWN0bHknLCAoKSA9PiB7XG4gICAgICByZW5kZXIoPFNraWxsUHJvZ3Jlc3NUcmFja2VyIHNraWxscz17bW9ja1NraWxsUHJvZ3Jlc3NEYXRhfSAvPik7XG4gICAgICBcbiAgICAgIC8vIE9ubHkgUmVhY3QgaXMgYXQgcmlza1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJzEnKSkudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdBdCBSaXNrJykpLnRvQmVJblRoZURvY3VtZW50KCk7XG4gICAgfSk7XG5cbiAgICBpdCgnc2hvdWxkIGhhbmRsZSBlbXB0eSBza2lsbHMgYXJyYXknLCAoKSA9PiB7XG4gICAgICByZW5kZXIoPFNraWxsUHJvZ3Jlc3NUcmFja2VyIHNraWxscz17W119IC8+KTtcbiAgICAgIFxuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJzAuMCUnKSkudG9CZUluVGhlRG9jdW1lbnQoKTsgLy8gT3ZlcmFsbCBwcm9ncmVzc1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJzAvMCcpKS50b0JlSW5UaGVEb2N1bWVudCgpOyAvLyBDb21wbGV0ZWQgc2tpbGxzXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnMCcpKS50b0JlSW5UaGVEb2N1bWVudCgpOyAvLyBBdCByaXNrIHNraWxsc1xuICAgIH0pO1xuICB9KTtcblxuICBkZXNjcmliZSgnU2tpbGwgSXRlbXMgRGlzcGxheScsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIGRpc3BsYXkgYWxsIHNraWxscyB3aXRoIGNvcnJlY3QgaW5mb3JtYXRpb24nLCAoKSA9PiB7XG4gICAgICByZW5kZXIoPFNraWxsUHJvZ3Jlc3NUcmFja2VyIHNraWxscz17bW9ja1NraWxsUHJvZ3Jlc3NEYXRhfSAvPik7XG4gICAgICBcbiAgICAgIC8vIENoZWNrIHNraWxsIG5hbWVzXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnSmF2YVNjcmlwdCcpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ1JlYWN0JykpLnRvQmVJblRoZURvY3VtZW50KCk7XG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnTm9kZS5qcycpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgICAgXG4gICAgICAvLyBDaGVjayBsZXZlbCBwcm9ncmVzc2lvblxuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ0xldmVsIDcg4oaSIDknKSkudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdMZXZlbCA2IOKGkiA4JykpLnRvQmVJblRoZURvY3VtZW50KCk7XG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnTGV2ZWwgOCDihpIgOCcpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCBkaXNwbGF5IHByb2dyZXNzIGJhcnMgZm9yIGVhY2ggc2tpbGwnLCAoKSA9PiB7XG4gICAgICByZW5kZXIoPFNraWxsUHJvZ3Jlc3NUcmFja2VyIHNraWxscz17bW9ja1NraWxsUHJvZ3Jlc3NEYXRhfSAvPik7XG4gICAgICBcbiAgICAgIGNvbnN0IHByb2dyZXNzQmFycyA9IHNjcmVlbi5nZXRBbGxCeVRlc3RJZCgncHJvZ3Jlc3MtYmFyJyk7XG4gICAgICBleHBlY3QocHJvZ3Jlc3NCYXJzKS50b0hhdmVMZW5ndGgoMyk7XG4gICAgICBcbiAgICAgIGV4cGVjdChwcm9ncmVzc0JhcnNbMF0pLnRvSGF2ZUF0dHJpYnV0ZSgnZGF0YS12YWx1ZScsICc3NScpO1xuICAgICAgZXhwZWN0KHByb2dyZXNzQmFyc1sxXSkudG9IYXZlQXR0cmlidXRlKCdkYXRhLXZhbHVlJywgJzUwJyk7XG4gICAgICBleHBlY3QocHJvZ3Jlc3NCYXJzWzJdKS50b0hhdmVBdHRyaWJ1dGUoJ2RhdGEtdmFsdWUnLCAnMTAwJyk7XG4gICAgfSk7XG5cbiAgICBpdCgnc2hvdWxkIGRpc3BsYXkgY29ycmVjdCBzdGF0dXMgYmFkZ2VzJywgKCkgPT4ge1xuICAgICAgcmVuZGVyKDxTa2lsbFByb2dyZXNzVHJhY2tlciBza2lsbHM9e21vY2tTa2lsbFByb2dyZXNzRGF0YX0gLz4pO1xuICAgICAgXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnaW4gcHJvZ3Jlc3MnKSkudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdhdCByaXNrJykpLnRvQmVJblRoZURvY3VtZW50KCk7XG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnY29tcGxldGVkJykpLnRvQmVJblRoZURvY3VtZW50KCk7XG4gICAgfSk7XG5cbiAgICBpdCgnc2hvdWxkIGRpc3BsYXkgY29ycmVjdCBwcmlvcml0eSBiYWRnZXMnLCAoKSA9PiB7XG4gICAgICByZW5kZXIoPFNraWxsUHJvZ3Jlc3NUcmFja2VyIHNraWxscz17bW9ja1NraWxsUHJvZ3Jlc3NEYXRhfSAvPik7XG4gICAgICBcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdoaWdoJykpLnRvQmVJblRoZURvY3VtZW50KCk7XG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnY3JpdGljYWwnKSkudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdtZWRpdW0nKSkudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgZGlzcGxheSBlc3RpbWF0ZWQgdGltZSB0byBjb21wbGV0ZScsICgpID0+IHtcbiAgICAgIHJlbmRlcig8U2tpbGxQcm9ncmVzc1RyYWNrZXIgc2tpbGxzPXttb2NrU2tpbGxQcm9ncmVzc0RhdGF9IC8+KTtcbiAgICAgIFxuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ0VzdC4gNCB3ZWVrcycpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ0VzdC4gNiB3ZWVrcycpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ0VzdC4gMCB3ZWVrcycpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgIH0pO1xuICB9KTtcblxuICBkZXNjcmliZSgnTWlsZXN0b25lcyBEaXNwbGF5JywgKCkgPT4ge1xuICAgIGl0KCdzaG91bGQgZGlzcGxheSBtaWxlc3RvbmVzIHdoZW4gc2hvd01pbGVzdG9uZXMgaXMgdHJ1ZScsICgpID0+IHtcbiAgICAgIHJlbmRlcig8U2tpbGxQcm9ncmVzc1RyYWNrZXIgc2tpbGxzPXttb2NrU2tpbGxQcm9ncmVzc0RhdGF9IHNob3dNaWxlc3RvbmVzPXt0cnVlfSAvPik7XG4gICAgICBcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdDb21wbGV0ZSBFUzYgbW9kdWxlcycpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ0J1aWxkIFJlYWN0IHByb2plY3QnKSkudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdMZWFybiBob29rcycpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ1N0YXRlIG1hbmFnZW1lbnQnKSkudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdFeHByZXNzLmpzIG1hc3RlcnknKSkudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgbm90IGRpc3BsYXkgbWlsZXN0b25lcyB3aGVuIHNob3dNaWxlc3RvbmVzIGlzIGZhbHNlJywgKCkgPT4ge1xuICAgICAgcmVuZGVyKDxTa2lsbFByb2dyZXNzVHJhY2tlciBza2lsbHM9e21vY2tTa2lsbFByb2dyZXNzRGF0YX0gc2hvd01pbGVzdG9uZXM9e2ZhbHNlfSAvPik7XG4gICAgICBcbiAgICAgIGV4cGVjdChzY3JlZW4ucXVlcnlCeVRleHQoJ0NvbXBsZXRlIEVTNiBtb2R1bGVzJykpLm5vdC50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgICAgZXhwZWN0KHNjcmVlbi5xdWVyeUJ5VGV4dCgnTWlsZXN0b25lcycpKS5ub3QudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgZGlzcGxheSBtaWxlc3RvbmUgZHVlIGRhdGVzIHdoZW4gYXZhaWxhYmxlJywgKCkgPT4ge1xuICAgICAgcmVuZGVyKDxTa2lsbFByb2dyZXNzVHJhY2tlciBza2lsbHM9e21vY2tTa2lsbFByb2dyZXNzRGF0YX0gc2hvd01pbGVzdG9uZXM9e3RydWV9IC8+KTtcbiAgICAgIFxuICAgICAgLy8gQ2hlY2sgZm9yIGZvcm1hdHRlZCBkYXRlc1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJzYvMTUvMjAyNScpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJzcvMS8yMDI1JykpLnRvQmVJblRoZURvY3VtZW50KCk7XG4gICAgfSk7XG5cbiAgICBpdCgnc2hvdWxkIGhhbmRsZSBtaWxlc3RvbmVzIHdpdGhvdXQgZHVlIGRhdGVzJywgKCkgPT4ge1xuICAgICAgcmVuZGVyKDxTa2lsbFByb2dyZXNzVHJhY2tlciBza2lsbHM9e21vY2tTa2lsbFByb2dyZXNzRGF0YX0gc2hvd01pbGVzdG9uZXM9e3RydWV9IC8+KTtcbiAgICAgIFxuICAgICAgLy8gTWlsZXN0b25lcyB3aXRob3V0IGR1ZSBkYXRlcyBzaG91bGQgc3RpbGwgYmUgZGlzcGxheWVkXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnTGVhcm4gaG9va3MnKSkudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdTdGF0ZSBtYW5hZ2VtZW50JykpLnRvQmVJblRoZURvY3VtZW50KCk7XG4gICAgfSk7XG4gIH0pO1xuXG4gIGRlc2NyaWJlKCdDYXRlZ29yeSBHcm91cGluZycsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIGdyb3VwIHNraWxscyBieSBjYXRlZ29yeSB3aGVuIGdyb3VwQnlDYXRlZ29yeSBpcyB0cnVlJywgKCkgPT4ge1xuICAgICAgcmVuZGVyKDxTa2lsbFByb2dyZXNzVHJhY2tlciBza2lsbHM9e21vY2tTa2lsbFByb2dyZXNzRGF0YX0gZ3JvdXBCeUNhdGVnb3J5PXt0cnVlfSAvPik7XG4gICAgICBcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdQcm9ncmFtbWluZycpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ0Zyb250ZW5kJykpLnRvQmVJblRoZURvY3VtZW50KCk7XG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnQmFja2VuZCcpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgIH0pO1xuXG4gICAgaXQoJ3Nob3VsZCBub3Qgc2hvdyBjYXRlZ29yeSBoZWFkZXJzIHdoZW4gZ3JvdXBCeUNhdGVnb3J5IGlzIGZhbHNlJywgKCkgPT4ge1xuICAgICAgcmVuZGVyKDxTa2lsbFByb2dyZXNzVHJhY2tlciBza2lsbHM9e21vY2tTa2lsbFByb2dyZXNzRGF0YX0gZ3JvdXBCeUNhdGVnb3J5PXtmYWxzZX0gLz4pO1xuICAgICAgXG4gICAgICBleHBlY3Qoc2NyZWVuLnF1ZXJ5QnlUZXh0KCdQcm9ncmFtbWluZycpKS5ub3QudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICAgIGV4cGVjdChzY3JlZW4ucXVlcnlCeVRleHQoJ0Zyb250ZW5kJykpLm5vdC50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgICAgZXhwZWN0KHNjcmVlbi5xdWVyeUJ5VGV4dCgnQmFja2VuZCcpKS5ub3QudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICB9KTtcbiAgfSk7XG5cbiAgZGVzY3JpYmUoJ1N0YXR1cyBJY29ucycsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIGRpc3BsYXkgY29ycmVjdCBzdGF0dXMgaWNvbnMnLCAoKSA9PiB7XG4gICAgICByZW5kZXIoPFNraWxsUHJvZ3Jlc3NUcmFja2VyIHNraWxscz17bW9ja1NraWxsUHJvZ3Jlc3NEYXRhfSAvPik7XG4gICAgICBcbiAgICAgIC8vIFdlIGNhbid0IGVhc2lseSB0ZXN0IHRoZSBhY3R1YWwgaWNvbnMsIGJ1dCB3ZSBjYW4gdmVyaWZ5IHRoZSBjb21wb25lbnQgcmVuZGVyc1xuICAgICAgLy8gSW4gYSByZWFsIHRlc3QsIHlvdSBtaWdodCBjaGVjayBmb3Igc3BlY2lmaWMgaWNvbiBjbGFzc2VzIG9yIGRhdGEgYXR0cmlidXRlc1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ0phdmFTY3JpcHQnKSkudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdSZWFjdCcpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ05vZGUuanMnKSkudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICB9KTtcbiAgfSk7XG5cbiAgZGVzY3JpYmUoJ0xhc3QgVXBkYXRlZCBEaXNwbGF5JywgKCkgPT4ge1xuICAgIGl0KCdzaG91bGQgZGlzcGxheSBsYXN0IHVwZGF0ZWQgZGF0ZXMnLCAoKSA9PiB7XG4gICAgICByZW5kZXIoPFNraWxsUHJvZ3Jlc3NUcmFja2VyIHNraWxscz17bW9ja1NraWxsUHJvZ3Jlc3NEYXRhfSAvPik7XG4gICAgICBcbiAgICAgIGV4cGVjdChzY3JlZW4uZ2V0QnlUZXh0KCdMYXN0IHVwZGF0ZWQ6IDYvMjEvMjAyNScpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ0xhc3QgdXBkYXRlZDogNi8yMC8yMDI1JykpLnRvQmVJblRoZURvY3VtZW50KCk7XG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnTGFzdCB1cGRhdGVkOiA2LzE5LzIwMjUnKSkudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICB9KTtcbiAgfSk7XG5cbiAgZGVzY3JpYmUoJ0VkZ2UgQ2FzZXMnLCAoKSA9PiB7XG4gICAgaXQoJ3Nob3VsZCBoYW5kbGUgc2tpbGxzIHdpdGggbm8gbWlsZXN0b25lcycsICgpID0+IHtcbiAgICAgIGNvbnN0IHNraWxsc1dpdGhvdXRNaWxlc3RvbmVzID0gW1xuICAgICAgICB7XG4gICAgICAgICAgLi4ubW9ja1NraWxsUHJvZ3Jlc3NEYXRhWzBdLFxuICAgICAgICAgIG1pbGVzdG9uZXM6IFtdLFxuICAgICAgICB9LFxuICAgICAgXTtcbiAgICAgIFxuICAgICAgcmVuZGVyKDxTa2lsbFByb2dyZXNzVHJhY2tlciBza2lsbHM9e3NraWxsc1dpdGhvdXRNaWxlc3RvbmVzfSBzaG93TWlsZXN0b25lcz17dHJ1ZX0gLz4pO1xuICAgICAgXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnSmF2YVNjcmlwdCcpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgICAgLy8gU2hvdWxkIG5vdCBzaG93IG1pbGVzdG9uZXMgc2VjdGlvbiBpZiBubyBtaWxlc3RvbmVzIGV4aXN0XG4gICAgfSk7XG5cbiAgICBpdCgnc2hvdWxkIGhhbmRsZSBza2lsbHMgd2l0aCBub3Rfc3RhcnRlZCBzdGF0dXMnLCAoKSA9PiB7XG4gICAgICBjb25zdCBub3RTdGFydGVkU2tpbGwgPSBbXG4gICAgICAgIHtcbiAgICAgICAgICAuLi5tb2NrU2tpbGxQcm9ncmVzc0RhdGFbMF0sXG4gICAgICAgICAgc3RhdHVzOiAnbm90X3N0YXJ0ZWQnIGFzIGNvbnN0LFxuICAgICAgICAgIHByb2dyZXNzOiAwLFxuICAgICAgICB9LFxuICAgICAgXTtcbiAgICAgIFxuICAgICAgcmVuZGVyKDxTa2lsbFByb2dyZXNzVHJhY2tlciBza2lsbHM9e25vdFN0YXJ0ZWRTa2lsbH0gLz4pO1xuICAgICAgXG4gICAgICBleHBlY3Qoc2NyZWVuLmdldEJ5VGV4dCgnbm90IHN0YXJ0ZWQnKSkudG9CZUluVGhlRG9jdW1lbnQoKTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgaGFuZGxlIHNraWxscyB3aXRoIGxvdyBwcmlvcml0eScsICgpID0+IHtcbiAgICAgIGNvbnN0IGxvd1ByaW9yaXR5U2tpbGwgPSBbXG4gICAgICAgIHtcbiAgICAgICAgICAuLi5tb2NrU2tpbGxQcm9ncmVzc0RhdGFbMF0sXG4gICAgICAgICAgcHJpb3JpdHk6ICdsb3cnIGFzIGNvbnN0LFxuICAgICAgICB9LFxuICAgICAgXTtcbiAgICAgIFxuICAgICAgcmVuZGVyKDxTa2lsbFByb2dyZXNzVHJhY2tlciBza2lsbHM9e2xvd1ByaW9yaXR5U2tpbGx9IC8+KTtcbiAgICAgIFxuICAgICAgZXhwZWN0KHNjcmVlbi5nZXRCeVRleHQoJ2xvdycpKS50b0JlSW5UaGVEb2N1bWVudCgpO1xuICAgIH0pO1xuICB9KTtcbn0pO1xuIl0sInZlcnNpb24iOjN9