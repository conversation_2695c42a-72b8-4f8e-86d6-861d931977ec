633c6df7808cbcdd424f0ac1559f9412
"use strict";

/**
 * Request Batching Service for Skills Analysis API
 * Implements intelligent request batching, deduplication, and concurrent processing
 * to optimize performance and reduce AI service calls
 */
/* istanbul ignore next */
function cov_1xfb0zeef1() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/request-batching-service.ts";
  var hash = "4e6bfeece177095d1d779e07ea639d10b8dfdcae";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/request-batching-service.ts",
    statementMap: {
      "0": {
        start: {
          line: 7,
          column: 15
        },
        end: {
          line: 17,
          column: 1
        }
      },
      "1": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 15,
          column: 6
        }
      },
      "2": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 13,
          column: 9
        }
      },
      "3": {
        start: {
          line: 9,
          column: 24
        },
        end: {
          line: 9,
          column: 25
        }
      },
      "4": {
        start: {
          line: 9,
          column: 31
        },
        end: {
          line: 9,
          column: 47
        }
      },
      "5": {
        start: {
          line: 10,
          column: 12
        },
        end: {
          line: 10,
          column: 29
        }
      },
      "6": {
        start: {
          line: 11,
          column: 12
        },
        end: {
          line: 12,
          column: 28
        }
      },
      "7": {
        start: {
          line: 11,
          column: 29
        },
        end: {
          line: 12,
          column: 28
        }
      },
      "8": {
        start: {
          line: 12,
          column: 16
        },
        end: {
          line: 12,
          column: 28
        }
      },
      "9": {
        start: {
          line: 14,
          column: 8
        },
        end: {
          line: 14,
          column: 17
        }
      },
      "10": {
        start: {
          line: 16,
          column: 4
        },
        end: {
          line: 16,
          column: 43
        }
      },
      "11": {
        start: {
          line: 18,
          column: 16
        },
        end: {
          line: 26,
          column: 1
        }
      },
      "12": {
        start: {
          line: 19,
          column: 28
        },
        end: {
          line: 19,
          column: 110
        }
      },
      "13": {
        start: {
          line: 19,
          column: 91
        },
        end: {
          line: 19,
          column: 106
        }
      },
      "14": {
        start: {
          line: 20,
          column: 4
        },
        end: {
          line: 25,
          column: 7
        }
      },
      "15": {
        start: {
          line: 21,
          column: 36
        },
        end: {
          line: 21,
          column: 97
        }
      },
      "16": {
        start: {
          line: 21,
          column: 42
        },
        end: {
          line: 21,
          column: 70
        }
      },
      "17": {
        start: {
          line: 21,
          column: 85
        },
        end: {
          line: 21,
          column: 95
        }
      },
      "18": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 100
        }
      },
      "19": {
        start: {
          line: 22,
          column: 41
        },
        end: {
          line: 22,
          column: 73
        }
      },
      "20": {
        start: {
          line: 22,
          column: 88
        },
        end: {
          line: 22,
          column: 98
        }
      },
      "21": {
        start: {
          line: 23,
          column: 32
        },
        end: {
          line: 23,
          column: 116
        }
      },
      "22": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 78
        }
      },
      "23": {
        start: {
          line: 27,
          column: 18
        },
        end: {
          line: 53,
          column: 1
        }
      },
      "24": {
        start: {
          line: 28,
          column: 12
        },
        end: {
          line: 28,
          column: 104
        }
      },
      "25": {
        start: {
          line: 28,
          column: 43
        },
        end: {
          line: 28,
          column: 68
        }
      },
      "26": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 68
        }
      },
      "27": {
        start: {
          line: 28,
          column: 69
        },
        end: {
          line: 28,
          column: 81
        }
      },
      "28": {
        start: {
          line: 28,
          column: 119
        },
        end: {
          line: 28,
          column: 196
        }
      },
      "29": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 29,
          column: 160
        }
      },
      "30": {
        start: {
          line: 29,
          column: 141
        },
        end: {
          line: 29,
          column: 153
        }
      },
      "31": {
        start: {
          line: 30,
          column: 23
        },
        end: {
          line: 30,
          column: 68
        }
      },
      "32": {
        start: {
          line: 30,
          column: 45
        },
        end: {
          line: 30,
          column: 65
        }
      },
      "33": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 70
        }
      },
      "34": {
        start: {
          line: 32,
          column: 15
        },
        end: {
          line: 32,
          column: 70
        }
      },
      "35": {
        start: {
          line: 33,
          column: 8
        },
        end: {
          line: 50,
          column: 66
        }
      },
      "36": {
        start: {
          line: 33,
          column: 50
        },
        end: {
          line: 50,
          column: 66
        }
      },
      "37": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 169
        }
      },
      "38": {
        start: {
          line: 34,
          column: 160
        },
        end: {
          line: 34,
          column: 169
        }
      },
      "39": {
        start: {
          line: 35,
          column: 12
        },
        end: {
          line: 35,
          column: 52
        }
      },
      "40": {
        start: {
          line: 35,
          column: 26
        },
        end: {
          line: 35,
          column: 52
        }
      },
      "41": {
        start: {
          line: 36,
          column: 12
        },
        end: {
          line: 48,
          column: 13
        }
      },
      "42": {
        start: {
          line: 37,
          column: 32
        },
        end: {
          line: 37,
          column: 39
        }
      },
      "43": {
        start: {
          line: 37,
          column: 40
        },
        end: {
          line: 37,
          column: 46
        }
      },
      "44": {
        start: {
          line: 38,
          column: 24
        },
        end: {
          line: 38,
          column: 34
        }
      },
      "45": {
        start: {
          line: 38,
          column: 35
        },
        end: {
          line: 38,
          column: 72
        }
      },
      "46": {
        start: {
          line: 39,
          column: 24
        },
        end: {
          line: 39,
          column: 34
        }
      },
      "47": {
        start: {
          line: 39,
          column: 35
        },
        end: {
          line: 39,
          column: 45
        }
      },
      "48": {
        start: {
          line: 39,
          column: 46
        },
        end: {
          line: 39,
          column: 55
        }
      },
      "49": {
        start: {
          line: 39,
          column: 56
        },
        end: {
          line: 39,
          column: 65
        }
      },
      "50": {
        start: {
          line: 40,
          column: 24
        },
        end: {
          line: 40,
          column: 41
        }
      },
      "51": {
        start: {
          line: 40,
          column: 42
        },
        end: {
          line: 40,
          column: 55
        }
      },
      "52": {
        start: {
          line: 40,
          column: 56
        },
        end: {
          line: 40,
          column: 65
        }
      },
      "53": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 128
        }
      },
      "54": {
        start: {
          line: 42,
          column: 110
        },
        end: {
          line: 42,
          column: 116
        }
      },
      "55": {
        start: {
          line: 42,
          column: 117
        },
        end: {
          line: 42,
          column: 126
        }
      },
      "56": {
        start: {
          line: 43,
          column: 20
        },
        end: {
          line: 43,
          column: 106
        }
      },
      "57": {
        start: {
          line: 43,
          column: 81
        },
        end: {
          line: 43,
          column: 97
        }
      },
      "58": {
        start: {
          line: 43,
          column: 98
        },
        end: {
          line: 43,
          column: 104
        }
      },
      "59": {
        start: {
          line: 44,
          column: 20
        },
        end: {
          line: 44,
          column: 89
        }
      },
      "60": {
        start: {
          line: 44,
          column: 57
        },
        end: {
          line: 44,
          column: 72
        }
      },
      "61": {
        start: {
          line: 44,
          column: 73
        },
        end: {
          line: 44,
          column: 80
        }
      },
      "62": {
        start: {
          line: 44,
          column: 81
        },
        end: {
          line: 44,
          column: 87
        }
      },
      "63": {
        start: {
          line: 45,
          column: 20
        },
        end: {
          line: 45,
          column: 87
        }
      },
      "64": {
        start: {
          line: 45,
          column: 47
        },
        end: {
          line: 45,
          column: 62
        }
      },
      "65": {
        start: {
          line: 45,
          column: 63
        },
        end: {
          line: 45,
          column: 78
        }
      },
      "66": {
        start: {
          line: 45,
          column: 79
        },
        end: {
          line: 45,
          column: 85
        }
      },
      "67": {
        start: {
          line: 46,
          column: 20
        },
        end: {
          line: 46,
          column: 42
        }
      },
      "68": {
        start: {
          line: 46,
          column: 30
        },
        end: {
          line: 46,
          column: 42
        }
      },
      "69": {
        start: {
          line: 47,
          column: 20
        },
        end: {
          line: 47,
          column: 33
        }
      },
      "70": {
        start: {
          line: 47,
          column: 34
        },
        end: {
          line: 47,
          column: 43
        }
      },
      "71": {
        start: {
          line: 49,
          column: 12
        },
        end: {
          line: 49,
          column: 39
        }
      },
      "72": {
        start: {
          line: 50,
          column: 22
        },
        end: {
          line: 50,
          column: 34
        }
      },
      "73": {
        start: {
          line: 50,
          column: 35
        },
        end: {
          line: 50,
          column: 41
        }
      },
      "74": {
        start: {
          line: 50,
          column: 54
        },
        end: {
          line: 50,
          column: 64
        }
      },
      "75": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 51,
          column: 35
        }
      },
      "76": {
        start: {
          line: 51,
          column: 23
        },
        end: {
          line: 51,
          column: 35
        }
      },
      "77": {
        start: {
          line: 51,
          column: 36
        },
        end: {
          line: 51,
          column: 89
        }
      },
      "78": {
        start: {
          line: 54,
          column: 0
        },
        end: {
          line: 54,
          column: 62
        }
      },
      "79": {
        start: {
          line: 55,
          column: 0
        },
        end: {
          line: 55,
          column: 73
        }
      },
      "80": {
        start: {
          line: 56,
          column: 35
        },
        end: {
          line: 56,
          column: 74
        }
      },
      "81": {
        start: {
          line: 57,
          column: 22
        },
        end: {
          line: 57,
          column: 48
        }
      },
      "82": {
        start: {
          line: 58,
          column: 31
        },
        end: {
          line: 58,
          column: 77
        }
      },
      "83": {
        start: {
          line: 59,
          column: 44
        },
        end: {
          line: 528,
          column: 3
        }
      },
      "84": {
        start: {
          line: 61,
          column: 20
        },
        end: {
          line: 61,
          column: 24
        }
      },
      "85": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 62,
          column: 41
        }
      },
      "86": {
        start: {
          line: 63,
          column: 8
        },
        end: {
          line: 63,
          column: 43
        }
      },
      "87": {
        start: {
          line: 64,
          column: 8
        },
        end: {
          line: 64,
          column: 37
        }
      },
      "88": {
        start: {
          line: 65,
          column: 8
        },
        end: {
          line: 71,
          column: 10
        }
      },
      "89": {
        start: {
          line: 73,
          column: 8
        },
        end: {
          line: 80,
          column: 10
        }
      },
      "90": {
        start: {
          line: 82,
          column: 8
        },
        end: {
          line: 82,
          column: 81
        }
      },
      "91": {
        start: {
          line: 82,
          column: 34
        },
        end: {
          line: 82,
          column: 70
        }
      },
      "92": {
        start: {
          line: 84,
          column: 4
        },
        end: {
          line: 89,
          column: 6
        }
      },
      "93": {
        start: {
          line: 85,
          column: 8
        },
        end: {
          line: 87,
          column: 9
        }
      },
      "94": {
        start: {
          line: 86,
          column: 12
        },
        end: {
          line: 86,
          column: 75
        }
      },
      "95": {
        start: {
          line: 88,
          column: 8
        },
        end: {
          line: 88,
          column: 47
        }
      },
      "96": {
        start: {
          line: 93,
          column: 4
        },
        end: {
          line: 143,
          column: 6
        }
      },
      "97": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 142,
          column: 11
        }
      },
      "98": {
        start: {
          line: 96,
          column: 24
        },
        end: {
          line: 96,
          column: 28
        }
      },
      "99": {
        start: {
          line: 97,
          column: 12
        },
        end: {
          line: 97,
          column: 61
        }
      },
      "100": {
        start: {
          line: 97,
          column: 39
        },
        end: {
          line: 97,
          column: 59
        }
      },
      "101": {
        start: {
          line: 98,
          column: 12
        },
        end: {
          line: 141,
          column: 15
        }
      },
      "102": {
        start: {
          line: 99,
          column: 16
        },
        end: {
          line: 140,
          column: 17
        }
      },
      "103": {
        start: {
          line: 101,
          column: 24
        },
        end: {
          line: 101,
          column: 47
        }
      },
      "104": {
        start: {
          line: 102,
          column: 24
        },
        end: {
          line: 102,
          column: 53
        }
      },
      "105": {
        start: {
          line: 103,
          column: 24
        },
        end: {
          line: 103,
          column: 86
        }
      },
      "106": {
        start: {
          line: 104,
          column: 24
        },
        end: {
          line: 104,
          column: 83
        }
      },
      "107": {
        start: {
          line: 105,
          column: 24
        },
        end: {
          line: 105,
          column: 107
        }
      },
      "108": {
        start: {
          line: 107,
          column: 24
        },
        end: {
          line: 107,
          column: 49
        }
      },
      "109": {
        start: {
          line: 108,
          column: 24
        },
        end: {
          line: 116,
          column: 25
        }
      },
      "110": {
        start: {
          line: 109,
          column: 28
        },
        end: {
          line: 109,
          column: 53
        }
      },
      "111": {
        start: {
          line: 110,
          column: 28
        },
        end: {
          line: 115,
          column: 35
        }
      },
      "112": {
        start: {
          line: 117,
          column: 24
        },
        end: {
          line: 117,
          column: 86
        }
      },
      "113": {
        start: {
          line: 117,
          column: 62
        },
        end: {
          line: 117,
          column: 86
        }
      },
      "114": {
        start: {
          line: 118,
          column: 24
        },
        end: {
          line: 118,
          column: 94
        }
      },
      "115": {
        start: {
          line: 120,
          column: 24
        },
        end: {
          line: 120,
          column: 52
        }
      },
      "116": {
        start: {
          line: 121,
          column: 24
        },
        end: {
          line: 124,
          column: 25
        }
      },
      "117": {
        start: {
          line: 122,
          column: 28
        },
        end: {
          line: 122,
          column: 64
        }
      },
      "118": {
        start: {
          line: 123,
          column: 28
        },
        end: {
          line: 123,
          column: 67
        }
      },
      "119": {
        start: {
          line: 125,
          column: 24
        },
        end: {
          line: 125,
          column: 37
        }
      },
      "120": {
        start: {
          line: 128,
          column: 20
        },
        end: {
          line: 139,
          column: 28
        }
      },
      "121": {
        start: {
          line: 129,
          column: 47
        },
        end: {
          line: 137,
          column: 29
        }
      },
      "122": {
        start: {
          line: 138,
          column: 28
        },
        end: {
          line: 138,
          column: 77
        }
      },
      "123": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 171,
          column: 6
        }
      },
      "124": {
        start: {
          line: 148,
          column: 20
        },
        end: {
          line: 148,
          column: 24
        }
      },
      "125": {
        start: {
          line: 149,
          column: 8
        },
        end: {
          line: 151,
          column: 9
        }
      },
      "126": {
        start: {
          line: 150,
          column: 12
        },
        end: {
          line: 150,
          column: 52
        }
      },
      "127": {
        start: {
          line: 152,
          column: 20
        },
        end: {
          line: 152,
          column: 55
        }
      },
      "128": {
        start: {
          line: 153,
          column: 8
        },
        end: {
          line: 153,
          column: 28
        }
      },
      "129": {
        start: {
          line: 154,
          column: 8
        },
        end: {
          line: 154,
          column: 39
        }
      },
      "130": {
        start: {
          line: 156,
          column: 8
        },
        end: {
          line: 159,
          column: 11
        }
      },
      "131": {
        start: {
          line: 157,
          column: 32
        },
        end: {
          line: 157,
          column: 62
        }
      },
      "132": {
        start: {
          line: 158,
          column: 12
        },
        end: {
          line: 158,
          column: 73
        }
      },
      "133": {
        start: {
          line: 161,
          column: 8
        },
        end: {
          line: 170,
          column: 9
        }
      },
      "134": {
        start: {
          line: 162,
          column: 12
        },
        end: {
          line: 162,
          column: 41
        }
      },
      "135": {
        start: {
          line: 164,
          column: 13
        },
        end: {
          line: 170,
          column: 9
        }
      },
      "136": {
        start: {
          line: 166,
          column: 24
        },
        end: {
          line: 168,
          column: 42
        }
      },
      "137": {
        start: {
          line: 167,
          column: 16
        },
        end: {
          line: 167,
          column: 46
        }
      },
      "138": {
        start: {
          line: 169,
          column: 12
        },
        end: {
          line: 169,
          column: 51
        }
      },
      "139": {
        start: {
          line: 175,
          column: 4
        },
        end: {
          line: 226,
          column: 6
        }
      },
      "140": {
        start: {
          line: 176,
          column: 8
        },
        end: {
          line: 225,
          column: 11
        }
      },
      "141": {
        start: {
          line: 178,
          column: 12
        },
        end: {
          line: 224,
          column: 15
        }
      },
      "142": {
        start: {
          line: 179,
          column: 16
        },
        end: {
          line: 223,
          column: 17
        }
      },
      "143": {
        start: {
          line: 181,
          column: 24
        },
        end: {
          line: 181,
          column: 68
        }
      },
      "144": {
        start: {
          line: 182,
          column: 24
        },
        end: {
          line: 184,
          column: 25
        }
      },
      "145": {
        start: {
          line: 183,
          column: 28
        },
        end: {
          line: 183,
          column: 50
        }
      },
      "146": {
        start: {
          line: 186,
          column: 24
        },
        end: {
          line: 186,
          column: 62
        }
      },
      "147": {
        start: {
          line: 187,
          column: 24
        },
        end: {
          line: 187,
          column: 63
        }
      },
      "148": {
        start: {
          line: 188,
          column: 24
        },
        end: {
          line: 188,
          column: 64
        }
      },
      "149": {
        start: {
          line: 189,
          column: 24
        },
        end: {
          line: 192,
          column: 25
        }
      },
      "150": {
        start: {
          line: 190,
          column: 28
        },
        end: {
          line: 190,
          column: 48
        }
      },
      "151": {
        start: {
          line: 191,
          column: 28
        },
        end: {
          line: 191,
          column: 63
        }
      },
      "152": {
        start: {
          line: 193,
          column: 24
        },
        end: {
          line: 193,
          column: 57
        }
      },
      "153": {
        start: {
          line: 194,
          column: 24
        },
        end: {
          line: 194,
          column: 47
        }
      },
      "154": {
        start: {
          line: 195,
          column: 24
        },
        end: {
          line: 195,
          column: 37
        }
      },
      "155": {
        start: {
          line: 197,
          column: 24
        },
        end: {
          line: 197,
          column: 51
        }
      },
      "156": {
        start: {
          line: 198,
          column: 24
        },
        end: {
          line: 198,
          column: 115
        }
      },
      "157": {
        start: {
          line: 198,
          column: 91
        },
        end: {
          line: 198,
          column: 115
        }
      },
      "158": {
        start: {
          line: 199,
          column: 24
        },
        end: {
          line: 199,
          column: 90
        }
      },
      "159": {
        start: {
          line: 201,
          column: 24
        },
        end: {
          line: 201,
          column: 34
        }
      },
      "160": {
        start: {
          line: 202,
          column: 24
        },
        end: {
          line: 202,
          column: 48
        }
      },
      "161": {
        start: {
          line: 203,
          column: 28
        },
        end: {
          line: 203,
          column: 94
        }
      },
      "162": {
        start: {
          line: 205,
          column: 24
        },
        end: {
          line: 205,
          column: 34
        }
      },
      "163": {
        start: {
          line: 206,
          column: 24
        },
        end: {
          line: 206,
          column: 37
        }
      },
      "164": {
        start: {
          line: 208,
          column: 24
        },
        end: {
          line: 208,
          column: 64
        }
      },
      "165": {
        start: {
          line: 209,
          column: 24
        },
        end: {
          line: 209,
          column: 110
        }
      },
      "166": {
        start: {
          line: 210,
          column: 24
        },
        end: {
          line: 210,
          column: 48
        }
      },
      "167": {
        start: {
          line: 212,
          column: 24
        },
        end: {
          line: 212,
          column: 44
        }
      },
      "168": {
        start: {
          line: 213,
          column: 24
        },
        end: {
          line: 213,
          column: 112
        }
      },
      "169": {
        start: {
          line: 215,
          column: 24
        },
        end: {
          line: 217,
          column: 27
        }
      },
      "170": {
        start: {
          line: 216,
          column: 28
        },
        end: {
          line: 216,
          column: 99
        }
      },
      "171": {
        start: {
          line: 218,
          column: 24
        },
        end: {
          line: 218,
          column: 48
        }
      },
      "172": {
        start: {
          line: 220,
          column: 24
        },
        end: {
          line: 220,
          column: 65
        }
      },
      "173": {
        start: {
          line: 221,
          column: 24
        },
        end: {
          line: 221,
          column: 50
        }
      },
      "174": {
        start: {
          line: 222,
          column: 28
        },
        end: {
          line: 222,
          column: 50
        }
      },
      "175": {
        start: {
          line: 230,
          column: 4
        },
        end: {
          line: 263,
          column: 6
        }
      },
      "176": {
        start: {
          line: 231,
          column: 8
        },
        end: {
          line: 262,
          column: 11
        }
      },
      "177": {
        start: {
          line: 233,
          column: 24
        },
        end: {
          line: 233,
          column: 28
        }
      },
      "178": {
        start: {
          line: 234,
          column: 12
        },
        end: {
          line: 261,
          column: 15
        }
      },
      "179": {
        start: {
          line: 235,
          column: 16
        },
        end: {
          line: 260,
          column: 17
        }
      },
      "180": {
        start: {
          line: 237,
          column: 24
        },
        end: {
          line: 255,
          column: 31
        }
      },
      "181": {
        start: {
          line: 237,
          column: 76
        },
        end: {
          line: 255,
          column: 27
        }
      },
      "182": {
        start: {
          line: 239,
          column: 28
        },
        end: {
          line: 254,
          column: 31
        }
      },
      "183": {
        start: {
          line: 240,
          column: 32
        },
        end: {
          line: 253,
          column: 33
        }
      },
      "184": {
        start: {
          line: 242,
          column: 40
        },
        end: {
          line: 242,
          column: 66
        }
      },
      "185": {
        start: {
          line: 243,
          column: 40
        },
        end: {
          line: 243,
          column: 110
        }
      },
      "186": {
        start: {
          line: 245,
          column: 40
        },
        end: {
          line: 245,
          column: 59
        }
      },
      "187": {
        start: {
          line: 246,
          column: 40
        },
        end: {
          line: 246,
          column: 64
        }
      },
      "188": {
        start: {
          line: 247,
          column: 40
        },
        end: {
          line: 247,
          column: 64
        }
      },
      "189": {
        start: {
          line: 249,
          column: 40
        },
        end: {
          line: 249,
          column: 60
        }
      },
      "190": {
        start: {
          line: 250,
          column: 40
        },
        end: {
          line: 250,
          column: 64
        }
      },
      "191": {
        start: {
          line: 251,
          column: 40
        },
        end: {
          line: 251,
          column: 64
        }
      },
      "192": {
        start: {
          line: 252,
          column: 44
        },
        end: {
          line: 252,
          column: 66
        }
      },
      "193": {
        start: {
          line: 256,
          column: 24
        },
        end: {
          line: 256,
          column: 85
        }
      },
      "194": {
        start: {
          line: 258,
          column: 24
        },
        end: {
          line: 258,
          column: 34
        }
      },
      "195": {
        start: {
          line: 259,
          column: 24
        },
        end: {
          line: 259,
          column: 46
        }
      },
      "196": {
        start: {
          line: 267,
          column: 4
        },
        end: {
          line: 297,
          column: 6
        }
      },
      "197": {
        start: {
          line: 268,
          column: 8
        },
        end: {
          line: 296,
          column: 11
        }
      },
      "198": {
        start: {
          line: 270,
          column: 12
        },
        end: {
          line: 295,
          column: 15
        }
      },
      "199": {
        start: {
          line: 271,
          column: 16
        },
        end: {
          line: 294,
          column: 17
        }
      },
      "200": {
        start: {
          line: 273,
          column: 24
        },
        end: {
          line: 273,
          column: 48
        }
      },
      "201": {
        start: {
          line: 274,
          column: 24
        },
        end: {
          line: 274,
          column: 37
        }
      },
      "202": {
        start: {
          line: 276,
          column: 24
        },
        end: {
          line: 276,
          column: 76
        }
      },
      "203": {
        start: {
          line: 276,
          column: 52
        },
        end: {
          line: 276,
          column: 76
        }
      },
      "204": {
        start: {
          line: 277,
          column: 24
        },
        end: {
          line: 277,
          column: 46
        }
      },
      "205": {
        start: {
          line: 278,
          column: 24
        },
        end: {
          line: 278,
          column: 37
        }
      },
      "206": {
        start: {
          line: 280,
          column: 24
        },
        end: {
          line: 280,
          column: 50
        }
      },
      "207": {
        start: {
          line: 281,
          column: 24
        },
        end: {
          line: 281,
          column: 94
        }
      },
      "208": {
        start: {
          line: 283,
          column: 24
        },
        end: {
          line: 283,
          column: 43
        }
      },
      "209": {
        start: {
          line: 284,
          column: 24
        },
        end: {
          line: 284,
          column: 48
        }
      },
      "210": {
        start: {
          line: 285,
          column: 24
        },
        end: {
          line: 285,
          column: 48
        }
      },
      "211": {
        start: {
          line: 287,
          column: 24
        },
        end: {
          line: 287,
          column: 44
        }
      },
      "212": {
        start: {
          line: 288,
          column: 24
        },
        end: {
          line: 288,
          column: 48
        }
      },
      "213": {
        start: {
          line: 289,
          column: 24
        },
        end: {
          line: 289,
          column: 48
        }
      },
      "214": {
        start: {
          line: 291,
          column: 24
        },
        end: {
          line: 291,
          column: 29
        }
      },
      "215": {
        start: {
          line: 292,
          column: 24
        },
        end: {
          line: 292,
          column: 48
        }
      },
      "216": {
        start: {
          line: 293,
          column: 28
        },
        end: {
          line: 293,
          column: 50
        }
      },
      "217": {
        start: {
          line: 301,
          column: 4
        },
        end: {
          line: 367,
          column: 6
        }
      },
      "218": {
        start: {
          line: 302,
          column: 8
        },
        end: {
          line: 366,
          column: 11
        }
      },
      "219": {
        start: {
          line: 305,
          column: 12
        },
        end: {
          line: 365,
          column: 15
        }
      },
      "220": {
        start: {
          line: 306,
          column: 16
        },
        end: {
          line: 364,
          column: 17
        }
      },
      "221": {
        start: {
          line: 308,
          column: 24
        },
        end: {
          line: 308,
          column: 47
        }
      },
      "222": {
        start: {
          line: 309,
          column: 24
        },
        end: {
          line: 309,
          column: 37
        }
      },
      "223": {
        start: {
          line: 311,
          column: 24
        },
        end: {
          line: 311,
          column: 50
        }
      },
      "224": {
        start: {
          line: 312,
          column: 24
        },
        end: {
          line: 324,
          column: 32
        }
      },
      "225": {
        start: {
          line: 314,
          column: 152
        },
        end: {
          line: 318,
          column: 35
        }
      },
      "226": {
        start: {
          line: 326,
          column: 24
        },
        end: {
          line: 326,
          column: 51
        }
      },
      "227": {
        start: {
          line: 327,
          column: 24
        },
        end: {
          line: 327,
          column: 48
        }
      },
      "228": {
        start: {
          line: 328,
          column: 24
        },
        end: {
          line: 328,
          column: 77
        }
      },
      "229": {
        start: {
          line: 328,
          column: 53
        },
        end: {
          line: 328,
          column: 77
        }
      },
      "230": {
        start: {
          line: 329,
          column: 24
        },
        end: {
          line: 329,
          column: 86
        }
      },
      "231": {
        start: {
          line: 330,
          column: 24
        },
        end: {
          line: 330,
          column: 48
        }
      },
      "232": {
        start: {
          line: 331,
          column: 28
        },
        end: {
          line: 332,
          column: 41
        }
      },
      "233": {
        start: {
          line: 335,
          column: 24
        },
        end: {
          line: 335,
          column: 51
        }
      },
      "234": {
        start: {
          line: 336,
          column: 24
        },
        end: {
          line: 336,
          column: 37
        }
      },
      "235": {
        start: {
          line: 338,
          column: 24
        },
        end: {
          line: 340,
          column: 25
        }
      },
      "236": {
        start: {
          line: 339,
          column: 28
        },
        end: {
          line: 339,
          column: 87
        }
      },
      "237": {
        start: {
          line: 341,
          column: 24
        },
        end: {
          line: 341,
          column: 94
        }
      },
      "238": {
        start: {
          line: 342,
          column: 24
        },
        end: {
          line: 342,
          column: 91
        }
      },
      "239": {
        start: {
          line: 343,
          column: 24
        },
        end: {
          line: 346,
          column: 32
        }
      },
      "240": {
        start: {
          line: 348,
          column: 24
        },
        end: {
          line: 348,
          column: 34
        }
      },
      "241": {
        start: {
          line: 349,
          column: 24
        },
        end: {
          line: 354,
          column: 31
        }
      },
      "242": {
        start: {
          line: 356,
          column: 24
        },
        end: {
          line: 356,
          column: 44
        }
      },
      "243": {
        start: {
          line: 357,
          column: 24
        },
        end: {
          line: 362,
          column: 31
        }
      },
      "244": {
        start: {
          line: 363,
          column: 28
        },
        end: {
          line: 363,
          column: 50
        }
      },
      "245": {
        start: {
          line: 371,
          column: 4
        },
        end: {
          line: 380,
          column: 6
        }
      },
      "246": {
        start: {
          line: 373,
          column: 18
        },
        end: {
          line: 378,
          column: 9
        }
      },
      "247": {
        start: {
          line: 379,
          column: 8
        },
        end: {
          line: 379,
          column: 80
        }
      },
      "248": {
        start: {
          line: 384,
          column: 4
        },
        end: {
          line: 386,
          column: 6
        }
      },
      "249": {
        start: {
          line: 385,
          column: 8
        },
        end: {
          line: 385,
          column: 71
        }
      },
      "250": {
        start: {
          line: 390,
          column: 4
        },
        end: {
          line: 402,
          column: 6
        }
      },
      "251": {
        start: {
          line: 391,
          column: 27
        },
        end: {
          line: 394,
          column: 22
        }
      },
      "252": {
        start: {
          line: 392,
          column: 36
        },
        end: {
          line: 392,
          column: 100
        }
      },
      "253": {
        start: {
          line: 395,
          column: 19
        },
        end: {
          line: 395,
          column: 20
        }
      },
      "254": {
        start: {
          line: 396,
          column: 8
        },
        end: {
          line: 400,
          column: 9
        }
      },
      "255": {
        start: {
          line: 396,
          column: 21
        },
        end: {
          line: 396,
          column: 22
        }
      },
      "256": {
        start: {
          line: 397,
          column: 23
        },
        end: {
          line: 397,
          column: 49
        }
      },
      "257": {
        start: {
          line: 398,
          column: 12
        },
        end: {
          line: 398,
          column: 47
        }
      },
      "258": {
        start: {
          line: 399,
          column: 12
        },
        end: {
          line: 399,
          column: 31
        }
      },
      "259": {
        start: {
          line: 401,
          column: 8
        },
        end: {
          line: 401,
          column: 43
        }
      },
      "260": {
        start: {
          line: 406,
          column: 4
        },
        end: {
          line: 438,
          column: 6
        }
      },
      "261": {
        start: {
          line: 407,
          column: 8
        },
        end: {
          line: 437,
          column: 11
        }
      },
      "262": {
        start: {
          line: 408,
          column: 24
        },
        end: {
          line: 408,
          column: 28
        }
      },
      "263": {
        start: {
          line: 409,
          column: 12
        },
        end: {
          line: 436,
          column: 15
        }
      },
      "264": {
        start: {
          line: 411,
          column: 16
        },
        end: {
          line: 434,
          column: 17
        }
      },
      "265": {
        start: {
          line: 413,
          column: 20
        },
        end: {
          line: 433,
          column: 28
        }
      },
      "266": {
        start: {
          line: 414,
          column: 48
        },
        end: {
          line: 432,
          column: 35
        }
      },
      "267": {
        start: {
          line: 415,
          column: 32
        },
        end: {
          line: 431,
          column: 33
        }
      },
      "268": {
        start: {
          line: 416,
          column: 36
        },
        end: {
          line: 416,
          column: 65
        }
      },
      "269": {
        start: {
          line: 418,
          column: 51
        },
        end: {
          line: 418,
          column: 94
        }
      },
      "270": {
        start: {
          line: 419,
          column: 36
        },
        end: {
          line: 430,
          column: 39
        }
      },
      "271": {
        start: {
          line: 420,
          column: 40
        },
        end: {
          line: 429,
          column: 41
        }
      },
      "272": {
        start: {
          line: 421,
          column: 44
        },
        end: {
          line: 425,
          column: 47
        }
      },
      "273": {
        start: {
          line: 428,
          column: 44
        },
        end: {
          line: 428,
          column: 58
        }
      },
      "274": {
        start: {
          line: 435,
          column: 16
        },
        end: {
          line: 435,
          column: 44
        }
      },
      "275": {
        start: {
          line: 442,
          column: 4
        },
        end: {
          line: 444,
          column: 6
        }
      },
      "276": {
        start: {
          line: 443,
          column: 8
        },
        end: {
          line: 443,
          column: 94
        }
      },
      "277": {
        start: {
          line: 445,
          column: 4
        },
        end: {
          line: 447,
          column: 6
        }
      },
      "278": {
        start: {
          line: 446,
          column: 8
        },
        end: {
          line: 446,
          column: 96
        }
      },
      "279": {
        start: {
          line: 448,
          column: 4
        },
        end: {
          line: 456,
          column: 6
        }
      },
      "280": {
        start: {
          line: 449,
          column: 8
        },
        end: {
          line: 455,
          column: 9
        }
      },
      "281": {
        start: {
          line: 450,
          column: 33
        },
        end: {
          line: 450,
          column: 42
        }
      },
      "282": {
        start: {
          line: 451,
          column: 31
        },
        end: {
          line: 451,
          column: 40
        }
      },
      "283": {
        start: {
          line: 452,
          column: 29
        },
        end: {
          line: 452,
          column: 39
        }
      },
      "284": {
        start: {
          line: 453,
          column: 30
        },
        end: {
          line: 453,
          column: 40
        }
      },
      "285": {
        start: {
          line: 454,
          column: 21
        },
        end: {
          line: 454,
          column: 31
        }
      },
      "286": {
        start: {
          line: 457,
          column: 4
        },
        end: {
          line: 464,
          column: 6
        }
      },
      "287": {
        start: {
          line: 458,
          column: 8
        },
        end: {
          line: 463,
          column: 9
        }
      },
      "288": {
        start: {
          line: 459,
          column: 25
        },
        end: {
          line: 459,
          column: 34
        }
      },
      "289": {
        start: {
          line: 460,
          column: 29
        },
        end: {
          line: 460,
          column: 40
        }
      },
      "290": {
        start: {
          line: 461,
          column: 25
        },
        end: {
          line: 461,
          column: 37
        }
      },
      "291": {
        start: {
          line: 462,
          column: 21
        },
        end: {
          line: 462,
          column: 32
        }
      },
      "292": {
        start: {
          line: 468,
          column: 4
        },
        end: {
          line: 495,
          column: 6
        }
      },
      "293": {
        start: {
          line: 469,
          column: 20
        },
        end: {
          line: 469,
          column: 24
        }
      },
      "294": {
        start: {
          line: 470,
          column: 18
        },
        end: {
          line: 470,
          column: 28
        }
      },
      "295": {
        start: {
          line: 471,
          column: 21
        },
        end: {
          line: 471,
          column: 46
        }
      },
      "296": {
        start: {
          line: 472,
          column: 8
        },
        end: {
          line: 494,
          column: 11
        }
      },
      "297": {
        start: {
          line: 473,
          column: 28
        },
        end: {
          line: 473,
          column: 33
        }
      },
      "298": {
        start: {
          line: 473,
          column: 43
        },
        end: {
          line: 473,
          column: 48
        }
      },
      "299": {
        start: {
          line: 474,
          column: 29
        },
        end: {
          line: 474,
          column: 106
        }
      },
      "300": {
        start: {
          line: 474,
          column: 63
        },
        end: {
          line: 474,
          column: 103
        }
      },
      "301": {
        start: {
          line: 475,
          column: 12
        },
        end: {
          line: 493,
          column: 13
        }
      },
      "302": {
        start: {
          line: 477,
          column: 16
        },
        end: {
          line: 479,
          column: 19
        }
      },
      "303": {
        start: {
          line: 478,
          column: 20
        },
        end: {
          line: 478,
          column: 94
        }
      },
      "304": {
        start: {
          line: 481,
          column: 33
        },
        end: {
          line: 481,
          column: 111
        }
      },
      "305": {
        start: {
          line: 481,
          column: 67
        },
        end: {
          line: 481,
          column: 108
        }
      },
      "306": {
        start: {
          line: 482,
          column: 16
        },
        end: {
          line: 492,
          column: 17
        }
      },
      "307": {
        start: {
          line: 483,
          column: 20
        },
        end: {
          line: 483,
          column: 69
        }
      },
      "308": {
        start: {
          line: 486,
          column: 20
        },
        end: {
          line: 486,
          column: 60
        }
      },
      "309": {
        start: {
          line: 487,
          column: 32
        },
        end: {
          line: 487,
          column: 64
        }
      },
      "310": {
        start: {
          line: 488,
          column: 20
        },
        end: {
          line: 491,
          column: 21
        }
      },
      "311": {
        start: {
          line: 489,
          column: 24
        },
        end: {
          line: 489,
          column: 44
        }
      },
      "312": {
        start: {
          line: 490,
          column: 24
        },
        end: {
          line: 490,
          column: 60
        }
      },
      "313": {
        start: {
          line: 499,
          column: 4
        },
        end: {
          line: 507,
          column: 6
        }
      },
      "314": {
        start: {
          line: 500,
          column: 8
        },
        end: {
          line: 506,
          column: 114
        }
      },
      "315": {
        start: {
          line: 511,
          column: 4
        },
        end: {
          line: 526,
          column: 6
        }
      },
      "316": {
        start: {
          line: 512,
          column: 8
        },
        end: {
          line: 525,
          column: 11
        }
      },
      "317": {
        start: {
          line: 514,
          column: 12
        },
        end: {
          line: 524,
          column: 15
        }
      },
      "318": {
        start: {
          line: 515,
          column: 16
        },
        end: {
          line: 522,
          column: 17
        }
      },
      "319": {
        start: {
          line: 516,
          column: 20
        },
        end: {
          line: 516,
          column: 48
        }
      },
      "320": {
        start: {
          line: 517,
          column: 20
        },
        end: {
          line: 517,
          column: 104
        }
      },
      "321": {
        start: {
          line: 520,
          column: 20
        },
        end: {
          line: 520,
          column: 90
        }
      },
      "322": {
        start: {
          line: 521,
          column: 20
        },
        end: {
          line: 521,
          column: 49
        }
      },
      "323": {
        start: {
          line: 523,
          column: 16
        },
        end: {
          line: 523,
          column: 38
        }
      },
      "324": {
        start: {
          line: 527,
          column: 4
        },
        end: {
          line: 527,
          column: 34
        }
      },
      "325": {
        start: {
          line: 529,
          column: 0
        },
        end: {
          line: 529,
          column: 56
        }
      },
      "326": {
        start: {
          line: 531,
          column: 0
        },
        end: {
          line: 531,
          column: 70
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 7,
            column: 42
          },
          end: {
            line: 7,
            column: 43
          }
        },
        loc: {
          start: {
            line: 7,
            column: 54
          },
          end: {
            line: 17,
            column: 1
          }
        },
        line: 7
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 8,
            column: 32
          },
          end: {
            line: 8,
            column: 33
          }
        },
        loc: {
          start: {
            line: 8,
            column: 44
          },
          end: {
            line: 15,
            column: 5
          }
        },
        line: 8
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 18,
            column: 44
          },
          end: {
            line: 18,
            column: 45
          }
        },
        loc: {
          start: {
            line: 18,
            column: 89
          },
          end: {
            line: 26,
            column: 1
          }
        },
        line: 18
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 19,
            column: 13
          },
          end: {
            line: 19,
            column: 18
          }
        },
        loc: {
          start: {
            line: 19,
            column: 26
          },
          end: {
            line: 19,
            column: 112
          }
        },
        line: 19
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 19,
            column: 70
          },
          end: {
            line: 19,
            column: 71
          }
        },
        loc: {
          start: {
            line: 19,
            column: 89
          },
          end: {
            line: 19,
            column: 108
          }
        },
        line: 19
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 20,
            column: 36
          },
          end: {
            line: 20,
            column: 37
          }
        },
        loc: {
          start: {
            line: 20,
            column: 63
          },
          end: {
            line: 25,
            column: 5
          }
        },
        line: 20
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 21,
            column: 17
          },
          end: {
            line: 21,
            column: 26
          }
        },
        loc: {
          start: {
            line: 21,
            column: 34
          },
          end: {
            line: 21,
            column: 99
          }
        },
        line: 21
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 22,
            column: 17
          },
          end: {
            line: 22,
            column: 25
          }
        },
        loc: {
          start: {
            line: 22,
            column: 33
          },
          end: {
            line: 22,
            column: 102
          }
        },
        line: 22
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 23,
            column: 17
          },
          end: {
            line: 23,
            column: 21
          }
        },
        loc: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 118
          }
        },
        line: 23
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 27,
            column: 48
          },
          end: {
            line: 27,
            column: 49
          }
        },
        loc: {
          start: {
            line: 27,
            column: 73
          },
          end: {
            line: 53,
            column: 1
          }
        },
        line: 27
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 28,
            column: 30
          },
          end: {
            line: 28,
            column: 31
          }
        },
        loc: {
          start: {
            line: 28,
            column: 41
          },
          end: {
            line: 28,
            column: 83
          }
        },
        line: 28
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 29,
            column: 128
          },
          end: {
            line: 29,
            column: 129
          }
        },
        loc: {
          start: {
            line: 29,
            column: 139
          },
          end: {
            line: 29,
            column: 155
          }
        },
        line: 29
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 30,
            column: 13
          },
          end: {
            line: 30,
            column: 17
          }
        },
        loc: {
          start: {
            line: 30,
            column: 21
          },
          end: {
            line: 30,
            column: 70
          }
        },
        line: 30
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 30,
            column: 30
          },
          end: {
            line: 30,
            column: 31
          }
        },
        loc: {
          start: {
            line: 30,
            column: 43
          },
          end: {
            line: 30,
            column: 67
          }
        },
        line: 30
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 31,
            column: 13
          },
          end: {
            line: 31,
            column: 17
          }
        },
        loc: {
          start: {
            line: 31,
            column: 22
          },
          end: {
            line: 52,
            column: 5
          }
        },
        line: 31
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 59,
            column: 44
          },
          end: {
            line: 59,
            column: 45
          }
        },
        loc: {
          start: {
            line: 59,
            column: 56
          },
          end: {
            line: 528,
            column: 1
          }
        },
        line: 59
      },
      "16": {
        name: "RequestBatchingService",
        decl: {
          start: {
            line: 60,
            column: 13
          },
          end: {
            line: 60,
            column: 35
          }
        },
        loc: {
          start: {
            line: 60,
            column: 38
          },
          end: {
            line: 83,
            column: 5
          }
        },
        line: 60
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 82,
            column: 20
          },
          end: {
            line: 82,
            column: 21
          }
        },
        loc: {
          start: {
            line: 82,
            column: 32
          },
          end: {
            line: 82,
            column: 72
          }
        },
        line: 82
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 84,
            column: 41
          },
          end: {
            line: 84,
            column: 42
          }
        },
        loc: {
          start: {
            line: 84,
            column: 53
          },
          end: {
            line: 89,
            column: 5
          }
        },
        line: 84
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 93,
            column: 66
          },
          end: {
            line: 93,
            column: 67
          }
        },
        loc: {
          start: {
            line: 93,
            column: 101
          },
          end: {
            line: 143,
            column: 5
          }
        },
        line: 93
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 94,
            column: 51
          },
          end: {
            line: 94,
            column: 52
          }
        },
        loc: {
          start: {
            line: 94,
            column: 92
          },
          end: {
            line: 142,
            column: 9
          }
        },
        line: 94
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 98,
            column: 37
          },
          end: {
            line: 98,
            column: 38
          }
        },
        loc: {
          start: {
            line: 98,
            column: 51
          },
          end: {
            line: 141,
            column: 13
          }
        },
        line: 98
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 128,
            column: 54
          },
          end: {
            line: 128,
            column: 55
          }
        },
        loc: {
          start: {
            line: 128,
            column: 81
          },
          end: {
            line: 139,
            column: 25
          }
        },
        line: 128
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 147,
            column: 50
          },
          end: {
            line: 147,
            column: 51
          }
        },
        loc: {
          start: {
            line: 147,
            column: 80
          },
          end: {
            line: 171,
            column: 5
          }
        },
        line: 147
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 156,
            column: 19
          },
          end: {
            line: 156,
            column: 20
          }
        },
        loc: {
          start: {
            line: 156,
            column: 35
          },
          end: {
            line: 159,
            column: 9
          }
        },
        line: 156
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 166,
            column: 35
          },
          end: {
            line: 166,
            column: 36
          }
        },
        loc: {
          start: {
            line: 166,
            column: 47
          },
          end: {
            line: 168,
            column: 13
          }
        },
        line: 166
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 175,
            column: 52
          },
          end: {
            line: 175,
            column: 53
          }
        },
        loc: {
          start: {
            line: 175,
            column: 73
          },
          end: {
            line: 226,
            column: 5
          }
        },
        line: 175
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 176,
            column: 48
          },
          end: {
            line: 176,
            column: 49
          }
        },
        loc: {
          start: {
            line: 176,
            column: 60
          },
          end: {
            line: 225,
            column: 9
          }
        },
        line: 176
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 178,
            column: 37
          },
          end: {
            line: 178,
            column: 38
          }
        },
        loc: {
          start: {
            line: 178,
            column: 51
          },
          end: {
            line: 224,
            column: 13
          }
        },
        line: 178
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 215,
            column: 38
          },
          end: {
            line: 215,
            column: 39
          }
        },
        loc: {
          start: {
            line: 215,
            column: 57
          },
          end: {
            line: 217,
            column: 25
          }
        },
        line: 215
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 230,
            column: 62
          },
          end: {
            line: 230,
            column: 63
          }
        },
        loc: {
          start: {
            line: 230,
            column: 88
          },
          end: {
            line: 263,
            column: 5
          }
        },
        line: 230
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 231,
            column: 48
          },
          end: {
            line: 231,
            column: 49
          }
        },
        loc: {
          start: {
            line: 231,
            column: 60
          },
          end: {
            line: 262,
            column: 9
          }
        },
        line: 231
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 234,
            column: 37
          },
          end: {
            line: 234,
            column: 38
          }
        },
        loc: {
          start: {
            line: 234,
            column: 51
          },
          end: {
            line: 261,
            column: 13
          }
        },
        line: 234
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 237,
            column: 55
          },
          end: {
            line: 237,
            column: 56
          }
        },
        loc: {
          start: {
            line: 237,
            column: 74
          },
          end: {
            line: 255,
            column: 29
          }
        },
        line: 237
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 237,
            column: 116
          },
          end: {
            line: 237,
            column: 117
          }
        },
        loc: {
          start: {
            line: 237,
            column: 128
          },
          end: {
            line: 255,
            column: 25
          }
        },
        line: 237
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 239,
            column: 53
          },
          end: {
            line: 239,
            column: 54
          }
        },
        loc: {
          start: {
            line: 239,
            column: 67
          },
          end: {
            line: 254,
            column: 29
          }
        },
        line: 239
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 267,
            column: 62
          },
          end: {
            line: 267,
            column: 63
          }
        },
        loc: {
          start: {
            line: 267,
            column: 88
          },
          end: {
            line: 297,
            column: 5
          }
        },
        line: 267
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 268,
            column: 48
          },
          end: {
            line: 268,
            column: 49
          }
        },
        loc: {
          start: {
            line: 268,
            column: 60
          },
          end: {
            line: 296,
            column: 9
          }
        },
        line: 268
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 270,
            column: 37
          },
          end: {
            line: 270,
            column: 38
          }
        },
        loc: {
          start: {
            line: 270,
            column: 51
          },
          end: {
            line: 295,
            column: 13
          }
        },
        line: 270
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 301,
            column: 64
          },
          end: {
            line: 301,
            column: 65
          }
        },
        loc: {
          start: {
            line: 301,
            column: 92
          },
          end: {
            line: 367,
            column: 5
          }
        },
        line: 301
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 302,
            column: 48
          },
          end: {
            line: 302,
            column: 49
          }
        },
        loc: {
          start: {
            line: 302,
            column: 60
          },
          end: {
            line: 366,
            column: 9
          }
        },
        line: 302
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 305,
            column: 37
          },
          end: {
            line: 305,
            column: 38
          }
        },
        loc: {
          start: {
            line: 305,
            column: 51
          },
          end: {
            line: 365,
            column: 13
          }
        },
        line: 305
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 314,
            column: 133
          },
          end: {
            line: 314,
            column: 134
          }
        },
        loc: {
          start: {
            line: 314,
            column: 150
          },
          end: {
            line: 318,
            column: 37
          }
        },
        line: 314
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 371,
            column: 64
          },
          end: {
            line: 371,
            column: 65
          }
        },
        loc: {
          start: {
            line: 371,
            column: 87
          },
          end: {
            line: 380,
            column: 5
          }
        },
        line: 371
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 384,
            column: 56
          },
          end: {
            line: 384,
            column: 57
          }
        },
        loc: {
          start: {
            line: 384,
            column: 85
          },
          end: {
            line: 386,
            column: 5
          }
        },
        line: 384
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 390,
            column: 50
          },
          end: {
            line: 390,
            column: 51
          }
        },
        loc: {
          start: {
            line: 390,
            column: 68
          },
          end: {
            line: 402,
            column: 5
          }
        },
        line: 390
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 392,
            column: 17
          },
          end: {
            line: 392,
            column: 18
          }
        },
        loc: {
          start: {
            line: 392,
            column: 34
          },
          end: {
            line: 392,
            column: 102
          }
        },
        line: 392
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 406,
            column: 64
          },
          end: {
            line: 406,
            column: 65
          }
        },
        loc: {
          start: {
            line: 406,
            column: 85
          },
          end: {
            line: 438,
            column: 5
          }
        },
        line: 406
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 407,
            column: 48
          },
          end: {
            line: 407,
            column: 49
          }
        },
        loc: {
          start: {
            line: 407,
            column: 60
          },
          end: {
            line: 437,
            column: 9
          }
        },
        line: 407
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 409,
            column: 37
          },
          end: {
            line: 409,
            column: 38
          }
        },
        loc: {
          start: {
            line: 409,
            column: 51
          },
          end: {
            line: 436,
            column: 13
          }
        },
        line: 409
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 413,
            column: 54
          },
          end: {
            line: 413,
            column: 55
          }
        },
        loc: {
          start: {
            line: 413,
            column: 73
          },
          end: {
            line: 433,
            column: 25
          }
        },
        line: 413
      },
      "51": {
        name: "(anonymous_51)",
        decl: {
          start: {
            line: 414,
            column: 60
          },
          end: {
            line: 414,
            column: 61
          }
        },
        loc: {
          start: {
            line: 414,
            column: 72
          },
          end: {
            line: 432,
            column: 29
          }
        },
        line: 414
      },
      "52": {
        name: "(anonymous_52)",
        decl: {
          start: {
            line: 419,
            column: 102
          },
          end: {
            line: 419,
            column: 103
          }
        },
        loc: {
          start: {
            line: 419,
            column: 120
          },
          end: {
            line: 430,
            column: 37
          }
        },
        line: 419
      },
      "53": {
        name: "(anonymous_53)",
        decl: {
          start: {
            line: 442,
            column: 57
          },
          end: {
            line: 442,
            column: 58
          }
        },
        loc: {
          start: {
            line: 442,
            column: 69
          },
          end: {
            line: 444,
            column: 5
          }
        },
        line: 442
      },
      "54": {
        name: "(anonymous_54)",
        decl: {
          start: {
            line: 445,
            column: 55
          },
          end: {
            line: 445,
            column: 56
          }
        },
        loc: {
          start: {
            line: 445,
            column: 67
          },
          end: {
            line: 447,
            column: 5
          }
        },
        line: 445
      },
      "55": {
        name: "(anonymous_55)",
        decl: {
          start: {
            line: 448,
            column: 60
          },
          end: {
            line: 448,
            column: 61
          }
        },
        loc: {
          start: {
            line: 448,
            column: 81
          },
          end: {
            line: 456,
            column: 5
          }
        },
        line: 448
      },
      "56": {
        name: "(anonymous_56)",
        decl: {
          start: {
            line: 457,
            column: 57
          },
          end: {
            line: 457,
            column: 58
          }
        },
        loc: {
          start: {
            line: 457,
            column: 75
          },
          end: {
            line: 464,
            column: 5
          }
        },
        line: 457
      },
      "57": {
        name: "(anonymous_57)",
        decl: {
          start: {
            line: 468,
            column: 60
          },
          end: {
            line: 468,
            column: 61
          }
        },
        loc: {
          start: {
            line: 468,
            column: 72
          },
          end: {
            line: 495,
            column: 5
          }
        },
        line: 468
      },
      "58": {
        name: "(anonymous_58)",
        decl: {
          start: {
            line: 472,
            column: 59
          },
          end: {
            line: 472,
            column: 60
          }
        },
        loc: {
          start: {
            line: 472,
            column: 73
          },
          end: {
            line: 494,
            column: 9
          }
        },
        line: 472
      },
      "59": {
        name: "(anonymous_59)",
        decl: {
          start: {
            line: 474,
            column: 42
          },
          end: {
            line: 474,
            column: 43
          }
        },
        loc: {
          start: {
            line: 474,
            column: 61
          },
          end: {
            line: 474,
            column: 105
          }
        },
        line: 474
      },
      "60": {
        name: "(anonymous_60)",
        decl: {
          start: {
            line: 477,
            column: 35
          },
          end: {
            line: 477,
            column: 36
          }
        },
        loc: {
          start: {
            line: 477,
            column: 54
          },
          end: {
            line: 479,
            column: 17
          }
        },
        line: 477
      },
      "61": {
        name: "(anonymous_61)",
        decl: {
          start: {
            line: 481,
            column: 46
          },
          end: {
            line: 481,
            column: 47
          }
        },
        loc: {
          start: {
            line: 481,
            column: 65
          },
          end: {
            line: 481,
            column: 110
          }
        },
        line: 481
      },
      "62": {
        name: "(anonymous_62)",
        decl: {
          start: {
            line: 499,
            column: 50
          },
          end: {
            line: 499,
            column: 51
          }
        },
        loc: {
          start: {
            line: 499,
            column: 62
          },
          end: {
            line: 507,
            column: 5
          }
        },
        line: 499
      },
      "63": {
        name: "(anonymous_63)",
        decl: {
          start: {
            line: 511,
            column: 51
          },
          end: {
            line: 511,
            column: 52
          }
        },
        loc: {
          start: {
            line: 511,
            column: 63
          },
          end: {
            line: 526,
            column: 5
          }
        },
        line: 511
      },
      "64": {
        name: "(anonymous_64)",
        decl: {
          start: {
            line: 512,
            column: 48
          },
          end: {
            line: 512,
            column: 49
          }
        },
        loc: {
          start: {
            line: 512,
            column: 60
          },
          end: {
            line: 525,
            column: 9
          }
        },
        line: 512
      },
      "65": {
        name: "(anonymous_65)",
        decl: {
          start: {
            line: 514,
            column: 37
          },
          end: {
            line: 514,
            column: 38
          }
        },
        loc: {
          start: {
            line: 514,
            column: 51
          },
          end: {
            line: 524,
            column: 13
          }
        },
        line: 514
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 17,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 7,
            column: 16
          },
          end: {
            line: 7,
            column: 20
          }
        }, {
          start: {
            line: 7,
            column: 24
          },
          end: {
            line: 7,
            column: 37
          }
        }, {
          start: {
            line: 7,
            column: 42
          },
          end: {
            line: 17,
            column: 1
          }
        }],
        line: 7
      },
      "1": {
        loc: {
          start: {
            line: 8,
            column: 15
          },
          end: {
            line: 15,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 15
          },
          end: {
            line: 8,
            column: 28
          }
        }, {
          start: {
            line: 8,
            column: 32
          },
          end: {
            line: 15,
            column: 5
          }
        }],
        line: 8
      },
      "2": {
        loc: {
          start: {
            line: 11,
            column: 29
          },
          end: {
            line: 12,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 11,
            column: 29
          },
          end: {
            line: 12,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 11
      },
      "3": {
        loc: {
          start: {
            line: 18,
            column: 16
          },
          end: {
            line: 26,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        }, {
          start: {
            line: 18,
            column: 25
          },
          end: {
            line: 18,
            column: 39
          }
        }, {
          start: {
            line: 18,
            column: 44
          },
          end: {
            line: 26,
            column: 1
          }
        }],
        line: 18
      },
      "4": {
        loc: {
          start: {
            line: 19,
            column: 35
          },
          end: {
            line: 19,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 19,
            column: 56
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 64
          },
          end: {
            line: 19,
            column: 109
          }
        }],
        line: 19
      },
      "5": {
        loc: {
          start: {
            line: 20,
            column: 16
          },
          end: {
            line: 20,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 20,
            column: 16
          },
          end: {
            line: 20,
            column: 17
          }
        }, {
          start: {
            line: 20,
            column: 22
          },
          end: {
            line: 20,
            column: 33
          }
        }],
        line: 20
      },
      "6": {
        loc: {
          start: {
            line: 23,
            column: 32
          },
          end: {
            line: 23,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 46
          },
          end: {
            line: 23,
            column: 67
          }
        }, {
          start: {
            line: 23,
            column: 70
          },
          end: {
            line: 23,
            column: 115
          }
        }],
        line: 23
      },
      "7": {
        loc: {
          start: {
            line: 24,
            column: 51
          },
          end: {
            line: 24,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 51
          },
          end: {
            line: 24,
            column: 61
          }
        }, {
          start: {
            line: 24,
            column: 65
          },
          end: {
            line: 24,
            column: 67
          }
        }],
        line: 24
      },
      "8": {
        loc: {
          start: {
            line: 27,
            column: 18
          },
          end: {
            line: 53,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 19
          },
          end: {
            line: 27,
            column: 23
          }
        }, {
          start: {
            line: 27,
            column: 27
          },
          end: {
            line: 27,
            column: 43
          }
        }, {
          start: {
            line: 27,
            column: 48
          },
          end: {
            line: 53,
            column: 1
          }
        }],
        line: 27
      },
      "9": {
        loc: {
          start: {
            line: 28,
            column: 43
          },
          end: {
            line: 28,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 43
          },
          end: {
            line: 28,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "10": {
        loc: {
          start: {
            line: 28,
            column: 134
          },
          end: {
            line: 28,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 28,
            column: 167
          },
          end: {
            line: 28,
            column: 175
          }
        }, {
          start: {
            line: 28,
            column: 178
          },
          end: {
            line: 28,
            column: 184
          }
        }],
        line: 28
      },
      "11": {
        loc: {
          start: {
            line: 29,
            column: 74
          },
          end: {
            line: 29,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 74
          },
          end: {
            line: 29,
            column: 102
          }
        }, {
          start: {
            line: 29,
            column: 107
          },
          end: {
            line: 29,
            column: 155
          }
        }],
        line: 29
      },
      "12": {
        loc: {
          start: {
            line: 32,
            column: 8
          },
          end: {
            line: 32,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 8
          },
          end: {
            line: 32,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "13": {
        loc: {
          start: {
            line: 33,
            column: 15
          },
          end: {
            line: 33,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 15
          },
          end: {
            line: 33,
            column: 16
          }
        }, {
          start: {
            line: 33,
            column: 21
          },
          end: {
            line: 33,
            column: 44
          }
        }],
        line: 33
      },
      "14": {
        loc: {
          start: {
            line: 33,
            column: 28
          },
          end: {
            line: 33,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 28
          },
          end: {
            line: 33,
            column: 33
          }
        }, {
          start: {
            line: 33,
            column: 38
          },
          end: {
            line: 33,
            column: 43
          }
        }],
        line: 33
      },
      "15": {
        loc: {
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "16": {
        loc: {
          start: {
            line: 34,
            column: 23
          },
          end: {
            line: 34,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 34,
            column: 23
          },
          end: {
            line: 34,
            column: 24
          }
        }, {
          start: {
            line: 34,
            column: 29
          },
          end: {
            line: 34,
            column: 125
          }
        }, {
          start: {
            line: 34,
            column: 130
          },
          end: {
            line: 34,
            column: 158
          }
        }],
        line: 34
      },
      "17": {
        loc: {
          start: {
            line: 34,
            column: 33
          },
          end: {
            line: 34,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 34,
            column: 45
          },
          end: {
            line: 34,
            column: 56
          }
        }, {
          start: {
            line: 34,
            column: 59
          },
          end: {
            line: 34,
            column: 125
          }
        }],
        line: 34
      },
      "18": {
        loc: {
          start: {
            line: 34,
            column: 59
          },
          end: {
            line: 34,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 34,
            column: 67
          },
          end: {
            line: 34,
            column: 116
          }
        }, {
          start: {
            line: 34,
            column: 119
          },
          end: {
            line: 34,
            column: 125
          }
        }],
        line: 34
      },
      "19": {
        loc: {
          start: {
            line: 34,
            column: 67
          },
          end: {
            line: 34,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 34,
            column: 67
          },
          end: {
            line: 34,
            column: 77
          }
        }, {
          start: {
            line: 34,
            column: 82
          },
          end: {
            line: 34,
            column: 115
          }
        }],
        line: 34
      },
      "20": {
        loc: {
          start: {
            line: 34,
            column: 82
          },
          end: {
            line: 34,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 34,
            column: 83
          },
          end: {
            line: 34,
            column: 98
          }
        }, {
          start: {
            line: 34,
            column: 103
          },
          end: {
            line: 34,
            column: 112
          }
        }],
        line: 34
      },
      "21": {
        loc: {
          start: {
            line: 35,
            column: 12
          },
          end: {
            line: 35,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 12
          },
          end: {
            line: 35,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "22": {
        loc: {
          start: {
            line: 36,
            column: 12
          },
          end: {
            line: 48,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 37,
            column: 16
          },
          end: {
            line: 37,
            column: 23
          }
        }, {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 46
          }
        }, {
          start: {
            line: 38,
            column: 16
          },
          end: {
            line: 38,
            column: 72
          }
        }, {
          start: {
            line: 39,
            column: 16
          },
          end: {
            line: 39,
            column: 65
          }
        }, {
          start: {
            line: 40,
            column: 16
          },
          end: {
            line: 40,
            column: 65
          }
        }, {
          start: {
            line: 41,
            column: 16
          },
          end: {
            line: 47,
            column: 43
          }
        }],
        line: 36
      },
      "23": {
        loc: {
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "24": {
        loc: {
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 74
          }
        }, {
          start: {
            line: 42,
            column: 79
          },
          end: {
            line: 42,
            column: 90
          }
        }, {
          start: {
            line: 42,
            column: 94
          },
          end: {
            line: 42,
            column: 105
          }
        }],
        line: 42
      },
      "25": {
        loc: {
          start: {
            line: 42,
            column: 42
          },
          end: {
            line: 42,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 42
          },
          end: {
            line: 42,
            column: 54
          }
        }, {
          start: {
            line: 42,
            column: 58
          },
          end: {
            line: 42,
            column: 73
          }
        }],
        line: 42
      },
      "26": {
        loc: {
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "27": {
        loc: {
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 43,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 43,
            column: 35
          }
        }, {
          start: {
            line: 43,
            column: 40
          },
          end: {
            line: 43,
            column: 42
          }
        }, {
          start: {
            line: 43,
            column: 47
          },
          end: {
            line: 43,
            column: 59
          }
        }, {
          start: {
            line: 43,
            column: 63
          },
          end: {
            line: 43,
            column: 75
          }
        }],
        line: 43
      },
      "28": {
        loc: {
          start: {
            line: 44,
            column: 20
          },
          end: {
            line: 44,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 20
          },
          end: {
            line: 44,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "29": {
        loc: {
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 35
          }
        }, {
          start: {
            line: 44,
            column: 39
          },
          end: {
            line: 44,
            column: 53
          }
        }],
        line: 44
      },
      "30": {
        loc: {
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 45
      },
      "31": {
        loc: {
          start: {
            line: 45,
            column: 24
          },
          end: {
            line: 45,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 45,
            column: 24
          },
          end: {
            line: 45,
            column: 25
          }
        }, {
          start: {
            line: 45,
            column: 29
          },
          end: {
            line: 45,
            column: 43
          }
        }],
        line: 45
      },
      "32": {
        loc: {
          start: {
            line: 46,
            column: 20
          },
          end: {
            line: 46,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 20
          },
          end: {
            line: 46,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "33": {
        loc: {
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 51,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 51,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 51
      },
      "34": {
        loc: {
          start: {
            line: 51,
            column: 52
          },
          end: {
            line: 51,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 51,
            column: 60
          },
          end: {
            line: 51,
            column: 65
          }
        }, {
          start: {
            line: 51,
            column: 68
          },
          end: {
            line: 51,
            column: 74
          }
        }],
        line: 51
      },
      "35": {
        loc: {
          start: {
            line: 66,
            column: 35
          },
          end: {
            line: 66,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 66,
            column: 35
          },
          end: {
            line: 66,
            column: 61
          }
        }, {
          start: {
            line: 66,
            column: 65
          },
          end: {
            line: 66,
            column: 68
          }
        }],
        line: 66
      },
      "36": {
        loc: {
          start: {
            line: 67,
            column: 37
          },
          end: {
            line: 67,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 67,
            column: 37
          },
          end: {
            line: 67,
            column: 65
          }
        }, {
          start: {
            line: 67,
            column: 69
          },
          end: {
            line: 67,
            column: 75
          }
        }],
        line: 67
      },
      "37": {
        loc: {
          start: {
            line: 68,
            column: 36
          },
          end: {
            line: 68,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 68,
            column: 36
          },
          end: {
            line: 68,
            column: 65
          }
        }, {
          start: {
            line: 68,
            column: 69
          },
          end: {
            line: 68,
            column: 75
          }
        }],
        line: 68
      },
      "38": {
        loc: {
          start: {
            line: 85,
            column: 8
          },
          end: {
            line: 87,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 85,
            column: 8
          },
          end: {
            line: 87,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 85
      },
      "39": {
        loc: {
          start: {
            line: 97,
            column: 12
          },
          end: {
            line: 97,
            column: 61
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 97,
            column: 12
          },
          end: {
            line: 97,
            column: 61
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 97
      },
      "40": {
        loc: {
          start: {
            line: 99,
            column: 16
          },
          end: {
            line: 140,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 100,
            column: 20
          },
          end: {
            line: 105,
            column: 107
          }
        }, {
          start: {
            line: 106,
            column: 20
          },
          end: {
            line: 118,
            column: 94
          }
        }, {
          start: {
            line: 119,
            column: 20
          },
          end: {
            line: 125,
            column: 37
          }
        }, {
          start: {
            line: 126,
            column: 20
          },
          end: {
            line: 139,
            column: 28
          }
        }],
        line: 99
      },
      "41": {
        loc: {
          start: {
            line: 108,
            column: 24
          },
          end: {
            line: 116,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 108,
            column: 24
          },
          end: {
            line: 116,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 108
      },
      "42": {
        loc: {
          start: {
            line: 117,
            column: 24
          },
          end: {
            line: 117,
            column: 86
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 117,
            column: 24
          },
          end: {
            line: 117,
            column: 86
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 117
      },
      "43": {
        loc: {
          start: {
            line: 121,
            column: 24
          },
          end: {
            line: 124,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 121,
            column: 24
          },
          end: {
            line: 124,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 121
      },
      "44": {
        loc: {
          start: {
            line: 149,
            column: 8
          },
          end: {
            line: 151,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 149,
            column: 8
          },
          end: {
            line: 151,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 149
      },
      "45": {
        loc: {
          start: {
            line: 161,
            column: 8
          },
          end: {
            line: 170,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 161,
            column: 8
          },
          end: {
            line: 170,
            column: 9
          }
        }, {
          start: {
            line: 164,
            column: 13
          },
          end: {
            line: 170,
            column: 9
          }
        }],
        line: 161
      },
      "46": {
        loc: {
          start: {
            line: 164,
            column: 13
          },
          end: {
            line: 170,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 164,
            column: 13
          },
          end: {
            line: 170,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 164
      },
      "47": {
        loc: {
          start: {
            line: 179,
            column: 16
          },
          end: {
            line: 223,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 180,
            column: 20
          },
          end: {
            line: 195,
            column: 37
          }
        }, {
          start: {
            line: 196,
            column: 20
          },
          end: {
            line: 199,
            column: 90
          }
        }, {
          start: {
            line: 200,
            column: 20
          },
          end: {
            line: 202,
            column: 48
          }
        }, {
          start: {
            line: 203,
            column: 20
          },
          end: {
            line: 203,
            column: 94
          }
        }, {
          start: {
            line: 204,
            column: 20
          },
          end: {
            line: 206,
            column: 37
          }
        }, {
          start: {
            line: 207,
            column: 20
          },
          end: {
            line: 210,
            column: 48
          }
        }, {
          start: {
            line: 211,
            column: 20
          },
          end: {
            line: 218,
            column: 48
          }
        }, {
          start: {
            line: 219,
            column: 20
          },
          end: {
            line: 221,
            column: 50
          }
        }, {
          start: {
            line: 222,
            column: 20
          },
          end: {
            line: 222,
            column: 50
          }
        }],
        line: 179
      },
      "48": {
        loc: {
          start: {
            line: 182,
            column: 24
          },
          end: {
            line: 184,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 182,
            column: 24
          },
          end: {
            line: 184,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 182
      },
      "49": {
        loc: {
          start: {
            line: 182,
            column: 28
          },
          end: {
            line: 182,
            column: 97
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 182,
            column: 28
          },
          end: {
            line: 182,
            column: 34
          }
        }, {
          start: {
            line: 182,
            column: 38
          },
          end: {
            line: 182,
            column: 56
          }
        }, {
          start: {
            line: 182,
            column: 60
          },
          end: {
            line: 182,
            column: 97
          }
        }],
        line: 182
      },
      "50": {
        loc: {
          start: {
            line: 189,
            column: 24
          },
          end: {
            line: 192,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 189,
            column: 24
          },
          end: {
            line: 192,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 189
      },
      "51": {
        loc: {
          start: {
            line: 198,
            column: 24
          },
          end: {
            line: 198,
            column: 115
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 198,
            column: 24
          },
          end: {
            line: 198,
            column: 115
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 198
      },
      "52": {
        loc: {
          start: {
            line: 198,
            column: 30
          },
          end: {
            line: 198,
            column: 88
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 198,
            column: 30
          },
          end: {
            line: 198,
            column: 68
          }
        }, {
          start: {
            line: 198,
            column: 72
          },
          end: {
            line: 198,
            column: 88
          }
        }],
        line: 198
      },
      "53": {
        loc: {
          start: {
            line: 235,
            column: 16
          },
          end: {
            line: 260,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 236,
            column: 20
          },
          end: {
            line: 256,
            column: 85
          }
        }, {
          start: {
            line: 257,
            column: 20
          },
          end: {
            line: 259,
            column: 46
          }
        }],
        line: 235
      },
      "54": {
        loc: {
          start: {
            line: 240,
            column: 32
          },
          end: {
            line: 253,
            column: 33
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 241,
            column: 36
          },
          end: {
            line: 243,
            column: 110
          }
        }, {
          start: {
            line: 244,
            column: 36
          },
          end: {
            line: 247,
            column: 64
          }
        }, {
          start: {
            line: 248,
            column: 36
          },
          end: {
            line: 251,
            column: 64
          }
        }, {
          start: {
            line: 252,
            column: 36
          },
          end: {
            line: 252,
            column: 66
          }
        }],
        line: 240
      },
      "55": {
        loc: {
          start: {
            line: 271,
            column: 16
          },
          end: {
            line: 294,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 272,
            column: 20
          },
          end: {
            line: 274,
            column: 37
          }
        }, {
          start: {
            line: 275,
            column: 20
          },
          end: {
            line: 278,
            column: 37
          }
        }, {
          start: {
            line: 279,
            column: 20
          },
          end: {
            line: 281,
            column: 94
          }
        }, {
          start: {
            line: 282,
            column: 20
          },
          end: {
            line: 285,
            column: 48
          }
        }, {
          start: {
            line: 286,
            column: 20
          },
          end: {
            line: 289,
            column: 48
          }
        }, {
          start: {
            line: 290,
            column: 20
          },
          end: {
            line: 292,
            column: 48
          }
        }, {
          start: {
            line: 293,
            column: 20
          },
          end: {
            line: 293,
            column: 50
          }
        }],
        line: 271
      },
      "56": {
        loc: {
          start: {
            line: 276,
            column: 24
          },
          end: {
            line: 276,
            column: 76
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 276,
            column: 24
          },
          end: {
            line: 276,
            column: 76
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 276
      },
      "57": {
        loc: {
          start: {
            line: 306,
            column: 16
          },
          end: {
            line: 364,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 307,
            column: 20
          },
          end: {
            line: 309,
            column: 37
          }
        }, {
          start: {
            line: 310,
            column: 20
          },
          end: {
            line: 324,
            column: 32
          }
        }, {
          start: {
            line: 325,
            column: 20
          },
          end: {
            line: 330,
            column: 48
          }
        }, {
          start: {
            line: 331,
            column: 20
          },
          end: {
            line: 332,
            column: 41
          }
        }, {
          start: {
            line: 333,
            column: 20
          },
          end: {
            line: 336,
            column: 37
          }
        }, {
          start: {
            line: 337,
            column: 20
          },
          end: {
            line: 346,
            column: 32
          }
        }, {
          start: {
            line: 347,
            column: 20
          },
          end: {
            line: 354,
            column: 31
          }
        }, {
          start: {
            line: 355,
            column: 20
          },
          end: {
            line: 362,
            column: 31
          }
        }, {
          start: {
            line: 363,
            column: 20
          },
          end: {
            line: 363,
            column: 50
          }
        }],
        line: 306
      },
      "58": {
        loc: {
          start: {
            line: 314,
            column: 47
          },
          end: {
            line: 318,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 314,
            column: 48
          },
          end: {
            line: 318,
            column: 38
          }
        }, {
          start: {
            line: 318,
            column: 43
          },
          end: {
            line: 318,
            column: 45
          }
        }],
        line: 314
      },
      "59": {
        loc: {
          start: {
            line: 314,
            column: 48
          },
          end: {
            line: 318,
            column: 38
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 314,
            column: 117
          },
          end: {
            line: 314,
            column: 123
          }
        }, {
          start: {
            line: 314,
            column: 126
          },
          end: {
            line: 318,
            column: 38
          }
        }],
        line: 314
      },
      "60": {
        loc: {
          start: {
            line: 314,
            column: 48
          },
          end: {
            line: 314,
            column: 114
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 314,
            column: 48
          },
          end: {
            line: 314,
            column: 97
          }
        }, {
          start: {
            line: 314,
            column: 101
          },
          end: {
            line: 314,
            column: 114
          }
        }],
        line: 314
      },
      "61": {
        loc: {
          start: {
            line: 319,
            column: 44
          },
          end: {
            line: 319,
            column: 142
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 319,
            column: 116
          },
          end: {
            line: 319,
            column: 122
          }
        }, {
          start: {
            line: 319,
            column: 125
          },
          end: {
            line: 319,
            column: 142
          }
        }],
        line: 319
      },
      "62": {
        loc: {
          start: {
            line: 319,
            column: 44
          },
          end: {
            line: 319,
            column: 113
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 319,
            column: 44
          },
          end: {
            line: 319,
            column: 96
          }
        }, {
          start: {
            line: 319,
            column: 100
          },
          end: {
            line: 319,
            column: 113
          }
        }],
        line: 319
      },
      "63": {
        loc: {
          start: {
            line: 320,
            column: 69
          },
          end: {
            line: 320,
            column: 157
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 320,
            column: 136
          },
          end: {
            line: 320,
            column: 142
          }
        }, {
          start: {
            line: 320,
            column: 145
          },
          end: {
            line: 320,
            column: 157
          }
        }],
        line: 320
      },
      "64": {
        loc: {
          start: {
            line: 320,
            column: 69
          },
          end: {
            line: 320,
            column: 133
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 320,
            column: 69
          },
          end: {
            line: 320,
            column: 116
          }
        }, {
          start: {
            line: 320,
            column: 120
          },
          end: {
            line: 320,
            column: 133
          }
        }],
        line: 320
      },
      "65": {
        loc: {
          start: {
            line: 322,
            column: 46
          },
          end: {
            line: 322,
            column: 145
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 322,
            column: 47
          },
          end: {
            line: 322,
            column: 138
          }
        }, {
          start: {
            line: 322,
            column: 143
          },
          end: {
            line: 322,
            column: 145
          }
        }],
        line: 322
      },
      "66": {
        loc: {
          start: {
            line: 322,
            column: 47
          },
          end: {
            line: 322,
            column: 138
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 322,
            column: 114
          },
          end: {
            line: 322,
            column: 120
          }
        }, {
          start: {
            line: 322,
            column: 123
          },
          end: {
            line: 322,
            column: 138
          }
        }],
        line: 322
      },
      "67": {
        loc: {
          start: {
            line: 322,
            column: 47
          },
          end: {
            line: 322,
            column: 111
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 322,
            column: 47
          },
          end: {
            line: 322,
            column: 94
          }
        }, {
          start: {
            line: 322,
            column: 98
          },
          end: {
            line: 322,
            column: 111
          }
        }],
        line: 322
      },
      "68": {
        loc: {
          start: {
            line: 323,
            column: 63
          },
          end: {
            line: 323,
            column: 148
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 323,
            column: 130
          },
          end: {
            line: 323,
            column: 136
          }
        }, {
          start: {
            line: 323,
            column: 139
          },
          end: {
            line: 323,
            column: 148
          }
        }],
        line: 323
      },
      "69": {
        loc: {
          start: {
            line: 323,
            column: 63
          },
          end: {
            line: 323,
            column: 127
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 323,
            column: 63
          },
          end: {
            line: 323,
            column: 110
          }
        }, {
          start: {
            line: 323,
            column: 114
          },
          end: {
            line: 323,
            column: 127
          }
        }],
        line: 323
      },
      "70": {
        loc: {
          start: {
            line: 328,
            column: 24
          },
          end: {
            line: 328,
            column: 77
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 328,
            column: 24
          },
          end: {
            line: 328,
            column: 77
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 328
      },
      "71": {
        loc: {
          start: {
            line: 331,
            column: 108
          },
          end: {
            line: 331,
            column: 147
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 331,
            column: 108
          },
          end: {
            line: 331,
            column: 141
          }
        }, {
          start: {
            line: 331,
            column: 145
          },
          end: {
            line: 331,
            column: 147
          }
        }],
        line: 331
      },
      "72": {
        loc: {
          start: {
            line: 338,
            column: 24
          },
          end: {
            line: 340,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 338,
            column: 24
          },
          end: {
            line: 340,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 338
      },
      "73": {
        loc: {
          start: {
            line: 339,
            column: 44
          },
          end: {
            line: 339,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 339,
            column: 44
          },
          end: {
            line: 339,
            column: 64
          }
        }, {
          start: {
            line: 339,
            column: 68
          },
          end: {
            line: 339,
            column: 85
          }
        }],
        line: 339
      },
      "74": {
        loc: {
          start: {
            line: 374,
            column: 24
          },
          end: {
            line: 374,
            column: 122
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 374,
            column: 25
          },
          end: {
            line: 374,
            column: 115
          }
        }, {
          start: {
            line: 374,
            column: 120
          },
          end: {
            line: 374,
            column: 122
          }
        }],
        line: 374
      },
      "75": {
        loc: {
          start: {
            line: 374,
            column: 25
          },
          end: {
            line: 374,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 374,
            column: 89
          },
          end: {
            line: 374,
            column: 95
          }
        }, {
          start: {
            line: 374,
            column: 98
          },
          end: {
            line: 374,
            column: 115
          }
        }],
        line: 374
      },
      "76": {
        loc: {
          start: {
            line: 374,
            column: 25
          },
          end: {
            line: 374,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 374,
            column: 25
          },
          end: {
            line: 374,
            column: 69
          }
        }, {
          start: {
            line: 374,
            column: 73
          },
          end: {
            line: 374,
            column: 86
          }
        }],
        line: 374
      },
      "77": {
        loc: {
          start: {
            line: 375,
            column: 25
          },
          end: {
            line: 375,
            column: 120
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 375,
            column: 26
          },
          end: {
            line: 375,
            column: 113
          }
        }, {
          start: {
            line: 375,
            column: 118
          },
          end: {
            line: 375,
            column: 120
          }
        }],
        line: 375
      },
      "78": {
        loc: {
          start: {
            line: 375,
            column: 26
          },
          end: {
            line: 375,
            column: 113
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 375,
            column: 90
          },
          end: {
            line: 375,
            column: 96
          }
        }, {
          start: {
            line: 375,
            column: 99
          },
          end: {
            line: 375,
            column: 113
          }
        }],
        line: 375
      },
      "79": {
        loc: {
          start: {
            line: 375,
            column: 26
          },
          end: {
            line: 375,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 375,
            column: 26
          },
          end: {
            line: 375,
            column: 70
          }
        }, {
          start: {
            line: 375,
            column: 74
          },
          end: {
            line: 375,
            column: 87
          }
        }],
        line: 375
      },
      "80": {
        loc: {
          start: {
            line: 376,
            column: 23
          },
          end: {
            line: 376,
            column: 111
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 376,
            column: 24
          },
          end: {
            line: 376,
            column: 104
          }
        }, {
          start: {
            line: 376,
            column: 109
          },
          end: {
            line: 376,
            column: 111
          }
        }],
        line: 376
      },
      "81": {
        loc: {
          start: {
            line: 376,
            column: 24
          },
          end: {
            line: 376,
            column: 104
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 376,
            column: 83
          },
          end: {
            line: 376,
            column: 89
          }
        }, {
          start: {
            line: 376,
            column: 92
          },
          end: {
            line: 376,
            column: 104
          }
        }],
        line: 376
      },
      "82": {
        loc: {
          start: {
            line: 376,
            column: 24
          },
          end: {
            line: 376,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 376,
            column: 24
          },
          end: {
            line: 376,
            column: 63
          }
        }, {
          start: {
            line: 376,
            column: 67
          },
          end: {
            line: 376,
            column: 80
          }
        }],
        line: 376
      },
      "83": {
        loc: {
          start: {
            line: 377,
            column: 40
          },
          end: {
            line: 377,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 377,
            column: 40
          },
          end: {
            line: 377,
            column: 65
          }
        }, {
          start: {
            line: 377,
            column: 69
          },
          end: {
            line: 377,
            column: 71
          }
        }],
        line: 377
      },
      "84": {
        loc: {
          start: {
            line: 411,
            column: 16
          },
          end: {
            line: 434,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 411,
            column: 16
          },
          end: {
            line: 434,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 411
      },
      "85": {
        loc: {
          start: {
            line: 415,
            column: 32
          },
          end: {
            line: 431,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 415,
            column: 32
          },
          end: {
            line: 431,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 415
      },
      "86": {
        loc: {
          start: {
            line: 420,
            column: 40
          },
          end: {
            line: 429,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 420,
            column: 40
          },
          end: {
            line: 429,
            column: 41
          }
        }, {
          start: {
            line: 427,
            column: 45
          },
          end: {
            line: 429,
            column: 41
          }
        }],
        line: 420
      },
      "87": {
        loc: {
          start: {
            line: 449,
            column: 8
          },
          end: {
            line: 455,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 450,
            column: 12
          },
          end: {
            line: 450,
            column: 42
          }
        }, {
          start: {
            line: 451,
            column: 12
          },
          end: {
            line: 451,
            column: 40
          }
        }, {
          start: {
            line: 452,
            column: 12
          },
          end: {
            line: 452,
            column: 39
          }
        }, {
          start: {
            line: 453,
            column: 12
          },
          end: {
            line: 453,
            column: 40
          }
        }, {
          start: {
            line: 454,
            column: 12
          },
          end: {
            line: 454,
            column: 31
          }
        }],
        line: 449
      },
      "88": {
        loc: {
          start: {
            line: 458,
            column: 8
          },
          end: {
            line: 463,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 459,
            column: 12
          },
          end: {
            line: 459,
            column: 34
          }
        }, {
          start: {
            line: 460,
            column: 12
          },
          end: {
            line: 460,
            column: 40
          }
        }, {
          start: {
            line: 461,
            column: 12
          },
          end: {
            line: 461,
            column: 37
          }
        }, {
          start: {
            line: 462,
            column: 12
          },
          end: {
            line: 462,
            column: 32
          }
        }],
        line: 458
      },
      "89": {
        loc: {
          start: {
            line: 475,
            column: 12
          },
          end: {
            line: 493,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 475,
            column: 12
          },
          end: {
            line: 493,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 475
      },
      "90": {
        loc: {
          start: {
            line: 482,
            column: 16
          },
          end: {
            line: 492,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 482,
            column: 16
          },
          end: {
            line: 492,
            column: 17
          }
        }, {
          start: {
            line: 485,
            column: 21
          },
          end: {
            line: 492,
            column: 17
          }
        }],
        line: 482
      },
      "91": {
        loc: {
          start: {
            line: 488,
            column: 20
          },
          end: {
            line: 491,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 488,
            column: 20
          },
          end: {
            line: 491,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 488
      },
      "92": {
        loc: {
          start: {
            line: 500,
            column: 71
          },
          end: {
            line: 502,
            column: 19
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 501,
            column: 18
          },
          end: {
            line: 501,
            column: 83
          }
        }, {
          start: {
            line: 502,
            column: 18
          },
          end: {
            line: 502,
            column: 19
          }
        }],
        line: 500
      },
      "93": {
        loc: {
          start: {
            line: 502,
            column: 35
          },
          end: {
            line: 504,
            column: 19
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 503,
            column: 18
          },
          end: {
            line: 503,
            column: 77
          }
        }, {
          start: {
            line: 504,
            column: 18
          },
          end: {
            line: 504,
            column: 19
          }
        }],
        line: 502
      },
      "94": {
        loc: {
          start: {
            line: 504,
            column: 40
          },
          end: {
            line: 506,
            column: 19
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 505,
            column: 18
          },
          end: {
            line: 505,
            column: 88
          }
        }, {
          start: {
            line: 506,
            column: 18
          },
          end: {
            line: 506,
            column: 19
          }
        }],
        line: 504
      },
      "95": {
        loc: {
          start: {
            line: 517,
            column: 42
          },
          end: {
            line: 517,
            column: 102
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 517,
            column: 42
          },
          end: {
            line: 517,
            column: 69
          }
        }, {
          start: {
            line: 517,
            column: 73
          },
          end: {
            line: 517,
            column: 102
          }
        }],
        line: 517
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0,
      "265": 0,
      "266": 0,
      "267": 0,
      "268": 0,
      "269": 0,
      "270": 0,
      "271": 0,
      "272": 0,
      "273": 0,
      "274": 0,
      "275": 0,
      "276": 0,
      "277": 0,
      "278": 0,
      "279": 0,
      "280": 0,
      "281": 0,
      "282": 0,
      "283": 0,
      "284": 0,
      "285": 0,
      "286": 0,
      "287": 0,
      "288": 0,
      "289": 0,
      "290": 0,
      "291": 0,
      "292": 0,
      "293": 0,
      "294": 0,
      "295": 0,
      "296": 0,
      "297": 0,
      "298": 0,
      "299": 0,
      "300": 0,
      "301": 0,
      "302": 0,
      "303": 0,
      "304": 0,
      "305": 0,
      "306": 0,
      "307": 0,
      "308": 0,
      "309": 0,
      "310": 0,
      "311": 0,
      "312": 0,
      "313": 0,
      "314": 0,
      "315": 0,
      "316": 0,
      "317": 0,
      "318": 0,
      "319": 0,
      "320": 0,
      "321": 0,
      "322": 0,
      "323": 0,
      "324": 0,
      "325": 0,
      "326": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0, 0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0, 0, 0, 0, 0, 0, 0, 0],
      "48": [0, 0],
      "49": [0, 0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0, 0, 0],
      "55": [0, 0, 0, 0, 0, 0, 0],
      "56": [0, 0],
      "57": [0, 0, 0, 0, 0, 0, 0, 0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0],
      "81": [0, 0],
      "82": [0, 0],
      "83": [0, 0],
      "84": [0, 0],
      "85": [0, 0],
      "86": [0, 0],
      "87": [0, 0, 0, 0, 0],
      "88": [0, 0, 0, 0],
      "89": [0, 0],
      "90": [0, 0],
      "91": [0, 0],
      "92": [0, 0],
      "93": [0, 0],
      "94": [0, 0],
      "95": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/request-batching-service.ts",
      mappings: ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,2EAAiE;AACjE,iDAAgD;AAChD,8EAA6E;AA6B7E;IAwBE;QAAA,iBAGC;QAzBO,oBAAe,GAAgC,IAAI,GAAG,EAAE,CAAC;QACzD,sBAAiB,GAAgB,IAAI,GAAG,EAAE,CAAC;QAC3C,gBAAW,GAAgC,IAAI,GAAG,EAAE,CAAC;QAE5C,WAAM,GAAuB;YAC5C,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,GAAG,CAAC;YACzD,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,MAAM,CAAC;YAChE,aAAa,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,MAAM,CAAC;YAChE,mBAAmB,EAAE,OAAO,CAAC,GAAG,CAAC,4BAA4B,KAAK,OAAO;YACzE,0BAA0B,EAAE,OAAO,CAAC,GAAG,CAAC,4BAA4B,KAAK,OAAO;SACjF,CAAC;QAEF,sBAAsB;QACd,YAAO,GAAG;YAChB,aAAa,EAAE,CAAC;YAChB,eAAe,EAAE,CAAC;YAClB,oBAAoB,EAAE,CAAC;YACvB,SAAS,EAAE,CAAC;YACZ,mBAAmB,EAAE,CAAC;YACtB,YAAY,EAAE,CAAC;SAChB,CAAC;QAGA,8BAA8B;QAC9B,WAAW,CAAC,cAAM,OAAA,KAAI,CAAC,oBAAoB,EAAE,EAA3B,CAA2B,EAAE,KAAK,CAAC,CAAC;IACxD,CAAC;IAEa,kCAAW,GAAzB;QACE,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,CAAC;YACrC,sBAAsB,CAAC,QAAQ,GAAG,IAAI,sBAAsB,EAAE,CAAC;QACjE,CAAC;QACD,OAAO,sBAAsB,CAAC,QAAQ,CAAC;IACzC,CAAC;IAED;;OAEG;IACG,2DAA0B,GAAhC;0CAIG,OAAO,YAHR,MAAc,EACd,WAAgB,EAChB,QAA8C;;;YAA9C,yBAAA,EAAA,mBAA8C;;;;wBAExC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBAC7B,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;wBAGvB,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;wBAG9D,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;wBAC5C,qBAAM,8CAAiB,CAAC,GAAG,CAAM,QAAQ,CAAC,EAAA;;wBAAzD,YAAY,GAAG,SAA0C;wBAC/D,IAAI,YAAY,EAAE,CAAC;4BACjB,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;4BACzB,sBAAO;oCACL,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,YAAY;oCAClB,MAAM,EAAE,IAAI;oCACZ,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;iCACvC,EAAC;wBACJ,CAAC;6BAGG,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAA/B,wBAA+B;wBACT,qBAAM,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,EAAA;;wBAAvE,eAAe,GAAG,SAAqD;wBAC7E,IAAI,eAAe,EAAE,CAAC;4BACpB,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC;4BACpC,sBAAO,eAAe,EAAC;wBACzB,CAAC;;;oBAGH,uBAAuB;oBACvB,sBAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;4BACjC,IAAM,YAAY,GAAiB;gCACjC,EAAE,EAAE,KAAI,CAAC,iBAAiB,EAAE;gCAC5B,MAAM,QAAA;gCACN,WAAW,aAAA;gCACX,OAAO,SAAA;gCACP,MAAM,QAAA;gCACN,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gCACrB,QAAQ,UAAA;6BACT,CAAC;4BAEF,KAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;wBAClD,CAAC,CAAC,EAAC;;;;KACJ;IAED;;OAEG;IACK,2CAAU,GAAlB,UAAmB,SAAiB,EAAE,OAAqB;QAA3D,iBAyBC;QAxBC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAC1C,CAAC;QAED,IAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;QACnD,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpB,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;QAE/B,mBAAmB;QACnB,KAAK,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;YACd,IAAM,aAAa,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;YACrD,OAAO,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,qCAAqC;QACrC,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC/B,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5C,iCAAiC;YACjC,IAAM,KAAK,GAAG,UAAU,CAAC;gBACvB,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC/B,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAC/B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED;;OAEG;IACW,6CAAY,GAA1B,UAA2B,SAAiB;uCAAG,OAAO;;;;;wBAC9C,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;wBAClD,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;4BAC1E,sBAAO;wBACT,CAAC;wBAED,2BAA2B;wBAC3B,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;wBACtC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;wBAGjC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;wBAC9C,IAAI,KAAK,EAAE,CAAC;4BACV,YAAY,CAAC,KAAK,CAAC,CAAC;4BACpB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;wBACrC,CAAC;wBAEK,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;wBACjC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;;;;6BAIvB,CAAA,IAAI,CAAC,MAAM,CAAC,0BAA0B,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,EAA1D,wBAA0D;wBAC5D,qBAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,OAAO,CAAC,EAAA;;wBAAjD,SAAiD,CAAC;;4BAElD,qBAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,OAAO,CAAC,EAAA;;wBAAjD,SAAiD,CAAC;;;wBAG9C,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBAC9C,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,cAAc,CAAC,CAAC;;;;wBAGtF,OAAO,CAAC,KAAK,CAAC,gDAAyC,SAAS,MAAG,EAAE,OAAK,CAAC,CAAC;wBAC5E,+BAA+B;wBAC/B,KAAK,CAAC,OAAO,CAAC,UAAA,OAAO;4BACnB,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,mCAA4B,OAAK,CAAE,CAAC,CAAC,CAAC;wBACjE,CAAC,CAAC,CAAC;;;wBAEH,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;;;;;;KAE5C;IAED;;OAEG;IACW,uDAAsB,GAApC,UAAqC,KAAqB,EAAE,OAAe;uCAAG,OAAO;;;;;;wBAC7E,kBAAkB,GAAG,KAAK,CAAC,GAAG,CAAC,UAAO,OAAO;;;;;;wCAEhC,qBAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,OAAO,CAAC,EAAA;;wCAA9D,MAAM,GAAG,SAAqD;wCACpE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;;;;wCAExB,OAAO,CAAC,MAAM,CAAC,OAAK,CAAC,CAAC;;;;;6BAEzB,CAAC,CAAC;wBAEH,qBAAM,OAAO,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAAA;;wBAA5C,SAA4C,CAAC;;;;;KAC9C;IAED;;OAEG;IACW,uDAAsB,GAApC,UAAqC,KAAqB,EAAE,OAAe;uCAAG,OAAO;;;;;8BACxD,EAAL,eAAK;;;6BAAL,CAAA,mBAAK,CAAA;wBAAhB,OAAO;;;;wBAEC,qBAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,OAAO,CAAC,EAAA;;wBAA9D,MAAM,GAAG,SAAqD;wBACpE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;;;;wBAExB,OAAO,CAAC,MAAM,CAAC,OAAK,CAAC,CAAC;;;wBALJ,IAAK,CAAA;;;;;;KAQ5B;IAED;;OAEG;IACW,yDAAwB,GAAtC,UACE,OAAqB,EACrB,OAAe;uCACd,OAAO;;;;;;wBACF,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;;;;wBAIJ,qBAAM,+CAAsB,CAAC,4BAA4B,CAAC;gCAC/E,MAAM,EAAE,OAAO,CAAC,MAAM;gCACtB,aAAa,EAAE,CAAA,MAAA,OAAO,CAAC,WAAW,CAAC,aAAa,0CAAE,GAAG,CAAC,UAAC,KAAU,IAAK,OAAA,CAAC;oCACrE,KAAK,EAAE,KAAK,CAAC,SAAS;oCACtB,KAAK,EAAE,KAAK,CAAC,UAAU;oCACvB,UAAU,EAAE,KAAK,CAAC,eAAe;iCAClC,CAAC,EAJoE,CAIpE,CAAC,KAAI,EAAE;gCACT,UAAU,EAAE,MAAA,OAAO,CAAC,WAAW,CAAC,gBAAgB,0CAAE,cAAc;gCAChE,SAAS,EAAE,IAAI,CAAC,oBAAoB,CAAC,MAAA,OAAO,CAAC,WAAW,CAAC,WAAW,0CAAE,SAAS,CAAC;gCAChF,aAAa,EAAE,UAAU;gCACzB,YAAY,EAAE,CAAA,MAAA,OAAO,CAAC,WAAW,CAAC,WAAW,0CAAE,YAAY,KAAI,EAAE;gCACjE,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAA,OAAO,CAAC,WAAW,CAAC,WAAW,0CAAE,MAAM,CAAC;6BACxE,CAAC,EAAA;;wBAZI,cAAc,GAAG,SAYrB;wBAEE,cAAc,SAAA,CAAC;6BACf,cAAc,CAAC,OAAO,EAAtB,wBAAsB;wBACxB,cAAc,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,CAAC,IAAI,EAAE,CAAC;;4BAG7C,qBAAM,6BAAa,CAAC,4BAA4B,CAC/D,OAAO,CAAC,WAAW,CAAC,aAAa,IAAI,EAAE,EACvC,OAAO,CAAC,WAAW,CAAC,gBAAgB,EACpC,OAAO,CAAC,WAAW,CAAC,WAAW,EAC/B,IAAI,EAAE,4CAA4C;wBAClD,OAAO,CAAC,MAAM,CACf,EAAA;;wBAPD,sCAAsC;wBACtC,cAAc,GAAG,SAMhB,CAAC;;;wBAGJ,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;4BAC5B,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,iBAAiB,CAAC,CAAC;wBAC7D,CAAC;wBAGK,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;wBACtE,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;wBACzE,qBAAM,8CAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,cAAc,CAAC,IAAI,EAAE;gCACzD,GAAG,EAAE,OAAO,EAAE,6BAA6B;gCAC3C,IAAI,EAAE,CAAC,iBAAiB,EAAE,eAAe,EAAE,OAAO,CAAC,MAAM,CAAC;6BAC3D,CAAC,EAAA;;wBAHF,SAGE,CAAC;wBAEH,sBAAO;gCACL,OAAO,EAAE,IAAI;gCACb,IAAI,EAAE,cAAc,CAAC,IAAI;gCACzB,OAAO,SAAA;gCACP,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;6BACvC,EAAC;;;wBAGF,sBAAO;gCACL,OAAO,EAAE,KAAK;gCACd,KAAK,EAAG,OAAe,CAAC,OAAO;gCAC/B,OAAO,SAAA;gCACP,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;6BACvC,EAAC;;;;;KAEL;IAED;;OAEG;IACK,yDAAwB,GAAhC,UAAiC,WAAgB;;QAC/C,IAAM,GAAG,GAAG;YACV,UAAU,EAAE,CAAA,MAAA,WAAW,CAAC,gBAAgB,0CAAE,cAAc,KAAI,EAAE;YAC9D,WAAW,EAAE,CAAA,MAAA,WAAW,CAAC,gBAAgB,0CAAE,WAAW,KAAI,EAAE;YAC5D,SAAS,EAAE,CAAA,MAAA,WAAW,CAAC,WAAW,0CAAE,SAAS,KAAI,EAAE;YACnD,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,aAAa,IAAI,EAAE,CAAC;SAC7D,CAAC;QAEF,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACK,iDAAgB,GAAxB,UAAyB,MAAc,EAAE,SAAiB;QACxD,OAAO,yBAAkB,SAAS,cAAI,MAAM,CAAE,CAAC;IACjD,CAAC;IAED;;OAEG;IACK,2CAAU,GAAlB,UAAmB,MAAa;QAC9B,IAAM,YAAY,GAAG,MAAM;aACxB,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,UAAG,KAAK,CAAC,SAAS,cAAI,KAAK,CAAC,UAAU,CAAE,EAAxC,CAAwC,CAAC;aACtD,IAAI,EAAE;aACN,IAAI,CAAC,GAAG,CAAC,CAAC;QAEb,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,IAAM,IAAI,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACxC,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YACnC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;QACrB,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACW,yDAAwB,GAAtC,UAAuC,SAAiB;uCAAG,OAAO;;;gBAChE,6DAA6D;gBAC7D,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC1C,iCAAiC;oBACjC,sBAAO,IAAI,OAAO,CAAC,UAAC,OAAO;4BACzB,IAAM,aAAa,GAAG,WAAW,CAAC;gCAChC,IAAI,CAAC,KAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;oCAC3C,aAAa,CAAC,aAAa,CAAC,CAAC;oCAC7B,wBAAwB;oCACxB,IAAM,QAAQ,GAAG,KAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;oCAC5D,8CAAiB,CAAC,GAAG,CAAM,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAA,MAAM;wCAC9C,IAAI,MAAM,EAAE,CAAC;4CACX,OAAO,CAAC;gDACN,OAAO,EAAE,IAAI;gDACb,IAAI,EAAE,MAAM;gDACZ,MAAM,EAAE,IAAI;6CACb,CAAC,CAAC;wCACL,CAAC;6CAAM,CAAC;4CACN,OAAO,CAAC,IAAI,CAAC,CAAC;wCAChB,CAAC;oCACH,CAAC,CAAC,CAAC;gCACL,CAAC;4BACH,CAAC,EAAE,GAAG,CAAC,CAAC;wBACV,CAAC,CAAC,EAAC;gBACL,CAAC;gBACD,sBAAO,IAAI,EAAC;;;KACb;IAED;;OAEG;IACK,kDAAiB,GAAzB;QACE,OAAO,cAAO,IAAI,CAAC,GAAG,EAAE,cAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE,CAAC;IACxE,CAAC;IAEO,gDAAe,GAAvB;QACE,OAAO,gBAAS,IAAI,CAAC,GAAG,EAAE,cAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE,CAAC;IAC1E,CAAC;IAEO,qDAAoB,GAA5B,UAA6B,SAAiB;QAC5C,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC;YAC9B,KAAK,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC;YAC5B,KAAK,UAAU,CAAC,CAAC,OAAO,EAAE,CAAC;YAC3B,KAAK,WAAW,CAAC,CAAC,OAAO,EAAE,CAAC;YAC5B,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAEO,kDAAiB,GAAzB,UAA0B,MAAc;QACtC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;YACtB,KAAK,UAAU,CAAC,CAAC,OAAO,GAAG,CAAC;YAC5B,KAAK,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC;YACzB,OAAO,CAAC,CAAC,OAAO,GAAG,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qDAAoB,GAA5B;QAAA,iBA0BC;QAzBC,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;QAEzC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,UAAC,EAAkB;gBAAjB,SAAS,QAAA,EAAE,KAAK,QAAA;YACnE,IAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,UAAA,OAAO,IAAI,OAAA,GAAG,GAAG,OAAO,CAAC,SAAS,GAAG,MAAM,EAAhC,CAAgC,CAAC,CAAC;YAC7E,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1B,wCAAwC;gBACxC,UAAU,CAAC,OAAO,CAAC,UAAA,OAAO;oBACxB,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC,CAAC;gBAC5E,CAAC,CAAC,CAAC;gBAEH,eAAe;gBACf,IAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,UAAA,OAAO,IAAI,OAAA,GAAG,GAAG,OAAO,CAAC,SAAS,IAAI,MAAM,EAAjC,CAAiC,CAAC,CAAC;gBAC9E,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC1B,KAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBAClD,CAAC;qBAAM,CAAC;oBACN,KAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBACvC,IAAM,KAAK,GAAG,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBAC9C,IAAI,KAAK,EAAE,CAAC;wBACV,YAAY,CAAC,KAAK,CAAC,CAAC;wBACpB,KAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBACrC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,2CAAU,GAAV;QACE,6BACK,IAAI,CAAC,OAAO,KACf,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,CAAC;gBAC7C,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,GAAG;gBACnE,CAAC,CAAC,CAAC,EACL,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,CAAC;gBAC1C,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,GAAG;gBAC7D,CAAC,CAAC,CAAC,EACL,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,CAAC;gBAC/C,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,GAAG;gBACxE,CAAC,CAAC,CAAC,EACL,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,EACzC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,IAC9C;IACJ,CAAC;IAED;;OAEG;IACG,4CAAW,GAAjB;uCAAqB,OAAO;;;gBAC1B,IAAI,CAAC;oBACG,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;oBAClC,sBAAO,OAAO,CAAC,cAAc,GAAG,EAAE,IAAI,OAAO,CAAC,iBAAiB,GAAG,CAAC,EAAC;gBACtE,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;oBACtE,sBAAO,KAAK,EAAC;gBACf,CAAC;;;;KACF;IACH,6BAAC;AAAD,CAAC,AAxaD,IAwaC;AAxaY,wDAAsB;AA0anC,4BAA4B;AACf,QAAA,sBAAsB,GAAG,sBAAsB,CAAC,WAAW,EAAE,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/request-batching-service.ts"],
      sourcesContent: ["/**\n * Request Batching Service for Skills Analysis API\n * Implements intelligent request batching, deduplication, and concurrent processing\n * to optimize performance and reduce AI service calls\n */\n\nimport { consolidatedCache } from './consolidated-cache-service';\nimport { geminiService } from './geminiService';\nimport { edgeCaseHandlerService } from '@/lib/skills/EdgeCaseHandlerService';\n\ninterface BatchRequest {\n  id: string;\n  userId: string;\n  requestData: any;\n  resolve: (result: any) => void;\n  reject: (error: any) => void;\n  timestamp: number;\n  priority: 'high' | 'medium' | 'low';\n}\n\ninterface BatchProcessingResult {\n  success: boolean;\n  data?: any;\n  error?: string;\n  cached?: boolean;\n  batchId?: string;\n  processingTime?: number;\n}\n\ninterface BatchConfiguration {\n  maxBatchSize: number;\n  batchTimeoutMs: number;\n  maxWaitTimeMs: number;\n  enableDeduplication: boolean;\n  enableConcurrentProcessing: boolean;\n}\n\nexport class RequestBatchingService {\n  private static instance: RequestBatchingService;\n  private pendingRequests: Map<string, BatchRequest[]> = new Map();\n  private processingBatches: Set<string> = new Set();\n  private batchTimers: Map<string, NodeJS.Timeout> = new Map();\n  \n  private readonly config: BatchConfiguration = {\n    maxBatchSize: parseInt(process.env.BATCH_MAX_SIZE || '5'),\n    batchTimeoutMs: parseInt(process.env.BATCH_TIMEOUT_MS || '2000'),\n    maxWaitTimeMs: parseInt(process.env.BATCH_MAX_WAIT_MS || '5000'),\n    enableDeduplication: process.env.ENABLE_REQUEST_DEDUPLICATION !== 'false',\n    enableConcurrentProcessing: process.env.ENABLE_CONCURRENT_PROCESSING !== 'false',\n  };\n\n  // Performance metrics\n  private metrics = {\n    totalRequests: 0,\n    batchedRequests: 0,\n    deduplicatedRequests: 0,\n    cacheHits: 0,\n    averageResponseTime: 0,\n    batchSavings: 0,\n  };\n\n  private constructor() {\n    // Initialize cleanup interval\n    setInterval(() => this.cleanupStaleRequests(), 30000);\n  }\n\n  public static getInstance(): RequestBatchingService {\n    if (!RequestBatchingService.instance) {\n      RequestBatchingService.instance = new RequestBatchingService();\n    }\n    return RequestBatchingService.instance;\n  }\n\n  /**\n   * Add a comprehensive skills analysis request to the batch\n   */\n  async batchComprehensiveAnalysis(\n    userId: string,\n    requestData: any,\n    priority: 'high' | 'medium' | 'low' = 'medium'\n  ): Promise<BatchProcessingResult> {\n    const startTime = Date.now();\n    this.metrics.totalRequests++;\n\n    // Generate request signature for deduplication\n    const requestSignature = this.generateRequestSignature(requestData);\n    \n    // Check for immediate cache hit\n    const cacheKey = this.generateCacheKey(userId, requestSignature);\n    const cachedResult = await consolidatedCache.get<any>(cacheKey);\n    if (cachedResult) {\n      this.metrics.cacheHits++;\n      return {\n        success: true,\n        data: cachedResult,\n        cached: true,\n        processingTime: Date.now() - startTime,\n      };\n    }\n\n    // Check for duplicate request in current batch\n    if (this.config.enableDeduplication) {\n      const duplicateResult = await this.checkForDuplicateRequest(requestSignature);\n      if (duplicateResult) {\n        this.metrics.deduplicatedRequests++;\n        return duplicateResult;\n      }\n    }\n\n    // Create batch request\n    return new Promise((resolve, reject) => {\n      const batchRequest: BatchRequest = {\n        id: this.generateRequestId(),\n        userId,\n        requestData,\n        resolve,\n        reject,\n        timestamp: Date.now(),\n        priority,\n      };\n\n      this.addToBatch(requestSignature, batchRequest);\n    });\n  }\n\n  /**\n   * Add request to appropriate batch and trigger processing if needed\n   */\n  private addToBatch(signature: string, request: BatchRequest): void {\n    if (!this.pendingRequests.has(signature)) {\n      this.pendingRequests.set(signature, []);\n    }\n\n    const batch = this.pendingRequests.get(signature)!;\n    batch.push(request);\n    this.metrics.batchedRequests++;\n\n    // Sort by priority\n    batch.sort((a, b) => {\n      const priorityOrder = { high: 3, medium: 2, low: 1 };\n      return priorityOrder[b.priority] - priorityOrder[a.priority];\n    });\n\n    // Check if batch should be processed\n    if (batch.length >= this.config.maxBatchSize) {\n      this.processBatch(signature);\n    } else if (!this.batchTimers.has(signature)) {\n      // Set timer for batch processing\n      const timer = setTimeout(() => {\n        this.processBatch(signature);\n      }, this.config.batchTimeoutMs);\n      this.batchTimers.set(signature, timer);\n    }\n  }\n\n  /**\n   * Process a batch of similar requests\n   */\n  private async processBatch(signature: string): Promise<void> {\n    const batch = this.pendingRequests.get(signature);\n    if (!batch || batch.length === 0 || this.processingBatches.has(signature)) {\n      return;\n    }\n\n    // Mark batch as processing\n    this.processingBatches.add(signature);\n    this.pendingRequests.delete(signature);\n    \n    // Clear timer\n    const timer = this.batchTimers.get(signature);\n    if (timer) {\n      clearTimeout(timer);\n      this.batchTimers.delete(signature);\n    }\n\n    const batchId = this.generateBatchId();\n    const startTime = Date.now();\n\n    try {\n      // Process requests concurrently if enabled\n      if (this.config.enableConcurrentProcessing && batch.length > 1) {\n        await this.processConcurrentBatch(batch, batchId);\n      } else {\n        await this.processSequentialBatch(batch, batchId);\n      }\n\n      const processingTime = Date.now() - startTime;\n      this.metrics.batchSavings += Math.max(0, (batch.length - 1) * 30000 - processingTime);\n      \n    } catch (error) {\n      console.error(`Batch processing failed for signature ${signature}:`, error);\n      // Reject all requests in batch\n      batch.forEach(request => {\n        request.reject(new Error(`Batch processing failed: ${error}`));\n      });\n    } finally {\n      this.processingBatches.delete(signature);\n    }\n  }\n\n  /**\n   * Process batch requests concurrently\n   */\n  private async processConcurrentBatch(batch: BatchRequest[], batchId: string): Promise<void> {\n    const concurrentPromises = batch.map(async (request) => {\n      try {\n        const result = await this.processIndividualRequest(request, batchId);\n        request.resolve(result);\n      } catch (error) {\n        request.reject(error);\n      }\n    });\n\n    await Promise.allSettled(concurrentPromises);\n  }\n\n  /**\n   * Process batch requests sequentially (fallback)\n   */\n  private async processSequentialBatch(batch: BatchRequest[], batchId: string): Promise<void> {\n    for (const request of batch) {\n      try {\n        const result = await this.processIndividualRequest(request, batchId);\n        request.resolve(result);\n      } catch (error) {\n        request.reject(error);\n      }\n    }\n  }\n\n  /**\n   * Process individual request within batch\n   */\n  private async processIndividualRequest(\n    request: BatchRequest,\n    batchId: string\n  ): Promise<BatchProcessingResult> {\n    const startTime = Date.now();\n    \n    try {\n      // Use EdgeCaseHandler for comprehensive error handling\n      const edgeCaseResult = await edgeCaseHandlerService.handleLearningPathGeneration({\n        userId: request.userId,\n        currentSkills: request.requestData.currentSkills?.map((skill: any) => ({\n          skill: skill.skillName,\n          level: skill.selfRating,\n          confidence: skill.confidenceLevel\n        })) || [],\n        targetRole: request.requestData.targetCareerPath?.careerPathName,\n        timeframe: this.mapTimeframeToMonths(request.requestData.preferences?.timeframe),\n        learningStyle: 'balanced',\n        availability: request.requestData.preferences?.hoursPerWeek || 10,\n        budget: this.mapBudgetToAmount(request.requestData.preferences?.budget)\n      });\n\n      let analysisResult;\n      if (edgeCaseResult.success) {\n        analysisResult = { success: true, data: edgeCaseResult.data };\n      } else {\n        // Fall back to direct AI service call\n        analysisResult = await geminiService.analyzeComprehensiveSkillGap(\n          request.requestData.currentSkills || [],\n          request.requestData.targetCareerPath,\n          request.requestData.preferences,\n          null, // careerPathData will be fetched separately\n          request.userId\n        );\n      }\n\n      if (!analysisResult.success) {\n        throw new Error(analysisResult.error || 'Analysis failed');\n      }\n\n      // Cache the result\n      const requestSignature = this.generateRequestSignature(request.requestData);\n      const cacheKey = this.generateCacheKey(request.userId, requestSignature);\n      await consolidatedCache.set(cacheKey, analysisResult.data, {\n        ttl: 1800000, // 30 minutes in milliseconds\n        tags: ['skills_analysis', 'batch_request', request.userId]\n      });\n\n      return {\n        success: true,\n        data: analysisResult.data,\n        batchId,\n        processingTime: Date.now() - startTime,\n      };\n\n    } catch (error) {\n      return {\n        success: false,\n        error: (error as Error).message,\n        batchId,\n        processingTime: Date.now() - startTime,\n      };\n    }\n  }\n\n  /**\n   * Generate request signature for deduplication\n   */\n  private generateRequestSignature(requestData: any): string {\n    const key = {\n      careerPath: requestData.targetCareerPath?.careerPathName || '',\n      targetLevel: requestData.targetCareerPath?.targetLevel || '',\n      timeframe: requestData.preferences?.timeframe || '',\n      skillsHash: this.hashSkills(requestData.currentSkills || []),\n    };\n    \n    return Buffer.from(JSON.stringify(key)).toString('base64').slice(0, 32);\n  }\n\n  /**\n   * Generate cache key for request\n   */\n  private generateCacheKey(userId: string, signature: string): string {\n    return `batch_analysis:${signature}:${userId}`;\n  }\n\n  /**\n   * Hash skills array for signature generation\n   */\n  private hashSkills(skills: any[]): string {\n    const skillsString = skills\n      .map(skill => `${skill.skillName}:${skill.selfRating}`)\n      .sort()\n      .join('|');\n    \n    let hash = 0;\n    for (let i = 0; i < skillsString.length; i++) {\n      const char = skillsString.charCodeAt(i);\n      hash = ((hash << 5) - hash) + char;\n      hash = hash & hash;\n    }\n    return Math.abs(hash).toString(36);\n  }\n\n  /**\n   * Check for duplicate request in processing batches\n   */\n  private async checkForDuplicateRequest(signature: string): Promise<BatchProcessingResult | null> {\n    // Check if there's already a batch processing this signature\n    if (this.processingBatches.has(signature)) {\n      // Wait for the batch to complete\n      return new Promise((resolve) => {\n        const checkInterval = setInterval(() => {\n          if (!this.processingBatches.has(signature)) {\n            clearInterval(checkInterval);\n            // Try to get from cache\n            const cacheKey = this.generateCacheKey('shared', signature);\n            consolidatedCache.get<any>(cacheKey).then(cached => {\n              if (cached) {\n                resolve({\n                  success: true,\n                  data: cached,\n                  cached: true,\n                });\n              } else {\n                resolve(null);\n              }\n            });\n          }\n        }, 100);\n      });\n    }\n    return null;\n  }\n\n  /**\n   * Utility methods\n   */\n  private generateRequestId(): string {\n    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  private generateBatchId(): string {\n    return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  private mapTimeframeToMonths(timeframe: string): number {\n    switch (timeframe) {\n      case 'THREE_MONTHS': return 3;\n      case 'SIX_MONTHS': return 6;\n      case 'ONE_YEAR': return 12;\n      case 'TWO_YEARS': return 24;\n      default: return 12;\n    }\n  }\n\n  private mapBudgetToAmount(budget: string): number {\n    switch (budget) {\n      case 'FREE': return 0;\n      case 'FREEMIUM': return 100;\n      case 'PAID': return 1000;\n      default: return 500;\n    }\n  }\n\n  /**\n   * Cleanup stale requests\n   */\n  private cleanupStaleRequests(): void {\n    const now = Date.now();\n    const maxAge = this.config.maxWaitTimeMs;\n\n    Array.from(this.pendingRequests.entries()).forEach(([signature, batch]) => {\n      const staleBatch = batch.filter(request => now - request.timestamp > maxAge);\n      if (staleBatch.length > 0) {\n        // Remove stale requests and reject them\n        staleBatch.forEach(request => {\n          request.reject(new Error('Request timeout - exceeded maximum wait time'));\n        });\n        \n        // Update batch\n        const freshBatch = batch.filter(request => now - request.timestamp <= maxAge);\n        if (freshBatch.length > 0) {\n          this.pendingRequests.set(signature, freshBatch);\n        } else {\n          this.pendingRequests.delete(signature);\n          const timer = this.batchTimers.get(signature);\n          if (timer) {\n            clearTimeout(timer);\n            this.batchTimers.delete(signature);\n          }\n        }\n      }\n    });\n  }\n\n  /**\n   * Get performance metrics\n   */\n  getMetrics() {\n    return {\n      ...this.metrics,\n      batchEfficiency: this.metrics.totalRequests > 0 \n        ? (this.metrics.batchedRequests / this.metrics.totalRequests) * 100 \n        : 0,\n      cacheHitRate: this.metrics.totalRequests > 0 \n        ? (this.metrics.cacheHits / this.metrics.totalRequests) * 100 \n        : 0,\n      deduplicationRate: this.metrics.totalRequests > 0 \n        ? (this.metrics.deduplicatedRequests / this.metrics.totalRequests) * 100 \n        : 0,\n      pendingBatches: this.pendingRequests.size,\n      processingBatches: this.processingBatches.size,\n    };\n  }\n\n  /**\n   * Health check\n   */\n  async healthCheck(): Promise<boolean> {\n    try {\n      const metrics = this.getMetrics();\n      return metrics.pendingBatches < 10 && metrics.processingBatches < 5;\n    } catch (error) {\n      console.error('Request batching service health check failed:', error);\n      return false;\n    }\n  }\n}\n\n// Export singleton instance\nexport const requestBatchingService = RequestBatchingService.getInstance();\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "4e6bfeece177095d1d779e07ea639d10b8dfdcae"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1xfb0zeef1 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1xfb0zeef1();
var __assign =
/* istanbul ignore next */
(cov_1xfb0zeef1().s[0]++,
/* istanbul ignore next */
(cov_1xfb0zeef1().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1xfb0zeef1().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_1xfb0zeef1().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_1xfb0zeef1().f[0]++;
  cov_1xfb0zeef1().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_1xfb0zeef1().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_1xfb0zeef1().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[1]++;
    cov_1xfb0zeef1().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_1xfb0zeef1().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_1xfb0zeef1().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_1xfb0zeef1().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_1xfb0zeef1().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_1xfb0zeef1().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_1xfb0zeef1().b[2][0]++;
          cov_1xfb0zeef1().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_1xfb0zeef1().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_1xfb0zeef1().s[11]++,
/* istanbul ignore next */
(cov_1xfb0zeef1().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_1xfb0zeef1().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1xfb0zeef1().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1xfb0zeef1().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[3]++;
    cov_1xfb0zeef1().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1xfb0zeef1().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_1xfb0zeef1().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1xfb0zeef1().f[4]++;
      cov_1xfb0zeef1().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_1xfb0zeef1().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_1xfb0zeef1().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1xfb0zeef1().f[6]++;
      cov_1xfb0zeef1().s[15]++;
      try {
        /* istanbul ignore next */
        cov_1xfb0zeef1().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1xfb0zeef1().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1xfb0zeef1().f[7]++;
      cov_1xfb0zeef1().s[18]++;
      try {
        /* istanbul ignore next */
        cov_1xfb0zeef1().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1xfb0zeef1().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1xfb0zeef1().f[8]++;
      cov_1xfb0zeef1().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1xfb0zeef1().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1xfb0zeef1().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1xfb0zeef1().s[23]++,
/* istanbul ignore next */
(cov_1xfb0zeef1().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_1xfb0zeef1().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1xfb0zeef1().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1xfb0zeef1().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_1xfb0zeef1().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1xfb0zeef1().f[10]++;
        cov_1xfb0zeef1().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1xfb0zeef1().b[9][0]++;
          cov_1xfb0zeef1().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1xfb0zeef1().b[9][1]++;
        }
        cov_1xfb0zeef1().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1xfb0zeef1().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1xfb0zeef1().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1xfb0zeef1().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1xfb0zeef1().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1xfb0zeef1().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[11]++;
    cov_1xfb0zeef1().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[12]++;
    cov_1xfb0zeef1().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1xfb0zeef1().f[13]++;
      cov_1xfb0zeef1().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[14]++;
    cov_1xfb0zeef1().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_1xfb0zeef1().b[12][0]++;
      cov_1xfb0zeef1().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1xfb0zeef1().b[12][1]++;
    }
    cov_1xfb0zeef1().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_1xfb0zeef1().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_1xfb0zeef1().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1xfb0zeef1().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1xfb0zeef1().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1xfb0zeef1().s[36]++;
      try {
        /* istanbul ignore next */
        cov_1xfb0zeef1().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1xfb0zeef1().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_1xfb0zeef1().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1xfb0zeef1().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1xfb0zeef1().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1xfb0zeef1().b[18][0]++,
        /* istanbul ignore next */
        (cov_1xfb0zeef1().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1xfb0zeef1().b[19][1]++,
        /* istanbul ignore next */
        (cov_1xfb0zeef1().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1xfb0zeef1().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1xfb0zeef1().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1xfb0zeef1().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1xfb0zeef1().b[15][0]++;
          cov_1xfb0zeef1().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1xfb0zeef1().b[15][1]++;
        }
        cov_1xfb0zeef1().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1xfb0zeef1().b[21][0]++;
          cov_1xfb0zeef1().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1xfb0zeef1().b[21][1]++;
        }
        cov_1xfb0zeef1().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[22][1]++;
            cov_1xfb0zeef1().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[22][2]++;
            cov_1xfb0zeef1().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[22][3]++;
            cov_1xfb0zeef1().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[22][4]++;
            cov_1xfb0zeef1().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[22][5]++;
            cov_1xfb0zeef1().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1xfb0zeef1().b[23][0]++;
              cov_1xfb0zeef1().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1xfb0zeef1().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1xfb0zeef1().b[23][1]++;
            }
            cov_1xfb0zeef1().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1xfb0zeef1().b[26][0]++;
              cov_1xfb0zeef1().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1xfb0zeef1().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1xfb0zeef1().b[26][1]++;
            }
            cov_1xfb0zeef1().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1xfb0zeef1().b[28][0]++;
              cov_1xfb0zeef1().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1xfb0zeef1().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_1xfb0zeef1().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1xfb0zeef1().b[28][1]++;
            }
            cov_1xfb0zeef1().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1xfb0zeef1().b[30][0]++;
              cov_1xfb0zeef1().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1xfb0zeef1().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1xfb0zeef1().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1xfb0zeef1().b[30][1]++;
            }
            cov_1xfb0zeef1().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1xfb0zeef1().b[32][0]++;
              cov_1xfb0zeef1().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1xfb0zeef1().b[32][1]++;
            }
            cov_1xfb0zeef1().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1xfb0zeef1().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1xfb0zeef1().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1xfb0zeef1().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1xfb0zeef1().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1xfb0zeef1().b[33][0]++;
      cov_1xfb0zeef1().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1xfb0zeef1().b[33][1]++;
    }
    cov_1xfb0zeef1().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[34][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_1xfb0zeef1().s[78]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1xfb0zeef1().s[79]++;
exports.requestBatchingService = exports.RequestBatchingService = void 0;
var consolidated_cache_service_1 =
/* istanbul ignore next */
(cov_1xfb0zeef1().s[80]++, require("./consolidated-cache-service"));
var geminiService_1 =
/* istanbul ignore next */
(cov_1xfb0zeef1().s[81]++, require("./geminiService"));
var EdgeCaseHandlerService_1 =
/* istanbul ignore next */
(cov_1xfb0zeef1().s[82]++, require("@/lib/skills/EdgeCaseHandlerService"));
var RequestBatchingService =
/* istanbul ignore next */
(/** @class */cov_1xfb0zeef1().s[83]++, function () {
  /* istanbul ignore next */
  cov_1xfb0zeef1().f[15]++;
  function RequestBatchingService() {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[16]++;
    var _this =
    /* istanbul ignore next */
    (cov_1xfb0zeef1().s[84]++, this);
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[85]++;
    this.pendingRequests = new Map();
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[86]++;
    this.processingBatches = new Set();
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[87]++;
    this.batchTimers = new Map();
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[88]++;
    this.config = {
      maxBatchSize: parseInt(
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[35][0]++, process.env.BATCH_MAX_SIZE) ||
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[35][1]++, '5')),
      batchTimeoutMs: parseInt(
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[36][0]++, process.env.BATCH_TIMEOUT_MS) ||
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[36][1]++, '2000')),
      maxWaitTimeMs: parseInt(
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[37][0]++, process.env.BATCH_MAX_WAIT_MS) ||
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[37][1]++, '5000')),
      enableDeduplication: process.env.ENABLE_REQUEST_DEDUPLICATION !== 'false',
      enableConcurrentProcessing: process.env.ENABLE_CONCURRENT_PROCESSING !== 'false'
    };
    // Performance metrics
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[89]++;
    this.metrics = {
      totalRequests: 0,
      batchedRequests: 0,
      deduplicatedRequests: 0,
      cacheHits: 0,
      averageResponseTime: 0,
      batchSavings: 0
    };
    // Initialize cleanup interval
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[90]++;
    setInterval(function () {
      /* istanbul ignore next */
      cov_1xfb0zeef1().f[17]++;
      cov_1xfb0zeef1().s[91]++;
      return _this.cleanupStaleRequests();
    }, 30000);
  }
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[92]++;
  RequestBatchingService.getInstance = function () {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[18]++;
    cov_1xfb0zeef1().s[93]++;
    if (!RequestBatchingService.instance) {
      /* istanbul ignore next */
      cov_1xfb0zeef1().b[38][0]++;
      cov_1xfb0zeef1().s[94]++;
      RequestBatchingService.instance = new RequestBatchingService();
    } else
    /* istanbul ignore next */
    {
      cov_1xfb0zeef1().b[38][1]++;
    }
    cov_1xfb0zeef1().s[95]++;
    return RequestBatchingService.instance;
  };
  /**
   * Add a comprehensive skills analysis request to the batch
   */
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[96]++;
  RequestBatchingService.prototype.batchComprehensiveAnalysis = function (userId_1, requestData_1) {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[19]++;
    cov_1xfb0zeef1().s[97]++;
    return __awaiter(this, arguments, Promise, function (userId, requestData, priority) {
      /* istanbul ignore next */
      cov_1xfb0zeef1().f[20]++;
      var startTime, requestSignature, cacheKey, cachedResult, duplicateResult;
      var _this =
      /* istanbul ignore next */
      (cov_1xfb0zeef1().s[98]++, this);
      /* istanbul ignore next */
      cov_1xfb0zeef1().s[99]++;
      if (priority === void 0) {
        /* istanbul ignore next */
        cov_1xfb0zeef1().b[39][0]++;
        cov_1xfb0zeef1().s[100]++;
        priority = 'medium';
      } else
      /* istanbul ignore next */
      {
        cov_1xfb0zeef1().b[39][1]++;
      }
      cov_1xfb0zeef1().s[101]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1xfb0zeef1().f[21]++;
        cov_1xfb0zeef1().s[102]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[40][0]++;
            cov_1xfb0zeef1().s[103]++;
            startTime = Date.now();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[104]++;
            this.metrics.totalRequests++;
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[105]++;
            requestSignature = this.generateRequestSignature(requestData);
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[106]++;
            cacheKey = this.generateCacheKey(userId, requestSignature);
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[107]++;
            return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.get(cacheKey)];
          case 1:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[40][1]++;
            cov_1xfb0zeef1().s[108]++;
            cachedResult = _a.sent();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[109]++;
            if (cachedResult) {
              /* istanbul ignore next */
              cov_1xfb0zeef1().b[41][0]++;
              cov_1xfb0zeef1().s[110]++;
              this.metrics.cacheHits++;
              /* istanbul ignore next */
              cov_1xfb0zeef1().s[111]++;
              return [2 /*return*/, {
                success: true,
                data: cachedResult,
                cached: true,
                processingTime: Date.now() - startTime
              }];
            } else
            /* istanbul ignore next */
            {
              cov_1xfb0zeef1().b[41][1]++;
            }
            cov_1xfb0zeef1().s[112]++;
            if (!this.config.enableDeduplication) {
              /* istanbul ignore next */
              cov_1xfb0zeef1().b[42][0]++;
              cov_1xfb0zeef1().s[113]++;
              return [3 /*break*/, 3];
            } else
            /* istanbul ignore next */
            {
              cov_1xfb0zeef1().b[42][1]++;
            }
            cov_1xfb0zeef1().s[114]++;
            return [4 /*yield*/, this.checkForDuplicateRequest(requestSignature)];
          case 2:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[40][2]++;
            cov_1xfb0zeef1().s[115]++;
            duplicateResult = _a.sent();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[116]++;
            if (duplicateResult) {
              /* istanbul ignore next */
              cov_1xfb0zeef1().b[43][0]++;
              cov_1xfb0zeef1().s[117]++;
              this.metrics.deduplicatedRequests++;
              /* istanbul ignore next */
              cov_1xfb0zeef1().s[118]++;
              return [2 /*return*/, duplicateResult];
            } else
            /* istanbul ignore next */
            {
              cov_1xfb0zeef1().b[43][1]++;
            }
            cov_1xfb0zeef1().s[119]++;
            _a.label = 3;
          case 3:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[40][3]++;
            cov_1xfb0zeef1().s[120]++;
            // Create batch request
            return [2 /*return*/, new Promise(function (resolve, reject) {
              /* istanbul ignore next */
              cov_1xfb0zeef1().f[22]++;
              var batchRequest =
              /* istanbul ignore next */
              (cov_1xfb0zeef1().s[121]++, {
                id: _this.generateRequestId(),
                userId: userId,
                requestData: requestData,
                resolve: resolve,
                reject: reject,
                timestamp: Date.now(),
                priority: priority
              });
              /* istanbul ignore next */
              cov_1xfb0zeef1().s[122]++;
              _this.addToBatch(requestSignature, batchRequest);
            })];
        }
      });
    });
  };
  /**
   * Add request to appropriate batch and trigger processing if needed
   */
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[123]++;
  RequestBatchingService.prototype.addToBatch = function (signature, request) {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[23]++;
    var _this =
    /* istanbul ignore next */
    (cov_1xfb0zeef1().s[124]++, this);
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[125]++;
    if (!this.pendingRequests.has(signature)) {
      /* istanbul ignore next */
      cov_1xfb0zeef1().b[44][0]++;
      cov_1xfb0zeef1().s[126]++;
      this.pendingRequests.set(signature, []);
    } else
    /* istanbul ignore next */
    {
      cov_1xfb0zeef1().b[44][1]++;
    }
    var batch =
    /* istanbul ignore next */
    (cov_1xfb0zeef1().s[127]++, this.pendingRequests.get(signature));
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[128]++;
    batch.push(request);
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[129]++;
    this.metrics.batchedRequests++;
    // Sort by priority
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[130]++;
    batch.sort(function (a, b) {
      /* istanbul ignore next */
      cov_1xfb0zeef1().f[24]++;
      var priorityOrder =
      /* istanbul ignore next */
      (cov_1xfb0zeef1().s[131]++, {
        high: 3,
        medium: 2,
        low: 1
      });
      /* istanbul ignore next */
      cov_1xfb0zeef1().s[132]++;
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
    // Check if batch should be processed
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[133]++;
    if (batch.length >= this.config.maxBatchSize) {
      /* istanbul ignore next */
      cov_1xfb0zeef1().b[45][0]++;
      cov_1xfb0zeef1().s[134]++;
      this.processBatch(signature);
    } else {
      /* istanbul ignore next */
      cov_1xfb0zeef1().b[45][1]++;
      cov_1xfb0zeef1().s[135]++;
      if (!this.batchTimers.has(signature)) {
        /* istanbul ignore next */
        cov_1xfb0zeef1().b[46][0]++;
        // Set timer for batch processing
        var timer =
        /* istanbul ignore next */
        (cov_1xfb0zeef1().s[136]++, setTimeout(function () {
          /* istanbul ignore next */
          cov_1xfb0zeef1().f[25]++;
          cov_1xfb0zeef1().s[137]++;
          _this.processBatch(signature);
        }, this.config.batchTimeoutMs));
        /* istanbul ignore next */
        cov_1xfb0zeef1().s[138]++;
        this.batchTimers.set(signature, timer);
      } else
      /* istanbul ignore next */
      {
        cov_1xfb0zeef1().b[46][1]++;
      }
    }
  };
  /**
   * Process a batch of similar requests
   */
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[139]++;
  RequestBatchingService.prototype.processBatch = function (signature) {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[26]++;
    cov_1xfb0zeef1().s[140]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1xfb0zeef1().f[27]++;
      var batch, timer, batchId, startTime, processingTime, error_1;
      /* istanbul ignore next */
      cov_1xfb0zeef1().s[141]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1xfb0zeef1().f[28]++;
        cov_1xfb0zeef1().s[142]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[47][0]++;
            cov_1xfb0zeef1().s[143]++;
            batch = this.pendingRequests.get(signature);
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[144]++;
            if (
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[49][0]++, !batch) ||
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[49][1]++, batch.length === 0) ||
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[49][2]++, this.processingBatches.has(signature))) {
              /* istanbul ignore next */
              cov_1xfb0zeef1().b[48][0]++;
              cov_1xfb0zeef1().s[145]++;
              return [2 /*return*/];
            } else
            /* istanbul ignore next */
            {
              cov_1xfb0zeef1().b[48][1]++;
            }
            // Mark batch as processing
            cov_1xfb0zeef1().s[146]++;
            this.processingBatches.add(signature);
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[147]++;
            this.pendingRequests.delete(signature);
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[148]++;
            timer = this.batchTimers.get(signature);
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[149]++;
            if (timer) {
              /* istanbul ignore next */
              cov_1xfb0zeef1().b[50][0]++;
              cov_1xfb0zeef1().s[150]++;
              clearTimeout(timer);
              /* istanbul ignore next */
              cov_1xfb0zeef1().s[151]++;
              this.batchTimers.delete(signature);
            } else
            /* istanbul ignore next */
            {
              cov_1xfb0zeef1().b[50][1]++;
            }
            cov_1xfb0zeef1().s[152]++;
            batchId = this.generateBatchId();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[153]++;
            startTime = Date.now();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[154]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[47][1]++;
            cov_1xfb0zeef1().s[155]++;
            _a.trys.push([1, 6, 7, 8]);
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[156]++;
            if (!(
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[52][0]++, this.config.enableConcurrentProcessing) &&
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[52][1]++, batch.length > 1))) {
              /* istanbul ignore next */
              cov_1xfb0zeef1().b[51][0]++;
              cov_1xfb0zeef1().s[157]++;
              return [3 /*break*/, 3];
            } else
            /* istanbul ignore next */
            {
              cov_1xfb0zeef1().b[51][1]++;
            }
            cov_1xfb0zeef1().s[158]++;
            return [4 /*yield*/, this.processConcurrentBatch(batch, batchId)];
          case 2:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[47][2]++;
            cov_1xfb0zeef1().s[159]++;
            _a.sent();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[160]++;
            return [3 /*break*/, 5];
          case 3:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[47][3]++;
            cov_1xfb0zeef1().s[161]++;
            return [4 /*yield*/, this.processSequentialBatch(batch, batchId)];
          case 4:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[47][4]++;
            cov_1xfb0zeef1().s[162]++;
            _a.sent();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[163]++;
            _a.label = 5;
          case 5:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[47][5]++;
            cov_1xfb0zeef1().s[164]++;
            processingTime = Date.now() - startTime;
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[165]++;
            this.metrics.batchSavings += Math.max(0, (batch.length - 1) * 30000 - processingTime);
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[166]++;
            return [3 /*break*/, 8];
          case 6:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[47][6]++;
            cov_1xfb0zeef1().s[167]++;
            error_1 = _a.sent();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[168]++;
            console.error("Batch processing failed for signature ".concat(signature, ":"), error_1);
            // Reject all requests in batch
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[169]++;
            batch.forEach(function (request) {
              /* istanbul ignore next */
              cov_1xfb0zeef1().f[29]++;
              cov_1xfb0zeef1().s[170]++;
              request.reject(new Error("Batch processing failed: ".concat(error_1)));
            });
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[171]++;
            return [3 /*break*/, 8];
          case 7:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[47][7]++;
            cov_1xfb0zeef1().s[172]++;
            this.processingBatches.delete(signature);
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[173]++;
            return [7 /*endfinally*/];
          case 8:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[47][8]++;
            cov_1xfb0zeef1().s[174]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Process batch requests concurrently
   */
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[175]++;
  RequestBatchingService.prototype.processConcurrentBatch = function (batch, batchId) {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[30]++;
    cov_1xfb0zeef1().s[176]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1xfb0zeef1().f[31]++;
      var concurrentPromises;
      var _this =
      /* istanbul ignore next */
      (cov_1xfb0zeef1().s[177]++, this);
      /* istanbul ignore next */
      cov_1xfb0zeef1().s[178]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1xfb0zeef1().f[32]++;
        cov_1xfb0zeef1().s[179]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[53][0]++;
            cov_1xfb0zeef1().s[180]++;
            concurrentPromises = batch.map(function (request) {
              /* istanbul ignore next */
              cov_1xfb0zeef1().f[33]++;
              cov_1xfb0zeef1().s[181]++;
              return __awaiter(_this, void 0, void 0, function () {
                /* istanbul ignore next */
                cov_1xfb0zeef1().f[34]++;
                var result, error_2;
                /* istanbul ignore next */
                cov_1xfb0zeef1().s[182]++;
                return __generator(this, function (_a) {
                  /* istanbul ignore next */
                  cov_1xfb0zeef1().f[35]++;
                  cov_1xfb0zeef1().s[183]++;
                  switch (_a.label) {
                    case 0:
                      /* istanbul ignore next */
                      cov_1xfb0zeef1().b[54][0]++;
                      cov_1xfb0zeef1().s[184]++;
                      _a.trys.push([0, 2,, 3]);
                      /* istanbul ignore next */
                      cov_1xfb0zeef1().s[185]++;
                      return [4 /*yield*/, this.processIndividualRequest(request, batchId)];
                    case 1:
                      /* istanbul ignore next */
                      cov_1xfb0zeef1().b[54][1]++;
                      cov_1xfb0zeef1().s[186]++;
                      result = _a.sent();
                      /* istanbul ignore next */
                      cov_1xfb0zeef1().s[187]++;
                      request.resolve(result);
                      /* istanbul ignore next */
                      cov_1xfb0zeef1().s[188]++;
                      return [3 /*break*/, 3];
                    case 2:
                      /* istanbul ignore next */
                      cov_1xfb0zeef1().b[54][2]++;
                      cov_1xfb0zeef1().s[189]++;
                      error_2 = _a.sent();
                      /* istanbul ignore next */
                      cov_1xfb0zeef1().s[190]++;
                      request.reject(error_2);
                      /* istanbul ignore next */
                      cov_1xfb0zeef1().s[191]++;
                      return [3 /*break*/, 3];
                    case 3:
                      /* istanbul ignore next */
                      cov_1xfb0zeef1().b[54][3]++;
                      cov_1xfb0zeef1().s[192]++;
                      return [2 /*return*/];
                  }
                });
              });
            });
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[193]++;
            return [4 /*yield*/, Promise.allSettled(concurrentPromises)];
          case 1:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[53][1]++;
            cov_1xfb0zeef1().s[194]++;
            _a.sent();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[195]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Process batch requests sequentially (fallback)
   */
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[196]++;
  RequestBatchingService.prototype.processSequentialBatch = function (batch, batchId) {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[36]++;
    cov_1xfb0zeef1().s[197]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1xfb0zeef1().f[37]++;
      var _i, batch_1, request, result, error_3;
      /* istanbul ignore next */
      cov_1xfb0zeef1().s[198]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1xfb0zeef1().f[38]++;
        cov_1xfb0zeef1().s[199]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[55][0]++;
            cov_1xfb0zeef1().s[200]++;
            _i = 0, batch_1 = batch;
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[201]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[55][1]++;
            cov_1xfb0zeef1().s[202]++;
            if (!(_i < batch_1.length)) {
              /* istanbul ignore next */
              cov_1xfb0zeef1().b[56][0]++;
              cov_1xfb0zeef1().s[203]++;
              return [3 /*break*/, 6];
            } else
            /* istanbul ignore next */
            {
              cov_1xfb0zeef1().b[56][1]++;
            }
            cov_1xfb0zeef1().s[204]++;
            request = batch_1[_i];
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[205]++;
            _a.label = 2;
          case 2:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[55][2]++;
            cov_1xfb0zeef1().s[206]++;
            _a.trys.push([2, 4,, 5]);
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[207]++;
            return [4 /*yield*/, this.processIndividualRequest(request, batchId)];
          case 3:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[55][3]++;
            cov_1xfb0zeef1().s[208]++;
            result = _a.sent();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[209]++;
            request.resolve(result);
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[210]++;
            return [3 /*break*/, 5];
          case 4:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[55][4]++;
            cov_1xfb0zeef1().s[211]++;
            error_3 = _a.sent();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[212]++;
            request.reject(error_3);
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[213]++;
            return [3 /*break*/, 5];
          case 5:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[55][5]++;
            cov_1xfb0zeef1().s[214]++;
            _i++;
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[215]++;
            return [3 /*break*/, 1];
          case 6:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[55][6]++;
            cov_1xfb0zeef1().s[216]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Process individual request within batch
   */
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[217]++;
  RequestBatchingService.prototype.processIndividualRequest = function (request, batchId) {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[39]++;
    cov_1xfb0zeef1().s[218]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1xfb0zeef1().f[40]++;
      var startTime, edgeCaseResult, analysisResult, requestSignature, cacheKey, error_4;
      var _a, _b, _c, _d, _e;
      /* istanbul ignore next */
      cov_1xfb0zeef1().s[219]++;
      return __generator(this, function (_f) {
        /* istanbul ignore next */
        cov_1xfb0zeef1().f[41]++;
        cov_1xfb0zeef1().s[220]++;
        switch (_f.label) {
          case 0:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[57][0]++;
            cov_1xfb0zeef1().s[221]++;
            startTime = Date.now();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[222]++;
            _f.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[57][1]++;
            cov_1xfb0zeef1().s[223]++;
            _f.trys.push([1, 7,, 8]);
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[224]++;
            return [4 /*yield*/, EdgeCaseHandlerService_1.edgeCaseHandlerService.handleLearningPathGeneration({
              userId: request.userId,
              currentSkills:
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[58][0]++,
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[60][0]++, (_a = request.requestData.currentSkills) === null) ||
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[60][1]++, _a === void 0) ?
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[59][0]++, void 0) :
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[59][1]++, _a.map(function (skill) {
                /* istanbul ignore next */
                cov_1xfb0zeef1().f[42]++;
                cov_1xfb0zeef1().s[225]++;
                return {
                  skill: skill.skillName,
                  level: skill.selfRating,
                  confidence: skill.confidenceLevel
                };
              }))) ||
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[58][1]++, []),
              targetRole:
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[62][0]++, (_b = request.requestData.targetCareerPath) === null) ||
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[62][1]++, _b === void 0) ?
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[61][0]++, void 0) :
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[61][1]++, _b.careerPathName),
              timeframe: this.mapTimeframeToMonths(
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[64][0]++, (_c = request.requestData.preferences) === null) ||
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[64][1]++, _c === void 0) ?
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[63][0]++, void 0) :
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[63][1]++, _c.timeframe)),
              learningStyle: 'balanced',
              availability:
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[65][0]++,
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[67][0]++, (_d = request.requestData.preferences) === null) ||
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[67][1]++, _d === void 0) ?
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[66][0]++, void 0) :
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[66][1]++, _d.hoursPerWeek)) ||
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[65][1]++, 10),
              budget: this.mapBudgetToAmount(
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[69][0]++, (_e = request.requestData.preferences) === null) ||
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[69][1]++, _e === void 0) ?
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[68][0]++, void 0) :
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[68][1]++, _e.budget))
            })];
          case 2:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[57][2]++;
            cov_1xfb0zeef1().s[226]++;
            edgeCaseResult = _f.sent();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[227]++;
            analysisResult = void 0;
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[228]++;
            if (!edgeCaseResult.success) {
              /* istanbul ignore next */
              cov_1xfb0zeef1().b[70][0]++;
              cov_1xfb0zeef1().s[229]++;
              return [3 /*break*/, 3];
            } else
            /* istanbul ignore next */
            {
              cov_1xfb0zeef1().b[70][1]++;
            }
            cov_1xfb0zeef1().s[230]++;
            analysisResult = {
              success: true,
              data: edgeCaseResult.data
            };
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[231]++;
            return [3 /*break*/, 5];
          case 3:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[57][3]++;
            cov_1xfb0zeef1().s[232]++;
            return [4 /*yield*/, geminiService_1.geminiService.analyzeComprehensiveSkillGap(
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[71][0]++, request.requestData.currentSkills) ||
            /* istanbul ignore next */
            (cov_1xfb0zeef1().b[71][1]++, []), request.requestData.targetCareerPath, request.requestData.preferences, null,
            // careerPathData will be fetched separately
            request.userId)];
          case 4:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[57][4]++;
            cov_1xfb0zeef1().s[233]++;
            // Fall back to direct AI service call
            analysisResult = _f.sent();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[234]++;
            _f.label = 5;
          case 5:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[57][5]++;
            cov_1xfb0zeef1().s[235]++;
            if (!analysisResult.success) {
              /* istanbul ignore next */
              cov_1xfb0zeef1().b[72][0]++;
              cov_1xfb0zeef1().s[236]++;
              throw new Error(
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[73][0]++, analysisResult.error) ||
              /* istanbul ignore next */
              (cov_1xfb0zeef1().b[73][1]++, 'Analysis failed'));
            } else
            /* istanbul ignore next */
            {
              cov_1xfb0zeef1().b[72][1]++;
            }
            cov_1xfb0zeef1().s[237]++;
            requestSignature = this.generateRequestSignature(request.requestData);
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[238]++;
            cacheKey = this.generateCacheKey(request.userId, requestSignature);
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[239]++;
            return [4 /*yield*/, consolidated_cache_service_1.consolidatedCache.set(cacheKey, analysisResult.data, {
              ttl: 1800000,
              // 30 minutes in milliseconds
              tags: ['skills_analysis', 'batch_request', request.userId]
            })];
          case 6:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[57][6]++;
            cov_1xfb0zeef1().s[240]++;
            _f.sent();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[241]++;
            return [2 /*return*/, {
              success: true,
              data: analysisResult.data,
              batchId: batchId,
              processingTime: Date.now() - startTime
            }];
          case 7:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[57][7]++;
            cov_1xfb0zeef1().s[242]++;
            error_4 = _f.sent();
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[243]++;
            return [2 /*return*/, {
              success: false,
              error: error_4.message,
              batchId: batchId,
              processingTime: Date.now() - startTime
            }];
          case 8:
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[57][8]++;
            cov_1xfb0zeef1().s[244]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Generate request signature for deduplication
   */
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[245]++;
  RequestBatchingService.prototype.generateRequestSignature = function (requestData) {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[43]++;
    var _a, _b, _c;
    var key =
    /* istanbul ignore next */
    (cov_1xfb0zeef1().s[246]++, {
      careerPath:
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[74][0]++,
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[76][0]++, (_a = requestData.targetCareerPath) === null) ||
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[76][1]++, _a === void 0) ?
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[75][0]++, void 0) :
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[75][1]++, _a.careerPathName)) ||
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[74][1]++, ''),
      targetLevel:
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[77][0]++,
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[79][0]++, (_b = requestData.targetCareerPath) === null) ||
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[79][1]++, _b === void 0) ?
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[78][0]++, void 0) :
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[78][1]++, _b.targetLevel)) ||
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[77][1]++, ''),
      timeframe:
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[80][0]++,
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[82][0]++, (_c = requestData.preferences) === null) ||
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[82][1]++, _c === void 0) ?
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[81][0]++, void 0) :
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[81][1]++, _c.timeframe)) ||
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[80][1]++, ''),
      skillsHash: this.hashSkills(
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[83][0]++, requestData.currentSkills) ||
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[83][1]++, []))
    });
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[247]++;
    return Buffer.from(JSON.stringify(key)).toString('base64').slice(0, 32);
  };
  /**
   * Generate cache key for request
   */
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[248]++;
  RequestBatchingService.prototype.generateCacheKey = function (userId, signature) {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[44]++;
    cov_1xfb0zeef1().s[249]++;
    return "batch_analysis:".concat(signature, ":").concat(userId);
  };
  /**
   * Hash skills array for signature generation
   */
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[250]++;
  RequestBatchingService.prototype.hashSkills = function (skills) {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[45]++;
    var skillsString =
    /* istanbul ignore next */
    (cov_1xfb0zeef1().s[251]++, skills.map(function (skill) {
      /* istanbul ignore next */
      cov_1xfb0zeef1().f[46]++;
      cov_1xfb0zeef1().s[252]++;
      return "".concat(skill.skillName, ":").concat(skill.selfRating);
    }).sort().join('|'));
    var hash =
    /* istanbul ignore next */
    (cov_1xfb0zeef1().s[253]++, 0);
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[254]++;
    for (var i =
    /* istanbul ignore next */
    (cov_1xfb0zeef1().s[255]++, 0); i < skillsString.length; i++) {
      var char =
      /* istanbul ignore next */
      (cov_1xfb0zeef1().s[256]++, skillsString.charCodeAt(i));
      /* istanbul ignore next */
      cov_1xfb0zeef1().s[257]++;
      hash = (hash << 5) - hash + char;
      /* istanbul ignore next */
      cov_1xfb0zeef1().s[258]++;
      hash = hash & hash;
    }
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[259]++;
    return Math.abs(hash).toString(36);
  };
  /**
   * Check for duplicate request in processing batches
   */
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[260]++;
  RequestBatchingService.prototype.checkForDuplicateRequest = function (signature) {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[47]++;
    cov_1xfb0zeef1().s[261]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1xfb0zeef1().f[48]++;
      var _this =
      /* istanbul ignore next */
      (cov_1xfb0zeef1().s[262]++, this);
      /* istanbul ignore next */
      cov_1xfb0zeef1().s[263]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1xfb0zeef1().f[49]++;
        cov_1xfb0zeef1().s[264]++;
        // Check if there's already a batch processing this signature
        if (this.processingBatches.has(signature)) {
          /* istanbul ignore next */
          cov_1xfb0zeef1().b[84][0]++;
          cov_1xfb0zeef1().s[265]++;
          // Wait for the batch to complete
          return [2 /*return*/, new Promise(function (resolve) {
            /* istanbul ignore next */
            cov_1xfb0zeef1().f[50]++;
            var checkInterval =
            /* istanbul ignore next */
            (cov_1xfb0zeef1().s[266]++, setInterval(function () {
              /* istanbul ignore next */
              cov_1xfb0zeef1().f[51]++;
              cov_1xfb0zeef1().s[267]++;
              if (!_this.processingBatches.has(signature)) {
                /* istanbul ignore next */
                cov_1xfb0zeef1().b[85][0]++;
                cov_1xfb0zeef1().s[268]++;
                clearInterval(checkInterval);
                // Try to get from cache
                var cacheKey =
                /* istanbul ignore next */
                (cov_1xfb0zeef1().s[269]++, _this.generateCacheKey('shared', signature));
                /* istanbul ignore next */
                cov_1xfb0zeef1().s[270]++;
                consolidated_cache_service_1.consolidatedCache.get(cacheKey).then(function (cached) {
                  /* istanbul ignore next */
                  cov_1xfb0zeef1().f[52]++;
                  cov_1xfb0zeef1().s[271]++;
                  if (cached) {
                    /* istanbul ignore next */
                    cov_1xfb0zeef1().b[86][0]++;
                    cov_1xfb0zeef1().s[272]++;
                    resolve({
                      success: true,
                      data: cached,
                      cached: true
                    });
                  } else {
                    /* istanbul ignore next */
                    cov_1xfb0zeef1().b[86][1]++;
                    cov_1xfb0zeef1().s[273]++;
                    resolve(null);
                  }
                });
              } else
              /* istanbul ignore next */
              {
                cov_1xfb0zeef1().b[85][1]++;
              }
            }, 100));
          })];
        } else
        /* istanbul ignore next */
        {
          cov_1xfb0zeef1().b[84][1]++;
        }
        cov_1xfb0zeef1().s[274]++;
        return [2 /*return*/, null];
      });
    });
  };
  /**
   * Utility methods
   */
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[275]++;
  RequestBatchingService.prototype.generateRequestId = function () {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[53]++;
    cov_1xfb0zeef1().s[276]++;
    return "req_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9));
  };
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[277]++;
  RequestBatchingService.prototype.generateBatchId = function () {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[54]++;
    cov_1xfb0zeef1().s[278]++;
    return "batch_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9));
  };
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[279]++;
  RequestBatchingService.prototype.mapTimeframeToMonths = function (timeframe) {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[55]++;
    cov_1xfb0zeef1().s[280]++;
    switch (timeframe) {
      case 'THREE_MONTHS':
        /* istanbul ignore next */
        cov_1xfb0zeef1().b[87][0]++;
        cov_1xfb0zeef1().s[281]++;
        return 3;
      case 'SIX_MONTHS':
        /* istanbul ignore next */
        cov_1xfb0zeef1().b[87][1]++;
        cov_1xfb0zeef1().s[282]++;
        return 6;
      case 'ONE_YEAR':
        /* istanbul ignore next */
        cov_1xfb0zeef1().b[87][2]++;
        cov_1xfb0zeef1().s[283]++;
        return 12;
      case 'TWO_YEARS':
        /* istanbul ignore next */
        cov_1xfb0zeef1().b[87][3]++;
        cov_1xfb0zeef1().s[284]++;
        return 24;
      default:
        /* istanbul ignore next */
        cov_1xfb0zeef1().b[87][4]++;
        cov_1xfb0zeef1().s[285]++;
        return 12;
    }
  };
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[286]++;
  RequestBatchingService.prototype.mapBudgetToAmount = function (budget) {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[56]++;
    cov_1xfb0zeef1().s[287]++;
    switch (budget) {
      case 'FREE':
        /* istanbul ignore next */
        cov_1xfb0zeef1().b[88][0]++;
        cov_1xfb0zeef1().s[288]++;
        return 0;
      case 'FREEMIUM':
        /* istanbul ignore next */
        cov_1xfb0zeef1().b[88][1]++;
        cov_1xfb0zeef1().s[289]++;
        return 100;
      case 'PAID':
        /* istanbul ignore next */
        cov_1xfb0zeef1().b[88][2]++;
        cov_1xfb0zeef1().s[290]++;
        return 1000;
      default:
        /* istanbul ignore next */
        cov_1xfb0zeef1().b[88][3]++;
        cov_1xfb0zeef1().s[291]++;
        return 500;
    }
  };
  /**
   * Cleanup stale requests
   */
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[292]++;
  RequestBatchingService.prototype.cleanupStaleRequests = function () {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[57]++;
    var _this =
    /* istanbul ignore next */
    (cov_1xfb0zeef1().s[293]++, this);
    var now =
    /* istanbul ignore next */
    (cov_1xfb0zeef1().s[294]++, Date.now());
    var maxAge =
    /* istanbul ignore next */
    (cov_1xfb0zeef1().s[295]++, this.config.maxWaitTimeMs);
    /* istanbul ignore next */
    cov_1xfb0zeef1().s[296]++;
    Array.from(this.pendingRequests.entries()).forEach(function (_a) {
      /* istanbul ignore next */
      cov_1xfb0zeef1().f[58]++;
      var signature =
        /* istanbul ignore next */
        (cov_1xfb0zeef1().s[297]++, _a[0]),
        batch =
        /* istanbul ignore next */
        (cov_1xfb0zeef1().s[298]++, _a[1]);
      var staleBatch =
      /* istanbul ignore next */
      (cov_1xfb0zeef1().s[299]++, batch.filter(function (request) {
        /* istanbul ignore next */
        cov_1xfb0zeef1().f[59]++;
        cov_1xfb0zeef1().s[300]++;
        return now - request.timestamp > maxAge;
      }));
      /* istanbul ignore next */
      cov_1xfb0zeef1().s[301]++;
      if (staleBatch.length > 0) {
        /* istanbul ignore next */
        cov_1xfb0zeef1().b[89][0]++;
        cov_1xfb0zeef1().s[302]++;
        // Remove stale requests and reject them
        staleBatch.forEach(function (request) {
          /* istanbul ignore next */
          cov_1xfb0zeef1().f[60]++;
          cov_1xfb0zeef1().s[303]++;
          request.reject(new Error('Request timeout - exceeded maximum wait time'));
        });
        // Update batch
        var freshBatch =
        /* istanbul ignore next */
        (cov_1xfb0zeef1().s[304]++, batch.filter(function (request) {
          /* istanbul ignore next */
          cov_1xfb0zeef1().f[61]++;
          cov_1xfb0zeef1().s[305]++;
          return now - request.timestamp <= maxAge;
        }));
        /* istanbul ignore next */
        cov_1xfb0zeef1().s[306]++;
        if (freshBatch.length > 0) {
          /* istanbul ignore next */
          cov_1xfb0zeef1().b[90][0]++;
          cov_1xfb0zeef1().s[307]++;
          _this.pendingRequests.set(signature, freshBatch);
        } else {
          /* istanbul ignore next */
          cov_1xfb0zeef1().b[90][1]++;
          cov_1xfb0zeef1().s[308]++;
          _this.pendingRequests.delete(signature);
          var timer =
          /* istanbul ignore next */
          (cov_1xfb0zeef1().s[309]++, _this.batchTimers.get(signature));
          /* istanbul ignore next */
          cov_1xfb0zeef1().s[310]++;
          if (timer) {
            /* istanbul ignore next */
            cov_1xfb0zeef1().b[91][0]++;
            cov_1xfb0zeef1().s[311]++;
            clearTimeout(timer);
            /* istanbul ignore next */
            cov_1xfb0zeef1().s[312]++;
            _this.batchTimers.delete(signature);
          } else
          /* istanbul ignore next */
          {
            cov_1xfb0zeef1().b[91][1]++;
          }
        }
      } else
      /* istanbul ignore next */
      {
        cov_1xfb0zeef1().b[89][1]++;
      }
    });
  };
  /**
   * Get performance metrics
   */
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[313]++;
  RequestBatchingService.prototype.getMetrics = function () {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[62]++;
    cov_1xfb0zeef1().s[314]++;
    return __assign(__assign({}, this.metrics), {
      batchEfficiency: this.metrics.totalRequests > 0 ?
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[92][0]++, this.metrics.batchedRequests / this.metrics.totalRequests * 100) :
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[92][1]++, 0),
      cacheHitRate: this.metrics.totalRequests > 0 ?
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[93][0]++, this.metrics.cacheHits / this.metrics.totalRequests * 100) :
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[93][1]++, 0),
      deduplicationRate: this.metrics.totalRequests > 0 ?
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[94][0]++, this.metrics.deduplicatedRequests / this.metrics.totalRequests * 100) :
      /* istanbul ignore next */
      (cov_1xfb0zeef1().b[94][1]++, 0),
      pendingBatches: this.pendingRequests.size,
      processingBatches: this.processingBatches.size
    });
  };
  /**
   * Health check
   */
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[315]++;
  RequestBatchingService.prototype.healthCheck = function () {
    /* istanbul ignore next */
    cov_1xfb0zeef1().f[63]++;
    cov_1xfb0zeef1().s[316]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1xfb0zeef1().f[64]++;
      var metrics;
      /* istanbul ignore next */
      cov_1xfb0zeef1().s[317]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1xfb0zeef1().f[65]++;
        cov_1xfb0zeef1().s[318]++;
        try {
          /* istanbul ignore next */
          cov_1xfb0zeef1().s[319]++;
          metrics = this.getMetrics();
          /* istanbul ignore next */
          cov_1xfb0zeef1().s[320]++;
          return [2 /*return*/,
          /* istanbul ignore next */
          (cov_1xfb0zeef1().b[95][0]++, metrics.pendingBatches < 10) &&
          /* istanbul ignore next */
          (cov_1xfb0zeef1().b[95][1]++, metrics.processingBatches < 5)];
        } catch (error) {
          /* istanbul ignore next */
          cov_1xfb0zeef1().s[321]++;
          console.error('Request batching service health check failed:', error);
          /* istanbul ignore next */
          cov_1xfb0zeef1().s[322]++;
          return [2 /*return*/, false];
        }
        /* istanbul ignore next */
        cov_1xfb0zeef1().s[323]++;
        return [2 /*return*/];
      });
    });
  };
  /* istanbul ignore next */
  cov_1xfb0zeef1().s[324]++;
  return RequestBatchingService;
}());
/* istanbul ignore next */
cov_1xfb0zeef1().s[325]++;
exports.RequestBatchingService = RequestBatchingService;
// Export singleton instance
/* istanbul ignore next */
cov_1xfb0zeef1().s[326]++;
exports.requestBatchingService = RequestBatchingService.getInstance();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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