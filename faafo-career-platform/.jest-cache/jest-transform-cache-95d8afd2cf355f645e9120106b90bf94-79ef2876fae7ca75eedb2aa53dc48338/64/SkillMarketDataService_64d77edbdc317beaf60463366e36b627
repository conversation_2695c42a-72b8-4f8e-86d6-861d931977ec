d37b591759b214d6a9df30935cdd018a
"use strict";

/* istanbul ignore next */
function cov_xt36771t1() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/SkillMarketDataService.ts";
  var hash = "2049000702a22369bc42c5a0a2ffc98ee037e0d1";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/SkillMarketDataService.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 20
        },
        end: {
          line: 57,
          column: 1
        }
      },
      "79": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 55,
          column: 5
        }
      },
      "80": {
        start: {
          line: 50,
          column: 40
        },
        end: {
          line: 55,
          column: 5
        }
      },
      "81": {
        start: {
          line: 50,
          column: 53
        },
        end: {
          line: 50,
          column: 54
        }
      },
      "82": {
        start: {
          line: 50,
          column: 60
        },
        end: {
          line: 50,
          column: 71
        }
      },
      "83": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 54,
          column: 9
        }
      },
      "84": {
        start: {
          line: 52,
          column: 12
        },
        end: {
          line: 52,
          column: 65
        }
      },
      "85": {
        start: {
          line: 52,
          column: 21
        },
        end: {
          line: 52,
          column: 65
        }
      },
      "86": {
        start: {
          line: 53,
          column: 12
        },
        end: {
          line: 53,
          column: 28
        }
      },
      "87": {
        start: {
          line: 56,
          column: 4
        },
        end: {
          line: 56,
          column: 61
        }
      },
      "88": {
        start: {
          line: 58,
          column: 0
        },
        end: {
          line: 58,
          column: 62
        }
      },
      "89": {
        start: {
          line: 59,
          column: 0
        },
        end: {
          line: 59,
          column: 40
        }
      },
      "90": {
        start: {
          line: 60,
          column: 44
        },
        end: {
          line: 662,
          column: 3
        }
      },
      "91": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 62,
          column: 31
        }
      },
      "92": {
        start: {
          line: 63,
          column: 8
        },
        end: {
          line: 67,
          column: 10
        }
      },
      "93": {
        start: {
          line: 68,
          column: 8
        },
        end: {
          line: 68,
          column: 45
        }
      },
      "94": {
        start: {
          line: 70,
          column: 8
        },
        end: {
          line: 116,
          column: 10
        }
      },
      "95": {
        start: {
          line: 119,
          column: 4
        },
        end: {
          line: 121,
          column: 6
        }
      },
      "96": {
        start: {
          line: 120,
          column: 8
        },
        end: {
          line: 120,
          column: 39
        }
      },
      "97": {
        start: {
          line: 122,
          column: 4
        },
        end: {
          line: 167,
          column: 6
        }
      },
      "98": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 166,
          column: 11
        }
      },
      "99": {
        start: {
          line: 125,
          column: 12
        },
        end: {
          line: 125,
          column: 53
        }
      },
      "100": {
        start: {
          line: 125,
          column: 38
        },
        end: {
          line: 125,
          column: 51
        }
      },
      "101": {
        start: {
          line: 126,
          column: 12
        },
        end: {
          line: 165,
          column: 15
        }
      },
      "102": {
        start: {
          line: 127,
          column: 16
        },
        end: {
          line: 164,
          column: 17
        }
      },
      "103": {
        start: {
          line: 129,
          column: 24
        },
        end: {
          line: 131,
          column: 25
        }
      },
      "104": {
        start: {
          line: 130,
          column: 28
        },
        end: {
          line: 130,
          column: 66
        }
      },
      "105": {
        start: {
          line: 132,
          column: 24
        },
        end: {
          line: 132,
          column: 69
        }
      },
      "106": {
        start: {
          line: 133,
          column: 24
        },
        end: {
          line: 133,
          column: 68
        }
      },
      "107": {
        start: {
          line: 134,
          column: 24
        },
        end: {
          line: 134,
          column: 56
        }
      },
      "108": {
        start: {
          line: 136,
          column: 24
        },
        end: {
          line: 142,
          column: 25
        }
      },
      "109": {
        start: {
          line: 137,
          column: 28
        },
        end: {
          line: 137,
          column: 62
        }
      },
      "110": {
        start: {
          line: 138,
          column: 28
        },
        end: {
          line: 141,
          column: 29
        }
      },
      "111": {
        start: {
          line: 139,
          column: 32
        },
        end: {
          line: 139,
          column: 55
        }
      },
      "112": {
        start: {
          line: 140,
          column: 32
        },
        end: {
          line: 140,
          column: 67
        }
      },
      "113": {
        start: {
          line: 143,
          column: 24
        },
        end: {
          line: 143,
          column: 49
        }
      },
      "114": {
        start: {
          line: 144,
          column: 24
        },
        end: {
          line: 144,
          column: 37
        }
      },
      "115": {
        start: {
          line: 146,
          column: 24
        },
        end: {
          line: 146,
          column: 50
        }
      },
      "116": {
        start: {
          line: 147,
          column: 24
        },
        end: {
          line: 147,
          column: 91
        }
      },
      "117": {
        start: {
          line: 149,
          column: 24
        },
        end: {
          line: 149,
          column: 47
        }
      },
      "118": {
        start: {
          line: 151,
          column: 24
        },
        end: {
          line: 154,
          column: 27
        }
      },
      "119": {
        start: {
          line: 155,
          column: 24
        },
        end: {
          line: 155,
          column: 58
        }
      },
      "120": {
        start: {
          line: 157,
          column: 24
        },
        end: {
          line: 157,
          column: 44
        }
      },
      "121": {
        start: {
          line: 158,
          column: 24
        },
        end: {
          line: 158,
          column: 58
        }
      },
      "122": {
        start: {
          line: 159,
          column: 24
        },
        end: {
          line: 161,
          column: 25
        }
      },
      "123": {
        start: {
          line: 160,
          column: 28
        },
        end: {
          line: 160,
          column: 106
        }
      },
      "124": {
        start: {
          line: 162,
          column: 24
        },
        end: {
          line: 162,
          column: 90
        }
      },
      "125": {
        start: {
          line: 163,
          column: 28
        },
        end: {
          line: 163,
          column: 50
        }
      },
      "126": {
        start: {
          line: 171,
          column: 4
        },
        end: {
          line: 204,
          column: 6
        }
      },
      "127": {
        start: {
          line: 172,
          column: 8
        },
        end: {
          line: 203,
          column: 11
        }
      },
      "128": {
        start: {
          line: 174,
          column: 12
        },
        end: {
          line: 174,
          column: 53
        }
      },
      "129": {
        start: {
          line: 174,
          column: 38
        },
        end: {
          line: 174,
          column: 51
        }
      },
      "130": {
        start: {
          line: 175,
          column: 12
        },
        end: {
          line: 202,
          column: 15
        }
      },
      "131": {
        start: {
          line: 176,
          column: 16
        },
        end: {
          line: 201,
          column: 17
        }
      },
      "132": {
        start: {
          line: 178,
          column: 24
        },
        end: {
          line: 180,
          column: 25
        }
      },
      "133": {
        start: {
          line: 179,
          column: 28
        },
        end: {
          line: 179,
          column: 133
        }
      },
      "134": {
        start: {
          line: 181,
          column: 24
        },
        end: {
          line: 181,
          column: 37
        }
      },
      "135": {
        start: {
          line: 183,
          column: 24
        },
        end: {
          line: 183,
          column: 50
        }
      },
      "136": {
        start: {
          line: 184,
          column: 24
        },
        end: {
          line: 184,
          column: 86
        }
      },
      "137": {
        start: {
          line: 186,
          column: 24
        },
        end: {
          line: 186,
          column: 41
        }
      },
      "138": {
        start: {
          line: 187,
          column: 24
        },
        end: {
          line: 191,
          column: 31
        }
      },
      "139": {
        start: {
          line: 193,
          column: 24
        },
        end: {
          line: 193,
          column: 44
        }
      },
      "140": {
        start: {
          line: 194,
          column: 24
        },
        end: {
          line: 199,
          column: 31
        }
      },
      "141": {
        start: {
          line: 200,
          column: 28
        },
        end: {
          line: 200,
          column: 50
        }
      },
      "142": {
        start: {
          line: 205,
          column: 4
        },
        end: {
          line: 218,
          column: 6
        }
      },
      "143": {
        start: {
          line: 206,
          column: 8
        },
        end: {
          line: 217,
          column: 11
        }
      },
      "144": {
        start: {
          line: 208,
          column: 24
        },
        end: {
          line: 208,
          column: 28
        }
      },
      "145": {
        start: {
          line: 209,
          column: 12
        },
        end: {
          line: 209,
          column: 53
        }
      },
      "146": {
        start: {
          line: 209,
          column: 38
        },
        end: {
          line: 209,
          column: 51
        }
      },
      "147": {
        start: {
          line: 210,
          column: 12
        },
        end: {
          line: 216,
          column: 15
        }
      },
      "148": {
        start: {
          line: 211,
          column: 16
        },
        end: {
          line: 213,
          column: 17
        }
      },
      "149": {
        start: {
          line: 212,
          column: 20
        },
        end: {
          line: 212,
          column: 58
        }
      },
      "150": {
        start: {
          line: 214,
          column: 16
        },
        end: {
          line: 214,
          column: 109
        }
      },
      "151": {
        start: {
          line: 214,
          column: 57
        },
        end: {
          line: 214,
          column: 105
        }
      },
      "152": {
        start: {
          line: 215,
          column: 16
        },
        end: {
          line: 215,
          column: 61
        }
      },
      "153": {
        start: {
          line: 219,
          column: 4
        },
        end: {
          line: 259,
          column: 6
        }
      },
      "154": {
        start: {
          line: 220,
          column: 8
        },
        end: {
          line: 258,
          column: 11
        }
      },
      "155": {
        start: {
          line: 222,
          column: 12
        },
        end: {
          line: 257,
          column: 15
        }
      },
      "156": {
        start: {
          line: 223,
          column: 16
        },
        end: {
          line: 256,
          column: 17
        }
      },
      "157": {
        start: {
          line: 224,
          column: 28
        },
        end: {
          line: 224,
          column: 91
        }
      },
      "158": {
        start: {
          line: 226,
          column: 24
        },
        end: {
          line: 226,
          column: 51
        }
      },
      "159": {
        start: {
          line: 227,
          column: 24
        },
        end: {
          line: 228,
          column: 41
        }
      },
      "160": {
        start: {
          line: 227,
          column: 105
        },
        end: {
          line: 227,
          column: 132
        }
      },
      "161": {
        start: {
          line: 229,
          column: 24
        },
        end: {
          line: 230,
          column: 41
        }
      },
      "162": {
        start: {
          line: 229,
          column: 110
        },
        end: {
          line: 229,
          column: 137
        }
      },
      "163": {
        start: {
          line: 231,
          column: 24
        },
        end: {
          line: 232,
          column: 41
        }
      },
      "164": {
        start: {
          line: 231,
          column: 109
        },
        end: {
          line: 231,
          column: 150
        }
      },
      "165": {
        start: {
          line: 233,
          column: 24
        },
        end: {
          line: 241,
          column: 103
        }
      },
      "166": {
        start: {
          line: 234,
          column: 54
        },
        end: {
          line: 234,
          column: 87
        }
      },
      "167": {
        start: {
          line: 235,
          column: 51
        },
        end: {
          line: 240,
          column: 27
        }
      },
      "168": {
        start: {
          line: 241,
          column: 52
        },
        end: {
          line: 241,
          column: 99
        }
      },
      "169": {
        start: {
          line: 242,
          column: 24
        },
        end: {
          line: 244,
          column: 83
        }
      },
      "170": {
        start: {
          line: 243,
          column: 54
        },
        end: {
          line: 243,
          column: 78
        }
      },
      "171": {
        start: {
          line: 244,
          column: 52
        },
        end: {
          line: 244,
          column: 79
        }
      },
      "172": {
        start: {
          line: 245,
          column: 24
        },
        end: {
          line: 247,
          column: 83
        }
      },
      "173": {
        start: {
          line: 246,
          column: 54
        },
        end: {
          line: 246,
          column: 77
        }
      },
      "174": {
        start: {
          line: 247,
          column: 52
        },
        end: {
          line: 247,
          column: 79
        }
      },
      "175": {
        start: {
          line: 248,
          column: 24
        },
        end: {
          line: 255,
          column: 31
        }
      },
      "176": {
        start: {
          line: 260,
          column: 4
        },
        end: {
          line: 294,
          column: 6
        }
      },
      "177": {
        start: {
          line: 261,
          column: 8
        },
        end: {
          line: 293,
          column: 11
        }
      },
      "178": {
        start: {
          line: 263,
          column: 12
        },
        end: {
          line: 292,
          column: 15
        }
      },
      "179": {
        start: {
          line: 264,
          column: 16
        },
        end: {
          line: 291,
          column: 17
        }
      },
      "180": {
        start: {
          line: 265,
          column: 28
        },
        end: {
          line: 265,
          column: 91
        }
      },
      "181": {
        start: {
          line: 267,
          column: 24
        },
        end: {
          line: 267,
          column: 51
        }
      },
      "182": {
        start: {
          line: 268,
          column: 24
        },
        end: {
          line: 268,
          column: 102
        }
      },
      "183": {
        start: {
          line: 268,
          column: 72
        },
        end: {
          line: 268,
          column: 98
        }
      },
      "184": {
        start: {
          line: 269,
          column: 24
        },
        end: {
          line: 269,
          column: 126
        }
      },
      "185": {
        start: {
          line: 269,
          column: 81
        },
        end: {
          line: 269,
          column: 101
        }
      },
      "186": {
        start: {
          line: 270,
          column: 24
        },
        end: {
          line: 270,
          column: 115
        }
      },
      "187": {
        start: {
          line: 270,
          column: 98
        },
        end: {
          line: 270,
          column: 111
        }
      },
      "188": {
        start: {
          line: 271,
          column: 24
        },
        end: {
          line: 276,
          column: 26
        }
      },
      "189": {
        start: {
          line: 277,
          column: 24
        },
        end: {
          line: 278,
          column: 41
        }
      },
      "190": {
        start: {
          line: 277,
          column: 105
        },
        end: {
          line: 277,
          column: 146
        }
      },
      "191": {
        start: {
          line: 279,
          column: 24
        },
        end: {
          line: 280,
          column: 115
        }
      },
      "192": {
        start: {
          line: 280,
          column: 59
        },
        end: {
          line: 280,
          column: 84
        }
      },
      "193": {
        start: {
          line: 281,
          column: 24
        },
        end: {
          line: 290,
          column: 31
        }
      },
      "194": {
        start: {
          line: 295,
          column: 4
        },
        end: {
          line: 315,
          column: 6
        }
      },
      "195": {
        start: {
          line: 296,
          column: 8
        },
        end: {
          line: 314,
          column: 11
        }
      },
      "196": {
        start: {
          line: 298,
          column: 12
        },
        end: {
          line: 313,
          column: 15
        }
      },
      "197": {
        start: {
          line: 299,
          column: 16
        },
        end: {
          line: 312,
          column: 17
        }
      },
      "198": {
        start: {
          line: 300,
          column: 28
        },
        end: {
          line: 300,
          column: 81
        }
      },
      "199": {
        start: {
          line: 302,
          column: 24
        },
        end: {
          line: 302,
          column: 51
        }
      },
      "200": {
        start: {
          line: 303,
          column: 24
        },
        end: {
          line: 303,
          column: 84
        }
      },
      "201": {
        start: {
          line: 304,
          column: 24
        },
        end: {
          line: 311,
          column: 31
        }
      },
      "202": {
        start: {
          line: 316,
          column: 4
        },
        end: {
          line: 343,
          column: 6
        }
      },
      "203": {
        start: {
          line: 317,
          column: 8
        },
        end: {
          line: 342,
          column: 11
        }
      },
      "204": {
        start: {
          line: 319,
          column: 24
        },
        end: {
          line: 319,
          column: 28
        }
      },
      "205": {
        start: {
          line: 320,
          column: 12
        },
        end: {
          line: 341,
          column: 15
        }
      },
      "206": {
        start: {
          line: 321,
          column: 16
        },
        end: {
          line: 340,
          column: 17
        }
      },
      "207": {
        start: {
          line: 322,
          column: 28
        },
        end: {
          line: 322,
          column: 81
        }
      },
      "208": {
        start: {
          line: 324,
          column: 24
        },
        end: {
          line: 324,
          column: 51
        }
      },
      "209": {
        start: {
          line: 325,
          column: 24
        },
        end: {
          line: 335,
          column: 27
        }
      },
      "210": {
        start: {
          line: 326,
          column: 46
        },
        end: {
          line: 326,
          column: 84
        }
      },
      "211": {
        start: {
          line: 327,
          column: 41
        },
        end: {
          line: 327,
          column: 90
        }
      },
      "212": {
        start: {
          line: 328,
          column: 28
        },
        end: {
          line: 334,
          column: 30
        }
      },
      "213": {
        start: {
          line: 336,
          column: 24
        },
        end: {
          line: 339,
          column: 31
        }
      },
      "214": {
        start: {
          line: 344,
          column: 4
        },
        end: {
          line: 364,
          column: 6
        }
      },
      "215": {
        start: {
          line: 345,
          column: 8
        },
        end: {
          line: 363,
          column: 11
        }
      },
      "216": {
        start: {
          line: 347,
          column: 12
        },
        end: {
          line: 362,
          column: 15
        }
      },
      "217": {
        start: {
          line: 348,
          column: 16
        },
        end: {
          line: 361,
          column: 17
        }
      },
      "218": {
        start: {
          line: 349,
          column: 28
        },
        end: {
          line: 349,
          column: 81
        }
      },
      "219": {
        start: {
          line: 351,
          column: 24
        },
        end: {
          line: 351,
          column: 51
        }
      },
      "220": {
        start: {
          line: 352,
          column: 24
        },
        end: {
          line: 352,
          column: 84
        }
      },
      "221": {
        start: {
          line: 353,
          column: 24
        },
        end: {
          line: 360,
          column: 31
        }
      },
      "222": {
        start: {
          line: 365,
          column: 4
        },
        end: {
          line: 396,
          column: 6
        }
      },
      "223": {
        start: {
          line: 366,
          column: 8
        },
        end: {
          line: 395,
          column: 11
        }
      },
      "224": {
        start: {
          line: 368,
          column: 24
        },
        end: {
          line: 368,
          column: 28
        }
      },
      "225": {
        start: {
          line: 369,
          column: 12
        },
        end: {
          line: 394,
          column: 15
        }
      },
      "226": {
        start: {
          line: 370,
          column: 16
        },
        end: {
          line: 393,
          column: 17
        }
      },
      "227": {
        start: {
          line: 372,
          column: 24
        },
        end: {
          line: 372,
          column: 100
        }
      },
      "228": {
        start: {
          line: 373,
          column: 24
        },
        end: {
          line: 373,
          column: 77
        }
      },
      "229": {
        start: {
          line: 375,
          column: 24
        },
        end: {
          line: 375,
          column: 51
        }
      },
      "230": {
        start: {
          line: 376,
          column: 24
        },
        end: {
          line: 388,
          column: 93
        }
      },
      "231": {
        start: {
          line: 377,
          column: 46
        },
        end: {
          line: 377,
          column: 84
        }
      },
      "232": {
        start: {
          line: 378,
          column: 46
        },
        end: {
          line: 378,
          column: 88
        }
      },
      "233": {
        start: {
          line: 379,
          column: 46
        },
        end: {
          line: 379,
          column: 102
        }
      },
      "234": {
        start: {
          line: 380,
          column: 46
        },
        end: {
          line: 380,
          column: 88
        }
      },
      "235": {
        start: {
          line: 381,
          column: 28
        },
        end: {
          line: 387,
          column: 30
        }
      },
      "236": {
        start: {
          line: 388,
          column: 50
        },
        end: {
          line: 388,
          column: 89
        }
      },
      "237": {
        start: {
          line: 389,
          column: 24
        },
        end: {
          line: 392,
          column: 31
        }
      },
      "238": {
        start: {
          line: 397,
          column: 4
        },
        end: {
          line: 437,
          column: 6
        }
      },
      "239": {
        start: {
          line: 398,
          column: 8
        },
        end: {
          line: 436,
          column: 11
        }
      },
      "240": {
        start: {
          line: 400,
          column: 24
        },
        end: {
          line: 400,
          column: 28
        }
      },
      "241": {
        start: {
          line: 401,
          column: 12
        },
        end: {
          line: 435,
          column: 15
        }
      },
      "242": {
        start: {
          line: 402,
          column: 16
        },
        end: {
          line: 434,
          column: 17
        }
      },
      "243": {
        start: {
          line: 403,
          column: 28
        },
        end: {
          line: 403,
          column: 106
        }
      },
      "244": {
        start: {
          line: 405,
          column: 24
        },
        end: {
          line: 405,
          column: 54
        }
      },
      "245": {
        start: {
          line: 406,
          column: 24
        },
        end: {
          line: 406,
          column: 128
        }
      },
      "246": {
        start: {
          line: 407,
          column: 24
        },
        end: {
          line: 407,
          column: 102
        }
      },
      "247": {
        start: {
          line: 409,
          column: 24
        },
        end: {
          line: 409,
          column: 58
        }
      },
      "248": {
        start: {
          line: 410,
          column: 24
        },
        end: {
          line: 417,
          column: 97
        }
      },
      "249": {
        start: {
          line: 411,
          column: 56
        },
        end: {
          line: 416,
          column: 27
        }
      },
      "250": {
        start: {
          line: 417,
          column: 52
        },
        end: {
          line: 417,
          column: 93
        }
      },
      "251": {
        start: {
          line: 418,
          column: 24
        },
        end: {
          line: 418,
          column: 127
        }
      },
      "252": {
        start: {
          line: 419,
          column: 24
        },
        end: {
          line: 419,
          column: 124
        }
      },
      "253": {
        start: {
          line: 419,
          column: 103
        },
        end: {
          line: 419,
          column: 118
        }
      },
      "254": {
        start: {
          line: 421,
          column: 24
        },
        end: {
          line: 421,
          column: 43
        }
      },
      "255": {
        start: {
          line: 422,
          column: 24
        },
        end: {
          line: 422,
          column: 110
        }
      },
      "256": {
        start: {
          line: 423,
          column: 24
        },
        end: {
          line: 423,
          column: 181
        }
      },
      "257": {
        start: {
          line: 424,
          column: 24
        },
        end: {
          line: 433,
          column: 31
        }
      },
      "258": {
        start: {
          line: 438,
          column: 4
        },
        end: {
          line: 466,
          column: 6
        }
      },
      "259": {
        start: {
          line: 439,
          column: 8
        },
        end: {
          line: 465,
          column: 11
        }
      },
      "260": {
        start: {
          line: 441,
          column: 24
        },
        end: {
          line: 441,
          column: 28
        }
      },
      "261": {
        start: {
          line: 442,
          column: 12
        },
        end: {
          line: 464,
          column: 15
        }
      },
      "262": {
        start: {
          line: 443,
          column: 16
        },
        end: {
          line: 463,
          column: 17
        }
      },
      "263": {
        start: {
          line: 445,
          column: 24
        },
        end: {
          line: 445,
          column: 39
        }
      },
      "264": {
        start: {
          line: 446,
          column: 24
        },
        end: {
          line: 446,
          column: 37
        }
      },
      "265": {
        start: {
          line: 447,
          column: 24
        },
        end: {
          line: 449,
          column: 25
        }
      },
      "266": {
        start: {
          line: 448,
          column: 28
        },
        end: {
          line: 448,
          column: 73
        }
      },
      "267": {
        start: {
          line: 450,
          column: 24
        },
        end: {
          line: 450,
          column: 52
        }
      },
      "268": {
        start: {
          line: 451,
          column: 24
        },
        end: {
          line: 451,
          column: 37
        }
      },
      "269": {
        start: {
          line: 453,
          column: 24
        },
        end: {
          line: 453,
          column: 78
        }
      },
      "270": {
        start: {
          line: 453,
          column: 54
        },
        end: {
          line: 453,
          column: 78
        }
      },
      "271": {
        start: {
          line: 454,
          column: 24
        },
        end: {
          line: 454,
          column: 46
        }
      },
      "272": {
        start: {
          line: 455,
          column: 24
        },
        end: {
          line: 455,
          column: 155
        }
      },
      "273": {
        start: {
          line: 455,
          column: 86
        },
        end: {
          line: 455,
          column: 149
        }
      },
      "274": {
        start: {
          line: 457,
          column: 24
        },
        end: {
          line: 457,
          column: 34
        }
      },
      "275": {
        start: {
          line: 458,
          column: 24
        },
        end: {
          line: 458,
          column: 37
        }
      },
      "276": {
        start: {
          line: 460,
          column: 24
        },
        end: {
          line: 460,
          column: 29
        }
      },
      "277": {
        start: {
          line: 461,
          column: 24
        },
        end: {
          line: 461,
          column: 48
        }
      },
      "278": {
        start: {
          line: 462,
          column: 28
        },
        end: {
          line: 462,
          column: 50
        }
      },
      "279": {
        start: {
          line: 467,
          column: 4
        },
        end: {
          line: 485,
          column: 6
        }
      },
      "280": {
        start: {
          line: 468,
          column: 8
        },
        end: {
          line: 484,
          column: 11
        }
      },
      "281": {
        start: {
          line: 470,
          column: 12
        },
        end: {
          line: 483,
          column: 15
        }
      },
      "282": {
        start: {
          line: 471,
          column: 16
        },
        end: {
          line: 473,
          column: 24
        }
      },
      "283": {
        start: {
          line: 474,
          column: 16
        },
        end: {
          line: 476,
          column: 24
        }
      },
      "284": {
        start: {
          line: 477,
          column: 16
        },
        end: {
          line: 482,
          column: 23
        }
      },
      "285": {
        start: {
          line: 486,
          column: 4
        },
        end: {
          line: 498,
          column: 6
        }
      },
      "286": {
        start: {
          line: 487,
          column: 8
        },
        end: {
          line: 497,
          column: 11
        }
      },
      "287": {
        start: {
          line: 488,
          column: 12
        },
        end: {
          line: 496,
          column: 15
        }
      },
      "288": {
        start: {
          line: 489,
          column: 16
        },
        end: {
          line: 489,
          column: 35
        }
      },
      "289": {
        start: {
          line: 490,
          column: 16
        },
        end: {
          line: 494,
          column: 18
        }
      },
      "290": {
        start: {
          line: 495,
          column: 16
        },
        end: {
          line: 495,
          column: 38
        }
      },
      "291": {
        start: {
          line: 499,
          column: 4
        },
        end: {
          line: 544,
          column: 6
        }
      },
      "292": {
        start: {
          line: 500,
          column: 8
        },
        end: {
          line: 543,
          column: 11
        }
      },
      "293": {
        start: {
          line: 502,
          column: 12
        },
        end: {
          line: 542,
          column: 15
        }
      },
      "294": {
        start: {
          line: 503,
          column: 16
        },
        end: {
          line: 541,
          column: 17
        }
      },
      "295": {
        start: {
          line: 505,
          column: 24
        },
        end: {
          line: 505,
          column: 107
        }
      },
      "296": {
        start: {
          line: 505,
          column: 83
        },
        end: {
          line: 505,
          column: 107
        }
      },
      "297": {
        start: {
          line: 506,
          column: 24
        },
        end: {
          line: 506,
          column: 37
        }
      },
      "298": {
        start: {
          line: 508,
          column: 24
        },
        end: {
          line: 508,
          column: 50
        }
      },
      "299": {
        start: {
          line: 509,
          column: 24
        },
        end: {
          line: 509,
          column: 89
        }
      },
      "300": {
        start: {
          line: 511,
          column: 24
        },
        end: {
          line: 511,
          column: 45
        }
      },
      "301": {
        start: {
          line: 512,
          column: 24
        },
        end: {
          line: 514,
          column: 25
        }
      },
      "302": {
        start: {
          line: 513,
          column: 28
        },
        end: {
          line: 513,
          column: 83
        }
      },
      "303": {
        start: {
          line: 515,
          column: 24
        },
        end: {
          line: 515,
          column: 48
        }
      },
      "304": {
        start: {
          line: 517,
          column: 24
        },
        end: {
          line: 517,
          column: 44
        }
      },
      "305": {
        start: {
          line: 519,
          column: 24
        },
        end: {
          line: 519,
          column: 38
        }
      },
      "306": {
        start: {
          line: 522,
          column: 20
        },
        end: {
          line: 522,
          column: 111
        }
      },
      "307": {
        start: {
          line: 522,
          column: 74
        },
        end: {
          line: 522,
          column: 106
        }
      },
      "308": {
        start: {
          line: 525,
          column: 24
        },
        end: {
          line: 525,
          column: 34
        }
      },
      "309": {
        start: {
          line: 526,
          column: 24
        },
        end: {
          line: 526,
          column: 62
        }
      },
      "310": {
        start: {
          line: 527,
          column: 24
        },
        end: {
          line: 529,
          column: 25
        }
      },
      "311": {
        start: {
          line: 528,
          column: 28
        },
        end: {
          line: 528,
          column: 84
        }
      },
      "312": {
        start: {
          line: 530,
          column: 24
        },
        end: {
          line: 540,
          column: 31
        }
      },
      "313": {
        start: {
          line: 545,
          column: 4
        },
        end: {
          line: 558,
          column: 6
        }
      },
      "314": {
        start: {
          line: 546,
          column: 8
        },
        end: {
          line: 557,
          column: 10
        }
      },
      "315": {
        start: {
          line: 559,
          column: 4
        },
        end: {
          line: 568,
          column: 6
        }
      },
      "316": {
        start: {
          line: 560,
          column: 20
        },
        end: {
          line: 560,
          column: 65
        }
      },
      "317": {
        start: {
          line: 561,
          column: 20
        },
        end: {
          line: 561,
          column: 37
        }
      },
      "318": {
        start: {
          line: 562,
          column: 20
        },
        end: {
          line: 562,
          column: 36
        }
      },
      "319": {
        start: {
          line: 563,
          column: 8
        },
        end: {
          line: 565,
          column: 9
        }
      },
      "320": {
        start: {
          line: 564,
          column: 12
        },
        end: {
          line: 564,
          column: 38
        }
      },
      "321": {
        start: {
          line: 566,
          column: 21
        },
        end: {
          line: 566,
          column: 34
        }
      },
      "322": {
        start: {
          line: 567,
          column: 8
        },
        end: {
          line: 567,
          column: 79
        }
      },
      "323": {
        start: {
          line: 569,
          column: 4
        },
        end: {
          line: 577,
          column: 6
        }
      },
      "324": {
        start: {
          line: 570,
          column: 26
        },
        end: {
          line: 575,
          column: 9
        }
      },
      "325": {
        start: {
          line: 576,
          column: 8
        },
        end: {
          line: 576,
          column: 102
        }
      },
      "326": {
        start: {
          line: 578,
          column: 4
        },
        end: {
          line: 582,
          column: 6
        }
      },
      "327": {
        start: {
          line: 580,
          column: 27
        },
        end: {
          line: 580,
          column: 84
        }
      },
      "328": {
        start: {
          line: 581,
          column: 8
        },
        end: {
          line: 581,
          column: 68
        }
      },
      "329": {
        start: {
          line: 583,
          column: 4
        },
        end: {
          line: 592,
          column: 6
        }
      },
      "330": {
        start: {
          line: 584,
          column: 26
        },
        end: {
          line: 590,
          column: 9
        }
      },
      "331": {
        start: {
          line: 591,
          column: 8
        },
        end: {
          line: 591,
          column: 96
        }
      },
      "332": {
        start: {
          line: 593,
          column: 4
        },
        end: {
          line: 602,
          column: 6
        }
      },
      "333": {
        start: {
          line: 594,
          column: 24
        },
        end: {
          line: 600,
          column: 9
        }
      },
      "334": {
        start: {
          line: 601,
          column: 8
        },
        end: {
          line: 601,
          column: 55
        }
      },
      "335": {
        start: {
          line: 603,
          column: 4
        },
        end: {
          line: 620,
          column: 6
        }
      },
      "336": {
        start: {
          line: 605,
          column: 27
        },
        end: {
          line: 616,
          column: 9
        }
      },
      "337": {
        start: {
          line: 617,
          column: 31
        },
        end: {
          line: 617,
          column: 55
        }
      },
      "338": {
        start: {
          line: 618,
          column: 21
        },
        end: {
          line: 618,
          column: 154
        }
      },
      "339": {
        start: {
          line: 619,
          column: 8
        },
        end: {
          line: 619,
          column: 22
        }
      },
      "340": {
        start: {
          line: 621,
          column: 4
        },
        end: {
          line: 637,
          column: 6
        }
      },
      "341": {
        start: {
          line: 622,
          column: 27
        },
        end: {
          line: 622,
          column: 30
        }
      },
      "342": {
        start: {
          line: 623,
          column: 27
        },
        end: {
          line: 623,
          column: 31
        }
      },
      "343": {
        start: {
          line: 624,
          column: 27
        },
        end: {
          line: 624,
          column: 30
        }
      },
      "344": {
        start: {
          line: 625,
          column: 24
        },
        end: {
          line: 625,
          column: 28
        }
      },
      "345": {
        start: {
          line: 626,
          column: 31
        },
        end: {
          line: 626,
          column: 34
        }
      },
      "346": {
        start: {
          line: 627,
          column: 26
        },
        end: {
          line: 627,
          column: 42
        }
      },
      "347": {
        start: {
          line: 628,
          column: 26
        },
        end: {
          line: 628,
          column: 42
        }
      },
      "348": {
        start: {
          line: 629,
          column: 26
        },
        end: {
          line: 629,
          column: 56
        }
      },
      "349": {
        start: {
          line: 630,
          column: 23
        },
        end: {
          line: 630,
          column: 71
        }
      },
      "350": {
        start: {
          line: 631,
          column: 30
        },
        end: {
          line: 631,
          column: 55
        }
      },
      "351": {
        start: {
          line: 632,
          column: 8
        },
        end: {
          line: 636,
          column: 48
        }
      },
      "352": {
        start: {
          line: 638,
          column: 4
        },
        end: {
          line: 655,
          column: 6
        }
      },
      "353": {
        start: {
          line: 639,
          column: 22
        },
        end: {
          line: 639,
          column: 24
        }
      },
      "354": {
        start: {
          line: 640,
          column: 8
        },
        end: {
          line: 642,
          column: 9
        }
      },
      "355": {
        start: {
          line: 641,
          column: 12
        },
        end: {
          line: 641,
          column: 47
        }
      },
      "356": {
        start: {
          line: 643,
          column: 8
        },
        end: {
          line: 645,
          column: 9
        }
      },
      "357": {
        start: {
          line: 644,
          column: 12
        },
        end: {
          line: 644,
          column: 52
        }
      },
      "358": {
        start: {
          line: 646,
          column: 8
        },
        end: {
          line: 648,
          column: 9
        }
      },
      "359": {
        start: {
          line: 647,
          column: 12
        },
        end: {
          line: 647,
          column: 55
        }
      },
      "360": {
        start: {
          line: 649,
          column: 8
        },
        end: {
          line: 651,
          column: 9
        }
      },
      "361": {
        start: {
          line: 650,
          column: 12
        },
        end: {
          line: 650,
          column: 51
        }
      },
      "362": {
        start: {
          line: 652,
          column: 8
        },
        end: {
          line: 654,
          column: 62
        }
      },
      "363": {
        start: {
          line: 656,
          column: 4
        },
        end: {
          line: 660,
          column: 6
        }
      },
      "364": {
        start: {
          line: 657,
          column: 33
        },
        end: {
          line: 657,
          column: 77
        }
      },
      "365": {
        start: {
          line: 658,
          column: 28
        },
        end: {
          line: 658,
          column: 129
        }
      },
      "366": {
        start: {
          line: 658,
          column: 70
        },
        end: {
          line: 658,
          column: 103
        }
      },
      "367": {
        start: {
          line: 659,
          column: 8
        },
        end: {
          line: 659,
          column: 50
        }
      },
      "368": {
        start: {
          line: 661,
          column: 4
        },
        end: {
          line: 661,
          column: 34
        }
      },
      "369": {
        start: {
          line: 663,
          column: 0
        },
        end: {
          line: 663,
          column: 56
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 49,
            column: 52
          },
          end: {
            line: 49,
            column: 53
          }
        },
        loc: {
          start: {
            line: 49,
            column: 78
          },
          end: {
            line: 57,
            column: 1
          }
        },
        line: 49
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 60,
            column: 44
          },
          end: {
            line: 60,
            column: 45
          }
        },
        loc: {
          start: {
            line: 60,
            column: 56
          },
          end: {
            line: 662,
            column: 1
          }
        },
        line: 60
      },
      "17": {
        name: "SkillMarketDataService",
        decl: {
          start: {
            line: 61,
            column: 13
          },
          end: {
            line: 61,
            column: 35
          }
        },
        loc: {
          start: {
            line: 61,
            column: 38
          },
          end: {
            line: 118,
            column: 5
          }
        },
        line: 61
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 119,
            column: 58
          },
          end: {
            line: 119,
            column: 59
          }
        },
        loc: {
          start: {
            line: 119,
            column: 77
          },
          end: {
            line: 121,
            column: 5
          }
        },
        line: 119
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 122,
            column: 58
          },
          end: {
            line: 122,
            column: 59
          }
        },
        loc: {
          start: {
            line: 122,
            column: 77
          },
          end: {
            line: 167,
            column: 5
          }
        },
        line: 122
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 123,
            column: 51
          },
          end: {
            line: 123,
            column: 52
          }
        },
        loc: {
          start: {
            line: 123,
            column: 77
          },
          end: {
            line: 166,
            column: 9
          }
        },
        line: 123
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 126,
            column: 37
          },
          end: {
            line: 126,
            column: 38
          }
        },
        loc: {
          start: {
            line: 126,
            column: 51
          },
          end: {
            line: 165,
            column: 13
          }
        },
        line: 126
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 171,
            column: 74
          },
          end: {
            line: 171,
            column: 75
          }
        },
        loc: {
          start: {
            line: 171,
            column: 93
          },
          end: {
            line: 204,
            column: 5
          }
        },
        line: 171
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 172,
            column: 51
          },
          end: {
            line: 172,
            column: 52
          }
        },
        loc: {
          start: {
            line: 172,
            column: 77
          },
          end: {
            line: 203,
            column: 9
          }
        },
        line: 172
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 175,
            column: 37
          },
          end: {
            line: 175,
            column: 38
          }
        },
        loc: {
          start: {
            line: 175,
            column: 51
          },
          end: {
            line: 202,
            column: 13
          }
        },
        line: 175
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 205,
            column: 67
          },
          end: {
            line: 205,
            column: 68
          }
        },
        loc: {
          start: {
            line: 205,
            column: 87
          },
          end: {
            line: 218,
            column: 5
          }
        },
        line: 205
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 206,
            column: 51
          },
          end: {
            line: 206,
            column: 52
          }
        },
        loc: {
          start: {
            line: 206,
            column: 78
          },
          end: {
            line: 217,
            column: 9
          }
        },
        line: 206
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 210,
            column: 37
          },
          end: {
            line: 210,
            column: 38
          }
        },
        loc: {
          start: {
            line: 210,
            column: 51
          },
          end: {
            line: 216,
            column: 13
          }
        },
        line: 210
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 214,
            column: 38
          },
          end: {
            line: 214,
            column: 39
          }
        },
        loc: {
          start: {
            line: 214,
            column: 55
          },
          end: {
            line: 214,
            column: 107
          }
        },
        line: 214
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 219,
            column: 59
          },
          end: {
            line: 219,
            column: 60
          }
        },
        loc: {
          start: {
            line: 219,
            column: 77
          },
          end: {
            line: 259,
            column: 5
          }
        },
        line: 219
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 220,
            column: 48
          },
          end: {
            line: 220,
            column: 49
          }
        },
        loc: {
          start: {
            line: 220,
            column: 60
          },
          end: {
            line: 258,
            column: 9
          }
        },
        line: 220
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 222,
            column: 37
          },
          end: {
            line: 222,
            column: 38
          }
        },
        loc: {
          start: {
            line: 222,
            column: 51
          },
          end: {
            line: 257,
            column: 13
          }
        },
        line: 222
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 227,
            column: 87
          },
          end: {
            line: 227,
            column: 88
          }
        },
        loc: {
          start: {
            line: 227,
            column: 103
          },
          end: {
            line: 227,
            column: 134
          }
        },
        line: 227
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 229,
            column: 92
          },
          end: {
            line: 229,
            column: 93
          }
        },
        loc: {
          start: {
            line: 229,
            column: 108
          },
          end: {
            line: 229,
            column: 139
          }
        },
        line: 229
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 231,
            column: 91
          },
          end: {
            line: 231,
            column: 92
          }
        },
        loc: {
          start: {
            line: 231,
            column: 107
          },
          end: {
            line: 231,
            column: 152
          }
        },
        line: 231
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 234,
            column: 36
          },
          end: {
            line: 234,
            column: 37
          }
        },
        loc: {
          start: {
            line: 234,
            column: 52
          },
          end: {
            line: 234,
            column: 89
          }
        },
        line: 234
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 235,
            column: 33
          },
          end: {
            line: 235,
            column: 34
          }
        },
        loc: {
          start: {
            line: 235,
            column: 49
          },
          end: {
            line: 240,
            column: 29
          }
        },
        line: 235
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 241,
            column: 34
          },
          end: {
            line: 241,
            column: 35
          }
        },
        loc: {
          start: {
            line: 241,
            column: 50
          },
          end: {
            line: 241,
            column: 101
          }
        },
        line: 241
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 243,
            column: 36
          },
          end: {
            line: 243,
            column: 37
          }
        },
        loc: {
          start: {
            line: 243,
            column: 52
          },
          end: {
            line: 243,
            column: 80
          }
        },
        line: 243
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 244,
            column: 34
          },
          end: {
            line: 244,
            column: 35
          }
        },
        loc: {
          start: {
            line: 244,
            column: 50
          },
          end: {
            line: 244,
            column: 81
          }
        },
        line: 244
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 246,
            column: 36
          },
          end: {
            line: 246,
            column: 37
          }
        },
        loc: {
          start: {
            line: 246,
            column: 52
          },
          end: {
            line: 246,
            column: 79
          }
        },
        line: 246
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 247,
            column: 34
          },
          end: {
            line: 247,
            column: 35
          }
        },
        loc: {
          start: {
            line: 247,
            column: 50
          },
          end: {
            line: 247,
            column: 81
          }
        },
        line: 247
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 260,
            column: 57
          },
          end: {
            line: 260,
            column: 58
          }
        },
        loc: {
          start: {
            line: 260,
            column: 75
          },
          end: {
            line: 294,
            column: 5
          }
        },
        line: 260
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 261,
            column: 48
          },
          end: {
            line: 261,
            column: 49
          }
        },
        loc: {
          start: {
            line: 261,
            column: 60
          },
          end: {
            line: 293,
            column: 9
          }
        },
        line: 261
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 263,
            column: 37
          },
          end: {
            line: 263,
            column: 38
          }
        },
        loc: {
          start: {
            line: 263,
            column: 51
          },
          end: {
            line: 292,
            column: 13
          }
        },
        line: 263
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 268,
            column: 54
          },
          end: {
            line: 268,
            column: 55
          }
        },
        loc: {
          start: {
            line: 268,
            column: 70
          },
          end: {
            line: 268,
            column: 100
          }
        },
        line: 268
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 269,
            column: 56
          },
          end: {
            line: 269,
            column: 57
          }
        },
        loc: {
          start: {
            line: 269,
            column: 79
          },
          end: {
            line: 269,
            column: 103
          }
        },
        line: 269
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 270,
            column: 80
          },
          end: {
            line: 270,
            column: 81
          }
        },
        loc: {
          start: {
            line: 270,
            column: 96
          },
          end: {
            line: 270,
            column: 113
          }
        },
        line: 270
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 277,
            column: 87
          },
          end: {
            line: 277,
            column: 88
          }
        },
        loc: {
          start: {
            line: 277,
            column: 103
          },
          end: {
            line: 277,
            column: 148
          }
        },
        line: 277
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 280,
            column: 36
          },
          end: {
            line: 280,
            column: 37
          }
        },
        loc: {
          start: {
            line: 280,
            column: 57
          },
          end: {
            line: 280,
            column: 86
          }
        },
        line: 280
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 295,
            column: 66
          },
          end: {
            line: 295,
            column: 67
          }
        },
        loc: {
          start: {
            line: 295,
            column: 93
          },
          end: {
            line: 315,
            column: 5
          }
        },
        line: 295
      },
      "51": {
        name: "(anonymous_51)",
        decl: {
          start: {
            line: 296,
            column: 48
          },
          end: {
            line: 296,
            column: 49
          }
        },
        loc: {
          start: {
            line: 296,
            column: 60
          },
          end: {
            line: 314,
            column: 9
          }
        },
        line: 296
      },
      "52": {
        name: "(anonymous_52)",
        decl: {
          start: {
            line: 298,
            column: 37
          },
          end: {
            line: 298,
            column: 38
          }
        },
        loc: {
          start: {
            line: 298,
            column: 51
          },
          end: {
            line: 313,
            column: 13
          }
        },
        line: 298
      },
      "53": {
        name: "(anonymous_53)",
        decl: {
          start: {
            line: 316,
            column: 62
          },
          end: {
            line: 316,
            column: 63
          }
        },
        loc: {
          start: {
            line: 316,
            column: 90
          },
          end: {
            line: 343,
            column: 5
          }
        },
        line: 316
      },
      "54": {
        name: "(anonymous_54)",
        decl: {
          start: {
            line: 317,
            column: 48
          },
          end: {
            line: 317,
            column: 49
          }
        },
        loc: {
          start: {
            line: 317,
            column: 60
          },
          end: {
            line: 342,
            column: 9
          }
        },
        line: 317
      },
      "55": {
        name: "(anonymous_55)",
        decl: {
          start: {
            line: 320,
            column: 37
          },
          end: {
            line: 320,
            column: 38
          }
        },
        loc: {
          start: {
            line: 320,
            column: 51
          },
          end: {
            line: 341,
            column: 13
          }
        },
        line: 320
      },
      "56": {
        name: "(anonymous_56)",
        decl: {
          start: {
            line: 325,
            column: 59
          },
          end: {
            line: 325,
            column: 60
          }
        },
        loc: {
          start: {
            line: 325,
            column: 79
          },
          end: {
            line: 335,
            column: 25
          }
        },
        line: 325
      },
      "57": {
        name: "(anonymous_57)",
        decl: {
          start: {
            line: 344,
            column: 61
          },
          end: {
            line: 344,
            column: 62
          }
        },
        loc: {
          start: {
            line: 344,
            column: 88
          },
          end: {
            line: 364,
            column: 5
          }
        },
        line: 344
      },
      "58": {
        name: "(anonymous_58)",
        decl: {
          start: {
            line: 345,
            column: 48
          },
          end: {
            line: 345,
            column: 49
          }
        },
        loc: {
          start: {
            line: 345,
            column: 60
          },
          end: {
            line: 363,
            column: 9
          }
        },
        line: 345
      },
      "59": {
        name: "(anonymous_59)",
        decl: {
          start: {
            line: 347,
            column: 37
          },
          end: {
            line: 347,
            column: 38
          }
        },
        loc: {
          start: {
            line: 347,
            column: 51
          },
          end: {
            line: 362,
            column: 13
          }
        },
        line: 347
      },
      "60": {
        name: "(anonymous_60)",
        decl: {
          start: {
            line: 365,
            column: 67
          },
          end: {
            line: 365,
            column: 68
          }
        },
        loc: {
          start: {
            line: 365,
            column: 84
          },
          end: {
            line: 396,
            column: 5
          }
        },
        line: 365
      },
      "61": {
        name: "(anonymous_61)",
        decl: {
          start: {
            line: 366,
            column: 48
          },
          end: {
            line: 366,
            column: 49
          }
        },
        loc: {
          start: {
            line: 366,
            column: 60
          },
          end: {
            line: 395,
            column: 9
          }
        },
        line: 366
      },
      "62": {
        name: "(anonymous_62)",
        decl: {
          start: {
            line: 369,
            column: 37
          },
          end: {
            line: 369,
            column: 38
          }
        },
        loc: {
          start: {
            line: 369,
            column: 51
          },
          end: {
            line: 394,
            column: 13
          }
        },
        line: 369
      },
      "63": {
        name: "(anonymous_63)",
        decl: {
          start: {
            line: 376,
            column: 58
          },
          end: {
            line: 376,
            column: 59
          }
        },
        loc: {
          start: {
            line: 376,
            column: 78
          },
          end: {
            line: 388,
            column: 25
          }
        },
        line: 376
      },
      "64": {
        name: "(anonymous_64)",
        decl: {
          start: {
            line: 388,
            column: 32
          },
          end: {
            line: 388,
            column: 33
          }
        },
        loc: {
          start: {
            line: 388,
            column: 48
          },
          end: {
            line: 388,
            column: 91
          }
        },
        line: 388
      },
      "65": {
        name: "(anonymous_65)",
        decl: {
          start: {
            line: 397,
            column: 69
          },
          end: {
            line: 397,
            column: 70
          }
        },
        loc: {
          start: {
            line: 397,
            column: 88
          },
          end: {
            line: 437,
            column: 5
          }
        },
        line: 397
      },
      "66": {
        name: "(anonymous_66)",
        decl: {
          start: {
            line: 398,
            column: 48
          },
          end: {
            line: 398,
            column: 49
          }
        },
        loc: {
          start: {
            line: 398,
            column: 60
          },
          end: {
            line: 436,
            column: 9
          }
        },
        line: 398
      },
      "67": {
        name: "(anonymous_67)",
        decl: {
          start: {
            line: 401,
            column: 37
          },
          end: {
            line: 401,
            column: 38
          }
        },
        loc: {
          start: {
            line: 401,
            column: 51
          },
          end: {
            line: 435,
            column: 13
          }
        },
        line: 401
      },
      "68": {
        name: "(anonymous_68)",
        decl: {
          start: {
            line: 411,
            column: 33
          },
          end: {
            line: 411,
            column: 34
          }
        },
        loc: {
          start: {
            line: 411,
            column: 54
          },
          end: {
            line: 416,
            column: 29
          }
        },
        line: 411
      },
      "69": {
        name: "(anonymous_69)",
        decl: {
          start: {
            line: 417,
            column: 34
          },
          end: {
            line: 417,
            column: 35
          }
        },
        loc: {
          start: {
            line: 417,
            column: 50
          },
          end: {
            line: 417,
            column: 95
          }
        },
        line: 417
      },
      "70": {
        name: "(anonymous_70)",
        decl: {
          start: {
            line: 419,
            column: 88
          },
          end: {
            line: 419,
            column: 89
          }
        },
        loc: {
          start: {
            line: 419,
            column: 101
          },
          end: {
            line: 419,
            column: 120
          }
        },
        line: 419
      },
      "71": {
        name: "(anonymous_71)",
        decl: {
          start: {
            line: 438,
            column: 61
          },
          end: {
            line: 438,
            column: 62
          }
        },
        loc: {
          start: {
            line: 438,
            column: 79
          },
          end: {
            line: 466,
            column: 5
          }
        },
        line: 438
      },
      "72": {
        name: "(anonymous_72)",
        decl: {
          start: {
            line: 439,
            column: 48
          },
          end: {
            line: 439,
            column: 49
          }
        },
        loc: {
          start: {
            line: 439,
            column: 60
          },
          end: {
            line: 465,
            column: 9
          }
        },
        line: 439
      },
      "73": {
        name: "(anonymous_73)",
        decl: {
          start: {
            line: 442,
            column: 37
          },
          end: {
            line: 442,
            column: 38
          }
        },
        loc: {
          start: {
            line: 442,
            column: 51
          },
          end: {
            line: 464,
            column: 13
          }
        },
        line: 442
      },
      "74": {
        name: "(anonymous_74)",
        decl: {
          start: {
            line: 455,
            column: 67
          },
          end: {
            line: 455,
            column: 68
          }
        },
        loc: {
          start: {
            line: 455,
            column: 84
          },
          end: {
            line: 455,
            column: 151
          }
        },
        line: 455
      },
      "75": {
        name: "(anonymous_75)",
        decl: {
          start: {
            line: 467,
            column: 58
          },
          end: {
            line: 467,
            column: 59
          }
        },
        loc: {
          start: {
            line: 467,
            column: 70
          },
          end: {
            line: 485,
            column: 5
          }
        },
        line: 467
      },
      "76": {
        name: "(anonymous_76)",
        decl: {
          start: {
            line: 468,
            column: 48
          },
          end: {
            line: 468,
            column: 49
          }
        },
        loc: {
          start: {
            line: 468,
            column: 60
          },
          end: {
            line: 484,
            column: 9
          }
        },
        line: 468
      },
      "77": {
        name: "(anonymous_77)",
        decl: {
          start: {
            line: 470,
            column: 37
          },
          end: {
            line: 470,
            column: 38
          }
        },
        loc: {
          start: {
            line: 470,
            column: 51
          },
          end: {
            line: 483,
            column: 13
          }
        },
        line: 470
      },
      "78": {
        name: "(anonymous_78)",
        decl: {
          start: {
            line: 486,
            column: 50
          },
          end: {
            line: 486,
            column: 51
          }
        },
        loc: {
          start: {
            line: 486,
            column: 62
          },
          end: {
            line: 498,
            column: 5
          }
        },
        line: 486
      },
      "79": {
        name: "(anonymous_79)",
        decl: {
          start: {
            line: 487,
            column: 48
          },
          end: {
            line: 487,
            column: 49
          }
        },
        loc: {
          start: {
            line: 487,
            column: 60
          },
          end: {
            line: 497,
            column: 9
          }
        },
        line: 487
      },
      "80": {
        name: "(anonymous_80)",
        decl: {
          start: {
            line: 488,
            column: 37
          },
          end: {
            line: 488,
            column: 38
          }
        },
        loc: {
          start: {
            line: 488,
            column: 51
          },
          end: {
            line: 496,
            column: 13
          }
        },
        line: 488
      },
      "81": {
        name: "(anonymous_81)",
        decl: {
          start: {
            line: 499,
            column: 62
          },
          end: {
            line: 499,
            column: 63
          }
        },
        loc: {
          start: {
            line: 499,
            column: 79
          },
          end: {
            line: 544,
            column: 5
          }
        },
        line: 499
      },
      "82": {
        name: "(anonymous_82)",
        decl: {
          start: {
            line: 500,
            column: 48
          },
          end: {
            line: 500,
            column: 49
          }
        },
        loc: {
          start: {
            line: 500,
            column: 60
          },
          end: {
            line: 543,
            column: 9
          }
        },
        line: 500
      },
      "83": {
        name: "(anonymous_83)",
        decl: {
          start: {
            line: 502,
            column: 37
          },
          end: {
            line: 502,
            column: 38
          }
        },
        loc: {
          start: {
            line: 502,
            column: 51
          },
          end: {
            line: 542,
            column: 13
          }
        },
        line: 502
      },
      "84": {
        name: "(anonymous_84)",
        decl: {
          start: {
            line: 522,
            column: 53
          },
          end: {
            line: 522,
            column: 54
          }
        },
        loc: {
          start: {
            line: 522,
            column: 72
          },
          end: {
            line: 522,
            column: 108
          }
        },
        line: 522
      },
      "85": {
        name: "(anonymous_85)",
        decl: {
          start: {
            line: 545,
            column: 60
          },
          end: {
            line: 545,
            column: 61
          }
        },
        loc: {
          start: {
            line: 545,
            column: 77
          },
          end: {
            line: 558,
            column: 5
          }
        },
        line: 545
      },
      "86": {
        name: "(anonymous_86)",
        decl: {
          start: {
            line: 559,
            column: 59
          },
          end: {
            line: 559,
            column: 60
          }
        },
        loc: {
          start: {
            line: 559,
            column: 94
          },
          end: {
            line: 568,
            column: 5
          }
        },
        line: 559
      },
      "87": {
        name: "(anonymous_87)",
        decl: {
          start: {
            line: 569,
            column: 62
          },
          end: {
            line: 569,
            column: 63
          }
        },
        loc: {
          start: {
            line: 569,
            column: 82
          },
          end: {
            line: 577,
            column: 5
          }
        },
        line: 569
      },
      "88": {
        name: "(anonymous_88)",
        decl: {
          start: {
            line: 578,
            column: 68
          },
          end: {
            line: 578,
            column: 69
          }
        },
        loc: {
          start: {
            line: 578,
            column: 85
          },
          end: {
            line: 582,
            column: 5
          }
        },
        line: 578
      },
      "89": {
        name: "(anonymous_89)",
        decl: {
          start: {
            line: 583,
            column: 62
          },
          end: {
            line: 583,
            column: 63
          }
        },
        loc: {
          start: {
            line: 583,
            column: 82
          },
          end: {
            line: 592,
            column: 5
          }
        },
        line: 583
      },
      "90": {
        name: "(anonymous_90)",
        decl: {
          start: {
            line: 593,
            column: 66
          },
          end: {
            line: 593,
            column: 67
          }
        },
        loc: {
          start: {
            line: 593,
            column: 86
          },
          end: {
            line: 602,
            column: 5
          }
        },
        line: 593
      },
      "91": {
        name: "(anonymous_91)",
        decl: {
          start: {
            line: 603,
            column: 69
          },
          end: {
            line: 603,
            column: 70
          }
        },
        loc: {
          start: {
            line: 603,
            column: 108
          },
          end: {
            line: 620,
            column: 5
          }
        },
        line: 603
      },
      "92": {
        name: "(anonymous_92)",
        decl: {
          start: {
            line: 621,
            column: 62
          },
          end: {
            line: 621,
            column: 63
          }
        },
        loc: {
          start: {
            line: 621,
            column: 92
          },
          end: {
            line: 637,
            column: 5
          }
        },
        line: 621
      },
      "93": {
        name: "(anonymous_93)",
        decl: {
          start: {
            line: 638,
            column: 71
          },
          end: {
            line: 638,
            column: 72
          }
        },
        loc: {
          start: {
            line: 638,
            column: 101
          },
          end: {
            line: 655,
            column: 5
          }
        },
        line: 638
      },
      "94": {
        name: "(anonymous_94)",
        decl: {
          start: {
            line: 656,
            column: 64
          },
          end: {
            line: 656,
            column: 65
          }
        },
        loc: {
          start: {
            line: 656,
            column: 96
          },
          end: {
            line: 660,
            column: 5
          }
        },
        line: 656
      },
      "95": {
        name: "(anonymous_95)",
        decl: {
          start: {
            line: 658,
            column: 46
          },
          end: {
            line: 658,
            column: 47
          }
        },
        loc: {
          start: {
            line: 658,
            column: 68
          },
          end: {
            line: 658,
            column: 105
          }
        },
        line: 658
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 49,
            column: 20
          },
          end: {
            line: 57,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 21
          },
          end: {
            line: 49,
            column: 25
          }
        }, {
          start: {
            line: 49,
            column: 29
          },
          end: {
            line: 49,
            column: 47
          }
        }, {
          start: {
            line: 49,
            column: 52
          },
          end: {
            line: 57,
            column: 1
          }
        }],
        line: 49
      },
      "36": {
        loc: {
          start: {
            line: 50,
            column: 4
          },
          end: {
            line: 55,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 4
          },
          end: {
            line: 55,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "37": {
        loc: {
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 12
          }
        }, {
          start: {
            line: 50,
            column: 16
          },
          end: {
            line: 50,
            column: 38
          }
        }],
        line: 50
      },
      "38": {
        loc: {
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 54,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 54,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 51
      },
      "39": {
        loc: {
          start: {
            line: 51,
            column: 12
          },
          end: {
            line: 51,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 51,
            column: 12
          },
          end: {
            line: 51,
            column: 14
          }
        }, {
          start: {
            line: 51,
            column: 18
          },
          end: {
            line: 51,
            column: 30
          }
        }],
        line: 51
      },
      "40": {
        loc: {
          start: {
            line: 52,
            column: 12
          },
          end: {
            line: 52,
            column: 65
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 52,
            column: 12
          },
          end: {
            line: 52,
            column: 65
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 52
      },
      "41": {
        loc: {
          start: {
            line: 56,
            column: 21
          },
          end: {
            line: 56,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 56,
            column: 21
          },
          end: {
            line: 56,
            column: 23
          }
        }, {
          start: {
            line: 56,
            column: 27
          },
          end: {
            line: 56,
            column: 59
          }
        }],
        line: 56
      },
      "42": {
        loc: {
          start: {
            line: 125,
            column: 12
          },
          end: {
            line: 125,
            column: 53
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 125,
            column: 12
          },
          end: {
            line: 125,
            column: 53
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 125
      },
      "43": {
        loc: {
          start: {
            line: 127,
            column: 16
          },
          end: {
            line: 164,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 128,
            column: 20
          },
          end: {
            line: 144,
            column: 37
          }
        }, {
          start: {
            line: 145,
            column: 20
          },
          end: {
            line: 147,
            column: 91
          }
        }, {
          start: {
            line: 148,
            column: 20
          },
          end: {
            line: 155,
            column: 58
          }
        }, {
          start: {
            line: 156,
            column: 20
          },
          end: {
            line: 162,
            column: 90
          }
        }, {
          start: {
            line: 163,
            column: 20
          },
          end: {
            line: 163,
            column: 50
          }
        }],
        line: 127
      },
      "44": {
        loc: {
          start: {
            line: 129,
            column: 24
          },
          end: {
            line: 131,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 129,
            column: 24
          },
          end: {
            line: 131,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 129
      },
      "45": {
        loc: {
          start: {
            line: 129,
            column: 28
          },
          end: {
            line: 129,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 129,
            column: 28
          },
          end: {
            line: 129,
            column: 34
          }
        }, {
          start: {
            line: 129,
            column: 38
          },
          end: {
            line: 129,
            column: 57
          }
        }],
        line: 129
      },
      "46": {
        loc: {
          start: {
            line: 136,
            column: 24
          },
          end: {
            line: 142,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 24
          },
          end: {
            line: 142,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 136
      },
      "47": {
        loc: {
          start: {
            line: 138,
            column: 28
          },
          end: {
            line: 141,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 138,
            column: 28
          },
          end: {
            line: 141,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 138
      },
      "48": {
        loc: {
          start: {
            line: 138,
            column: 32
          },
          end: {
            line: 138,
            column: 108
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 138,
            column: 32
          },
          end: {
            line: 138,
            column: 38
          }
        }, {
          start: {
            line: 138,
            column: 42
          },
          end: {
            line: 138,
            column: 108
          }
        }],
        line: 138
      },
      "49": {
        loc: {
          start: {
            line: 138,
            column: 75
          },
          end: {
            line: 138,
            column: 107
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 138,
            column: 75
          },
          end: {
            line: 138,
            column: 89
          }
        }, {
          start: {
            line: 138,
            column: 93
          },
          end: {
            line: 138,
            column: 107
          }
        }],
        line: 138
      },
      "50": {
        loc: {
          start: {
            line: 159,
            column: 24
          },
          end: {
            line: 161,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 159,
            column: 24
          },
          end: {
            line: 161,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 159
      },
      "51": {
        loc: {
          start: {
            line: 174,
            column: 12
          },
          end: {
            line: 174,
            column: 53
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 174,
            column: 12
          },
          end: {
            line: 174,
            column: 53
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 174
      },
      "52": {
        loc: {
          start: {
            line: 176,
            column: 16
          },
          end: {
            line: 201,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 177,
            column: 20
          },
          end: {
            line: 181,
            column: 37
          }
        }, {
          start: {
            line: 182,
            column: 20
          },
          end: {
            line: 184,
            column: 86
          }
        }, {
          start: {
            line: 185,
            column: 20
          },
          end: {
            line: 191,
            column: 31
          }
        }, {
          start: {
            line: 192,
            column: 20
          },
          end: {
            line: 199,
            column: 31
          }
        }, {
          start: {
            line: 200,
            column: 20
          },
          end: {
            line: 200,
            column: 50
          }
        }],
        line: 176
      },
      "53": {
        loc: {
          start: {
            line: 178,
            column: 24
          },
          end: {
            line: 180,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 178,
            column: 24
          },
          end: {
            line: 180,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 178
      },
      "54": {
        loc: {
          start: {
            line: 196,
            column: 39
          },
          end: {
            line: 196,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 196,
            column: 66
          },
          end: {
            line: 196,
            column: 81
          }
        }, {
          start: {
            line: 196,
            column: 84
          },
          end: {
            line: 196,
            column: 99
          }
        }],
        line: 196
      },
      "55": {
        loc: {
          start: {
            line: 209,
            column: 12
          },
          end: {
            line: 209,
            column: 53
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 209,
            column: 12
          },
          end: {
            line: 209,
            column: 53
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 209
      },
      "56": {
        loc: {
          start: {
            line: 211,
            column: 16
          },
          end: {
            line: 213,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 211,
            column: 16
          },
          end: {
            line: 213,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 211
      },
      "57": {
        loc: {
          start: {
            line: 211,
            column: 20
          },
          end: {
            line: 211,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 211,
            column: 20
          },
          end: {
            line: 211,
            column: 27
          }
        }, {
          start: {
            line: 211,
            column: 31
          },
          end: {
            line: 211,
            column: 50
          }
        }],
        line: 211
      },
      "58": {
        loc: {
          start: {
            line: 223,
            column: 16
          },
          end: {
            line: 256,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 224,
            column: 20
          },
          end: {
            line: 224,
            column: 91
          }
        }, {
          start: {
            line: 225,
            column: 20
          },
          end: {
            line: 255,
            column: 31
          }
        }],
        line: 223
      },
      "59": {
        loc: {
          start: {
            line: 264,
            column: 16
          },
          end: {
            line: 291,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 265,
            column: 20
          },
          end: {
            line: 265,
            column: 91
          }
        }, {
          start: {
            line: 266,
            column: 20
          },
          end: {
            line: 290,
            column: 31
          }
        }],
        line: 264
      },
      "60": {
        loc: {
          start: {
            line: 299,
            column: 16
          },
          end: {
            line: 312,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 300,
            column: 20
          },
          end: {
            line: 300,
            column: 81
          }
        }, {
          start: {
            line: 301,
            column: 20
          },
          end: {
            line: 311,
            column: 31
          }
        }],
        line: 299
      },
      "61": {
        loc: {
          start: {
            line: 321,
            column: 16
          },
          end: {
            line: 340,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 322,
            column: 20
          },
          end: {
            line: 322,
            column: 81
          }
        }, {
          start: {
            line: 323,
            column: 20
          },
          end: {
            line: 339,
            column: 31
          }
        }],
        line: 321
      },
      "62": {
        loc: {
          start: {
            line: 348,
            column: 16
          },
          end: {
            line: 361,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 349,
            column: 20
          },
          end: {
            line: 349,
            column: 81
          }
        }, {
          start: {
            line: 350,
            column: 20
          },
          end: {
            line: 360,
            column: 31
          }
        }],
        line: 348
      },
      "63": {
        loc: {
          start: {
            line: 370,
            column: 16
          },
          end: {
            line: 393,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 371,
            column: 20
          },
          end: {
            line: 373,
            column: 77
          }
        }, {
          start: {
            line: 374,
            column: 20
          },
          end: {
            line: 392,
            column: 31
          }
        }],
        line: 370
      },
      "64": {
        loc: {
          start: {
            line: 402,
            column: 16
          },
          end: {
            line: 434,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 403,
            column: 20
          },
          end: {
            line: 403,
            column: 106
          }
        }, {
          start: {
            line: 404,
            column: 20
          },
          end: {
            line: 407,
            column: 102
          }
        }, {
          start: {
            line: 408,
            column: 20
          },
          end: {
            line: 419,
            column: 124
          }
        }, {
          start: {
            line: 420,
            column: 20
          },
          end: {
            line: 433,
            column: 31
          }
        }],
        line: 402
      },
      "65": {
        loc: {
          start: {
            line: 443,
            column: 16
          },
          end: {
            line: 463,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 444,
            column: 20
          },
          end: {
            line: 451,
            column: 37
          }
        }, {
          start: {
            line: 452,
            column: 20
          },
          end: {
            line: 455,
            column: 155
          }
        }, {
          start: {
            line: 456,
            column: 20
          },
          end: {
            line: 458,
            column: 37
          }
        }, {
          start: {
            line: 459,
            column: 20
          },
          end: {
            line: 461,
            column: 48
          }
        }, {
          start: {
            line: 462,
            column: 20
          },
          end: {
            line: 462,
            column: 50
          }
        }],
        line: 443
      },
      "66": {
        loc: {
          start: {
            line: 453,
            column: 24
          },
          end: {
            line: 453,
            column: 78
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 453,
            column: 24
          },
          end: {
            line: 453,
            column: 78
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 453
      },
      "67": {
        loc: {
          start: {
            line: 471,
            column: 26
          },
          end: {
            line: 473,
            column: 23
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 472,
            column: 22
          },
          end: {
            line: 472,
            column: 74
          }
        }, {
          start: {
            line: 473,
            column: 22
          },
          end: {
            line: 473,
            column: 23
          }
        }],
        line: 471
      },
      "68": {
        loc: {
          start: {
            line: 474,
            column: 27
          },
          end: {
            line: 476,
            column: 23
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 475,
            column: 22
          },
          end: {
            line: 475,
            column: 76
          }
        }, {
          start: {
            line: 476,
            column: 22
          },
          end: {
            line: 476,
            column: 23
          }
        }],
        line: 474
      },
      "69": {
        loc: {
          start: {
            line: 503,
            column: 16
          },
          end: {
            line: 541,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 504,
            column: 20
          },
          end: {
            line: 506,
            column: 37
          }
        }, {
          start: {
            line: 507,
            column: 20
          },
          end: {
            line: 509,
            column: 89
          }
        }, {
          start: {
            line: 510,
            column: 20
          },
          end: {
            line: 515,
            column: 48
          }
        }, {
          start: {
            line: 516,
            column: 20
          },
          end: {
            line: 519,
            column: 38
          }
        }, {
          start: {
            line: 520,
            column: 20
          },
          end: {
            line: 522,
            column: 111
          }
        }, {
          start: {
            line: 523,
            column: 20
          },
          end: {
            line: 540,
            column: 31
          }
        }],
        line: 503
      },
      "70": {
        loc: {
          start: {
            line: 505,
            column: 24
          },
          end: {
            line: 505,
            column: 107
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 505,
            column: 24
          },
          end: {
            line: 505,
            column: 107
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 505
      },
      "71": {
        loc: {
          start: {
            line: 505,
            column: 30
          },
          end: {
            line: 505,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 505,
            column: 30
          },
          end: {
            line: 505,
            column: 42
          }
        }, {
          start: {
            line: 505,
            column: 46
          },
          end: {
            line: 505,
            column: 80
          }
        }],
        line: 505
      },
      "72": {
        loc: {
          start: {
            line: 512,
            column: 24
          },
          end: {
            line: 514,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 512,
            column: 24
          },
          end: {
            line: 514,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 512
      },
      "73": {
        loc: {
          start: {
            line: 527,
            column: 24
          },
          end: {
            line: 529,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 527,
            column: 24
          },
          end: {
            line: 529,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 527
      },
      "74": {
        loc: {
          start: {
            line: 532,
            column: 40
          },
          end: {
            line: 532,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 532,
            column: 40
          },
          end: {
            line: 532,
            column: 55
          }
        }, {
          start: {
            line: 532,
            column: 59
          },
          end: {
            line: 532,
            column: 60
          }
        }],
        line: 532
      },
      "75": {
        loc: {
          start: {
            line: 533,
            column: 40
          },
          end: {
            line: 533,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 533,
            column: 40
          },
          end: {
            line: 533,
            column: 55
          }
        }, {
          start: {
            line: 533,
            column: 59
          },
          end: {
            line: 533,
            column: 60
          }
        }],
        line: 533
      },
      "76": {
        loc: {
          start: {
            line: 534,
            column: 47
          },
          end: {
            line: 534,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 534,
            column: 47
          },
          end: {
            line: 534,
            column: 69
          }
        }, {
          start: {
            line: 534,
            column: 73
          },
          end: {
            line: 534,
            column: 74
          }
        }],
        line: 534
      },
      "77": {
        loc: {
          start: {
            line: 535,
            column: 40
          },
          end: {
            line: 535,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 535,
            column: 40
          },
          end: {
            line: 535,
            column: 55
          }
        }, {
          start: {
            line: 535,
            column: 59
          },
          end: {
            line: 535,
            column: 60
          }
        }],
        line: 535
      },
      "78": {
        loc: {
          start: {
            line: 536,
            column: 44
          },
          end: {
            line: 536,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 536,
            column: 44
          },
          end: {
            line: 536,
            column: 63
          }
        }, {
          start: {
            line: 536,
            column: 67
          },
          end: {
            line: 536,
            column: 68
          }
        }],
        line: 536
      },
      "79": {
        loc: {
          start: {
            line: 537,
            column: 45
          },
          end: {
            line: 537,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 537,
            column: 45
          },
          end: {
            line: 537,
            column: 65
          }
        }, {
          start: {
            line: 537,
            column: 69
          },
          end: {
            line: 537,
            column: 71
          }
        }],
        line: 537
      },
      "80": {
        loc: {
          start: {
            line: 538,
            column: 42
          },
          end: {
            line: 538,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 538,
            column: 42
          },
          end: {
            line: 538,
            column: 59
          }
        }, {
          start: {
            line: 538,
            column: 63
          },
          end: {
            line: 538,
            column: 72
          }
        }],
        line: 538
      },
      "81": {
        loc: {
          start: {
            line: 563,
            column: 8
          },
          end: {
            line: 565,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 563,
            column: 8
          },
          end: {
            line: 565,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 563
      },
      "82": {
        loc: {
          start: {
            line: 576,
            column: 15
          },
          end: {
            line: 576,
            column: 101
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 576,
            column: 15
          },
          end: {
            line: 576,
            column: 50
          }
        }, {
          start: {
            line: 576,
            column: 54
          },
          end: {
            line: 576,
            column: 101
          }
        }],
        line: 576
      },
      "83": {
        loc: {
          start: {
            line: 581,
            column: 15
          },
          end: {
            line: 581,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 581,
            column: 60
          },
          end: {
            line: 581,
            column: 62
          }
        }, {
          start: {
            line: 581,
            column: 65
          },
          end: {
            line: 581,
            column: 67
          }
        }],
        line: 581
      },
      "84": {
        loc: {
          start: {
            line: 591,
            column: 15
          },
          end: {
            line: 591,
            column: 95
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 591,
            column: 15
          },
          end: {
            line: 591,
            column: 50
          }
        }, {
          start: {
            line: 591,
            column: 54
          },
          end: {
            line: 591,
            column: 95
          }
        }],
        line: 591
      },
      "85": {
        loc: {
          start: {
            line: 601,
            column: 15
          },
          end: {
            line: 601,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 601,
            column: 15
          },
          end: {
            line: 601,
            column: 48
          }
        }, {
          start: {
            line: 601,
            column: 52
          },
          end: {
            line: 601,
            column: 54
          }
        }],
        line: 601
      },
      "86": {
        loc: {
          start: {
            line: 618,
            column: 21
          },
          end: {
            line: 618,
            column: 154
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 618,
            column: 22
          },
          end: {
            line: 618,
            column: 116
          }
        }, {
          start: {
            line: 618,
            column: 121
          },
          end: {
            line: 618,
            column: 154
          }
        }],
        line: 618
      },
      "87": {
        loc: {
          start: {
            line: 618,
            column: 22
          },
          end: {
            line: 618,
            column: 116
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 618,
            column: 88
          },
          end: {
            line: 618,
            column: 94
          }
        }, {
          start: {
            line: 618,
            column: 97
          },
          end: {
            line: 618,
            column: 116
          }
        }],
        line: 618
      },
      "88": {
        loc: {
          start: {
            line: 618,
            column: 22
          },
          end: {
            line: 618,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 618,
            column: 22
          },
          end: {
            line: 618,
            column: 68
          }
        }, {
          start: {
            line: 618,
            column: 72
          },
          end: {
            line: 618,
            column: 85
          }
        }],
        line: 618
      },
      "89": {
        loc: {
          start: {
            line: 640,
            column: 8
          },
          end: {
            line: 642,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 640,
            column: 8
          },
          end: {
            line: 642,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 640
      },
      "90": {
        loc: {
          start: {
            line: 643,
            column: 8
          },
          end: {
            line: 645,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 643,
            column: 8
          },
          end: {
            line: 645,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 643
      },
      "91": {
        loc: {
          start: {
            line: 646,
            column: 8
          },
          end: {
            line: 648,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 646,
            column: 8
          },
          end: {
            line: 648,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 646
      },
      "92": {
        loc: {
          start: {
            line: 649,
            column: 8
          },
          end: {
            line: 651,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 649,
            column: 8
          },
          end: {
            line: 651,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 649
      },
      "93": {
        loc: {
          start: {
            line: 652,
            column: 15
          },
          end: {
            line: 654,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 653,
            column: 14
          },
          end: {
            line: 653,
            column: 62
          }
        }, {
          start: {
            line: 654,
            column: 14
          },
          end: {
            line: 654,
            column: 61
          }
        }],
        line: 652
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0,
      "265": 0,
      "266": 0,
      "267": 0,
      "268": 0,
      "269": 0,
      "270": 0,
      "271": 0,
      "272": 0,
      "273": 0,
      "274": 0,
      "275": 0,
      "276": 0,
      "277": 0,
      "278": 0,
      "279": 0,
      "280": 0,
      "281": 0,
      "282": 0,
      "283": 0,
      "284": 0,
      "285": 0,
      "286": 0,
      "287": 0,
      "288": 0,
      "289": 0,
      "290": 0,
      "291": 0,
      "292": 0,
      "293": 0,
      "294": 0,
      "295": 0,
      "296": 0,
      "297": 0,
      "298": 0,
      "299": 0,
      "300": 0,
      "301": 0,
      "302": 0,
      "303": 0,
      "304": 0,
      "305": 0,
      "306": 0,
      "307": 0,
      "308": 0,
      "309": 0,
      "310": 0,
      "311": 0,
      "312": 0,
      "313": 0,
      "314": 0,
      "315": 0,
      "316": 0,
      "317": 0,
      "318": 0,
      "319": 0,
      "320": 0,
      "321": 0,
      "322": 0,
      "323": 0,
      "324": 0,
      "325": 0,
      "326": 0,
      "327": 0,
      "328": 0,
      "329": 0,
      "330": 0,
      "331": 0,
      "332": 0,
      "333": 0,
      "334": 0,
      "335": 0,
      "336": 0,
      "337": 0,
      "338": 0,
      "339": 0,
      "340": 0,
      "341": 0,
      "342": 0,
      "343": 0,
      "344": 0,
      "345": 0,
      "346": 0,
      "347": 0,
      "348": 0,
      "349": 0,
      "350": 0,
      "351": 0,
      "352": 0,
      "353": 0,
      "354": 0,
      "355": 0,
      "356": 0,
      "357": 0,
      "358": 0,
      "359": 0,
      "360": 0,
      "361": 0,
      "362": 0,
      "363": 0,
      "364": 0,
      "365": 0,
      "366": 0,
      "367": 0,
      "368": 0,
      "369": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0, 0, 0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0, 0, 0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0, 0, 0],
      "65": [0, 0, 0, 0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0, 0, 0, 0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0],
      "81": [0, 0],
      "82": [0, 0],
      "83": [0, 0],
      "84": [0, 0],
      "85": [0, 0],
      "86": [0, 0],
      "87": [0, 0],
      "88": [0, 0],
      "89": [0, 0],
      "90": [0, 0],
      "91": [0, 0],
      "92": [0, 0],
      "93": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/SkillMarketDataService.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwHA;IAUE;QATQ,UAAK,GAA8D,IAAI,GAAG,EAAE,CAAC;QAC7E,eAAU,GAAG;YACnB,IAAI,EAAE,CAAC;YACP,MAAM,EAAE,CAAC;YACT,aAAa,EAAE,CAAC;SACjB,CAAC;QACe,cAAS,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW;QAW7D,+CAA+C;QACvC,mBAAc,GAA6C;YACjE,UAAU,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,aAAa,EAAE,KAAK;gBACpB,MAAM,EAAE,IAAI;gBACZ,UAAU,EAAE,CAAC;gBACb,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,aAAa;aACxB;YACD,KAAK,EAAE;gBACL,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,aAAa,EAAE,KAAK;gBACpB,MAAM,EAAE,IAAI;gBACZ,UAAU,EAAE,CAAC;gBACb,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,UAAU;aACrB;YACD,MAAM,EAAE;gBACN,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,aAAa,EAAE,KAAK;gBACpB,MAAM,EAAE,IAAI;gBACZ,UAAU,EAAE,CAAC;gBACb,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,SAAS;aACpB;YACD,MAAM,EAAE;gBACN,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,aAAa,EAAE,MAAM;gBACrB,MAAM,EAAE,IAAI;gBACZ,UAAU,EAAE,CAAC;gBACb,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,aAAa;aACxB;YACD,UAAU,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,EAAE;gBACV,aAAa,EAAE,KAAK;gBACpB,MAAM,EAAE,IAAI;gBACZ,UAAU,EAAE,CAAC;gBACb,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,aAAa;aACxB;SACF,CAAC;QAtDA,wEAAwE;IAC1E,CAAC;IAED,mDAAkB,GAAlB,UAAmB,OAAY;QAC7B,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;IACjC,CAAC;IAmDK,mDAAkB,GAAxB;0CAA0E,OAAO,YAAxD,KAAa,EAAE,OAA+B;;YAA/B,wBAAA,EAAA,YAA+B;;;;wBACrE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;4BAClC,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;wBACxC,CAAC;wBAEK,eAAe,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;wBAC7C,QAAQ,GAAG,gBAAS,eAAe,CAAE,CAAC;wBAE5C,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;wBAEhC,oBAAoB;wBACpB,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;4BACpB,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;4BACxC,IAAI,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gCACjF,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;gCACvB,sBAAO,MAAM,CAAC,IAAI,EAAC;4BACrB,CAAC;wBACH,CAAC;wBAED,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;;;;wBAIJ,qBAAM,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,EAAA;;wBAA/D,UAAU,GAAG,SAAkD;wBAErE,mBAAmB;wBACnB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;4BACvB,IAAI,EAAE,UAAU;4BAChB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;yBACtB,CAAC,CAAC;wBAEH,sBAAO,UAAU,EAAC;;;wBAGZ,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;wBACxC,IAAI,MAAM,EAAE,CAAC;4BACX,4CAAY,MAAM,CAAC,IAAI,KAAE,OAAO,EAAE,IAAI,KAAG;wBAC3C,CAAC;wBAED,sBAAO,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,EAAC;;;;;KAErD;IAED;;OAEG;IACG,mEAAkC,GAAxC;0CAA0F,OAAO,YAAxD,KAAa,EAAE,OAA+B;;YAA/B,wBAAA,EAAA,YAA+B;;;;wBACrF,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;4BACzB,sBAAO,IAAI,CAAC,eAAe,CAAC,uBAAuB,YAAG,KAAK,OAAA,IAAK,OAAO,EAAG,EAAC;wBAC7E,CAAC;;;;wBAIc,qBAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,EAAA;;wBAApD,IAAI,GAAG,SAA6C;wBAC1D,sBAAO;gCACL,OAAO,EAAE,IAAI;gCACb,IAAI,MAAA;gCACJ,cAAc,aAAI,KAAK,OAAA,IAAK,OAAO,CAAE;6BACtC,EAAC;;;wBAEF,sBAAO;gCACL,OAAO,EAAE,KAAK;gCACd,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gCAC/D,SAAS,EAAE,cAAc;gCACzB,YAAY,EAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;6BAC/C,EAAC;;;;;KAEL;IAEK,4DAA2B,GAAjC;0CAAsF,OAAO,YAA3D,MAAgB,EAAE,OAA+B;;;YAA/B,wBAAA,EAAA,YAA+B;;gBACjF,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACnC,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;gBACxC,CAAC;gBAEK,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,KAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,EAAvC,CAAuC,CAAC,CAAC;gBAC9E,sBAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAC;;;KAC9B;IAEK,oDAAmB,GAAzB,UAA0B,MAAgB;uCAAG,OAAO;;;;4BAC3B,qBAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,EAAA;;wBAA/D,cAAc,GAAG,SAA8C;wBAE/D,eAAe,GAAG,kBAAI,cAAc,QACvC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,EAAnB,CAAmB,CAAC;6BACnC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBAET,oBAAoB,GAAG,kBAAI,cAAc,QAC5C,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,EAAnB,CAAmB,CAAC;6BACnC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBAET,mBAAmB,GAAG,kBAAI,cAAc,QAC3C,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,aAAa,EAAjC,CAAiC,CAAC;6BACjD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBAET,UAAU,GAAG,cAAc;6BAC9B,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAzB,CAAyB,CAAC;6BACzC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,CAAC;4BACZ,KAAK,EAAE,IAAI,CAAC,KAAK;4BACjB,iBAAiB,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;4BAC5C,gBAAgB,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;4BACnE,UAAU,EAAE,IAAI;yBACjB,CAAC,EALW,CAKX,CAAC;6BACF,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,gBAAgB,GAAG,CAAC,CAAC,gBAAgB,EAAvC,CAAuC,CAAC,CAAC;wBAErD,cAAc,GAAG,cAAc;6BAClC,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,GAAG,EAAE,EAAhB,CAAgB,CAAC;6BAChC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,EAAnB,CAAmB,CAAC,CAAC;wBAEjC,eAAe,GAAG,cAAc;6BACnC,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,GAAG,CAAC,EAAf,CAAe,CAAC;6BAC/B,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,EAAnB,CAAmB,CAAC,CAAC;wBAEvC,sBAAO;gCACL,eAAe,iBAAA;gCACf,oBAAoB,sBAAA;gCACpB,mBAAmB,qBAAA;gCACnB,UAAU,YAAA;gCACV,cAAc,gBAAA;gCACd,eAAe,iBAAA;6BAChB,EAAC;;;;KACH;IAEK,kDAAiB,GAAvB,UAAwB,MAAgB;uCAAG,OAAO;;;;4BACzB,qBAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,EAAA;;wBAA/D,cAAc,GAAG,SAA8C;wBAC/D,QAAQ,GAAG,cAAc,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,aAAa,EAAlB,CAAkB,CAAC,CAAC;wBAE1D,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,MAAM,IAAK,OAAA,GAAG,GAAG,MAAM,EAAZ,CAAY,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;wBACpF,cAAc,GAAG,kBAAI,QAAQ,QAAE,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,GAAG,CAAC,EAAL,CAAK,CAAC,CAAC;wBAErD,WAAW,GAAG;4BAClB,GAAG,EAAE,IAAI,CAAC,mBAAmB,CAAC,cAAc,EAAE,EAAE,CAAC;4BACjD,GAAG,EAAE,IAAI,CAAC,mBAAmB,CAAC,cAAc,EAAE,EAAE,CAAC;4BACjD,GAAG,EAAE,IAAI,CAAC,mBAAmB,CAAC,cAAc,EAAE,EAAE,CAAC;4BACjD,GAAG,EAAE,IAAI,CAAC,mBAAmB,CAAC,cAAc,EAAE,EAAE,CAAC;yBAClD,CAAC;wBAEI,eAAe,GAAG,kBAAI,cAAc,QACvC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,aAAa,EAAjC,CAAiC,CAAC;6BACjD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBAET,qBAAqB,GAAG,cAAc;6BACzC,MAAM,CAAC,UAAC,GAAG,EAAE,IAAI,IAAK,OAAA,GAAG,GAAG,IAAI,CAAC,MAAM,EAAjB,CAAiB,EAAE,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC;wBAEvE,sBAAO;gCACL,aAAa,eAAA;gCACb,WAAW,EAAE;oCACX,GAAG,EAAE,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,QAAQ,CAAC;oCAC1B,GAAG,EAAE,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,QAAQ,CAAC;iCAC3B;gCACD,WAAW,aAAA;gCACX,eAAe,iBAAA;gCACf,qBAAqB,uBAAA;6BACtB,EAAC;;;;KACH;IAEK,2DAA0B,GAAhC,UAAiC,KAAa,EAAE,QAAgB;uCAAG,OAAO;;;;4BACjD,qBAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAA;;wBAArD,cAAc,GAAG,SAAoC;wBAGrD,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;wBAElE,sBAAO;gCACL,KAAK,OAAA;gCACL,QAAQ,UAAA;gCACR,WAAW,EAAE,cAAc,CAAC,MAAM,GAAG,mBAAmB,CAAC,MAAM;gCAC/D,WAAW,EAAE,cAAc,CAAC,aAAa,GAAG,mBAAmB,CAAC,MAAM;gCACtE,sBAAsB,EAAE,mBAAmB,CAAC,YAAY;gCACxD,mBAAmB,EAAE,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC;6BAC9D,EAAC;;;;KACH;IAEK,uDAAsB,GAA5B,UAA6B,KAAa,EAAE,SAAmB;uCAAG,OAAO;;;;;4BAChD,qBAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAA;;wBAArD,cAAc,GAAG,SAAoC;wBAErD,kBAAkB,GAAG,SAAS,CAAC,GAAG,CAAC,UAAA,QAAQ;4BAC/C,IAAM,WAAW,GAAG,KAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;4BAC1D,IAAM,MAAM,GAAG,cAAc,CAAC,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC;4BAEjE,OAAO;gCACL,QAAQ,UAAA;gCACR,MAAM,EAAE,cAAc,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM;gCAClD,MAAM,QAAA;gCACN,iBAAiB,EAAE,WAAW,CAAC,YAAY;gCAC3C,cAAc,EAAE,MAAM,GAAG,WAAW,CAAC,YAAY;6BAClD,CAAC;wBACJ,CAAC,CAAC,CAAC;wBAEH,sBAAO;gCACL,KAAK,OAAA;gCACL,kBAAkB,oBAAA;6BACnB,EAAC;;;;KACH;IAEK,sDAAqB,GAA3B,UAA4B,KAAa,EAAE,QAAgB;uCAAG,OAAO;;;;4BAC5C,qBAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAA;;wBAArD,cAAc,GAAG,SAAoC;wBACrD,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;wBAElE,sBAAO;gCACL,KAAK,OAAA;gCACL,QAAQ,UAAA;gCACR,cAAc,EAAE,cAAc,CAAC,MAAM,GAAG,mBAAmB,CAAC,MAAM;gCAClE,qBAAqB,EAAE,cAAc,CAAC,aAAa,GAAG,mBAAmB,CAAC,MAAM;gCAChF,gBAAgB,EAAE,cAAc,CAAC,MAAM,GAAG,mBAAmB,CAAC,MAAM;gCACpE,YAAY,EAAE,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC;6BACxD,EAAC;;;;KACH;IAEK,4DAA2B,GAAjC,UAAkC,KAAa;uCAAG,OAAO;;;;;;wBACjD,UAAU,GAAG,CAAC,YAAY,EAAE,SAAS,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;wBAC3D,qBAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAA;;wBAArD,cAAc,GAAG,SAAoC;wBAErD,gBAAgB,GAAG,UAAU,CAAC,GAAG,CAAC,UAAA,QAAQ;4BAC9C,IAAM,WAAW,GAAG,KAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;4BAC1D,IAAM,WAAW,GAAG,cAAc,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;4BAC/D,IAAM,WAAW,GAAG,cAAc,CAAC,aAAa,GAAG,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,YAAY;4BAC1F,IAAM,WAAW,GAAG,cAAc,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;4BAE/D,OAAO;gCACL,QAAQ,UAAA;gCACR,WAAW,aAAA;gCACX,WAAW,aAAA;gCACX,WAAW,aAAA;gCACX,YAAY,EAAE,CAAC,WAAW,GAAG,WAAW,GAAG,WAAW,CAAC,GAAG,CAAC;6BAC5D,CAAC;wBACJ,CAAC,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,YAAY,EAA/B,CAA+B,CAAC,CAAC;wBAEnD,sBAAO;gCACL,KAAK,OAAA;gCACL,UAAU,EAAE,gBAAgB;6BAC7B,EAAC;;;;KACH;IAEK,8DAA6B,GAAnC,UAAoC,OAAoC;uCAAG,OAAO;;;;;4BAEtD,qBAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,aAAa,CAAC,EAAA;;wBAAjF,iBAAiB,GAAG,SAA6D;wBAGjF,qBAAqB,GAAG,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;wBAChF,qBAAM,IAAI,CAAC,2BAA2B,CAAC,qBAAqB,CAAC,EAAA;;wBAArF,qBAAqB,GAAG,SAA6D;wBAGrF,gBAAgB,GAAG,qBAAqB;6BAC3C,GAAG,CAAC,UAAA,SAAS,IAAI,OAAA,CAAC;4BACjB,KAAK,EAAE,SAAS,CAAC,KAAK;4BACtB,aAAa,EAAE,KAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,OAAO,CAAC;4BAC9D,SAAS,EAAE,KAAI,CAAC,+BAA+B,CAAC,SAAS,EAAE,OAAO,CAAC;4BACnE,UAAU,EAAE,SAAS;yBACtB,CAAC,EALgB,CAKhB,CAAC;6BACF,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,aAAa,EAAjC,CAAiC,CAAC,CAAC;wBAG/C,aAAa,mCAAO,iBAAiB,SAAK,qBAAqB,OAAC,CAAC;wBACxD,qBAAM,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAP,CAAO,CAAC,CAAC,EAAA;;wBAAxE,MAAM,GAAG,SAA+D;wBAGxE,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;wBACtF,kBAAkB,GAAG,IAAI,CAAC,wBAAwB,iCAAK,iBAAiB,SAAK,qBAAqB,SAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;wBAE7H,sBAAO;gCACL,iBAAiB,EAAE,qBAAqB;gCACxC,gBAAgB,kBAAA;gCAChB,mBAAmB,EAAE,MAAM,CAAC,UAAU;gCACtC,YAAY,EAAE;oCACZ,gBAAgB,kBAAA;oCAChB,kBAAkB,oBAAA;oCAClB,QAAQ,EAAE,kBAAkB,GAAG,gBAAgB;iCAChD;6BACF,EAAC;;;;KACH;IAEK,sDAAqB,GAA3B,UAA4B,MAAgB;uCAAG,OAAO;;;;;;wBAE9C,SAAS,GAAG,EAAE,CAAC;wBACf,OAAO,GAAG,EAAE,CAAC;wBAEnB,KAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;4BAClD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;wBAC/C,CAAC;8BAE0B,EAAP,mBAAO;;;6BAAP,CAAA,qBAAO,CAAA;wBAAhB,KAAK;wBACd,qBAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,KAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,EAAtD,CAAsD,CAAC,CAAC,EAAA;;wBAA7F,SAA6F,CAAC;;;wBAD5E,IAAO,CAAA;;;;;;KAG5B;IAEK,mDAAkB,GAAxB;uCAA4B,OAAO;;;gBAC3B,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,CAAC;oBAC/C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;oBACtD,CAAC,CAAC,CAAC,CAAC;gBAEA,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,CAAC;oBAChD,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;oBACxD,CAAC,CAAC,CAAC,CAAC;gBAEN,sBAAO;wBACL,OAAO,SAAA;wBACP,QAAQ,UAAA;wBACR,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,aAAa;wBAC5C,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;qBAC3B,EAAC;;;KACH;IAEK,2CAAU,GAAhB;uCAAoB,OAAO;;gBACzB,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;gBACnB,IAAI,CAAC,UAAU,GAAG;oBAChB,IAAI,EAAE,CAAC;oBACP,MAAM,EAAE,CAAC;oBACT,aAAa,EAAE,CAAC;iBACjB,CAAC;;;;KACH;IAEa,uDAAsB,GAApC,UAAqC,KAAa;uCAAG,OAAO;;;;;6BAEtD,CAAA,MAAM,CAAC,KAAK,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,UAAU,CAAA,EAAlD,wBAAkD;;;;wBAEjC,qBAAM,MAAM,CAAC,KAAK,CAAC,sBAAe,KAAK,CAAE,CAAC,EAAA;;wBAArD,QAAQ,GAAG,SAA0C;wBAC3D,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;4BACjB,MAAM,IAAI,KAAK,CAAC,qBAAc,QAAQ,CAAC,MAAM,CAAE,CAAC,CAAC;wBACnD,CAAC;;;;wBAID,oDAAoD;wBACpD,MAAM,OAAK,CAAC;;oBAIhB,0BAA0B;oBAC1B,qBAAM,IAAI,OAAO,CAAC,UAAA,OAAO,IAAI,OAAA,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,EAAxB,CAAwB,CAAC,EAAA;;wBADtD,0BAA0B;wBAC1B,SAAsD,CAAC;wBAEjD,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;wBAC5C,IAAI,CAAC,QAAQ,EAAE,CAAC;4BACd,sBAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAC;wBAC1C,CAAC;wBAED,sBAAO;gCACL,KAAK,OAAA;gCACL,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,CAAC;gCAC5B,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,CAAC;gCAC5B,aAAa,EAAE,QAAQ,CAAC,aAAa,IAAI,CAAC;gCAC1C,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,CAAC;gCAC5B,UAAU,EAAE,QAAQ,CAAC,UAAU,IAAI,CAAC;gCACpC,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,EAAE;gCACvC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,SAAS;gCACxC,WAAW,EAAE,IAAI,IAAI,EAAE;6BACxB,EAAC;;;;KACH;IAEO,qDAAoB,GAA5B,UAA6B,KAAa;QACxC,OAAO;YACL,KAAK,OAAA;YACL,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,CAAC;YACT,aAAa,EAAE,CAAC;YAChB,MAAM,EAAE,CAAC;YACT,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,EAAE;YACf,QAAQ,EAAE,SAAS;YACnB,WAAW,EAAE,IAAI,IAAI,EAAE;YACvB,OAAO,EAAE,IAAI;SACd,CAAC;IACJ,CAAC;IAEO,oDAAmB,GAA3B,UAA4B,WAAqB,EAAE,UAAkB;QACnE,IAAM,KAAK,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5D,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAChC,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE/B,IAAI,KAAK,KAAK,KAAK,EAAE,CAAC;YACpB,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC;QAED,IAAM,MAAM,GAAG,KAAK,GAAG,KAAK,CAAC;QAC7B,OAAO,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;IACzE,CAAC;IAEO,uDAAsB,GAA9B,UAA+B,QAAgB;QAC7C,IAAM,WAAW,GAA6E;YAC5F,eAAe,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,EAAE;YAChE,UAAU,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,EAAE;YAC3D,QAAQ,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,EAAE;YACzD,QAAQ,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,EAAE;SAC1D,CAAC;QAEF,OAAO,WAAW,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,EAAE,CAAC;IAChG,CAAC;IAEO,6DAA4B,GAApC,UAAqC,KAAa;QAChD,uCAAuC;QACvC,IAAM,YAAY,GAAG,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;QAC/E,OAAO,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC9D,CAAC;IAEO,uDAAsB,GAA9B,UAA+B,QAAgB;QAC7C,IAAM,WAAW,GAAuE;YACtF,UAAU,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;YACrD,OAAO,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;YAClD,UAAU,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;YACrD,SAAS,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;YACpD,MAAM,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;SAClD,CAAC;QAEF,OAAO,WAAW,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;IAC1F,CAAC;IAEO,2DAA0B,GAAlC,UAAmC,QAAgB;QACjD,IAAM,SAAS,GAA6B;YAC1C,UAAU,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;YAC9D,OAAO,EAAE,CAAC,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,aAAa,CAAC;YAC1F,UAAU,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,QAAQ,CAAC;YAC9E,SAAS,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,UAAU,EAAE,cAAc,EAAE,OAAO,CAAC;YAC1E,MAAM,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,CAAC;SAChE,CAAC;QAEF,OAAO,SAAS,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;IACjD,CAAC;IAEO,8DAA6B,GAArC,UAAsC,UAAkB,EAAE,eAAuB;;QAC/E,IAAM,YAAY,GAA6C;YAC7D,sBAAsB,EAAE;gBACtB,QAAQ,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC;gBAChD,YAAY,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,CAAC;gBAC5D,QAAQ,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,eAAe,CAAC;aAC3D;YACD,oBAAoB,EAAE;gBACpB,QAAQ,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,CAAC;gBAChD,YAAY,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC;gBACxD,QAAQ,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,CAAC;aAC1D;SACF,CAAC;QAEF,IAAM,gBAAgB,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;QAClD,IAAM,MAAM,GAAG,CAAA,MAAA,YAAY,CAAC,gBAAgB,CAAC,0CAAG,eAAe,CAAC,KAAI,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAEtG,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,uDAAsB,GAA9B,UAA+B,SAA0B,EAAE,OAAoC;QAC7F,IAAM,YAAY,GAAG,GAAG,CAAC;QACzB,IAAM,YAAY,GAAG,IAAI,CAAC;QAC1B,IAAM,YAAY,GAAG,GAAG,CAAC;QACzB,IAAM,SAAS,GAAG,IAAI,CAAC;QACvB,IAAM,gBAAgB,GAAG,GAAG,CAAC;QAE7B,IAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC;QACrC,IAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC;QACrC,IAAM,WAAW,GAAG,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC,YAAY;QAChE,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;QAClE,IAAM,eAAe,GAAG,EAAE,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,4CAA4C;QAE/F,OAAO,CACL,WAAW,GAAG,YAAY;YAC1B,WAAW,GAAG,YAAY;YAC1B,WAAW,GAAG,YAAY;YAC1B,QAAQ,GAAG,SAAS;YACpB,eAAe,GAAG,gBAAgB,CACnC,CAAC;IACJ,CAAC;IAEO,gEAA+B,GAAvC,UAAwC,SAA0B,EAAE,OAAoC;QACtG,IAAM,OAAO,GAAG,EAAE,CAAC;QAEnB,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC1C,CAAC;QACD,IAAI,SAAS,CAAC,aAAa,GAAG,KAAK,EAAE,CAAC;YACpC,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC7C,CAAC;QACD,IAAI,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;YACxC,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC;YACvB,CAAC,CAAC,6BAAsB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE;YAC5C,CAAC,CAAC,+CAA+C,CAAC;IACtD,CAAC;IAEO,yDAAwB,GAAhC,UAAiC,UAA6B,EAAE,QAAgB;QAC9E,IAAM,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;QACxE,IAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,KAAK,IAAK,OAAA,GAAG,GAAG,KAAK,CAAC,aAAa,EAAzB,CAAyB,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC;QAE1G,OAAO,aAAa,GAAG,kBAAkB,CAAC;IAC5C,CAAC;IACH,6BAAC;AAAD,CAAC,AAviBD,IAuiBC;AAviBY,wDAAsB",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/SkillMarketDataService.ts"],
      sourcesContent: ["export interface SkillMarketData {\n  skill: string;\n  demand: number; // 0-100 scale\n  supply: number; // 0-100 scale\n  averageSalary: number;\n  growth: number; // percentage\n  difficulty: number; // 1-10 scale\n  timeToLearn: number; // weeks\n  category: string;\n  lastUpdated: Date;\n  isStale?: boolean;\n}\n\nexport interface MarketTrends {\n  topDemandSkills: SkillMarketData[];\n  fastestGrowingSkills: SkillMarketData[];\n  highestPayingSkills: SkillMarketData[];\n  marketGaps: MarketGap[];\n  emergingSkills: SkillMarketData[];\n  decliningSkills: SkillMarketData[];\n}\n\nexport interface MarketGap {\n  skill: string;\n  demandSupplyRatio: number;\n  opportunityScore: number;\n  marketData: SkillMarketData;\n}\n\nexport interface SalaryInsights {\n  averageSalary: number;\n  salaryRange: {\n    min: number;\n    max: number;\n  };\n  percentiles: {\n    p25: number;\n    p50: number;\n    p75: number;\n    p90: number;\n  };\n  topPayingSkills: SkillMarketData[];\n  salaryGrowthPotential: number;\n}\n\nexport interface LocationMarketData {\n  skill: string;\n  location: string;\n  localDemand: number;\n  localSalary: number;\n  costOfLivingAdjustment: number;\n  remoteOpportunities: number;\n}\n\nexport interface LocationComparison {\n  skill: string;\n  locationComparison: {\n    location: string;\n    demand: number;\n    salary: number;\n    costOfLivingIndex: number;\n    adjustedSalary: number;\n  }[];\n}\n\nexport interface IndustryMarketData {\n  skill: string;\n  industry: string;\n  industryDemand: number;\n  averageIndustrySalary: number;\n  growthProjection: number;\n  keyCompanies: string[];\n}\n\nexport interface IndustryRanking {\n  skill: string;\n  industries: {\n    industry: string;\n    demandScore: number;\n    salaryScore: number;\n    growthScore: number;\n    overallScore: number;\n  }[];\n}\n\nexport interface MarketRecommendationRequest {\n  currentSkills: string[];\n  careerGoal: string;\n  location: string;\n  experienceLevel: 'beginner' | 'intermediate' | 'advanced';\n}\n\nexport interface MarketBasedRecommendations {\n  recommendedSkills: SkillMarketData[];\n  learningPriority: {\n    skill: string;\n    priorityScore: number;\n    reasoning: string;\n    marketData: SkillMarketData;\n  }[];\n  marketOpportunities: MarketGap[];\n  salaryImpact: {\n    currentPotential: number;\n    projectedPotential: number;\n    increase: number;\n  };\n}\n\nexport interface CacheStatistics {\n  hitRate: number;\n  missRate: number;\n  totalRequests: number;\n  cacheSize: number;\n}\n\nexport interface MarketDataOptions {\n  forceRefresh?: boolean;\n  maxAge?: number; // milliseconds\n}\n\nexport class SkillMarketDataService {\n  private cache: Map<string, { data: SkillMarketData; timestamp: number }> = new Map();\n  private cacheStats = {\n    hits: 0,\n    misses: 0,\n    totalRequests: 0,\n  };\n  private readonly CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours\n  private edgeCaseHandler: any; // Will be injected\n\n  constructor() {\n    // EdgeCaseHandler will be injected later to avoid circular dependencies\n  }\n\n  setEdgeCaseHandler(handler: any) {\n    this.edgeCaseHandler = handler;\n  }\n\n  // Mock market data for testing and development\n  private mockMarketData: Record<string, Partial<SkillMarketData>> = {\n    javascript: {\n      demand: 85,\n      supply: 70,\n      averageSalary: 95000,\n      growth: 12.5,\n      difficulty: 6,\n      timeToLearn: 8,\n      category: 'Programming',\n    },\n    react: {\n      demand: 80,\n      supply: 65,\n      averageSalary: 90000,\n      growth: 15.2,\n      difficulty: 7,\n      timeToLearn: 10,\n      category: 'Frontend',\n    },\n    nodejs: {\n      demand: 75,\n      supply: 60,\n      averageSalary: 88000,\n      growth: 10.8,\n      difficulty: 6,\n      timeToLearn: 12,\n      category: 'Backend',\n    },\n    python: {\n      demand: 90,\n      supply: 75,\n      averageSalary: 100000,\n      growth: 18.7,\n      difficulty: 5,\n      timeToLearn: 6,\n      category: 'Programming',\n    },\n    typescript: {\n      demand: 78,\n      supply: 55,\n      averageSalary: 98000,\n      growth: 22.3,\n      difficulty: 7,\n      timeToLearn: 4,\n      category: 'Programming',\n    },\n  };\n\n  async getSkillMarketData(skill: string, options: MarketDataOptions = {}): Promise<SkillMarketData> {\n    if (!skill || skill.trim() === '') {\n      throw new Error('Invalid skill name');\n    }\n\n    const normalizedSkill = skill.toLowerCase().trim();\n    const cacheKey = `skill:${normalizedSkill}`;\n\n    this.cacheStats.totalRequests++;\n\n    // Check cache first\n    if (!options.forceRefresh) {\n      const cached = this.cache.get(cacheKey);\n      if (cached && Date.now() - cached.timestamp < (options.maxAge || this.CACHE_TTL)) {\n        this.cacheStats.hits++;\n        return cached.data;\n      }\n    }\n\n    this.cacheStats.misses++;\n\n    try {\n      // In a real implementation, this would call external APIs\n      const marketData = await this.fetchMarketDataFromAPI(normalizedSkill);\n\n      // Cache the result\n      this.cache.set(cacheKey, {\n        data: marketData,\n        timestamp: Date.now(),\n      });\n\n      return marketData;\n    } catch (error) {\n      // Return stale data if available, otherwise return default\n      const cached = this.cache.get(cacheKey);\n      if (cached) {\n        return { ...cached.data, isStale: true };\n      }\n\n      return this.getDefaultMarketData(normalizedSkill);\n    }\n  }\n\n  /**\n   * Get skill market data with comprehensive edge case handling\n   */\n  async getSkillMarketDataWithEdgeHandling(skill: string, options: MarketDataOptions = {}): Promise<any> {\n    if (this.edgeCaseHandler) {\n      return this.edgeCaseHandler.handleMarketDataRequest({ skill, ...options });\n    }\n\n    // Fallback to regular method if no edge case handler\n    try {\n      const data = await this.getSkillMarketData(skill, options);\n      return {\n        success: true,\n        data,\n        sanitizedInput: { skill, ...options }\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error',\n        errorType: 'SYSTEM_ERROR',\n        fallbackData: this.getDefaultMarketData(skill)\n      };\n    }\n  }\n\n  async getMultipleSkillsMarketData(skills: string[], options: MarketDataOptions = {}): Promise<SkillMarketData[]> {\n    if (!skills || skills.length === 0) {\n      throw new Error('No skills provided');\n    }\n\n    const promises = skills.map(skill => this.getSkillMarketData(skill, options));\n    return Promise.all(promises);\n  }\n\n  async analyzeMarketTrends(skills: string[]): Promise<MarketTrends> {\n    const marketDataList = await this.getMultipleSkillsMarketData(skills);\n\n    const topDemandSkills = [...marketDataList]\n      .sort((a, b) => b.demand - a.demand)\n      .slice(0, 5);\n\n    const fastestGrowingSkills = [...marketDataList]\n      .sort((a, b) => b.growth - a.growth)\n      .slice(0, 5);\n\n    const highestPayingSkills = [...marketDataList]\n      .sort((a, b) => b.averageSalary - a.averageSalary)\n      .slice(0, 5);\n\n    const marketGaps = marketDataList\n      .filter(data => data.demand > data.supply)\n      .map(data => ({\n        skill: data.skill,\n        demandSupplyRatio: data.demand / data.supply,\n        opportunityScore: (data.demand - data.supply) * (data.growth / 100),\n        marketData: data,\n      }))\n      .sort((a, b) => b.opportunityScore - a.opportunityScore);\n\n    const emergingSkills = marketDataList\n      .filter(data => data.growth > 15)\n      .sort((a, b) => b.growth - a.growth);\n\n    const decliningSkills = marketDataList\n      .filter(data => data.growth < 5)\n      .sort((a, b) => a.growth - b.growth);\n\n    return {\n      topDemandSkills,\n      fastestGrowingSkills,\n      highestPayingSkills,\n      marketGaps,\n      emergingSkills,\n      decliningSkills,\n    };\n  }\n\n  async getSalaryInsights(skills: string[]): Promise<SalaryInsights> {\n    const marketDataList = await this.getMultipleSkillsMarketData(skills);\n    const salaries = marketDataList.map(data => data.averageSalary);\n\n    const averageSalary = salaries.reduce((sum, salary) => sum + salary, 0) / salaries.length;\n    const sortedSalaries = [...salaries].sort((a, b) => a - b);\n\n    const percentiles = {\n      p25: this.calculatePercentile(sortedSalaries, 25),\n      p50: this.calculatePercentile(sortedSalaries, 50),\n      p75: this.calculatePercentile(sortedSalaries, 75),\n      p90: this.calculatePercentile(sortedSalaries, 90),\n    };\n\n    const topPayingSkills = [...marketDataList]\n      .sort((a, b) => b.averageSalary - a.averageSalary)\n      .slice(0, 5);\n\n    const salaryGrowthPotential = marketDataList\n      .reduce((sum, data) => sum + data.growth, 0) / marketDataList.length;\n\n    return {\n      averageSalary,\n      salaryRange: {\n        min: Math.min(...salaries),\n        max: Math.max(...salaries),\n      },\n      percentiles,\n      topPayingSkills,\n      salaryGrowthPotential,\n    };\n  }\n\n  async getLocationBasedMarketData(skill: string, location: string): Promise<LocationMarketData> {\n    const baseMarketData = await this.getSkillMarketData(skill);\n    \n    // Mock location-based adjustments\n    const locationMultipliers = this.getLocationMultipliers(location);\n    \n    return {\n      skill,\n      location,\n      localDemand: baseMarketData.demand * locationMultipliers.demand,\n      localSalary: baseMarketData.averageSalary * locationMultipliers.salary,\n      costOfLivingAdjustment: locationMultipliers.costOfLiving,\n      remoteOpportunities: this.calculateRemoteOpportunities(skill),\n    };\n  }\n\n  async compareLocationMarkets(skill: string, locations: string[]): Promise<LocationComparison> {\n    const baseMarketData = await this.getSkillMarketData(skill);\n    \n    const locationComparison = locations.map(location => {\n      const multipliers = this.getLocationMultipliers(location);\n      const salary = baseMarketData.averageSalary * multipliers.salary;\n      \n      return {\n        location,\n        demand: baseMarketData.demand * multipliers.demand,\n        salary,\n        costOfLivingIndex: multipliers.costOfLiving,\n        adjustedSalary: salary / multipliers.costOfLiving,\n      };\n    });\n\n    return {\n      skill,\n      locationComparison,\n    };\n  }\n\n  async getIndustryMarketData(skill: string, industry: string): Promise<IndustryMarketData> {\n    const baseMarketData = await this.getSkillMarketData(skill);\n    const industryMultipliers = this.getIndustryMultipliers(industry);\n    \n    return {\n      skill,\n      industry,\n      industryDemand: baseMarketData.demand * industryMultipliers.demand,\n      averageIndustrySalary: baseMarketData.averageSalary * industryMultipliers.salary,\n      growthProjection: baseMarketData.growth * industryMultipliers.growth,\n      keyCompanies: this.getKeyCompaniesForIndustry(industry),\n    };\n  }\n\n  async rankIndustriesBySkillDemand(skill: string): Promise<IndustryRanking> {\n    const industries = ['technology', 'finance', 'healthcare', 'education', 'retail'];\n    const baseMarketData = await this.getSkillMarketData(skill);\n    \n    const industryRankings = industries.map(industry => {\n      const multipliers = this.getIndustryMultipliers(industry);\n      const demandScore = baseMarketData.demand * multipliers.demand;\n      const salaryScore = baseMarketData.averageSalary * multipliers.salary / 1000; // Normalize\n      const growthScore = baseMarketData.growth * multipliers.growth;\n      \n      return {\n        industry,\n        demandScore,\n        salaryScore,\n        growthScore,\n        overallScore: (demandScore + salaryScore + growthScore) / 3,\n      };\n    }).sort((a, b) => b.overallScore - a.overallScore);\n\n    return {\n      skill,\n      industries: industryRankings,\n    };\n  }\n\n  async getMarketBasedRecommendations(request: MarketRecommendationRequest): Promise<MarketBasedRecommendations> {\n    // Get market data for current skills\n    const currentSkillsData = await this.getMultipleSkillsMarketData(request.currentSkills);\n    \n    // Get recommended skills based on career goal\n    const recommendedSkillNames = this.getRecommendedSkillsForCareer(request.careerGoal, request.experienceLevel);\n    const recommendedSkillsData = await this.getMultipleSkillsMarketData(recommendedSkillNames);\n    \n    // Calculate learning priority\n    const learningPriority = recommendedSkillsData\n      .map(skillData => ({\n        skill: skillData.skill,\n        priorityScore: this.calculatePriorityScore(skillData, request),\n        reasoning: this.generateRecommendationReasoning(skillData, request),\n        marketData: skillData,\n      }))\n      .sort((a, b) => b.priorityScore - a.priorityScore);\n\n    // Identify market opportunities\n    const allSkillsData = [...currentSkillsData, ...recommendedSkillsData];\n    const trends = await this.analyzeMarketTrends(allSkillsData.map(s => s.skill));\n    \n    // Calculate salary impact\n    const currentPotential = this.calculateSalaryPotential(currentSkillsData, request.location);\n    const projectedPotential = this.calculateSalaryPotential([...currentSkillsData, ...recommendedSkillsData], request.location);\n    \n    return {\n      recommendedSkills: recommendedSkillsData,\n      learningPriority,\n      marketOpportunities: trends.marketGaps,\n      salaryImpact: {\n        currentPotential,\n        projectedPotential,\n        increase: projectedPotential - currentPotential,\n      },\n    };\n  }\n\n  async batchUpdateMarketData(skills: string[]): Promise<void> {\n    // Batch update for efficiency\n    const batchSize = 10;\n    const batches = [];\n    \n    for (let i = 0; i < skills.length; i += batchSize) {\n      batches.push(skills.slice(i, i + batchSize));\n    }\n\n    for (const batch of batches) {\n      await Promise.all(batch.map(skill => this.getSkillMarketData(skill, { forceRefresh: true })));\n    }\n  }\n\n  async getCacheStatistics(): Promise<CacheStatistics> {\n    const hitRate = this.cacheStats.totalRequests > 0 \n      ? this.cacheStats.hits / this.cacheStats.totalRequests \n      : 0;\n    \n    const missRate = this.cacheStats.totalRequests > 0 \n      ? this.cacheStats.misses / this.cacheStats.totalRequests \n      : 0;\n\n    return {\n      hitRate,\n      missRate,\n      totalRequests: this.cacheStats.totalRequests,\n      cacheSize: this.cache.size,\n    };\n  }\n\n  async clearCache(): Promise<void> {\n    this.cache.clear();\n    this.cacheStats = {\n      hits: 0,\n      misses: 0,\n      totalRequests: 0,\n    };\n  }\n\n  private async fetchMarketDataFromAPI(skill: string): Promise<SkillMarketData> {\n    // Check if fetch is mocked and will fail\n    if (global.fetch && typeof global.fetch === 'function') {\n      try {\n        const response = await global.fetch(`/api/skills/${skill}`);\n        if (!response.ok) {\n          throw new Error(`API Error: ${response.status}`);\n        }\n        // In a real implementation, we would parse the response\n        // For testing, we'll fall through to mock data\n      } catch (error) {\n        // API failed, throw error to trigger fallback logic\n        throw error;\n      }\n    }\n\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 100));\n\n    const mockData = this.mockMarketData[skill];\n    if (!mockData) {\n      return this.getDefaultMarketData(skill);\n    }\n\n    return {\n      skill,\n      demand: mockData.demand || 0,\n      supply: mockData.supply || 0,\n      averageSalary: mockData.averageSalary || 0,\n      growth: mockData.growth || 0,\n      difficulty: mockData.difficulty || 5,\n      timeToLearn: mockData.timeToLearn || 12,\n      category: mockData.category || 'General',\n      lastUpdated: new Date(),\n    };\n  }\n\n  private getDefaultMarketData(skill: string): SkillMarketData {\n    return {\n      skill,\n      demand: 0,\n      supply: 0,\n      averageSalary: 0,\n      growth: 0,\n      difficulty: 5,\n      timeToLearn: 12,\n      category: 'Unknown',\n      lastUpdated: new Date(),\n      isStale: true,\n    };\n  }\n\n  private calculatePercentile(sortedArray: number[], percentile: number): number {\n    const index = (percentile / 100) * (sortedArray.length - 1);\n    const lower = Math.floor(index);\n    const upper = Math.ceil(index);\n    \n    if (lower === upper) {\n      return sortedArray[lower];\n    }\n    \n    const weight = index - lower;\n    return sortedArray[lower] * (1 - weight) + sortedArray[upper] * weight;\n  }\n\n  private getLocationMultipliers(location: string): { demand: number; salary: number; costOfLiving: number } {\n    const multipliers: Record<string, { demand: number; salary: number; costOfLiving: number }> = {\n      'san francisco': { demand: 1.3, salary: 1.4, costOfLiving: 1.8 },\n      'new york': { demand: 1.2, salary: 1.3, costOfLiving: 1.6 },\n      'austin': { demand: 1.1, salary: 1.1, costOfLiving: 1.2 },\n      'remote': { demand: 1.0, salary: 1.0, costOfLiving: 1.0 },\n    };\n\n    return multipliers[location.toLowerCase()] || { demand: 1.0, salary: 1.0, costOfLiving: 1.0 };\n  }\n\n  private calculateRemoteOpportunities(skill: string): number {\n    // Mock calculation based on skill type\n    const remoteSkills = ['javascript', 'react', 'nodejs', 'python', 'typescript'];\n    return remoteSkills.includes(skill.toLowerCase()) ? 85 : 45;\n  }\n\n  private getIndustryMultipliers(industry: string): { demand: number; salary: number; growth: number } {\n    const multipliers: Record<string, { demand: number; salary: number; growth: number }> = {\n      technology: { demand: 1.3, salary: 1.2, growth: 1.4 },\n      finance: { demand: 1.1, salary: 1.3, growth: 1.1 },\n      healthcare: { demand: 0.8, salary: 0.9, growth: 1.2 },\n      education: { demand: 0.6, salary: 0.7, growth: 0.8 },\n      retail: { demand: 0.7, salary: 0.8, growth: 0.9 },\n    };\n\n    return multipliers[industry.toLowerCase()] || { demand: 1.0, salary: 1.0, growth: 1.0 };\n  }\n\n  private getKeyCompaniesForIndustry(industry: string): string[] {\n    const companies: Record<string, string[]> = {\n      technology: ['Google', 'Microsoft', 'Apple', 'Amazon', 'Meta'],\n      finance: ['JPMorgan', 'Goldman Sachs', 'Morgan Stanley', 'Bank of America', 'Wells Fargo'],\n      healthcare: ['Johnson & Johnson', 'Pfizer', 'UnitedHealth', 'Merck', 'AbbVie'],\n      education: ['Pearson', 'McGraw-Hill', 'Coursera', 'Khan Academy', 'Udemy'],\n      retail: ['Amazon', 'Walmart', 'Target', 'Home Depot', 'Costco'],\n    };\n\n    return companies[industry.toLowerCase()] || [];\n  }\n\n  private getRecommendedSkillsForCareer(careerGoal: string, experienceLevel: string): string[] {\n    const careerSkills: Record<string, Record<string, string[]>> = {\n      'full stack developer': {\n        beginner: ['javascript', 'html', 'css', 'react'],\n        intermediate: ['nodejs', 'typescript', 'mongodb', 'express'],\n        advanced: ['docker', 'kubernetes', 'aws', 'microservices'],\n      },\n      'frontend developer': {\n        beginner: ['html', 'css', 'javascript', 'react'],\n        intermediate: ['typescript', 'redux', 'webpack', 'sass'],\n        advanced: ['nextjs', 'graphql', 'testing', 'performance'],\n      },\n    };\n\n    const normalizedCareer = careerGoal.toLowerCase();\n    const skills = careerSkills[normalizedCareer]?.[experienceLevel] || ['javascript', 'react', 'nodejs'];\n    \n    return skills;\n  }\n\n  private calculatePriorityScore(skillData: SkillMarketData, request: MarketRecommendationRequest): number {\n    const demandWeight = 0.3;\n    const growthWeight = 0.25;\n    const salaryWeight = 0.2;\n    const gapWeight = 0.15;\n    const difficultyWeight = 0.1;\n\n    const demandScore = skillData.demand;\n    const growthScore = skillData.growth;\n    const salaryScore = skillData.averageSalary / 1000; // Normalize\n    const gapScore = Math.max(0, skillData.demand - skillData.supply);\n    const difficultyScore = 11 - skillData.difficulty; // Invert difficulty (easier = higher score)\n\n    return (\n      demandScore * demandWeight +\n      growthScore * growthWeight +\n      salaryScore * salaryWeight +\n      gapScore * gapWeight +\n      difficultyScore * difficultyWeight\n    );\n  }\n\n  private generateRecommendationReasoning(skillData: SkillMarketData, request: MarketRecommendationRequest): string {\n    const reasons = [];\n\n    if (skillData.demand > 80) {\n      reasons.push('high market demand');\n    }\n    if (skillData.growth > 15) {\n      reasons.push('rapid growth trajectory');\n    }\n    if (skillData.averageSalary > 90000) {\n      reasons.push('excellent salary potential');\n    }\n    if (skillData.demand > skillData.supply) {\n      reasons.push('market opportunity gap');\n    }\n\n    return reasons.length > 0 \n      ? `Recommended due to ${reasons.join(', ')}`\n      : 'Solid foundational skill for your career path';\n  }\n\n  private calculateSalaryPotential(skillsData: SkillMarketData[], location: string): number {\n    const locationMultiplier = this.getLocationMultipliers(location).salary;\n    const averageSalary = skillsData.reduce((sum, skill) => sum + skill.averageSalary, 0) / skillsData.length;\n    \n    return averageSalary * locationMultiplier;\n  }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "2049000702a22369bc42c5a0a2ffc98ee037e0d1"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_xt36771t1 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_xt36771t1();
var __assign =
/* istanbul ignore next */
(cov_xt36771t1().s[0]++,
/* istanbul ignore next */
(cov_xt36771t1().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_xt36771t1().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_xt36771t1().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_xt36771t1().f[0]++;
  cov_xt36771t1().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_xt36771t1().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_xt36771t1().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_xt36771t1().f[1]++;
    cov_xt36771t1().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_xt36771t1().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_xt36771t1().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_xt36771t1().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_xt36771t1().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_xt36771t1().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_xt36771t1().b[2][0]++;
          cov_xt36771t1().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_xt36771t1().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_xt36771t1().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_xt36771t1().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_xt36771t1().s[11]++,
/* istanbul ignore next */
(cov_xt36771t1().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_xt36771t1().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_xt36771t1().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_xt36771t1().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_xt36771t1().f[3]++;
    cov_xt36771t1().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_xt36771t1().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_xt36771t1().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_xt36771t1().f[4]++;
      cov_xt36771t1().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_xt36771t1().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_xt36771t1().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_xt36771t1().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_xt36771t1().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_xt36771t1().f[6]++;
      cov_xt36771t1().s[15]++;
      try {
        /* istanbul ignore next */
        cov_xt36771t1().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_xt36771t1().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_xt36771t1().f[7]++;
      cov_xt36771t1().s[18]++;
      try {
        /* istanbul ignore next */
        cov_xt36771t1().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_xt36771t1().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_xt36771t1().f[8]++;
      cov_xt36771t1().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_xt36771t1().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_xt36771t1().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_xt36771t1().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_xt36771t1().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_xt36771t1().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_xt36771t1().s[23]++,
/* istanbul ignore next */
(cov_xt36771t1().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_xt36771t1().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_xt36771t1().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_xt36771t1().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_xt36771t1().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_xt36771t1().f[10]++;
        cov_xt36771t1().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_xt36771t1().b[9][0]++;
          cov_xt36771t1().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_xt36771t1().b[9][1]++;
        }
        cov_xt36771t1().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_xt36771t1().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_xt36771t1().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_xt36771t1().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_xt36771t1().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_xt36771t1().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_xt36771t1().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_xt36771t1().f[11]++;
    cov_xt36771t1().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_xt36771t1().f[12]++;
    cov_xt36771t1().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_xt36771t1().f[13]++;
      cov_xt36771t1().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_xt36771t1().f[14]++;
    cov_xt36771t1().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_xt36771t1().b[12][0]++;
      cov_xt36771t1().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_xt36771t1().b[12][1]++;
    }
    cov_xt36771t1().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_xt36771t1().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_xt36771t1().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_xt36771t1().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_xt36771t1().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_xt36771t1().s[36]++;
      try {
        /* istanbul ignore next */
        cov_xt36771t1().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_xt36771t1().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_xt36771t1().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_xt36771t1().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_xt36771t1().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_xt36771t1().b[18][0]++,
        /* istanbul ignore next */
        (cov_xt36771t1().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_xt36771t1().b[19][1]++,
        /* istanbul ignore next */
        (cov_xt36771t1().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_xt36771t1().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_xt36771t1().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_xt36771t1().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_xt36771t1().b[15][0]++;
          cov_xt36771t1().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_xt36771t1().b[15][1]++;
        }
        cov_xt36771t1().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_xt36771t1().b[21][0]++;
          cov_xt36771t1().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_xt36771t1().b[21][1]++;
        }
        cov_xt36771t1().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_xt36771t1().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_xt36771t1().b[22][1]++;
            cov_xt36771t1().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_xt36771t1().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_xt36771t1().b[22][2]++;
            cov_xt36771t1().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_xt36771t1().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_xt36771t1().b[22][3]++;
            cov_xt36771t1().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_xt36771t1().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_xt36771t1().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_xt36771t1().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_xt36771t1().b[22][4]++;
            cov_xt36771t1().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_xt36771t1().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_xt36771t1().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_xt36771t1().b[22][5]++;
            cov_xt36771t1().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_xt36771t1().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_xt36771t1().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_xt36771t1().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_xt36771t1().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_xt36771t1().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_xt36771t1().b[23][0]++;
              cov_xt36771t1().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_xt36771t1().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_xt36771t1().b[23][1]++;
            }
            cov_xt36771t1().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_xt36771t1().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_xt36771t1().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_xt36771t1().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_xt36771t1().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_xt36771t1().b[26][0]++;
              cov_xt36771t1().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_xt36771t1().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_xt36771t1().b[26][1]++;
            }
            cov_xt36771t1().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_xt36771t1().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_xt36771t1().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_xt36771t1().b[28][0]++;
              cov_xt36771t1().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_xt36771t1().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_xt36771t1().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_xt36771t1().b[28][1]++;
            }
            cov_xt36771t1().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_xt36771t1().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_xt36771t1().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_xt36771t1().b[30][0]++;
              cov_xt36771t1().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_xt36771t1().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_xt36771t1().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_xt36771t1().b[30][1]++;
            }
            cov_xt36771t1().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_xt36771t1().b[32][0]++;
              cov_xt36771t1().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_xt36771t1().b[32][1]++;
            }
            cov_xt36771t1().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_xt36771t1().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_xt36771t1().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_xt36771t1().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_xt36771t1().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_xt36771t1().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_xt36771t1().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_xt36771t1().b[33][0]++;
      cov_xt36771t1().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_xt36771t1().b[33][1]++;
    }
    cov_xt36771t1().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_xt36771t1().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_xt36771t1().b[34][1]++, void 0),
      done: true
    };
  }
}));
var __spreadArray =
/* istanbul ignore next */
(cov_xt36771t1().s[78]++,
/* istanbul ignore next */
(cov_xt36771t1().b[35][0]++, this) &&
/* istanbul ignore next */
(cov_xt36771t1().b[35][1]++, this.__spreadArray) ||
/* istanbul ignore next */
(cov_xt36771t1().b[35][2]++, function (to, from, pack) {
  /* istanbul ignore next */
  cov_xt36771t1().f[15]++;
  cov_xt36771t1().s[79]++;
  if (
  /* istanbul ignore next */
  (cov_xt36771t1().b[37][0]++, pack) ||
  /* istanbul ignore next */
  (cov_xt36771t1().b[37][1]++, arguments.length === 2)) {
    /* istanbul ignore next */
    cov_xt36771t1().b[36][0]++;
    cov_xt36771t1().s[80]++;
    for (var i =
      /* istanbul ignore next */
      (cov_xt36771t1().s[81]++, 0), l =
      /* istanbul ignore next */
      (cov_xt36771t1().s[82]++, from.length), ar; i < l; i++) {
      /* istanbul ignore next */
      cov_xt36771t1().s[83]++;
      if (
      /* istanbul ignore next */
      (cov_xt36771t1().b[39][0]++, ar) ||
      /* istanbul ignore next */
      (cov_xt36771t1().b[39][1]++, !(i in from))) {
        /* istanbul ignore next */
        cov_xt36771t1().b[38][0]++;
        cov_xt36771t1().s[84]++;
        if (!ar) {
          /* istanbul ignore next */
          cov_xt36771t1().b[40][0]++;
          cov_xt36771t1().s[85]++;
          ar = Array.prototype.slice.call(from, 0, i);
        } else
        /* istanbul ignore next */
        {
          cov_xt36771t1().b[40][1]++;
        }
        cov_xt36771t1().s[86]++;
        ar[i] = from[i];
      } else
      /* istanbul ignore next */
      {
        cov_xt36771t1().b[38][1]++;
      }
    }
  } else
  /* istanbul ignore next */
  {
    cov_xt36771t1().b[36][1]++;
  }
  cov_xt36771t1().s[87]++;
  return to.concat(
  /* istanbul ignore next */
  (cov_xt36771t1().b[41][0]++, ar) ||
  /* istanbul ignore next */
  (cov_xt36771t1().b[41][1]++, Array.prototype.slice.call(from)));
}));
/* istanbul ignore next */
cov_xt36771t1().s[88]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_xt36771t1().s[89]++;
exports.SkillMarketDataService = void 0;
var SkillMarketDataService =
/* istanbul ignore next */
(/** @class */cov_xt36771t1().s[90]++, function () {
  /* istanbul ignore next */
  cov_xt36771t1().f[16]++;
  function SkillMarketDataService() {
    /* istanbul ignore next */
    cov_xt36771t1().f[17]++;
    cov_xt36771t1().s[91]++;
    this.cache = new Map();
    /* istanbul ignore next */
    cov_xt36771t1().s[92]++;
    this.cacheStats = {
      hits: 0,
      misses: 0,
      totalRequests: 0
    };
    /* istanbul ignore next */
    cov_xt36771t1().s[93]++;
    this.CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours
    // Mock market data for testing and development
    /* istanbul ignore next */
    cov_xt36771t1().s[94]++;
    this.mockMarketData = {
      javascript: {
        demand: 85,
        supply: 70,
        averageSalary: 95000,
        growth: 12.5,
        difficulty: 6,
        timeToLearn: 8,
        category: 'Programming'
      },
      react: {
        demand: 80,
        supply: 65,
        averageSalary: 90000,
        growth: 15.2,
        difficulty: 7,
        timeToLearn: 10,
        category: 'Frontend'
      },
      nodejs: {
        demand: 75,
        supply: 60,
        averageSalary: 88000,
        growth: 10.8,
        difficulty: 6,
        timeToLearn: 12,
        category: 'Backend'
      },
      python: {
        demand: 90,
        supply: 75,
        averageSalary: 100000,
        growth: 18.7,
        difficulty: 5,
        timeToLearn: 6,
        category: 'Programming'
      },
      typescript: {
        demand: 78,
        supply: 55,
        averageSalary: 98000,
        growth: 22.3,
        difficulty: 7,
        timeToLearn: 4,
        category: 'Programming'
      }
    };
    // EdgeCaseHandler will be injected later to avoid circular dependencies
  }
  /* istanbul ignore next */
  cov_xt36771t1().s[95]++;
  SkillMarketDataService.prototype.setEdgeCaseHandler = function (handler) {
    /* istanbul ignore next */
    cov_xt36771t1().f[18]++;
    cov_xt36771t1().s[96]++;
    this.edgeCaseHandler = handler;
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[97]++;
  SkillMarketDataService.prototype.getSkillMarketData = function (skill_1) {
    /* istanbul ignore next */
    cov_xt36771t1().f[19]++;
    cov_xt36771t1().s[98]++;
    return __awaiter(this, arguments, Promise, function (skill, options) {
      /* istanbul ignore next */
      cov_xt36771t1().f[20]++;
      var normalizedSkill, cacheKey, cached, marketData, error_1, cached;
      /* istanbul ignore next */
      cov_xt36771t1().s[99]++;
      if (options === void 0) {
        /* istanbul ignore next */
        cov_xt36771t1().b[42][0]++;
        cov_xt36771t1().s[100]++;
        options = {};
      } else
      /* istanbul ignore next */
      {
        cov_xt36771t1().b[42][1]++;
      }
      cov_xt36771t1().s[101]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_xt36771t1().f[21]++;
        cov_xt36771t1().s[102]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_xt36771t1().b[43][0]++;
            cov_xt36771t1().s[103]++;
            if (
            /* istanbul ignore next */
            (cov_xt36771t1().b[45][0]++, !skill) ||
            /* istanbul ignore next */
            (cov_xt36771t1().b[45][1]++, skill.trim() === '')) {
              /* istanbul ignore next */
              cov_xt36771t1().b[44][0]++;
              cov_xt36771t1().s[104]++;
              throw new Error('Invalid skill name');
            } else
            /* istanbul ignore next */
            {
              cov_xt36771t1().b[44][1]++;
            }
            cov_xt36771t1().s[105]++;
            normalizedSkill = skill.toLowerCase().trim();
            /* istanbul ignore next */
            cov_xt36771t1().s[106]++;
            cacheKey = "skill:".concat(normalizedSkill);
            /* istanbul ignore next */
            cov_xt36771t1().s[107]++;
            this.cacheStats.totalRequests++;
            // Check cache first
            /* istanbul ignore next */
            cov_xt36771t1().s[108]++;
            if (!options.forceRefresh) {
              /* istanbul ignore next */
              cov_xt36771t1().b[46][0]++;
              cov_xt36771t1().s[109]++;
              cached = this.cache.get(cacheKey);
              /* istanbul ignore next */
              cov_xt36771t1().s[110]++;
              if (
              /* istanbul ignore next */
              (cov_xt36771t1().b[48][0]++, cached) &&
              /* istanbul ignore next */
              (cov_xt36771t1().b[48][1]++, Date.now() - cached.timestamp < (
              /* istanbul ignore next */
              (cov_xt36771t1().b[49][0]++, options.maxAge) ||
              /* istanbul ignore next */
              (cov_xt36771t1().b[49][1]++, this.CACHE_TTL)))) {
                /* istanbul ignore next */
                cov_xt36771t1().b[47][0]++;
                cov_xt36771t1().s[111]++;
                this.cacheStats.hits++;
                /* istanbul ignore next */
                cov_xt36771t1().s[112]++;
                return [2 /*return*/, cached.data];
              } else
              /* istanbul ignore next */
              {
                cov_xt36771t1().b[47][1]++;
              }
            } else
            /* istanbul ignore next */
            {
              cov_xt36771t1().b[46][1]++;
            }
            cov_xt36771t1().s[113]++;
            this.cacheStats.misses++;
            /* istanbul ignore next */
            cov_xt36771t1().s[114]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_xt36771t1().b[43][1]++;
            cov_xt36771t1().s[115]++;
            _a.trys.push([1, 3,, 4]);
            /* istanbul ignore next */
            cov_xt36771t1().s[116]++;
            return [4 /*yield*/, this.fetchMarketDataFromAPI(normalizedSkill)];
          case 2:
            /* istanbul ignore next */
            cov_xt36771t1().b[43][2]++;
            cov_xt36771t1().s[117]++;
            marketData = _a.sent();
            // Cache the result
            /* istanbul ignore next */
            cov_xt36771t1().s[118]++;
            this.cache.set(cacheKey, {
              data: marketData,
              timestamp: Date.now()
            });
            /* istanbul ignore next */
            cov_xt36771t1().s[119]++;
            return [2 /*return*/, marketData];
          case 3:
            /* istanbul ignore next */
            cov_xt36771t1().b[43][3]++;
            cov_xt36771t1().s[120]++;
            error_1 = _a.sent();
            /* istanbul ignore next */
            cov_xt36771t1().s[121]++;
            cached = this.cache.get(cacheKey);
            /* istanbul ignore next */
            cov_xt36771t1().s[122]++;
            if (cached) {
              /* istanbul ignore next */
              cov_xt36771t1().b[50][0]++;
              cov_xt36771t1().s[123]++;
              return [2 /*return*/, __assign(__assign({}, cached.data), {
                isStale: true
              })];
            } else
            /* istanbul ignore next */
            {
              cov_xt36771t1().b[50][1]++;
            }
            cov_xt36771t1().s[124]++;
            return [2 /*return*/, this.getDefaultMarketData(normalizedSkill)];
          case 4:
            /* istanbul ignore next */
            cov_xt36771t1().b[43][4]++;
            cov_xt36771t1().s[125]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Get skill market data with comprehensive edge case handling
   */
  /* istanbul ignore next */
  cov_xt36771t1().s[126]++;
  SkillMarketDataService.prototype.getSkillMarketDataWithEdgeHandling = function (skill_1) {
    /* istanbul ignore next */
    cov_xt36771t1().f[22]++;
    cov_xt36771t1().s[127]++;
    return __awaiter(this, arguments, Promise, function (skill, options) {
      /* istanbul ignore next */
      cov_xt36771t1().f[23]++;
      var data, error_2;
      /* istanbul ignore next */
      cov_xt36771t1().s[128]++;
      if (options === void 0) {
        /* istanbul ignore next */
        cov_xt36771t1().b[51][0]++;
        cov_xt36771t1().s[129]++;
        options = {};
      } else
      /* istanbul ignore next */
      {
        cov_xt36771t1().b[51][1]++;
      }
      cov_xt36771t1().s[130]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_xt36771t1().f[24]++;
        cov_xt36771t1().s[131]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_xt36771t1().b[52][0]++;
            cov_xt36771t1().s[132]++;
            if (this.edgeCaseHandler) {
              /* istanbul ignore next */
              cov_xt36771t1().b[53][0]++;
              cov_xt36771t1().s[133]++;
              return [2 /*return*/, this.edgeCaseHandler.handleMarketDataRequest(__assign({
                skill: skill
              }, options))];
            } else
            /* istanbul ignore next */
            {
              cov_xt36771t1().b[53][1]++;
            }
            cov_xt36771t1().s[134]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_xt36771t1().b[52][1]++;
            cov_xt36771t1().s[135]++;
            _a.trys.push([1, 3,, 4]);
            /* istanbul ignore next */
            cov_xt36771t1().s[136]++;
            return [4 /*yield*/, this.getSkillMarketData(skill, options)];
          case 2:
            /* istanbul ignore next */
            cov_xt36771t1().b[52][2]++;
            cov_xt36771t1().s[137]++;
            data = _a.sent();
            /* istanbul ignore next */
            cov_xt36771t1().s[138]++;
            return [2 /*return*/, {
              success: true,
              data: data,
              sanitizedInput: __assign({
                skill: skill
              }, options)
            }];
          case 3:
            /* istanbul ignore next */
            cov_xt36771t1().b[52][3]++;
            cov_xt36771t1().s[139]++;
            error_2 = _a.sent();
            /* istanbul ignore next */
            cov_xt36771t1().s[140]++;
            return [2 /*return*/, {
              success: false,
              error: error_2 instanceof Error ?
              /* istanbul ignore next */
              (cov_xt36771t1().b[54][0]++, error_2.message) :
              /* istanbul ignore next */
              (cov_xt36771t1().b[54][1]++, 'Unknown error'),
              errorType: 'SYSTEM_ERROR',
              fallbackData: this.getDefaultMarketData(skill)
            }];
          case 4:
            /* istanbul ignore next */
            cov_xt36771t1().b[52][4]++;
            cov_xt36771t1().s[141]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[142]++;
  SkillMarketDataService.prototype.getMultipleSkillsMarketData = function (skills_1) {
    /* istanbul ignore next */
    cov_xt36771t1().f[25]++;
    cov_xt36771t1().s[143]++;
    return __awaiter(this, arguments, Promise, function (skills, options) {
      /* istanbul ignore next */
      cov_xt36771t1().f[26]++;
      var promises;
      var _this =
      /* istanbul ignore next */
      (cov_xt36771t1().s[144]++, this);
      /* istanbul ignore next */
      cov_xt36771t1().s[145]++;
      if (options === void 0) {
        /* istanbul ignore next */
        cov_xt36771t1().b[55][0]++;
        cov_xt36771t1().s[146]++;
        options = {};
      } else
      /* istanbul ignore next */
      {
        cov_xt36771t1().b[55][1]++;
      }
      cov_xt36771t1().s[147]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_xt36771t1().f[27]++;
        cov_xt36771t1().s[148]++;
        if (
        /* istanbul ignore next */
        (cov_xt36771t1().b[57][0]++, !skills) ||
        /* istanbul ignore next */
        (cov_xt36771t1().b[57][1]++, skills.length === 0)) {
          /* istanbul ignore next */
          cov_xt36771t1().b[56][0]++;
          cov_xt36771t1().s[149]++;
          throw new Error('No skills provided');
        } else
        /* istanbul ignore next */
        {
          cov_xt36771t1().b[56][1]++;
        }
        cov_xt36771t1().s[150]++;
        promises = skills.map(function (skill) {
          /* istanbul ignore next */
          cov_xt36771t1().f[28]++;
          cov_xt36771t1().s[151]++;
          return _this.getSkillMarketData(skill, options);
        });
        /* istanbul ignore next */
        cov_xt36771t1().s[152]++;
        return [2 /*return*/, Promise.all(promises)];
      });
    });
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[153]++;
  SkillMarketDataService.prototype.analyzeMarketTrends = function (skills) {
    /* istanbul ignore next */
    cov_xt36771t1().f[29]++;
    cov_xt36771t1().s[154]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_xt36771t1().f[30]++;
      var marketDataList, topDemandSkills, fastestGrowingSkills, highestPayingSkills, marketGaps, emergingSkills, decliningSkills;
      /* istanbul ignore next */
      cov_xt36771t1().s[155]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_xt36771t1().f[31]++;
        cov_xt36771t1().s[156]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_xt36771t1().b[58][0]++;
            cov_xt36771t1().s[157]++;
            return [4 /*yield*/, this.getMultipleSkillsMarketData(skills)];
          case 1:
            /* istanbul ignore next */
            cov_xt36771t1().b[58][1]++;
            cov_xt36771t1().s[158]++;
            marketDataList = _a.sent();
            /* istanbul ignore next */
            cov_xt36771t1().s[159]++;
            topDemandSkills = __spreadArray([], marketDataList, true).sort(function (a, b) {
              /* istanbul ignore next */
              cov_xt36771t1().f[32]++;
              cov_xt36771t1().s[160]++;
              return b.demand - a.demand;
            }).slice(0, 5);
            /* istanbul ignore next */
            cov_xt36771t1().s[161]++;
            fastestGrowingSkills = __spreadArray([], marketDataList, true).sort(function (a, b) {
              /* istanbul ignore next */
              cov_xt36771t1().f[33]++;
              cov_xt36771t1().s[162]++;
              return b.growth - a.growth;
            }).slice(0, 5);
            /* istanbul ignore next */
            cov_xt36771t1().s[163]++;
            highestPayingSkills = __spreadArray([], marketDataList, true).sort(function (a, b) {
              /* istanbul ignore next */
              cov_xt36771t1().f[34]++;
              cov_xt36771t1().s[164]++;
              return b.averageSalary - a.averageSalary;
            }).slice(0, 5);
            /* istanbul ignore next */
            cov_xt36771t1().s[165]++;
            marketGaps = marketDataList.filter(function (data) {
              /* istanbul ignore next */
              cov_xt36771t1().f[35]++;
              cov_xt36771t1().s[166]++;
              return data.demand > data.supply;
            }).map(function (data) {
              /* istanbul ignore next */
              cov_xt36771t1().f[36]++;
              cov_xt36771t1().s[167]++;
              return {
                skill: data.skill,
                demandSupplyRatio: data.demand / data.supply,
                opportunityScore: (data.demand - data.supply) * (data.growth / 100),
                marketData: data
              };
            }).sort(function (a, b) {
              /* istanbul ignore next */
              cov_xt36771t1().f[37]++;
              cov_xt36771t1().s[168]++;
              return b.opportunityScore - a.opportunityScore;
            });
            /* istanbul ignore next */
            cov_xt36771t1().s[169]++;
            emergingSkills = marketDataList.filter(function (data) {
              /* istanbul ignore next */
              cov_xt36771t1().f[38]++;
              cov_xt36771t1().s[170]++;
              return data.growth > 15;
            }).sort(function (a, b) {
              /* istanbul ignore next */
              cov_xt36771t1().f[39]++;
              cov_xt36771t1().s[171]++;
              return b.growth - a.growth;
            });
            /* istanbul ignore next */
            cov_xt36771t1().s[172]++;
            decliningSkills = marketDataList.filter(function (data) {
              /* istanbul ignore next */
              cov_xt36771t1().f[40]++;
              cov_xt36771t1().s[173]++;
              return data.growth < 5;
            }).sort(function (a, b) {
              /* istanbul ignore next */
              cov_xt36771t1().f[41]++;
              cov_xt36771t1().s[174]++;
              return a.growth - b.growth;
            });
            /* istanbul ignore next */
            cov_xt36771t1().s[175]++;
            return [2 /*return*/, {
              topDemandSkills: topDemandSkills,
              fastestGrowingSkills: fastestGrowingSkills,
              highestPayingSkills: highestPayingSkills,
              marketGaps: marketGaps,
              emergingSkills: emergingSkills,
              decliningSkills: decliningSkills
            }];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[176]++;
  SkillMarketDataService.prototype.getSalaryInsights = function (skills) {
    /* istanbul ignore next */
    cov_xt36771t1().f[42]++;
    cov_xt36771t1().s[177]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_xt36771t1().f[43]++;
      var marketDataList, salaries, averageSalary, sortedSalaries, percentiles, topPayingSkills, salaryGrowthPotential;
      /* istanbul ignore next */
      cov_xt36771t1().s[178]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_xt36771t1().f[44]++;
        cov_xt36771t1().s[179]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_xt36771t1().b[59][0]++;
            cov_xt36771t1().s[180]++;
            return [4 /*yield*/, this.getMultipleSkillsMarketData(skills)];
          case 1:
            /* istanbul ignore next */
            cov_xt36771t1().b[59][1]++;
            cov_xt36771t1().s[181]++;
            marketDataList = _a.sent();
            /* istanbul ignore next */
            cov_xt36771t1().s[182]++;
            salaries = marketDataList.map(function (data) {
              /* istanbul ignore next */
              cov_xt36771t1().f[45]++;
              cov_xt36771t1().s[183]++;
              return data.averageSalary;
            });
            /* istanbul ignore next */
            cov_xt36771t1().s[184]++;
            averageSalary = salaries.reduce(function (sum, salary) {
              /* istanbul ignore next */
              cov_xt36771t1().f[46]++;
              cov_xt36771t1().s[185]++;
              return sum + salary;
            }, 0) / salaries.length;
            /* istanbul ignore next */
            cov_xt36771t1().s[186]++;
            sortedSalaries = __spreadArray([], salaries, true).sort(function (a, b) {
              /* istanbul ignore next */
              cov_xt36771t1().f[47]++;
              cov_xt36771t1().s[187]++;
              return a - b;
            });
            /* istanbul ignore next */
            cov_xt36771t1().s[188]++;
            percentiles = {
              p25: this.calculatePercentile(sortedSalaries, 25),
              p50: this.calculatePercentile(sortedSalaries, 50),
              p75: this.calculatePercentile(sortedSalaries, 75),
              p90: this.calculatePercentile(sortedSalaries, 90)
            };
            /* istanbul ignore next */
            cov_xt36771t1().s[189]++;
            topPayingSkills = __spreadArray([], marketDataList, true).sort(function (a, b) {
              /* istanbul ignore next */
              cov_xt36771t1().f[48]++;
              cov_xt36771t1().s[190]++;
              return b.averageSalary - a.averageSalary;
            }).slice(0, 5);
            /* istanbul ignore next */
            cov_xt36771t1().s[191]++;
            salaryGrowthPotential = marketDataList.reduce(function (sum, data) {
              /* istanbul ignore next */
              cov_xt36771t1().f[49]++;
              cov_xt36771t1().s[192]++;
              return sum + data.growth;
            }, 0) / marketDataList.length;
            /* istanbul ignore next */
            cov_xt36771t1().s[193]++;
            return [2 /*return*/, {
              averageSalary: averageSalary,
              salaryRange: {
                min: Math.min.apply(Math, salaries),
                max: Math.max.apply(Math, salaries)
              },
              percentiles: percentiles,
              topPayingSkills: topPayingSkills,
              salaryGrowthPotential: salaryGrowthPotential
            }];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[194]++;
  SkillMarketDataService.prototype.getLocationBasedMarketData = function (skill, location) {
    /* istanbul ignore next */
    cov_xt36771t1().f[50]++;
    cov_xt36771t1().s[195]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_xt36771t1().f[51]++;
      var baseMarketData, locationMultipliers;
      /* istanbul ignore next */
      cov_xt36771t1().s[196]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_xt36771t1().f[52]++;
        cov_xt36771t1().s[197]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_xt36771t1().b[60][0]++;
            cov_xt36771t1().s[198]++;
            return [4 /*yield*/, this.getSkillMarketData(skill)];
          case 1:
            /* istanbul ignore next */
            cov_xt36771t1().b[60][1]++;
            cov_xt36771t1().s[199]++;
            baseMarketData = _a.sent();
            /* istanbul ignore next */
            cov_xt36771t1().s[200]++;
            locationMultipliers = this.getLocationMultipliers(location);
            /* istanbul ignore next */
            cov_xt36771t1().s[201]++;
            return [2 /*return*/, {
              skill: skill,
              location: location,
              localDemand: baseMarketData.demand * locationMultipliers.demand,
              localSalary: baseMarketData.averageSalary * locationMultipliers.salary,
              costOfLivingAdjustment: locationMultipliers.costOfLiving,
              remoteOpportunities: this.calculateRemoteOpportunities(skill)
            }];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[202]++;
  SkillMarketDataService.prototype.compareLocationMarkets = function (skill, locations) {
    /* istanbul ignore next */
    cov_xt36771t1().f[53]++;
    cov_xt36771t1().s[203]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_xt36771t1().f[54]++;
      var baseMarketData, locationComparison;
      var _this =
      /* istanbul ignore next */
      (cov_xt36771t1().s[204]++, this);
      /* istanbul ignore next */
      cov_xt36771t1().s[205]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_xt36771t1().f[55]++;
        cov_xt36771t1().s[206]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_xt36771t1().b[61][0]++;
            cov_xt36771t1().s[207]++;
            return [4 /*yield*/, this.getSkillMarketData(skill)];
          case 1:
            /* istanbul ignore next */
            cov_xt36771t1().b[61][1]++;
            cov_xt36771t1().s[208]++;
            baseMarketData = _a.sent();
            /* istanbul ignore next */
            cov_xt36771t1().s[209]++;
            locationComparison = locations.map(function (location) {
              /* istanbul ignore next */
              cov_xt36771t1().f[56]++;
              var multipliers =
              /* istanbul ignore next */
              (cov_xt36771t1().s[210]++, _this.getLocationMultipliers(location));
              var salary =
              /* istanbul ignore next */
              (cov_xt36771t1().s[211]++, baseMarketData.averageSalary * multipliers.salary);
              /* istanbul ignore next */
              cov_xt36771t1().s[212]++;
              return {
                location: location,
                demand: baseMarketData.demand * multipliers.demand,
                salary: salary,
                costOfLivingIndex: multipliers.costOfLiving,
                adjustedSalary: salary / multipliers.costOfLiving
              };
            });
            /* istanbul ignore next */
            cov_xt36771t1().s[213]++;
            return [2 /*return*/, {
              skill: skill,
              locationComparison: locationComparison
            }];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[214]++;
  SkillMarketDataService.prototype.getIndustryMarketData = function (skill, industry) {
    /* istanbul ignore next */
    cov_xt36771t1().f[57]++;
    cov_xt36771t1().s[215]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_xt36771t1().f[58]++;
      var baseMarketData, industryMultipliers;
      /* istanbul ignore next */
      cov_xt36771t1().s[216]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_xt36771t1().f[59]++;
        cov_xt36771t1().s[217]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_xt36771t1().b[62][0]++;
            cov_xt36771t1().s[218]++;
            return [4 /*yield*/, this.getSkillMarketData(skill)];
          case 1:
            /* istanbul ignore next */
            cov_xt36771t1().b[62][1]++;
            cov_xt36771t1().s[219]++;
            baseMarketData = _a.sent();
            /* istanbul ignore next */
            cov_xt36771t1().s[220]++;
            industryMultipliers = this.getIndustryMultipliers(industry);
            /* istanbul ignore next */
            cov_xt36771t1().s[221]++;
            return [2 /*return*/, {
              skill: skill,
              industry: industry,
              industryDemand: baseMarketData.demand * industryMultipliers.demand,
              averageIndustrySalary: baseMarketData.averageSalary * industryMultipliers.salary,
              growthProjection: baseMarketData.growth * industryMultipliers.growth,
              keyCompanies: this.getKeyCompaniesForIndustry(industry)
            }];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[222]++;
  SkillMarketDataService.prototype.rankIndustriesBySkillDemand = function (skill) {
    /* istanbul ignore next */
    cov_xt36771t1().f[60]++;
    cov_xt36771t1().s[223]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_xt36771t1().f[61]++;
      var industries, baseMarketData, industryRankings;
      var _this =
      /* istanbul ignore next */
      (cov_xt36771t1().s[224]++, this);
      /* istanbul ignore next */
      cov_xt36771t1().s[225]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_xt36771t1().f[62]++;
        cov_xt36771t1().s[226]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_xt36771t1().b[63][0]++;
            cov_xt36771t1().s[227]++;
            industries = ['technology', 'finance', 'healthcare', 'education', 'retail'];
            /* istanbul ignore next */
            cov_xt36771t1().s[228]++;
            return [4 /*yield*/, this.getSkillMarketData(skill)];
          case 1:
            /* istanbul ignore next */
            cov_xt36771t1().b[63][1]++;
            cov_xt36771t1().s[229]++;
            baseMarketData = _a.sent();
            /* istanbul ignore next */
            cov_xt36771t1().s[230]++;
            industryRankings = industries.map(function (industry) {
              /* istanbul ignore next */
              cov_xt36771t1().f[63]++;
              var multipliers =
              /* istanbul ignore next */
              (cov_xt36771t1().s[231]++, _this.getIndustryMultipliers(industry));
              var demandScore =
              /* istanbul ignore next */
              (cov_xt36771t1().s[232]++, baseMarketData.demand * multipliers.demand);
              var salaryScore =
              /* istanbul ignore next */
              (cov_xt36771t1().s[233]++, baseMarketData.averageSalary * multipliers.salary / 1000); // Normalize
              var growthScore =
              /* istanbul ignore next */
              (cov_xt36771t1().s[234]++, baseMarketData.growth * multipliers.growth);
              /* istanbul ignore next */
              cov_xt36771t1().s[235]++;
              return {
                industry: industry,
                demandScore: demandScore,
                salaryScore: salaryScore,
                growthScore: growthScore,
                overallScore: (demandScore + salaryScore + growthScore) / 3
              };
            }).sort(function (a, b) {
              /* istanbul ignore next */
              cov_xt36771t1().f[64]++;
              cov_xt36771t1().s[236]++;
              return b.overallScore - a.overallScore;
            });
            /* istanbul ignore next */
            cov_xt36771t1().s[237]++;
            return [2 /*return*/, {
              skill: skill,
              industries: industryRankings
            }];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[238]++;
  SkillMarketDataService.prototype.getMarketBasedRecommendations = function (request) {
    /* istanbul ignore next */
    cov_xt36771t1().f[65]++;
    cov_xt36771t1().s[239]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_xt36771t1().f[66]++;
      var currentSkillsData, recommendedSkillNames, recommendedSkillsData, learningPriority, allSkillsData, trends, currentPotential, projectedPotential;
      var _this =
      /* istanbul ignore next */
      (cov_xt36771t1().s[240]++, this);
      /* istanbul ignore next */
      cov_xt36771t1().s[241]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_xt36771t1().f[67]++;
        cov_xt36771t1().s[242]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_xt36771t1().b[64][0]++;
            cov_xt36771t1().s[243]++;
            return [4 /*yield*/, this.getMultipleSkillsMarketData(request.currentSkills)];
          case 1:
            /* istanbul ignore next */
            cov_xt36771t1().b[64][1]++;
            cov_xt36771t1().s[244]++;
            currentSkillsData = _a.sent();
            /* istanbul ignore next */
            cov_xt36771t1().s[245]++;
            recommendedSkillNames = this.getRecommendedSkillsForCareer(request.careerGoal, request.experienceLevel);
            /* istanbul ignore next */
            cov_xt36771t1().s[246]++;
            return [4 /*yield*/, this.getMultipleSkillsMarketData(recommendedSkillNames)];
          case 2:
            /* istanbul ignore next */
            cov_xt36771t1().b[64][2]++;
            cov_xt36771t1().s[247]++;
            recommendedSkillsData = _a.sent();
            /* istanbul ignore next */
            cov_xt36771t1().s[248]++;
            learningPriority = recommendedSkillsData.map(function (skillData) {
              /* istanbul ignore next */
              cov_xt36771t1().f[68]++;
              cov_xt36771t1().s[249]++;
              return {
                skill: skillData.skill,
                priorityScore: _this.calculatePriorityScore(skillData, request),
                reasoning: _this.generateRecommendationReasoning(skillData, request),
                marketData: skillData
              };
            }).sort(function (a, b) {
              /* istanbul ignore next */
              cov_xt36771t1().f[69]++;
              cov_xt36771t1().s[250]++;
              return b.priorityScore - a.priorityScore;
            });
            /* istanbul ignore next */
            cov_xt36771t1().s[251]++;
            allSkillsData = __spreadArray(__spreadArray([], currentSkillsData, true), recommendedSkillsData, true);
            /* istanbul ignore next */
            cov_xt36771t1().s[252]++;
            return [4 /*yield*/, this.analyzeMarketTrends(allSkillsData.map(function (s) {
              /* istanbul ignore next */
              cov_xt36771t1().f[70]++;
              cov_xt36771t1().s[253]++;
              return s.skill;
            }))];
          case 3:
            /* istanbul ignore next */
            cov_xt36771t1().b[64][3]++;
            cov_xt36771t1().s[254]++;
            trends = _a.sent();
            /* istanbul ignore next */
            cov_xt36771t1().s[255]++;
            currentPotential = this.calculateSalaryPotential(currentSkillsData, request.location);
            /* istanbul ignore next */
            cov_xt36771t1().s[256]++;
            projectedPotential = this.calculateSalaryPotential(__spreadArray(__spreadArray([], currentSkillsData, true), recommendedSkillsData, true), request.location);
            /* istanbul ignore next */
            cov_xt36771t1().s[257]++;
            return [2 /*return*/, {
              recommendedSkills: recommendedSkillsData,
              learningPriority: learningPriority,
              marketOpportunities: trends.marketGaps,
              salaryImpact: {
                currentPotential: currentPotential,
                projectedPotential: projectedPotential,
                increase: projectedPotential - currentPotential
              }
            }];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[258]++;
  SkillMarketDataService.prototype.batchUpdateMarketData = function (skills) {
    /* istanbul ignore next */
    cov_xt36771t1().f[71]++;
    cov_xt36771t1().s[259]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_xt36771t1().f[72]++;
      var batchSize, batches, i, _i, batches_1, batch;
      var _this =
      /* istanbul ignore next */
      (cov_xt36771t1().s[260]++, this);
      /* istanbul ignore next */
      cov_xt36771t1().s[261]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_xt36771t1().f[73]++;
        cov_xt36771t1().s[262]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_xt36771t1().b[65][0]++;
            cov_xt36771t1().s[263]++;
            batchSize = 10;
            /* istanbul ignore next */
            cov_xt36771t1().s[264]++;
            batches = [];
            /* istanbul ignore next */
            cov_xt36771t1().s[265]++;
            for (i = 0; i < skills.length; i += batchSize) {
              /* istanbul ignore next */
              cov_xt36771t1().s[266]++;
              batches.push(skills.slice(i, i + batchSize));
            }
            /* istanbul ignore next */
            cov_xt36771t1().s[267]++;
            _i = 0, batches_1 = batches;
            /* istanbul ignore next */
            cov_xt36771t1().s[268]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_xt36771t1().b[65][1]++;
            cov_xt36771t1().s[269]++;
            if (!(_i < batches_1.length)) {
              /* istanbul ignore next */
              cov_xt36771t1().b[66][0]++;
              cov_xt36771t1().s[270]++;
              return [3 /*break*/, 4];
            } else
            /* istanbul ignore next */
            {
              cov_xt36771t1().b[66][1]++;
            }
            cov_xt36771t1().s[271]++;
            batch = batches_1[_i];
            /* istanbul ignore next */
            cov_xt36771t1().s[272]++;
            return [4 /*yield*/, Promise.all(batch.map(function (skill) {
              /* istanbul ignore next */
              cov_xt36771t1().f[74]++;
              cov_xt36771t1().s[273]++;
              return _this.getSkillMarketData(skill, {
                forceRefresh: true
              });
            }))];
          case 2:
            /* istanbul ignore next */
            cov_xt36771t1().b[65][2]++;
            cov_xt36771t1().s[274]++;
            _a.sent();
            /* istanbul ignore next */
            cov_xt36771t1().s[275]++;
            _a.label = 3;
          case 3:
            /* istanbul ignore next */
            cov_xt36771t1().b[65][3]++;
            cov_xt36771t1().s[276]++;
            _i++;
            /* istanbul ignore next */
            cov_xt36771t1().s[277]++;
            return [3 /*break*/, 1];
          case 4:
            /* istanbul ignore next */
            cov_xt36771t1().b[65][4]++;
            cov_xt36771t1().s[278]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[279]++;
  SkillMarketDataService.prototype.getCacheStatistics = function () {
    /* istanbul ignore next */
    cov_xt36771t1().f[75]++;
    cov_xt36771t1().s[280]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_xt36771t1().f[76]++;
      var hitRate, missRate;
      /* istanbul ignore next */
      cov_xt36771t1().s[281]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_xt36771t1().f[77]++;
        cov_xt36771t1().s[282]++;
        hitRate = this.cacheStats.totalRequests > 0 ?
        /* istanbul ignore next */
        (cov_xt36771t1().b[67][0]++, this.cacheStats.hits / this.cacheStats.totalRequests) :
        /* istanbul ignore next */
        (cov_xt36771t1().b[67][1]++, 0);
        /* istanbul ignore next */
        cov_xt36771t1().s[283]++;
        missRate = this.cacheStats.totalRequests > 0 ?
        /* istanbul ignore next */
        (cov_xt36771t1().b[68][0]++, this.cacheStats.misses / this.cacheStats.totalRequests) :
        /* istanbul ignore next */
        (cov_xt36771t1().b[68][1]++, 0);
        /* istanbul ignore next */
        cov_xt36771t1().s[284]++;
        return [2 /*return*/, {
          hitRate: hitRate,
          missRate: missRate,
          totalRequests: this.cacheStats.totalRequests,
          cacheSize: this.cache.size
        }];
      });
    });
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[285]++;
  SkillMarketDataService.prototype.clearCache = function () {
    /* istanbul ignore next */
    cov_xt36771t1().f[78]++;
    cov_xt36771t1().s[286]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_xt36771t1().f[79]++;
      cov_xt36771t1().s[287]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_xt36771t1().f[80]++;
        cov_xt36771t1().s[288]++;
        this.cache.clear();
        /* istanbul ignore next */
        cov_xt36771t1().s[289]++;
        this.cacheStats = {
          hits: 0,
          misses: 0,
          totalRequests: 0
        };
        /* istanbul ignore next */
        cov_xt36771t1().s[290]++;
        return [2 /*return*/];
      });
    });
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[291]++;
  SkillMarketDataService.prototype.fetchMarketDataFromAPI = function (skill) {
    /* istanbul ignore next */
    cov_xt36771t1().f[81]++;
    cov_xt36771t1().s[292]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_xt36771t1().f[82]++;
      var response, error_3, mockData;
      /* istanbul ignore next */
      cov_xt36771t1().s[293]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_xt36771t1().f[83]++;
        cov_xt36771t1().s[294]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_xt36771t1().b[69][0]++;
            cov_xt36771t1().s[295]++;
            if (!(
            /* istanbul ignore next */
            (cov_xt36771t1().b[71][0]++, global.fetch) &&
            /* istanbul ignore next */
            (cov_xt36771t1().b[71][1]++, typeof global.fetch === 'function'))) {
              /* istanbul ignore next */
              cov_xt36771t1().b[70][0]++;
              cov_xt36771t1().s[296]++;
              return [3 /*break*/, 4];
            } else
            /* istanbul ignore next */
            {
              cov_xt36771t1().b[70][1]++;
            }
            cov_xt36771t1().s[297]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_xt36771t1().b[69][1]++;
            cov_xt36771t1().s[298]++;
            _a.trys.push([1, 3,, 4]);
            /* istanbul ignore next */
            cov_xt36771t1().s[299]++;
            return [4 /*yield*/, global.fetch("/api/skills/".concat(skill))];
          case 2:
            /* istanbul ignore next */
            cov_xt36771t1().b[69][2]++;
            cov_xt36771t1().s[300]++;
            response = _a.sent();
            /* istanbul ignore next */
            cov_xt36771t1().s[301]++;
            if (!response.ok) {
              /* istanbul ignore next */
              cov_xt36771t1().b[72][0]++;
              cov_xt36771t1().s[302]++;
              throw new Error("API Error: ".concat(response.status));
            } else
            /* istanbul ignore next */
            {
              cov_xt36771t1().b[72][1]++;
            }
            cov_xt36771t1().s[303]++;
            return [3 /*break*/, 4];
          case 3:
            /* istanbul ignore next */
            cov_xt36771t1().b[69][3]++;
            cov_xt36771t1().s[304]++;
            error_3 = _a.sent();
            // API failed, throw error to trigger fallback logic
            /* istanbul ignore next */
            cov_xt36771t1().s[305]++;
            throw error_3;
          case 4:
            /* istanbul ignore next */
            cov_xt36771t1().b[69][4]++;
            cov_xt36771t1().s[306]++;
            // Simulate API call delay
            return [4 /*yield*/, new Promise(function (resolve) {
              /* istanbul ignore next */
              cov_xt36771t1().f[84]++;
              cov_xt36771t1().s[307]++;
              return setTimeout(resolve, 100);
            })];
          case 5:
            /* istanbul ignore next */
            cov_xt36771t1().b[69][5]++;
            cov_xt36771t1().s[308]++;
            // Simulate API call delay
            _a.sent();
            /* istanbul ignore next */
            cov_xt36771t1().s[309]++;
            mockData = this.mockMarketData[skill];
            /* istanbul ignore next */
            cov_xt36771t1().s[310]++;
            if (!mockData) {
              /* istanbul ignore next */
              cov_xt36771t1().b[73][0]++;
              cov_xt36771t1().s[311]++;
              return [2 /*return*/, this.getDefaultMarketData(skill)];
            } else
            /* istanbul ignore next */
            {
              cov_xt36771t1().b[73][1]++;
            }
            cov_xt36771t1().s[312]++;
            return [2 /*return*/, {
              skill: skill,
              demand:
              /* istanbul ignore next */
              (cov_xt36771t1().b[74][0]++, mockData.demand) ||
              /* istanbul ignore next */
              (cov_xt36771t1().b[74][1]++, 0),
              supply:
              /* istanbul ignore next */
              (cov_xt36771t1().b[75][0]++, mockData.supply) ||
              /* istanbul ignore next */
              (cov_xt36771t1().b[75][1]++, 0),
              averageSalary:
              /* istanbul ignore next */
              (cov_xt36771t1().b[76][0]++, mockData.averageSalary) ||
              /* istanbul ignore next */
              (cov_xt36771t1().b[76][1]++, 0),
              growth:
              /* istanbul ignore next */
              (cov_xt36771t1().b[77][0]++, mockData.growth) ||
              /* istanbul ignore next */
              (cov_xt36771t1().b[77][1]++, 0),
              difficulty:
              /* istanbul ignore next */
              (cov_xt36771t1().b[78][0]++, mockData.difficulty) ||
              /* istanbul ignore next */
              (cov_xt36771t1().b[78][1]++, 5),
              timeToLearn:
              /* istanbul ignore next */
              (cov_xt36771t1().b[79][0]++, mockData.timeToLearn) ||
              /* istanbul ignore next */
              (cov_xt36771t1().b[79][1]++, 12),
              category:
              /* istanbul ignore next */
              (cov_xt36771t1().b[80][0]++, mockData.category) ||
              /* istanbul ignore next */
              (cov_xt36771t1().b[80][1]++, 'General'),
              lastUpdated: new Date()
            }];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[313]++;
  SkillMarketDataService.prototype.getDefaultMarketData = function (skill) {
    /* istanbul ignore next */
    cov_xt36771t1().f[85]++;
    cov_xt36771t1().s[314]++;
    return {
      skill: skill,
      demand: 0,
      supply: 0,
      averageSalary: 0,
      growth: 0,
      difficulty: 5,
      timeToLearn: 12,
      category: 'Unknown',
      lastUpdated: new Date(),
      isStale: true
    };
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[315]++;
  SkillMarketDataService.prototype.calculatePercentile = function (sortedArray, percentile) {
    /* istanbul ignore next */
    cov_xt36771t1().f[86]++;
    var index =
    /* istanbul ignore next */
    (cov_xt36771t1().s[316]++, percentile / 100 * (sortedArray.length - 1));
    var lower =
    /* istanbul ignore next */
    (cov_xt36771t1().s[317]++, Math.floor(index));
    var upper =
    /* istanbul ignore next */
    (cov_xt36771t1().s[318]++, Math.ceil(index));
    /* istanbul ignore next */
    cov_xt36771t1().s[319]++;
    if (lower === upper) {
      /* istanbul ignore next */
      cov_xt36771t1().b[81][0]++;
      cov_xt36771t1().s[320]++;
      return sortedArray[lower];
    } else
    /* istanbul ignore next */
    {
      cov_xt36771t1().b[81][1]++;
    }
    var weight =
    /* istanbul ignore next */
    (cov_xt36771t1().s[321]++, index - lower);
    /* istanbul ignore next */
    cov_xt36771t1().s[322]++;
    return sortedArray[lower] * (1 - weight) + sortedArray[upper] * weight;
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[323]++;
  SkillMarketDataService.prototype.getLocationMultipliers = function (location) {
    /* istanbul ignore next */
    cov_xt36771t1().f[87]++;
    var multipliers =
    /* istanbul ignore next */
    (cov_xt36771t1().s[324]++, {
      'san francisco': {
        demand: 1.3,
        salary: 1.4,
        costOfLiving: 1.8
      },
      'new york': {
        demand: 1.2,
        salary: 1.3,
        costOfLiving: 1.6
      },
      'austin': {
        demand: 1.1,
        salary: 1.1,
        costOfLiving: 1.2
      },
      'remote': {
        demand: 1.0,
        salary: 1.0,
        costOfLiving: 1.0
      }
    });
    /* istanbul ignore next */
    cov_xt36771t1().s[325]++;
    return /* istanbul ignore next */(cov_xt36771t1().b[82][0]++, multipliers[location.toLowerCase()]) ||
    /* istanbul ignore next */
    (cov_xt36771t1().b[82][1]++, {
      demand: 1.0,
      salary: 1.0,
      costOfLiving: 1.0
    });
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[326]++;
  SkillMarketDataService.prototype.calculateRemoteOpportunities = function (skill) {
    /* istanbul ignore next */
    cov_xt36771t1().f[88]++;
    // Mock calculation based on skill type
    var remoteSkills =
    /* istanbul ignore next */
    (cov_xt36771t1().s[327]++, ['javascript', 'react', 'nodejs', 'python', 'typescript']);
    /* istanbul ignore next */
    cov_xt36771t1().s[328]++;
    return remoteSkills.includes(skill.toLowerCase()) ?
    /* istanbul ignore next */
    (cov_xt36771t1().b[83][0]++, 85) :
    /* istanbul ignore next */
    (cov_xt36771t1().b[83][1]++, 45);
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[329]++;
  SkillMarketDataService.prototype.getIndustryMultipliers = function (industry) {
    /* istanbul ignore next */
    cov_xt36771t1().f[89]++;
    var multipliers =
    /* istanbul ignore next */
    (cov_xt36771t1().s[330]++, {
      technology: {
        demand: 1.3,
        salary: 1.2,
        growth: 1.4
      },
      finance: {
        demand: 1.1,
        salary: 1.3,
        growth: 1.1
      },
      healthcare: {
        demand: 0.8,
        salary: 0.9,
        growth: 1.2
      },
      education: {
        demand: 0.6,
        salary: 0.7,
        growth: 0.8
      },
      retail: {
        demand: 0.7,
        salary: 0.8,
        growth: 0.9
      }
    });
    /* istanbul ignore next */
    cov_xt36771t1().s[331]++;
    return /* istanbul ignore next */(cov_xt36771t1().b[84][0]++, multipliers[industry.toLowerCase()]) ||
    /* istanbul ignore next */
    (cov_xt36771t1().b[84][1]++, {
      demand: 1.0,
      salary: 1.0,
      growth: 1.0
    });
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[332]++;
  SkillMarketDataService.prototype.getKeyCompaniesForIndustry = function (industry) {
    /* istanbul ignore next */
    cov_xt36771t1().f[90]++;
    var companies =
    /* istanbul ignore next */
    (cov_xt36771t1().s[333]++, {
      technology: ['Google', 'Microsoft', 'Apple', 'Amazon', 'Meta'],
      finance: ['JPMorgan', 'Goldman Sachs', 'Morgan Stanley', 'Bank of America', 'Wells Fargo'],
      healthcare: ['Johnson & Johnson', 'Pfizer', 'UnitedHealth', 'Merck', 'AbbVie'],
      education: ['Pearson', 'McGraw-Hill', 'Coursera', 'Khan Academy', 'Udemy'],
      retail: ['Amazon', 'Walmart', 'Target', 'Home Depot', 'Costco']
    });
    /* istanbul ignore next */
    cov_xt36771t1().s[334]++;
    return /* istanbul ignore next */(cov_xt36771t1().b[85][0]++, companies[industry.toLowerCase()]) ||
    /* istanbul ignore next */
    (cov_xt36771t1().b[85][1]++, []);
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[335]++;
  SkillMarketDataService.prototype.getRecommendedSkillsForCareer = function (careerGoal, experienceLevel) {
    /* istanbul ignore next */
    cov_xt36771t1().f[91]++;
    var _a;
    var careerSkills =
    /* istanbul ignore next */
    (cov_xt36771t1().s[336]++, {
      'full stack developer': {
        beginner: ['javascript', 'html', 'css', 'react'],
        intermediate: ['nodejs', 'typescript', 'mongodb', 'express'],
        advanced: ['docker', 'kubernetes', 'aws', 'microservices']
      },
      'frontend developer': {
        beginner: ['html', 'css', 'javascript', 'react'],
        intermediate: ['typescript', 'redux', 'webpack', 'sass'],
        advanced: ['nextjs', 'graphql', 'testing', 'performance']
      }
    });
    var normalizedCareer =
    /* istanbul ignore next */
    (cov_xt36771t1().s[337]++, careerGoal.toLowerCase());
    var skills =
    /* istanbul ignore next */
    (cov_xt36771t1().s[338]++,
    /* istanbul ignore next */
    (cov_xt36771t1().b[86][0]++,
    /* istanbul ignore next */
    (cov_xt36771t1().b[88][0]++, (_a = careerSkills[normalizedCareer]) === null) ||
    /* istanbul ignore next */
    (cov_xt36771t1().b[88][1]++, _a === void 0) ?
    /* istanbul ignore next */
    (cov_xt36771t1().b[87][0]++, void 0) :
    /* istanbul ignore next */
    (cov_xt36771t1().b[87][1]++, _a[experienceLevel])) ||
    /* istanbul ignore next */
    (cov_xt36771t1().b[86][1]++, ['javascript', 'react', 'nodejs']));
    /* istanbul ignore next */
    cov_xt36771t1().s[339]++;
    return skills;
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[340]++;
  SkillMarketDataService.prototype.calculatePriorityScore = function (skillData, request) {
    /* istanbul ignore next */
    cov_xt36771t1().f[92]++;
    var demandWeight =
    /* istanbul ignore next */
    (cov_xt36771t1().s[341]++, 0.3);
    var growthWeight =
    /* istanbul ignore next */
    (cov_xt36771t1().s[342]++, 0.25);
    var salaryWeight =
    /* istanbul ignore next */
    (cov_xt36771t1().s[343]++, 0.2);
    var gapWeight =
    /* istanbul ignore next */
    (cov_xt36771t1().s[344]++, 0.15);
    var difficultyWeight =
    /* istanbul ignore next */
    (cov_xt36771t1().s[345]++, 0.1);
    var demandScore =
    /* istanbul ignore next */
    (cov_xt36771t1().s[346]++, skillData.demand);
    var growthScore =
    /* istanbul ignore next */
    (cov_xt36771t1().s[347]++, skillData.growth);
    var salaryScore =
    /* istanbul ignore next */
    (cov_xt36771t1().s[348]++, skillData.averageSalary / 1000); // Normalize
    var gapScore =
    /* istanbul ignore next */
    (cov_xt36771t1().s[349]++, Math.max(0, skillData.demand - skillData.supply));
    var difficultyScore =
    /* istanbul ignore next */
    (cov_xt36771t1().s[350]++, 11 - skillData.difficulty); // Invert difficulty (easier = higher score)
    /* istanbul ignore next */
    cov_xt36771t1().s[351]++;
    return demandScore * demandWeight + growthScore * growthWeight + salaryScore * salaryWeight + gapScore * gapWeight + difficultyScore * difficultyWeight;
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[352]++;
  SkillMarketDataService.prototype.generateRecommendationReasoning = function (skillData, request) {
    /* istanbul ignore next */
    cov_xt36771t1().f[93]++;
    var reasons =
    /* istanbul ignore next */
    (cov_xt36771t1().s[353]++, []);
    /* istanbul ignore next */
    cov_xt36771t1().s[354]++;
    if (skillData.demand > 80) {
      /* istanbul ignore next */
      cov_xt36771t1().b[89][0]++;
      cov_xt36771t1().s[355]++;
      reasons.push('high market demand');
    } else
    /* istanbul ignore next */
    {
      cov_xt36771t1().b[89][1]++;
    }
    cov_xt36771t1().s[356]++;
    if (skillData.growth > 15) {
      /* istanbul ignore next */
      cov_xt36771t1().b[90][0]++;
      cov_xt36771t1().s[357]++;
      reasons.push('rapid growth trajectory');
    } else
    /* istanbul ignore next */
    {
      cov_xt36771t1().b[90][1]++;
    }
    cov_xt36771t1().s[358]++;
    if (skillData.averageSalary > 90000) {
      /* istanbul ignore next */
      cov_xt36771t1().b[91][0]++;
      cov_xt36771t1().s[359]++;
      reasons.push('excellent salary potential');
    } else
    /* istanbul ignore next */
    {
      cov_xt36771t1().b[91][1]++;
    }
    cov_xt36771t1().s[360]++;
    if (skillData.demand > skillData.supply) {
      /* istanbul ignore next */
      cov_xt36771t1().b[92][0]++;
      cov_xt36771t1().s[361]++;
      reasons.push('market opportunity gap');
    } else
    /* istanbul ignore next */
    {
      cov_xt36771t1().b[92][1]++;
    }
    cov_xt36771t1().s[362]++;
    return reasons.length > 0 ?
    /* istanbul ignore next */
    (cov_xt36771t1().b[93][0]++, "Recommended due to ".concat(reasons.join(', '))) :
    /* istanbul ignore next */
    (cov_xt36771t1().b[93][1]++, 'Solid foundational skill for your career path');
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[363]++;
  SkillMarketDataService.prototype.calculateSalaryPotential = function (skillsData, location) {
    /* istanbul ignore next */
    cov_xt36771t1().f[94]++;
    var locationMultiplier =
    /* istanbul ignore next */
    (cov_xt36771t1().s[364]++, this.getLocationMultipliers(location).salary);
    var averageSalary =
    /* istanbul ignore next */
    (cov_xt36771t1().s[365]++, skillsData.reduce(function (sum, skill) {
      /* istanbul ignore next */
      cov_xt36771t1().f[95]++;
      cov_xt36771t1().s[366]++;
      return sum + skill.averageSalary;
    }, 0) / skillsData.length);
    /* istanbul ignore next */
    cov_xt36771t1().s[367]++;
    return averageSalary * locationMultiplier;
  };
  /* istanbul ignore next */
  cov_xt36771t1().s[368]++;
  return SkillMarketDataService;
}());
/* istanbul ignore next */
cov_xt36771t1().s[369]++;
exports.SkillMarketDataService = SkillMarketDataService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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