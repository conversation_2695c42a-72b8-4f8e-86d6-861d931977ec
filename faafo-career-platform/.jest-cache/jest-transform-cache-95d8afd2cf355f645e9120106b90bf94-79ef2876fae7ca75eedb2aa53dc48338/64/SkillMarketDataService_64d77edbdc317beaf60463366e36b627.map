{"version": 3, "names": ["SkillMarketDataService", "cov_xt36771t1", "s", "f", "cache", "Map", "cacheStats", "hits", "misses", "totalRequests", "CACHE_TTL", "mockMarketData", "javascript", "demand", "supply", "averageSalary", "growth", "difficulty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "category", "react", "nodejs", "python", "typescript", "prototype", "setEdge<PERSON>ase<PERSON><PERSON><PERSON>", "handler", "edgeCaseHandler", "getSkillMarketData", "skill_1", "Promise", "skill", "options", "b", "trim", "Error", "normalizedSkill", "toLowerCase", "cache<PERSON>ey", "concat", "forceRefresh", "cached", "get", "Date", "now", "timestamp", "maxAge", "data", "fetchMarketDataFromAPI", "marketData", "_a", "sent", "set", "__assign", "isStale", "getDefaultMarketData", "getSkillMarketDataWithEdgeHandling", "handleMarketDataRequest", "success", "sanitizedInput", "error", "error_2", "message", "errorType", "fallbackD<PERSON>", "getMultipleSkillsMarketData", "skills_1", "skills", "length", "promises", "map", "_this", "all", "analyzeMarketTrends", "marketDataList", "topDemandSkills", "__spread<PERSON><PERSON>y", "sort", "a", "slice", "fastestGrowingSkills", "highestPayingSkills", "marketGaps", "filter", "demandSupplyRatio", "opportunityScore", "emergingSkills", "decliningSkills", "getSalaryInsights", "salaries", "reduce", "sum", "salary", "sortedSalaries", "percentiles", "p25", "calculatePercentile", "p50", "p75", "p90", "topPayingSkills", "salaryGrowthPotential", "salaryRange", "min", "Math", "apply", "max", "getLocationBasedMarketData", "location", "baseMarketData", "locationMultipliers", "getLocationMultipliers", "localDemand", "localSalary", "costOfLivingAdjustment", "costOfLiving", "remoteOpportunities", "calculateRemoteOpportunities", "compareLocationMarkets", "locations", "locationComparison", "multipliers", "costOfLivingIndex", "adjustedSalary", "getIndustryMarketData", "industry", "industryMultipliers", "getIndustryMultipliers", "industryDemand", "averageIndustrySalary", "growthProjection", "keyCompanies", "getKeyCompaniesForIndustry", "rankIndustriesBySkillDemand", "industries", "industryRankings", "demandScore", "salaryScore", "growthScore", "overallScore", "getMarketBasedRecommendations", "request", "currentSkills", "currentSkillsData", "recommendedSkillNames", "getRecommendedSkillsForCareer", "careerGoal", "experienceLevel", "recommendedSkillsData", "learningPriority", "skillData", "priorityScore", "calculatePriorityScore", "reasoning", "generateRecommendationReasoning", "allSkillsData", "trends", "currentPotential", "calculateSalaryPotential", "projectedPotential", "recommendedSkills", "marketOpportunities", "salaryImpact", "increase", "batchUpdateMarketData", "batchSize", "batches", "i", "push", "batches_1", "_i", "batch", "getCacheStatistics", "hitRate", "missRate", "cacheSize", "size", "clearCache", "clear", "global", "fetch", "response", "ok", "status", "error_3", "resolve", "setTimeout", "mockData", "lastUpdated", "sortedArray", "percentile", "index", "lower", "floor", "upper", "ceil", "weight", "remoteSkills", "includes", "technology", "finance", "healthcare", "education", "retail", "companies", "careerSkills", "beginner", "intermediate", "advanced", "normalizedCareer", "demandWeight", "growthWeight", "salaryWeight", "gapWeight", "difficultyWeight", "gapScore", "difficultyScore", "reasons", "join", "skillsData", "locationMultiplier", "exports"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/skills/SkillMarketDataService.ts"], "sourcesContent": ["export interface SkillMarketData {\n  skill: string;\n  demand: number; // 0-100 scale\n  supply: number; // 0-100 scale\n  averageSalary: number;\n  growth: number; // percentage\n  difficulty: number; // 1-10 scale\n  timeToLearn: number; // weeks\n  category: string;\n  lastUpdated: Date;\n  isStale?: boolean;\n}\n\nexport interface MarketTrends {\n  topDemandSkills: SkillMarketData[];\n  fastestGrowingSkills: SkillMarketData[];\n  highestPayingSkills: SkillMarketData[];\n  marketGaps: MarketGap[];\n  emergingSkills: SkillMarketData[];\n  decliningSkills: SkillMarketData[];\n}\n\nexport interface MarketGap {\n  skill: string;\n  demandSupplyRatio: number;\n  opportunityScore: number;\n  marketData: SkillMarketData;\n}\n\nexport interface SalaryInsights {\n  averageSalary: number;\n  salaryRange: {\n    min: number;\n    max: number;\n  };\n  percentiles: {\n    p25: number;\n    p50: number;\n    p75: number;\n    p90: number;\n  };\n  topPayingSkills: SkillMarketData[];\n  salaryGrowthPotential: number;\n}\n\nexport interface LocationMarketData {\n  skill: string;\n  location: string;\n  localDemand: number;\n  localSalary: number;\n  costOfLivingAdjustment: number;\n  remoteOpportunities: number;\n}\n\nexport interface LocationComparison {\n  skill: string;\n  locationComparison: {\n    location: string;\n    demand: number;\n    salary: number;\n    costOfLivingIndex: number;\n    adjustedSalary: number;\n  }[];\n}\n\nexport interface IndustryMarketData {\n  skill: string;\n  industry: string;\n  industryDemand: number;\n  averageIndustrySalary: number;\n  growthProjection: number;\n  keyCompanies: string[];\n}\n\nexport interface IndustryRanking {\n  skill: string;\n  industries: {\n    industry: string;\n    demandScore: number;\n    salaryScore: number;\n    growthScore: number;\n    overallScore: number;\n  }[];\n}\n\nexport interface MarketRecommendationRequest {\n  currentSkills: string[];\n  careerGoal: string;\n  location: string;\n  experienceLevel: 'beginner' | 'intermediate' | 'advanced';\n}\n\nexport interface MarketBasedRecommendations {\n  recommendedSkills: SkillMarketData[];\n  learningPriority: {\n    skill: string;\n    priorityScore: number;\n    reasoning: string;\n    marketData: SkillMarketData;\n  }[];\n  marketOpportunities: MarketGap[];\n  salaryImpact: {\n    currentPotential: number;\n    projectedPotential: number;\n    increase: number;\n  };\n}\n\nexport interface CacheStatistics {\n  hitRate: number;\n  missRate: number;\n  totalRequests: number;\n  cacheSize: number;\n}\n\nexport interface MarketDataOptions {\n  forceRefresh?: boolean;\n  maxAge?: number; // milliseconds\n}\n\nexport class SkillMarketDataService {\n  private cache: Map<string, { data: SkillMarketData; timestamp: number }> = new Map();\n  private cacheStats = {\n    hits: 0,\n    misses: 0,\n    totalRequests: 0,\n  };\n  private readonly CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours\n  private edgeCaseHandler: any; // Will be injected\n\n  constructor() {\n    // EdgeCaseHandler will be injected later to avoid circular dependencies\n  }\n\n  setEdgeCaseHandler(handler: any) {\n    this.edgeCaseHandler = handler;\n  }\n\n  // Mock market data for testing and development\n  private mockMarketData: Record<string, Partial<SkillMarketData>> = {\n    javascript: {\n      demand: 85,\n      supply: 70,\n      averageSalary: 95000,\n      growth: 12.5,\n      difficulty: 6,\n      timeToLearn: 8,\n      category: 'Programming',\n    },\n    react: {\n      demand: 80,\n      supply: 65,\n      averageSalary: 90000,\n      growth: 15.2,\n      difficulty: 7,\n      timeToLearn: 10,\n      category: 'Frontend',\n    },\n    nodejs: {\n      demand: 75,\n      supply: 60,\n      averageSalary: 88000,\n      growth: 10.8,\n      difficulty: 6,\n      timeToLearn: 12,\n      category: 'Backend',\n    },\n    python: {\n      demand: 90,\n      supply: 75,\n      averageSalary: 100000,\n      growth: 18.7,\n      difficulty: 5,\n      timeToLearn: 6,\n      category: 'Programming',\n    },\n    typescript: {\n      demand: 78,\n      supply: 55,\n      averageSalary: 98000,\n      growth: 22.3,\n      difficulty: 7,\n      timeToLearn: 4,\n      category: 'Programming',\n    },\n  };\n\n  async getSkillMarketData(skill: string, options: MarketDataOptions = {}): Promise<SkillMarketData> {\n    if (!skill || skill.trim() === '') {\n      throw new Error('Invalid skill name');\n    }\n\n    const normalizedSkill = skill.toLowerCase().trim();\n    const cacheKey = `skill:${normalizedSkill}`;\n\n    this.cacheStats.totalRequests++;\n\n    // Check cache first\n    if (!options.forceRefresh) {\n      const cached = this.cache.get(cacheKey);\n      if (cached && Date.now() - cached.timestamp < (options.maxAge || this.CACHE_TTL)) {\n        this.cacheStats.hits++;\n        return cached.data;\n      }\n    }\n\n    this.cacheStats.misses++;\n\n    try {\n      // In a real implementation, this would call external APIs\n      const marketData = await this.fetchMarketDataFromAPI(normalizedSkill);\n\n      // Cache the result\n      this.cache.set(cacheKey, {\n        data: marketData,\n        timestamp: Date.now(),\n      });\n\n      return marketData;\n    } catch (error) {\n      // Return stale data if available, otherwise return default\n      const cached = this.cache.get(cacheKey);\n      if (cached) {\n        return { ...cached.data, isStale: true };\n      }\n\n      return this.getDefaultMarketData(normalizedSkill);\n    }\n  }\n\n  /**\n   * Get skill market data with comprehensive edge case handling\n   */\n  async getSkillMarketDataWithEdgeHandling(skill: string, options: MarketDataOptions = {}): Promise<any> {\n    if (this.edgeCaseHandler) {\n      return this.edgeCaseHandler.handleMarketDataRequest({ skill, ...options });\n    }\n\n    // Fallback to regular method if no edge case handler\n    try {\n      const data = await this.getSkillMarketData(skill, options);\n      return {\n        success: true,\n        data,\n        sanitizedInput: { skill, ...options }\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error',\n        errorType: 'SYSTEM_ERROR',\n        fallbackData: this.getDefaultMarketData(skill)\n      };\n    }\n  }\n\n  async getMultipleSkillsMarketData(skills: string[], options: MarketDataOptions = {}): Promise<SkillMarketData[]> {\n    if (!skills || skills.length === 0) {\n      throw new Error('No skills provided');\n    }\n\n    const promises = skills.map(skill => this.getSkillMarketData(skill, options));\n    return Promise.all(promises);\n  }\n\n  async analyzeMarketTrends(skills: string[]): Promise<MarketTrends> {\n    const marketDataList = await this.getMultipleSkillsMarketData(skills);\n\n    const topDemandSkills = [...marketDataList]\n      .sort((a, b) => b.demand - a.demand)\n      .slice(0, 5);\n\n    const fastestGrowingSkills = [...marketDataList]\n      .sort((a, b) => b.growth - a.growth)\n      .slice(0, 5);\n\n    const highestPayingSkills = [...marketDataList]\n      .sort((a, b) => b.averageSalary - a.averageSalary)\n      .slice(0, 5);\n\n    const marketGaps = marketDataList\n      .filter(data => data.demand > data.supply)\n      .map(data => ({\n        skill: data.skill,\n        demandSupplyRatio: data.demand / data.supply,\n        opportunityScore: (data.demand - data.supply) * (data.growth / 100),\n        marketData: data,\n      }))\n      .sort((a, b) => b.opportunityScore - a.opportunityScore);\n\n    const emergingSkills = marketDataList\n      .filter(data => data.growth > 15)\n      .sort((a, b) => b.growth - a.growth);\n\n    const decliningSkills = marketDataList\n      .filter(data => data.growth < 5)\n      .sort((a, b) => a.growth - b.growth);\n\n    return {\n      topDemandSkills,\n      fastestGrowingSkills,\n      highestPayingSkills,\n      marketGaps,\n      emergingSkills,\n      decliningSkills,\n    };\n  }\n\n  async getSalaryInsights(skills: string[]): Promise<SalaryInsights> {\n    const marketDataList = await this.getMultipleSkillsMarketData(skills);\n    const salaries = marketDataList.map(data => data.averageSalary);\n\n    const averageSalary = salaries.reduce((sum, salary) => sum + salary, 0) / salaries.length;\n    const sortedSalaries = [...salaries].sort((a, b) => a - b);\n\n    const percentiles = {\n      p25: this.calculatePercentile(sortedSalaries, 25),\n      p50: this.calculatePercentile(sortedSalaries, 50),\n      p75: this.calculatePercentile(sortedSalaries, 75),\n      p90: this.calculatePercentile(sortedSalaries, 90),\n    };\n\n    const topPayingSkills = [...marketDataList]\n      .sort((a, b) => b.averageSalary - a.averageSalary)\n      .slice(0, 5);\n\n    const salaryGrowthPotential = marketDataList\n      .reduce((sum, data) => sum + data.growth, 0) / marketDataList.length;\n\n    return {\n      averageSalary,\n      salaryRange: {\n        min: Math.min(...salaries),\n        max: Math.max(...salaries),\n      },\n      percentiles,\n      topPayingSkills,\n      salaryGrowthPotential,\n    };\n  }\n\n  async getLocationBasedMarketData(skill: string, location: string): Promise<LocationMarketData> {\n    const baseMarketData = await this.getSkillMarketData(skill);\n    \n    // Mock location-based adjustments\n    const locationMultipliers = this.getLocationMultipliers(location);\n    \n    return {\n      skill,\n      location,\n      localDemand: baseMarketData.demand * locationMultipliers.demand,\n      localSalary: baseMarketData.averageSalary * locationMultipliers.salary,\n      costOfLivingAdjustment: locationMultipliers.costOfLiving,\n      remoteOpportunities: this.calculateRemoteOpportunities(skill),\n    };\n  }\n\n  async compareLocationMarkets(skill: string, locations: string[]): Promise<LocationComparison> {\n    const baseMarketData = await this.getSkillMarketData(skill);\n    \n    const locationComparison = locations.map(location => {\n      const multipliers = this.getLocationMultipliers(location);\n      const salary = baseMarketData.averageSalary * multipliers.salary;\n      \n      return {\n        location,\n        demand: baseMarketData.demand * multipliers.demand,\n        salary,\n        costOfLivingIndex: multipliers.costOfLiving,\n        adjustedSalary: salary / multipliers.costOfLiving,\n      };\n    });\n\n    return {\n      skill,\n      locationComparison,\n    };\n  }\n\n  async getIndustryMarketData(skill: string, industry: string): Promise<IndustryMarketData> {\n    const baseMarketData = await this.getSkillMarketData(skill);\n    const industryMultipliers = this.getIndustryMultipliers(industry);\n    \n    return {\n      skill,\n      industry,\n      industryDemand: baseMarketData.demand * industryMultipliers.demand,\n      averageIndustrySalary: baseMarketData.averageSalary * industryMultipliers.salary,\n      growthProjection: baseMarketData.growth * industryMultipliers.growth,\n      keyCompanies: this.getKeyCompaniesForIndustry(industry),\n    };\n  }\n\n  async rankIndustriesBySkillDemand(skill: string): Promise<IndustryRanking> {\n    const industries = ['technology', 'finance', 'healthcare', 'education', 'retail'];\n    const baseMarketData = await this.getSkillMarketData(skill);\n    \n    const industryRankings = industries.map(industry => {\n      const multipliers = this.getIndustryMultipliers(industry);\n      const demandScore = baseMarketData.demand * multipliers.demand;\n      const salaryScore = baseMarketData.averageSalary * multipliers.salary / 1000; // Normalize\n      const growthScore = baseMarketData.growth * multipliers.growth;\n      \n      return {\n        industry,\n        demandScore,\n        salaryScore,\n        growthScore,\n        overallScore: (demandScore + salaryScore + growthScore) / 3,\n      };\n    }).sort((a, b) => b.overallScore - a.overallScore);\n\n    return {\n      skill,\n      industries: industryRankings,\n    };\n  }\n\n  async getMarketBasedRecommendations(request: MarketRecommendationRequest): Promise<MarketBasedRecommendations> {\n    // Get market data for current skills\n    const currentSkillsData = await this.getMultipleSkillsMarketData(request.currentSkills);\n    \n    // Get recommended skills based on career goal\n    const recommendedSkillNames = this.getRecommendedSkillsForCareer(request.careerGoal, request.experienceLevel);\n    const recommendedSkillsData = await this.getMultipleSkillsMarketData(recommendedSkillNames);\n    \n    // Calculate learning priority\n    const learningPriority = recommendedSkillsData\n      .map(skillData => ({\n        skill: skillData.skill,\n        priorityScore: this.calculatePriorityScore(skillData, request),\n        reasoning: this.generateRecommendationReasoning(skillData, request),\n        marketData: skillData,\n      }))\n      .sort((a, b) => b.priorityScore - a.priorityScore);\n\n    // Identify market opportunities\n    const allSkillsData = [...currentSkillsData, ...recommendedSkillsData];\n    const trends = await this.analyzeMarketTrends(allSkillsData.map(s => s.skill));\n    \n    // Calculate salary impact\n    const currentPotential = this.calculateSalaryPotential(currentSkillsData, request.location);\n    const projectedPotential = this.calculateSalaryPotential([...currentSkillsData, ...recommendedSkillsData], request.location);\n    \n    return {\n      recommendedSkills: recommendedSkillsData,\n      learningPriority,\n      marketOpportunities: trends.marketGaps,\n      salaryImpact: {\n        currentPotential,\n        projectedPotential,\n        increase: projectedPotential - currentPotential,\n      },\n    };\n  }\n\n  async batchUpdateMarketData(skills: string[]): Promise<void> {\n    // Batch update for efficiency\n    const batchSize = 10;\n    const batches = [];\n    \n    for (let i = 0; i < skills.length; i += batchSize) {\n      batches.push(skills.slice(i, i + batchSize));\n    }\n\n    for (const batch of batches) {\n      await Promise.all(batch.map(skill => this.getSkillMarketData(skill, { forceRefresh: true })));\n    }\n  }\n\n  async getCacheStatistics(): Promise<CacheStatistics> {\n    const hitRate = this.cacheStats.totalRequests > 0 \n      ? this.cacheStats.hits / this.cacheStats.totalRequests \n      : 0;\n    \n    const missRate = this.cacheStats.totalRequests > 0 \n      ? this.cacheStats.misses / this.cacheStats.totalRequests \n      : 0;\n\n    return {\n      hitRate,\n      missRate,\n      totalRequests: this.cacheStats.totalRequests,\n      cacheSize: this.cache.size,\n    };\n  }\n\n  async clearCache(): Promise<void> {\n    this.cache.clear();\n    this.cacheStats = {\n      hits: 0,\n      misses: 0,\n      totalRequests: 0,\n    };\n  }\n\n  private async fetchMarketDataFromAPI(skill: string): Promise<SkillMarketData> {\n    // Check if fetch is mocked and will fail\n    if (global.fetch && typeof global.fetch === 'function') {\n      try {\n        const response = await global.fetch(`/api/skills/${skill}`);\n        if (!response.ok) {\n          throw new Error(`API Error: ${response.status}`);\n        }\n        // In a real implementation, we would parse the response\n        // For testing, we'll fall through to mock data\n      } catch (error) {\n        // API failed, throw error to trigger fallback logic\n        throw error;\n      }\n    }\n\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 100));\n\n    const mockData = this.mockMarketData[skill];\n    if (!mockData) {\n      return this.getDefaultMarketData(skill);\n    }\n\n    return {\n      skill,\n      demand: mockData.demand || 0,\n      supply: mockData.supply || 0,\n      averageSalary: mockData.averageSalary || 0,\n      growth: mockData.growth || 0,\n      difficulty: mockData.difficulty || 5,\n      timeToLearn: mockData.timeToLearn || 12,\n      category: mockData.category || 'General',\n      lastUpdated: new Date(),\n    };\n  }\n\n  private getDefaultMarketData(skill: string): SkillMarketData {\n    return {\n      skill,\n      demand: 0,\n      supply: 0,\n      averageSalary: 0,\n      growth: 0,\n      difficulty: 5,\n      timeToLearn: 12,\n      category: 'Unknown',\n      lastUpdated: new Date(),\n      isStale: true,\n    };\n  }\n\n  private calculatePercentile(sortedArray: number[], percentile: number): number {\n    const index = (percentile / 100) * (sortedArray.length - 1);\n    const lower = Math.floor(index);\n    const upper = Math.ceil(index);\n    \n    if (lower === upper) {\n      return sortedArray[lower];\n    }\n    \n    const weight = index - lower;\n    return sortedArray[lower] * (1 - weight) + sortedArray[upper] * weight;\n  }\n\n  private getLocationMultipliers(location: string): { demand: number; salary: number; costOfLiving: number } {\n    const multipliers: Record<string, { demand: number; salary: number; costOfLiving: number }> = {\n      'san francisco': { demand: 1.3, salary: 1.4, costOfLiving: 1.8 },\n      'new york': { demand: 1.2, salary: 1.3, costOfLiving: 1.6 },\n      'austin': { demand: 1.1, salary: 1.1, costOfLiving: 1.2 },\n      'remote': { demand: 1.0, salary: 1.0, costOfLiving: 1.0 },\n    };\n\n    return multipliers[location.toLowerCase()] || { demand: 1.0, salary: 1.0, costOfLiving: 1.0 };\n  }\n\n  private calculateRemoteOpportunities(skill: string): number {\n    // Mock calculation based on skill type\n    const remoteSkills = ['javascript', 'react', 'nodejs', 'python', 'typescript'];\n    return remoteSkills.includes(skill.toLowerCase()) ? 85 : 45;\n  }\n\n  private getIndustryMultipliers(industry: string): { demand: number; salary: number; growth: number } {\n    const multipliers: Record<string, { demand: number; salary: number; growth: number }> = {\n      technology: { demand: 1.3, salary: 1.2, growth: 1.4 },\n      finance: { demand: 1.1, salary: 1.3, growth: 1.1 },\n      healthcare: { demand: 0.8, salary: 0.9, growth: 1.2 },\n      education: { demand: 0.6, salary: 0.7, growth: 0.8 },\n      retail: { demand: 0.7, salary: 0.8, growth: 0.9 },\n    };\n\n    return multipliers[industry.toLowerCase()] || { demand: 1.0, salary: 1.0, growth: 1.0 };\n  }\n\n  private getKeyCompaniesForIndustry(industry: string): string[] {\n    const companies: Record<string, string[]> = {\n      technology: ['Google', 'Microsoft', 'Apple', 'Amazon', 'Meta'],\n      finance: ['JPMorgan', 'Goldman Sachs', 'Morgan Stanley', 'Bank of America', 'Wells Fargo'],\n      healthcare: ['Johnson & Johnson', 'Pfizer', 'UnitedHealth', 'Merck', 'AbbVie'],\n      education: ['Pearson', 'McGraw-Hill', 'Coursera', 'Khan Academy', 'Udemy'],\n      retail: ['Amazon', 'Walmart', 'Target', 'Home Depot', 'Costco'],\n    };\n\n    return companies[industry.toLowerCase()] || [];\n  }\n\n  private getRecommendedSkillsForCareer(careerGoal: string, experienceLevel: string): string[] {\n    const careerSkills: Record<string, Record<string, string[]>> = {\n      'full stack developer': {\n        beginner: ['javascript', 'html', 'css', 'react'],\n        intermediate: ['nodejs', 'typescript', 'mongodb', 'express'],\n        advanced: ['docker', 'kubernetes', 'aws', 'microservices'],\n      },\n      'frontend developer': {\n        beginner: ['html', 'css', 'javascript', 'react'],\n        intermediate: ['typescript', 'redux', 'webpack', 'sass'],\n        advanced: ['nextjs', 'graphql', 'testing', 'performance'],\n      },\n    };\n\n    const normalizedCareer = careerGoal.toLowerCase();\n    const skills = careerSkills[normalizedCareer]?.[experienceLevel] || ['javascript', 'react', 'nodejs'];\n    \n    return skills;\n  }\n\n  private calculatePriorityScore(skillData: SkillMarketData, request: MarketRecommendationRequest): number {\n    const demandWeight = 0.3;\n    const growthWeight = 0.25;\n    const salaryWeight = 0.2;\n    const gapWeight = 0.15;\n    const difficultyWeight = 0.1;\n\n    const demandScore = skillData.demand;\n    const growthScore = skillData.growth;\n    const salaryScore = skillData.averageSalary / 1000; // Normalize\n    const gapScore = Math.max(0, skillData.demand - skillData.supply);\n    const difficultyScore = 11 - skillData.difficulty; // Invert difficulty (easier = higher score)\n\n    return (\n      demandScore * demandWeight +\n      growthScore * growthWeight +\n      salaryScore * salaryWeight +\n      gapScore * gapWeight +\n      difficultyScore * difficultyWeight\n    );\n  }\n\n  private generateRecommendationReasoning(skillData: SkillMarketData, request: MarketRecommendationRequest): string {\n    const reasons = [];\n\n    if (skillData.demand > 80) {\n      reasons.push('high market demand');\n    }\n    if (skillData.growth > 15) {\n      reasons.push('rapid growth trajectory');\n    }\n    if (skillData.averageSalary > 90000) {\n      reasons.push('excellent salary potential');\n    }\n    if (skillData.demand > skillData.supply) {\n      reasons.push('market opportunity gap');\n    }\n\n    return reasons.length > 0 \n      ? `Recommended due to ${reasons.join(', ')}`\n      : 'Solid foundational skill for your career path';\n  }\n\n  private calculateSalaryPotential(skillsData: SkillMarketData[], location: string): number {\n    const locationMultiplier = this.getLocationMultipliers(location).salary;\n    const averageSalary = skillsData.reduce((sum, skill) => sum + skill.averageSalary, 0) / skillsData.length;\n    \n    return averageSalary * locationMultiplier;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwHA,IAAAA,sBAAA;AAAA;AAAA,cAAAC,aAAA,GAAAC,CAAA;EAAA;EAAAD,aAAA,GAAAE,CAAA;EAUE,SAAAH,uBAAA;IAAA;IAAAC,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAC,CAAA;IATQ,KAAAE,KAAK,GAA8D,IAAIC,GAAG,EAAE;IAAC;IAAAJ,aAAA,GAAAC,CAAA;IAC7E,KAAAI,UAAU,GAAG;MACnBC,IAAI,EAAE,CAAC;MACPC,MAAM,EAAE,CAAC;MACTC,aAAa,EAAE;KAChB;IAAC;IAAAR,aAAA,GAAAC,CAAA;IACe,KAAAQ,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAWlD;IAAA;IAAAT,aAAA,GAAAC,CAAA;IACQ,KAAAS,cAAc,GAA6C;MACjEC,UAAU,EAAE;QACVC,MAAM,EAAE,EAAE;QACVC,MAAM,EAAE,EAAE;QACVC,aAAa,EAAE,KAAK;QACpBC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,CAAC;QACbC,WAAW,EAAE,CAAC;QACdC,QAAQ,EAAE;OACX;MACDC,KAAK,EAAE;QACLP,MAAM,EAAE,EAAE;QACVC,MAAM,EAAE,EAAE;QACVC,aAAa,EAAE,KAAK;QACpBC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,CAAC;QACbC,WAAW,EAAE,EAAE;QACfC,QAAQ,EAAE;OACX;MACDE,MAAM,EAAE;QACNR,MAAM,EAAE,EAAE;QACVC,MAAM,EAAE,EAAE;QACVC,aAAa,EAAE,KAAK;QACpBC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,CAAC;QACbC,WAAW,EAAE,EAAE;QACfC,QAAQ,EAAE;OACX;MACDG,MAAM,EAAE;QACNT,MAAM,EAAE,EAAE;QACVC,MAAM,EAAE,EAAE;QACVC,aAAa,EAAE,MAAM;QACrBC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,CAAC;QACbC,WAAW,EAAE,CAAC;QACdC,QAAQ,EAAE;OACX;MACDI,UAAU,EAAE;QACVV,MAAM,EAAE,EAAE;QACVC,MAAM,EAAE,EAAE;QACVC,aAAa,EAAE,KAAK;QACpBC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,CAAC;QACbC,WAAW,EAAE,CAAC;QACdC,QAAQ,EAAE;;KAEb;IAtDC;EACF;EAAC;EAAAlB,aAAA,GAAAC,CAAA;EAEDF,sBAAA,CAAAwB,SAAA,CAAAC,kBAAkB,GAAlB,UAAmBC,OAAY;IAAA;IAAAzB,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAC,CAAA;IAC7B,IAAI,CAACyB,eAAe,GAAGD,OAAO;EAChC,CAAC;EAAA;EAAAzB,aAAA,GAAAC,CAAA;EAmDKF,sBAAA,CAAAwB,SAAA,CAAAI,kBAAkB,GAAxB,UAAAC,OAAA;IAAA;IAAA5B,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAC,CAAA;sCAA0E4B,OAAO,YAAxDC,KAAa,EAAEC,OAA+B;MAAA;MAAA/B,aAAA,GAAAE,CAAA;;;;MAA/B,IAAA6B,OAAA;QAAA;QAAA/B,aAAA,GAAAgC,CAAA;QAAAhC,aAAA,GAAAC,CAAA;QAAA8B,OAAA,KAA+B;MAAA;MAAA;MAAA;QAAA/B,aAAA,GAAAgC,CAAA;MAAA;MAAAhC,aAAA,GAAAC,CAAA;;;;;;;;;;YACrE;YAAI;YAAA,CAAAD,aAAA,GAAAgC,CAAA,YAACF,KAAK;YAAA;YAAA,CAAA9B,aAAA,GAAAgC,CAAA,WAAIF,KAAK,CAACG,IAAI,EAAE,KAAK,EAAE,GAAE;cAAA;cAAAjC,aAAA,GAAAgC,CAAA;cAAAhC,aAAA,GAAAC,CAAA;cACjC,MAAM,IAAIiC,KAAK,CAAC,oBAAoB,CAAC;YACvC,CAAC;YAAA;YAAA;cAAAlC,aAAA,GAAAgC,CAAA;YAAA;YAAAhC,aAAA,GAAAC,CAAA;YAEKkC,eAAe,GAAGL,KAAK,CAACM,WAAW,EAAE,CAACH,IAAI,EAAE;YAAC;YAAAjC,aAAA,GAAAC,CAAA;YAC7CoC,QAAQ,GAAG,SAAAC,MAAA,CAASH,eAAe,CAAE;YAAC;YAAAnC,aAAA,GAAAC,CAAA;YAE5C,IAAI,CAACI,UAAU,CAACG,aAAa,EAAE;YAE/B;YAAA;YAAAR,aAAA,GAAAC,CAAA;YACA,IAAI,CAAC8B,OAAO,CAACQ,YAAY,EAAE;cAAA;cAAAvC,aAAA,GAAAgC,CAAA;cAAAhC,aAAA,GAAAC,CAAA;cACnBuC,MAAM,GAAG,IAAI,CAACrC,KAAK,CAACsC,GAAG,CAACJ,QAAQ,CAAC;cAAC;cAAArC,aAAA,GAAAC,CAAA;cACxC;cAAI;cAAA,CAAAD,aAAA,GAAAgC,CAAA,WAAAQ,MAAM;cAAA;cAAA,CAAAxC,aAAA,GAAAgC,CAAA,WAAIU,IAAI,CAACC,GAAG,EAAE,GAAGH,MAAM,CAACI,SAAS;cAAI;cAAA,CAAA5C,aAAA,GAAAgC,CAAA,WAAAD,OAAO,CAACc,MAAM;cAAA;cAAA,CAAA7C,aAAA,GAAAgC,CAAA,WAAI,IAAI,CAACvB,SAAS,EAAC,GAAE;gBAAA;gBAAAT,aAAA,GAAAgC,CAAA;gBAAAhC,aAAA,GAAAC,CAAA;gBAChF,IAAI,CAACI,UAAU,CAACC,IAAI,EAAE;gBAAC;gBAAAN,aAAA,GAAAC,CAAA;gBACvB,sBAAOuC,MAAM,CAACM,IAAI;cACpB,CAAC;cAAA;cAAA;gBAAA9C,aAAA,GAAAgC,CAAA;cAAA;YACH,CAAC;YAAA;YAAA;cAAAhC,aAAA,GAAAgC,CAAA;YAAA;YAAAhC,aAAA,GAAAC,CAAA;YAED,IAAI,CAACI,UAAU,CAACE,MAAM,EAAE;YAAC;YAAAP,aAAA,GAAAC,CAAA;;;;;;;;;YAIJ,qBAAM,IAAI,CAAC8C,sBAAsB,CAACZ,eAAe,CAAC;;;;;YAA/Da,UAAU,GAAGC,EAAA,CAAAC,IAAA,EAAkD;YAErE;YAAA;YAAAlD,aAAA,GAAAC,CAAA;YACA,IAAI,CAACE,KAAK,CAACgD,GAAG,CAACd,QAAQ,EAAE;cACvBS,IAAI,EAAEE,UAAU;cAChBJ,SAAS,EAAEF,IAAI,CAACC,GAAG;aACpB,CAAC;YAAC;YAAA3C,aAAA,GAAAC,CAAA;YAEH,sBAAO+C,UAAU;;;;;;;;YAGXR,MAAM,GAAG,IAAI,CAACrC,KAAK,CAACsC,GAAG,CAACJ,QAAQ,CAAC;YAAC;YAAArC,aAAA,GAAAC,CAAA;YACxC,IAAIuC,MAAM,EAAE;cAAA;cAAAxC,aAAA,GAAAgC,CAAA;cAAAhC,aAAA,GAAAC,CAAA;cACV,sBAAAmD,QAAA,CAAAA,QAAA,KAAYZ,MAAM,CAACM,IAAI;gBAAEO,OAAO,EAAE;cAAI;YACxC,CAAC;YAAA;YAAA;cAAArD,aAAA,GAAAgC,CAAA;YAAA;YAAAhC,aAAA,GAAAC,CAAA;YAED,sBAAO,IAAI,CAACqD,oBAAoB,CAACnB,eAAe,CAAC;;;;;;;;;GAEpD;EAED;;;EAAA;EAAAnC,aAAA,GAAAC,CAAA;EAGMF,sBAAA,CAAAwB,SAAA,CAAAgC,kCAAkC,GAAxC,UAAA3B,OAAA;IAAA;IAAA5B,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAC,CAAA;sCAA0F4B,OAAO,YAAxDC,KAAa,EAAEC,OAA+B;MAAA;MAAA/B,aAAA,GAAAE,CAAA;;;;MAA/B,IAAA6B,OAAA;QAAA;QAAA/B,aAAA,GAAAgC,CAAA;QAAAhC,aAAA,GAAAC,CAAA;QAAA8B,OAAA,KAA+B;MAAA;MAAA;MAAA;QAAA/B,aAAA,GAAAgC,CAAA;MAAA;MAAAhC,aAAA,GAAAC,CAAA;;;;;;;;;;YACrF,IAAI,IAAI,CAACyB,eAAe,EAAE;cAAA;cAAA1B,aAAA,GAAAgC,CAAA;cAAAhC,aAAA,GAAAC,CAAA;cACxB,sBAAO,IAAI,CAACyB,eAAe,CAAC8B,uBAAuB,CAAAJ,QAAA;gBAAGtB,KAAK,EAAAA;cAAA,GAAKC,OAAO,EAAG;YAC5E,CAAC;YAAA;YAAA;cAAA/B,aAAA,GAAAgC,CAAA;YAAA;YAAAhC,aAAA,GAAAC,CAAA;;;;;;;;;YAIc,qBAAM,IAAI,CAAC0B,kBAAkB,CAACG,KAAK,EAAEC,OAAO,CAAC;;;;;YAApDe,IAAI,GAAGG,EAAA,CAAAC,IAAA,EAA6C;YAAA;YAAAlD,aAAA,GAAAC,CAAA;YAC1D,sBAAO;cACLwD,OAAO,EAAE,IAAI;cACbX,IAAI,EAAAA,IAAA;cACJY,cAAc,EAAAN,QAAA;gBAAItB,KAAK,EAAAA;cAAA,GAAKC,OAAO;aACpC;;;;;;;;YAED,sBAAO;cACL0B,OAAO,EAAE,KAAK;cACdE,KAAK,EAAEC,OAAK,YAAY1B,KAAK;cAAA;cAAA,CAAAlC,aAAA,GAAAgC,CAAA,WAAG4B,OAAK,CAACC,OAAO;cAAA;cAAA,CAAA7D,aAAA,GAAAgC,CAAA,WAAG,eAAe;cAC/D8B,SAAS,EAAE,cAAc;cACzBC,YAAY,EAAE,IAAI,CAACT,oBAAoB,CAACxB,KAAK;aAC9C;;;;;;;;;GAEJ;EAAA;EAAA9B,aAAA,GAAAC,CAAA;EAEKF,sBAAA,CAAAwB,SAAA,CAAAyC,2BAA2B,GAAjC,UAAAC,QAAA;IAAA;IAAAjE,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAC,CAAA;sCAAsF4B,OAAO,YAA3DqC,MAAgB,EAAEnC,OAA+B;MAAA;MAAA/B,aAAA,GAAAE,CAAA;;;;;;;MAA/B,IAAA6B,OAAA;QAAA;QAAA/B,aAAA,GAAAgC,CAAA;QAAAhC,aAAA,GAAAC,CAAA;QAAA8B,OAAA,KAA+B;MAAA;MAAA;MAAA;QAAA/B,aAAA,GAAAgC,CAAA;MAAA;MAAAhC,aAAA,GAAAC,CAAA;;;;;QACjF;QAAI;QAAA,CAAAD,aAAA,GAAAgC,CAAA,YAACkC,MAAM;QAAA;QAAA,CAAAlE,aAAA,GAAAgC,CAAA,WAAIkC,MAAM,CAACC,MAAM,KAAK,CAAC,GAAE;UAAA;UAAAnE,aAAA,GAAAgC,CAAA;UAAAhC,aAAA,GAAAC,CAAA;UAClC,MAAM,IAAIiC,KAAK,CAAC,oBAAoB,CAAC;QACvC,CAAC;QAAA;QAAA;UAAAlC,aAAA,GAAAgC,CAAA;QAAA;QAAAhC,aAAA,GAAAC,CAAA;QAEKmE,QAAQ,GAAGF,MAAM,CAACG,GAAG,CAAC,UAAAvC,KAAK;UAAA;UAAA9B,aAAA,GAAAE,CAAA;UAAAF,aAAA,GAAAC,CAAA;UAAI,OAAAqE,KAAI,CAAC3C,kBAAkB,CAACG,KAAK,EAAEC,OAAO,CAAC;QAAvC,CAAuC,CAAC;QAAC;QAAA/B,aAAA,GAAAC,CAAA;QAC9E,sBAAO4B,OAAO,CAAC0C,GAAG,CAACH,QAAQ,CAAC;;;GAC7B;EAAA;EAAApE,aAAA,GAAAC,CAAA;EAEKF,sBAAA,CAAAwB,SAAA,CAAAiD,mBAAmB,GAAzB,UAA0BN,MAAgB;IAAA;IAAAlE,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAC,CAAA;mCAAG4B,OAAO;MAAA;MAAA7B,aAAA,GAAAE,CAAA;;;;;;;;;;;;;YAC3B,qBAAM,IAAI,CAAC8D,2BAA2B,CAACE,MAAM,CAAC;;;;;YAA/DO,cAAc,GAAGxB,EAAA,CAAAC,IAAA,EAA8C;YAAA;YAAAlD,aAAA,GAAAC,CAAA;YAE/DyE,eAAe,GAAGC,aAAA,KAAIF,cAAc,QACvCG,IAAI,CAAC,UAACC,CAAC,EAAE7C,CAAC;cAAA;cAAAhC,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAK,OAAA+B,CAAC,CAACpB,MAAM,GAAGiE,CAAC,CAACjE,MAAM;YAAnB,CAAmB,CAAC,CACnCkE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;YAAC;YAAA9E,aAAA,GAAAC,CAAA;YAET8E,oBAAoB,GAAGJ,aAAA,KAAIF,cAAc,QAC5CG,IAAI,CAAC,UAACC,CAAC,EAAE7C,CAAC;cAAA;cAAAhC,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAK,OAAA+B,CAAC,CAACjB,MAAM,GAAG8D,CAAC,CAAC9D,MAAM;YAAnB,CAAmB,CAAC,CACnC+D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;YAAC;YAAA9E,aAAA,GAAAC,CAAA;YAET+E,mBAAmB,GAAGL,aAAA,KAAIF,cAAc,QAC3CG,IAAI,CAAC,UAACC,CAAC,EAAE7C,CAAC;cAAA;cAAAhC,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAK,OAAA+B,CAAC,CAAClB,aAAa,GAAG+D,CAAC,CAAC/D,aAAa;YAAjC,CAAiC,CAAC,CACjDgE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;YAAC;YAAA9E,aAAA,GAAAC,CAAA;YAETgF,UAAU,GAAGR,cAAc,CAC9BS,MAAM,CAAC,UAAApC,IAAI;cAAA;cAAA9C,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAI,OAAA6C,IAAI,CAAClC,MAAM,GAAGkC,IAAI,CAACjC,MAAM;YAAzB,CAAyB,CAAC,CACzCwD,GAAG,CAAC,UAAAvB,IAAI;cAAA;cAAA9C,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAI,OAAC;gBACZ6B,KAAK,EAAEgB,IAAI,CAAChB,KAAK;gBACjBqD,iBAAiB,EAAErC,IAAI,CAAClC,MAAM,GAAGkC,IAAI,CAACjC,MAAM;gBAC5CuE,gBAAgB,EAAE,CAACtC,IAAI,CAAClC,MAAM,GAAGkC,IAAI,CAACjC,MAAM,KAAKiC,IAAI,CAAC/B,MAAM,GAAG,GAAG,CAAC;gBACnEiC,UAAU,EAAEF;eACb;YALY,CAKX,CAAC,CACF8B,IAAI,CAAC,UAACC,CAAC,EAAE7C,CAAC;cAAA;cAAAhC,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAK,OAAA+B,CAAC,CAACoD,gBAAgB,GAAGP,CAAC,CAACO,gBAAgB;YAAvC,CAAuC,CAAC;YAAC;YAAApF,aAAA,GAAAC,CAAA;YAErDoF,cAAc,GAAGZ,cAAc,CAClCS,MAAM,CAAC,UAAApC,IAAI;cAAA;cAAA9C,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAI,OAAA6C,IAAI,CAAC/B,MAAM,GAAG,EAAE;YAAhB,CAAgB,CAAC,CAChC6D,IAAI,CAAC,UAACC,CAAC,EAAE7C,CAAC;cAAA;cAAAhC,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAK,OAAA+B,CAAC,CAACjB,MAAM,GAAG8D,CAAC,CAAC9D,MAAM;YAAnB,CAAmB,CAAC;YAAC;YAAAf,aAAA,GAAAC,CAAA;YAEjCqF,eAAe,GAAGb,cAAc,CACnCS,MAAM,CAAC,UAAApC,IAAI;cAAA;cAAA9C,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAI,OAAA6C,IAAI,CAAC/B,MAAM,GAAG,CAAC;YAAf,CAAe,CAAC,CAC/B6D,IAAI,CAAC,UAACC,CAAC,EAAE7C,CAAC;cAAA;cAAAhC,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAK,OAAA4E,CAAC,CAAC9D,MAAM,GAAGiB,CAAC,CAACjB,MAAM;YAAnB,CAAmB,CAAC;YAAC;YAAAf,aAAA,GAAAC,CAAA;YAEvC,sBAAO;cACLyE,eAAe,EAAAA,eAAA;cACfK,oBAAoB,EAAAA,oBAAA;cACpBC,mBAAmB,EAAAA,mBAAA;cACnBC,UAAU,EAAAA,UAAA;cACVI,cAAc,EAAAA,cAAA;cACdC,eAAe,EAAAA;aAChB;;;;GACF;EAAA;EAAAtF,aAAA,GAAAC,CAAA;EAEKF,sBAAA,CAAAwB,SAAA,CAAAgE,iBAAiB,GAAvB,UAAwBrB,MAAgB;IAAA;IAAAlE,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAC,CAAA;mCAAG4B,OAAO;MAAA;MAAA7B,aAAA,GAAAE,CAAA;;;;;;;;;;;;;YACzB,qBAAM,IAAI,CAAC8D,2BAA2B,CAACE,MAAM,CAAC;;;;;YAA/DO,cAAc,GAAGxB,EAAA,CAAAC,IAAA,EAA8C;YAAA;YAAAlD,aAAA,GAAAC,CAAA;YAC/DuF,QAAQ,GAAGf,cAAc,CAACJ,GAAG,CAAC,UAAAvB,IAAI;cAAA;cAAA9C,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAI,OAAA6C,IAAI,CAAChC,aAAa;YAAlB,CAAkB,CAAC;YAAC;YAAAd,aAAA,GAAAC,CAAA;YAE1Da,aAAa,GAAG0E,QAAQ,CAACC,MAAM,CAAC,UAACC,GAAG,EAAEC,MAAM;cAAA;cAAA3F,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAK,OAAAyF,GAAG,GAAGC,MAAM;YAAZ,CAAY,EAAE,CAAC,CAAC,GAAGH,QAAQ,CAACrB,MAAM;YAAC;YAAAnE,aAAA,GAAAC,CAAA;YACpF2F,cAAc,GAAGjB,aAAA,KAAIa,QAAQ,QAAEZ,IAAI,CAAC,UAACC,CAAC,EAAE7C,CAAC;cAAA;cAAAhC,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAK,OAAA4E,CAAC,GAAG7C,CAAC;YAAL,CAAK,CAAC;YAAC;YAAAhC,aAAA,GAAAC,CAAA;YAErD4F,WAAW,GAAG;cAClBC,GAAG,EAAE,IAAI,CAACC,mBAAmB,CAACH,cAAc,EAAE,EAAE,CAAC;cACjDI,GAAG,EAAE,IAAI,CAACD,mBAAmB,CAACH,cAAc,EAAE,EAAE,CAAC;cACjDK,GAAG,EAAE,IAAI,CAACF,mBAAmB,CAACH,cAAc,EAAE,EAAE,CAAC;cACjDM,GAAG,EAAE,IAAI,CAACH,mBAAmB,CAACH,cAAc,EAAE,EAAE;aACjD;YAAC;YAAA5F,aAAA,GAAAC,CAAA;YAEIkG,eAAe,GAAGxB,aAAA,KAAIF,cAAc,QACvCG,IAAI,CAAC,UAACC,CAAC,EAAE7C,CAAC;cAAA;cAAAhC,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAK,OAAA+B,CAAC,CAAClB,aAAa,GAAG+D,CAAC,CAAC/D,aAAa;YAAjC,CAAiC,CAAC,CACjDgE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;YAAC;YAAA9E,aAAA,GAAAC,CAAA;YAETmG,qBAAqB,GAAG3B,cAAc,CACzCgB,MAAM,CAAC,UAACC,GAAG,EAAE5C,IAAI;cAAA;cAAA9C,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAK,OAAAyF,GAAG,GAAG5C,IAAI,CAAC/B,MAAM;YAAjB,CAAiB,EAAE,CAAC,CAAC,GAAG0D,cAAc,CAACN,MAAM;YAAC;YAAAnE,aAAA,GAAAC,CAAA;YAEvE,sBAAO;cACLa,aAAa,EAAAA,aAAA;cACbuF,WAAW,EAAE;gBACXC,GAAG,EAAEC,IAAI,CAACD,GAAG,CAAAE,KAAA,CAARD,IAAI,EAAQf,QAAQ,CAAC;gBAC1BiB,GAAG,EAAEF,IAAI,CAACE,GAAG,CAAAD,KAAA,CAARD,IAAI,EAAQf,QAAQ;eAC1B;cACDK,WAAW,EAAAA,WAAA;cACXM,eAAe,EAAAA,eAAA;cACfC,qBAAqB,EAAAA;aACtB;;;;GACF;EAAA;EAAApG,aAAA,GAAAC,CAAA;EAEKF,sBAAA,CAAAwB,SAAA,CAAAmF,0BAA0B,GAAhC,UAAiC5E,KAAa,EAAE6E,QAAgB;IAAA;IAAA3G,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAC,CAAA;mCAAG4B,OAAO;MAAA;MAAA7B,aAAA,GAAAE,CAAA;;;;;;;;;;;;;YACjD,qBAAM,IAAI,CAACyB,kBAAkB,CAACG,KAAK,CAAC;;;;;YAArD8E,cAAc,GAAG3D,EAAA,CAAAC,IAAA,EAAoC;YAAA;YAAAlD,aAAA,GAAAC,CAAA;YAGrD4G,mBAAmB,GAAG,IAAI,CAACC,sBAAsB,CAACH,QAAQ,CAAC;YAAC;YAAA3G,aAAA,GAAAC,CAAA;YAElE,sBAAO;cACL6B,KAAK,EAAAA,KAAA;cACL6E,QAAQ,EAAAA,QAAA;cACRI,WAAW,EAAEH,cAAc,CAAChG,MAAM,GAAGiG,mBAAmB,CAACjG,MAAM;cAC/DoG,WAAW,EAAEJ,cAAc,CAAC9F,aAAa,GAAG+F,mBAAmB,CAAClB,MAAM;cACtEsB,sBAAsB,EAAEJ,mBAAmB,CAACK,YAAY;cACxDC,mBAAmB,EAAE,IAAI,CAACC,4BAA4B,CAACtF,KAAK;aAC7D;;;;GACF;EAAA;EAAA9B,aAAA,GAAAC,CAAA;EAEKF,sBAAA,CAAAwB,SAAA,CAAA8F,sBAAsB,GAA5B,UAA6BvF,KAAa,EAAEwF,SAAmB;IAAA;IAAAtH,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAC,CAAA;mCAAG4B,OAAO;MAAA;MAAA7B,aAAA,GAAAE,CAAA;;;;;;;;;;;;;;;;YAChD,qBAAM,IAAI,CAACyB,kBAAkB,CAACG,KAAK,CAAC;;;;;YAArD8E,cAAc,GAAG3D,EAAA,CAAAC,IAAA,EAAoC;YAAA;YAAAlD,aAAA,GAAAC,CAAA;YAErDsH,kBAAkB,GAAGD,SAAS,CAACjD,GAAG,CAAC,UAAAsC,QAAQ;cAAA;cAAA3G,aAAA,GAAAE,CAAA;cAC/C,IAAMsH,WAAW;cAAA;cAAA,CAAAxH,aAAA,GAAAC,CAAA,SAAGqE,KAAI,CAACwC,sBAAsB,CAACH,QAAQ,CAAC;cACzD,IAAMhB,MAAM;cAAA;cAAA,CAAA3F,aAAA,GAAAC,CAAA,SAAG2G,cAAc,CAAC9F,aAAa,GAAG0G,WAAW,CAAC7B,MAAM;cAAC;cAAA3F,aAAA,GAAAC,CAAA;cAEjE,OAAO;gBACL0G,QAAQ,EAAAA,QAAA;gBACR/F,MAAM,EAAEgG,cAAc,CAAChG,MAAM,GAAG4G,WAAW,CAAC5G,MAAM;gBAClD+E,MAAM,EAAAA,MAAA;gBACN8B,iBAAiB,EAAED,WAAW,CAACN,YAAY;gBAC3CQ,cAAc,EAAE/B,MAAM,GAAG6B,WAAW,CAACN;eACtC;YACH,CAAC,CAAC;YAAC;YAAAlH,aAAA,GAAAC,CAAA;YAEH,sBAAO;cACL6B,KAAK,EAAAA,KAAA;cACLyF,kBAAkB,EAAAA;aACnB;;;;GACF;EAAA;EAAAvH,aAAA,GAAAC,CAAA;EAEKF,sBAAA,CAAAwB,SAAA,CAAAoG,qBAAqB,GAA3B,UAA4B7F,KAAa,EAAE8F,QAAgB;IAAA;IAAA5H,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAC,CAAA;mCAAG4B,OAAO;MAAA;MAAA7B,aAAA,GAAAE,CAAA;;;;;;;;;;;;;YAC5C,qBAAM,IAAI,CAACyB,kBAAkB,CAACG,KAAK,CAAC;;;;;YAArD8E,cAAc,GAAG3D,EAAA,CAAAC,IAAA,EAAoC;YAAA;YAAAlD,aAAA,GAAAC,CAAA;YACrD4H,mBAAmB,GAAG,IAAI,CAACC,sBAAsB,CAACF,QAAQ,CAAC;YAAC;YAAA5H,aAAA,GAAAC,CAAA;YAElE,sBAAO;cACL6B,KAAK,EAAAA,KAAA;cACL8F,QAAQ,EAAAA,QAAA;cACRG,cAAc,EAAEnB,cAAc,CAAChG,MAAM,GAAGiH,mBAAmB,CAACjH,MAAM;cAClEoH,qBAAqB,EAAEpB,cAAc,CAAC9F,aAAa,GAAG+G,mBAAmB,CAAClC,MAAM;cAChFsC,gBAAgB,EAAErB,cAAc,CAAC7F,MAAM,GAAG8G,mBAAmB,CAAC9G,MAAM;cACpEmH,YAAY,EAAE,IAAI,CAACC,0BAA0B,CAACP,QAAQ;aACvD;;;;GACF;EAAA;EAAA5H,aAAA,GAAAC,CAAA;EAEKF,sBAAA,CAAAwB,SAAA,CAAA6G,2BAA2B,GAAjC,UAAkCtG,KAAa;IAAA;IAAA9B,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAC,CAAA;mCAAG4B,OAAO;MAAA;MAAA7B,aAAA,GAAAE,CAAA;;;;;;;;;;;;;;;;YACjDmI,UAAU,GAAG,CAAC,YAAY,EAAE,SAAS,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,CAAC;YAAC;YAAArI,aAAA,GAAAC,CAAA;YAC3D,qBAAM,IAAI,CAAC0B,kBAAkB,CAACG,KAAK,CAAC;;;;;YAArD8E,cAAc,GAAG3D,EAAA,CAAAC,IAAA,EAAoC;YAAA;YAAAlD,aAAA,GAAAC,CAAA;YAErDqI,gBAAgB,GAAGD,UAAU,CAAChE,GAAG,CAAC,UAAAuD,QAAQ;cAAA;cAAA5H,aAAA,GAAAE,CAAA;cAC9C,IAAMsH,WAAW;cAAA;cAAA,CAAAxH,aAAA,GAAAC,CAAA,SAAGqE,KAAI,CAACwD,sBAAsB,CAACF,QAAQ,CAAC;cACzD,IAAMW,WAAW;cAAA;cAAA,CAAAvI,aAAA,GAAAC,CAAA,SAAG2G,cAAc,CAAChG,MAAM,GAAG4G,WAAW,CAAC5G,MAAM;cAC9D,IAAM4H,WAAW;cAAA;cAAA,CAAAxI,aAAA,GAAAC,CAAA,SAAG2G,cAAc,CAAC9F,aAAa,GAAG0G,WAAW,CAAC7B,MAAM,GAAG,IAAI,EAAC,CAAC;cAC9E,IAAM8C,WAAW;cAAA;cAAA,CAAAzI,aAAA,GAAAC,CAAA,SAAG2G,cAAc,CAAC7F,MAAM,GAAGyG,WAAW,CAACzG,MAAM;cAAC;cAAAf,aAAA,GAAAC,CAAA;cAE/D,OAAO;gBACL2H,QAAQ,EAAAA,QAAA;gBACRW,WAAW,EAAAA,WAAA;gBACXC,WAAW,EAAAA,WAAA;gBACXC,WAAW,EAAAA,WAAA;gBACXC,YAAY,EAAE,CAACH,WAAW,GAAGC,WAAW,GAAGC,WAAW,IAAI;eAC3D;YACH,CAAC,CAAC,CAAC7D,IAAI,CAAC,UAACC,CAAC,EAAE7C,CAAC;cAAA;cAAAhC,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAK,OAAA+B,CAAC,CAAC0G,YAAY,GAAG7D,CAAC,CAAC6D,YAAY;YAA/B,CAA+B,CAAC;YAAC;YAAA1I,aAAA,GAAAC,CAAA;YAEnD,sBAAO;cACL6B,KAAK,EAAAA,KAAA;cACLuG,UAAU,EAAEC;aACb;;;;GACF;EAAA;EAAAtI,aAAA,GAAAC,CAAA;EAEKF,sBAAA,CAAAwB,SAAA,CAAAoH,6BAA6B,GAAnC,UAAoCC,OAAoC;IAAA;IAAA5I,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAC,CAAA;mCAAG4B,OAAO;MAAA;MAAA7B,aAAA,GAAAE,CAAA;;;;;;;;;;;;;;;;YAEtD,qBAAM,IAAI,CAAC8D,2BAA2B,CAAC4E,OAAO,CAACC,aAAa,CAAC;;;;;YAAjFC,iBAAiB,GAAG7F,EAAA,CAAAC,IAAA,EAA6D;YAAA;YAAAlD,aAAA,GAAAC,CAAA;YAGjF8I,qBAAqB,GAAG,IAAI,CAACC,6BAA6B,CAACJ,OAAO,CAACK,UAAU,EAAEL,OAAO,CAACM,eAAe,CAAC;YAAC;YAAAlJ,aAAA,GAAAC,CAAA;YAChF,qBAAM,IAAI,CAAC+D,2BAA2B,CAAC+E,qBAAqB,CAAC;;;;;YAArFI,qBAAqB,GAAGlG,EAAA,CAAAC,IAAA,EAA6D;YAAA;YAAAlD,aAAA,GAAAC,CAAA;YAGrFmJ,gBAAgB,GAAGD,qBAAqB,CAC3C9E,GAAG,CAAC,UAAAgF,SAAS;cAAA;cAAArJ,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAI,OAAC;gBACjB6B,KAAK,EAAEuH,SAAS,CAACvH,KAAK;gBACtBwH,aAAa,EAAEhF,KAAI,CAACiF,sBAAsB,CAACF,SAAS,EAAET,OAAO,CAAC;gBAC9DY,SAAS,EAAElF,KAAI,CAACmF,+BAA+B,CAACJ,SAAS,EAAET,OAAO,CAAC;gBACnE5F,UAAU,EAAEqG;eACb;YALiB,CAKhB,CAAC,CACFzE,IAAI,CAAC,UAACC,CAAC,EAAE7C,CAAC;cAAA;cAAAhC,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAK,OAAA+B,CAAC,CAACsH,aAAa,GAAGzE,CAAC,CAACyE,aAAa;YAAjC,CAAiC,CAAC;YAAC;YAAAtJ,aAAA,GAAAC,CAAA;YAG/CyJ,aAAa,GAAA/E,aAAA,CAAAA,aAAA,KAAOmE,iBAAiB,SAAKK,qBAAqB,OAAC;YAAC;YAAAnJ,aAAA,GAAAC,CAAA;YACxD,qBAAM,IAAI,CAACuE,mBAAmB,CAACkF,aAAa,CAACrF,GAAG,CAAC,UAAApE,CAAC;cAAA;cAAAD,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAI,OAAAA,CAAC,CAAC6B,KAAK;YAAP,CAAO,CAAC,CAAC;;;;;YAAxE6H,MAAM,GAAG1G,EAAA,CAAAC,IAAA,EAA+D;YAAA;YAAAlD,aAAA,GAAAC,CAAA;YAGxE2J,gBAAgB,GAAG,IAAI,CAACC,wBAAwB,CAACf,iBAAiB,EAAEF,OAAO,CAACjC,QAAQ,CAAC;YAAC;YAAA3G,aAAA,GAAAC,CAAA;YACtF6J,kBAAkB,GAAG,IAAI,CAACD,wBAAwB,CAAAlF,aAAA,CAAAA,aAAA,KAAKmE,iBAAiB,SAAKK,qBAAqB,SAAGP,OAAO,CAACjC,QAAQ,CAAC;YAAC;YAAA3G,aAAA,GAAAC,CAAA;YAE7H,sBAAO;cACL8J,iBAAiB,EAAEZ,qBAAqB;cACxCC,gBAAgB,EAAAA,gBAAA;cAChBY,mBAAmB,EAAEL,MAAM,CAAC1E,UAAU;cACtCgF,YAAY,EAAE;gBACZL,gBAAgB,EAAAA,gBAAA;gBAChBE,kBAAkB,EAAAA,kBAAA;gBAClBI,QAAQ,EAAEJ,kBAAkB,GAAGF;;aAElC;;;;GACF;EAAA;EAAA5J,aAAA,GAAAC,CAAA;EAEKF,sBAAA,CAAAwB,SAAA,CAAA4I,qBAAqB,GAA3B,UAA4BjG,MAAgB;IAAA;IAAAlE,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAC,CAAA;mCAAG4B,OAAO;MAAA;MAAA7B,aAAA,GAAAE,CAAA;;;;;;;;;;;;;;;;YAE9CkK,SAAS,GAAG,EAAE;YAAC;YAAApK,aAAA,GAAAC,CAAA;YACfoK,OAAO,GAAG,EAAE;YAAC;YAAArK,aAAA,GAAAC,CAAA;YAEnB,KAASqK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpG,MAAM,CAACC,MAAM,EAAEmG,CAAC,IAAIF,SAAS,EAAE;cAAA;cAAApK,aAAA,GAAAC,CAAA;cACjDoK,OAAO,CAACE,IAAI,CAACrG,MAAM,CAACY,KAAK,CAACwF,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAAC,CAAC;YAC9C;YAAC;YAAApK,aAAA,GAAAC,CAAA;kBAE0B,EAAPuK,SAAA,GAAAH,OAAO;YAAA;YAAArK,aAAA,GAAAC,CAAA;;;;;;kBAAPwK,EAAA,GAAAD,SAAA,CAAArG,MAAO;cAAA;cAAAnE,aAAA,GAAAgC,CAAA;cAAAhC,aAAA,GAAAC,CAAA;cAAA;YAAA;YAAA;YAAA;cAAAD,aAAA,GAAAgC,CAAA;YAAA;YAAAhC,aAAA,GAAAC,CAAA;YAAhByK,KAAK,GAAAF,SAAA,CAAAC,EAAA;YAAA;YAAAzK,aAAA,GAAAC,CAAA;YACd,qBAAM4B,OAAO,CAAC0C,GAAG,CAACmG,KAAK,CAACrG,GAAG,CAAC,UAAAvC,KAAK;cAAA;cAAA9B,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAI,OAAAqE,KAAI,CAAC3C,kBAAkB,CAACG,KAAK,EAAE;gBAAES,YAAY,EAAE;cAAI,CAAE,CAAC;YAAtD,CAAsD,CAAC,CAAC;;;;;YAA7FU,EAAA,CAAAC,IAAA,EAA6F;YAAC;YAAAlD,aAAA,GAAAC,CAAA;;;;;;YAD5EwK,EAAA,EAAO;YAAA;YAAAzK,aAAA,GAAAC,CAAA;;;;;;;;;;GAG5B;EAAA;EAAAD,aAAA,GAAAC,CAAA;EAEKF,sBAAA,CAAAwB,SAAA,CAAAoJ,kBAAkB,GAAxB;IAAA;IAAA3K,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAC,CAAA;mCAA4B4B,OAAO;MAAA;MAAA7B,aAAA,GAAAE,CAAA;;;;;;;;QAC3B0K,OAAO,GAAG,IAAI,CAACvK,UAAU,CAACG,aAAa,GAAG,CAAC;QAAA;QAAA,CAAAR,aAAA,GAAAgC,CAAA,WAC7C,IAAI,CAAC3B,UAAU,CAACC,IAAI,GAAG,IAAI,CAACD,UAAU,CAACG,aAAa;QAAA;QAAA,CAAAR,aAAA,GAAAgC,CAAA,WACpD,CAAC;QAAC;QAAAhC,aAAA,GAAAC,CAAA;QAEA4K,QAAQ,GAAG,IAAI,CAACxK,UAAU,CAACG,aAAa,GAAG,CAAC;QAAA;QAAA,CAAAR,aAAA,GAAAgC,CAAA,WAC9C,IAAI,CAAC3B,UAAU,CAACE,MAAM,GAAG,IAAI,CAACF,UAAU,CAACG,aAAa;QAAA;QAAA,CAAAR,aAAA,GAAAgC,CAAA,WACtD,CAAC;QAAC;QAAAhC,aAAA,GAAAC,CAAA;QAEN,sBAAO;UACL2K,OAAO,EAAAA,OAAA;UACPC,QAAQ,EAAAA,QAAA;UACRrK,aAAa,EAAE,IAAI,CAACH,UAAU,CAACG,aAAa;UAC5CsK,SAAS,EAAE,IAAI,CAAC3K,KAAK,CAAC4K;SACvB;;;GACF;EAAA;EAAA/K,aAAA,GAAAC,CAAA;EAEKF,sBAAA,CAAAwB,SAAA,CAAAyJ,UAAU,GAAhB;IAAA;IAAAhL,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAC,CAAA;mCAAoB4B,OAAO;MAAA;MAAA7B,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAC,CAAA;;;;;QACzB,IAAI,CAACE,KAAK,CAAC8K,KAAK,EAAE;QAAC;QAAAjL,aAAA,GAAAC,CAAA;QACnB,IAAI,CAACI,UAAU,GAAG;UAChBC,IAAI,EAAE,CAAC;UACPC,MAAM,EAAE,CAAC;UACTC,aAAa,EAAE;SAChB;QAAC;QAAAR,aAAA,GAAAC,CAAA;;;;GACH;EAAA;EAAAD,aAAA,GAAAC,CAAA;EAEaF,sBAAA,CAAAwB,SAAA,CAAAwB,sBAAsB,GAApC,UAAqCjB,KAAa;IAAA;IAAA9B,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAC,CAAA;mCAAG4B,OAAO;MAAA;MAAA7B,aAAA,GAAAE,CAAA;;;;;;;;;;;;;;YAEtD;YAAA,CAAAF,aAAA,GAAAgC,CAAA,WAAAkJ,MAAM,CAACC,KAAK;YAAA;YAAA,CAAAnL,aAAA,GAAAgC,CAAA,WAAI,OAAOkJ,MAAM,CAACC,KAAK,KAAK,UAAU,IAAlD;cAAA;cAAAnL,aAAA,GAAAgC,CAAA;cAAAhC,aAAA,GAAAC,CAAA;cAAA;YAAA,CAAkD;YAAA;YAAA;cAAAD,aAAA,GAAAgC,CAAA;YAAA;YAAAhC,aAAA,GAAAC,CAAA;;;;;;;;;YAEjC,qBAAMiL,MAAM,CAACC,KAAK,CAAC,eAAA7I,MAAA,CAAeR,KAAK,CAAE,CAAC;;;;;YAArDsJ,QAAQ,GAAGnI,EAAA,CAAAC,IAAA,EAA0C;YAAA;YAAAlD,aAAA,GAAAC,CAAA;YAC3D,IAAI,CAACmL,QAAQ,CAACC,EAAE,EAAE;cAAA;cAAArL,aAAA,GAAAgC,CAAA;cAAAhC,aAAA,GAAAC,CAAA;cAChB,MAAM,IAAIiC,KAAK,CAAC,cAAAI,MAAA,CAAc8I,QAAQ,CAACE,MAAM,CAAE,CAAC;YAClD,CAAC;YAAA;YAAA;cAAAtL,aAAA,GAAAgC,CAAA;YAAA;YAAAhC,aAAA,GAAAC,CAAA;;;;;;;YAID;YAAA;YAAAD,aAAA,GAAAC,CAAA;YACA,MAAMsL,OAAK;;;;;YAIf;YACA,qBAAM,IAAI1J,OAAO,CAAC,UAAA2J,OAAO;cAAA;cAAAxL,aAAA,GAAAE,CAAA;cAAAF,aAAA,GAAAC,CAAA;cAAI,OAAAwL,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC;YAAxB,CAAwB,CAAC;;;;;YADtD;YACAvI,EAAA,CAAAC,IAAA,EAAsD;YAAC;YAAAlD,aAAA,GAAAC,CAAA;YAEjDyL,QAAQ,GAAG,IAAI,CAAChL,cAAc,CAACoB,KAAK,CAAC;YAAC;YAAA9B,aAAA,GAAAC,CAAA;YAC5C,IAAI,CAACyL,QAAQ,EAAE;cAAA;cAAA1L,aAAA,GAAAgC,CAAA;cAAAhC,aAAA,GAAAC,CAAA;cACb,sBAAO,IAAI,CAACqD,oBAAoB,CAACxB,KAAK,CAAC;YACzC,CAAC;YAAA;YAAA;cAAA9B,aAAA,GAAAgC,CAAA;YAAA;YAAAhC,aAAA,GAAAC,CAAA;YAED,sBAAO;cACL6B,KAAK,EAAAA,KAAA;cACLlB,MAAM;cAAE;cAAA,CAAAZ,aAAA,GAAAgC,CAAA,WAAA0J,QAAQ,CAAC9K,MAAM;cAAA;cAAA,CAAAZ,aAAA,GAAAgC,CAAA,WAAI,CAAC;cAC5BnB,MAAM;cAAE;cAAA,CAAAb,aAAA,GAAAgC,CAAA,WAAA0J,QAAQ,CAAC7K,MAAM;cAAA;cAAA,CAAAb,aAAA,GAAAgC,CAAA,WAAI,CAAC;cAC5BlB,aAAa;cAAE;cAAA,CAAAd,aAAA,GAAAgC,CAAA,WAAA0J,QAAQ,CAAC5K,aAAa;cAAA;cAAA,CAAAd,aAAA,GAAAgC,CAAA,WAAI,CAAC;cAC1CjB,MAAM;cAAE;cAAA,CAAAf,aAAA,GAAAgC,CAAA,WAAA0J,QAAQ,CAAC3K,MAAM;cAAA;cAAA,CAAAf,aAAA,GAAAgC,CAAA,WAAI,CAAC;cAC5BhB,UAAU;cAAE;cAAA,CAAAhB,aAAA,GAAAgC,CAAA,WAAA0J,QAAQ,CAAC1K,UAAU;cAAA;cAAA,CAAAhB,aAAA,GAAAgC,CAAA,WAAI,CAAC;cACpCf,WAAW;cAAE;cAAA,CAAAjB,aAAA,GAAAgC,CAAA,WAAA0J,QAAQ,CAACzK,WAAW;cAAA;cAAA,CAAAjB,aAAA,GAAAgC,CAAA,WAAI,EAAE;cACvCd,QAAQ;cAAE;cAAA,CAAAlB,aAAA,GAAAgC,CAAA,WAAA0J,QAAQ,CAACxK,QAAQ;cAAA;cAAA,CAAAlB,aAAA,GAAAgC,CAAA,WAAI,SAAS;cACxC2J,WAAW,EAAE,IAAIjJ,IAAI;aACtB;;;;GACF;EAAA;EAAA1C,aAAA,GAAAC,CAAA;EAEOF,sBAAA,CAAAwB,SAAA,CAAA+B,oBAAoB,GAA5B,UAA6BxB,KAAa;IAAA;IAAA9B,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAC,CAAA;IACxC,OAAO;MACL6B,KAAK,EAAAA,KAAA;MACLlB,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTC,aAAa,EAAE,CAAC;MAChBC,MAAM,EAAE,CAAC;MACTC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,SAAS;MACnByK,WAAW,EAAE,IAAIjJ,IAAI,EAAE;MACvBW,OAAO,EAAE;KACV;EACH,CAAC;EAAA;EAAArD,aAAA,GAAAC,CAAA;EAEOF,sBAAA,CAAAwB,SAAA,CAAAwE,mBAAmB,GAA3B,UAA4B6F,WAAqB,EAAEC,UAAkB;IAAA;IAAA7L,aAAA,GAAAE,CAAA;IACnE,IAAM4L,KAAK;IAAA;IAAA,CAAA9L,aAAA,GAAAC,CAAA,SAAI4L,UAAU,GAAG,GAAG,IAAKD,WAAW,CAACzH,MAAM,GAAG,CAAC,CAAC;IAC3D,IAAM4H,KAAK;IAAA;IAAA,CAAA/L,aAAA,GAAAC,CAAA,SAAGsG,IAAI,CAACyF,KAAK,CAACF,KAAK,CAAC;IAC/B,IAAMG,KAAK;IAAA;IAAA,CAAAjM,aAAA,GAAAC,CAAA,SAAGsG,IAAI,CAAC2F,IAAI,CAACJ,KAAK,CAAC;IAAC;IAAA9L,aAAA,GAAAC,CAAA;IAE/B,IAAI8L,KAAK,KAAKE,KAAK,EAAE;MAAA;MAAAjM,aAAA,GAAAgC,CAAA;MAAAhC,aAAA,GAAAC,CAAA;MACnB,OAAO2L,WAAW,CAACG,KAAK,CAAC;IAC3B,CAAC;IAAA;IAAA;MAAA/L,aAAA,GAAAgC,CAAA;IAAA;IAED,IAAMmK,MAAM;IAAA;IAAA,CAAAnM,aAAA,GAAAC,CAAA,SAAG6L,KAAK,GAAGC,KAAK;IAAC;IAAA/L,aAAA,GAAAC,CAAA;IAC7B,OAAO2L,WAAW,CAACG,KAAK,CAAC,IAAI,CAAC,GAAGI,MAAM,CAAC,GAAGP,WAAW,CAACK,KAAK,CAAC,GAAGE,MAAM;EACxE,CAAC;EAAA;EAAAnM,aAAA,GAAAC,CAAA;EAEOF,sBAAA,CAAAwB,SAAA,CAAAuF,sBAAsB,GAA9B,UAA+BH,QAAgB;IAAA;IAAA3G,aAAA,GAAAE,CAAA;IAC7C,IAAMsH,WAAW;IAAA;IAAA,CAAAxH,aAAA,GAAAC,CAAA,SAA6E;MAC5F,eAAe,EAAE;QAAEW,MAAM,EAAE,GAAG;QAAE+E,MAAM,EAAE,GAAG;QAAEuB,YAAY,EAAE;MAAG,CAAE;MAChE,UAAU,EAAE;QAAEtG,MAAM,EAAE,GAAG;QAAE+E,MAAM,EAAE,GAAG;QAAEuB,YAAY,EAAE;MAAG,CAAE;MAC3D,QAAQ,EAAE;QAAEtG,MAAM,EAAE,GAAG;QAAE+E,MAAM,EAAE,GAAG;QAAEuB,YAAY,EAAE;MAAG,CAAE;MACzD,QAAQ,EAAE;QAAEtG,MAAM,EAAE,GAAG;QAAE+E,MAAM,EAAE,GAAG;QAAEuB,YAAY,EAAE;MAAG;KACxD;IAAC;IAAAlH,aAAA,GAAAC,CAAA;IAEF,OAAO,2BAAAD,aAAA,GAAAgC,CAAA,WAAAwF,WAAW,CAACb,QAAQ,CAACvE,WAAW,EAAE,CAAC;IAAA;IAAA,CAAApC,aAAA,GAAAgC,CAAA,WAAI;MAAEpB,MAAM,EAAE,GAAG;MAAE+E,MAAM,EAAE,GAAG;MAAEuB,YAAY,EAAE;IAAG,CAAE;EAC/F,CAAC;EAAA;EAAAlH,aAAA,GAAAC,CAAA;EAEOF,sBAAA,CAAAwB,SAAA,CAAA6F,4BAA4B,GAApC,UAAqCtF,KAAa;IAAA;IAAA9B,aAAA,GAAAE,CAAA;IAChD;IACA,IAAMkM,YAAY;IAAA;IAAA,CAAApM,aAAA,GAAAC,CAAA,SAAG,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC;IAAC;IAAAD,aAAA,GAAAC,CAAA;IAC/E,OAAOmM,YAAY,CAACC,QAAQ,CAACvK,KAAK,CAACM,WAAW,EAAE,CAAC;IAAA;IAAA,CAAApC,aAAA,GAAAgC,CAAA,WAAG,EAAE;IAAA;IAAA,CAAAhC,aAAA,GAAAgC,CAAA,WAAG,EAAE;EAC7D,CAAC;EAAA;EAAAhC,aAAA,GAAAC,CAAA;EAEOF,sBAAA,CAAAwB,SAAA,CAAAuG,sBAAsB,GAA9B,UAA+BF,QAAgB;IAAA;IAAA5H,aAAA,GAAAE,CAAA;IAC7C,IAAMsH,WAAW;IAAA;IAAA,CAAAxH,aAAA,GAAAC,CAAA,SAAuE;MACtFqM,UAAU,EAAE;QAAE1L,MAAM,EAAE,GAAG;QAAE+E,MAAM,EAAE,GAAG;QAAE5E,MAAM,EAAE;MAAG,CAAE;MACrDwL,OAAO,EAAE;QAAE3L,MAAM,EAAE,GAAG;QAAE+E,MAAM,EAAE,GAAG;QAAE5E,MAAM,EAAE;MAAG,CAAE;MAClDyL,UAAU,EAAE;QAAE5L,MAAM,EAAE,GAAG;QAAE+E,MAAM,EAAE,GAAG;QAAE5E,MAAM,EAAE;MAAG,CAAE;MACrD0L,SAAS,EAAE;QAAE7L,MAAM,EAAE,GAAG;QAAE+E,MAAM,EAAE,GAAG;QAAE5E,MAAM,EAAE;MAAG,CAAE;MACpD2L,MAAM,EAAE;QAAE9L,MAAM,EAAE,GAAG;QAAE+E,MAAM,EAAE,GAAG;QAAE5E,MAAM,EAAE;MAAG;KAChD;IAAC;IAAAf,aAAA,GAAAC,CAAA;IAEF,OAAO,2BAAAD,aAAA,GAAAgC,CAAA,WAAAwF,WAAW,CAACI,QAAQ,CAACxF,WAAW,EAAE,CAAC;IAAA;IAAA,CAAApC,aAAA,GAAAgC,CAAA,WAAI;MAAEpB,MAAM,EAAE,GAAG;MAAE+E,MAAM,EAAE,GAAG;MAAE5E,MAAM,EAAE;IAAG,CAAE;EACzF,CAAC;EAAA;EAAAf,aAAA,GAAAC,CAAA;EAEOF,sBAAA,CAAAwB,SAAA,CAAA4G,0BAA0B,GAAlC,UAAmCP,QAAgB;IAAA;IAAA5H,aAAA,GAAAE,CAAA;IACjD,IAAMyM,SAAS;IAAA;IAAA,CAAA3M,aAAA,GAAAC,CAAA,SAA6B;MAC1CqM,UAAU,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;MAC9DC,OAAO,EAAE,CAAC,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,aAAa,CAAC;MAC1FC,UAAU,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,QAAQ,CAAC;MAC9EC,SAAS,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,UAAU,EAAE,cAAc,EAAE,OAAO,CAAC;MAC1EC,MAAM,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ;KAC/D;IAAC;IAAA1M,aAAA,GAAAC,CAAA;IAEF,OAAO,2BAAAD,aAAA,GAAAgC,CAAA,WAAA2K,SAAS,CAAC/E,QAAQ,CAACxF,WAAW,EAAE,CAAC;IAAA;IAAA,CAAApC,aAAA,GAAAgC,CAAA,WAAI,EAAE;EAChD,CAAC;EAAA;EAAAhC,aAAA,GAAAC,CAAA;EAEOF,sBAAA,CAAAwB,SAAA,CAAAyH,6BAA6B,GAArC,UAAsCC,UAAkB,EAAEC,eAAuB;IAAA;IAAAlJ,aAAA,GAAAE,CAAA;;IAC/E,IAAM0M,YAAY;IAAA;IAAA,CAAA5M,aAAA,GAAAC,CAAA,SAA6C;MAC7D,sBAAsB,EAAE;QACtB4M,QAAQ,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC;QAChDC,YAAY,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,CAAC;QAC5DC,QAAQ,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,eAAe;OAC1D;MACD,oBAAoB,EAAE;QACpBF,QAAQ,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,CAAC;QAChDC,YAAY,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC;QACxDC,QAAQ,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa;;KAE3D;IAED,IAAMC,gBAAgB;IAAA;IAAA,CAAAhN,aAAA,GAAAC,CAAA,SAAGgJ,UAAU,CAAC7G,WAAW,EAAE;IACjD,IAAM8B,MAAM;IAAA;IAAA,CAAAlE,aAAA,GAAAC,CAAA;IAAG;IAAA,CAAAD,aAAA,GAAAgC,CAAA;IAAA;IAAA,CAAAhC,aAAA,GAAAgC,CAAA,YAAAiB,EAAA,GAAA2J,YAAY,CAACI,gBAAgB,CAAC;IAAA;IAAA,CAAAhN,aAAA,GAAAgC,CAAA,WAAAiB,EAAA;IAAA;IAAA,CAAAjD,aAAA,GAAAgC,CAAA;IAAA;IAAA,CAAAhC,aAAA,GAAAgC,CAAA,WAAAiB,EAAA,CAAGiG,eAAe,CAAC;IAAA;IAAA,CAAAlJ,aAAA,GAAAgC,CAAA,WAAI,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC;IAAC;IAAAhC,aAAA,GAAAC,CAAA;IAEtG,OAAOiE,MAAM;EACf,CAAC;EAAA;EAAAlE,aAAA,GAAAC,CAAA;EAEOF,sBAAA,CAAAwB,SAAA,CAAAgI,sBAAsB,GAA9B,UAA+BF,SAA0B,EAAET,OAAoC;IAAA;IAAA5I,aAAA,GAAAE,CAAA;IAC7F,IAAM+M,YAAY;IAAA;IAAA,CAAAjN,aAAA,GAAAC,CAAA,SAAG,GAAG;IACxB,IAAMiN,YAAY;IAAA;IAAA,CAAAlN,aAAA,GAAAC,CAAA,SAAG,IAAI;IACzB,IAAMkN,YAAY;IAAA;IAAA,CAAAnN,aAAA,GAAAC,CAAA,SAAG,GAAG;IACxB,IAAMmN,SAAS;IAAA;IAAA,CAAApN,aAAA,GAAAC,CAAA,SAAG,IAAI;IACtB,IAAMoN,gBAAgB;IAAA;IAAA,CAAArN,aAAA,GAAAC,CAAA,SAAG,GAAG;IAE5B,IAAMsI,WAAW;IAAA;IAAA,CAAAvI,aAAA,GAAAC,CAAA,SAAGoJ,SAAS,CAACzI,MAAM;IACpC,IAAM6H,WAAW;IAAA;IAAA,CAAAzI,aAAA,GAAAC,CAAA,SAAGoJ,SAAS,CAACtI,MAAM;IACpC,IAAMyH,WAAW;IAAA;IAAA,CAAAxI,aAAA,GAAAC,CAAA,SAAGoJ,SAAS,CAACvI,aAAa,GAAG,IAAI,EAAC,CAAC;IACpD,IAAMwM,QAAQ;IAAA;IAAA,CAAAtN,aAAA,GAAAC,CAAA,SAAGsG,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE4C,SAAS,CAACzI,MAAM,GAAGyI,SAAS,CAACxI,MAAM,CAAC;IACjE,IAAM0M,eAAe;IAAA;IAAA,CAAAvN,aAAA,GAAAC,CAAA,SAAG,EAAE,GAAGoJ,SAAS,CAACrI,UAAU,EAAC,CAAC;IAAA;IAAAhB,aAAA,GAAAC,CAAA;IAEnD,OACEsI,WAAW,GAAG0E,YAAY,GAC1BxE,WAAW,GAAGyE,YAAY,GAC1B1E,WAAW,GAAG2E,YAAY,GAC1BG,QAAQ,GAAGF,SAAS,GACpBG,eAAe,GAAGF,gBAAgB;EAEtC,CAAC;EAAA;EAAArN,aAAA,GAAAC,CAAA;EAEOF,sBAAA,CAAAwB,SAAA,CAAAkI,+BAA+B,GAAvC,UAAwCJ,SAA0B,EAAET,OAAoC;IAAA;IAAA5I,aAAA,GAAAE,CAAA;IACtG,IAAMsN,OAAO;IAAA;IAAA,CAAAxN,aAAA,GAAAC,CAAA,SAAG,EAAE;IAAC;IAAAD,aAAA,GAAAC,CAAA;IAEnB,IAAIoJ,SAAS,CAACzI,MAAM,GAAG,EAAE,EAAE;MAAA;MAAAZ,aAAA,GAAAgC,CAAA;MAAAhC,aAAA,GAAAC,CAAA;MACzBuN,OAAO,CAACjD,IAAI,CAAC,oBAAoB,CAAC;IACpC,CAAC;IAAA;IAAA;MAAAvK,aAAA,GAAAgC,CAAA;IAAA;IAAAhC,aAAA,GAAAC,CAAA;IACD,IAAIoJ,SAAS,CAACtI,MAAM,GAAG,EAAE,EAAE;MAAA;MAAAf,aAAA,GAAAgC,CAAA;MAAAhC,aAAA,GAAAC,CAAA;MACzBuN,OAAO,CAACjD,IAAI,CAAC,yBAAyB,CAAC;IACzC,CAAC;IAAA;IAAA;MAAAvK,aAAA,GAAAgC,CAAA;IAAA;IAAAhC,aAAA,GAAAC,CAAA;IACD,IAAIoJ,SAAS,CAACvI,aAAa,GAAG,KAAK,EAAE;MAAA;MAAAd,aAAA,GAAAgC,CAAA;MAAAhC,aAAA,GAAAC,CAAA;MACnCuN,OAAO,CAACjD,IAAI,CAAC,4BAA4B,CAAC;IAC5C,CAAC;IAAA;IAAA;MAAAvK,aAAA,GAAAgC,CAAA;IAAA;IAAAhC,aAAA,GAAAC,CAAA;IACD,IAAIoJ,SAAS,CAACzI,MAAM,GAAGyI,SAAS,CAACxI,MAAM,EAAE;MAAA;MAAAb,aAAA,GAAAgC,CAAA;MAAAhC,aAAA,GAAAC,CAAA;MACvCuN,OAAO,CAACjD,IAAI,CAAC,wBAAwB,CAAC;IACxC,CAAC;IAAA;IAAA;MAAAvK,aAAA,GAAAgC,CAAA;IAAA;IAAAhC,aAAA,GAAAC,CAAA;IAED,OAAOuN,OAAO,CAACrJ,MAAM,GAAG,CAAC;IAAA;IAAA,CAAAnE,aAAA,GAAAgC,CAAA,WACrB,sBAAAM,MAAA,CAAsBkL,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC,CAAE;IAAA;IAAA,CAAAzN,aAAA,GAAAgC,CAAA,WAC1C,+CAA+C;EACrD,CAAC;EAAA;EAAAhC,aAAA,GAAAC,CAAA;EAEOF,sBAAA,CAAAwB,SAAA,CAAAsI,wBAAwB,GAAhC,UAAiC6D,UAA6B,EAAE/G,QAAgB;IAAA;IAAA3G,aAAA,GAAAE,CAAA;IAC9E,IAAMyN,kBAAkB;IAAA;IAAA,CAAA3N,aAAA,GAAAC,CAAA,SAAG,IAAI,CAAC6G,sBAAsB,CAACH,QAAQ,CAAC,CAAChB,MAAM;IACvE,IAAM7E,aAAa;IAAA;IAAA,CAAAd,aAAA,GAAAC,CAAA,SAAGyN,UAAU,CAACjI,MAAM,CAAC,UAACC,GAAG,EAAE5D,KAAK;MAAA;MAAA9B,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAC,CAAA;MAAK,OAAAyF,GAAG,GAAG5D,KAAK,CAAChB,aAAa;IAAzB,CAAyB,EAAE,CAAC,CAAC,GAAG4M,UAAU,CAACvJ,MAAM;IAAC;IAAAnE,aAAA,GAAAC,CAAA;IAE1G,OAAOa,aAAa,GAAG6M,kBAAkB;EAC3C,CAAC;EAAA;EAAA3N,aAAA,GAAAC,CAAA;EACH,OAAAF,sBAAC;AAAD,CAAC,CAviBD;AAuiBC;AAAAC,aAAA,GAAAC,CAAA;AAviBY2N,OAAA,CAAA7N,sBAAA,GAAAA,sBAAA", "ignoreList": []}