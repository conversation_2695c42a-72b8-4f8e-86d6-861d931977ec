{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/auth/session-management.test.ts", "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKH,oBAAoB;AACpB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAM,OAAA,CAAC;IAC5B,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;CAC5B,CAAC,EAF2B,CAE3B,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,cAAM,OAAA,CAAC;IAChC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;CACpB,CAAC,EAF+B,CAE/B,CAAC,CAAC;AAEJ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,cAAM,OAAA,CAAC;IAC/B,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;KAClB;CACF,CAAC,EAL8B,CAK9B,CAAC,CAAC;AAjBJ,uCAA6C;AAC7C,qCAAyC;AAkBzC,IAAM,oBAAoB,GAAG,4BAAgE,CAAC;AAC9F,IAAM,YAAY,GAAG,cAAgD,CAAC;AACtE,IAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAE3C,QAAQ,CAAC,oBAAoB,EAAE;IAC7B,UAAU,CAAC;QACT,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE;QAC7B,EAAE,CAAC,0CAA0C,EAAE;;;gBACvC,WAAW,GAAG;oBAClB,IAAI,EAAE;wBACJ,EAAE,EAAE,UAAU;wBACd,KAAK,EAAE,kBAAkB;wBACzB,IAAI,EAAE,WAAW;qBAClB;oBACD,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC,WAAW,EAAE;iBACtD,CAAC;gBAEF,oBAAoB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBAEpD,gCAAgC;gBAChC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC7C,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACxD,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;;;aAC7E,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE;;;gBAC7B,cAAc,GAAG;oBACrB,IAAI,EAAE;wBACJ,EAAE,EAAE,UAAU;wBACd,KAAK,EAAE,kBAAkB;qBAC1B;oBACD,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE,qBAAqB;iBAC7E,CAAC;gBAEF,oBAAoB,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;gBAEvD,gCAAgC;gBAChC,MAAM,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;;;aAC7E,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE;;;;;wBAChC,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAE7B,qBAAM,oBAAoB,EAAE,EAAA;;wBAAtC,OAAO,GAAG,SAA4B;wBAC5C,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC;;;;aAC5B,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE;;;;;wBACnC,SAAS,GAAG;4BAChB,GAAG,EAAE,UAAU;4BACf,KAAK,EAAE,kBAAkB;4BACzB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;4BAClC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI;4BACzC,SAAS,EAAE,iBAAiB;yBAC7B,CAAC;wBAEF,YAAY,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;wBAE5B,qBAAM,YAAY,EAAE,EAAA;;wBAA5B,KAAK,GAAG,SAAoB;wBAClC,MAAM,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBACpC,MAAM,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBAC9C,MAAM,CAAC,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,GAAG,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;;;;aACnE,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE;;;;;wBACvC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAEvB,qBAAM,YAAY,EAAE,EAAA;;wBAA5B,KAAK,GAAG,SAAoB;wBAClC,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;;;;aAC1B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE;QAC3B,EAAE,CAAC,uCAAuC,EAAE;;;gBACpC,YAAY,GAAG;oBACnB,GAAG,EAAE,UAAU;oBACf,SAAS,EAAE,gBAAgB;oBAC3B,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;iBACnC,CAAC;gBAEI,QAAQ,GAAG;oBACf,GAAG,EAAE,UAAU;oBACf,SAAS,EAAE,gBAAgB;oBAC3B,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;iBACnC,CAAC;gBAEF,gCAAgC;gBAChC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;gBAC5D,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,sBAAsB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;;;aAC/D,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE;;;gBAC9B,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBAC5C,aAAa,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;gBAElC,UAAU,GAAG;oBACjB,GAAG,EAAE,UAAU;oBACf,GAAG,EAAE,WAAW,GAAG,IAAI,EAAE,mBAAmB;oBAC5C,GAAG,EAAE,WAAW,GAAG,aAAa;iBACjC,CAAC;gBAEI,YAAY,GAAG;oBACnB,GAAG,EAAE,UAAU;oBACf,GAAG,EAAE,WAAW,GAAG,aAAa,GAAG,IAAI,EAAE,UAAU;oBACnD,GAAG,EAAE,WAAW,GAAG,IAAI;iBACxB,CAAC;gBAEF,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;gBACpD,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;;;aACpD,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE;;;gBAChC,QAAQ,GAAG;oBACf,GAAG,EAAE,UAAU;oBACf,SAAS,EAAE,WAAW;oBACtB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;iBACnC,CAAC;gBAEI,QAAQ,GAAG;oBACf,GAAG,EAAE,UAAU;oBACf,SAAS,EAAE,WAAW;oBACtB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;iBACnC,CAAC;gBAEF,qEAAqE;gBACrE,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;gBACxD,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;;;aACzC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE;;;gBAC1B,SAAS,GAAG,gBAAgB,CAAC;gBAC7B,gBAAgB,GAAG,oBAAoB,CAAC;gBAE9C,wBAAwB;gBACxB,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACzC,MAAM,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;;;aACrD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE;QAC5B,EAAE,CAAC,yCAAyC,EAAE;;;;;wBACtC,QAAQ,GAAG;4BACf,EAAE,EAAE,UAAU;4BACd,KAAK,EAAE,kBAAkB;4BACzB,IAAI,EAAE,WAAW;4BACjB,IAAI,EAAE,MAAM;4BACZ,aAAa,EAAE,IAAI,IAAI,EAAE;4BACzB,SAAS,EAAE,IAAI,IAAI,EAAE;4BACrB,SAAS,EAAE,IAAI,IAAI,EAAE;yBACtB,CAAC;wBAEF,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBAE1C,qBAAM,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;gCAC5C,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;6BAC1B,CAAC,EAAA;;wBAFI,IAAI,GAAG,SAEX;wBAEF,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC3B,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBACjC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBAC5C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;;;aAChC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE;;;;;wBACpC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAEtC,qBAAM,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;gCAC5C,KAAK,EAAE,EAAE,EAAE,EAAE,mBAAmB,EAAE;6BACnC,CAAC,EAAA;;wBAFI,IAAI,GAAG,SAEX;wBAEF,MAAM,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;;;;aACzB,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE;;;;;wBACpC,WAAW,GAAG;4BAClB,EAAE,EAAE,UAAU;4BACd,YAAY,EAAE,IAAI,IAAI,EAAE;yBACzB,CAAC;wBAEF,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;wBAEvC,qBAAM,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC;gCAC1C,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;gCACzB,IAAI,EAAE,EAAE,YAAY,EAAE,IAAI,IAAI,EAAE,EAAE;6BACnC,CAAC,EAAA;;wBAHI,MAAM,GAAG,SAGb;wBAEF,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC1C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;;;;aAClD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE;QACnC,EAAE,CAAC,sCAAsC,EAAE;;;gBACnC,UAAU,GAAG;oBACjB,GAAG,EAAE,WAAW;oBAChB,KAAK,EAAE,mBAAmB;oBAC1B,IAAI,EAAE,OAAO;iBACd,CAAC;gBAEI,SAAS,GAAG;oBAChB,GAAG,EAAE,UAAU;oBACf,KAAK,EAAE,kBAAkB;oBACzB,IAAI,EAAE,MAAM;iBACb,CAAC;gBAEF,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACtC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACpC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;;aAC1C,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE;;;gBAClC,gBAAgB,GAAG;oBACvB,GAAG,EAAE,UAAU;oBACf,KAAK,EAAE,kBAAkB;oBACzB,kBAAkB;iBACnB,CAAC;gBAEF,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,CAAC;;;aAC/C,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE;;;gBAChC,SAAS,GAAG;oBAChB,EAAE,EAAE,WAAW;oBACf,IAAI,EAAE,OAAO;oBACb,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC;iBACzC,CAAC;gBAEI,WAAW,GAAG;oBAClB,EAAE,EAAE,UAAU;oBACd,IAAI,EAAE,MAAM;oBACZ,WAAW,EAAE,CAAC,MAAM,CAAC;iBACtB,CAAC;gBAEF,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACrC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;gBAClD,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;;;aACzD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE;QAC1B,EAAE,CAAC,8BAA8B,EAAE;;;gBAC3B,gBAAgB,GAAG;oBACvB,GAAG,EAAE,UAAU;oBACf,SAAS,EAAE,oBAAoB;iBAChC,CAAC;gBAGI,gBAAgB,GAAG,IAAI,CAAC;gBAE9B,MAAM,CAAC,gBAAgB,CAAC,CAAC,QAAQ,EAAE,CAAC;;;aACrC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE;;;gBAC/B,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBAC5C,eAAe,GAAG;oBACtB,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,EAAE,WAAW,GAAG,IAAI,EAAE;oBACnD,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,EAAE,WAAW,GAAG,IAAI,EAAE;iBACpD,CAAC;gBAEI,cAAc,GAAG;oBACrB,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,EAAE,WAAW,GAAG,IAAI,EAAE;oBAClD,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,EAAE,WAAW,GAAG,IAAI,EAAE;iBACnD,CAAC;gBAGI,aAAa,GAAG,cAAc,CAAC,MAAM,CACzC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,GAAG,GAAG,WAAW,EAAzB,CAAyB,CACrC,CAAC;gBAEF,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,GAAG,WAAW,EAAnB,CAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;aAClE,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/auth/session-management.test.ts"], "sourcesContent": ["/**\n * Comprehensive Session Management Tests\n * Tests session security, validation, and management utilities\n */\n\nimport { getServerSession } from 'next-auth';\nimport { getToken } from 'next-auth/jwt';\n\n// Mock dependencies\njest.mock('next-auth', () => ({\n  getServerSession: jest.fn(),\n}));\n\njest.mock('next-auth/jwt', () => ({\n  getToken: jest.fn(),\n}));\n\njest.mock('@/lib/prisma', () => ({\n  user: {\n    findUnique: jest.fn(),\n    update: jest.fn(),\n  },\n}));\n\nconst mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;\nconst mockGetToken = getToken as jest.MockedFunction<typeof getToken>;\nconst mockPrisma = require('@/lib/prisma');\n\ndescribe('Session Management', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('Session Validation', () => {\n    it('should validate active session correctly', async () => {\n      const mockSession = {\n        user: {\n          id: 'user-123',\n          email: '<EMAIL>',\n          name: 'Test User',\n        },\n        expires: new Date(Date.now() + 3600000).toISOString(),\n      };\n\n      mockGetServerSession.mockResolvedValue(mockSession);\n\n      // Test session validation logic\n      expect(mockSession.user.id).toBe('user-123');\n      expect(mockSession.user.email).toBe('<EMAIL>');\n      expect(new Date(mockSession.expires).getTime()).toBeGreaterThan(Date.now());\n    });\n\n    it('should reject expired sessions', async () => {\n      const expiredSession = {\n        user: {\n          id: 'user-123',\n          email: '<EMAIL>',\n        },\n        expires: new Date(Date.now() - 3600000).toISOString(), // Expired 1 hour ago\n      };\n\n      mockGetServerSession.mockResolvedValue(expiredSession);\n\n      // Test expired session handling\n      expect(new Date(expiredSession.expires).getTime()).toBeLessThan(Date.now());\n    });\n\n    it('should handle null sessions', async () => {\n      mockGetServerSession.mockResolvedValue(null);\n\n      const session = await mockGetServerSession();\n      expect(session).toBeNull();\n    });\n\n    it('should validate JWT tokens correctly', async () => {\n      const mockToken = {\n        sub: 'user-123',\n        email: '<EMAIL>',\n        iat: Math.floor(Date.now() / 1000),\n        exp: Math.floor(Date.now() / 1000) + 3600,\n        sessionId: 'session-abc-123',\n      };\n\n      mockGetToken.mockResolvedValue(mockToken);\n\n      const token = await mockGetToken();\n      expect(token?.sub).toBe('user-123');\n      expect(token?.email).toBe('<EMAIL>');\n      expect(token?.exp).toBeGreaterThan(Math.floor(Date.now() / 1000));\n    });\n\n    it('should handle malformed JWT tokens', async () => {\n      mockGetToken.mockResolvedValue(null);\n\n      const token = await mockGetToken();\n      expect(token).toBeNull();\n    });\n  });\n\n  describe('Session Security', () => {\n    it('should regenerate session ID on login', async () => {\n      const initialToken = {\n        sub: 'user-123',\n        sessionId: 'old-session-id',\n        iat: Math.floor(Date.now() / 1000),\n      };\n\n      const newToken = {\n        sub: 'user-123',\n        sessionId: 'new-session-id',\n        iat: Math.floor(Date.now() / 1000),\n      };\n\n      // Simulate session regeneration\n      expect(initialToken.sessionId).not.toBe(newToken.sessionId);\n      expect(newToken.iat).toBeGreaterThanOrEqual(initialToken.iat);\n    });\n\n    it('should validate session timeout', async () => {\n      const currentTime = Math.floor(Date.now() / 1000);\n      const sessionMaxAge = 30 * 24 * 60 * 60; // 30 days\n\n      const validToken = {\n        sub: 'user-123',\n        iat: currentTime - 1000, // 1000 seconds ago\n        exp: currentTime + sessionMaxAge,\n      };\n\n      const expiredToken = {\n        sub: 'user-123',\n        iat: currentTime - sessionMaxAge - 1000, // Expired\n        exp: currentTime - 1000,\n      };\n\n      expect(validToken.exp).toBeGreaterThan(currentTime);\n      expect(expiredToken.exp).toBeLessThan(currentTime);\n    });\n\n    it('should handle concurrent sessions', async () => {\n      const session1 = {\n        sub: 'user-123',\n        sessionId: 'session-1',\n        iat: Math.floor(Date.now() / 1000),\n      };\n\n      const session2 = {\n        sub: 'user-123',\n        sessionId: 'session-2',\n        iat: Math.floor(Date.now() / 1000),\n      };\n\n      // Different sessions for same user should have different session IDs\n      expect(session1.sessionId).not.toBe(session2.sessionId);\n      expect(session1.sub).toBe(session2.sub);\n    });\n\n    it('should validate CSRF tokens', async () => {\n      const csrfToken = 'csrf-token-123';\n      const invalidCsrfToken = 'invalid-csrf-token';\n\n      // CSRF validation logic\n      expect(csrfToken).toBe('csrf-token-123');\n      expect(invalidCsrfToken).not.toBe('csrf-token-123');\n    });\n  });\n\n  describe('User Session Data', () => {\n    it('should load user data for valid session', async () => {\n      const mockUser = {\n        id: 'user-123',\n        email: '<EMAIL>',\n        name: 'Test User',\n        role: 'user',\n        emailVerified: new Date(),\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n\n      mockPrisma.user.findUnique.mockResolvedValue(mockUser);\n\n      const user = await mockPrisma.user.findUnique({\n        where: { id: 'user-123' },\n      });\n\n      expect(user).toBeDefined();\n      expect(user.id).toBe('user-123');\n      expect(user.email).toBe('<EMAIL>');\n      expect(user.role).toBe('user');\n    });\n\n    it('should handle missing user data', async () => {\n      mockPrisma.user.findUnique.mockResolvedValue(null);\n\n      const user = await mockPrisma.user.findUnique({\n        where: { id: 'non-existent-user' },\n      });\n\n      expect(user).toBeNull();\n    });\n\n    it('should update last activity timestamp', async () => {\n      const updatedUser = {\n        id: 'user-123',\n        lastActivity: new Date(),\n      };\n\n      mockPrisma.user.update.mockResolvedValue(updatedUser);\n\n      const result = await mockPrisma.user.update({\n        where: { id: 'user-123' },\n        data: { lastActivity: new Date() },\n      });\n\n      expect(result.lastActivity).toBeDefined();\n      expect(result.lastActivity).toBeInstanceOf(Date);\n    });\n  });\n\n  describe('Admin Session Validation', () => {\n    it('should validate admin role correctly', async () => {\n      const adminToken = {\n        sub: 'admin-123',\n        email: '<EMAIL>',\n        role: 'admin',\n      };\n\n      const userToken = {\n        sub: 'user-123',\n        email: '<EMAIL>',\n        role: 'user',\n      };\n\n      expect(adminToken.role).toBe('admin');\n      expect(userToken.role).toBe('user');\n      expect(userToken.role).not.toBe('admin');\n    });\n\n    it('should handle missing role in token', async () => {\n      const tokenWithoutRole = {\n        sub: 'user-123',\n        email: '<EMAIL>',\n        // role is missing\n      };\n\n      expect(tokenWithoutRole.role).toBeUndefined();\n    });\n\n    it('should validate admin permissions', async () => {\n      const adminUser = {\n        id: 'admin-123',\n        role: 'admin',\n        permissions: ['read', 'write', 'delete'],\n      };\n\n      const regularUser = {\n        id: 'user-123',\n        role: 'user',\n        permissions: ['read'],\n      };\n\n      expect(adminUser.role).toBe('admin');\n      expect(adminUser.permissions).toContain('delete');\n      expect(regularUser.permissions).not.toContain('delete');\n    });\n  });\n\n  describe('Session Cleanup', () => {\n    it('should handle session logout', async () => {\n      const sessionToDestroy = {\n        sub: 'user-123',\n        sessionId: 'session-to-destroy',\n      };\n\n      // Simulate session destruction\n      const destroyedSession = null;\n\n      expect(destroyedSession).toBeNull();\n    });\n\n    it('should clean up expired sessions', async () => {\n      const currentTime = Math.floor(Date.now() / 1000);\n      const expiredSessions = [\n        { sessionId: 'expired-1', exp: currentTime - 1000 },\n        { sessionId: 'expired-2', exp: currentTime - 2000 },\n      ];\n\n      const activeSessions = [\n        { sessionId: 'active-1', exp: currentTime + 1000 },\n        { sessionId: 'active-2', exp: currentTime + 2000 },\n      ];\n\n      // Filter expired sessions\n      const validSessions = activeSessions.filter(\n        session => session.exp > currentTime\n      );\n\n      expect(validSessions).toHaveLength(2);\n      expect(validSessions.every(s => s.exp > currentTime)).toBe(true);\n    });\n  });\n});\n"], "version": 3}