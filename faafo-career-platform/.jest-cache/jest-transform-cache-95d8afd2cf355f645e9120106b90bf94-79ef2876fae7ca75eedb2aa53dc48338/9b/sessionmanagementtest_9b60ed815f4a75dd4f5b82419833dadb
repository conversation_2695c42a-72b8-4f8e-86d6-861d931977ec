2492157b6e5942bf5747ccb07947257a
"use strict";
/**
 * Comprehensive Session Management Tests
 * Tests session security, validation, and management utilities
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
// Mock dependencies
jest.mock('next-auth', function () { return ({
    getServerSession: jest.fn(),
}); });
jest.mock('next-auth/jwt', function () { return ({
    getToken: jest.fn(),
}); });
jest.mock('@/lib/prisma', function () { return ({
    user: {
        findUnique: jest.fn(),
        update: jest.fn(),
    },
}); });
var next_auth_1 = require("next-auth");
var jwt_1 = require("next-auth/jwt");
var mockGetServerSession = next_auth_1.getServerSession;
var mockGetToken = jwt_1.getToken;
var mockPrisma = require('@/lib/prisma');
describe('Session Management', function () {
    beforeEach(function () {
        jest.clearAllMocks();
    });
    describe('Session Validation', function () {
        it('should validate active session correctly', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockSession;
            return __generator(this, function (_a) {
                mockSession = {
                    user: {
                        id: 'user-123',
                        email: '<EMAIL>',
                        name: 'Test User',
                    },
                    expires: new Date(Date.now() + 3600000).toISOString(),
                };
                mockGetServerSession.mockResolvedValue(mockSession);
                // Test session validation logic
                expect(mockSession.user.id).toBe('user-123');
                expect(mockSession.user.email).toBe('<EMAIL>');
                expect(new Date(mockSession.expires).getTime()).toBeGreaterThan(Date.now());
                return [2 /*return*/];
            });
        }); });
        it('should reject expired sessions', function () { return __awaiter(void 0, void 0, void 0, function () {
            var expiredSession;
            return __generator(this, function (_a) {
                expiredSession = {
                    user: {
                        id: 'user-123',
                        email: '<EMAIL>',
                    },
                    expires: new Date(Date.now() - 3600000).toISOString(), // Expired 1 hour ago
                };
                mockGetServerSession.mockResolvedValue(expiredSession);
                // Test expired session handling
                expect(new Date(expiredSession.expires).getTime()).toBeLessThan(Date.now());
                return [2 /*return*/];
            });
        }); });
        it('should handle null sessions', function () { return __awaiter(void 0, void 0, void 0, function () {
            var session;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockGetServerSession.mockResolvedValue(null);
                        return [4 /*yield*/, mockGetServerSession()];
                    case 1:
                        session = _a.sent();
                        expect(session).toBeNull();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should validate JWT tokens correctly', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockToken, token;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockToken = {
                            sub: 'user-123',
                            email: '<EMAIL>',
                            iat: Math.floor(Date.now() / 1000),
                            exp: Math.floor(Date.now() / 1000) + 3600,
                            sessionId: 'session-abc-123',
                        };
                        mockGetToken.mockResolvedValue(mockToken);
                        return [4 /*yield*/, mockGetToken()];
                    case 1:
                        token = _a.sent();
                        expect(token === null || token === void 0 ? void 0 : token.sub).toBe('user-123');
                        expect(token === null || token === void 0 ? void 0 : token.email).toBe('<EMAIL>');
                        expect(token === null || token === void 0 ? void 0 : token.exp).toBeGreaterThan(Math.floor(Date.now() / 1000));
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle malformed JWT tokens', function () { return __awaiter(void 0, void 0, void 0, function () {
            var token;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockGetToken.mockResolvedValue(null);
                        return [4 /*yield*/, mockGetToken()];
                    case 1:
                        token = _a.sent();
                        expect(token).toBeNull();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Session Security', function () {
        it('should regenerate session ID on login', function () { return __awaiter(void 0, void 0, void 0, function () {
            var initialToken, newToken;
            return __generator(this, function (_a) {
                initialToken = {
                    sub: 'user-123',
                    sessionId: 'old-session-id',
                    iat: Math.floor(Date.now() / 1000),
                };
                newToken = {
                    sub: 'user-123',
                    sessionId: 'new-session-id',
                    iat: Math.floor(Date.now() / 1000),
                };
                // Simulate session regeneration
                expect(initialToken.sessionId).not.toBe(newToken.sessionId);
                expect(newToken.iat).toBeGreaterThanOrEqual(initialToken.iat);
                return [2 /*return*/];
            });
        }); });
        it('should validate session timeout', function () { return __awaiter(void 0, void 0, void 0, function () {
            var currentTime, sessionMaxAge, validToken, expiredToken;
            return __generator(this, function (_a) {
                currentTime = Math.floor(Date.now() / 1000);
                sessionMaxAge = 30 * 24 * 60 * 60;
                validToken = {
                    sub: 'user-123',
                    iat: currentTime - 1000, // 1000 seconds ago
                    exp: currentTime + sessionMaxAge,
                };
                expiredToken = {
                    sub: 'user-123',
                    iat: currentTime - sessionMaxAge - 1000, // Expired
                    exp: currentTime - 1000,
                };
                expect(validToken.exp).toBeGreaterThan(currentTime);
                expect(expiredToken.exp).toBeLessThan(currentTime);
                return [2 /*return*/];
            });
        }); });
        it('should handle concurrent sessions', function () { return __awaiter(void 0, void 0, void 0, function () {
            var session1, session2;
            return __generator(this, function (_a) {
                session1 = {
                    sub: 'user-123',
                    sessionId: 'session-1',
                    iat: Math.floor(Date.now() / 1000),
                };
                session2 = {
                    sub: 'user-123',
                    sessionId: 'session-2',
                    iat: Math.floor(Date.now() / 1000),
                };
                // Different sessions for same user should have different session IDs
                expect(session1.sessionId).not.toBe(session2.sessionId);
                expect(session1.sub).toBe(session2.sub);
                return [2 /*return*/];
            });
        }); });
        it('should validate CSRF tokens', function () { return __awaiter(void 0, void 0, void 0, function () {
            var csrfToken, invalidCsrfToken;
            return __generator(this, function (_a) {
                csrfToken = 'csrf-token-123';
                invalidCsrfToken = 'invalid-csrf-token';
                // CSRF validation logic
                expect(csrfToken).toBe('csrf-token-123');
                expect(invalidCsrfToken).not.toBe('csrf-token-123');
                return [2 /*return*/];
            });
        }); });
    });
    describe('User Session Data', function () {
        it('should load user data for valid session', function () { return __awaiter(void 0, void 0, void 0, function () {
            var mockUser, user;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockUser = {
                            id: 'user-123',
                            email: '<EMAIL>',
                            name: 'Test User',
                            role: 'user',
                            emailVerified: new Date(),
                            createdAt: new Date(),
                            updatedAt: new Date(),
                        };
                        mockPrisma.user.findUnique.mockResolvedValue(mockUser);
                        return [4 /*yield*/, mockPrisma.user.findUnique({
                                where: { id: 'user-123' },
                            })];
                    case 1:
                        user = _a.sent();
                        expect(user).toBeDefined();
                        expect(user.id).toBe('user-123');
                        expect(user.email).toBe('<EMAIL>');
                        expect(user.role).toBe('user');
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle missing user data', function () { return __awaiter(void 0, void 0, void 0, function () {
            var user;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mockPrisma.user.findUnique.mockResolvedValue(null);
                        return [4 /*yield*/, mockPrisma.user.findUnique({
                                where: { id: 'non-existent-user' },
                            })];
                    case 1:
                        user = _a.sent();
                        expect(user).toBeNull();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should update last activity timestamp', function () { return __awaiter(void 0, void 0, void 0, function () {
            var updatedUser, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        updatedUser = {
                            id: 'user-123',
                            lastActivity: new Date(),
                        };
                        mockPrisma.user.update.mockResolvedValue(updatedUser);
                        return [4 /*yield*/, mockPrisma.user.update({
                                where: { id: 'user-123' },
                                data: { lastActivity: new Date() },
                            })];
                    case 1:
                        result = _a.sent();
                        expect(result.lastActivity).toBeDefined();
                        expect(result.lastActivity).toBeInstanceOf(Date);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Admin Session Validation', function () {
        it('should validate admin role correctly', function () { return __awaiter(void 0, void 0, void 0, function () {
            var adminToken, userToken;
            return __generator(this, function (_a) {
                adminToken = {
                    sub: 'admin-123',
                    email: '<EMAIL>',
                    role: 'admin',
                };
                userToken = {
                    sub: 'user-123',
                    email: '<EMAIL>',
                    role: 'user',
                };
                expect(adminToken.role).toBe('admin');
                expect(userToken.role).toBe('user');
                expect(userToken.role).not.toBe('admin');
                return [2 /*return*/];
            });
        }); });
        it('should handle missing role in token', function () { return __awaiter(void 0, void 0, void 0, function () {
            var tokenWithoutRole;
            return __generator(this, function (_a) {
                tokenWithoutRole = {
                    sub: 'user-123',
                    email: '<EMAIL>',
                    // role is missing
                };
                expect(tokenWithoutRole.role).toBeUndefined();
                return [2 /*return*/];
            });
        }); });
        it('should validate admin permissions', function () { return __awaiter(void 0, void 0, void 0, function () {
            var adminUser, regularUser;
            return __generator(this, function (_a) {
                adminUser = {
                    id: 'admin-123',
                    role: 'admin',
                    permissions: ['read', 'write', 'delete'],
                };
                regularUser = {
                    id: 'user-123',
                    role: 'user',
                    permissions: ['read'],
                };
                expect(adminUser.role).toBe('admin');
                expect(adminUser.permissions).toContain('delete');
                expect(regularUser.permissions).not.toContain('delete');
                return [2 /*return*/];
            });
        }); });
    });
    describe('Session Cleanup', function () {
        it('should handle session logout', function () { return __awaiter(void 0, void 0, void 0, function () {
            var sessionToDestroy, destroyedSession;
            return __generator(this, function (_a) {
                sessionToDestroy = {
                    sub: 'user-123',
                    sessionId: 'session-to-destroy',
                };
                destroyedSession = null;
                expect(destroyedSession).toBeNull();
                return [2 /*return*/];
            });
        }); });
        it('should clean up expired sessions', function () { return __awaiter(void 0, void 0, void 0, function () {
            var currentTime, expiredSessions, activeSessions, validSessions;
            return __generator(this, function (_a) {
                currentTime = Math.floor(Date.now() / 1000);
                expiredSessions = [
                    { sessionId: 'expired-1', exp: currentTime - 1000 },
                    { sessionId: 'expired-2', exp: currentTime - 2000 },
                ];
                activeSessions = [
                    { sessionId: 'active-1', exp: currentTime + 1000 },
                    { sessionId: 'active-2', exp: currentTime + 2000 },
                ];
                validSessions = activeSessions.filter(function (session) { return session.exp > currentTime; });
                expect(validSessions).toHaveLength(2);
                expect(validSessions.every(function (s) { return s.exp > currentTime; })).toBe(true);
                return [2 /*return*/];
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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