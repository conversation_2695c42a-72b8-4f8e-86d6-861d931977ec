75dec983407e65128626495ccecf08e6
"use strict";
/**
 * EdgeCaseHandler System Failures Tests
 * Focused on system failure scenarios and error handling
 * Optimized for fast execution
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EdgeCaseHandler_1 = require("@/lib/skills/EdgeCaseHandler");
// Shared mock factories for performance
var createMockServices = function () { return ({
    mockAssessmentEngine: {
        createAssessment: jest.fn(),
        generateQuestions: jest.fn(),
        submitResponse: jest.fn(),
        calculateResults: jest.fn(),
        getAssessment: jest.fn(),
        getAssessmentsByUser: jest.fn(),
    },
    mockMarketDataService: {
        getSkillMarketData: jest.fn(),
        getMultipleSkillsMarketData: jest.fn(),
        analyzeMarketTrends: jest.fn(),
        getSalaryInsights: jest.fn(),
        getLocationBasedMarketData: jest.fn(),
        getMarketBasedRecommendations: jest.fn(),
    },
    mockLearningPathService: {
        generateLearningPath: jest.fn(),
        updateProgress: jest.fn(),
        completeMilestone: jest.fn(),
    },
}); };
describe('EdgeCaseHandler - System Failures', function () {
    var edgeCaseHandler;
    var mocks;
    beforeEach(function () {
        mocks = createMockServices();
        edgeCaseHandler = new EdgeCaseHandler_1.EdgeCaseHandler(mocks.mockAssessmentEngine, mocks.mockMarketDataService, mocks.mockLearningPathService);
    });
    describe('Database Failures', function () {
        it('should handle database connection failures', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                        };
                        mocks.mockAssessmentEngine.createAssessment.mockImplementation(function () {
                            throw new Error('Database connection failed');
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Database connection failed');
                        expect(result.errorType).toBe('SYSTEM_ERROR');
                        expect(result.retryable).toBe(true);
                        expect(result.fallbackData).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
        it('should handle database timeout errors', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                        };
                        mocks.mockAssessmentEngine.createAssessment.mockImplementation(function () {
                            throw new Error('Query timeout');
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Query timeout');
                        expect(result.errorType).toBe('TIMEOUT_ERROR');
                        expect(result.retryable).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
    });
    describe('AI Service Failures', function () {
        it('should handle AI service timeouts', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
                            targetRole: 'Full Stack Developer',
                            timeframe: 6,
                            learningStyle: 'structured',
                            availability: 10,
                            budget: 500,
                        };
                        mocks.mockLearningPathService.generateLearningPath.mockImplementation(function () {
                            throw new Error('AI service timeout');
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleLearningPathGeneration(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('AI service timeout');
                        expect(result.errorType).toBe('TIMEOUT_ERROR');
                        expect(result.retryable).toBe(true);
                        expect(result.fallbackData).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
        it('should handle AI service rate limiting', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
                            targetRole: 'Full Stack Developer',
                            timeframe: 6,
                            learningStyle: 'structured',
                            availability: 10,
                            budget: 500,
                        };
                        mocks.mockLearningPathService.generateLearningPath.mockImplementation(function () {
                            throw new Error('Rate limit exceeded');
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleLearningPathGeneration(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Rate limit exceeded');
                        expect(result.errorType).toBe('AI_SERVICE_ERROR');
                        expect(result.retryAfter).toBeDefined();
                        expect(result.fallbackData).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
    });
    describe('Memory and Performance Limits', function () {
        it('should handle memory exhaustion gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillIds: Array(1000).fill('skill'), // Reduced from 100000 for performance
                            careerPathId: 'path-456',
                        };
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Values exceed maximum thresholds');
                        expect(result.errorType).toBe('VALIDATION_ERROR');
                        expect(result.suggestedOptimizations).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
        it('should handle processing timeouts', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            currentSkills: Array(100).fill({ skill: 'javascript', level: 5, confidence: 6 }), // Reduced from 1000
                            targetRole: 'Full Stack Developer',
                            timeframe: 6,
                            learningStyle: 'structured',
                            availability: 10,
                            budget: 500,
                        };
                        // Mock a timeout operation with shorter delay for testing
                        mocks.mockLearningPathService.generateLearningPath.mockImplementation(function () {
                            return new Promise(function (resolve) {
                                setTimeout(function () { return resolve({}); }, 6000); // 6 second delay
                            });
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleLearningPathGeneration(request, { timeout: 2000 })];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Processing timeout');
                        expect(result.errorType).toBe('TIMEOUT_ERROR');
                        expect(result.partialResults).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 8000);
    });
    describe('Monitoring and Alerting', function () {
        it('should log security incidents', function () { return __awaiter(void 0, void 0, void 0, function () {
            var maliciousRequest, logSpy;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        maliciousRequest = {
                            userId: "'; DROP TABLE users; --",
                            skillIds: ["'; DELETE FROM skills; --"],
                            careerPathId: "1' OR '1'='1",
                        };
                        logSpy = jest.spyOn(console, 'error').mockImplementation();
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(maliciousRequest)];
                    case 1:
                        _a.sent();
                        expect(logSpy).toHaveBeenCalledWith(expect.stringContaining('SECURITY_ALERT'), expect.objectContaining({
                            type: 'SQL_INJECTION_ATTEMPT',
                            userId: maliciousRequest.userId,
                            timestamp: expect.any(Date),
                        }));
                        logSpy.mockRestore();
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
        it('should track error patterns', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, i, errorStats;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                        };
                        mocks.mockAssessmentEngine.createAssessment.mockImplementation(function () {
                            throw new Error('Database connection failed');
                        });
                        i = 0;
                        _a.label = 1;
                    case 1:
                        if (!(i < 3)) return [3 /*break*/, 4];
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        i++;
                        return [3 /*break*/, 1];
                    case 4: return [4 /*yield*/, edgeCaseHandler.getErrorStatistics()];
                    case 5:
                        errorStats = _a.sent();
                        expect(errorStats.totalErrors).toBeGreaterThanOrEqual(3);
                        expect(errorStats.errorsByType.SYSTEM_ERROR).toBeGreaterThanOrEqual(3);
                        expect(errorStats.mostCommonError).toBe('Database connection failed');
                        return [2 /*return*/];
                }
            });
        }); }, 8000);
    });
    describe('Performance Optimization Tests', function () {
        it('should complete validation within performance threshold', function () { return __awaiter(void 0, void 0, void 0, function () {
            var startTime, request, duration;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        startTime = Date.now();
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript', 'react', 'node'],
                            careerPathId: 'path-456',
                        };
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        _a.sent();
                        duration = Date.now() - startTime;
                        expect(duration).toBeLessThan(3000); // Should complete within 3 seconds
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
        it('should handle multiple concurrent requests efficiently', function () { return __awaiter(void 0, void 0, void 0, function () {
            var startTime, request, promises, duration;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        startTime = Date.now();
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                        };
                        promises = Array(3).fill(null).map(function (_, index) {
                            return edgeCaseHandler.handleSkillAssessment(__assign(__assign({}, request), { userId: "user-".concat(index) }));
                        });
                        return [4 /*yield*/, Promise.all(promises)];
                    case 1:
                        _a.sent();
                        duration = Date.now() - startTime;
                        expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
                        return [2 /*return*/];
                }
            });
        }); }, 8000);
        it('should maintain memory efficiency during processing', function () { return __awaiter(void 0, void 0, void 0, function () {
            var initialMemory, request, i, finalMemory, memoryIncrease;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        initialMemory = process.memoryUsage().heapUsed;
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                        };
                        i = 0;
                        _a.label = 1;
                    case 1:
                        if (!(i < 5)) return [3 /*break*/, 4];
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(__assign(__assign({}, request), { userId: "user-".concat(i) }))];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        i++;
                        return [3 /*break*/, 1];
                    case 4:
                        finalMemory = process.memoryUsage().heapUsed;
                        memoryIncrease = finalMemory - initialMemory;
                        // Memory increase should be reasonable (less than 50MB)
                        expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
                        return [2 /*return*/];
                }
            });
        }); }, 10000);
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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