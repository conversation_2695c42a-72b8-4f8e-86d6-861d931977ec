5f8dbcd59a2f50dbfbd9d6918aae4cdf
"use strict";

/**
 * Cache Invalidation Service
 * Handles intelligent cache invalidation based on data changes
 */
/* istanbul ignore next */
function cov_1zzdcwt1y4() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/cache-invalidation-service.ts";
  var hash = "e20713c601ab7e4b4a64879ca56fe12d25778966";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/cache-invalidation-service.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 16
        },
        end: {
          line: 14,
          column: 1
        }
      },
      "1": {
        start: {
          line: 7,
          column: 28
        },
        end: {
          line: 7,
          column: 110
        }
      },
      "2": {
        start: {
          line: 7,
          column: 91
        },
        end: {
          line: 7,
          column: 106
        }
      },
      "3": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 13,
          column: 7
        }
      },
      "4": {
        start: {
          line: 9,
          column: 36
        },
        end: {
          line: 9,
          column: 97
        }
      },
      "5": {
        start: {
          line: 9,
          column: 42
        },
        end: {
          line: 9,
          column: 70
        }
      },
      "6": {
        start: {
          line: 9,
          column: 85
        },
        end: {
          line: 9,
          column: 95
        }
      },
      "7": {
        start: {
          line: 10,
          column: 35
        },
        end: {
          line: 10,
          column: 100
        }
      },
      "8": {
        start: {
          line: 10,
          column: 41
        },
        end: {
          line: 10,
          column: 73
        }
      },
      "9": {
        start: {
          line: 10,
          column: 88
        },
        end: {
          line: 10,
          column: 98
        }
      },
      "10": {
        start: {
          line: 11,
          column: 32
        },
        end: {
          line: 11,
          column: 116
        }
      },
      "11": {
        start: {
          line: 12,
          column: 8
        },
        end: {
          line: 12,
          column: 78
        }
      },
      "12": {
        start: {
          line: 15,
          column: 18
        },
        end: {
          line: 41,
          column: 1
        }
      },
      "13": {
        start: {
          line: 16,
          column: 12
        },
        end: {
          line: 16,
          column: 104
        }
      },
      "14": {
        start: {
          line: 16,
          column: 43
        },
        end: {
          line: 16,
          column: 68
        }
      },
      "15": {
        start: {
          line: 16,
          column: 57
        },
        end: {
          line: 16,
          column: 68
        }
      },
      "16": {
        start: {
          line: 16,
          column: 69
        },
        end: {
          line: 16,
          column: 81
        }
      },
      "17": {
        start: {
          line: 16,
          column: 119
        },
        end: {
          line: 16,
          column: 196
        }
      },
      "18": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 160
        }
      },
      "19": {
        start: {
          line: 17,
          column: 141
        },
        end: {
          line: 17,
          column: 153
        }
      },
      "20": {
        start: {
          line: 18,
          column: 23
        },
        end: {
          line: 18,
          column: 68
        }
      },
      "21": {
        start: {
          line: 18,
          column: 45
        },
        end: {
          line: 18,
          column: 65
        }
      },
      "22": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "23": {
        start: {
          line: 20,
          column: 15
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "24": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 38,
          column: 66
        }
      },
      "25": {
        start: {
          line: 21,
          column: 50
        },
        end: {
          line: 38,
          column: 66
        }
      },
      "26": {
        start: {
          line: 22,
          column: 12
        },
        end: {
          line: 22,
          column: 169
        }
      },
      "27": {
        start: {
          line: 22,
          column: 160
        },
        end: {
          line: 22,
          column: 169
        }
      },
      "28": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 52
        }
      },
      "29": {
        start: {
          line: 23,
          column: 26
        },
        end: {
          line: 23,
          column: 52
        }
      },
      "30": {
        start: {
          line: 24,
          column: 12
        },
        end: {
          line: 36,
          column: 13
        }
      },
      "31": {
        start: {
          line: 25,
          column: 32
        },
        end: {
          line: 25,
          column: 39
        }
      },
      "32": {
        start: {
          line: 25,
          column: 40
        },
        end: {
          line: 25,
          column: 46
        }
      },
      "33": {
        start: {
          line: 26,
          column: 24
        },
        end: {
          line: 26,
          column: 34
        }
      },
      "34": {
        start: {
          line: 26,
          column: 35
        },
        end: {
          line: 26,
          column: 72
        }
      },
      "35": {
        start: {
          line: 27,
          column: 24
        },
        end: {
          line: 27,
          column: 34
        }
      },
      "36": {
        start: {
          line: 27,
          column: 35
        },
        end: {
          line: 27,
          column: 45
        }
      },
      "37": {
        start: {
          line: 27,
          column: 46
        },
        end: {
          line: 27,
          column: 55
        }
      },
      "38": {
        start: {
          line: 27,
          column: 56
        },
        end: {
          line: 27,
          column: 65
        }
      },
      "39": {
        start: {
          line: 28,
          column: 24
        },
        end: {
          line: 28,
          column: 41
        }
      },
      "40": {
        start: {
          line: 28,
          column: 42
        },
        end: {
          line: 28,
          column: 55
        }
      },
      "41": {
        start: {
          line: 28,
          column: 56
        },
        end: {
          line: 28,
          column: 65
        }
      },
      "42": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 128
        }
      },
      "43": {
        start: {
          line: 30,
          column: 110
        },
        end: {
          line: 30,
          column: 116
        }
      },
      "44": {
        start: {
          line: 30,
          column: 117
        },
        end: {
          line: 30,
          column: 126
        }
      },
      "45": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 106
        }
      },
      "46": {
        start: {
          line: 31,
          column: 81
        },
        end: {
          line: 31,
          column: 97
        }
      },
      "47": {
        start: {
          line: 31,
          column: 98
        },
        end: {
          line: 31,
          column: 104
        }
      },
      "48": {
        start: {
          line: 32,
          column: 20
        },
        end: {
          line: 32,
          column: 89
        }
      },
      "49": {
        start: {
          line: 32,
          column: 57
        },
        end: {
          line: 32,
          column: 72
        }
      },
      "50": {
        start: {
          line: 32,
          column: 73
        },
        end: {
          line: 32,
          column: 80
        }
      },
      "51": {
        start: {
          line: 32,
          column: 81
        },
        end: {
          line: 32,
          column: 87
        }
      },
      "52": {
        start: {
          line: 33,
          column: 20
        },
        end: {
          line: 33,
          column: 87
        }
      },
      "53": {
        start: {
          line: 33,
          column: 47
        },
        end: {
          line: 33,
          column: 62
        }
      },
      "54": {
        start: {
          line: 33,
          column: 63
        },
        end: {
          line: 33,
          column: 78
        }
      },
      "55": {
        start: {
          line: 33,
          column: 79
        },
        end: {
          line: 33,
          column: 85
        }
      },
      "56": {
        start: {
          line: 34,
          column: 20
        },
        end: {
          line: 34,
          column: 42
        }
      },
      "57": {
        start: {
          line: 34,
          column: 30
        },
        end: {
          line: 34,
          column: 42
        }
      },
      "58": {
        start: {
          line: 35,
          column: 20
        },
        end: {
          line: 35,
          column: 33
        }
      },
      "59": {
        start: {
          line: 35,
          column: 34
        },
        end: {
          line: 35,
          column: 43
        }
      },
      "60": {
        start: {
          line: 37,
          column: 12
        },
        end: {
          line: 37,
          column: 39
        }
      },
      "61": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 38,
          column: 34
        }
      },
      "62": {
        start: {
          line: 38,
          column: 35
        },
        end: {
          line: 38,
          column: 41
        }
      },
      "63": {
        start: {
          line: 38,
          column: 54
        },
        end: {
          line: 38,
          column: 64
        }
      },
      "64": {
        start: {
          line: 39,
          column: 8
        },
        end: {
          line: 39,
          column: 35
        }
      },
      "65": {
        start: {
          line: 39,
          column: 23
        },
        end: {
          line: 39,
          column: 35
        }
      },
      "66": {
        start: {
          line: 39,
          column: 36
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "67": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 50,
          column: 1
        }
      },
      "68": {
        start: {
          line: 43,
          column: 4
        },
        end: {
          line: 48,
          column: 5
        }
      },
      "69": {
        start: {
          line: 43,
          column: 40
        },
        end: {
          line: 48,
          column: 5
        }
      },
      "70": {
        start: {
          line: 43,
          column: 53
        },
        end: {
          line: 43,
          column: 54
        }
      },
      "71": {
        start: {
          line: 43,
          column: 60
        },
        end: {
          line: 43,
          column: 71
        }
      },
      "72": {
        start: {
          line: 44,
          column: 8
        },
        end: {
          line: 47,
          column: 9
        }
      },
      "73": {
        start: {
          line: 45,
          column: 12
        },
        end: {
          line: 45,
          column: 65
        }
      },
      "74": {
        start: {
          line: 45,
          column: 21
        },
        end: {
          line: 45,
          column: 65
        }
      },
      "75": {
        start: {
          line: 46,
          column: 12
        },
        end: {
          line: 46,
          column: 28
        }
      },
      "76": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 49,
          column: 61
        }
      },
      "77": {
        start: {
          line: 51,
          column: 0
        },
        end: {
          line: 51,
          column: 62
        }
      },
      "78": {
        start: {
          line: 52,
          column: 0
        },
        end: {
          line: 52,
          column: 77
        }
      },
      "79": {
        start: {
          line: 53,
          column: 35
        },
        end: {
          line: 53,
          column: 74
        }
      },
      "80": {
        start: {
          line: 57,
          column: 46
        },
        end: {
          line: 406,
          column: 3
        }
      },
      "81": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 59,
          column: 104
        }
      },
      "82": {
        start: {
          line: 60,
          column: 8
        },
        end: {
          line: 60,
          column: 43
        }
      },
      "83": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 61,
          column: 38
        }
      },
      "84": {
        start: {
          line: 66,
          column: 4
        },
        end: {
          line: 103,
          column: 6
        }
      },
      "85": {
        start: {
          line: 68,
          column: 8
        },
        end: {
          line: 77,
          column: 11
        }
      },
      "86": {
        start: {
          line: 79,
          column: 8
        },
        end: {
          line: 88,
          column: 11
        }
      },
      "87": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 95,
          column: 11
        }
      },
      "88": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 102,
          column: 11
        }
      },
      "89": {
        start: {
          line: 107,
          column: 4
        },
        end: {
          line: 110,
          column: 6
        }
      },
      "90": {
        start: {
          line: 108,
          column: 28
        },
        end: {
          line: 108,
          column: 71
        }
      },
      "91": {
        start: {
          line: 109,
          column: 8
        },
        end: {
          line: 109,
          column: 114
        }
      },
      "92": {
        start: {
          line: 114,
          column: 4
        },
        end: {
          line: 145,
          column: 6
        }
      },
      "93": {
        start: {
          line: 115,
          column: 8
        },
        end: {
          line: 144,
          column: 11
        }
      },
      "94": {
        start: {
          line: 117,
          column: 12
        },
        end: {
          line: 143,
          column: 15
        }
      },
      "95": {
        start: {
          line: 118,
          column: 16
        },
        end: {
          line: 142,
          column: 17
        }
      },
      "96": {
        start: {
          line: 120,
          column: 24
        },
        end: {
          line: 120,
          column: 71
        }
      },
      "97": {
        start: {
          line: 121,
          column: 24
        },
        end: {
          line: 123,
          column: 25
        }
      },
      "98": {
        start: {
          line: 122,
          column: 28
        },
        end: {
          line: 122,
          column: 50
        }
      },
      "99": {
        start: {
          line: 124,
          column: 24
        },
        end: {
          line: 124,
          column: 53
        }
      },
      "100": {
        start: {
          line: 125,
          column: 24
        },
        end: {
          line: 135,
          column: 25
        }
      },
      "101": {
        start: {
          line: 126,
          column: 28
        },
        end: {
          line: 126,
          column: 47
        }
      },
      "102": {
        start: {
          line: 127,
          column: 28
        },
        end: {
          line: 127,
          column: 76
        }
      },
      "103": {
        start: {
          line: 128,
          column: 28
        },
        end: {
          line: 128,
          column: 100
        }
      },
      "104": {
        start: {
          line: 128,
          column: 63
        },
        end: {
          line: 128,
          column: 96
        }
      },
      "105": {
        start: {
          line: 130,
          column: 28
        },
        end: {
          line: 134,
          column: 29
        }
      },
      "106": {
        start: {
          line: 131,
          column: 32
        },
        end: {
          line: 131,
          column: 52
        }
      },
      "107": {
        start: {
          line: 132,
          column: 32
        },
        end: {
          line: 132,
          column: 96
        }
      },
      "108": {
        start: {
          line: 133,
          column: 32
        },
        end: {
          line: 133,
          column: 109
        }
      },
      "109": {
        start: {
          line: 133,
          column: 72
        },
        end: {
          line: 133,
          column: 105
        }
      },
      "110": {
        start: {
          line: 137,
          column: 24
        },
        end: {
          line: 137,
          column: 96
        }
      },
      "111": {
        start: {
          line: 140,
          column: 24
        },
        end: {
          line: 140,
          column: 34
        }
      },
      "112": {
        start: {
          line: 141,
          column: 24
        },
        end: {
          line: 141,
          column: 46
        }
      },
      "113": {
        start: {
          line: 149,
          column: 4
        },
        end: {
          line: 178,
          column: 6
        }
      },
      "114": {
        start: {
          line: 150,
          column: 19
        },
        end: {
          line: 150,
          column: 21
        }
      },
      "115": {
        start: {
          line: 151,
          column: 8
        },
        end: {
          line: 176,
          column: 9
        }
      },
      "116": {
        start: {
          line: 153,
          column: 12
        },
        end: {
          line: 155,
          column: 13
        }
      },
      "117": {
        start: {
          line: 154,
          column: 16
        },
        end: {
          line: 154,
          column: 70
        }
      },
      "118": {
        start: {
          line: 161,
          column: 12
        },
        end: {
          line: 175,
          column: 13
        }
      },
      "119": {
        start: {
          line: 162,
          column: 37
        },
        end: {
          line: 169,
          column: 17
        }
      },
      "120": {
        start: {
          line: 170,
          column: 16
        },
        end: {
          line: 174,
          column: 20
        }
      },
      "121": {
        start: {
          line: 171,
          column: 20
        },
        end: {
          line: 173,
          column: 47
        }
      },
      "122": {
        start: {
          line: 177,
          column: 8
        },
        end: {
          line: 177,
          column: 20
        }
      },
      "123": {
        start: {
          line: 182,
          column: 4
        },
        end: {
          line: 222,
          column: 6
        }
      },
      "124": {
        start: {
          line: 183,
          column: 19
        },
        end: {
          line: 183,
          column: 21
        }
      },
      "125": {
        start: {
          line: 184,
          column: 8
        },
        end: {
          line: 220,
          column: 9
        }
      },
      "126": {
        start: {
          line: 185,
          column: 12
        },
        end: {
          line: 219,
          column: 13
        }
      },
      "127": {
        start: {
          line: 187,
          column: 20
        },
        end: {
          line: 187,
          column: 67
        }
      },
      "128": {
        start: {
          line: 188,
          column: 20
        },
        end: {
          line: 188,
          column: 26
        }
      },
      "129": {
        start: {
          line: 190,
          column: 20
        },
        end: {
          line: 190,
          column: 74
        }
      },
      "130": {
        start: {
          line: 191,
          column: 20
        },
        end: {
          line: 191,
          column: 26
        }
      },
      "131": {
        start: {
          line: 193,
          column: 20
        },
        end: {
          line: 193,
          column: 76
        }
      },
      "132": {
        start: {
          line: 194,
          column: 20
        },
        end: {
          line: 194,
          column: 26
        }
      },
      "133": {
        start: {
          line: 196,
          column: 20
        },
        end: {
          line: 196,
          column: 80
        }
      },
      "134": {
        start: {
          line: 197,
          column: 20
        },
        end: {
          line: 197,
          column: 26
        }
      },
      "135": {
        start: {
          line: 199,
          column: 20
        },
        end: {
          line: 199,
          column: 70
        }
      },
      "136": {
        start: {
          line: 200,
          column: 20
        },
        end: {
          line: 200,
          column: 26
        }
      },
      "137": {
        start: {
          line: 202,
          column: 20
        },
        end: {
          line: 202,
          column: 67
        }
      },
      "138": {
        start: {
          line: 203,
          column: 20
        },
        end: {
          line: 203,
          column: 26
        }
      },
      "139": {
        start: {
          line: 205,
          column: 20
        },
        end: {
          line: 205,
          column: 64
        }
      },
      "140": {
        start: {
          line: 206,
          column: 20
        },
        end: {
          line: 206,
          column: 26
        }
      },
      "141": {
        start: {
          line: 208,
          column: 20
        },
        end: {
          line: 208,
          column: 65
        }
      },
      "142": {
        start: {
          line: 209,
          column: 20
        },
        end: {
          line: 209,
          column: 26
        }
      },
      "143": {
        start: {
          line: 211,
          column: 20
        },
        end: {
          line: 211,
          column: 72
        }
      },
      "144": {
        start: {
          line: 212,
          column: 20
        },
        end: {
          line: 212,
          column: 26
        }
      },
      "145": {
        start: {
          line: 214,
          column: 20
        },
        end: {
          line: 214,
          column: 69
        }
      },
      "146": {
        start: {
          line: 215,
          column: 20
        },
        end: {
          line: 215,
          column: 26
        }
      },
      "147": {
        start: {
          line: 218,
          column: 20
        },
        end: {
          line: 218,
          column: 79
        }
      },
      "148": {
        start: {
          line: 221,
          column: 8
        },
        end: {
          line: 221,
          column: 20
        }
      },
      "149": {
        start: {
          line: 226,
          column: 4
        },
        end: {
          line: 241,
          column: 6
        }
      },
      "150": {
        start: {
          line: 227,
          column: 8
        },
        end: {
          line: 240,
          column: 11
        }
      },
      "151": {
        start: {
          line: 229,
          column: 24
        },
        end: {
          line: 229,
          column: 28
        }
      },
      "152": {
        start: {
          line: 230,
          column: 12
        },
        end: {
          line: 239,
          column: 15
        }
      },
      "153": {
        start: {
          line: 231,
          column: 16
        },
        end: {
          line: 238,
          column: 17
        }
      },
      "154": {
        start: {
          line: 233,
          column: 24
        },
        end: {
          line: 233,
          column: 103
        }
      },
      "155": {
        start: {
          line: 233,
          column: 61
        },
        end: {
          line: 233,
          column: 99
        }
      },
      "156": {
        start: {
          line: 234,
          column: 24
        },
        end: {
          line: 234,
          column: 68
        }
      },
      "157": {
        start: {
          line: 236,
          column: 24
        },
        end: {
          line: 236,
          column: 34
        }
      },
      "158": {
        start: {
          line: 237,
          column: 24
        },
        end: {
          line: 237,
          column: 46
        }
      },
      "159": {
        start: {
          line: 245,
          column: 4
        },
        end: {
          line: 259,
          column: 6
        }
      },
      "160": {
        start: {
          line: 246,
          column: 8
        },
        end: {
          line: 258,
          column: 11
        }
      },
      "161": {
        start: {
          line: 247,
          column: 12
        },
        end: {
          line: 257,
          column: 15
        }
      },
      "162": {
        start: {
          line: 248,
          column: 16
        },
        end: {
          line: 256,
          column: 17
        }
      },
      "163": {
        start: {
          line: 249,
          column: 28
        },
        end: {
          line: 252,
          column: 28
        }
      },
      "164": {
        start: {
          line: 254,
          column: 24
        },
        end: {
          line: 254,
          column: 34
        }
      },
      "165": {
        start: {
          line: 255,
          column: 24
        },
        end: {
          line: 255,
          column: 46
        }
      },
      "166": {
        start: {
          line: 263,
          column: 4
        },
        end: {
          line: 279,
          column: 6
        }
      },
      "167": {
        start: {
          line: 264,
          column: 8
        },
        end: {
          line: 278,
          column: 11
        }
      },
      "168": {
        start: {
          line: 265,
          column: 12
        },
        end: {
          line: 277,
          column: 15
        }
      },
      "169": {
        start: {
          line: 266,
          column: 16
        },
        end: {
          line: 276,
          column: 17
        }
      },
      "170": {
        start: {
          line: 267,
          column: 28
        },
        end: {
          line: 272,
          column: 28
        }
      },
      "171": {
        start: {
          line: 274,
          column: 24
        },
        end: {
          line: 274,
          column: 34
        }
      },
      "172": {
        start: {
          line: 275,
          column: 24
        },
        end: {
          line: 275,
          column: 46
        }
      },
      "173": {
        start: {
          line: 283,
          column: 4
        },
        end: {
          line: 297,
          column: 6
        }
      },
      "174": {
        start: {
          line: 284,
          column: 8
        },
        end: {
          line: 296,
          column: 11
        }
      },
      "175": {
        start: {
          line: 285,
          column: 12
        },
        end: {
          line: 295,
          column: 15
        }
      },
      "176": {
        start: {
          line: 286,
          column: 16
        },
        end: {
          line: 294,
          column: 17
        }
      },
      "177": {
        start: {
          line: 287,
          column: 28
        },
        end: {
          line: 290,
          column: 28
        }
      },
      "178": {
        start: {
          line: 292,
          column: 24
        },
        end: {
          line: 292,
          column: 34
        }
      },
      "179": {
        start: {
          line: 293,
          column: 24
        },
        end: {
          line: 293,
          column: 46
        }
      },
      "180": {
        start: {
          line: 301,
          column: 4
        },
        end: {
          line: 317,
          column: 6
        }
      },
      "181": {
        start: {
          line: 302,
          column: 8
        },
        end: {
          line: 316,
          column: 11
        }
      },
      "182": {
        start: {
          line: 303,
          column: 12
        },
        end: {
          line: 315,
          column: 15
        }
      },
      "183": {
        start: {
          line: 304,
          column: 16
        },
        end: {
          line: 314,
          column: 17
        }
      },
      "184": {
        start: {
          line: 305,
          column: 28
        },
        end: {
          line: 310,
          column: 28
        }
      },
      "185": {
        start: {
          line: 312,
          column: 24
        },
        end: {
          line: 312,
          column: 34
        }
      },
      "186": {
        start: {
          line: 313,
          column: 24
        },
        end: {
          line: 313,
          column: 46
        }
      },
      "187": {
        start: {
          line: 321,
          column: 4
        },
        end: {
          line: 356,
          column: 6
        }
      },
      "188": {
        start: {
          line: 322,
          column: 8
        },
        end: {
          line: 355,
          column: 11
        }
      },
      "189": {
        start: {
          line: 324,
          column: 12
        },
        end: {
          line: 354,
          column: 15
        }
      },
      "190": {
        start: {
          line: 325,
          column: 16
        },
        end: {
          line: 353,
          column: 17
        }
      },
      "191": {
        start: {
          line: 327,
          column: 24
        },
        end: {
          line: 335,
          column: 26
        }
      },
      "192": {
        start: {
          line: 336,
          column: 24
        },
        end: {
          line: 348,
          column: 26
        }
      },
      "193": {
        start: {
          line: 349,
          column: 24
        },
        end: {
          line: 349,
          column: 84
        }
      },
      "194": {
        start: {
          line: 351,
          column: 24
        },
        end: {
          line: 351,
          column: 34
        }
      },
      "195": {
        start: {
          line: 352,
          column: 24
        },
        end: {
          line: 352,
          column: 46
        }
      },
      "196": {
        start: {
          line: 360,
          column: 4
        },
        end: {
          line: 404,
          column: 6
        }
      },
      "197": {
        start: {
          line: 361,
          column: 8
        },
        end: {
          line: 403,
          column: 11
        }
      },
      "198": {
        start: {
          line: 363,
          column: 12
        },
        end: {
          line: 402,
          column: 15
        }
      },
      "199": {
        start: {
          line: 364,
          column: 16
        },
        end: {
          line: 401,
          column: 17
        }
      },
      "200": {
        start: {
          line: 366,
          column: 24
        },
        end: {
          line: 366,
          column: 123
        }
      },
      "201": {
        start: {
          line: 367,
          column: 24
        },
        end: {
          line: 368,
          column: 50
        }
      },
      "202": {
        start: {
          line: 368,
          column: 28
        },
        end: {
          line: 368,
          column: 50
        }
      },
      "203": {
        start: {
          line: 369,
          column: 24
        },
        end: {
          line: 369,
          column: 35
        }
      },
      "204": {
        start: {
          line: 370,
          column: 24
        },
        end: {
          line: 375,
          column: 25
        }
      },
      "205": {
        start: {
          line: 371,
          column: 41
        },
        end: {
          line: 371,
          column: 65
        }
      },
      "206": {
        start: {
          line: 372,
          column: 44
        },
        end: {
          line: 372,
          column: 68
        }
      },
      "207": {
        start: {
          line: 373,
          column: 52
        },
        end: {
          line: 373,
          column: 76
        }
      },
      "208": {
        start: {
          line: 374,
          column: 53
        },
        end: {
          line: 374,
          column: 77
        }
      },
      "209": {
        start: {
          line: 376,
          column: 24
        },
        end: {
          line: 376,
          column: 48
        }
      },
      "210": {
        start: {
          line: 377,
          column: 28
        },
        end: {
          line: 377,
          column: 84
        }
      },
      "211": {
        start: {
          line: 379,
          column: 24
        },
        end: {
          line: 379,
          column: 34
        }
      },
      "212": {
        start: {
          line: 380,
          column: 24
        },
        end: {
          line: 380,
          column: 49
        }
      },
      "213": {
        start: {
          line: 381,
          column: 28
        },
        end: {
          line: 381,
          column: 87
        }
      },
      "214": {
        start: {
          line: 383,
          column: 24
        },
        end: {
          line: 383,
          column: 34
        }
      },
      "215": {
        start: {
          line: 384,
          column: 24
        },
        end: {
          line: 384,
          column: 49
        }
      },
      "216": {
        start: {
          line: 385,
          column: 28
        },
        end: {
          line: 385,
          column: 143
        }
      },
      "217": {
        start: {
          line: 387,
          column: 24
        },
        end: {
          line: 387,
          column: 34
        }
      },
      "218": {
        start: {
          line: 388,
          column: 24
        },
        end: {
          line: 388,
          column: 49
        }
      },
      "219": {
        start: {
          line: 389,
          column: 28
        },
        end: {
          line: 389,
          column: 141
        }
      },
      "220": {
        start: {
          line: 391,
          column: 24
        },
        end: {
          line: 391,
          column: 34
        }
      },
      "221": {
        start: {
          line: 392,
          column: 24
        },
        end: {
          line: 392,
          column: 49
        }
      },
      "222": {
        start: {
          line: 395,
          column: 20
        },
        end: {
          line: 395,
          column: 76
        }
      },
      "223": {
        start: {
          line: 398,
          column: 24
        },
        end: {
          line: 398,
          column: 34
        }
      },
      "224": {
        start: {
          line: 399,
          column: 24
        },
        end: {
          line: 399,
          column: 38
        }
      },
      "225": {
        start: {
          line: 400,
          column: 29
        },
        end: {
          line: 400,
          column: 51
        }
      },
      "226": {
        start: {
          line: 405,
          column: 4
        },
        end: {
          line: 405,
          column: 36
        }
      },
      "227": {
        start: {
          line: 407,
          column: 0
        },
        end: {
          line: 407,
          column: 60
        }
      },
      "228": {
        start: {
          line: 409,
          column: 0
        },
        end: {
          line: 409,
          column: 66
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 44
          },
          end: {
            line: 6,
            column: 45
          }
        },
        loc: {
          start: {
            line: 6,
            column: 89
          },
          end: {
            line: 14,
            column: 1
          }
        },
        line: 6
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 7,
            column: 13
          },
          end: {
            line: 7,
            column: 18
          }
        },
        loc: {
          start: {
            line: 7,
            column: 26
          },
          end: {
            line: 7,
            column: 112
          }
        },
        line: 7
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 71
          }
        },
        loc: {
          start: {
            line: 7,
            column: 89
          },
          end: {
            line: 7,
            column: 108
          }
        },
        line: 7
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 8,
            column: 36
          },
          end: {
            line: 8,
            column: 37
          }
        },
        loc: {
          start: {
            line: 8,
            column: 63
          },
          end: {
            line: 13,
            column: 5
          }
        },
        line: 8
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 9,
            column: 17
          },
          end: {
            line: 9,
            column: 26
          }
        },
        loc: {
          start: {
            line: 9,
            column: 34
          },
          end: {
            line: 9,
            column: 99
          }
        },
        line: 9
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 10,
            column: 17
          },
          end: {
            line: 10,
            column: 25
          }
        },
        loc: {
          start: {
            line: 10,
            column: 33
          },
          end: {
            line: 10,
            column: 102
          }
        },
        line: 10
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 11,
            column: 17
          },
          end: {
            line: 11,
            column: 21
          }
        },
        loc: {
          start: {
            line: 11,
            column: 30
          },
          end: {
            line: 11,
            column: 118
          }
        },
        line: 11
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 15,
            column: 48
          },
          end: {
            line: 15,
            column: 49
          }
        },
        loc: {
          start: {
            line: 15,
            column: 73
          },
          end: {
            line: 41,
            column: 1
          }
        },
        line: 15
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 16,
            column: 30
          },
          end: {
            line: 16,
            column: 31
          }
        },
        loc: {
          start: {
            line: 16,
            column: 41
          },
          end: {
            line: 16,
            column: 83
          }
        },
        line: 16
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 17,
            column: 128
          },
          end: {
            line: 17,
            column: 129
          }
        },
        loc: {
          start: {
            line: 17,
            column: 139
          },
          end: {
            line: 17,
            column: 155
          }
        },
        line: 17
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 18,
            column: 13
          },
          end: {
            line: 18,
            column: 17
          }
        },
        loc: {
          start: {
            line: 18,
            column: 21
          },
          end: {
            line: 18,
            column: 70
          }
        },
        line: 18
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 31
          }
        },
        loc: {
          start: {
            line: 18,
            column: 43
          },
          end: {
            line: 18,
            column: 67
          }
        },
        line: 18
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 19,
            column: 13
          },
          end: {
            line: 19,
            column: 17
          }
        },
        loc: {
          start: {
            line: 19,
            column: 22
          },
          end: {
            line: 40,
            column: 5
          }
        },
        line: 19
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 42,
            column: 52
          },
          end: {
            line: 42,
            column: 53
          }
        },
        loc: {
          start: {
            line: 42,
            column: 78
          },
          end: {
            line: 50,
            column: 1
          }
        },
        line: 42
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 57,
            column: 46
          },
          end: {
            line: 57,
            column: 47
          }
        },
        loc: {
          start: {
            line: 57,
            column: 58
          },
          end: {
            line: 406,
            column: 1
          }
        },
        line: 57
      },
      "15": {
        name: "CacheInvalidationService",
        decl: {
          start: {
            line: 58,
            column: 13
          },
          end: {
            line: 58,
            column: 37
          }
        },
        loc: {
          start: {
            line: 58,
            column: 52
          },
          end: {
            line: 62,
            column: 5
          }
        },
        line: 58
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 66,
            column: 64
          },
          end: {
            line: 66,
            column: 65
          }
        },
        loc: {
          start: {
            line: 66,
            column: 76
          },
          end: {
            line: 103,
            column: 5
          }
        },
        line: 66
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 107,
            column: 49
          },
          end: {
            line: 107,
            column: 50
          }
        },
        loc: {
          start: {
            line: 107,
            column: 77
          },
          end: {
            line: 110,
            column: 5
          }
        },
        line: 107
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 114,
            column: 59
          },
          end: {
            line: 114,
            column: 60
          }
        },
        loc: {
          start: {
            line: 114,
            column: 76
          },
          end: {
            line: 145,
            column: 5
          }
        },
        line: 114
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 115,
            column: 48
          },
          end: {
            line: 115,
            column: 49
          }
        },
        loc: {
          start: {
            line: 115,
            column: 60
          },
          end: {
            line: 144,
            column: 9
          }
        },
        line: 115
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 117,
            column: 37
          },
          end: {
            line: 117,
            column: 38
          }
        },
        loc: {
          start: {
            line: 117,
            column: 51
          },
          end: {
            line: 143,
            column: 13
          }
        },
        line: 117
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 128,
            column: 46
          },
          end: {
            line: 128,
            column: 47
          }
        },
        loc: {
          start: {
            line: 128,
            column: 61
          },
          end: {
            line: 128,
            column: 98
          }
        },
        line: 128
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 133,
            column: 55
          },
          end: {
            line: 133,
            column: 56
          }
        },
        loc: {
          start: {
            line: 133,
            column: 70
          },
          end: {
            line: 133,
            column: 107
          }
        },
        line: 133
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 149,
            column: 59
          },
          end: {
            line: 149,
            column: 60
          }
        },
        loc: {
          start: {
            line: 149,
            column: 82
          },
          end: {
            line: 178,
            column: 5
          }
        },
        line: 149
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 170,
            column: 60
          },
          end: {
            line: 170,
            column: 61
          }
        },
        loc: {
          start: {
            line: 170,
            column: 75
          },
          end: {
            line: 174,
            column: 17
          }
        },
        line: 170
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 182,
            column: 64
          },
          end: {
            line: 182,
            column: 65
          }
        },
        loc: {
          start: {
            line: 182,
            column: 93
          },
          end: {
            line: 222,
            column: 5
          }
        },
        line: 182
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 226,
            column: 56
          },
          end: {
            line: 226,
            column: 57
          }
        },
        loc: {
          start: {
            line: 226,
            column: 72
          },
          end: {
            line: 241,
            column: 5
          }
        },
        line: 226
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 227,
            column: 48
          },
          end: {
            line: 227,
            column: 49
          }
        },
        loc: {
          start: {
            line: 227,
            column: 60
          },
          end: {
            line: 240,
            column: 9
          }
        },
        line: 227
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 230,
            column: 37
          },
          end: {
            line: 230,
            column: 38
          }
        },
        loc: {
          start: {
            line: 230,
            column: 51
          },
          end: {
            line: 239,
            column: 13
          }
        },
        line: 230
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 233,
            column: 44
          },
          end: {
            line: 233,
            column: 45
          }
        },
        loc: {
          start: {
            line: 233,
            column: 59
          },
          end: {
            line: 233,
            column: 101
          }
        },
        line: 233
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 245,
            column: 62
          },
          end: {
            line: 245,
            column: 63
          }
        },
        loc: {
          start: {
            line: 245,
            column: 80
          },
          end: {
            line: 259,
            column: 5
          }
        },
        line: 245
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 246,
            column: 48
          },
          end: {
            line: 246,
            column: 49
          }
        },
        loc: {
          start: {
            line: 246,
            column: 60
          },
          end: {
            line: 258,
            column: 9
          }
        },
        line: 246
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 247,
            column: 37
          },
          end: {
            line: 247,
            column: 38
          }
        },
        loc: {
          start: {
            line: 247,
            column: 51
          },
          end: {
            line: 257,
            column: 13
          }
        },
        line: 247
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 263,
            column: 63
          },
          end: {
            line: 263,
            column: 64
          }
        },
        loc: {
          start: {
            line: 263,
            column: 90
          },
          end: {
            line: 279,
            column: 5
          }
        },
        line: 263
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 264,
            column: 48
          },
          end: {
            line: 264,
            column: 49
          }
        },
        loc: {
          start: {
            line: 264,
            column: 60
          },
          end: {
            line: 278,
            column: 9
          }
        },
        line: 264
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 265,
            column: 37
          },
          end: {
            line: 265,
            column: 38
          }
        },
        loc: {
          start: {
            line: 265,
            column: 51
          },
          end: {
            line: 277,
            column: 13
          }
        },
        line: 265
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 283,
            column: 65
          },
          end: {
            line: 283,
            column: 66
          }
        },
        loc: {
          start: {
            line: 283,
            column: 83
          },
          end: {
            line: 297,
            column: 5
          }
        },
        line: 283
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 284,
            column: 48
          },
          end: {
            line: 284,
            column: 49
          }
        },
        loc: {
          start: {
            line: 284,
            column: 60
          },
          end: {
            line: 296,
            column: 9
          }
        },
        line: 284
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 285,
            column: 37
          },
          end: {
            line: 285,
            column: 38
          }
        },
        loc: {
          start: {
            line: 285,
            column: 51
          },
          end: {
            line: 295,
            column: 13
          }
        },
        line: 285
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 301,
            column: 66
          },
          end: {
            line: 301,
            column: 67
          }
        },
        loc: {
          start: {
            line: 301,
            column: 96
          },
          end: {
            line: 317,
            column: 5
          }
        },
        line: 301
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 302,
            column: 48
          },
          end: {
            line: 302,
            column: 49
          }
        },
        loc: {
          start: {
            line: 302,
            column: 60
          },
          end: {
            line: 316,
            column: 9
          }
        },
        line: 302
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 303,
            column: 37
          },
          end: {
            line: 303,
            column: 38
          }
        },
        loc: {
          start: {
            line: 303,
            column: 51
          },
          end: {
            line: 315,
            column: 13
          }
        },
        line: 303
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 321,
            column: 65
          },
          end: {
            line: 321,
            column: 66
          }
        },
        loc: {
          start: {
            line: 321,
            column: 83
          },
          end: {
            line: 356,
            column: 5
          }
        },
        line: 321
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 322,
            column: 48
          },
          end: {
            line: 322,
            column: 49
          }
        },
        loc: {
          start: {
            line: 322,
            column: 60
          },
          end: {
            line: 355,
            column: 9
          }
        },
        line: 322
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 324,
            column: 37
          },
          end: {
            line: 324,
            column: 38
          }
        },
        loc: {
          start: {
            line: 324,
            column: 51
          },
          end: {
            line: 354,
            column: 13
          }
        },
        line: 324
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 360,
            column: 57
          },
          end: {
            line: 360,
            column: 58
          }
        },
        loc: {
          start: {
            line: 360,
            column: 76
          },
          end: {
            line: 404,
            column: 5
          }
        },
        line: 360
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 361,
            column: 48
          },
          end: {
            line: 361,
            column: 49
          }
        },
        loc: {
          start: {
            line: 361,
            column: 60
          },
          end: {
            line: 403,
            column: 9
          }
        },
        line: 361
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 363,
            column: 37
          },
          end: {
            line: 363,
            column: 38
          }
        },
        loc: {
          start: {
            line: 363,
            column: 51
          },
          end: {
            line: 402,
            column: 13
          }
        },
        line: 363
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 16
          },
          end: {
            line: 14,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 21
          }
        }, {
          start: {
            line: 6,
            column: 25
          },
          end: {
            line: 6,
            column: 39
          }
        }, {
          start: {
            line: 6,
            column: 44
          },
          end: {
            line: 14,
            column: 1
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 7,
            column: 35
          },
          end: {
            line: 7,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 56
          },
          end: {
            line: 7,
            column: 61
          }
        }, {
          start: {
            line: 7,
            column: 64
          },
          end: {
            line: 7,
            column: 109
          }
        }],
        line: 7
      },
      "2": {
        loc: {
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 17
          }
        }, {
          start: {
            line: 8,
            column: 22
          },
          end: {
            line: 8,
            column: 33
          }
        }],
        line: 8
      },
      "3": {
        loc: {
          start: {
            line: 11,
            column: 32
          },
          end: {
            line: 11,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 11,
            column: 46
          },
          end: {
            line: 11,
            column: 67
          }
        }, {
          start: {
            line: 11,
            column: 70
          },
          end: {
            line: 11,
            column: 115
          }
        }],
        line: 11
      },
      "4": {
        loc: {
          start: {
            line: 12,
            column: 51
          },
          end: {
            line: 12,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 12,
            column: 51
          },
          end: {
            line: 12,
            column: 61
          }
        }, {
          start: {
            line: 12,
            column: 65
          },
          end: {
            line: 12,
            column: 67
          }
        }],
        line: 12
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 18
          },
          end: {
            line: 41,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 19
          },
          end: {
            line: 15,
            column: 23
          }
        }, {
          start: {
            line: 15,
            column: 27
          },
          end: {
            line: 15,
            column: 43
          }
        }, {
          start: {
            line: 15,
            column: 48
          },
          end: {
            line: 41,
            column: 1
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 16,
            column: 43
          },
          end: {
            line: 16,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 43
          },
          end: {
            line: 16,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "7": {
        loc: {
          start: {
            line: 16,
            column: 134
          },
          end: {
            line: 16,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 16,
            column: 167
          },
          end: {
            line: 16,
            column: 175
          }
        }, {
          start: {
            line: 16,
            column: 178
          },
          end: {
            line: 16,
            column: 184
          }
        }],
        line: 16
      },
      "8": {
        loc: {
          start: {
            line: 17,
            column: 74
          },
          end: {
            line: 17,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 74
          },
          end: {
            line: 17,
            column: 102
          }
        }, {
          start: {
            line: 17,
            column: 107
          },
          end: {
            line: 17,
            column: 155
          }
        }],
        line: 17
      },
      "9": {
        loc: {
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 20
      },
      "10": {
        loc: {
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 21,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 21,
            column: 16
          }
        }, {
          start: {
            line: 21,
            column: 21
          },
          end: {
            line: 21,
            column: 44
          }
        }],
        line: 21
      },
      "11": {
        loc: {
          start: {
            line: 21,
            column: 28
          },
          end: {
            line: 21,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 28
          },
          end: {
            line: 21,
            column: 33
          }
        }, {
          start: {
            line: 21,
            column: 38
          },
          end: {
            line: 21,
            column: 43
          }
        }],
        line: 21
      },
      "12": {
        loc: {
          start: {
            line: 22,
            column: 12
          },
          end: {
            line: 22,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 12
          },
          end: {
            line: 22,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "13": {
        loc: {
          start: {
            line: 22,
            column: 23
          },
          end: {
            line: 22,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 23
          },
          end: {
            line: 22,
            column: 24
          }
        }, {
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 125
          }
        }, {
          start: {
            line: 22,
            column: 130
          },
          end: {
            line: 22,
            column: 158
          }
        }],
        line: 22
      },
      "14": {
        loc: {
          start: {
            line: 22,
            column: 33
          },
          end: {
            line: 22,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 45
          },
          end: {
            line: 22,
            column: 56
          }
        }, {
          start: {
            line: 22,
            column: 59
          },
          end: {
            line: 22,
            column: 125
          }
        }],
        line: 22
      },
      "15": {
        loc: {
          start: {
            line: 22,
            column: 59
          },
          end: {
            line: 22,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 116
          }
        }, {
          start: {
            line: 22,
            column: 119
          },
          end: {
            line: 22,
            column: 125
          }
        }],
        line: 22
      },
      "16": {
        loc: {
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 77
          }
        }, {
          start: {
            line: 22,
            column: 82
          },
          end: {
            line: 22,
            column: 115
          }
        }],
        line: 22
      },
      "17": {
        loc: {
          start: {
            line: 22,
            column: 82
          },
          end: {
            line: 22,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 83
          },
          end: {
            line: 22,
            column: 98
          }
        }, {
          start: {
            line: 22,
            column: 103
          },
          end: {
            line: 22,
            column: 112
          }
        }],
        line: 22
      },
      "18": {
        loc: {
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 23,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 23,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "19": {
        loc: {
          start: {
            line: 24,
            column: 12
          },
          end: {
            line: 36,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 25,
            column: 23
          }
        }, {
          start: {
            line: 25,
            column: 24
          },
          end: {
            line: 25,
            column: 46
          }
        }, {
          start: {
            line: 26,
            column: 16
          },
          end: {
            line: 26,
            column: 72
          }
        }, {
          start: {
            line: 27,
            column: 16
          },
          end: {
            line: 27,
            column: 65
          }
        }, {
          start: {
            line: 28,
            column: 16
          },
          end: {
            line: 28,
            column: 65
          }
        }, {
          start: {
            line: 29,
            column: 16
          },
          end: {
            line: 35,
            column: 43
          }
        }],
        line: 24
      },
      "20": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 74
          }
        }, {
          start: {
            line: 30,
            column: 79
          },
          end: {
            line: 30,
            column: 90
          }
        }, {
          start: {
            line: 30,
            column: 94
          },
          end: {
            line: 30,
            column: 105
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 30,
            column: 42
          },
          end: {
            line: 30,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 42
          },
          end: {
            line: 30,
            column: 54
          }
        }, {
          start: {
            line: 30,
            column: 58
          },
          end: {
            line: 30,
            column: 73
          }
        }],
        line: 30
      },
      "23": {
        loc: {
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "24": {
        loc: {
          start: {
            line: 31,
            column: 24
          },
          end: {
            line: 31,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 31,
            column: 24
          },
          end: {
            line: 31,
            column: 35
          }
        }, {
          start: {
            line: 31,
            column: 40
          },
          end: {
            line: 31,
            column: 42
          }
        }, {
          start: {
            line: 31,
            column: 47
          },
          end: {
            line: 31,
            column: 59
          }
        }, {
          start: {
            line: 31,
            column: 63
          },
          end: {
            line: 31,
            column: 75
          }
        }],
        line: 31
      },
      "25": {
        loc: {
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "26": {
        loc: {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 35
          }
        }, {
          start: {
            line: 32,
            column: 39
          },
          end: {
            line: 32,
            column: 53
          }
        }],
        line: 32
      },
      "27": {
        loc: {
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "28": {
        loc: {
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 25
          }
        }, {
          start: {
            line: 33,
            column: 29
          },
          end: {
            line: 33,
            column: 43
          }
        }],
        line: 33
      },
      "29": {
        loc: {
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "30": {
        loc: {
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "31": {
        loc: {
          start: {
            line: 39,
            column: 52
          },
          end: {
            line: 39,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 60
          },
          end: {
            line: 39,
            column: 65
          }
        }, {
          start: {
            line: 39,
            column: 68
          },
          end: {
            line: 39,
            column: 74
          }
        }],
        line: 39
      },
      "32": {
        loc: {
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 50,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 21
          },
          end: {
            line: 42,
            column: 25
          }
        }, {
          start: {
            line: 42,
            column: 29
          },
          end: {
            line: 42,
            column: 47
          }
        }, {
          start: {
            line: 42,
            column: 52
          },
          end: {
            line: 50,
            column: 1
          }
        }],
        line: 42
      },
      "33": {
        loc: {
          start: {
            line: 43,
            column: 4
          },
          end: {
            line: 48,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 4
          },
          end: {
            line: 48,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "34": {
        loc: {
          start: {
            line: 43,
            column: 8
          },
          end: {
            line: 43,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 43,
            column: 8
          },
          end: {
            line: 43,
            column: 12
          }
        }, {
          start: {
            line: 43,
            column: 16
          },
          end: {
            line: 43,
            column: 38
          }
        }],
        line: 43
      },
      "35": {
        loc: {
          start: {
            line: 44,
            column: 8
          },
          end: {
            line: 47,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 8
          },
          end: {
            line: 47,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "36": {
        loc: {
          start: {
            line: 44,
            column: 12
          },
          end: {
            line: 44,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 44,
            column: 12
          },
          end: {
            line: 44,
            column: 14
          }
        }, {
          start: {
            line: 44,
            column: 18
          },
          end: {
            line: 44,
            column: 30
          }
        }],
        line: 44
      },
      "37": {
        loc: {
          start: {
            line: 45,
            column: 12
          },
          end: {
            line: 45,
            column: 65
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 45,
            column: 12
          },
          end: {
            line: 45,
            column: 65
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 45
      },
      "38": {
        loc: {
          start: {
            line: 49,
            column: 21
          },
          end: {
            line: 49,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 21
          },
          end: {
            line: 49,
            column: 23
          }
        }, {
          start: {
            line: 49,
            column: 27
          },
          end: {
            line: 49,
            column: 59
          }
        }],
        line: 49
      },
      "39": {
        loc: {
          start: {
            line: 59,
            column: 28
          },
          end: {
            line: 59,
            column: 103
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 59,
            column: 28
          },
          end: {
            line: 59,
            column: 40
          }
        }, {
          start: {
            line: 59,
            column: 44
          },
          end: {
            line: 59,
            column: 103
          }
        }],
        line: 59
      },
      "40": {
        loc: {
          start: {
            line: 108,
            column: 28
          },
          end: {
            line: 108,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 108,
            column: 28
          },
          end: {
            line: 108,
            column: 65
          }
        }, {
          start: {
            line: 108,
            column: 69
          },
          end: {
            line: 108,
            column: 71
          }
        }],
        line: 108
      },
      "41": {
        loc: {
          start: {
            line: 118,
            column: 16
          },
          end: {
            line: 142,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 119,
            column: 20
          },
          end: {
            line: 137,
            column: 96
          }
        }, {
          start: {
            line: 138,
            column: 20
          },
          end: {
            line: 141,
            column: 46
          }
        }],
        line: 118
      },
      "42": {
        loc: {
          start: {
            line: 121,
            column: 24
          },
          end: {
            line: 123,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 121,
            column: 24
          },
          end: {
            line: 123,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 121
      },
      "43": {
        loc: {
          start: {
            line: 151,
            column: 8
          },
          end: {
            line: 176,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 151,
            column: 8
          },
          end: {
            line: 176,
            column: 9
          }
        }, {
          start: {
            line: 157,
            column: 13
          },
          end: {
            line: 176,
            column: 9
          }
        }],
        line: 151
      },
      "44": {
        loc: {
          start: {
            line: 153,
            column: 12
          },
          end: {
            line: 155,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 153,
            column: 12
          },
          end: {
            line: 155,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 153
      },
      "45": {
        loc: {
          start: {
            line: 161,
            column: 12
          },
          end: {
            line: 175,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 161,
            column: 12
          },
          end: {
            line: 175,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 161
      },
      "46": {
        loc: {
          start: {
            line: 171,
            column: 27
          },
          end: {
            line: 173,
            column: 46
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 172,
            column: 24
          },
          end: {
            line: 172,
            column: 50
          }
        }, {
          start: {
            line: 173,
            column: 24
          },
          end: {
            line: 173,
            column: 46
          }
        }],
        line: 171
      },
      "47": {
        loc: {
          start: {
            line: 184,
            column: 8
          },
          end: {
            line: 220,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 184,
            column: 8
          },
          end: {
            line: 220,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 184
      },
      "48": {
        loc: {
          start: {
            line: 185,
            column: 12
          },
          end: {
            line: 219,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 186,
            column: 16
          },
          end: {
            line: 188,
            column: 26
          }
        }, {
          start: {
            line: 189,
            column: 16
          },
          end: {
            line: 191,
            column: 26
          }
        }, {
          start: {
            line: 192,
            column: 16
          },
          end: {
            line: 194,
            column: 26
          }
        }, {
          start: {
            line: 195,
            column: 16
          },
          end: {
            line: 197,
            column: 26
          }
        }, {
          start: {
            line: 198,
            column: 16
          },
          end: {
            line: 200,
            column: 26
          }
        }, {
          start: {
            line: 201,
            column: 16
          },
          end: {
            line: 203,
            column: 26
          }
        }, {
          start: {
            line: 204,
            column: 16
          },
          end: {
            line: 206,
            column: 26
          }
        }, {
          start: {
            line: 207,
            column: 16
          },
          end: {
            line: 209,
            column: 26
          }
        }, {
          start: {
            line: 210,
            column: 16
          },
          end: {
            line: 212,
            column: 26
          }
        }, {
          start: {
            line: 213,
            column: 16
          },
          end: {
            line: 215,
            column: 26
          }
        }, {
          start: {
            line: 216,
            column: 16
          },
          end: {
            line: 218,
            column: 79
          }
        }],
        line: 185
      },
      "49": {
        loc: {
          start: {
            line: 231,
            column: 16
          },
          end: {
            line: 238,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 232,
            column: 20
          },
          end: {
            line: 234,
            column: 68
          }
        }, {
          start: {
            line: 235,
            column: 20
          },
          end: {
            line: 237,
            column: 46
          }
        }],
        line: 231
      },
      "50": {
        loc: {
          start: {
            line: 248,
            column: 16
          },
          end: {
            line: 256,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 249,
            column: 20
          },
          end: {
            line: 252,
            column: 28
          }
        }, {
          start: {
            line: 253,
            column: 20
          },
          end: {
            line: 255,
            column: 46
          }
        }],
        line: 248
      },
      "51": {
        loc: {
          start: {
            line: 266,
            column: 16
          },
          end: {
            line: 276,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 267,
            column: 20
          },
          end: {
            line: 272,
            column: 28
          }
        }, {
          start: {
            line: 273,
            column: 20
          },
          end: {
            line: 275,
            column: 46
          }
        }],
        line: 266
      },
      "52": {
        loc: {
          start: {
            line: 286,
            column: 16
          },
          end: {
            line: 294,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 287,
            column: 20
          },
          end: {
            line: 290,
            column: 28
          }
        }, {
          start: {
            line: 291,
            column: 20
          },
          end: {
            line: 293,
            column: 46
          }
        }],
        line: 286
      },
      "53": {
        loc: {
          start: {
            line: 304,
            column: 16
          },
          end: {
            line: 314,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 305,
            column: 20
          },
          end: {
            line: 310,
            column: 28
          }
        }, {
          start: {
            line: 311,
            column: 20
          },
          end: {
            line: 313,
            column: 46
          }
        }],
        line: 304
      },
      "54": {
        loc: {
          start: {
            line: 325,
            column: 16
          },
          end: {
            line: 353,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 326,
            column: 20
          },
          end: {
            line: 349,
            column: 84
          }
        }, {
          start: {
            line: 350,
            column: 20
          },
          end: {
            line: 352,
            column: 46
          }
        }],
        line: 325
      },
      "55": {
        loc: {
          start: {
            line: 364,
            column: 16
          },
          end: {
            line: 401,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 365,
            column: 20
          },
          end: {
            line: 376,
            column: 48
          }
        }, {
          start: {
            line: 377,
            column: 20
          },
          end: {
            line: 377,
            column: 84
          }
        }, {
          start: {
            line: 378,
            column: 20
          },
          end: {
            line: 380,
            column: 49
          }
        }, {
          start: {
            line: 381,
            column: 20
          },
          end: {
            line: 381,
            column: 87
          }
        }, {
          start: {
            line: 382,
            column: 20
          },
          end: {
            line: 384,
            column: 49
          }
        }, {
          start: {
            line: 385,
            column: 20
          },
          end: {
            line: 385,
            column: 143
          }
        }, {
          start: {
            line: 386,
            column: 20
          },
          end: {
            line: 388,
            column: 49
          }
        }, {
          start: {
            line: 389,
            column: 20
          },
          end: {
            line: 389,
            column: 141
          }
        }, {
          start: {
            line: 390,
            column: 20
          },
          end: {
            line: 392,
            column: 49
          }
        }, {
          start: {
            line: 393,
            column: 20
          },
          end: {
            line: 395,
            column: 76
          }
        }, {
          start: {
            line: 396,
            column: 20
          },
          end: {
            line: 399,
            column: 38
          }
        }, {
          start: {
            line: 400,
            column: 20
          },
          end: {
            line: 400,
            column: 51
          }
        }],
        line: 364
      },
      "56": {
        loc: {
          start: {
            line: 367,
            column: 24
          },
          end: {
            line: 368,
            column: 50
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 367,
            column: 24
          },
          end: {
            line: 368,
            column: 50
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 367
      },
      "57": {
        loc: {
          start: {
            line: 370,
            column: 24
          },
          end: {
            line: 375,
            column: 25
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 371,
            column: 28
          },
          end: {
            line: 371,
            column: 65
          }
        }, {
          start: {
            line: 372,
            column: 28
          },
          end: {
            line: 372,
            column: 68
          }
        }, {
          start: {
            line: 373,
            column: 28
          },
          end: {
            line: 373,
            column: 76
          }
        }, {
          start: {
            line: 374,
            column: 28
          },
          end: {
            line: 374,
            column: 77
          }
        }],
        line: 370
      },
      "58": {
        loc: {
          start: {
            line: 385,
            column: 84
          },
          end: {
            line: 385,
            column: 140
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 385,
            column: 119
          },
          end: {
            line: 385,
            column: 125
          }
        }, {
          start: {
            line: 385,
            column: 128
          },
          end: {
            line: 385,
            column: 140
          }
        }],
        line: 385
      },
      "59": {
        loc: {
          start: {
            line: 385,
            column: 84
          },
          end: {
            line: 385,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 385,
            column: 84
          },
          end: {
            line: 385,
            column: 97
          }
        }, {
          start: {
            line: 385,
            column: 101
          },
          end: {
            line: 385,
            column: 116
          }
        }],
        line: 385
      },
      "60": {
        loc: {
          start: {
            line: 389,
            column: 87
          },
          end: {
            line: 389,
            column: 138
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 389,
            column: 122
          },
          end: {
            line: 389,
            column: 128
          }
        }, {
          start: {
            line: 389,
            column: 131
          },
          end: {
            line: 389,
            column: 138
          }
        }],
        line: 389
      },
      "61": {
        loc: {
          start: {
            line: 389,
            column: 87
          },
          end: {
            line: 389,
            column: 119
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 389,
            column: 87
          },
          end: {
            line: 389,
            column: 100
          }
        }, {
          start: {
            line: 389,
            column: 104
          },
          end: {
            line: 389,
            column: 119
          }
        }],
        line: 389
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "56": [0, 0],
      "57": [0, 0, 0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/cache-invalidation-service.ts",
      mappings: ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,2EAAwE;AAgBxE;;GAEG;AACH;IAIE,kCAAY,YAAuC;QACjD,IAAI,CAAC,YAAY,GAAG,YAAY,IAAI,IAAI,qDAAwB,EAAE,CAAC;QACnE,IAAI,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;QACnC,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,yDAAsB,GAA9B;QACE,wDAAwD;QACxD,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;YAC1B;gBACE,OAAO,EAAE,oBAAoB;gBAC7B,YAAY,EAAE,CAAC,aAAa,EAAE,oBAAoB,EAAE,iBAAiB,CAAC;aACvE;YACD;gBACE,OAAO,EAAE,mBAAmB;gBAC5B,YAAY,EAAE,CAAC,oBAAoB,EAAE,0BAA0B,CAAC;aACjE;SACF,CAAC,CAAC;QAEH,qDAAqD;QACrD,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAC3B;gBACE,OAAO,EAAE,0BAA0B;gBACnC,YAAY,EAAE,CAAC,sBAAsB,EAAE,gBAAgB,CAAC;aACzD;YACD;gBACE,OAAO,EAAE,mBAAmB;gBAC5B,YAAY,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;aACzD;SACF,CAAC,CAAC;QAEH,wDAAwD;QACxD,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YAC7B;gBACE,OAAO,EAAE,oBAAoB;gBAC7B,YAAY,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,CAAC;aACrD;SACF,CAAC,CAAC;QAEH,2DAA2D;QAC3D,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;YAC9B;gBACE,OAAO,EAAE,0BAA0B;gBACnC,YAAY,EAAE,CAAC,iBAAiB,EAAE,eAAe,CAAC;aACnD;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,0CAAO,GAAP,UAAQ,SAAiB,EAAE,KAA8B;QACvD,IAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAClE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,kCAAM,aAAa,SAAK,KAAK,QAAE,CAAC;IACtE,CAAC;IAED;;OAEG;IACG,oDAAiB,GAAvB,UAAwB,KAAwB;uCAAG,OAAO;;;;;wBAClD,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBACrD,IAAI,CAAC,KAAK,EAAE,CAAC;4BACX,sBAAO;wBACT,CAAC;wBAEK,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;wBAE3C,WAAwB,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK,EAAE,CAAC;4BAAhB,IAAI;4BAEP,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;4BACtD,SAAS,CAAC,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAzB,CAAyB,CAAC,CAAC;4BAEpD,sBAAsB;4BACtB,WAA0C,EAAjB,KAAA,IAAI,CAAC,YAAY,EAAjB,cAAiB,EAAjB,IAAiB,EAAE,CAAC;gCAAlC,UAAU;gCACb,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gCACtE,cAAc,CAAC,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAzB,CAAyB,CAAC,CAAC;4BAC3D,CAAC;wBACH,CAAC;wBAED,iCAAiC;wBACjC,qBAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAA;;wBADvD,iCAAiC;wBACjC,SAAuD,CAAC;;;;;KACzD;IAED;;OAEG;IACK,oDAAiB,GAAzB,UAA0B,KAAwB,EAAE,IAA2B;QAC7E,IAAM,IAAI,GAAa,EAAE,CAAC;QAE1B,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YACrC,wBAAwB;YACxB,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBACjB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,8CAA8C;YAC9C,sEAAsE;YACtE,0CAA0C;YAC1C,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBACjB,IAAM,cAAc,GAAG;oBACrB,uBAAgB,KAAK,CAAC,MAAM,CAAE;oBAC9B,sBAAe,KAAK,CAAC,MAAM,CAAE;oBAC7B,6BAAsB,KAAK,CAAC,MAAM,CAAE;oBACpC,+BAAwB,KAAK,CAAC,MAAM,CAAE;oBACtC,yBAAkB,KAAK,CAAC,MAAM,CAAE;oBAChC,2BAAoB,KAAK,CAAC,MAAM,CAAE;iBACnC,CAAC;gBAEF,IAAI,CAAC,IAAI,OAAT,IAAI,EAAS,cAAc,CAAC,MAAM,CAAC,UAAA,GAAG;oBACpC,OAAA,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC;wBAChC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;wBAC5B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;gBAFxB,CAEwB,CACzB,EAAE;YACL,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,yDAAsB,GAA9B,UAA+B,KAAwB,EAAE,UAAkB;QACzE,IAAM,IAAI,GAAa,EAAE,CAAC;QAE1B,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YACjB,QAAQ,UAAU,EAAE,CAAC;gBACnB,KAAK,aAAa;oBAChB,IAAI,CAAC,IAAI,CAAC,sBAAe,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;oBACzC,MAAM;gBACR,KAAK,oBAAoB;oBACvB,IAAI,CAAC,IAAI,CAAC,6BAAsB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;oBAChD,MAAM;gBACR,KAAK,iBAAiB;oBACpB,IAAI,CAAC,IAAI,CAAC,+BAAwB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;oBAClD,MAAM;gBACR,KAAK,0BAA0B;oBAC7B,IAAI,CAAC,IAAI,CAAC,mCAA4B,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;oBACtD,MAAM;gBACR,KAAK,gBAAgB;oBACnB,IAAI,CAAC,IAAI,CAAC,yBAAkB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;oBAC5C,MAAM;gBACR,KAAK,mBAAmB;oBACtB,IAAI,CAAC,IAAI,CAAC,sBAAe,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;oBACzC,MAAM;gBACR,KAAK,mBAAmB;oBACtB,IAAI,CAAC,IAAI,CAAC,mBAAY,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;oBACtC,MAAM;gBACR,KAAK,gBAAgB;oBACnB,IAAI,CAAC,IAAI,CAAC,oBAAa,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;oBACvC,MAAM;gBACR,KAAK,kBAAkB;oBACrB,IAAI,CAAC,IAAI,CAAC,2BAAoB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;oBAC9C,MAAM;gBACR,KAAK,eAAe;oBAClB,IAAI,CAAC,IAAI,CAAC,wBAAiB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;oBAC3C,MAAM;gBACR;oBACE,oBAAoB;oBACpB,IAAI,CAAC,IAAI,CAAC,UAAG,UAAU,cAAI,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACW,iDAAc,GAA5B,UAA6B,IAAc;uCAAG,OAAO;;;;;;wBAC7C,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,KAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAA7B,CAA6B,CAAC,CAAC;wBAChE,qBAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAA;;wBAA3B,SAA2B,CAAC;;;;;KAC7B;IAED;;OAEG;IACG,uDAAoB,GAA1B,UAA2B,MAAc;uCAAG,OAAO;;;4BACjD,qBAAM,IAAI,CAAC,iBAAiB,CAAC;4BAC3B,IAAI,EAAE,aAAa;4BACnB,MAAM,QAAA;yBACP,CAAC,EAAA;;wBAHF,SAGE,CAAC;;;;;KACJ;IAED;;OAEG;IACG,wDAAqB,GAA3B,UAA4B,MAAc,EAAE,OAAgB;uCAAG,OAAO;;;4BACpE,qBAAM,IAAI,CAAC,iBAAiB,CAAC;4BAC3B,IAAI,EAAE,cAAc;4BACpB,MAAM,QAAA;4BACN,QAAQ,EAAE,OAAO;4BACjB,UAAU,EAAE,OAAO;yBACpB,CAAC,EAAA;;wBALF,SAKE,CAAC;;;;;KACJ;IAED;;OAEG;IACG,0DAAuB,GAA7B,UAA8B,MAAc;uCAAG,OAAO;;;4BACpD,qBAAM,IAAI,CAAC,iBAAiB,CAAC;4BAC3B,IAAI,EAAE,gBAAgB;4BACtB,MAAM,QAAA;yBACP,CAAC,EAAA;;wBAHF,SAGE,CAAC;;;;;KACJ;IAED;;OAEG;IACG,2DAAwB,GAA9B,UAA+B,MAAc,EAAE,UAAmB;uCAAG,OAAO;;;4BAC1E,qBAAM,IAAI,CAAC,iBAAiB,CAAC;4BAC3B,IAAI,EAAE,iBAAiB;4BACvB,MAAM,QAAA;4BACN,QAAQ,EAAE,UAAU;4BACpB,UAAU,EAAE,UAAU;yBACvB,CAAC,EAAA;;wBALF,SAKE,CAAC;;;;;KACJ;IAED;;OAEG;IACG,0DAAuB,GAA7B,UAA8B,MAAc;uCAAG,OAAO;;;;;wBAC9C,iBAAiB,GAAG;4BACxB,iBAAU,MAAM,CAAE;4BAClB,6BAAsB,MAAM,CAAE;4BAC9B,0BAAmB,MAAM,CAAE;4BAC3B,qBAAc,MAAM,CAAE;4BACtB,sBAAe,MAAM,CAAE;4BACvB,qBAAc,MAAM,CAAE;4BACtB,oBAAa,MAAM,CAAE;yBACtB,CAAC;wBAGI,gBAAgB,GAAG;4BACvB,uBAAgB,MAAM,CAAE;4BACxB,sBAAe,MAAM,CAAE;4BACvB,6BAAsB,MAAM,CAAE;4BAC9B,+BAAwB,MAAM,CAAE;4BAChC,mCAA4B,MAAM,CAAE;4BACpC,yBAAkB,MAAM,CAAE;4BAC1B,wBAAiB,MAAM,CAAE;4BACzB,sBAAe,MAAM,CAAE;4BACvB,mBAAY,MAAM,CAAE;4BACpB,2BAAoB,MAAM,CAAE;4BAC5B,oBAAa,MAAM,CAAE;yBACtB,CAAC;wBAEF,qBAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAAA;;wBAA3C,SAA2C,CAAC;;;;;KAC7C;IAED;;OAEG;IACG,kDAAe,GAArB,UAAsB,OAKrB;uCAAG,OAAO;;;;;wBACD,KAAK,GAA8B,OAAO,MAArC,EAAE,SAAS,GAAmB,OAAO,UAA1B,EAAE,MAAM,GAAW,OAAO,OAAlB,EAAE,IAAI,GAAK,OAAO,KAAZ,CAAa;wBAEnD,IAAI,CAAC,MAAM;4BAAE,sBAAO;wBAEZ,KAAA,KAAK,CAAA;;iCACN,MAAM,CAAC,CAAP,wBAAM;iCAIN,SAAS,CAAC,CAAV,wBAAS;iCAIT,iBAAiB,CAAC,CAAlB,wBAAiB;iCAIjB,kBAAkB,CAAC,CAAnB,wBAAkB;;;4BAXrB,qBAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAA;;wBAAvC,SAAuC,CAAC;wBACxC,yBAAM;4BAGN,qBAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,EAAA;;wBAA1C,SAA0C,CAAC;wBAC3C,yBAAM;4BAGN,qBAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,OAAO,CAAC,EAAA;;wBAAvD,SAAuD,CAAC;wBACxD,yBAAM;4BAGN,qBAAM,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,EAAE,CAAC,EAAA;;wBAArD,SAAqD,CAAC;wBACtD,yBAAM;;oBAGN,gEAAgE;oBAChE,qBAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAA;;wBADvC,gEAAgE;wBAChE,SAAuC,CAAC;;;;;;KAE7C;IACH,+BAAC;AAAD,CAAC,AApSD,IAoSC;AApSY,4DAAwB;AAsSrC,qBAAqB;AACR,QAAA,wBAAwB,GAAG,IAAI,wBAAwB,EAAE,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/services/cache-invalidation-service.ts"],
      sourcesContent: ["/**\n * Cache Invalidation Service\n * Handles intelligent cache invalidation based on data changes\n */\n\nimport { ConsolidatedCacheService } from './consolidated-cache-service';\n\nexport interface CacheInvalidationRule {\n  pattern: string | RegExp;\n  dependencies: string[];\n  ttl?: number;\n}\n\nexport interface InvalidationEvent {\n  type: 'user_update' | 'skill_update' | 'profile_update' | 'analysis_update' | 'custom';\n  userId?: string;\n  entityId?: string;\n  entityType?: string;\n  metadata?: Record<string, any>;\n}\n\n/**\n * Intelligent cache invalidation service\n */\nexport class CacheInvalidationService {\n  private cacheService: ConsolidatedCacheService;\n  private invalidationRules: Map<string, CacheInvalidationRule[]>;\n\n  constructor(cacheService?: ConsolidatedCacheService) {\n    this.cacheService = cacheService || new ConsolidatedCacheService();\n    this.invalidationRules = new Map();\n    this.initializeDefaultRules();\n  }\n\n  /**\n   * Initialize default invalidation rules\n   */\n  private initializeDefaultRules(): void {\n    // User profile updates should invalidate related caches\n    this.addRule('user_update', [\n      {\n        pattern: /^user:profile:\\w+$/,\n        dependencies: ['user_skills', 'skill_gap_analysis', 'recommendations']\n      },\n      {\n        pattern: /^user:skills:\\w+$/,\n        dependencies: ['skill_gap_analysis', 'learning_recommendations']\n      }\n    ]);\n\n    // Skill updates should invalidate skill gap analyses\n    this.addRule('skill_update', [\n      {\n        pattern: /^skill_gap_analysis:\\w+$/,\n        dependencies: ['user_recommendations', 'learning_paths']\n      },\n      {\n        pattern: /^user:skills:\\w+$/,\n        dependencies: ['skill_assessments', 'progress_tracking']\n      }\n    ]);\n\n    // Profile updates should invalidate user-related caches\n    this.addRule('profile_update', [\n      {\n        pattern: /^user:profile:\\w+$/,\n        dependencies: ['user_dashboard', 'progress_summary']\n      }\n    ]);\n\n    // Analysis updates should invalidate recommendation caches\n    this.addRule('analysis_update', [\n      {\n        pattern: /^skill_gap_analysis:\\w+$/,\n        dependencies: ['recommendations', 'learning_plan']\n      }\n    ]);\n  }\n\n  /**\n   * Add invalidation rule for a specific event type\n   */\n  addRule(eventType: string, rules: CacheInvalidationRule[]): void {\n    const existingRules = this.invalidationRules.get(eventType) || [];\n    this.invalidationRules.set(eventType, [...existingRules, ...rules]);\n  }\n\n  /**\n   * Invalidate caches based on an event\n   */\n  async invalidateByEvent(event: InvalidationEvent): Promise<void> {\n    const rules = this.invalidationRules.get(event.type);\n    if (!rules) {\n      return;\n    }\n\n    const keysToInvalidate = new Set<string>();\n\n    for (const rule of rules) {\n      // Generate cache keys based on the event\n      const cacheKeys = this.generateCacheKeys(event, rule);\n      cacheKeys.forEach(key => keysToInvalidate.add(key));\n\n      // Add dependency keys\n      for (const dependency of rule.dependencies) {\n        const dependencyKeys = this.generateDependencyKeys(event, dependency);\n        dependencyKeys.forEach(key => keysToInvalidate.add(key));\n      }\n    }\n\n    // Invalidate all identified keys\n    await this.invalidateKeys(Array.from(keysToInvalidate));\n  }\n\n  /**\n   * Generate cache keys based on event and rule\n   */\n  private generateCacheKeys(event: InvalidationEvent, rule: CacheInvalidationRule): string[] {\n    const keys: string[] = [];\n\n    if (typeof rule.pattern === 'string') {\n      // Simple string pattern\n      if (event.userId) {\n        keys.push(rule.pattern.replace(/\\w+$/, event.userId));\n      }\n    } else {\n      // RegExp pattern - need to find matching keys\n      // This would require scanning existing cache keys, which is expensive\n      // For now, we'll generate common patterns\n      if (event.userId) {\n        const commonPatterns = [\n          `user:profile:${event.userId}`,\n          `user:skills:${event.userId}`,\n          `skill_gap_analysis:${event.userId}`,\n          `user:recommendations:${event.userId}`,\n          `user:dashboard:${event.userId}`,\n          `progress_summary:${event.userId}`\n        ];\n\n        keys.push(...commonPatterns.filter(key =>\n          typeof rule.pattern === 'string' ?\n            key.includes(rule.pattern) :\n            rule.pattern.test(key)\n        ));\n      }\n    }\n\n    return keys;\n  }\n\n  /**\n   * Generate dependency keys based on event\n   */\n  private generateDependencyKeys(event: InvalidationEvent, dependency: string): string[] {\n    const keys: string[] = [];\n\n    if (event.userId) {\n      switch (dependency) {\n        case 'user_skills':\n          keys.push(`user:skills:${event.userId}`);\n          break;\n        case 'skill_gap_analysis':\n          keys.push(`skill_gap_analysis:${event.userId}`);\n          break;\n        case 'recommendations':\n          keys.push(`user:recommendations:${event.userId}`);\n          break;\n        case 'learning_recommendations':\n          keys.push(`learning:recommendations:${event.userId}`);\n          break;\n        case 'learning_paths':\n          keys.push(`learning:paths:${event.userId}`);\n          break;\n        case 'skill_assessments':\n          keys.push(`assessments:${event.userId}`);\n          break;\n        case 'progress_tracking':\n          keys.push(`progress:${event.userId}`);\n          break;\n        case 'user_dashboard':\n          keys.push(`dashboard:${event.userId}`);\n          break;\n        case 'progress_summary':\n          keys.push(`progress:summary:${event.userId}`);\n          break;\n        case 'learning_plan':\n          keys.push(`learning:plan:${event.userId}`);\n          break;\n        default:\n          // Custom dependency\n          keys.push(`${dependency}:${event.userId}`);\n      }\n    }\n\n    return keys;\n  }\n\n  /**\n   * Invalidate specific cache keys\n   */\n  private async invalidateKeys(keys: string[]): Promise<void> {\n    const promises = keys.map(key => this.cacheService.delete(key));\n    await Promise.all(promises);\n  }\n\n  /**\n   * Invalidate user-related caches\n   */\n  async invalidateUserCaches(userId: string): Promise<void> {\n    await this.invalidateByEvent({\n      type: 'user_update',\n      userId\n    });\n  }\n\n  /**\n   * Invalidate skill-related caches\n   */\n  async invalidateSkillCaches(userId: string, skillId?: string): Promise<void> {\n    await this.invalidateByEvent({\n      type: 'skill_update',\n      userId,\n      entityId: skillId,\n      entityType: 'skill'\n    });\n  }\n\n  /**\n   * Invalidate profile-related caches\n   */\n  async invalidateProfileCaches(userId: string): Promise<void> {\n    await this.invalidateByEvent({\n      type: 'profile_update',\n      userId\n    });\n  }\n\n  /**\n   * Invalidate analysis-related caches\n   */\n  async invalidateAnalysisCaches(userId: string, analysisId?: string): Promise<void> {\n    await this.invalidateByEvent({\n      type: 'analysis_update',\n      userId,\n      entityId: analysisId,\n      entityType: 'analysis'\n    });\n  }\n\n  /**\n   * Invalidate all caches for a user\n   */\n  async invalidateAllUserCaches(userId: string): Promise<void> {\n    const userCachePatterns = [\n      `user:*:${userId}`,\n      `skill_gap_analysis:${userId}`,\n      `recommendations:${userId}`,\n      `learning:*:${userId}`,\n      `assessments:${userId}`,\n      `progress:*:${userId}`,\n      `dashboard:${userId}`\n    ];\n\n    // Since we can't use wildcards directly, we'll invalidate known patterns\n    const keysToInvalidate = [\n      `user:profile:${userId}`,\n      `user:skills:${userId}`,\n      `skill_gap_analysis:${userId}`,\n      `user:recommendations:${userId}`,\n      `learning:recommendations:${userId}`,\n      `learning:paths:${userId}`,\n      `learning:plan:${userId}`,\n      `assessments:${userId}`,\n      `progress:${userId}`,\n      `progress:summary:${userId}`,\n      `dashboard:${userId}`\n    ];\n\n    await this.invalidateKeys(keysToInvalidate);\n  }\n\n  /**\n   * Smart invalidation based on data changes\n   */\n  async smartInvalidate(changes: {\n    table: string;\n    operation: 'INSERT' | 'UPDATE' | 'DELETE';\n    userId?: string;\n    data?: Record<string, any>;\n  }): Promise<void> {\n    const { table, operation, userId, data } = changes;\n\n    if (!userId) return;\n\n    switch (table) {\n      case 'User':\n        await this.invalidateUserCaches(userId);\n        break;\n      \n      case 'Profile':\n        await this.invalidateProfileCaches(userId);\n        break;\n      \n      case 'SkillAssessment':\n        await this.invalidateSkillCaches(userId, data?.skillId);\n        break;\n      \n      case 'SkillGapAnalysis':\n        await this.invalidateAnalysisCaches(userId, data?.id);\n        break;\n      \n      default:\n        // For unknown tables, invalidate user caches as a safe fallback\n        await this.invalidateUserCaches(userId);\n    }\n  }\n}\n\n// Singleton instance\nexport const cacheInvalidationService = new CacheInvalidationService();\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e20713c601ab7e4b4a64879ca56fe12d25778966"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1zzdcwt1y4 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1zzdcwt1y4();
var __awaiter =
/* istanbul ignore next */
(cov_1zzdcwt1y4().s[0]++,
/* istanbul ignore next */
(cov_1zzdcwt1y4().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1zzdcwt1y4().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1zzdcwt1y4().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1zzdcwt1y4().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[1]++;
    cov_1zzdcwt1y4().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().f[2]++;
      cov_1zzdcwt1y4().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1zzdcwt1y4().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_1zzdcwt1y4().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_1zzdcwt1y4().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().f[4]++;
      cov_1zzdcwt1y4().s[4]++;
      try {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().f[5]++;
      cov_1zzdcwt1y4().s[7]++;
      try {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().f[6]++;
      cov_1zzdcwt1y4().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1zzdcwt1y4().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1zzdcwt1y4().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1zzdcwt1y4().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1zzdcwt1y4().s[12]++,
/* istanbul ignore next */
(cov_1zzdcwt1y4().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_1zzdcwt1y4().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1zzdcwt1y4().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1zzdcwt1y4().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().f[8]++;
        cov_1zzdcwt1y4().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1zzdcwt1y4().b[6][0]++;
          cov_1zzdcwt1y4().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1zzdcwt1y4().b[6][1]++;
        }
        cov_1zzdcwt1y4().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1zzdcwt1y4().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1zzdcwt1y4().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1zzdcwt1y4().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[9]++;
    cov_1zzdcwt1y4().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[10]++;
    cov_1zzdcwt1y4().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().f[11]++;
      cov_1zzdcwt1y4().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[12]++;
    cov_1zzdcwt1y4().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().b[9][0]++;
      cov_1zzdcwt1y4().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1zzdcwt1y4().b[9][1]++;
    }
    cov_1zzdcwt1y4().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().s[25]++;
      try {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1zzdcwt1y4().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_1zzdcwt1y4().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1zzdcwt1y4().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1zzdcwt1y4().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1zzdcwt1y4().b[15][0]++,
        /* istanbul ignore next */
        (cov_1zzdcwt1y4().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1zzdcwt1y4().b[16][1]++,
        /* istanbul ignore next */
        (cov_1zzdcwt1y4().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1zzdcwt1y4().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1zzdcwt1y4().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1zzdcwt1y4().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1zzdcwt1y4().b[12][0]++;
          cov_1zzdcwt1y4().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1zzdcwt1y4().b[12][1]++;
        }
        cov_1zzdcwt1y4().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1zzdcwt1y4().b[18][0]++;
          cov_1zzdcwt1y4().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1zzdcwt1y4().b[18][1]++;
        }
        cov_1zzdcwt1y4().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[19][1]++;
            cov_1zzdcwt1y4().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[19][2]++;
            cov_1zzdcwt1y4().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[19][3]++;
            cov_1zzdcwt1y4().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[19][4]++;
            cov_1zzdcwt1y4().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[19][5]++;
            cov_1zzdcwt1y4().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1zzdcwt1y4().b[20][0]++;
              cov_1zzdcwt1y4().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1zzdcwt1y4().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1zzdcwt1y4().b[20][1]++;
            }
            cov_1zzdcwt1y4().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1zzdcwt1y4().b[23][0]++;
              cov_1zzdcwt1y4().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1zzdcwt1y4().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1zzdcwt1y4().b[23][1]++;
            }
            cov_1zzdcwt1y4().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1zzdcwt1y4().b[25][0]++;
              cov_1zzdcwt1y4().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1zzdcwt1y4().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_1zzdcwt1y4().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1zzdcwt1y4().b[25][1]++;
            }
            cov_1zzdcwt1y4().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1zzdcwt1y4().b[27][0]++;
              cov_1zzdcwt1y4().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1zzdcwt1y4().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1zzdcwt1y4().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1zzdcwt1y4().b[27][1]++;
            }
            cov_1zzdcwt1y4().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1zzdcwt1y4().b[29][0]++;
              cov_1zzdcwt1y4().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1zzdcwt1y4().b[29][1]++;
            }
            cov_1zzdcwt1y4().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1zzdcwt1y4().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1zzdcwt1y4().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1zzdcwt1y4().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().b[30][0]++;
      cov_1zzdcwt1y4().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1zzdcwt1y4().b[30][1]++;
    }
    cov_1zzdcwt1y4().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1zzdcwt1y4().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1zzdcwt1y4().b[31][1]++, void 0),
      done: true
    };
  }
}));
var __spreadArray =
/* istanbul ignore next */
(cov_1zzdcwt1y4().s[67]++,
/* istanbul ignore next */
(cov_1zzdcwt1y4().b[32][0]++, this) &&
/* istanbul ignore next */
(cov_1zzdcwt1y4().b[32][1]++, this.__spreadArray) ||
/* istanbul ignore next */
(cov_1zzdcwt1y4().b[32][2]++, function (to, from, pack) {
  /* istanbul ignore next */
  cov_1zzdcwt1y4().f[13]++;
  cov_1zzdcwt1y4().s[68]++;
  if (
  /* istanbul ignore next */
  (cov_1zzdcwt1y4().b[34][0]++, pack) ||
  /* istanbul ignore next */
  (cov_1zzdcwt1y4().b[34][1]++, arguments.length === 2)) {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().b[33][0]++;
    cov_1zzdcwt1y4().s[69]++;
    for (var i =
      /* istanbul ignore next */
      (cov_1zzdcwt1y4().s[70]++, 0), l =
      /* istanbul ignore next */
      (cov_1zzdcwt1y4().s[71]++, from.length), ar; i < l; i++) {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().s[72]++;
      if (
      /* istanbul ignore next */
      (cov_1zzdcwt1y4().b[36][0]++, ar) ||
      /* istanbul ignore next */
      (cov_1zzdcwt1y4().b[36][1]++, !(i in from))) {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().b[35][0]++;
        cov_1zzdcwt1y4().s[73]++;
        if (!ar) {
          /* istanbul ignore next */
          cov_1zzdcwt1y4().b[37][0]++;
          cov_1zzdcwt1y4().s[74]++;
          ar = Array.prototype.slice.call(from, 0, i);
        } else
        /* istanbul ignore next */
        {
          cov_1zzdcwt1y4().b[37][1]++;
        }
        cov_1zzdcwt1y4().s[75]++;
        ar[i] = from[i];
      } else
      /* istanbul ignore next */
      {
        cov_1zzdcwt1y4().b[35][1]++;
      }
    }
  } else
  /* istanbul ignore next */
  {
    cov_1zzdcwt1y4().b[33][1]++;
  }
  cov_1zzdcwt1y4().s[76]++;
  return to.concat(
  /* istanbul ignore next */
  (cov_1zzdcwt1y4().b[38][0]++, ar) ||
  /* istanbul ignore next */
  (cov_1zzdcwt1y4().b[38][1]++, Array.prototype.slice.call(from)));
}));
/* istanbul ignore next */
cov_1zzdcwt1y4().s[77]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1zzdcwt1y4().s[78]++;
exports.cacheInvalidationService = exports.CacheInvalidationService = void 0;
var consolidated_cache_service_1 =
/* istanbul ignore next */
(cov_1zzdcwt1y4().s[79]++, require("./consolidated-cache-service"));
/**
 * Intelligent cache invalidation service
 */
var CacheInvalidationService =
/* istanbul ignore next */
(/** @class */cov_1zzdcwt1y4().s[80]++, function () {
  /* istanbul ignore next */
  cov_1zzdcwt1y4().f[14]++;
  function CacheInvalidationService(cacheService) {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[15]++;
    cov_1zzdcwt1y4().s[81]++;
    this.cacheService =
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().b[39][0]++, cacheService) ||
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().b[39][1]++, new consolidated_cache_service_1.ConsolidatedCacheService());
    /* istanbul ignore next */
    cov_1zzdcwt1y4().s[82]++;
    this.invalidationRules = new Map();
    /* istanbul ignore next */
    cov_1zzdcwt1y4().s[83]++;
    this.initializeDefaultRules();
  }
  /**
   * Initialize default invalidation rules
   */
  /* istanbul ignore next */
  cov_1zzdcwt1y4().s[84]++;
  CacheInvalidationService.prototype.initializeDefaultRules = function () {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[16]++;
    cov_1zzdcwt1y4().s[85]++;
    // User profile updates should invalidate related caches
    this.addRule('user_update', [{
      pattern: /^user:profile:\w+$/,
      dependencies: ['user_skills', 'skill_gap_analysis', 'recommendations']
    }, {
      pattern: /^user:skills:\w+$/,
      dependencies: ['skill_gap_analysis', 'learning_recommendations']
    }]);
    // Skill updates should invalidate skill gap analyses
    /* istanbul ignore next */
    cov_1zzdcwt1y4().s[86]++;
    this.addRule('skill_update', [{
      pattern: /^skill_gap_analysis:\w+$/,
      dependencies: ['user_recommendations', 'learning_paths']
    }, {
      pattern: /^user:skills:\w+$/,
      dependencies: ['skill_assessments', 'progress_tracking']
    }]);
    // Profile updates should invalidate user-related caches
    /* istanbul ignore next */
    cov_1zzdcwt1y4().s[87]++;
    this.addRule('profile_update', [{
      pattern: /^user:profile:\w+$/,
      dependencies: ['user_dashboard', 'progress_summary']
    }]);
    // Analysis updates should invalidate recommendation caches
    /* istanbul ignore next */
    cov_1zzdcwt1y4().s[88]++;
    this.addRule('analysis_update', [{
      pattern: /^skill_gap_analysis:\w+$/,
      dependencies: ['recommendations', 'learning_plan']
    }]);
  };
  /**
   * Add invalidation rule for a specific event type
   */
  /* istanbul ignore next */
  cov_1zzdcwt1y4().s[89]++;
  CacheInvalidationService.prototype.addRule = function (eventType, rules) {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[17]++;
    var existingRules =
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().s[90]++,
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().b[40][0]++, this.invalidationRules.get(eventType)) ||
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().b[40][1]++, []));
    /* istanbul ignore next */
    cov_1zzdcwt1y4().s[91]++;
    this.invalidationRules.set(eventType, __spreadArray(__spreadArray([], existingRules, true), rules, true));
  };
  /**
   * Invalidate caches based on an event
   */
  /* istanbul ignore next */
  cov_1zzdcwt1y4().s[92]++;
  CacheInvalidationService.prototype.invalidateByEvent = function (event) {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[18]++;
    cov_1zzdcwt1y4().s[93]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().f[19]++;
      var rules, keysToInvalidate, _i, rules_1, rule, cacheKeys, _a, _b, dependency, dependencyKeys;
      /* istanbul ignore next */
      cov_1zzdcwt1y4().s[94]++;
      return __generator(this, function (_c) {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().f[20]++;
        cov_1zzdcwt1y4().s[95]++;
        switch (_c.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[41][0]++;
            cov_1zzdcwt1y4().s[96]++;
            rules = this.invalidationRules.get(event.type);
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[97]++;
            if (!rules) {
              /* istanbul ignore next */
              cov_1zzdcwt1y4().b[42][0]++;
              cov_1zzdcwt1y4().s[98]++;
              return [2 /*return*/];
            } else
            /* istanbul ignore next */
            {
              cov_1zzdcwt1y4().b[42][1]++;
            }
            cov_1zzdcwt1y4().s[99]++;
            keysToInvalidate = new Set();
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[100]++;
            for (_i = 0, rules_1 = rules; _i < rules_1.length; _i++) {
              /* istanbul ignore next */
              cov_1zzdcwt1y4().s[101]++;
              rule = rules_1[_i];
              /* istanbul ignore next */
              cov_1zzdcwt1y4().s[102]++;
              cacheKeys = this.generateCacheKeys(event, rule);
              /* istanbul ignore next */
              cov_1zzdcwt1y4().s[103]++;
              cacheKeys.forEach(function (key) {
                /* istanbul ignore next */
                cov_1zzdcwt1y4().f[21]++;
                cov_1zzdcwt1y4().s[104]++;
                return keysToInvalidate.add(key);
              });
              // Add dependency keys
              /* istanbul ignore next */
              cov_1zzdcwt1y4().s[105]++;
              for (_a = 0, _b = rule.dependencies; _a < _b.length; _a++) {
                /* istanbul ignore next */
                cov_1zzdcwt1y4().s[106]++;
                dependency = _b[_a];
                /* istanbul ignore next */
                cov_1zzdcwt1y4().s[107]++;
                dependencyKeys = this.generateDependencyKeys(event, dependency);
                /* istanbul ignore next */
                cov_1zzdcwt1y4().s[108]++;
                dependencyKeys.forEach(function (key) {
                  /* istanbul ignore next */
                  cov_1zzdcwt1y4().f[22]++;
                  cov_1zzdcwt1y4().s[109]++;
                  return keysToInvalidate.add(key);
                });
              }
            }
            // Invalidate all identified keys
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[110]++;
            return [4 /*yield*/, this.invalidateKeys(Array.from(keysToInvalidate))];
          case 1:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[41][1]++;
            cov_1zzdcwt1y4().s[111]++;
            // Invalidate all identified keys
            _c.sent();
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[112]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Generate cache keys based on event and rule
   */
  /* istanbul ignore next */
  cov_1zzdcwt1y4().s[113]++;
  CacheInvalidationService.prototype.generateCacheKeys = function (event, rule) {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[23]++;
    var keys =
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().s[114]++, []);
    /* istanbul ignore next */
    cov_1zzdcwt1y4().s[115]++;
    if (typeof rule.pattern === 'string') {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().b[43][0]++;
      cov_1zzdcwt1y4().s[116]++;
      // Simple string pattern
      if (event.userId) {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().b[44][0]++;
        cov_1zzdcwt1y4().s[117]++;
        keys.push(rule.pattern.replace(/\w+$/, event.userId));
      } else
      /* istanbul ignore next */
      {
        cov_1zzdcwt1y4().b[44][1]++;
      }
    } else {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().b[43][1]++;
      cov_1zzdcwt1y4().s[118]++;
      // RegExp pattern - need to find matching keys
      // This would require scanning existing cache keys, which is expensive
      // For now, we'll generate common patterns
      if (event.userId) {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().b[45][0]++;
        var commonPatterns =
        /* istanbul ignore next */
        (cov_1zzdcwt1y4().s[119]++, ["user:profile:".concat(event.userId), "user:skills:".concat(event.userId), "skill_gap_analysis:".concat(event.userId), "user:recommendations:".concat(event.userId), "user:dashboard:".concat(event.userId), "progress_summary:".concat(event.userId)]);
        /* istanbul ignore next */
        cov_1zzdcwt1y4().s[120]++;
        keys.push.apply(keys, commonPatterns.filter(function (key) {
          /* istanbul ignore next */
          cov_1zzdcwt1y4().f[24]++;
          cov_1zzdcwt1y4().s[121]++;
          return typeof rule.pattern === 'string' ?
          /* istanbul ignore next */
          (cov_1zzdcwt1y4().b[46][0]++, key.includes(rule.pattern)) :
          /* istanbul ignore next */
          (cov_1zzdcwt1y4().b[46][1]++, rule.pattern.test(key));
        }));
      } else
      /* istanbul ignore next */
      {
        cov_1zzdcwt1y4().b[45][1]++;
      }
    }
    /* istanbul ignore next */
    cov_1zzdcwt1y4().s[122]++;
    return keys;
  };
  /**
   * Generate dependency keys based on event
   */
  /* istanbul ignore next */
  cov_1zzdcwt1y4().s[123]++;
  CacheInvalidationService.prototype.generateDependencyKeys = function (event, dependency) {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[25]++;
    var keys =
    /* istanbul ignore next */
    (cov_1zzdcwt1y4().s[124]++, []);
    /* istanbul ignore next */
    cov_1zzdcwt1y4().s[125]++;
    if (event.userId) {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().b[47][0]++;
      cov_1zzdcwt1y4().s[126]++;
      switch (dependency) {
        case 'user_skills':
          /* istanbul ignore next */
          cov_1zzdcwt1y4().b[48][0]++;
          cov_1zzdcwt1y4().s[127]++;
          keys.push("user:skills:".concat(event.userId));
          /* istanbul ignore next */
          cov_1zzdcwt1y4().s[128]++;
          break;
        case 'skill_gap_analysis':
          /* istanbul ignore next */
          cov_1zzdcwt1y4().b[48][1]++;
          cov_1zzdcwt1y4().s[129]++;
          keys.push("skill_gap_analysis:".concat(event.userId));
          /* istanbul ignore next */
          cov_1zzdcwt1y4().s[130]++;
          break;
        case 'recommendations':
          /* istanbul ignore next */
          cov_1zzdcwt1y4().b[48][2]++;
          cov_1zzdcwt1y4().s[131]++;
          keys.push("user:recommendations:".concat(event.userId));
          /* istanbul ignore next */
          cov_1zzdcwt1y4().s[132]++;
          break;
        case 'learning_recommendations':
          /* istanbul ignore next */
          cov_1zzdcwt1y4().b[48][3]++;
          cov_1zzdcwt1y4().s[133]++;
          keys.push("learning:recommendations:".concat(event.userId));
          /* istanbul ignore next */
          cov_1zzdcwt1y4().s[134]++;
          break;
        case 'learning_paths':
          /* istanbul ignore next */
          cov_1zzdcwt1y4().b[48][4]++;
          cov_1zzdcwt1y4().s[135]++;
          keys.push("learning:paths:".concat(event.userId));
          /* istanbul ignore next */
          cov_1zzdcwt1y4().s[136]++;
          break;
        case 'skill_assessments':
          /* istanbul ignore next */
          cov_1zzdcwt1y4().b[48][5]++;
          cov_1zzdcwt1y4().s[137]++;
          keys.push("assessments:".concat(event.userId));
          /* istanbul ignore next */
          cov_1zzdcwt1y4().s[138]++;
          break;
        case 'progress_tracking':
          /* istanbul ignore next */
          cov_1zzdcwt1y4().b[48][6]++;
          cov_1zzdcwt1y4().s[139]++;
          keys.push("progress:".concat(event.userId));
          /* istanbul ignore next */
          cov_1zzdcwt1y4().s[140]++;
          break;
        case 'user_dashboard':
          /* istanbul ignore next */
          cov_1zzdcwt1y4().b[48][7]++;
          cov_1zzdcwt1y4().s[141]++;
          keys.push("dashboard:".concat(event.userId));
          /* istanbul ignore next */
          cov_1zzdcwt1y4().s[142]++;
          break;
        case 'progress_summary':
          /* istanbul ignore next */
          cov_1zzdcwt1y4().b[48][8]++;
          cov_1zzdcwt1y4().s[143]++;
          keys.push("progress:summary:".concat(event.userId));
          /* istanbul ignore next */
          cov_1zzdcwt1y4().s[144]++;
          break;
        case 'learning_plan':
          /* istanbul ignore next */
          cov_1zzdcwt1y4().b[48][9]++;
          cov_1zzdcwt1y4().s[145]++;
          keys.push("learning:plan:".concat(event.userId));
          /* istanbul ignore next */
          cov_1zzdcwt1y4().s[146]++;
          break;
        default:
          /* istanbul ignore next */
          cov_1zzdcwt1y4().b[48][10]++;
          cov_1zzdcwt1y4().s[147]++;
          // Custom dependency
          keys.push("".concat(dependency, ":").concat(event.userId));
      }
    } else
    /* istanbul ignore next */
    {
      cov_1zzdcwt1y4().b[47][1]++;
    }
    cov_1zzdcwt1y4().s[148]++;
    return keys;
  };
  /**
   * Invalidate specific cache keys
   */
  /* istanbul ignore next */
  cov_1zzdcwt1y4().s[149]++;
  CacheInvalidationService.prototype.invalidateKeys = function (keys) {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[26]++;
    cov_1zzdcwt1y4().s[150]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().f[27]++;
      var promises;
      var _this =
      /* istanbul ignore next */
      (cov_1zzdcwt1y4().s[151]++, this);
      /* istanbul ignore next */
      cov_1zzdcwt1y4().s[152]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().f[28]++;
        cov_1zzdcwt1y4().s[153]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[49][0]++;
            cov_1zzdcwt1y4().s[154]++;
            promises = keys.map(function (key) {
              /* istanbul ignore next */
              cov_1zzdcwt1y4().f[29]++;
              cov_1zzdcwt1y4().s[155]++;
              return _this.cacheService.delete(key);
            });
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[156]++;
            return [4 /*yield*/, Promise.all(promises)];
          case 1:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[49][1]++;
            cov_1zzdcwt1y4().s[157]++;
            _a.sent();
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[158]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Invalidate user-related caches
   */
  /* istanbul ignore next */
  cov_1zzdcwt1y4().s[159]++;
  CacheInvalidationService.prototype.invalidateUserCaches = function (userId) {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[30]++;
    cov_1zzdcwt1y4().s[160]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().f[31]++;
      cov_1zzdcwt1y4().s[161]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().f[32]++;
        cov_1zzdcwt1y4().s[162]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[50][0]++;
            cov_1zzdcwt1y4().s[163]++;
            return [4 /*yield*/, this.invalidateByEvent({
              type: 'user_update',
              userId: userId
            })];
          case 1:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[50][1]++;
            cov_1zzdcwt1y4().s[164]++;
            _a.sent();
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[165]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Invalidate skill-related caches
   */
  /* istanbul ignore next */
  cov_1zzdcwt1y4().s[166]++;
  CacheInvalidationService.prototype.invalidateSkillCaches = function (userId, skillId) {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[33]++;
    cov_1zzdcwt1y4().s[167]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().f[34]++;
      cov_1zzdcwt1y4().s[168]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().f[35]++;
        cov_1zzdcwt1y4().s[169]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[51][0]++;
            cov_1zzdcwt1y4().s[170]++;
            return [4 /*yield*/, this.invalidateByEvent({
              type: 'skill_update',
              userId: userId,
              entityId: skillId,
              entityType: 'skill'
            })];
          case 1:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[51][1]++;
            cov_1zzdcwt1y4().s[171]++;
            _a.sent();
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[172]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Invalidate profile-related caches
   */
  /* istanbul ignore next */
  cov_1zzdcwt1y4().s[173]++;
  CacheInvalidationService.prototype.invalidateProfileCaches = function (userId) {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[36]++;
    cov_1zzdcwt1y4().s[174]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().f[37]++;
      cov_1zzdcwt1y4().s[175]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().f[38]++;
        cov_1zzdcwt1y4().s[176]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[52][0]++;
            cov_1zzdcwt1y4().s[177]++;
            return [4 /*yield*/, this.invalidateByEvent({
              type: 'profile_update',
              userId: userId
            })];
          case 1:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[52][1]++;
            cov_1zzdcwt1y4().s[178]++;
            _a.sent();
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[179]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Invalidate analysis-related caches
   */
  /* istanbul ignore next */
  cov_1zzdcwt1y4().s[180]++;
  CacheInvalidationService.prototype.invalidateAnalysisCaches = function (userId, analysisId) {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[39]++;
    cov_1zzdcwt1y4().s[181]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().f[40]++;
      cov_1zzdcwt1y4().s[182]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().f[41]++;
        cov_1zzdcwt1y4().s[183]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[53][0]++;
            cov_1zzdcwt1y4().s[184]++;
            return [4 /*yield*/, this.invalidateByEvent({
              type: 'analysis_update',
              userId: userId,
              entityId: analysisId,
              entityType: 'analysis'
            })];
          case 1:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[53][1]++;
            cov_1zzdcwt1y4().s[185]++;
            _a.sent();
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[186]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Invalidate all caches for a user
   */
  /* istanbul ignore next */
  cov_1zzdcwt1y4().s[187]++;
  CacheInvalidationService.prototype.invalidateAllUserCaches = function (userId) {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[42]++;
    cov_1zzdcwt1y4().s[188]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().f[43]++;
      var userCachePatterns, keysToInvalidate;
      /* istanbul ignore next */
      cov_1zzdcwt1y4().s[189]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().f[44]++;
        cov_1zzdcwt1y4().s[190]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[54][0]++;
            cov_1zzdcwt1y4().s[191]++;
            userCachePatterns = ["user:*:".concat(userId), "skill_gap_analysis:".concat(userId), "recommendations:".concat(userId), "learning:*:".concat(userId), "assessments:".concat(userId), "progress:*:".concat(userId), "dashboard:".concat(userId)];
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[192]++;
            keysToInvalidate = ["user:profile:".concat(userId), "user:skills:".concat(userId), "skill_gap_analysis:".concat(userId), "user:recommendations:".concat(userId), "learning:recommendations:".concat(userId), "learning:paths:".concat(userId), "learning:plan:".concat(userId), "assessments:".concat(userId), "progress:".concat(userId), "progress:summary:".concat(userId), "dashboard:".concat(userId)];
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[193]++;
            return [4 /*yield*/, this.invalidateKeys(keysToInvalidate)];
          case 1:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[54][1]++;
            cov_1zzdcwt1y4().s[194]++;
            _a.sent();
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[195]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Smart invalidation based on data changes
   */
  /* istanbul ignore next */
  cov_1zzdcwt1y4().s[196]++;
  CacheInvalidationService.prototype.smartInvalidate = function (changes) {
    /* istanbul ignore next */
    cov_1zzdcwt1y4().f[45]++;
    cov_1zzdcwt1y4().s[197]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1zzdcwt1y4().f[46]++;
      var table, operation, userId, data, _a;
      /* istanbul ignore next */
      cov_1zzdcwt1y4().s[198]++;
      return __generator(this, function (_b) {
        /* istanbul ignore next */
        cov_1zzdcwt1y4().f[47]++;
        cov_1zzdcwt1y4().s[199]++;
        switch (_b.label) {
          case 0:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[55][0]++;
            cov_1zzdcwt1y4().s[200]++;
            table = changes.table, operation = changes.operation, userId = changes.userId, data = changes.data;
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[201]++;
            if (!userId) {
              /* istanbul ignore next */
              cov_1zzdcwt1y4().b[56][0]++;
              cov_1zzdcwt1y4().s[202]++;
              return [2 /*return*/];
            } else
            /* istanbul ignore next */
            {
              cov_1zzdcwt1y4().b[56][1]++;
            }
            cov_1zzdcwt1y4().s[203]++;
            _a = table;
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[204]++;
            switch (_a) {
              case 'User':
                /* istanbul ignore next */
                cov_1zzdcwt1y4().b[57][0]++;
                cov_1zzdcwt1y4().s[205]++;
                return [3 /*break*/, 1];
              case 'Profile':
                /* istanbul ignore next */
                cov_1zzdcwt1y4().b[57][1]++;
                cov_1zzdcwt1y4().s[206]++;
                return [3 /*break*/, 3];
              case 'SkillAssessment':
                /* istanbul ignore next */
                cov_1zzdcwt1y4().b[57][2]++;
                cov_1zzdcwt1y4().s[207]++;
                return [3 /*break*/, 5];
              case 'SkillGapAnalysis':
                /* istanbul ignore next */
                cov_1zzdcwt1y4().b[57][3]++;
                cov_1zzdcwt1y4().s[208]++;
                return [3 /*break*/, 7];
            }
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[209]++;
            return [3 /*break*/, 9];
          case 1:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[55][1]++;
            cov_1zzdcwt1y4().s[210]++;
            return [4 /*yield*/, this.invalidateUserCaches(userId)];
          case 2:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[55][2]++;
            cov_1zzdcwt1y4().s[211]++;
            _b.sent();
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[212]++;
            return [3 /*break*/, 11];
          case 3:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[55][3]++;
            cov_1zzdcwt1y4().s[213]++;
            return [4 /*yield*/, this.invalidateProfileCaches(userId)];
          case 4:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[55][4]++;
            cov_1zzdcwt1y4().s[214]++;
            _b.sent();
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[215]++;
            return [3 /*break*/, 11];
          case 5:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[55][5]++;
            cov_1zzdcwt1y4().s[216]++;
            return [4 /*yield*/, this.invalidateSkillCaches(userId,
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[59][0]++, data === null) ||
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[59][1]++, data === void 0) ?
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[58][0]++, void 0) :
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[58][1]++, data.skillId))];
          case 6:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[55][6]++;
            cov_1zzdcwt1y4().s[217]++;
            _b.sent();
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[218]++;
            return [3 /*break*/, 11];
          case 7:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[55][7]++;
            cov_1zzdcwt1y4().s[219]++;
            return [4 /*yield*/, this.invalidateAnalysisCaches(userId,
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[61][0]++, data === null) ||
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[61][1]++, data === void 0) ?
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[60][0]++, void 0) :
            /* istanbul ignore next */
            (cov_1zzdcwt1y4().b[60][1]++, data.id))];
          case 8:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[55][8]++;
            cov_1zzdcwt1y4().s[220]++;
            _b.sent();
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[221]++;
            return [3 /*break*/, 11];
          case 9:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[55][9]++;
            cov_1zzdcwt1y4().s[222]++;
            // For unknown tables, invalidate user caches as a safe fallback
            return [4 /*yield*/, this.invalidateUserCaches(userId)];
          case 10:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[55][10]++;
            cov_1zzdcwt1y4().s[223]++;
            // For unknown tables, invalidate user caches as a safe fallback
            _b.sent();
            /* istanbul ignore next */
            cov_1zzdcwt1y4().s[224]++;
            _b.label = 11;
          case 11:
            /* istanbul ignore next */
            cov_1zzdcwt1y4().b[55][11]++;
            cov_1zzdcwt1y4().s[225]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_1zzdcwt1y4().s[226]++;
  return CacheInvalidationService;
}());
/* istanbul ignore next */
cov_1zzdcwt1y4().s[227]++;
exports.CacheInvalidationService = CacheInvalidationService;
// Singleton instance
/* istanbul ignore next */
cov_1zzdcwt1y4().s[228]++;
exports.cacheInvalidationService = new CacheInvalidationService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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