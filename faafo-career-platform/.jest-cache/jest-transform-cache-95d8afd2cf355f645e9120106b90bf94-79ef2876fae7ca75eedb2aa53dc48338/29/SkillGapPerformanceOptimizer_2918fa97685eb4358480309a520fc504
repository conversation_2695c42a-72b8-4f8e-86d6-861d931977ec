c256d4e2b97666b1feaa38874d8d9d9c
"use strict";

/* istanbul ignore next */
function cov_ir4na7dz0() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/performance/SkillGapPerformanceOptimizer.ts";
  var hash = "2f0aa26643085ba4285a448f9a836ec3c97dc5b4";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/performance/SkillGapPerformanceOptimizer.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 49,
          column: 62
        }
      },
      "79": {
        start: {
          line: 50,
          column: 0
        },
        end: {
          line: 50,
          column: 85
        }
      },
      "80": {
        start: {
          line: 51,
          column: 19
        },
        end: {
          line: 51,
          column: 40
        }
      },
      "81": {
        start: {
          line: 52,
          column: 15
        },
        end: {
          line: 52,
          column: 38
        }
      },
      "82": {
        start: {
          line: 53,
          column: 14
        },
        end: {
          line: 53,
          column: 36
        }
      },
      "83": {
        start: {
          line: 58,
          column: 50
        },
        end: {
          line: 572,
          column: 3
        }
      },
      "84": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 61,
          column: 33
        }
      },
      "85": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 62,
          column: 41
        }
      },
      "86": {
        start: {
          line: 63,
          column: 8
        },
        end: {
          line: 63,
          column: 35
        }
      },
      "87": {
        start: {
          line: 64,
          column: 8
        },
        end: {
          line: 64,
          column: 37
        }
      },
      "88": {
        start: {
          line: 66,
          column: 8
        },
        end: {
          line: 66,
          column: 36
        }
      },
      "89": {
        start: {
          line: 67,
          column: 8
        },
        end: {
          line: 67,
          column: 37
        }
      },
      "90": {
        start: {
          line: 68,
          column: 8
        },
        end: {
          line: 68,
          column: 30
        }
      },
      "91": {
        start: {
          line: 69,
          column: 8
        },
        end: {
          line: 69,
          column: 33
        }
      },
      "92": {
        start: {
          line: 71,
          column: 8
        },
        end: {
          line: 74,
          column: 11
        }
      },
      "93": {
        start: {
          line: 75,
          column: 8
        },
        end: {
          line: 78,
          column: 11
        }
      },
      "94": {
        start: {
          line: 79,
          column: 8
        },
        end: {
          line: 82,
          column: 11
        }
      },
      "95": {
        start: {
          line: 83,
          column: 8
        },
        end: {
          line: 86,
          column: 11
        }
      },
      "96": {
        start: {
          line: 88,
          column: 4
        },
        end: {
          line: 93,
          column: 6
        }
      },
      "97": {
        start: {
          line: 89,
          column: 8
        },
        end: {
          line: 91,
          column: 9
        }
      },
      "98": {
        start: {
          line: 90,
          column: 12
        },
        end: {
          line: 90,
          column: 87
        }
      },
      "99": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 92,
          column: 53
        }
      },
      "100": {
        start: {
          line: 97,
          column: 4
        },
        end: {
          line: 143,
          column: 6
        }
      },
      "101": {
        start: {
          line: 98,
          column: 8
        },
        end: {
          line: 142,
          column: 11
        }
      },
      "102": {
        start: {
          line: 100,
          column: 12
        },
        end: {
          line: 141,
          column: 15
        }
      },
      "103": {
        start: {
          line: 101,
          column: 16
        },
        end: {
          line: 140,
          column: 17
        }
      },
      "104": {
        start: {
          line: 103,
          column: 24
        },
        end: {
          line: 103,
          column: 67
        }
      },
      "105": {
        start: {
          line: 104,
          column: 24
        },
        end: {
          line: 104,
          column: 58
        }
      },
      "106": {
        start: {
          line: 105,
          column: 24
        },
        end: {
          line: 105,
          column: 60
        }
      },
      "107": {
        start: {
          line: 106,
          column: 24
        },
        end: {
          line: 110,
          column: 25
        }
      },
      "108": {
        start: {
          line: 107,
          column: 28
        },
        end: {
          line: 107,
          column: 59
        }
      },
      "109": {
        start: {
          line: 108,
          column: 28
        },
        end: {
          line: 108,
          column: 69
        }
      },
      "110": {
        start: {
          line: 109,
          column: 28
        },
        end: {
          line: 109,
          column: 56
        }
      },
      "111": {
        start: {
          line: 111,
          column: 24
        },
        end: {
          line: 111,
          column: 56
        }
      },
      "112": {
        start: {
          line: 112,
          column: 24
        },
        end: {
          line: 112,
          column: 37
        }
      },
      "113": {
        start: {
          line: 114,
          column: 24
        },
        end: {
          line: 114,
          column: 50
        }
      },
      "114": {
        start: {
          line: 115,
          column: 24
        },
        end: {
          line: 124,
          column: 32
        }
      },
      "115": {
        start: {
          line: 127,
          column: 24
        },
        end: {
          line: 127,
          column: 41
        }
      },
      "116": {
        start: {
          line: 128,
          column: 24
        },
        end: {
          line: 130,
          column: 25
        }
      },
      "117": {
        start: {
          line: 129,
          column: 28
        },
        end: {
          line: 129,
          column: 63
        }
      },
      "118": {
        start: {
          line: 131,
          column: 24
        },
        end: {
          line: 131,
          column: 48
        }
      },
      "119": {
        start: {
          line: 133,
          column: 24
        },
        end: {
          line: 133,
          column: 44
        }
      },
      "120": {
        start: {
          line: 134,
          column: 24
        },
        end: {
          line: 134,
          column: 71
        }
      },
      "121": {
        start: {
          line: 135,
          column: 24
        },
        end: {
          line: 135,
          column: 36
        }
      },
      "122": {
        start: {
          line: 136,
          column: 24
        },
        end: {
          line: 136,
          column: 48
        }
      },
      "123": {
        start: {
          line: 138,
          column: 24
        },
        end: {
          line: 138,
          column: 65
        }
      },
      "124": {
        start: {
          line: 139,
          column: 24
        },
        end: {
          line: 139,
          column: 52
        }
      },
      "125": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 206,
          column: 6
        }
      },
      "126": {
        start: {
          line: 148,
          column: 8
        },
        end: {
          line: 205,
          column: 11
        }
      },
      "127": {
        start: {
          line: 150,
          column: 12
        },
        end: {
          line: 204,
          column: 15
        }
      },
      "128": {
        start: {
          line: 151,
          column: 16
        },
        end: {
          line: 203,
          column: 17
        }
      },
      "129": {
        start: {
          line: 153,
          column: 24
        },
        end: {
          line: 153,
          column: 67
        }
      },
      "130": {
        start: {
          line: 154,
          column: 24
        },
        end: {
          line: 154,
          column: 37
        }
      },
      "131": {
        start: {
          line: 155,
          column: 24
        },
        end: {
          line: 155,
          column: 41
        }
      },
      "132": {
        start: {
          line: 157,
          column: 24
        },
        end: {
          line: 169,
          column: 25
        }
      },
      "133": {
        start: {
          line: 158,
          column: 28
        },
        end: {
          line: 158,
          column: 53
        }
      },
      "134": {
        start: {
          line: 159,
          column: 28
        },
        end: {
          line: 159,
          column: 64
        }
      },
      "135": {
        start: {
          line: 160,
          column: 28
        },
        end: {
          line: 160,
          column: 67
        }
      },
      "136": {
        start: {
          line: 161,
          column: 28
        },
        end: {
          line: 168,
          column: 29
        }
      },
      "137": {
        start: {
          line: 162,
          column: 32
        },
        end: {
          line: 162,
          column: 53
        }
      },
      "138": {
        start: {
          line: 163,
          column: 32
        },
        end: {
          line: 163,
          column: 65
        }
      },
      "139": {
        start: {
          line: 166,
          column: 32
        },
        end: {
          line: 166,
          column: 58
        }
      },
      "140": {
        start: {
          line: 167,
          column: 32
        },
        end: {
          line: 167,
          column: 66
        }
      },
      "141": {
        start: {
          line: 170,
          column: 24
        },
        end: {
          line: 170,
          column: 79
        }
      },
      "142": {
        start: {
          line: 170,
          column: 55
        },
        end: {
          line: 170,
          column: 79
        }
      },
      "143": {
        start: {
          line: 171,
          column: 24
        },
        end: {
          line: 171,
          column: 37
        }
      },
      "144": {
        start: {
          line: 173,
          column: 24
        },
        end: {
          line: 173,
          column: 50
        }
      },
      "145": {
        start: {
          line: 174,
          column: 24
        },
        end: {
          line: 185,
          column: 32
        }
      },
      "146": {
        start: {
          line: 187,
          column: 24
        },
        end: {
          line: 187,
          column: 43
        }
      },
      "147": {
        start: {
          line: 189,
          column: 24
        },
        end: {
          line: 194,
          column: 25
        }
      },
      "148": {
        start: {
          line: 190,
          column: 28
        },
        end: {
          line: 190,
          column: 49
        }
      },
      "149": {
        start: {
          line: 191,
          column: 28
        },
        end: {
          line: 191,
          column: 65
        }
      },
      "150": {
        start: {
          line: 192,
          column: 28
        },
        end: {
          line: 192,
          column: 65
        }
      },
      "151": {
        start: {
          line: 193,
          column: 28
        },
        end: {
          line: 193,
          column: 48
        }
      },
      "152": {
        start: {
          line: 195,
          column: 24
        },
        end: {
          line: 195,
          column: 48
        }
      },
      "153": {
        start: {
          line: 197,
          column: 24
        },
        end: {
          line: 197,
          column: 44
        }
      },
      "154": {
        start: {
          line: 198,
          column: 24
        },
        end: {
          line: 198,
          column: 73
        }
      },
      "155": {
        start: {
          line: 199,
          column: 24
        },
        end: {
          line: 199,
          column: 48
        }
      },
      "156": {
        start: {
          line: 201,
          column: 24
        },
        end: {
          line: 201,
          column: 67
        }
      },
      "157": {
        start: {
          line: 202,
          column: 24
        },
        end: {
          line: 202,
          column: 55
        }
      },
      "158": {
        start: {
          line: 210,
          column: 4
        },
        end: {
          line: 272,
          column: 6
        }
      },
      "159": {
        start: {
          line: 211,
          column: 8
        },
        end: {
          line: 271,
          column: 11
        }
      },
      "160": {
        start: {
          line: 213,
          column: 12
        },
        end: {
          line: 270,
          column: 15
        }
      },
      "161": {
        start: {
          line: 214,
          column: 16
        },
        end: {
          line: 269,
          column: 17
        }
      },
      "162": {
        start: {
          line: 216,
          column: 24
        },
        end: {
          line: 216,
          column: 67
        }
      },
      "163": {
        start: {
          line: 217,
          column: 24
        },
        end: {
          line: 217,
          column: 93
        }
      },
      "164": {
        start: {
          line: 218,
          column: 24
        },
        end: {
          line: 218,
          column: 72
        }
      },
      "165": {
        start: {
          line: 219,
          column: 24
        },
        end: {
          line: 223,
          column: 25
        }
      },
      "166": {
        start: {
          line: 220,
          column: 28
        },
        end: {
          line: 220,
          column: 65
        }
      },
      "167": {
        start: {
          line: 221,
          column: 28
        },
        end: {
          line: 221,
          column: 75
        }
      },
      "168": {
        start: {
          line: 222,
          column: 28
        },
        end: {
          line: 222,
          column: 62
        }
      },
      "169": {
        start: {
          line: 224,
          column: 24
        },
        end: {
          line: 224,
          column: 62
        }
      },
      "170": {
        start: {
          line: 225,
          column: 24
        },
        end: {
          line: 225,
          column: 37
        }
      },
      "171": {
        start: {
          line: 227,
          column: 24
        },
        end: {
          line: 227,
          column: 50
        }
      },
      "172": {
        start: {
          line: 228,
          column: 24
        },
        end: {
          line: 228,
          column: 73
        }
      },
      "173": {
        start: {
          line: 230,
          column: 24
        },
        end: {
          line: 230,
          column: 48
        }
      },
      "174": {
        start: {
          line: 231,
          column: 24
        },
        end: {
          line: 237,
          column: 25
        }
      },
      "175": {
        start: {
          line: 233,
          column: 28
        },
        end: {
          line: 233,
          column: 74
        }
      },
      "176": {
        start: {
          line: 234,
          column: 28
        },
        end: {
          line: 234,
          column: 75
        }
      },
      "177": {
        start: {
          line: 235,
          column: 28
        },
        end: {
          line: 235,
          column: 75
        }
      },
      "178": {
        start: {
          line: 236,
          column: 28
        },
        end: {
          line: 236,
          column: 62
        }
      },
      "179": {
        start: {
          line: 238,
          column: 24
        },
        end: {
          line: 253,
          column: 32
        }
      },
      "180": {
        start: {
          line: 255,
          column: 24
        },
        end: {
          line: 255,
          column: 49
        }
      },
      "181": {
        start: {
          line: 257,
          column: 24
        },
        end: {
          line: 257,
          column: 210
        }
      },
      "182": {
        start: {
          line: 258,
          column: 24
        },
        end: {
          line: 258,
          column: 71
        }
      },
      "183": {
        start: {
          line: 259,
          column: 24
        },
        end: {
          line: 259,
          column: 48
        }
      },
      "184": {
        start: {
          line: 261,
          column: 24
        },
        end: {
          line: 261,
          column: 44
        }
      },
      "185": {
        start: {
          line: 262,
          column: 24
        },
        end: {
          line: 262,
          column: 78
        }
      },
      "186": {
        start: {
          line: 263,
          column: 24
        },
        end: {
          line: 263,
          column: 70
        }
      },
      "187": {
        start: {
          line: 264,
          column: 24
        },
        end: {
          line: 264,
          column: 71
        }
      },
      "188": {
        start: {
          line: 265,
          column: 24
        },
        end: {
          line: 265,
          column: 48
        }
      },
      "189": {
        start: {
          line: 267,
          column: 24
        },
        end: {
          line: 267,
          column: 71
        }
      },
      "190": {
        start: {
          line: 268,
          column: 24
        },
        end: {
          line: 268,
          column: 58
        }
      },
      "191": {
        start: {
          line: 276,
          column: 4
        },
        end: {
          line: 320,
          column: 6
        }
      },
      "192": {
        start: {
          line: 277,
          column: 8
        },
        end: {
          line: 319,
          column: 11
        }
      },
      "193": {
        start: {
          line: 279,
          column: 12
        },
        end: {
          line: 318,
          column: 15
        }
      },
      "194": {
        start: {
          line: 280,
          column: 16
        },
        end: {
          line: 317,
          column: 17
        }
      },
      "195": {
        start: {
          line: 282,
          column: 24
        },
        end: {
          line: 282,
          column: 81
        }
      },
      "196": {
        start: {
          line: 283,
          column: 24
        },
        end: {
          line: 283,
          column: 62
        }
      },
      "197": {
        start: {
          line: 284,
          column: 24
        },
        end: {
          line: 287,
          column: 25
        }
      },
      "198": {
        start: {
          line: 285,
          column: 28
        },
        end: {
          line: 285,
          column: 66
        }
      },
      "199": {
        start: {
          line: 286,
          column: 28
        },
        end: {
          line: 286,
          column: 57
        }
      },
      "200": {
        start: {
          line: 288,
          column: 24
        },
        end: {
          line: 288,
          column: 63
        }
      },
      "201": {
        start: {
          line: 289,
          column: 24
        },
        end: {
          line: 289,
          column: 37
        }
      },
      "202": {
        start: {
          line: 291,
          column: 24
        },
        end: {
          line: 291,
          column: 50
        }
      },
      "203": {
        start: {
          line: 292,
          column: 24
        },
        end: {
          line: 304,
          column: 32
        }
      },
      "204": {
        start: {
          line: 306,
          column: 24
        },
        end: {
          line: 306,
          column: 42
        }
      },
      "205": {
        start: {
          line: 307,
          column: 24
        },
        end: {
          line: 309,
          column: 25
        }
      },
      "206": {
        start: {
          line: 308,
          column: 28
        },
        end: {
          line: 308,
          column: 65
        }
      },
      "207": {
        start: {
          line: 310,
          column: 24
        },
        end: {
          line: 310,
          column: 48
        }
      },
      "208": {
        start: {
          line: 312,
          column: 24
        },
        end: {
          line: 312,
          column: 44
        }
      },
      "209": {
        start: {
          line: 313,
          column: 24
        },
        end: {
          line: 313,
          column: 80
        }
      },
      "210": {
        start: {
          line: 314,
          column: 24
        },
        end: {
          line: 314,
          column: 37
        }
      },
      "211": {
        start: {
          line: 315,
          column: 24
        },
        end: {
          line: 315,
          column: 48
        }
      },
      "212": {
        start: {
          line: 316,
          column: 28
        },
        end: {
          line: 316,
          column: 57
        }
      },
      "213": {
        start: {
          line: 324,
          column: 4
        },
        end: {
          line: 378,
          column: 6
        }
      },
      "214": {
        start: {
          line: 325,
          column: 8
        },
        end: {
          line: 377,
          column: 11
        }
      },
      "215": {
        start: {
          line: 327,
          column: 24
        },
        end: {
          line: 327,
          column: 28
        }
      },
      "216": {
        start: {
          line: 328,
          column: 12
        },
        end: {
          line: 376,
          column: 15
        }
      },
      "217": {
        start: {
          line: 329,
          column: 16
        },
        end: {
          line: 375,
          column: 17
        }
      },
      "218": {
        start: {
          line: 331,
          column: 24
        },
        end: {
          line: 331,
          column: 67
        }
      },
      "219": {
        start: {
          line: 332,
          column: 24
        },
        end: {
          line: 364,
          column: 36
        }
      },
      "220": {
        start: {
          line: 332,
          column: 90
        },
        end: {
          line: 364,
          column: 31
        }
      },
      "221": {
        start: {
          line: 334,
          column: 32
        },
        end: {
          line: 363,
          column: 35
        }
      },
      "222": {
        start: {
          line: 335,
          column: 36
        },
        end: {
          line: 362,
          column: 37
        }
      },
      "223": {
        start: {
          line: 337,
          column: 44
        },
        end: {
          line: 337,
          column: 68
        }
      },
      "224": {
        start: {
          line: 338,
          column: 44
        },
        end: {
          line: 338,
          column: 80
        }
      },
      "225": {
        start: {
          line: 339,
          column: 44
        },
        end: {
          line: 339,
          column: 57
        }
      },
      "226": {
        start: {
          line: 341,
          column: 44
        },
        end: {
          line: 341,
          column: 102
        }
      },
      "227": {
        start: {
          line: 341,
          column: 78
        },
        end: {
          line: 341,
          column: 102
        }
      },
      "228": {
        start: {
          line: 342,
          column: 44
        },
        end: {
          line: 342,
          column: 75
        }
      },
      "229": {
        start: {
          line: 343,
          column: 44
        },
        end: {
          line: 353,
          column: 52
        }
      },
      "230": {
        start: {
          line: 355,
          column: 44
        },
        end: {
          line: 355,
          column: 64
        }
      },
      "231": {
        start: {
          line: 356,
          column: 44
        },
        end: {
          line: 356,
          column: 77
        }
      },
      "232": {
        start: {
          line: 357,
          column: 44
        },
        end: {
          line: 357,
          column: 57
        }
      },
      "233": {
        start: {
          line: 359,
          column: 44
        },
        end: {
          line: 359,
          column: 49
        }
      },
      "234": {
        start: {
          line: 360,
          column: 44
        },
        end: {
          line: 360,
          column: 68
        }
      },
      "235": {
        start: {
          line: 361,
          column: 48
        },
        end: {
          line: 361,
          column: 90
        }
      },
      "236": {
        start: {
          line: 366,
          column: 24
        },
        end: {
          line: 366,
          column: 44
        }
      },
      "237": {
        start: {
          line: 368,
          column: 24
        },
        end: {
          line: 372,
          column: 25
        }
      },
      "238": {
        start: {
          line: 369,
          column: 28
        },
        end: {
          line: 369,
          column: 55
        }
      },
      "239": {
        start: {
          line: 370,
          column: 28
        },
        end: {
          line: 370,
          column: 111
        }
      },
      "240": {
        start: {
          line: 371,
          column: 28
        },
        end: {
          line: 371,
          column: 75
        }
      },
      "241": {
        start: {
          line: 373,
          column: 24
        },
        end: {
          line: 373,
          column: 80
        }
      },
      "242": {
        start: {
          line: 374,
          column: 24
        },
        end: {
          line: 374,
          column: 55
        }
      },
      "243": {
        start: {
          line: 382,
          column: 4
        },
        end: {
          line: 427,
          column: 6
        }
      },
      "244": {
        start: {
          line: 383,
          column: 8
        },
        end: {
          line: 426,
          column: 11
        }
      },
      "245": {
        start: {
          line: 384,
          column: 24
        },
        end: {
          line: 384,
          column: 28
        }
      },
      "246": {
        start: {
          line: 385,
          column: 12
        },
        end: {
          line: 385,
          column: 54
        }
      },
      "247": {
        start: {
          line: 385,
          column: 39
        },
        end: {
          line: 385,
          column: 52
        }
      },
      "248": {
        start: {
          line: 386,
          column: 12
        },
        end: {
          line: 425,
          column: 15
        }
      },
      "249": {
        start: {
          line: 387,
          column: 16
        },
        end: {
          line: 424,
          column: 24
        }
      },
      "250": {
        start: {
          line: 388,
          column: 24
        },
        end: {
          line: 390,
          column: 25
        }
      },
      "251": {
        start: {
          line: 389,
          column: 28
        },
        end: {
          line: 389,
          column: 64
        }
      },
      "252": {
        start: {
          line: 391,
          column: 36
        },
        end: {
          line: 391,
          column: 67
        }
      },
      "253": {
        start: {
          line: 392,
          column: 24
        },
        end: {
          line: 414,
          column: 27
        }
      },
      "254": {
        start: {
          line: 394,
          column: 53
        },
        end: {
          line: 412,
          column: 31
        }
      },
      "255": {
        start: {
          line: 396,
          column: 32
        },
        end: {
          line: 411,
          column: 35
        }
      },
      "256": {
        start: {
          line: 397,
          column: 36
        },
        end: {
          line: 410,
          column: 37
        }
      },
      "257": {
        start: {
          line: 399,
          column: 44
        },
        end: {
          line: 399,
          column: 70
        }
      },
      "258": {
        start: {
          line: 400,
          column: 44
        },
        end: {
          line: 400,
          column: 78
        }
      },
      "259": {
        start: {
          line: 402,
          column: 44
        },
        end: {
          line: 402,
          column: 63
        }
      },
      "260": {
        start: {
          line: 403,
          column: 44
        },
        end: {
          line: 403,
          column: 60
        }
      },
      "261": {
        start: {
          line: 404,
          column: 44
        },
        end: {
          line: 404,
          column: 74
        }
      },
      "262": {
        start: {
          line: 406,
          column: 44
        },
        end: {
          line: 406,
          column: 64
        }
      },
      "263": {
        start: {
          line: 407,
          column: 44
        },
        end: {
          line: 407,
          column: 60
        }
      },
      "264": {
        start: {
          line: 408,
          column: 44
        },
        end: {
          line: 408,
          column: 58
        }
      },
      "265": {
        start: {
          line: 409,
          column: 48
        },
        end: {
          line: 409,
          column: 70
        }
      },
      "266": {
        start: {
          line: 416,
          column: 24
        },
        end: {
          line: 416,
          column: 88
        }
      },
      "267": {
        start: {
          line: 416,
          column: 53
        },
        end: {
          line: 416,
          column: 84
        }
      },
      "268": {
        start: {
          line: 418,
          column: 24
        },
        end: {
          line: 423,
          column: 25
        }
      },
      "269": {
        start: {
          line: 419,
          column: 28
        },
        end: {
          line: 419,
          column: 58
        }
      },
      "270": {
        start: {
          line: 422,
          column: 28
        },
        end: {
          line: 422,
          column: 69
        }
      },
      "271": {
        start: {
          line: 431,
          column: 4
        },
        end: {
          line: 456,
          column: 6
        }
      },
      "272": {
        start: {
          line: 432,
          column: 8
        },
        end: {
          line: 455,
          column: 11
        }
      },
      "273": {
        start: {
          line: 434,
          column: 12
        },
        end: {
          line: 454,
          column: 15
        }
      },
      "274": {
        start: {
          line: 435,
          column: 16
        },
        end: {
          line: 453,
          column: 17
        }
      },
      "275": {
        start: {
          line: 437,
          column: 24
        },
        end: {
          line: 437,
          column: 63
        }
      },
      "276": {
        start: {
          line: 438,
          column: 24
        },
        end: {
          line: 439,
          column: 50
        }
      },
      "277": {
        start: {
          line: 439,
          column: 28
        },
        end: {
          line: 439,
          column: 50
        }
      },
      "278": {
        start: {
          line: 441,
          column: 24
        },
        end: {
          line: 441,
          column: 59
        }
      },
      "279": {
        start: {
          line: 442,
          column: 24
        },
        end: {
          line: 442,
          column: 64
        }
      },
      "280": {
        start: {
          line: 443,
          column: 24
        },
        end: {
          line: 446,
          column: 25
        }
      },
      "281": {
        start: {
          line: 444,
          column: 28
        },
        end: {
          line: 444,
          column: 48
        }
      },
      "282": {
        start: {
          line: 445,
          column: 28
        },
        end: {
          line: 445,
          column: 63
        }
      },
      "283": {
        start: {
          line: 448,
          column: 24
        },
        end: {
          line: 448,
          column: 122
        }
      },
      "284": {
        start: {
          line: 448,
          column: 92
        },
        end: {
          line: 448,
          column: 116
        }
      },
      "285": {
        start: {
          line: 451,
          column: 24
        },
        end: {
          line: 451,
          column: 34
        }
      },
      "286": {
        start: {
          line: 452,
          column: 24
        },
        end: {
          line: 452,
          column: 46
        }
      },
      "287": {
        start: {
          line: 460,
          column: 4
        },
        end: {
          line: 468,
          column: 6
        }
      },
      "288": {
        start: {
          line: 461,
          column: 20
        },
        end: {
          line: 461,
          column: 24
        }
      },
      "289": {
        start: {
          line: 462,
          column: 8
        },
        end: {
          line: 463,
          column: 19
        }
      },
      "290": {
        start: {
          line: 463,
          column: 12
        },
        end: {
          line: 463,
          column: 19
        }
      },
      "291": {
        start: {
          line: 464,
          column: 20
        },
        end: {
          line: 466,
          column: 28
        }
      },
      "292": {
        start: {
          line: 465,
          column: 12
        },
        end: {
          line: 465,
          column: 42
        }
      },
      "293": {
        start: {
          line: 467,
          column: 8
        },
        end: {
          line: 467,
          column: 47
        }
      },
      "294": {
        start: {
          line: 472,
          column: 4
        },
        end: {
          line: 485,
          column: 6
        }
      },
      "295": {
        start: {
          line: 473,
          column: 8
        },
        end: {
          line: 484,
          column: 10
        }
      },
      "296": {
        start: {
          line: 489,
          column: 4
        },
        end: {
          line: 492,
          column: 6
        }
      },
      "297": {
        start: {
          line: 490,
          column: 22
        },
        end: {
          line: 490,
          column: 51
        }
      },
      "298": {
        start: {
          line: 491,
          column: 8
        },
        end: {
          line: 491,
          column: 46
        }
      },
      "299": {
        start: {
          line: 496,
          column: 4
        },
        end: {
          line: 499,
          column: 6
        }
      },
      "300": {
        start: {
          line: 497,
          column: 22
        },
        end: {
          line: 497,
          column: 53
        }
      },
      "301": {
        start: {
          line: 498,
          column: 8
        },
        end: {
          line: 498,
          column: 48
        }
      },
      "302": {
        start: {
          line: 503,
          column: 4
        },
        end: {
          line: 519,
          column: 6
        }
      },
      "303": {
        start: {
          line: 504,
          column: 22
        },
        end: {
          line: 504,
          column: 52
        }
      },
      "304": {
        start: {
          line: 505,
          column: 24
        },
        end: {
          line: 505,
          column: 43
        }
      },
      "305": {
        start: {
          line: 506,
          column: 22
        },
        end: {
          line: 506,
          column: 62
        }
      },
      "306": {
        start: {
          line: 507,
          column: 8
        },
        end: {
          line: 507,
          column: 57
        }
      },
      "307": {
        start: {
          line: 509,
          column: 19
        },
        end: {
          line: 509,
          column: 53
        }
      },
      "308": {
        start: {
          line: 510,
          column: 21
        },
        end: {
          line: 510,
          column: 57
        }
      },
      "309": {
        start: {
          line: 511,
          column: 20
        },
        end: {
          line: 511,
          column: 33
        }
      },
      "310": {
        start: {
          line: 512,
          column: 27
        },
        end: {
          line: 512,
          column: 55
        }
      },
      "311": {
        start: {
          line: 513,
          column: 8
        },
        end: {
          line: 518,
          column: 11
        }
      },
      "312": {
        start: {
          line: 523,
          column: 4
        },
        end: {
          line: 525,
          column: 6
        }
      },
      "313": {
        start: {
          line: 524,
          column: 8
        },
        end: {
          line: 524,
          column: 37
        }
      },
      "314": {
        start: {
          line: 529,
          column: 4
        },
        end: {
          line: 534,
          column: 6
        }
      },
      "315": {
        start: {
          line: 530,
          column: 8
        },
        end: {
          line: 530,
          column: 31
        }
      },
      "316": {
        start: {
          line: 531,
          column: 8
        },
        end: {
          line: 531,
          column: 32
        }
      },
      "317": {
        start: {
          line: 532,
          column: 8
        },
        end: {
          line: 532,
          column: 37
        }
      },
      "318": {
        start: {
          line: 533,
          column: 8
        },
        end: {
          line: 533,
          column: 37
        }
      },
      "319": {
        start: {
          line: 538,
          column: 4
        },
        end: {
          line: 570,
          column: 6
        }
      },
      "320": {
        start: {
          line: 540,
          column: 24
        },
        end: {
          line: 540,
          column: 58
        }
      },
      "321": {
        start: {
          line: 541,
          column: 26
        },
        end: {
          line: 541,
          column: 62
        }
      },
      "322": {
        start: {
          line: 542,
          column: 24
        },
        end: {
          line: 542,
          column: 108
        }
      },
      "323": {
        start: {
          line: 543,
          column: 26
        },
        end: {
          line: 543,
          column: 114
        }
      },
      "324": {
        start: {
          line: 544,
          column: 30
        },
        end: {
          line: 544,
          column: 70
        }
      },
      "325": {
        start: {
          line: 545,
          column: 32
        },
        end: {
          line: 545,
          column: 74
        }
      },
      "326": {
        start: {
          line: 546,
          column: 30
        },
        end: {
          line: 546,
          column: 67
        }
      },
      "327": {
        start: {
          line: 547,
          column: 32
        },
        end: {
          line: 547,
          column: 71
        }
      },
      "328": {
        start: {
          line: 548,
          column: 8
        },
        end: {
          line: 569,
          column: 10
        }
      },
      "329": {
        start: {
          line: 571,
          column: 4
        },
        end: {
          line: 571,
          column: 40
        }
      },
      "330": {
        start: {
          line: 573,
          column: 0
        },
        end: {
          line: 573,
          column: 68
        }
      },
      "331": {
        start: {
          line: 575,
          column: 0
        },
        end: {
          line: 575,
          column: 82
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 58,
            column: 50
          },
          end: {
            line: 58,
            column: 51
          }
        },
        loc: {
          start: {
            line: 58,
            column: 62
          },
          end: {
            line: 572,
            column: 1
          }
        },
        line: 58
      },
      "16": {
        name: "SkillGapPerformanceOptimizer",
        decl: {
          start: {
            line: 59,
            column: 13
          },
          end: {
            line: 59,
            column: 41
          }
        },
        loc: {
          start: {
            line: 59,
            column: 44
          },
          end: {
            line: 87,
            column: 5
          }
        },
        line: 59
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 88,
            column: 47
          },
          end: {
            line: 88,
            column: 48
          }
        },
        loc: {
          start: {
            line: 88,
            column: 59
          },
          end: {
            line: 93,
            column: 5
          }
        },
        line: 88
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 97,
            column: 53
          },
          end: {
            line: 97,
            column: 54
          }
        },
        loc: {
          start: {
            line: 97,
            column: 71
          },
          end: {
            line: 143,
            column: 5
          }
        },
        line: 97
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 98,
            column: 48
          },
          end: {
            line: 98,
            column: 49
          }
        },
        loc: {
          start: {
            line: 98,
            column: 60
          },
          end: {
            line: 142,
            column: 9
          }
        },
        line: 98
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 100,
            column: 37
          },
          end: {
            line: 100,
            column: 38
          }
        },
        loc: {
          start: {
            line: 100,
            column: 51
          },
          end: {
            line: 141,
            column: 13
          }
        },
        line: 100
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 147,
            column: 55
          },
          end: {
            line: 147,
            column: 56
          }
        },
        loc: {
          start: {
            line: 147,
            column: 75
          },
          end: {
            line: 206,
            column: 5
          }
        },
        line: 147
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 148,
            column: 48
          },
          end: {
            line: 148,
            column: 49
          }
        },
        loc: {
          start: {
            line: 148,
            column: 60
          },
          end: {
            line: 205,
            column: 9
          }
        },
        line: 148
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 150,
            column: 37
          },
          end: {
            line: 150,
            column: 38
          }
        },
        loc: {
          start: {
            line: 150,
            column: 51
          },
          end: {
            line: 204,
            column: 13
          }
        },
        line: 150
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 210,
            column: 59
          },
          end: {
            line: 210,
            column: 60
          }
        },
        loc: {
          start: {
            line: 210,
            column: 86
          },
          end: {
            line: 272,
            column: 5
          }
        },
        line: 210
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 211,
            column: 48
          },
          end: {
            line: 211,
            column: 49
          }
        },
        loc: {
          start: {
            line: 211,
            column: 60
          },
          end: {
            line: 271,
            column: 9
          }
        },
        line: 211
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 213,
            column: 37
          },
          end: {
            line: 213,
            column: 38
          }
        },
        loc: {
          start: {
            line: 213,
            column: 51
          },
          end: {
            line: 270,
            column: 13
          }
        },
        line: 213
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 276,
            column: 60
          },
          end: {
            line: 276,
            column: 61
          }
        },
        loc: {
          start: {
            line: 276,
            column: 81
          },
          end: {
            line: 320,
            column: 5
          }
        },
        line: 276
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 277,
            column: 48
          },
          end: {
            line: 277,
            column: 49
          }
        },
        loc: {
          start: {
            line: 277,
            column: 60
          },
          end: {
            line: 319,
            column: 9
          }
        },
        line: 277
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 279,
            column: 37
          },
          end: {
            line: 279,
            column: 38
          }
        },
        loc: {
          start: {
            line: 279,
            column: 51
          },
          end: {
            line: 318,
            column: 13
          }
        },
        line: 279
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 324,
            column: 68
          },
          end: {
            line: 324,
            column: 69
          }
        },
        loc: {
          start: {
            line: 324,
            column: 91
          },
          end: {
            line: 378,
            column: 5
          }
        },
        line: 324
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 325,
            column: 48
          },
          end: {
            line: 325,
            column: 49
          }
        },
        loc: {
          start: {
            line: 325,
            column: 60
          },
          end: {
            line: 377,
            column: 9
          }
        },
        line: 325
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 328,
            column: 37
          },
          end: {
            line: 328,
            column: 38
          }
        },
        loc: {
          start: {
            line: 328,
            column: 51
          },
          end: {
            line: 376,
            column: 13
          }
        },
        line: 328
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 332,
            column: 74
          },
          end: {
            line: 332,
            column: 75
          }
        },
        loc: {
          start: {
            line: 332,
            column: 88
          },
          end: {
            line: 364,
            column: 33
          }
        },
        line: 332
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 332,
            column: 130
          },
          end: {
            line: 332,
            column: 131
          }
        },
        loc: {
          start: {
            line: 332,
            column: 142
          },
          end: {
            line: 364,
            column: 29
          }
        },
        line: 332
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 334,
            column: 57
          },
          end: {
            line: 334,
            column: 58
          }
        },
        loc: {
          start: {
            line: 334,
            column: 71
          },
          end: {
            line: 363,
            column: 33
          }
        },
        line: 334
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 382,
            column: 56
          },
          end: {
            line: 382,
            column: 57
          }
        },
        loc: {
          start: {
            line: 382,
            column: 107
          },
          end: {
            line: 427,
            column: 5
          }
        },
        line: 382
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 383,
            column: 51
          },
          end: {
            line: 383,
            column: 52
          }
        },
        loc: {
          start: {
            line: 383,
            column: 106
          },
          end: {
            line: 426,
            column: 9
          }
        },
        line: 383
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 386,
            column: 37
          },
          end: {
            line: 386,
            column: 38
          }
        },
        loc: {
          start: {
            line: 386,
            column: 51
          },
          end: {
            line: 425,
            column: 13
          }
        },
        line: 386
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 387,
            column: 50
          },
          end: {
            line: 387,
            column: 51
          }
        },
        loc: {
          start: {
            line: 387,
            column: 77
          },
          end: {
            line: 424,
            column: 21
          }
        },
        line: 387
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 394,
            column: 39
          },
          end: {
            line: 394,
            column: 40
          }
        },
        loc: {
          start: {
            line: 394,
            column: 51
          },
          end: {
            line: 412,
            column: 33
          }
        },
        line: 394
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 394,
            column: 93
          },
          end: {
            line: 394,
            column: 94
          }
        },
        loc: {
          start: {
            line: 394,
            column: 105
          },
          end: {
            line: 412,
            column: 29
          }
        },
        line: 394
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 396,
            column: 57
          },
          end: {
            line: 396,
            column: 58
          }
        },
        loc: {
          start: {
            line: 396,
            column: 71
          },
          end: {
            line: 411,
            column: 33
          }
        },
        line: 396
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 416,
            column: 35
          },
          end: {
            line: 416,
            column: 36
          }
        },
        loc: {
          start: {
            line: 416,
            column: 51
          },
          end: {
            line: 416,
            column: 86
          }
        },
        line: 416
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 431,
            column: 58
          },
          end: {
            line: 431,
            column: 59
          }
        },
        loc: {
          start: {
            line: 431,
            column: 79
          },
          end: {
            line: 456,
            column: 5
          }
        },
        line: 431
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 432,
            column: 48
          },
          end: {
            line: 432,
            column: 49
          }
        },
        loc: {
          start: {
            line: 432,
            column: 60
          },
          end: {
            line: 455,
            column: 9
          }
        },
        line: 432
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 434,
            column: 37
          },
          end: {
            line: 434,
            column: 38
          }
        },
        loc: {
          start: {
            line: 434,
            column: 51
          },
          end: {
            line: 454,
            column: 13
          }
        },
        line: 434
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 448,
            column: 74
          },
          end: {
            line: 448,
            column: 75
          }
        },
        loc: {
          start: {
            line: 448,
            column: 90
          },
          end: {
            line: 448,
            column: 118
          }
        },
        line: 448
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 460,
            column: 69
          },
          end: {
            line: 460,
            column: 70
          }
        },
        loc: {
          start: {
            line: 460,
            column: 90
          },
          end: {
            line: 468,
            column: 5
          }
        },
        line: 460
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 464,
            column: 31
          },
          end: {
            line: 464,
            column: 32
          }
        },
        loc: {
          start: {
            line: 464,
            column: 43
          },
          end: {
            line: 466,
            column: 9
          }
        },
        line: 464
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 472,
            column: 66
          },
          end: {
            line: 472,
            column: 67
          }
        },
        loc: {
          start: {
            line: 472,
            column: 83
          },
          end: {
            line: 485,
            column: 5
          }
        },
        line: 472
      },
      "51": {
        name: "(anonymous_51)",
        decl: {
          start: {
            line: 489,
            column: 60
          },
          end: {
            line: 489,
            column: 61
          }
        },
        loc: {
          start: {
            line: 489,
            column: 76
          },
          end: {
            line: 492,
            column: 5
          }
        },
        line: 489
      },
      "52": {
        name: "(anonymous_52)",
        decl: {
          start: {
            line: 496,
            column: 61
          },
          end: {
            line: 496,
            column: 62
          }
        },
        loc: {
          start: {
            line: 496,
            column: 77
          },
          end: {
            line: 499,
            column: 5
          }
        },
        line: 496
      },
      "53": {
        name: "(anonymous_53)",
        decl: {
          start: {
            line: 503,
            column: 59
          },
          end: {
            line: 503,
            column: 60
          }
        },
        loc: {
          start: {
            line: 503,
            column: 91
          },
          end: {
            line: 519,
            column: 5
          }
        },
        line: 503
      },
      "54": {
        name: "(anonymous_54)",
        decl: {
          start: {
            line: 523,
            column: 67
          },
          end: {
            line: 523,
            column: 68
          }
        },
        loc: {
          start: {
            line: 523,
            column: 79
          },
          end: {
            line: 525,
            column: 5
          }
        },
        line: 523
      },
      "55": {
        name: "(anonymous_55)",
        decl: {
          start: {
            line: 529,
            column: 57
          },
          end: {
            line: 529,
            column: 58
          }
        },
        loc: {
          start: {
            line: 529,
            column: 69
          },
          end: {
            line: 534,
            column: 5
          }
        },
        line: 529
      },
      "56": {
        name: "(anonymous_56)",
        decl: {
          start: {
            line: 538,
            column: 59
          },
          end: {
            line: 538,
            column: 60
          }
        },
        loc: {
          start: {
            line: 538,
            column: 71
          },
          end: {
            line: 570,
            column: 5
          }
        },
        line: 538
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 89,
            column: 8
          },
          end: {
            line: 91,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 89,
            column: 8
          },
          end: {
            line: 91,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 89
      },
      "36": {
        loc: {
          start: {
            line: 101,
            column: 16
          },
          end: {
            line: 140,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 102,
            column: 20
          },
          end: {
            line: 112,
            column: 37
          }
        }, {
          start: {
            line: 113,
            column: 20
          },
          end: {
            line: 124,
            column: 32
          }
        }, {
          start: {
            line: 125,
            column: 20
          },
          end: {
            line: 131,
            column: 48
          }
        }, {
          start: {
            line: 132,
            column: 20
          },
          end: {
            line: 136,
            column: 48
          }
        }, {
          start: {
            line: 137,
            column: 20
          },
          end: {
            line: 139,
            column: 52
          }
        }],
        line: 101
      },
      "37": {
        loc: {
          start: {
            line: 106,
            column: 24
          },
          end: {
            line: 110,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 106,
            column: 24
          },
          end: {
            line: 110,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 106
      },
      "38": {
        loc: {
          start: {
            line: 128,
            column: 24
          },
          end: {
            line: 130,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 128,
            column: 24
          },
          end: {
            line: 130,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 128
      },
      "39": {
        loc: {
          start: {
            line: 151,
            column: 16
          },
          end: {
            line: 203,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 152,
            column: 20
          },
          end: {
            line: 171,
            column: 37
          }
        }, {
          start: {
            line: 172,
            column: 20
          },
          end: {
            line: 185,
            column: 32
          }
        }, {
          start: {
            line: 186,
            column: 20
          },
          end: {
            line: 195,
            column: 48
          }
        }, {
          start: {
            line: 196,
            column: 20
          },
          end: {
            line: 199,
            column: 48
          }
        }, {
          start: {
            line: 200,
            column: 20
          },
          end: {
            line: 202,
            column: 55
          }
        }],
        line: 151
      },
      "40": {
        loc: {
          start: {
            line: 161,
            column: 28
          },
          end: {
            line: 168,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 161,
            column: 28
          },
          end: {
            line: 168,
            column: 29
          }
        }, {
          start: {
            line: 165,
            column: 33
          },
          end: {
            line: 168,
            column: 29
          }
        }],
        line: 161
      },
      "41": {
        loc: {
          start: {
            line: 170,
            column: 24
          },
          end: {
            line: 170,
            column: 79
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 170,
            column: 24
          },
          end: {
            line: 170,
            column: 79
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 170
      },
      "42": {
        loc: {
          start: {
            line: 214,
            column: 16
          },
          end: {
            line: 269,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 215,
            column: 20
          },
          end: {
            line: 225,
            column: 37
          }
        }, {
          start: {
            line: 226,
            column: 20
          },
          end: {
            line: 228,
            column: 73
          }
        }, {
          start: {
            line: 229,
            column: 20
          },
          end: {
            line: 253,
            column: 32
          }
        }, {
          start: {
            line: 254,
            column: 20
          },
          end: {
            line: 259,
            column: 48
          }
        }, {
          start: {
            line: 260,
            column: 20
          },
          end: {
            line: 265,
            column: 48
          }
        }, {
          start: {
            line: 266,
            column: 20
          },
          end: {
            line: 268,
            column: 58
          }
        }],
        line: 214
      },
      "43": {
        loc: {
          start: {
            line: 217,
            column: 71
          },
          end: {
            line: 217,
            column: 91
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 217,
            column: 71
          },
          end: {
            line: 217,
            column: 79
          }
        }, {
          start: {
            line: 217,
            column: 83
          },
          end: {
            line: 217,
            column: 91
          }
        }],
        line: 217
      },
      "44": {
        loc: {
          start: {
            line: 219,
            column: 24
          },
          end: {
            line: 223,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 219,
            column: 24
          },
          end: {
            line: 223,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 219
      },
      "45": {
        loc: {
          start: {
            line: 231,
            column: 24
          },
          end: {
            line: 237,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 231,
            column: 24
          },
          end: {
            line: 237,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 231
      },
      "46": {
        loc: {
          start: {
            line: 257,
            column: 37
          },
          end: {
            line: 257,
            column: 209
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 257,
            column: 52
          },
          end: {
            line: 257,
            column: 174
          }
        }, {
          start: {
            line: 257,
            column: 177
          },
          end: {
            line: 257,
            column: 209
          }
        }],
        line: 257
      },
      "47": {
        loc: {
          start: {
            line: 280,
            column: 16
          },
          end: {
            line: 317,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 281,
            column: 20
          },
          end: {
            line: 289,
            column: 37
          }
        }, {
          start: {
            line: 290,
            column: 20
          },
          end: {
            line: 304,
            column: 32
          }
        }, {
          start: {
            line: 305,
            column: 20
          },
          end: {
            line: 310,
            column: 48
          }
        }, {
          start: {
            line: 311,
            column: 20
          },
          end: {
            line: 315,
            column: 48
          }
        }, {
          start: {
            line: 316,
            column: 20
          },
          end: {
            line: 316,
            column: 57
          }
        }],
        line: 280
      },
      "48": {
        loc: {
          start: {
            line: 284,
            column: 24
          },
          end: {
            line: 287,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 284,
            column: 24
          },
          end: {
            line: 287,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 284
      },
      "49": {
        loc: {
          start: {
            line: 307,
            column: 24
          },
          end: {
            line: 309,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 307,
            column: 24
          },
          end: {
            line: 309,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 307
      },
      "50": {
        loc: {
          start: {
            line: 329,
            column: 16
          },
          end: {
            line: 375,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 330,
            column: 20
          },
          end: {
            line: 364,
            column: 36
          }
        }, {
          start: {
            line: 365,
            column: 20
          },
          end: {
            line: 374,
            column: 55
          }
        }],
        line: 329
      },
      "51": {
        loc: {
          start: {
            line: 335,
            column: 36
          },
          end: {
            line: 362,
            column: 37
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 336,
            column: 40
          },
          end: {
            line: 339,
            column: 57
          }
        }, {
          start: {
            line: 340,
            column: 40
          },
          end: {
            line: 353,
            column: 52
          }
        }, {
          start: {
            line: 354,
            column: 40
          },
          end: {
            line: 357,
            column: 57
          }
        }, {
          start: {
            line: 358,
            column: 40
          },
          end: {
            line: 360,
            column: 68
          }
        }, {
          start: {
            line: 361,
            column: 40
          },
          end: {
            line: 361,
            column: 90
          }
        }],
        line: 335
      },
      "52": {
        loc: {
          start: {
            line: 341,
            column: 44
          },
          end: {
            line: 341,
            column: 102
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 341,
            column: 44
          },
          end: {
            line: 341,
            column: 102
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 341
      },
      "53": {
        loc: {
          start: {
            line: 385,
            column: 12
          },
          end: {
            line: 385,
            column: 54
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 385,
            column: 12
          },
          end: {
            line: 385,
            column: 54
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 385
      },
      "54": {
        loc: {
          start: {
            line: 388,
            column: 24
          },
          end: {
            line: 390,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 388,
            column: 24
          },
          end: {
            line: 390,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 388
      },
      "55": {
        loc: {
          start: {
            line: 397,
            column: 36
          },
          end: {
            line: 410,
            column: 37
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 398,
            column: 40
          },
          end: {
            line: 400,
            column: 78
          }
        }, {
          start: {
            line: 401,
            column: 40
          },
          end: {
            line: 404,
            column: 74
          }
        }, {
          start: {
            line: 405,
            column: 40
          },
          end: {
            line: 408,
            column: 58
          }
        }, {
          start: {
            line: 409,
            column: 40
          },
          end: {
            line: 409,
            column: 70
          }
        }],
        line: 397
      },
      "56": {
        loc: {
          start: {
            line: 418,
            column: 24
          },
          end: {
            line: 423,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 418,
            column: 24
          },
          end: {
            line: 423,
            column: 25
          }
        }, {
          start: {
            line: 421,
            column: 29
          },
          end: {
            line: 423,
            column: 25
          }
        }],
        line: 418
      },
      "57": {
        loc: {
          start: {
            line: 435,
            column: 16
          },
          end: {
            line: 453,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 436,
            column: 20
          },
          end: {
            line: 448,
            column: 122
          }
        }, {
          start: {
            line: 449,
            column: 20
          },
          end: {
            line: 452,
            column: 46
          }
        }],
        line: 435
      },
      "58": {
        loc: {
          start: {
            line: 438,
            column: 24
          },
          end: {
            line: 439,
            column: 50
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 438,
            column: 24
          },
          end: {
            line: 439,
            column: 50
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 438
      },
      "59": {
        loc: {
          start: {
            line: 438,
            column: 28
          },
          end: {
            line: 438,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 438,
            column: 28
          },
          end: {
            line: 438,
            column: 34
          }
        }, {
          start: {
            line: 438,
            column: 38
          },
          end: {
            line: 438,
            column: 56
          }
        }],
        line: 438
      },
      "60": {
        loc: {
          start: {
            line: 443,
            column: 24
          },
          end: {
            line: 446,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 443,
            column: 24
          },
          end: {
            line: 446,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 443
      },
      "61": {
        loc: {
          start: {
            line: 462,
            column: 8
          },
          end: {
            line: 463,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 462,
            column: 8
          },
          end: {
            line: 463,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 462
      },
      "62": {
        loc: {
          start: {
            line: 490,
            column: 22
          },
          end: {
            line: 490,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 490,
            column: 22
          },
          end: {
            line: 490,
            column: 46
          }
        }, {
          start: {
            line: 490,
            column: 50
          },
          end: {
            line: 490,
            column: 51
          }
        }],
        line: 490
      },
      "63": {
        loc: {
          start: {
            line: 497,
            column: 22
          },
          end: {
            line: 497,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 497,
            column: 22
          },
          end: {
            line: 497,
            column: 48
          }
        }, {
          start: {
            line: 497,
            column: 52
          },
          end: {
            line: 497,
            column: 53
          }
        }],
        line: 497
      },
      "64": {
        loc: {
          start: {
            line: 506,
            column: 22
          },
          end: {
            line: 506,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 506,
            column: 22
          },
          end: {
            line: 506,
            column: 57
          }
        }, {
          start: {
            line: 506,
            column: 61
          },
          end: {
            line: 506,
            column: 62
          }
        }],
        line: 506
      },
      "65": {
        loc: {
          start: {
            line: 509,
            column: 19
          },
          end: {
            line: 509,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 509,
            column: 19
          },
          end: {
            line: 509,
            column: 48
          }
        }, {
          start: {
            line: 509,
            column: 52
          },
          end: {
            line: 509,
            column: 53
          }
        }],
        line: 509
      },
      "66": {
        loc: {
          start: {
            line: 510,
            column: 21
          },
          end: {
            line: 510,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 510,
            column: 21
          },
          end: {
            line: 510,
            column: 52
          }
        }, {
          start: {
            line: 510,
            column: 56
          },
          end: {
            line: 510,
            column: 57
          }
        }],
        line: 510
      },
      "67": {
        loc: {
          start: {
            line: 512,
            column: 27
          },
          end: {
            line: 512,
            column: 55
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 512,
            column: 39
          },
          end: {
            line: 512,
            column: 51
          }
        }, {
          start: {
            line: 512,
            column: 54
          },
          end: {
            line: 512,
            column: 55
          }
        }],
        line: 512
      },
      "68": {
        loc: {
          start: {
            line: 540,
            column: 24
          },
          end: {
            line: 540,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 540,
            column: 24
          },
          end: {
            line: 540,
            column: 53
          }
        }, {
          start: {
            line: 540,
            column: 57
          },
          end: {
            line: 540,
            column: 58
          }
        }],
        line: 540
      },
      "69": {
        loc: {
          start: {
            line: 541,
            column: 26
          },
          end: {
            line: 541,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 541,
            column: 26
          },
          end: {
            line: 541,
            column: 57
          }
        }, {
          start: {
            line: 541,
            column: 61
          },
          end: {
            line: 541,
            column: 62
          }
        }],
        line: 541
      },
      "70": {
        loc: {
          start: {
            line: 542,
            column: 25
          },
          end: {
            line: 542,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 542,
            column: 25
          },
          end: {
            line: 542,
            column: 56
          }
        }, {
          start: {
            line: 542,
            column: 60
          },
          end: {
            line: 542,
            column: 61
          }
        }],
        line: 542
      },
      "71": {
        loc: {
          start: {
            line: 542,
            column: 66
          },
          end: {
            line: 542,
            column: 107
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 542,
            column: 66
          },
          end: {
            line: 542,
            column: 102
          }
        }, {
          start: {
            line: 542,
            column: 106
          },
          end: {
            line: 542,
            column: 107
          }
        }],
        line: 542
      },
      "72": {
        loc: {
          start: {
            line: 543,
            column: 27
          },
          end: {
            line: 543,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 543,
            column: 27
          },
          end: {
            line: 543,
            column: 60
          }
        }, {
          start: {
            line: 543,
            column: 64
          },
          end: {
            line: 543,
            column: 65
          }
        }],
        line: 543
      },
      "73": {
        loc: {
          start: {
            line: 543,
            column: 70
          },
          end: {
            line: 543,
            column: 113
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 543,
            column: 70
          },
          end: {
            line: 543,
            column: 108
          }
        }, {
          start: {
            line: 543,
            column: 112
          },
          end: {
            line: 543,
            column: 113
          }
        }],
        line: 543
      },
      "74": {
        loc: {
          start: {
            line: 544,
            column: 30
          },
          end: {
            line: 544,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 544,
            column: 30
          },
          end: {
            line: 544,
            column: 65
          }
        }, {
          start: {
            line: 544,
            column: 69
          },
          end: {
            line: 544,
            column: 70
          }
        }],
        line: 544
      },
      "75": {
        loc: {
          start: {
            line: 545,
            column: 32
          },
          end: {
            line: 545,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 545,
            column: 32
          },
          end: {
            line: 545,
            column: 69
          }
        }, {
          start: {
            line: 545,
            column: 73
          },
          end: {
            line: 545,
            column: 74
          }
        }],
        line: 545
      },
      "76": {
        loc: {
          start: {
            line: 546,
            column: 30
          },
          end: {
            line: 546,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 546,
            column: 30
          },
          end: {
            line: 546,
            column: 62
          }
        }, {
          start: {
            line: 546,
            column: 66
          },
          end: {
            line: 546,
            column: 67
          }
        }],
        line: 546
      },
      "77": {
        loc: {
          start: {
            line: 547,
            column: 32
          },
          end: {
            line: 547,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 547,
            column: 32
          },
          end: {
            line: 547,
            column: 66
          }
        }, {
          start: {
            line: 547,
            column: 70
          },
          end: {
            line: 547,
            column: 71
          }
        }],
        line: 547
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0,
      "265": 0,
      "266": 0,
      "267": 0,
      "268": 0,
      "269": 0,
      "270": 0,
      "271": 0,
      "272": 0,
      "273": 0,
      "274": 0,
      "275": 0,
      "276": 0,
      "277": 0,
      "278": 0,
      "279": 0,
      "280": 0,
      "281": 0,
      "282": 0,
      "283": 0,
      "284": 0,
      "285": 0,
      "286": 0,
      "287": 0,
      "288": 0,
      "289": 0,
      "290": 0,
      "291": 0,
      "292": 0,
      "293": 0,
      "294": 0,
      "295": 0,
      "296": 0,
      "297": 0,
      "298": 0,
      "299": 0,
      "300": 0,
      "301": 0,
      "302": 0,
      "303": 0,
      "304": 0,
      "305": 0,
      "306": 0,
      "307": 0,
      "308": 0,
      "309": 0,
      "310": 0,
      "311": 0,
      "312": 0,
      "313": 0,
      "314": 0,
      "315": 0,
      "316": 0,
      "317": 0,
      "318": 0,
      "319": 0,
      "320": 0,
      "321": 0,
      "322": 0,
      "323": 0,
      "324": 0,
      "325": 0,
      "326": 0,
      "327": 0,
      "328": 0,
      "329": 0,
      "330": 0,
      "331": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0, 0, 0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0, 0, 0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0, 0, 0, 0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0, 0, 0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0, 0, 0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0, 0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/performance/SkillGapPerformanceOptimizer.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yCAAyC;AACzC,uCAAsC;AACtC,qCAA0C;AAoB1C;;;GAGG;AACH;IAqBE;QAZA,uBAAuB;QACf,YAAO,GAAoC,IAAI,GAAG,EAAE,CAAC;QACrD,oBAAe,GAAwB,IAAI,GAAG,EAAE,CAAC;QACjD,cAAS,GAAwB,IAAI,GAAG,EAAE,CAAC;QAC3C,gBAAW,GAAwB,IAAI,GAAG,EAAE,CAAC;QAErD,mBAAmB;QACX,eAAU,GAAuC,IAAI,GAAG,EAAE,CAAC;QAC3D,gBAAW,GAAgC,IAAI,GAAG,EAAE,CAAC;QAC5C,gBAAW,GAAG,EAAE,CAAC,CAAC,mBAAmB;QACrC,mBAAc,GAAG,EAAE,CAAC;QAGnC,kDAAkD;QAClD,IAAI,CAAC,SAAS,GAAG,IAAI,mBAAW,CAAC;YAC/B,OAAO,EAAE,IAAI;YACb,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,YAAY;SACjC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,GAAG,IAAI,mBAAW,CAAC;YAChC,OAAO,EAAE,IAAI;YACb,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;SACnC,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,GAAG,IAAI,mBAAW,CAAC;YACrC,OAAO,EAAE,IAAI;YACb,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,SAAS;SAC/B,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,GAAG,IAAI,mBAAW,CAAC;YACrC,OAAO,EAAE,KAAK;YACd,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;SACnC,CAAC,CAAC;IACL,CAAC;IAEa,wCAAW,GAAzB;QACE,IAAI,CAAC,4BAA4B,CAAC,QAAQ,EAAE,CAAC;YAC3C,4BAA4B,CAAC,QAAQ,GAAG,IAAI,4BAA4B,EAAE,CAAC;QAC7E,CAAC;QACD,OAAO,4BAA4B,CAAC,QAAQ,CAAC;IAC/C,CAAC;IAED;;OAEG;IACG,8CAAO,GAAb,UAAc,MAAc;uCAAG,OAAO;;;;;wBAC9B,SAAS,GAAG,wBAAW,CAAC,GAAG,EAAE,CAAC;wBAC9B,QAAQ,GAAG,eAAQ,MAAM,CAAE,CAAC;wBAG9B,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;wBACxC,IAAI,IAAI,EAAE,CAAC;4BACT,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;4BAC/B,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;4BACzC,sBAAO,IAAI,EAAC;wBACd,CAAC;wBAED,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;;;;wBAIvB,qBAAM,eAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gCAClC,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;gCACrB,MAAM,EAAE;oCACN,EAAE,EAAE,IAAI;oCACR,KAAK,EAAE,IAAI;oCACX,IAAI,EAAE,IAAI;oCACV,SAAS,EAAE,IAAI;oCACf,sCAAsC;iCACvC;6BACF,CAAC,EAAA;;wBAVF,0CAA0C;wBAC1C,IAAI,GAAG,SASL,CAAC;wBAEH,IAAI,IAAI,EAAE,CAAC;4BACT,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;wBACrC,CAAC;;;;wBAED,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,OAAK,CAAC,CAAC;wBAC7C,IAAI,GAAG,IAAI,CAAC;;;wBAGd,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;wBACzC,sBAAO,IAAI,EAAC;;;;KACb;IAED;;OAEG;IACG,gDAAS,GAAf,UAAgB,QAAkB;uCAAG,OAAO;;;;;wBACpC,SAAS,GAAG,wBAAW,CAAC,GAAG,EAAE,CAAC;wBAC9B,OAAO,GAAU,EAAE,CAAC;wBACpB,WAAW,GAAa,EAAE,CAAC;wBAEjC,6BAA6B;wBAC7B,WAA8B,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ,EAAE,CAAC;4BAAtB,OAAO;4BACV,QAAQ,GAAG,gBAAS,OAAO,CAAE,CAAC;4BAC9B,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;4BAC7C,IAAI,MAAM,EAAE,CAAC;gCACX,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gCACrB,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;4BACnC,CAAC;iCAAM,CAAC;gCACN,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gCAC1B,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;4BACpC,CAAC;wBACH,CAAC;6BAGG,CAAA,WAAW,CAAC,MAAM,GAAG,CAAC,CAAA,EAAtB,wBAAsB;;;;wBAEP,qBAAM,eAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;gCACzC,KAAK,EAAE;oCACL,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE;iCACxB;gCACD,MAAM,EAAE;oCACN,EAAE,EAAE,IAAI;oCACR,IAAI,EAAE,IAAI;oCACV,QAAQ,EAAE,IAAI;oCACd,WAAW,EAAE,IAAI;oCACjB,2CAA2C;iCAC5C;6BACF,CAAC,EAAA;;wBAXI,MAAM,GAAG,SAWb;wBAEF,oBAAoB;wBACpB,WAA0B,EAAN,iBAAM,EAAN,oBAAM,EAAN,IAAM,EAAE,CAAC;4BAAlB,KAAK;4BACR,QAAQ,GAAG,gBAAS,KAAK,CAAC,EAAE,CAAE,CAAC;4BACrC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;4BACrC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACtB,CAAC;;;;wBAED,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,OAAK,CAAC,CAAC;;;wBAKnD,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;wBAC3C,sBAAO,OAAO,EAAC;;;;KAChB;IAED;;OAEG;IACG,oDAAa,GAAnB,UAAoB,KAAa,EAAE,QAAiB;uCAAG,OAAO;;;;;wBACtD,SAAS,GAAG,wBAAW,CAAC,GAAG,EAAE,CAAC;wBAC9B,QAAQ,GAAG,iBAAU,KAAK,cAAI,QAAQ,IAAI,QAAQ,CAAE,CAAC;wBAGvD,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;wBACpD,IAAI,UAAU,EAAE,CAAC;4BACf,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;4BACrC,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;4BAC/C,sBAAO,UAAU,EAAC;wBACpB,CAAC;wBAED,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;;;;wBAIhB,qBAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAA;;wBAA9C,WAAW,GAAG,SAAgC;wBACpD,IAAI,CAAC,WAAW,EAAE,CAAC;4BACjB,gDAAgD;4BAChD,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;4BAC9C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;4BAC/C,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;4BAC/C,sBAAO,UAAU,EAAC;wBACpB,CAAC;wBAGoB,qBAAM,eAAM,CAAC,eAAe,CAAC,SAAS,CAAC;gCAC1D,KAAK,EAAE;oCACL,OAAO,EAAE,WAAW,CAAC,EAAE;oCACvB,QAAQ,EAAE,IAAI;iCACf;gCACD,OAAO,EAAE;oCACP,QAAQ,EAAE,MAAM;iCACjB;gCACD,MAAM,EAAE;oCACN,WAAW,EAAE,IAAI;oCACjB,mBAAmB,EAAE,IAAI;oCACzB,WAAW,EAAE,IAAI;oCACjB,QAAQ,EAAE,IAAI;oCACd,MAAM,EAAE,IAAI;iCACb;6BACF,CAAC,EAAA;;wBAfI,YAAY,GAAG,SAenB;wBAEF,0DAA0D;wBAC1D,UAAU,GAAG,YAAY,CAAC,CAAC,uBACtB,YAAY,KACf,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE,EAC1B,OAAO,EAAE,WAAW,CAAC,EAAE,EACvB,SAAS,EAAE,WAAW,CAAC,IAAI,IAC3B,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;wBAErC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;;;;wBAE/C,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,OAAK,CAAC,CAAC;wBACpD,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;wBAC9C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;;;wBAGjD,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;wBAC/C,sBAAO,UAAU,EAAC;;;;KACnB;IAED;;OAEG;IACG,qDAAc,GAApB,UAAqB,SAAiB;uCAAG,OAAO;;;;;wBACxC,QAAQ,GAAG,qBAAc,SAAS,CAAC,WAAW,EAAE,CAAE,CAAC;wBAErD,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;wBAC1C,IAAI,KAAK,EAAE,CAAC;4BACV,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;4BACtC,sBAAO,KAAK,EAAC;wBACf,CAAC;wBAED,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;;;;wBAG7B,qBAAM,eAAM,CAAC,KAAK,CAAC,SAAS,CAAC;gCACnC,KAAK,EAAE;oCACL,IAAI,EAAE;wCACJ,MAAM,EAAE,SAAS;wCACjB,IAAI,EAAE,aAAa;qCACpB;iCACF;gCACD,MAAM,EAAE;oCACN,EAAE,EAAE,IAAI;oCACR,IAAI,EAAE,IAAI;oCACV,QAAQ,EAAE,IAAI;iCACf;6BACF,CAAC,EAAA;;wBAZF,KAAK,GAAG,SAYN,CAAC;wBAEH,IAAI,KAAK,EAAE,CAAC;4BACV,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;wBACvC,CAAC;;;;wBAED,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,OAAK,CAAC,CAAC;wBACtD,KAAK,GAAG,IAAI,CAAC;;4BAGf,sBAAO,KAAK,EAAC;;;;KACd;IAED;;OAEG;IACG,6DAAsB,GAA5B,UAA6B,WAAkB;uCAAG,OAAO;;;;;;wBACjD,SAAS,GAAG,wBAAW,CAAC,GAAG,EAAE,CAAC;wBAGpB,qBAAM,eAAM,CAAC,YAAY,CAAC,UAAO,EAAE;;;;;4CAC3C,kBAAkB,GAAG,EAAE,CAAC;kDAEM,EAAX,2BAAW;;;iDAAX,CAAA,yBAAW,CAAA;4CAAzB,UAAU;4CACH,qBAAM,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC;oDAC9C,IAAI,EAAE,UAAU;oDAChB,MAAM,EAAE;wDACN,EAAE,EAAE,IAAI;wDACR,MAAM,EAAE,IAAI;wDACZ,OAAO,EAAE,IAAI;wDACb,UAAU,EAAE,IAAI;wDAChB,eAAe,EAAE,IAAI;wDACrB,cAAc,EAAE,IAAI;qDACrB;iDACF,CAAC,EAAA;;4CAVI,OAAO,GAAG,SAUd;4CACF,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;;4CAZV,IAAW,CAAA;;gDAepC,sBAAO,kBAAkB,EAAC;;;iCAC3B,CAAC,EAAA;;wBAnBI,OAAO,GAAG,SAmBd;wBAEF,oBAAoB;wBACpB,WAAgC,EAAP,mBAAO,EAAP,qBAAO,EAAP,IAAO,EAAE,CAAC;4BAAxB,UAAU;4BACb,QAAQ,GAAG,qBAAc,UAAU,CAAC,MAAM,cAAI,UAAU,CAAC,OAAO,CAAE,CAAC;4BACzE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;wBACjD,CAAC;wBAED,IAAI,CAAC,aAAa,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;wBACxD,sBAAO,OAAO,EAAC;;;;KAChB;IAED;;OAEG;IACG,iDAAU,GAAhB;0CAKG,OAAO,YAJR,SAAiB,EACjB,WAAmB,EACnB,SAA2B,EAC3B,QAAoB;;YAApB,yBAAA,EAAA,YAAoB;;gBAEpB,sBAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;wBACjC,IAAI,CAAC,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;4BACpC,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;wBACrC,CAAC;wBAED,IAAM,KAAK,GAAG,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;wBAC9C,KAAK,CAAC,IAAI,CAAC;4BACT,EAAE,EAAE,WAAW;4BACf,SAAS,EAAE;;;;;;4CAEQ,qBAAM,SAAS,EAAE,EAAA;;4CAA1B,MAAM,GAAG,SAAiB;4CAChC,OAAO,CAAC,MAAM,CAAC,CAAC;4CAChB,sBAAO,MAAM,EAAC;;;4CAEd,MAAM,CAAC,OAAK,CAAC,CAAC;4CACd,MAAM,OAAK,CAAC;;;;iCAEf;4BACD,QAAQ,UAAA;yBACT,CAAC,CAAC;wBAEH,mBAAmB;wBACnB,KAAK,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAvB,CAAuB,CAAC,CAAC;wBAE9C,0CAA0C;wBAC1C,IAAI,KAAK,CAAC,MAAM,IAAI,KAAI,CAAC,cAAc,EAAE,CAAC;4BACxC,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;wBAC/B,CAAC;6BAAM,CAAC;4BACN,KAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;wBAC1C,CAAC;oBACH,CAAC,CAAC,EAAC;;;KACJ;IAED;;OAEG;IACW,mDAAY,GAA1B,UAA2B,SAAiB;uCAAG,OAAO;;;;;wBAC9C,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;wBAC7C,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;4BAAE,sBAAO;wBAEzC,4BAA4B;wBAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;wBAC7B,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;wBAC9C,IAAI,KAAK,EAAE,CAAC;4BACV,YAAY,CAAC,KAAK,CAAC,CAAC;4BACpB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;wBACrC,CAAC;wBAED,qCAAqC;wBACrC,qBAAM,OAAO,CAAC,UAAU,CACtB,KAAK,CAAC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,SAAS,EAAE,EAAhB,CAAgB,CAAC,CACpC,EAAA;;wBAHD,qCAAqC;wBACrC,SAEC,CAAC;;;;;KACH;IAED;;OAEG;IACK,8DAAuB,GAA/B,UAAgC,SAAiB;QAAjD,iBAQC;QAPC,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC;YAAE,OAAO;QAE5C,IAAM,KAAK,GAAG,UAAU,CAAC;YACvB,KAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC/B,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAErB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,2DAAoB,GAA5B,UAA6B,KAAa;QACxC,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE;YAC1B,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,EAAE;YACV,aAAa,EAAE,KAAK;YACpB,MAAM,EAAE,CAAC;YACT,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,EAAE;YACf,QAAQ,EAAE,SAAS;YACnB,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACrC,OAAO,EAAE,IAAI;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,qDAAc,GAAtB,UAAuB,IAAY;QACjC,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACK,sDAAe,GAAvB,UAAwB,IAAY;QAClC,IAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACK,oDAAa,GAArB,UAAsB,SAAiB,EAAE,SAAiB;QACxD,IAAM,OAAO,GAAG,wBAAW,CAAC,GAAG,EAAE,CAAC;QAClC,IAAM,SAAS,GAAG,OAAO,GAAG,SAAS,CAAC;QAEtC,IAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;QAEjD,uDAAuD;QACvD,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChD,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACpD,IAAM,KAAK,GAAG,IAAI,GAAG,MAAM,CAAC;QAC5B,IAAM,YAAY,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAElD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE;YAC1B,SAAS,WAAA;YACT,YAAY,cAAA;YACZ,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ;YAC3C,mBAAmB,EAAE,OAAO,GAAG,CAAC,CAAC,4BAA4B;SAC9D,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,4DAAqB,GAArB;QACE,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,kDAAW,GAAX;QACE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,oDAAa,GAAb;QACE,mDAAmD;QACnD,IAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QACtD,IAAM,UAAU,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QAE1D,IAAM,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;QACvG,IAAM,WAAW,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;QAE7G,IAAM,cAAc,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;QAClE,IAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;QAEtE,IAAM,cAAc,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/D,IAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;QAEnE,OAAO;YACL,IAAI,EAAE;gBACJ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,IAAI;gBACpC,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,UAAU;aACnB;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,IAAI;gBACrC,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,WAAW;aACpB;YACD,UAAU,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,IAAI;gBAC1C,IAAI,EAAE,cAAc;gBACpB,MAAM,EAAE,gBAAgB;aACzB;YACD,UAAU,EAAE;gBACV,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,IAAI;gBAC1C,IAAI,EAAE,cAAc;gBACpB,MAAM,EAAE,gBAAgB;aACzB;SACF,CAAC;IACJ,CAAC;IACH,mCAAC;AAAD,CAAC,AAheD,IAgeC;AAheY,oEAA4B;AAkezC,4BAA4B;AACf,QAAA,4BAA4B,GAAG,4BAA4B,CAAC,WAAW,EAAE,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/performance/SkillGapPerformanceOptimizer.ts"],
      sourcesContent: ["import { performance } from 'perf_hooks';\nimport { prisma } from '@/lib/prisma';\nimport { MemoryCache } from '@/lib/cache';\n\ninterface CacheConfig {\n  maxSize: number;\n  ttl: number; // Time to live in milliseconds\n}\n\ninterface PerformanceMetrics {\n  queryTime: number;\n  cacheHitRate: number;\n  memoryUsage: number;\n  operationsPerSecond: number;\n}\n\ninterface BatchOperation<T> {\n  id: string;\n  operation: () => Promise<T>;\n  priority: number;\n}\n\n/**\n * Performance optimizer for Skill Gap Analyzer operations\n * Implements caching, batching, and query optimization\n */\nexport class SkillGapPerformanceOptimizer {\n  private static instance: SkillGapPerformanceOptimizer;\n  \n  // Multi-level caching\n  private userCache: MemoryCache;\n  private skillCache: MemoryCache;\n  private marketDataCache: MemoryCache;\n  private assessmentCache: MemoryCache;\n  \n  // Performance tracking\n  private metrics: Map<string, PerformanceMetrics> = new Map();\n  private operationCounts: Map<string, number> = new Map();\n  private cacheHits: Map<string, number> = new Map();\n  private cacheMisses: Map<string, number> = new Map();\n  \n  // Batch processing\n  private batchQueue: Map<string, BatchOperation<any>[]> = new Map();\n  private batchTimers: Map<string, NodeJS.Timeout> = new Map();\n  private readonly BATCH_DELAY = 50; // 50ms batch delay\n  private readonly MAX_BATCH_SIZE = 10;\n\n  private constructor() {\n    // Initialize caches with optimized configurations\n    this.userCache = new MemoryCache({\n      maxSize: 1000,\n      ttl: 5 * 60 * 1000, // 5 minutes\n    });\n\n    this.skillCache = new MemoryCache({\n      maxSize: 5000,\n      ttl: 30 * 60 * 1000, // 30 minutes\n    });\n\n    this.marketDataCache = new MemoryCache({\n      maxSize: 2000,\n      ttl: 60 * 60 * 1000, // 1 hour\n    });\n\n    this.assessmentCache = new MemoryCache({\n      maxSize: 10000,\n      ttl: 10 * 60 * 1000, // 10 minutes\n    });\n  }\n\n  public static getInstance(): SkillGapPerformanceOptimizer {\n    if (!SkillGapPerformanceOptimizer.instance) {\n      SkillGapPerformanceOptimizer.instance = new SkillGapPerformanceOptimizer();\n    }\n    return SkillGapPerformanceOptimizer.instance;\n  }\n\n  /**\n   * Optimized user lookup with caching\n   */\n  async getUser(userId: string): Promise<any> {\n    const startTime = performance.now();\n    const cacheKey = `user:${userId}`;\n\n    // Check cache first\n    let user = this.userCache.get(cacheKey);\n    if (user) {\n      this.recordCacheHit('getUser');\n      this.recordMetrics('getUser', startTime);\n      return user;\n    }\n\n    this.recordCacheMiss('getUser');\n\n    try {\n      // Database query with optimized selection\n      user = await prisma.user.findUnique({\n        where: { id: userId },\n        select: {\n          id: true,\n          email: true,\n          name: true,\n          createdAt: true,\n          // Only select fields we actually need\n        }\n      });\n\n      if (user) {\n        this.userCache.set(cacheKey, user);\n      }\n    } catch (error) {\n      console.error('Error fetching user:', error);\n      user = null;\n    }\n\n    this.recordMetrics('getUser', startTime);\n    return user;\n  }\n\n  /**\n   * Batch skill lookup with caching\n   */\n  async getSkills(skillIds: string[]): Promise<any[]> {\n    const startTime = performance.now();\n    const results: any[] = [];\n    const uncachedIds: string[] = [];\n\n    // Check cache for each skill\n    for (const skillId of skillIds) {\n      const cacheKey = `skill:${skillId}`;\n      const cached = this.skillCache.get(cacheKey);\n      if (cached) {\n        results.push(cached);\n        this.recordCacheHit('getSkills');\n      } else {\n        uncachedIds.push(skillId);\n        this.recordCacheMiss('getSkills');\n      }\n    }\n\n    // Batch fetch uncached skills\n    if (uncachedIds.length > 0) {\n      try {\n        const skills = await prisma.skill.findMany({\n          where: {\n            id: { in: uncachedIds }\n          },\n          select: {\n            id: true,\n            name: true,\n            category: true,\n            description: true,\n            // Optimize by only selecting needed fields\n          }\n        });\n\n        // Cache the results\n        for (const skill of skills) {\n          const cacheKey = `skill:${skill.id}`;\n          this.skillCache.set(cacheKey, skill);\n          results.push(skill);\n        }\n      } catch (error) {\n        console.error('Error fetching skills:', error);\n        // Return partial results if database fails\n      }\n    }\n\n    this.recordMetrics('getSkills', startTime);\n    return results;\n  }\n\n  /**\n   * Optimized market data retrieval with intelligent caching\n   */\n  async getMarketData(skill: string, location?: string): Promise<any> {\n    const startTime = performance.now();\n    const cacheKey = `market:${skill}:${location || 'global'}`;\n\n    // Check cache first\n    let marketData = this.marketDataCache.get(cacheKey);\n    if (marketData) {\n      this.recordCacheHit('getMarketData');\n      this.recordMetrics('getMarketData', startTime);\n      return marketData;\n    }\n\n    this.recordCacheMiss('getMarketData');\n\n    try {\n      // Try to find skill first (with caching)\n      const skillRecord = await this.getSkillByName(skill);\n      if (!skillRecord) {\n        // Return default market data for unknown skills\n        marketData = this.getDefaultMarketData(skill);\n        this.marketDataCache.set(cacheKey, marketData);\n        this.recordMetrics('getMarketData', startTime);\n        return marketData;\n      }\n\n      // Query market data with optimized query\n      const dbMarketData = await prisma.skillMarketData.findFirst({\n        where: {\n          skillId: skillRecord.id,\n          isActive: true,\n        },\n        orderBy: {\n          dataDate: 'desc'\n        },\n        select: {\n          demandLevel: true,\n          averageSalaryImpact: true,\n          growthTrend: true,\n          dataDate: true,\n          region: true,\n        }\n      });\n\n      // Ensure we always include the skill name in the response\n      marketData = dbMarketData ? {\n        ...dbMarketData,\n        skill: skill.toLowerCase(),\n        skillId: skillRecord.id,\n        skillName: skillRecord.name\n      } : this.getDefaultMarketData(skill);\n\n      this.marketDataCache.set(cacheKey, marketData);\n    } catch (error) {\n      console.error('Error fetching market data:', error);\n      marketData = this.getDefaultMarketData(skill);\n      this.marketDataCache.set(cacheKey, marketData);\n    }\n\n    this.recordMetrics('getMarketData', startTime);\n    return marketData;\n  }\n\n  /**\n   * Optimized skill lookup by name with caching\n   */\n  async getSkillByName(skillName: string): Promise<any> {\n    const cacheKey = `skill:name:${skillName.toLowerCase()}`;\n\n    let skill = this.skillCache.get(cacheKey);\n    if (skill) {\n      this.recordCacheHit('getSkillByName');\n      return skill;\n    }\n\n    this.recordCacheMiss('getSkillByName');\n\n    try {\n      skill = await prisma.skill.findFirst({\n        where: {\n          name: {\n            equals: skillName,\n            mode: 'insensitive'\n          }\n        },\n        select: {\n          id: true,\n          name: true,\n          category: true,\n        }\n      });\n\n      if (skill) {\n        this.skillCache.set(cacheKey, skill);\n      }\n    } catch (error) {\n      console.error('Error fetching skill by name:', error);\n      skill = null;\n    }\n\n    return skill;\n  }\n\n  /**\n   * Batch assessment creation with optimized database operations\n   */\n  async createAssessmentsBatch(assessments: any[]): Promise<any[]> {\n    const startTime = performance.now();\n    \n    // Use transaction for batch operations\n    const results = await prisma.$transaction(async (tx) => {\n      const createdAssessments = [];\n      \n      for (const assessment of assessments) {\n        const created = await tx.skillAssessment.create({\n          data: assessment,\n          select: {\n            id: true,\n            userId: true,\n            skillId: true,\n            selfRating: true,\n            confidenceLevel: true,\n            assessmentDate: true,\n          }\n        });\n        createdAssessments.push(created);\n      }\n      \n      return createdAssessments;\n    });\n\n    // Cache the results\n    for (const assessment of results) {\n      const cacheKey = `assessment:${assessment.userId}:${assessment.skillId}`;\n      this.assessmentCache.set(cacheKey, assessment);\n    }\n\n    this.recordMetrics('createAssessmentsBatch', startTime);\n    return results;\n  }\n\n  /**\n   * Add operation to batch queue for processing\n   */\n  async addToBatch<T>(\n    batchType: string,\n    operationId: string,\n    operation: () => Promise<T>,\n    priority: number = 1\n  ): Promise<T> {\n    return new Promise((resolve, reject) => {\n      if (!this.batchQueue.has(batchType)) {\n        this.batchQueue.set(batchType, []);\n      }\n\n      const batch = this.batchQueue.get(batchType)!;\n      batch.push({\n        id: operationId,\n        operation: async () => {\n          try {\n            const result = await operation();\n            resolve(result);\n            return result;\n          } catch (error) {\n            reject(error);\n            throw error;\n          }\n        },\n        priority\n      });\n\n      // Sort by priority\n      batch.sort((a, b) => b.priority - a.priority);\n\n      // Process batch if it's full or set timer\n      if (batch.length >= this.MAX_BATCH_SIZE) {\n        this.processBatch(batchType);\n      } else {\n        this.scheduleBatchProcessing(batchType);\n      }\n    });\n  }\n\n  /**\n   * Process a batch of operations\n   */\n  private async processBatch(batchType: string): Promise<void> {\n    const batch = this.batchQueue.get(batchType);\n    if (!batch || batch.length === 0) return;\n\n    // Clear the batch and timer\n    this.batchQueue.set(batchType, []);\n    const timer = this.batchTimers.get(batchType);\n    if (timer) {\n      clearTimeout(timer);\n      this.batchTimers.delete(batchType);\n    }\n\n    // Execute all operations in parallel\n    await Promise.allSettled(\n      batch.map(item => item.operation())\n    );\n  }\n\n  /**\n   * Schedule batch processing with delay\n   */\n  private scheduleBatchProcessing(batchType: string): void {\n    if (this.batchTimers.has(batchType)) return;\n\n    const timer = setTimeout(() => {\n      this.processBatch(batchType);\n    }, this.BATCH_DELAY);\n\n    this.batchTimers.set(batchType, timer);\n  }\n\n  /**\n   * Get default market data for unknown skills\n   */\n  private getDefaultMarketData(skill: string): any {\n    return {\n      skill: skill.toLowerCase(),\n      demand: 50,\n      supply: 50,\n      averageSalary: 75000,\n      growth: 5,\n      difficulty: 5,\n      timeToLearn: 12,\n      category: 'Unknown',\n      lastUpdated: new Date().toISOString(),\n      isStale: true\n    };\n  }\n\n  /**\n   * Record cache hit\n   */\n  private recordCacheHit(type: string): void {\n    const current = this.cacheHits.get(type) || 0;\n    this.cacheHits.set(type, current + 1);\n  }\n\n  /**\n   * Record cache miss\n   */\n  private recordCacheMiss(type: string): void {\n    const current = this.cacheMisses.get(type) || 0;\n    this.cacheMisses.set(type, current + 1);\n  }\n\n  /**\n   * Record performance metrics\n   */\n  private recordMetrics(operation: string, startTime: number): void {\n    const endTime = performance.now();\n    const queryTime = endTime - startTime;\n\n    const current = this.operationCounts.get(operation) || 0;\n    this.operationCounts.set(operation, current + 1);\n\n    // Calculate cache hit rate for this specific operation\n    const hits = this.cacheHits.get(operation) || 0;\n    const misses = this.cacheMisses.get(operation) || 0;\n    const total = hits + misses;\n    const cacheHitRate = total > 0 ? hits / total : 0;\n\n    this.metrics.set(operation, {\n      queryTime,\n      cacheHitRate,\n      memoryUsage: process.memoryUsage().heapUsed,\n      operationsPerSecond: current + 1 // Include current operation\n    });\n  }\n\n  /**\n   * Get performance metrics\n   */\n  getPerformanceMetrics(): Map<string, PerformanceMetrics> {\n    return new Map(this.metrics);\n  }\n\n  /**\n   * Clear all caches\n   */\n  clearCaches(): void {\n    this.userCache.clear();\n    this.skillCache.clear();\n    this.marketDataCache.clear();\n    this.assessmentCache.clear();\n  }\n\n  /**\n   * Get cache statistics\n   */\n  getCacheStats(): any {\n    // Aggregate hits and misses for related operations\n    const userHits = (this.cacheHits.get('getUser') || 0);\n    const userMisses = (this.cacheMisses.get('getUser') || 0);\n\n    const skillHits = (this.cacheHits.get('getSkills') || 0) + (this.cacheHits.get('getSkillByName') || 0);\n    const skillMisses = (this.cacheMisses.get('getSkills') || 0) + (this.cacheMisses.get('getSkillByName') || 0);\n\n    const marketDataHits = (this.cacheHits.get('getMarketData') || 0);\n    const marketDataMisses = (this.cacheMisses.get('getMarketData') || 0);\n\n    const assessmentHits = (this.cacheHits.get('assessment') || 0);\n    const assessmentMisses = (this.cacheMisses.get('assessment') || 0);\n\n    return {\n      user: {\n        size: this.userCache.getStats().size,\n        hits: userHits,\n        misses: userMisses,\n      },\n      skill: {\n        size: this.skillCache.getStats().size,\n        hits: skillHits,\n        misses: skillMisses,\n      },\n      marketData: {\n        size: this.marketDataCache.getStats().size,\n        hits: marketDataHits,\n        misses: marketDataMisses,\n      },\n      assessment: {\n        size: this.assessmentCache.getStats().size,\n        hits: assessmentHits,\n        misses: assessmentMisses,\n      }\n    };\n  }\n}\n\n// Export singleton instance\nexport const skillGapPerformanceOptimizer = SkillGapPerformanceOptimizer.getInstance();\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "2f0aa26643085ba4285a448f9a836ec3c97dc5b4"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_ir4na7dz0 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_ir4na7dz0();
var __assign =
/* istanbul ignore next */
(cov_ir4na7dz0().s[0]++,
/* istanbul ignore next */
(cov_ir4na7dz0().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_ir4na7dz0().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_ir4na7dz0().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_ir4na7dz0().f[0]++;
  cov_ir4na7dz0().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_ir4na7dz0().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_ir4na7dz0().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[1]++;
    cov_ir4na7dz0().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_ir4na7dz0().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_ir4na7dz0().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_ir4na7dz0().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_ir4na7dz0().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_ir4na7dz0().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_ir4na7dz0().b[2][0]++;
          cov_ir4na7dz0().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_ir4na7dz0().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_ir4na7dz0().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_ir4na7dz0().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_ir4na7dz0().s[11]++,
/* istanbul ignore next */
(cov_ir4na7dz0().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_ir4na7dz0().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_ir4na7dz0().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_ir4na7dz0().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[3]++;
    cov_ir4na7dz0().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_ir4na7dz0().f[4]++;
      cov_ir4na7dz0().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_ir4na7dz0().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_ir4na7dz0().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_ir4na7dz0().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_ir4na7dz0().f[6]++;
      cov_ir4na7dz0().s[15]++;
      try {
        /* istanbul ignore next */
        cov_ir4na7dz0().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_ir4na7dz0().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_ir4na7dz0().f[7]++;
      cov_ir4na7dz0().s[18]++;
      try {
        /* istanbul ignore next */
        cov_ir4na7dz0().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_ir4na7dz0().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_ir4na7dz0().f[8]++;
      cov_ir4na7dz0().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_ir4na7dz0().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_ir4na7dz0().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_ir4na7dz0().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_ir4na7dz0().s[23]++,
/* istanbul ignore next */
(cov_ir4na7dz0().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_ir4na7dz0().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_ir4na7dz0().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_ir4na7dz0().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_ir4na7dz0().f[10]++;
        cov_ir4na7dz0().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_ir4na7dz0().b[9][0]++;
          cov_ir4na7dz0().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_ir4na7dz0().b[9][1]++;
        }
        cov_ir4na7dz0().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_ir4na7dz0().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_ir4na7dz0().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_ir4na7dz0().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[11]++;
    cov_ir4na7dz0().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[12]++;
    cov_ir4na7dz0().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_ir4na7dz0().f[13]++;
      cov_ir4na7dz0().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[14]++;
    cov_ir4na7dz0().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_ir4na7dz0().b[12][0]++;
      cov_ir4na7dz0().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_ir4na7dz0().b[12][1]++;
    }
    cov_ir4na7dz0().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_ir4na7dz0().s[36]++;
      try {
        /* istanbul ignore next */
        cov_ir4na7dz0().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_ir4na7dz0().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_ir4na7dz0().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_ir4na7dz0().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_ir4na7dz0().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_ir4na7dz0().b[18][0]++,
        /* istanbul ignore next */
        (cov_ir4na7dz0().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_ir4na7dz0().b[19][1]++,
        /* istanbul ignore next */
        (cov_ir4na7dz0().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_ir4na7dz0().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_ir4na7dz0().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_ir4na7dz0().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_ir4na7dz0().b[15][0]++;
          cov_ir4na7dz0().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_ir4na7dz0().b[15][1]++;
        }
        cov_ir4na7dz0().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_ir4na7dz0().b[21][0]++;
          cov_ir4na7dz0().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_ir4na7dz0().b[21][1]++;
        }
        cov_ir4na7dz0().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[22][1]++;
            cov_ir4na7dz0().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_ir4na7dz0().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[22][2]++;
            cov_ir4na7dz0().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_ir4na7dz0().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[22][3]++;
            cov_ir4na7dz0().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_ir4na7dz0().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_ir4na7dz0().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_ir4na7dz0().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[22][4]++;
            cov_ir4na7dz0().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_ir4na7dz0().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_ir4na7dz0().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[22][5]++;
            cov_ir4na7dz0().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_ir4na7dz0().b[23][0]++;
              cov_ir4na7dz0().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_ir4na7dz0().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_ir4na7dz0().b[23][1]++;
            }
            cov_ir4na7dz0().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_ir4na7dz0().b[26][0]++;
              cov_ir4na7dz0().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_ir4na7dz0().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_ir4na7dz0().b[26][1]++;
            }
            cov_ir4na7dz0().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_ir4na7dz0().b[28][0]++;
              cov_ir4na7dz0().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_ir4na7dz0().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_ir4na7dz0().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_ir4na7dz0().b[28][1]++;
            }
            cov_ir4na7dz0().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_ir4na7dz0().b[30][0]++;
              cov_ir4na7dz0().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_ir4na7dz0().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_ir4na7dz0().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_ir4na7dz0().b[30][1]++;
            }
            cov_ir4na7dz0().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_ir4na7dz0().b[32][0]++;
              cov_ir4na7dz0().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_ir4na7dz0().b[32][1]++;
            }
            cov_ir4na7dz0().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_ir4na7dz0().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_ir4na7dz0().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_ir4na7dz0().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_ir4na7dz0().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_ir4na7dz0().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_ir4na7dz0().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_ir4na7dz0().b[33][0]++;
      cov_ir4na7dz0().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_ir4na7dz0().b[33][1]++;
    }
    cov_ir4na7dz0().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_ir4na7dz0().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_ir4na7dz0().b[34][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_ir4na7dz0().s[78]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_ir4na7dz0().s[79]++;
exports.skillGapPerformanceOptimizer = exports.SkillGapPerformanceOptimizer = void 0;
var perf_hooks_1 =
/* istanbul ignore next */
(cov_ir4na7dz0().s[80]++, require("perf_hooks"));
var prisma_1 =
/* istanbul ignore next */
(cov_ir4na7dz0().s[81]++, require("@/lib/prisma"));
var cache_1 =
/* istanbul ignore next */
(cov_ir4na7dz0().s[82]++, require("@/lib/cache"));
/**
 * Performance optimizer for Skill Gap Analyzer operations
 * Implements caching, batching, and query optimization
 */
var SkillGapPerformanceOptimizer =
/* istanbul ignore next */
(/** @class */cov_ir4na7dz0().s[83]++, function () {
  /* istanbul ignore next */
  cov_ir4na7dz0().f[15]++;
  function SkillGapPerformanceOptimizer() {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[16]++;
    cov_ir4na7dz0().s[84]++;
    // Performance tracking
    this.metrics = new Map();
    /* istanbul ignore next */
    cov_ir4na7dz0().s[85]++;
    this.operationCounts = new Map();
    /* istanbul ignore next */
    cov_ir4na7dz0().s[86]++;
    this.cacheHits = new Map();
    /* istanbul ignore next */
    cov_ir4na7dz0().s[87]++;
    this.cacheMisses = new Map();
    // Batch processing
    /* istanbul ignore next */
    cov_ir4na7dz0().s[88]++;
    this.batchQueue = new Map();
    /* istanbul ignore next */
    cov_ir4na7dz0().s[89]++;
    this.batchTimers = new Map();
    /* istanbul ignore next */
    cov_ir4na7dz0().s[90]++;
    this.BATCH_DELAY = 50; // 50ms batch delay
    /* istanbul ignore next */
    cov_ir4na7dz0().s[91]++;
    this.MAX_BATCH_SIZE = 10;
    // Initialize caches with optimized configurations
    /* istanbul ignore next */
    cov_ir4na7dz0().s[92]++;
    this.userCache = new cache_1.MemoryCache({
      maxSize: 1000,
      ttl: 5 * 60 * 1000 // 5 minutes
    });
    /* istanbul ignore next */
    cov_ir4na7dz0().s[93]++;
    this.skillCache = new cache_1.MemoryCache({
      maxSize: 5000,
      ttl: 30 * 60 * 1000 // 30 minutes
    });
    /* istanbul ignore next */
    cov_ir4na7dz0().s[94]++;
    this.marketDataCache = new cache_1.MemoryCache({
      maxSize: 2000,
      ttl: 60 * 60 * 1000 // 1 hour
    });
    /* istanbul ignore next */
    cov_ir4na7dz0().s[95]++;
    this.assessmentCache = new cache_1.MemoryCache({
      maxSize: 10000,
      ttl: 10 * 60 * 1000 // 10 minutes
    });
  }
  /* istanbul ignore next */
  cov_ir4na7dz0().s[96]++;
  SkillGapPerformanceOptimizer.getInstance = function () {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[17]++;
    cov_ir4na7dz0().s[97]++;
    if (!SkillGapPerformanceOptimizer.instance) {
      /* istanbul ignore next */
      cov_ir4na7dz0().b[35][0]++;
      cov_ir4na7dz0().s[98]++;
      SkillGapPerformanceOptimizer.instance = new SkillGapPerformanceOptimizer();
    } else
    /* istanbul ignore next */
    {
      cov_ir4na7dz0().b[35][1]++;
    }
    cov_ir4na7dz0().s[99]++;
    return SkillGapPerformanceOptimizer.instance;
  };
  /**
   * Optimized user lookup with caching
   */
  /* istanbul ignore next */
  cov_ir4na7dz0().s[100]++;
  SkillGapPerformanceOptimizer.prototype.getUser = function (userId) {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[18]++;
    cov_ir4na7dz0().s[101]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_ir4na7dz0().f[19]++;
      var startTime, cacheKey, user, error_1;
      /* istanbul ignore next */
      cov_ir4na7dz0().s[102]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_ir4na7dz0().f[20]++;
        cov_ir4na7dz0().s[103]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[36][0]++;
            cov_ir4na7dz0().s[104]++;
            startTime = perf_hooks_1.performance.now();
            /* istanbul ignore next */
            cov_ir4na7dz0().s[105]++;
            cacheKey = "user:".concat(userId);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[106]++;
            user = this.userCache.get(cacheKey);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[107]++;
            if (user) {
              /* istanbul ignore next */
              cov_ir4na7dz0().b[37][0]++;
              cov_ir4na7dz0().s[108]++;
              this.recordCacheHit('getUser');
              /* istanbul ignore next */
              cov_ir4na7dz0().s[109]++;
              this.recordMetrics('getUser', startTime);
              /* istanbul ignore next */
              cov_ir4na7dz0().s[110]++;
              return [2 /*return*/, user];
            } else
            /* istanbul ignore next */
            {
              cov_ir4na7dz0().b[37][1]++;
            }
            cov_ir4na7dz0().s[111]++;
            this.recordCacheMiss('getUser');
            /* istanbul ignore next */
            cov_ir4na7dz0().s[112]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[36][1]++;
            cov_ir4na7dz0().s[113]++;
            _a.trys.push([1, 3,, 4]);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[114]++;
            return [4 /*yield*/, prisma_1.prisma.user.findUnique({
              where: {
                id: userId
              },
              select: {
                id: true,
                email: true,
                name: true,
                createdAt: true
                // Only select fields we actually need
              }
            })];
          case 2:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[36][2]++;
            cov_ir4na7dz0().s[115]++;
            // Database query with optimized selection
            user = _a.sent();
            /* istanbul ignore next */
            cov_ir4na7dz0().s[116]++;
            if (user) {
              /* istanbul ignore next */
              cov_ir4na7dz0().b[38][0]++;
              cov_ir4na7dz0().s[117]++;
              this.userCache.set(cacheKey, user);
            } else
            /* istanbul ignore next */
            {
              cov_ir4na7dz0().b[38][1]++;
            }
            cov_ir4na7dz0().s[118]++;
            return [3 /*break*/, 4];
          case 3:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[36][3]++;
            cov_ir4na7dz0().s[119]++;
            error_1 = _a.sent();
            /* istanbul ignore next */
            cov_ir4na7dz0().s[120]++;
            console.error('Error fetching user:', error_1);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[121]++;
            user = null;
            /* istanbul ignore next */
            cov_ir4na7dz0().s[122]++;
            return [3 /*break*/, 4];
          case 4:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[36][4]++;
            cov_ir4na7dz0().s[123]++;
            this.recordMetrics('getUser', startTime);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[124]++;
            return [2 /*return*/, user];
        }
      });
    });
  };
  /**
   * Batch skill lookup with caching
   */
  /* istanbul ignore next */
  cov_ir4na7dz0().s[125]++;
  SkillGapPerformanceOptimizer.prototype.getSkills = function (skillIds) {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[21]++;
    cov_ir4na7dz0().s[126]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_ir4na7dz0().f[22]++;
      var startTime, results, uncachedIds, _i, skillIds_1, skillId, cacheKey, cached, skills, _a, skills_1, skill, cacheKey, error_2;
      /* istanbul ignore next */
      cov_ir4na7dz0().s[127]++;
      return __generator(this, function (_b) {
        /* istanbul ignore next */
        cov_ir4na7dz0().f[23]++;
        cov_ir4na7dz0().s[128]++;
        switch (_b.label) {
          case 0:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[39][0]++;
            cov_ir4na7dz0().s[129]++;
            startTime = perf_hooks_1.performance.now();
            /* istanbul ignore next */
            cov_ir4na7dz0().s[130]++;
            results = [];
            /* istanbul ignore next */
            cov_ir4na7dz0().s[131]++;
            uncachedIds = [];
            // Check cache for each skill
            /* istanbul ignore next */
            cov_ir4na7dz0().s[132]++;
            for (_i = 0, skillIds_1 = skillIds; _i < skillIds_1.length; _i++) {
              /* istanbul ignore next */
              cov_ir4na7dz0().s[133]++;
              skillId = skillIds_1[_i];
              /* istanbul ignore next */
              cov_ir4na7dz0().s[134]++;
              cacheKey = "skill:".concat(skillId);
              /* istanbul ignore next */
              cov_ir4na7dz0().s[135]++;
              cached = this.skillCache.get(cacheKey);
              /* istanbul ignore next */
              cov_ir4na7dz0().s[136]++;
              if (cached) {
                /* istanbul ignore next */
                cov_ir4na7dz0().b[40][0]++;
                cov_ir4na7dz0().s[137]++;
                results.push(cached);
                /* istanbul ignore next */
                cov_ir4na7dz0().s[138]++;
                this.recordCacheHit('getSkills');
              } else {
                /* istanbul ignore next */
                cov_ir4na7dz0().b[40][1]++;
                cov_ir4na7dz0().s[139]++;
                uncachedIds.push(skillId);
                /* istanbul ignore next */
                cov_ir4na7dz0().s[140]++;
                this.recordCacheMiss('getSkills');
              }
            }
            /* istanbul ignore next */
            cov_ir4na7dz0().s[141]++;
            if (!(uncachedIds.length > 0)) {
              /* istanbul ignore next */
              cov_ir4na7dz0().b[41][0]++;
              cov_ir4na7dz0().s[142]++;
              return [3 /*break*/, 4];
            } else
            /* istanbul ignore next */
            {
              cov_ir4na7dz0().b[41][1]++;
            }
            cov_ir4na7dz0().s[143]++;
            _b.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[39][1]++;
            cov_ir4na7dz0().s[144]++;
            _b.trys.push([1, 3,, 4]);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[145]++;
            return [4 /*yield*/, prisma_1.prisma.skill.findMany({
              where: {
                id: {
                  in: uncachedIds
                }
              },
              select: {
                id: true,
                name: true,
                category: true,
                description: true
                // Optimize by only selecting needed fields
              }
            })];
          case 2:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[39][2]++;
            cov_ir4na7dz0().s[146]++;
            skills = _b.sent();
            // Cache the results
            /* istanbul ignore next */
            cov_ir4na7dz0().s[147]++;
            for (_a = 0, skills_1 = skills; _a < skills_1.length; _a++) {
              /* istanbul ignore next */
              cov_ir4na7dz0().s[148]++;
              skill = skills_1[_a];
              /* istanbul ignore next */
              cov_ir4na7dz0().s[149]++;
              cacheKey = "skill:".concat(skill.id);
              /* istanbul ignore next */
              cov_ir4na7dz0().s[150]++;
              this.skillCache.set(cacheKey, skill);
              /* istanbul ignore next */
              cov_ir4na7dz0().s[151]++;
              results.push(skill);
            }
            /* istanbul ignore next */
            cov_ir4na7dz0().s[152]++;
            return [3 /*break*/, 4];
          case 3:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[39][3]++;
            cov_ir4na7dz0().s[153]++;
            error_2 = _b.sent();
            /* istanbul ignore next */
            cov_ir4na7dz0().s[154]++;
            console.error('Error fetching skills:', error_2);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[155]++;
            return [3 /*break*/, 4];
          case 4:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[39][4]++;
            cov_ir4na7dz0().s[156]++;
            this.recordMetrics('getSkills', startTime);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[157]++;
            return [2 /*return*/, results];
        }
      });
    });
  };
  /**
   * Optimized market data retrieval with intelligent caching
   */
  /* istanbul ignore next */
  cov_ir4na7dz0().s[158]++;
  SkillGapPerformanceOptimizer.prototype.getMarketData = function (skill, location) {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[24]++;
    cov_ir4na7dz0().s[159]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_ir4na7dz0().f[25]++;
      var startTime, cacheKey, marketData, skillRecord, dbMarketData, error_3;
      /* istanbul ignore next */
      cov_ir4na7dz0().s[160]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_ir4na7dz0().f[26]++;
        cov_ir4na7dz0().s[161]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[42][0]++;
            cov_ir4na7dz0().s[162]++;
            startTime = perf_hooks_1.performance.now();
            /* istanbul ignore next */
            cov_ir4na7dz0().s[163]++;
            cacheKey = "market:".concat(skill, ":").concat(
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[43][0]++, location) ||
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[43][1]++, 'global'));
            /* istanbul ignore next */
            cov_ir4na7dz0().s[164]++;
            marketData = this.marketDataCache.get(cacheKey);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[165]++;
            if (marketData) {
              /* istanbul ignore next */
              cov_ir4na7dz0().b[44][0]++;
              cov_ir4na7dz0().s[166]++;
              this.recordCacheHit('getMarketData');
              /* istanbul ignore next */
              cov_ir4na7dz0().s[167]++;
              this.recordMetrics('getMarketData', startTime);
              /* istanbul ignore next */
              cov_ir4na7dz0().s[168]++;
              return [2 /*return*/, marketData];
            } else
            /* istanbul ignore next */
            {
              cov_ir4na7dz0().b[44][1]++;
            }
            cov_ir4na7dz0().s[169]++;
            this.recordCacheMiss('getMarketData');
            /* istanbul ignore next */
            cov_ir4na7dz0().s[170]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[42][1]++;
            cov_ir4na7dz0().s[171]++;
            _a.trys.push([1, 4,, 5]);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[172]++;
            return [4 /*yield*/, this.getSkillByName(skill)];
          case 2:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[42][2]++;
            cov_ir4na7dz0().s[173]++;
            skillRecord = _a.sent();
            /* istanbul ignore next */
            cov_ir4na7dz0().s[174]++;
            if (!skillRecord) {
              /* istanbul ignore next */
              cov_ir4na7dz0().b[45][0]++;
              cov_ir4na7dz0().s[175]++;
              // Return default market data for unknown skills
              marketData = this.getDefaultMarketData(skill);
              /* istanbul ignore next */
              cov_ir4na7dz0().s[176]++;
              this.marketDataCache.set(cacheKey, marketData);
              /* istanbul ignore next */
              cov_ir4na7dz0().s[177]++;
              this.recordMetrics('getMarketData', startTime);
              /* istanbul ignore next */
              cov_ir4na7dz0().s[178]++;
              return [2 /*return*/, marketData];
            } else
            /* istanbul ignore next */
            {
              cov_ir4na7dz0().b[45][1]++;
            }
            cov_ir4na7dz0().s[179]++;
            return [4 /*yield*/, prisma_1.prisma.skillMarketData.findFirst({
              where: {
                skillId: skillRecord.id,
                isActive: true
              },
              orderBy: {
                dataDate: 'desc'
              },
              select: {
                demandLevel: true,
                averageSalaryImpact: true,
                growthTrend: true,
                dataDate: true,
                region: true
              }
            })];
          case 3:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[42][3]++;
            cov_ir4na7dz0().s[180]++;
            dbMarketData = _a.sent();
            // Ensure we always include the skill name in the response
            /* istanbul ignore next */
            cov_ir4na7dz0().s[181]++;
            marketData = dbMarketData ?
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[46][0]++, __assign(__assign({}, dbMarketData), {
              skill: skill.toLowerCase(),
              skillId: skillRecord.id,
              skillName: skillRecord.name
            })) :
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[46][1]++, this.getDefaultMarketData(skill));
            /* istanbul ignore next */
            cov_ir4na7dz0().s[182]++;
            this.marketDataCache.set(cacheKey, marketData);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[183]++;
            return [3 /*break*/, 5];
          case 4:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[42][4]++;
            cov_ir4na7dz0().s[184]++;
            error_3 = _a.sent();
            /* istanbul ignore next */
            cov_ir4na7dz0().s[185]++;
            console.error('Error fetching market data:', error_3);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[186]++;
            marketData = this.getDefaultMarketData(skill);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[187]++;
            this.marketDataCache.set(cacheKey, marketData);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[188]++;
            return [3 /*break*/, 5];
          case 5:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[42][5]++;
            cov_ir4na7dz0().s[189]++;
            this.recordMetrics('getMarketData', startTime);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[190]++;
            return [2 /*return*/, marketData];
        }
      });
    });
  };
  /**
   * Optimized skill lookup by name with caching
   */
  /* istanbul ignore next */
  cov_ir4na7dz0().s[191]++;
  SkillGapPerformanceOptimizer.prototype.getSkillByName = function (skillName) {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[27]++;
    cov_ir4na7dz0().s[192]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_ir4na7dz0().f[28]++;
      var cacheKey, skill, error_4;
      /* istanbul ignore next */
      cov_ir4na7dz0().s[193]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_ir4na7dz0().f[29]++;
        cov_ir4na7dz0().s[194]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[47][0]++;
            cov_ir4na7dz0().s[195]++;
            cacheKey = "skill:name:".concat(skillName.toLowerCase());
            /* istanbul ignore next */
            cov_ir4na7dz0().s[196]++;
            skill = this.skillCache.get(cacheKey);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[197]++;
            if (skill) {
              /* istanbul ignore next */
              cov_ir4na7dz0().b[48][0]++;
              cov_ir4na7dz0().s[198]++;
              this.recordCacheHit('getSkillByName');
              /* istanbul ignore next */
              cov_ir4na7dz0().s[199]++;
              return [2 /*return*/, skill];
            } else
            /* istanbul ignore next */
            {
              cov_ir4na7dz0().b[48][1]++;
            }
            cov_ir4na7dz0().s[200]++;
            this.recordCacheMiss('getSkillByName');
            /* istanbul ignore next */
            cov_ir4na7dz0().s[201]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[47][1]++;
            cov_ir4na7dz0().s[202]++;
            _a.trys.push([1, 3,, 4]);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[203]++;
            return [4 /*yield*/, prisma_1.prisma.skill.findFirst({
              where: {
                name: {
                  equals: skillName,
                  mode: 'insensitive'
                }
              },
              select: {
                id: true,
                name: true,
                category: true
              }
            })];
          case 2:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[47][2]++;
            cov_ir4na7dz0().s[204]++;
            skill = _a.sent();
            /* istanbul ignore next */
            cov_ir4na7dz0().s[205]++;
            if (skill) {
              /* istanbul ignore next */
              cov_ir4na7dz0().b[49][0]++;
              cov_ir4na7dz0().s[206]++;
              this.skillCache.set(cacheKey, skill);
            } else
            /* istanbul ignore next */
            {
              cov_ir4na7dz0().b[49][1]++;
            }
            cov_ir4na7dz0().s[207]++;
            return [3 /*break*/, 4];
          case 3:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[47][3]++;
            cov_ir4na7dz0().s[208]++;
            error_4 = _a.sent();
            /* istanbul ignore next */
            cov_ir4na7dz0().s[209]++;
            console.error('Error fetching skill by name:', error_4);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[210]++;
            skill = null;
            /* istanbul ignore next */
            cov_ir4na7dz0().s[211]++;
            return [3 /*break*/, 4];
          case 4:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[47][4]++;
            cov_ir4na7dz0().s[212]++;
            return [2 /*return*/, skill];
        }
      });
    });
  };
  /**
   * Batch assessment creation with optimized database operations
   */
  /* istanbul ignore next */
  cov_ir4na7dz0().s[213]++;
  SkillGapPerformanceOptimizer.prototype.createAssessmentsBatch = function (assessments) {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[30]++;
    cov_ir4na7dz0().s[214]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_ir4na7dz0().f[31]++;
      var startTime, results, _i, results_1, assessment, cacheKey;
      var _this =
      /* istanbul ignore next */
      (cov_ir4na7dz0().s[215]++, this);
      /* istanbul ignore next */
      cov_ir4na7dz0().s[216]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_ir4na7dz0().f[32]++;
        cov_ir4na7dz0().s[217]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[50][0]++;
            cov_ir4na7dz0().s[218]++;
            startTime = perf_hooks_1.performance.now();
            /* istanbul ignore next */
            cov_ir4na7dz0().s[219]++;
            return [4 /*yield*/, prisma_1.prisma.$transaction(function (tx) {
              /* istanbul ignore next */
              cov_ir4na7dz0().f[33]++;
              cov_ir4na7dz0().s[220]++;
              return __awaiter(_this, void 0, void 0, function () {
                /* istanbul ignore next */
                cov_ir4na7dz0().f[34]++;
                var createdAssessments, _i, assessments_1, assessment, created;
                /* istanbul ignore next */
                cov_ir4na7dz0().s[221]++;
                return __generator(this, function (_a) {
                  /* istanbul ignore next */
                  cov_ir4na7dz0().f[35]++;
                  cov_ir4na7dz0().s[222]++;
                  switch (_a.label) {
                    case 0:
                      /* istanbul ignore next */
                      cov_ir4na7dz0().b[51][0]++;
                      cov_ir4na7dz0().s[223]++;
                      createdAssessments = [];
                      /* istanbul ignore next */
                      cov_ir4na7dz0().s[224]++;
                      _i = 0, assessments_1 = assessments;
                      /* istanbul ignore next */
                      cov_ir4na7dz0().s[225]++;
                      _a.label = 1;
                    case 1:
                      /* istanbul ignore next */
                      cov_ir4na7dz0().b[51][1]++;
                      cov_ir4na7dz0().s[226]++;
                      if (!(_i < assessments_1.length)) {
                        /* istanbul ignore next */
                        cov_ir4na7dz0().b[52][0]++;
                        cov_ir4na7dz0().s[227]++;
                        return [3 /*break*/, 4];
                      } else
                      /* istanbul ignore next */
                      {
                        cov_ir4na7dz0().b[52][1]++;
                      }
                      cov_ir4na7dz0().s[228]++;
                      assessment = assessments_1[_i];
                      /* istanbul ignore next */
                      cov_ir4na7dz0().s[229]++;
                      return [4 /*yield*/, tx.skillAssessment.create({
                        data: assessment,
                        select: {
                          id: true,
                          userId: true,
                          skillId: true,
                          selfRating: true,
                          confidenceLevel: true,
                          assessmentDate: true
                        }
                      })];
                    case 2:
                      /* istanbul ignore next */
                      cov_ir4na7dz0().b[51][2]++;
                      cov_ir4na7dz0().s[230]++;
                      created = _a.sent();
                      /* istanbul ignore next */
                      cov_ir4na7dz0().s[231]++;
                      createdAssessments.push(created);
                      /* istanbul ignore next */
                      cov_ir4na7dz0().s[232]++;
                      _a.label = 3;
                    case 3:
                      /* istanbul ignore next */
                      cov_ir4na7dz0().b[51][3]++;
                      cov_ir4na7dz0().s[233]++;
                      _i++;
                      /* istanbul ignore next */
                      cov_ir4na7dz0().s[234]++;
                      return [3 /*break*/, 1];
                    case 4:
                      /* istanbul ignore next */
                      cov_ir4na7dz0().b[51][4]++;
                      cov_ir4na7dz0().s[235]++;
                      return [2 /*return*/, createdAssessments];
                  }
                });
              });
            })];
          case 1:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[50][1]++;
            cov_ir4na7dz0().s[236]++;
            results = _a.sent();
            // Cache the results
            /* istanbul ignore next */
            cov_ir4na7dz0().s[237]++;
            for (_i = 0, results_1 = results; _i < results_1.length; _i++) {
              /* istanbul ignore next */
              cov_ir4na7dz0().s[238]++;
              assessment = results_1[_i];
              /* istanbul ignore next */
              cov_ir4na7dz0().s[239]++;
              cacheKey = "assessment:".concat(assessment.userId, ":").concat(assessment.skillId);
              /* istanbul ignore next */
              cov_ir4na7dz0().s[240]++;
              this.assessmentCache.set(cacheKey, assessment);
            }
            /* istanbul ignore next */
            cov_ir4na7dz0().s[241]++;
            this.recordMetrics('createAssessmentsBatch', startTime);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[242]++;
            return [2 /*return*/, results];
        }
      });
    });
  };
  /**
   * Add operation to batch queue for processing
   */
  /* istanbul ignore next */
  cov_ir4na7dz0().s[243]++;
  SkillGapPerformanceOptimizer.prototype.addToBatch = function (batchType_1, operationId_1, operation_1) {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[36]++;
    cov_ir4na7dz0().s[244]++;
    return __awaiter(this, arguments, Promise, function (batchType, operationId, operation, priority) {
      /* istanbul ignore next */
      cov_ir4na7dz0().f[37]++;
      var _this =
      /* istanbul ignore next */
      (cov_ir4na7dz0().s[245]++, this);
      /* istanbul ignore next */
      cov_ir4na7dz0().s[246]++;
      if (priority === void 0) {
        /* istanbul ignore next */
        cov_ir4na7dz0().b[53][0]++;
        cov_ir4na7dz0().s[247]++;
        priority = 1;
      } else
      /* istanbul ignore next */
      {
        cov_ir4na7dz0().b[53][1]++;
      }
      cov_ir4na7dz0().s[248]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_ir4na7dz0().f[38]++;
        cov_ir4na7dz0().s[249]++;
        return [2 /*return*/, new Promise(function (resolve, reject) {
          /* istanbul ignore next */
          cov_ir4na7dz0().f[39]++;
          cov_ir4na7dz0().s[250]++;
          if (!_this.batchQueue.has(batchType)) {
            /* istanbul ignore next */
            cov_ir4na7dz0().b[54][0]++;
            cov_ir4na7dz0().s[251]++;
            _this.batchQueue.set(batchType, []);
          } else
          /* istanbul ignore next */
          {
            cov_ir4na7dz0().b[54][1]++;
          }
          var batch =
          /* istanbul ignore next */
          (cov_ir4na7dz0().s[252]++, _this.batchQueue.get(batchType));
          /* istanbul ignore next */
          cov_ir4na7dz0().s[253]++;
          batch.push({
            id: operationId,
            operation: function () {
              /* istanbul ignore next */
              cov_ir4na7dz0().f[40]++;
              cov_ir4na7dz0().s[254]++;
              return __awaiter(_this, void 0, void 0, function () {
                /* istanbul ignore next */
                cov_ir4na7dz0().f[41]++;
                var result, error_5;
                /* istanbul ignore next */
                cov_ir4na7dz0().s[255]++;
                return __generator(this, function (_a) {
                  /* istanbul ignore next */
                  cov_ir4na7dz0().f[42]++;
                  cov_ir4na7dz0().s[256]++;
                  switch (_a.label) {
                    case 0:
                      /* istanbul ignore next */
                      cov_ir4na7dz0().b[55][0]++;
                      cov_ir4na7dz0().s[257]++;
                      _a.trys.push([0, 2,, 3]);
                      /* istanbul ignore next */
                      cov_ir4na7dz0().s[258]++;
                      return [4 /*yield*/, operation()];
                    case 1:
                      /* istanbul ignore next */
                      cov_ir4na7dz0().b[55][1]++;
                      cov_ir4na7dz0().s[259]++;
                      result = _a.sent();
                      /* istanbul ignore next */
                      cov_ir4na7dz0().s[260]++;
                      resolve(result);
                      /* istanbul ignore next */
                      cov_ir4na7dz0().s[261]++;
                      return [2 /*return*/, result];
                    case 2:
                      /* istanbul ignore next */
                      cov_ir4na7dz0().b[55][2]++;
                      cov_ir4na7dz0().s[262]++;
                      error_5 = _a.sent();
                      /* istanbul ignore next */
                      cov_ir4na7dz0().s[263]++;
                      reject(error_5);
                      /* istanbul ignore next */
                      cov_ir4na7dz0().s[264]++;
                      throw error_5;
                    case 3:
                      /* istanbul ignore next */
                      cov_ir4na7dz0().b[55][3]++;
                      cov_ir4na7dz0().s[265]++;
                      return [2 /*return*/];
                  }
                });
              });
            },
            priority: priority
          });
          // Sort by priority
          /* istanbul ignore next */
          cov_ir4na7dz0().s[266]++;
          batch.sort(function (a, b) {
            /* istanbul ignore next */
            cov_ir4na7dz0().f[43]++;
            cov_ir4na7dz0().s[267]++;
            return b.priority - a.priority;
          });
          // Process batch if it's full or set timer
          /* istanbul ignore next */
          cov_ir4na7dz0().s[268]++;
          if (batch.length >= _this.MAX_BATCH_SIZE) {
            /* istanbul ignore next */
            cov_ir4na7dz0().b[56][0]++;
            cov_ir4na7dz0().s[269]++;
            _this.processBatch(batchType);
          } else {
            /* istanbul ignore next */
            cov_ir4na7dz0().b[56][1]++;
            cov_ir4na7dz0().s[270]++;
            _this.scheduleBatchProcessing(batchType);
          }
        })];
      });
    });
  };
  /**
   * Process a batch of operations
   */
  /* istanbul ignore next */
  cov_ir4na7dz0().s[271]++;
  SkillGapPerformanceOptimizer.prototype.processBatch = function (batchType) {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[44]++;
    cov_ir4na7dz0().s[272]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_ir4na7dz0().f[45]++;
      var batch, timer;
      /* istanbul ignore next */
      cov_ir4na7dz0().s[273]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_ir4na7dz0().f[46]++;
        cov_ir4na7dz0().s[274]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[57][0]++;
            cov_ir4na7dz0().s[275]++;
            batch = this.batchQueue.get(batchType);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[276]++;
            if (
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[59][0]++, !batch) ||
            /* istanbul ignore next */
            (cov_ir4na7dz0().b[59][1]++, batch.length === 0)) {
              /* istanbul ignore next */
              cov_ir4na7dz0().b[58][0]++;
              cov_ir4na7dz0().s[277]++;
              return [2 /*return*/];
            } else
            /* istanbul ignore next */
            {
              cov_ir4na7dz0().b[58][1]++;
            }
            // Clear the batch and timer
            cov_ir4na7dz0().s[278]++;
            this.batchQueue.set(batchType, []);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[279]++;
            timer = this.batchTimers.get(batchType);
            /* istanbul ignore next */
            cov_ir4na7dz0().s[280]++;
            if (timer) {
              /* istanbul ignore next */
              cov_ir4na7dz0().b[60][0]++;
              cov_ir4na7dz0().s[281]++;
              clearTimeout(timer);
              /* istanbul ignore next */
              cov_ir4na7dz0().s[282]++;
              this.batchTimers.delete(batchType);
            } else
            /* istanbul ignore next */
            {
              cov_ir4na7dz0().b[60][1]++;
            }
            // Execute all operations in parallel
            cov_ir4na7dz0().s[283]++;
            return [4 /*yield*/, Promise.allSettled(batch.map(function (item) {
              /* istanbul ignore next */
              cov_ir4na7dz0().f[47]++;
              cov_ir4na7dz0().s[284]++;
              return item.operation();
            }))];
          case 1:
            /* istanbul ignore next */
            cov_ir4na7dz0().b[57][1]++;
            cov_ir4na7dz0().s[285]++;
            // Execute all operations in parallel
            _a.sent();
            /* istanbul ignore next */
            cov_ir4na7dz0().s[286]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Schedule batch processing with delay
   */
  /* istanbul ignore next */
  cov_ir4na7dz0().s[287]++;
  SkillGapPerformanceOptimizer.prototype.scheduleBatchProcessing = function (batchType) {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[48]++;
    var _this =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[288]++, this);
    /* istanbul ignore next */
    cov_ir4na7dz0().s[289]++;
    if (this.batchTimers.has(batchType)) {
      /* istanbul ignore next */
      cov_ir4na7dz0().b[61][0]++;
      cov_ir4na7dz0().s[290]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_ir4na7dz0().b[61][1]++;
    }
    var timer =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[291]++, setTimeout(function () {
      /* istanbul ignore next */
      cov_ir4na7dz0().f[49]++;
      cov_ir4na7dz0().s[292]++;
      _this.processBatch(batchType);
    }, this.BATCH_DELAY));
    /* istanbul ignore next */
    cov_ir4na7dz0().s[293]++;
    this.batchTimers.set(batchType, timer);
  };
  /**
   * Get default market data for unknown skills
   */
  /* istanbul ignore next */
  cov_ir4na7dz0().s[294]++;
  SkillGapPerformanceOptimizer.prototype.getDefaultMarketData = function (skill) {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[50]++;
    cov_ir4na7dz0().s[295]++;
    return {
      skill: skill.toLowerCase(),
      demand: 50,
      supply: 50,
      averageSalary: 75000,
      growth: 5,
      difficulty: 5,
      timeToLearn: 12,
      category: 'Unknown',
      lastUpdated: new Date().toISOString(),
      isStale: true
    };
  };
  /**
   * Record cache hit
   */
  /* istanbul ignore next */
  cov_ir4na7dz0().s[296]++;
  SkillGapPerformanceOptimizer.prototype.recordCacheHit = function (type) {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[51]++;
    var current =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[297]++,
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[62][0]++, this.cacheHits.get(type)) ||
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[62][1]++, 0));
    /* istanbul ignore next */
    cov_ir4na7dz0().s[298]++;
    this.cacheHits.set(type, current + 1);
  };
  /**
   * Record cache miss
   */
  /* istanbul ignore next */
  cov_ir4na7dz0().s[299]++;
  SkillGapPerformanceOptimizer.prototype.recordCacheMiss = function (type) {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[52]++;
    var current =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[300]++,
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[63][0]++, this.cacheMisses.get(type)) ||
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[63][1]++, 0));
    /* istanbul ignore next */
    cov_ir4na7dz0().s[301]++;
    this.cacheMisses.set(type, current + 1);
  };
  /**
   * Record performance metrics
   */
  /* istanbul ignore next */
  cov_ir4na7dz0().s[302]++;
  SkillGapPerformanceOptimizer.prototype.recordMetrics = function (operation, startTime) {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[53]++;
    var endTime =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[303]++, perf_hooks_1.performance.now());
    var queryTime =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[304]++, endTime - startTime);
    var current =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[305]++,
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[64][0]++, this.operationCounts.get(operation)) ||
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[64][1]++, 0));
    /* istanbul ignore next */
    cov_ir4na7dz0().s[306]++;
    this.operationCounts.set(operation, current + 1);
    // Calculate cache hit rate for this specific operation
    var hits =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[307]++,
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[65][0]++, this.cacheHits.get(operation)) ||
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[65][1]++, 0));
    var misses =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[308]++,
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[66][0]++, this.cacheMisses.get(operation)) ||
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[66][1]++, 0));
    var total =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[309]++, hits + misses);
    var cacheHitRate =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[310]++, total > 0 ?
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[67][0]++, hits / total) :
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[67][1]++, 0));
    /* istanbul ignore next */
    cov_ir4na7dz0().s[311]++;
    this.metrics.set(operation, {
      queryTime: queryTime,
      cacheHitRate: cacheHitRate,
      memoryUsage: process.memoryUsage().heapUsed,
      operationsPerSecond: current + 1 // Include current operation
    });
  };
  /**
   * Get performance metrics
   */
  /* istanbul ignore next */
  cov_ir4na7dz0().s[312]++;
  SkillGapPerformanceOptimizer.prototype.getPerformanceMetrics = function () {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[54]++;
    cov_ir4na7dz0().s[313]++;
    return new Map(this.metrics);
  };
  /**
   * Clear all caches
   */
  /* istanbul ignore next */
  cov_ir4na7dz0().s[314]++;
  SkillGapPerformanceOptimizer.prototype.clearCaches = function () {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[55]++;
    cov_ir4na7dz0().s[315]++;
    this.userCache.clear();
    /* istanbul ignore next */
    cov_ir4na7dz0().s[316]++;
    this.skillCache.clear();
    /* istanbul ignore next */
    cov_ir4na7dz0().s[317]++;
    this.marketDataCache.clear();
    /* istanbul ignore next */
    cov_ir4na7dz0().s[318]++;
    this.assessmentCache.clear();
  };
  /**
   * Get cache statistics
   */
  /* istanbul ignore next */
  cov_ir4na7dz0().s[319]++;
  SkillGapPerformanceOptimizer.prototype.getCacheStats = function () {
    /* istanbul ignore next */
    cov_ir4na7dz0().f[56]++;
    // Aggregate hits and misses for related operations
    var userHits =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[320]++,
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[68][0]++, this.cacheHits.get('getUser')) ||
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[68][1]++, 0));
    var userMisses =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[321]++,
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[69][0]++, this.cacheMisses.get('getUser')) ||
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[69][1]++, 0));
    var skillHits =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[322]++, (
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[70][0]++, this.cacheHits.get('getSkills')) ||
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[70][1]++, 0)) + (
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[71][0]++, this.cacheHits.get('getSkillByName')) ||
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[71][1]++, 0)));
    var skillMisses =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[323]++, (
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[72][0]++, this.cacheMisses.get('getSkills')) ||
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[72][1]++, 0)) + (
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[73][0]++, this.cacheMisses.get('getSkillByName')) ||
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[73][1]++, 0)));
    var marketDataHits =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[324]++,
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[74][0]++, this.cacheHits.get('getMarketData')) ||
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[74][1]++, 0));
    var marketDataMisses =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[325]++,
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[75][0]++, this.cacheMisses.get('getMarketData')) ||
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[75][1]++, 0));
    var assessmentHits =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[326]++,
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[76][0]++, this.cacheHits.get('assessment')) ||
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[76][1]++, 0));
    var assessmentMisses =
    /* istanbul ignore next */
    (cov_ir4na7dz0().s[327]++,
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[77][0]++, this.cacheMisses.get('assessment')) ||
    /* istanbul ignore next */
    (cov_ir4na7dz0().b[77][1]++, 0));
    /* istanbul ignore next */
    cov_ir4na7dz0().s[328]++;
    return {
      user: {
        size: this.userCache.getStats().size,
        hits: userHits,
        misses: userMisses
      },
      skill: {
        size: this.skillCache.getStats().size,
        hits: skillHits,
        misses: skillMisses
      },
      marketData: {
        size: this.marketDataCache.getStats().size,
        hits: marketDataHits,
        misses: marketDataMisses
      },
      assessment: {
        size: this.assessmentCache.getStats().size,
        hits: assessmentHits,
        misses: assessmentMisses
      }
    };
  };
  /* istanbul ignore next */
  cov_ir4na7dz0().s[329]++;
  return SkillGapPerformanceOptimizer;
}());
/* istanbul ignore next */
cov_ir4na7dz0().s[330]++;
exports.SkillGapPerformanceOptimizer = SkillGapPerformanceOptimizer;
// Export singleton instance
/* istanbul ignore next */
cov_ir4na7dz0().s[331]++;
exports.skillGapPerformanceOptimizer = SkillGapPerformanceOptimizer.getInstance();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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