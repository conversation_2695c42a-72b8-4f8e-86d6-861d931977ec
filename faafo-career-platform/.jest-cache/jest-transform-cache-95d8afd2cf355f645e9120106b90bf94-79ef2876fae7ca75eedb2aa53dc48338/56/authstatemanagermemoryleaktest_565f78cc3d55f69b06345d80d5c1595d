65d9fe39a7b899876095182c310109a4
"use strict";
/**
 * Memory Leak Test for Auth State Manager
 *
 * This test verifies that the auth state manager properly cleans up
 * intervals and event listeners to prevent memory leaks.
 */
Object.defineProperty(exports, "__esModule", { value: true });
// Mock next-auth
jest.mock('next-auth/react', function () { return ({
    getSession: jest.fn().mockResolvedValue(null)
}); });
var auth_state_manager_1 = require("../auth-state-manager");
// Mock window and localStorage for testing
var mockWindow = {
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    localStorage: {
        setItem: jest.fn(),
        getItem: jest.fn(),
        removeItem: jest.fn()
    }
};
// Mock setInterval and clearInterval
var mockSetInterval = jest.fn();
var mockClearInterval = jest.fn();
describe('Auth State Manager Memory Leak Prevention', function () {
    var originalSetInterval;
    var originalClearInterval;
    beforeEach(function () {
        // Store original globals
        originalSetInterval = global.setInterval;
        originalClearInterval = global.clearInterval;
        // Mock globals
        global.setInterval = mockSetInterval;
        global.clearInterval = mockClearInterval;
        // Ensure window is available for testing
        if (typeof global.window === 'undefined') {
            global.window = mockWindow;
        }
        else {
            // Replace window methods with mocks
            global.window.addEventListener = mockWindow.addEventListener;
            global.window.removeEventListener = mockWindow.removeEventListener;
        }
        // Reset mocks
        jest.clearAllMocks();
        // Mock setInterval to return a fake timer ID
        mockSetInterval.mockReturnValue(12345);
    });
    afterEach(function () {
        // Clean up any existing auth state manager first
        (0, auth_state_manager_1.destroyAuthStateManager)();
        // Restore original globals
        global.setInterval = originalSetInterval;
        global.clearInterval = originalClearInterval;
    });
    test('should set up interval and event listeners on initialization', function () {
        // Create auth state manager instance
        var authStateManager = (0, auth_state_manager_1.getAuthStateManager)();
        // Verify interval was set up
        expect(mockSetInterval).toHaveBeenCalledWith(expect.any(Function), 10 * 60 * 1000 // 10 minutes
        );
        // Verify event listeners were added
        expect(mockWindow.addEventListener).toHaveBeenCalledWith('storage', expect.any(Function));
        expect(mockWindow.addEventListener).toHaveBeenCalledWith('focus', expect.any(Function));
        expect(mockWindow.addEventListener).toHaveBeenCalledWith('beforeunload', expect.any(Function));
    });
    test('should clean up interval and event listeners on destroy', function () {
        // Create auth state manager instance
        var authStateManager = (0, auth_state_manager_1.getAuthStateManager)();
        // Reset mocks to track cleanup calls
        jest.clearAllMocks();
        // Destroy the auth state manager
        (0, auth_state_manager_1.destroyAuthStateManager)();
        // Verify interval was cleared
        expect(mockClearInterval).toHaveBeenCalledWith(12345);
        // Verify event listeners were removed
        expect(mockWindow.removeEventListener).toHaveBeenCalledWith('storage', expect.any(Function));
        expect(mockWindow.removeEventListener).toHaveBeenCalledWith('focus', expect.any(Function));
        expect(mockWindow.removeEventListener).toHaveBeenCalledWith('beforeunload', expect.any(Function));
    });
    test('should not perform operations on destroyed instance', function () {
        // Create and destroy auth state manager
        var authStateManager = (0, auth_state_manager_1.getAuthStateManager)();
        (0, auth_state_manager_1.destroyAuthStateManager)();
        // Reset mocks
        jest.clearAllMocks();
        // Try to subscribe to a destroyed instance
        var unsubscribe = authStateManager.subscribe(function () { });
        // Should return a no-op function
        expect(typeof unsubscribe).toBe('function');
        // Calling unsubscribe should not throw
        expect(function () { return unsubscribe(); }).not.toThrow();
    });
    test('should prevent double initialization', function () {
        // Create auth state manager instance
        var authStateManager1 = (0, auth_state_manager_1.getAuthStateManager)();
        // Reset mocks to track second initialization
        jest.clearAllMocks();
        // Get the same instance again
        var authStateManager2 = (0, auth_state_manager_1.getAuthStateManager)();
        // Should be the same instance
        expect(authStateManager1).toBe(authStateManager2);
        // Should not set up interval again
        expect(mockSetInterval).not.toHaveBeenCalled();
    });
    test('should add beforeunload listener for activity tracking', function () {
        // Create auth state manager instance
        (0, auth_state_manager_1.getAuthStateManager)();
        // Find the beforeunload listener that was added by the auth manager
        var beforeUnloadCalls = mockWindow.addEventListener.mock.calls.filter(function (call) { return call[0] === 'beforeunload'; });
        // Should have at least one beforeunload listener (from auth manager)
        expect(beforeUnloadCalls.length).toBeGreaterThanOrEqual(1);
        // Get the auth manager's beforeunload listener
        var activityListener = beforeUnloadCalls[0][1];
        // Should not throw when called
        expect(function () { return activityListener(); }).not.toThrow();
    });
    test('should handle automatic cleanup via destroyAuthStateManager', function () {
        // Create auth state manager instance
        (0, auth_state_manager_1.getAuthStateManager)();
        // Reset mocks to track cleanup
        jest.clearAllMocks();
        // Call destroy function directly (simulates what happens on page unload)
        (0, auth_state_manager_1.destroyAuthStateManager)();
        // Should have cleaned up the interval
        expect(mockClearInterval).toHaveBeenCalledWith(12345);
    });
    test('should not crash when destroying non-existent instance', function () {
        // Should not throw when destroying before creation
        expect(function () { return (0, auth_state_manager_1.destroyAuthStateManager)(); }).not.toThrow();
        // Should not call clearInterval if no instance exists
        expect(mockClearInterval).not.toHaveBeenCalled();
    });
    test('should handle interval callback safely after destruction', function () {
        // Create auth state manager instance
        (0, auth_state_manager_1.getAuthStateManager)();
        // Get the interval callback
        var intervalCallback = mockSetInterval.mock.calls[0][0];
        // Destroy the instance
        (0, auth_state_manager_1.destroyAuthStateManager)();
        // Calling the interval callback should not throw
        expect(function () { return intervalCallback(); }).not.toThrow();
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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