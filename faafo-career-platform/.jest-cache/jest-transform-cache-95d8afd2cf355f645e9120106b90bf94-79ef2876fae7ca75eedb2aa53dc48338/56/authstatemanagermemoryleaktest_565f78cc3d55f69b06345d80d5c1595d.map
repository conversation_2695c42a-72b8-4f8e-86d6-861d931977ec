{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/__tests__/auth-state-manager-memory-leak.test.ts", "mappings": ";AAAA;;;;;GAKG;;AAIH,iBAAiB;AACjB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,cAAM,OAAA,CAAC;IAClC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC;CAC9C,CAAC,EAFiC,CAEjC,CAAC,CAAC;AALJ,4DAAqF;AAOrF,2CAA2C;AAC3C,IAAM,UAAU,GAAG;IACjB,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;IAC3B,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE;IAC9B,YAAY,EAAE;QACZ,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;KACtB;CACF,CAAC;AAEF,qCAAqC;AACrC,IAAM,eAAe,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;AAClC,IAAM,iBAAiB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;AAEpC,QAAQ,CAAC,2CAA2C,EAAE;IACpD,IAAI,mBAAwB,CAAC;IAC7B,IAAI,qBAA0B,CAAC;IAE/B,UAAU,CAAC;QACT,yBAAyB;QACzB,mBAAmB,GAAG,MAAM,CAAC,WAAW,CAAC;QACzC,qBAAqB,GAAG,MAAM,CAAC,aAAa,CAAC;QAE7C,eAAe;QACf,MAAM,CAAC,WAAW,GAAG,eAAe,CAAC;QACrC,MAAM,CAAC,aAAa,GAAG,iBAAiB,CAAC;QAEzC,yCAAyC;QACzC,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;YACxC,MAAc,CAAC,MAAM,GAAG,UAAU,CAAC;QACtC,CAAC;aAAM,CAAC;YACN,oCAAoC;YACpC,MAAM,CAAC,MAAM,CAAC,gBAAgB,GAAG,UAAU,CAAC,gBAAgB,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,mBAAmB,GAAG,UAAU,CAAC,mBAAmB,CAAC;QACrE,CAAC;QAED,cAAc;QACd,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,6CAA6C;QAC7C,eAAe,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC;QACR,iDAAiD;QACjD,IAAA,4CAAuB,GAAE,CAAC;QAE1B,2BAA2B;QAC3B,MAAM,CAAC,WAAW,GAAG,mBAAmB,CAAC;QACzC,MAAM,CAAC,aAAa,GAAG,qBAAqB,CAAC;IAC/C,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,8DAA8D,EAAE;QACnE,qCAAqC;QACrC,IAAM,gBAAgB,GAAG,IAAA,wCAAmB,GAAE,CAAC;QAE/C,6BAA6B;QAC7B,MAAM,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAC1C,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EACpB,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa;SAC7B,CAAC;QAEF,oCAAoC;QACpC,MAAM,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,oBAAoB,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC1F,MAAM,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,oBAAoB,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;QACxF,MAAM,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,oBAAoB,CAAC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;IACjG,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,yDAAyD,EAAE;QAC9D,qCAAqC;QACrC,IAAM,gBAAgB,GAAG,IAAA,wCAAmB,GAAE,CAAC;QAE/C,qCAAqC;QACrC,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,iCAAiC;QACjC,IAAA,4CAAuB,GAAE,CAAC;QAE1B,8BAA8B;QAC9B,MAAM,CAAC,iBAAiB,CAAC,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAEtD,sCAAsC;QACtC,MAAM,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,oBAAoB,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC7F,MAAM,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,oBAAoB,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC3F,MAAM,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,oBAAoB,CAAC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;IACpG,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,qDAAqD,EAAE;QAC1D,wCAAwC;QACxC,IAAM,gBAAgB,GAAG,IAAA,wCAAmB,GAAE,CAAC;QAC/C,IAAA,4CAAuB,GAAE,CAAC;QAE1B,cAAc;QACd,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,2CAA2C;QAC3C,IAAM,WAAW,GAAG,gBAAgB,CAAC,SAAS,CAAC,cAAO,CAAC,CAAC,CAAC;QAEzD,iCAAiC;QACjC,MAAM,CAAC,OAAO,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE5C,uCAAuC;QACvC,MAAM,CAAC,cAAM,OAAA,WAAW,EAAE,EAAb,CAAa,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,sCAAsC,EAAE;QAC3C,qCAAqC;QACrC,IAAM,iBAAiB,GAAG,IAAA,wCAAmB,GAAE,CAAC;QAEhD,6CAA6C;QAC7C,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,8BAA8B;QAC9B,IAAM,iBAAiB,GAAG,IAAA,wCAAmB,GAAE,CAAC;QAEhD,8BAA8B;QAC9B,MAAM,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAElD,mCAAmC;QACnC,MAAM,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,wDAAwD,EAAE;QAC7D,qCAAqC;QACrC,IAAA,wCAAmB,GAAE,CAAC;QAEtB,oEAAoE;QACpE,IAAM,iBAAiB,GAAG,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CACrE,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,CAAC,CAAC,KAAK,cAAc,EAA1B,CAA0B,CACnC,CAAC;QAEF,qEAAqE;QACrE,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QAE3D,+CAA+C;QAC/C,IAAM,gBAAgB,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjD,+BAA+B;QAC/B,MAAM,CAAC,cAAM,OAAA,gBAAgB,EAAE,EAAlB,CAAkB,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,6DAA6D,EAAE;QAClE,qCAAqC;QACrC,IAAA,wCAAmB,GAAE,CAAC;QAEtB,+BAA+B;QAC/B,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,yEAAyE;QACzE,IAAA,4CAAuB,GAAE,CAAC;QAE1B,sCAAsC;QACtC,MAAM,CAAC,iBAAiB,CAAC,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,wDAAwD,EAAE;QAC7D,mDAAmD;QACnD,MAAM,CAAC,cAAM,OAAA,IAAA,4CAAuB,GAAE,EAAzB,CAAyB,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QAEtD,sDAAsD;QACtD,MAAM,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;IACnD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,0DAA0D,EAAE;QAC/D,qCAAqC;QACrC,IAAA,wCAAmB,GAAE,CAAC;QAEtB,4BAA4B;QAC5B,IAAM,gBAAgB,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1D,uBAAuB;QACvB,IAAA,4CAAuB,GAAE,CAAC;QAE1B,iDAAiD;QACjD,MAAM,CAAC,cAAM,OAAA,gBAAgB,EAAE,EAAlB,CAAkB,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;IACjD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/__tests__/auth-state-manager-memory-leak.test.ts"], "sourcesContent": ["/**\n * Memory Leak Test for Auth State Manager\n * \n * This test verifies that the auth state manager properly cleans up\n * intervals and event listeners to prevent memory leaks.\n */\n\nimport { getAuthStateManager, destroyAuthStateManager } from '../auth-state-manager';\n\n// Mock next-auth\njest.mock('next-auth/react', () => ({\n  getSession: jest.fn().mockResolvedValue(null)\n}));\n\n// Mock window and localStorage for testing\nconst mockWindow = {\n  addEventListener: jest.fn(),\n  removeEventListener: jest.fn(),\n  localStorage: {\n    setItem: jest.fn(),\n    getItem: jest.fn(),\n    removeItem: jest.fn()\n  }\n};\n\n// Mock setInterval and clearInterval\nconst mockSetInterval = jest.fn();\nconst mockClearInterval = jest.fn();\n\ndescribe('Auth State Manager Memory Leak Prevention', () => {\n  let originalSetInterval: any;\n  let originalClearInterval: any;\n\n  beforeEach(() => {\n    // Store original globals\n    originalSetInterval = global.setInterval;\n    originalClearInterval = global.clearInterval;\n\n    // Mock globals\n    global.setInterval = mockSetInterval;\n    global.clearInterval = mockClearInterval;\n\n    // Ensure window is available for testing\n    if (typeof global.window === 'undefined') {\n      (global as any).window = mockWindow;\n    } else {\n      // Replace window methods with mocks\n      global.window.addEventListener = mockWindow.addEventListener;\n      global.window.removeEventListener = mockWindow.removeEventListener;\n    }\n\n    // Reset mocks\n    jest.clearAllMocks();\n\n    // Mock setInterval to return a fake timer ID\n    mockSetInterval.mockReturnValue(12345);\n  });\n\n  afterEach(() => {\n    // Clean up any existing auth state manager first\n    destroyAuthStateManager();\n\n    // Restore original globals\n    global.setInterval = originalSetInterval;\n    global.clearInterval = originalClearInterval;\n  });\n\n  test('should set up interval and event listeners on initialization', () => {\n    // Create auth state manager instance\n    const authStateManager = getAuthStateManager();\n\n    // Verify interval was set up\n    expect(mockSetInterval).toHaveBeenCalledWith(\n      expect.any(Function),\n      10 * 60 * 1000 // 10 minutes\n    );\n\n    // Verify event listeners were added\n    expect(mockWindow.addEventListener).toHaveBeenCalledWith('storage', expect.any(Function));\n    expect(mockWindow.addEventListener).toHaveBeenCalledWith('focus', expect.any(Function));\n    expect(mockWindow.addEventListener).toHaveBeenCalledWith('beforeunload', expect.any(Function));\n  });\n\n  test('should clean up interval and event listeners on destroy', () => {\n    // Create auth state manager instance\n    const authStateManager = getAuthStateManager();\n\n    // Reset mocks to track cleanup calls\n    jest.clearAllMocks();\n\n    // Destroy the auth state manager\n    destroyAuthStateManager();\n\n    // Verify interval was cleared\n    expect(mockClearInterval).toHaveBeenCalledWith(12345);\n\n    // Verify event listeners were removed\n    expect(mockWindow.removeEventListener).toHaveBeenCalledWith('storage', expect.any(Function));\n    expect(mockWindow.removeEventListener).toHaveBeenCalledWith('focus', expect.any(Function));\n    expect(mockWindow.removeEventListener).toHaveBeenCalledWith('beforeunload', expect.any(Function));\n  });\n\n  test('should not perform operations on destroyed instance', () => {\n    // Create and destroy auth state manager\n    const authStateManager = getAuthStateManager();\n    destroyAuthStateManager();\n\n    // Reset mocks\n    jest.clearAllMocks();\n\n    // Try to subscribe to a destroyed instance\n    const unsubscribe = authStateManager.subscribe(() => {});\n\n    // Should return a no-op function\n    expect(typeof unsubscribe).toBe('function');\n    \n    // Calling unsubscribe should not throw\n    expect(() => unsubscribe()).not.toThrow();\n  });\n\n  test('should prevent double initialization', () => {\n    // Create auth state manager instance\n    const authStateManager1 = getAuthStateManager();\n    \n    // Reset mocks to track second initialization\n    jest.clearAllMocks();\n    \n    // Get the same instance again\n    const authStateManager2 = getAuthStateManager();\n\n    // Should be the same instance\n    expect(authStateManager1).toBe(authStateManager2);\n\n    // Should not set up interval again\n    expect(mockSetInterval).not.toHaveBeenCalled();\n  });\n\n  test('should add beforeunload listener for activity tracking', () => {\n    // Create auth state manager instance\n    getAuthStateManager();\n\n    // Find the beforeunload listener that was added by the auth manager\n    const beforeUnloadCalls = mockWindow.addEventListener.mock.calls.filter(\n      call => call[0] === 'beforeunload'\n    );\n\n    // Should have at least one beforeunload listener (from auth manager)\n    expect(beforeUnloadCalls.length).toBeGreaterThanOrEqual(1);\n\n    // Get the auth manager's beforeunload listener\n    const activityListener = beforeUnloadCalls[0][1];\n\n    // Should not throw when called\n    expect(() => activityListener()).not.toThrow();\n  });\n\n  test('should handle automatic cleanup via destroyAuthStateManager', () => {\n    // Create auth state manager instance\n    getAuthStateManager();\n\n    // Reset mocks to track cleanup\n    jest.clearAllMocks();\n\n    // Call destroy function directly (simulates what happens on page unload)\n    destroyAuthStateManager();\n\n    // Should have cleaned up the interval\n    expect(mockClearInterval).toHaveBeenCalledWith(12345);\n  });\n\n  test('should not crash when destroying non-existent instance', () => {\n    // Should not throw when destroying before creation\n    expect(() => destroyAuthStateManager()).not.toThrow();\n\n    // Should not call clearInterval if no instance exists\n    expect(mockClearInterval).not.toHaveBeenCalled();\n  });\n\n  test('should handle interval callback safely after destruction', () => {\n    // Create auth state manager instance\n    getAuthStateManager();\n\n    // Get the interval callback\n    const intervalCallback = mockSetInterval.mock.calls[0][0];\n\n    // Destroy the instance\n    destroyAuthStateManager();\n\n    // Calling the interval callback should not throw\n    expect(() => intervalCallback()).not.toThrow();\n  });\n});\n"], "version": 3}