cc019e7bbafef9714002521a46050200
"use strict";

/**
 * Assessment Service Integration Layer
 * Provides a clean interface for integrating algorithmic assessment with existing services
 */
/* istanbul ignore next */
function cov_1as9fh3sbl() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/assessmentServiceIntegration.ts";
  var hash = "c8e15960ab3ce31b004e38ca8758afb620ca2243";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/assessmentServiceIntegration.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 16
        },
        end: {
          line: 14,
          column: 1
        }
      },
      "1": {
        start: {
          line: 7,
          column: 28
        },
        end: {
          line: 7,
          column: 110
        }
      },
      "2": {
        start: {
          line: 7,
          column: 91
        },
        end: {
          line: 7,
          column: 106
        }
      },
      "3": {
        start: {
          line: 8,
          column: 4
        },
        end: {
          line: 13,
          column: 7
        }
      },
      "4": {
        start: {
          line: 9,
          column: 36
        },
        end: {
          line: 9,
          column: 97
        }
      },
      "5": {
        start: {
          line: 9,
          column: 42
        },
        end: {
          line: 9,
          column: 70
        }
      },
      "6": {
        start: {
          line: 9,
          column: 85
        },
        end: {
          line: 9,
          column: 95
        }
      },
      "7": {
        start: {
          line: 10,
          column: 35
        },
        end: {
          line: 10,
          column: 100
        }
      },
      "8": {
        start: {
          line: 10,
          column: 41
        },
        end: {
          line: 10,
          column: 73
        }
      },
      "9": {
        start: {
          line: 10,
          column: 88
        },
        end: {
          line: 10,
          column: 98
        }
      },
      "10": {
        start: {
          line: 11,
          column: 32
        },
        end: {
          line: 11,
          column: 116
        }
      },
      "11": {
        start: {
          line: 12,
          column: 8
        },
        end: {
          line: 12,
          column: 78
        }
      },
      "12": {
        start: {
          line: 15,
          column: 18
        },
        end: {
          line: 41,
          column: 1
        }
      },
      "13": {
        start: {
          line: 16,
          column: 12
        },
        end: {
          line: 16,
          column: 104
        }
      },
      "14": {
        start: {
          line: 16,
          column: 43
        },
        end: {
          line: 16,
          column: 68
        }
      },
      "15": {
        start: {
          line: 16,
          column: 57
        },
        end: {
          line: 16,
          column: 68
        }
      },
      "16": {
        start: {
          line: 16,
          column: 69
        },
        end: {
          line: 16,
          column: 81
        }
      },
      "17": {
        start: {
          line: 16,
          column: 119
        },
        end: {
          line: 16,
          column: 196
        }
      },
      "18": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 17,
          column: 160
        }
      },
      "19": {
        start: {
          line: 17,
          column: 141
        },
        end: {
          line: 17,
          column: 153
        }
      },
      "20": {
        start: {
          line: 18,
          column: 23
        },
        end: {
          line: 18,
          column: 68
        }
      },
      "21": {
        start: {
          line: 18,
          column: 45
        },
        end: {
          line: 18,
          column: 65
        }
      },
      "22": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "23": {
        start: {
          line: 20,
          column: 15
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "24": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 38,
          column: 66
        }
      },
      "25": {
        start: {
          line: 21,
          column: 50
        },
        end: {
          line: 38,
          column: 66
        }
      },
      "26": {
        start: {
          line: 22,
          column: 12
        },
        end: {
          line: 22,
          column: 169
        }
      },
      "27": {
        start: {
          line: 22,
          column: 160
        },
        end: {
          line: 22,
          column: 169
        }
      },
      "28": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 52
        }
      },
      "29": {
        start: {
          line: 23,
          column: 26
        },
        end: {
          line: 23,
          column: 52
        }
      },
      "30": {
        start: {
          line: 24,
          column: 12
        },
        end: {
          line: 36,
          column: 13
        }
      },
      "31": {
        start: {
          line: 25,
          column: 32
        },
        end: {
          line: 25,
          column: 39
        }
      },
      "32": {
        start: {
          line: 25,
          column: 40
        },
        end: {
          line: 25,
          column: 46
        }
      },
      "33": {
        start: {
          line: 26,
          column: 24
        },
        end: {
          line: 26,
          column: 34
        }
      },
      "34": {
        start: {
          line: 26,
          column: 35
        },
        end: {
          line: 26,
          column: 72
        }
      },
      "35": {
        start: {
          line: 27,
          column: 24
        },
        end: {
          line: 27,
          column: 34
        }
      },
      "36": {
        start: {
          line: 27,
          column: 35
        },
        end: {
          line: 27,
          column: 45
        }
      },
      "37": {
        start: {
          line: 27,
          column: 46
        },
        end: {
          line: 27,
          column: 55
        }
      },
      "38": {
        start: {
          line: 27,
          column: 56
        },
        end: {
          line: 27,
          column: 65
        }
      },
      "39": {
        start: {
          line: 28,
          column: 24
        },
        end: {
          line: 28,
          column: 41
        }
      },
      "40": {
        start: {
          line: 28,
          column: 42
        },
        end: {
          line: 28,
          column: 55
        }
      },
      "41": {
        start: {
          line: 28,
          column: 56
        },
        end: {
          line: 28,
          column: 65
        }
      },
      "42": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 128
        }
      },
      "43": {
        start: {
          line: 30,
          column: 110
        },
        end: {
          line: 30,
          column: 116
        }
      },
      "44": {
        start: {
          line: 30,
          column: 117
        },
        end: {
          line: 30,
          column: 126
        }
      },
      "45": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 106
        }
      },
      "46": {
        start: {
          line: 31,
          column: 81
        },
        end: {
          line: 31,
          column: 97
        }
      },
      "47": {
        start: {
          line: 31,
          column: 98
        },
        end: {
          line: 31,
          column: 104
        }
      },
      "48": {
        start: {
          line: 32,
          column: 20
        },
        end: {
          line: 32,
          column: 89
        }
      },
      "49": {
        start: {
          line: 32,
          column: 57
        },
        end: {
          line: 32,
          column: 72
        }
      },
      "50": {
        start: {
          line: 32,
          column: 73
        },
        end: {
          line: 32,
          column: 80
        }
      },
      "51": {
        start: {
          line: 32,
          column: 81
        },
        end: {
          line: 32,
          column: 87
        }
      },
      "52": {
        start: {
          line: 33,
          column: 20
        },
        end: {
          line: 33,
          column: 87
        }
      },
      "53": {
        start: {
          line: 33,
          column: 47
        },
        end: {
          line: 33,
          column: 62
        }
      },
      "54": {
        start: {
          line: 33,
          column: 63
        },
        end: {
          line: 33,
          column: 78
        }
      },
      "55": {
        start: {
          line: 33,
          column: 79
        },
        end: {
          line: 33,
          column: 85
        }
      },
      "56": {
        start: {
          line: 34,
          column: 20
        },
        end: {
          line: 34,
          column: 42
        }
      },
      "57": {
        start: {
          line: 34,
          column: 30
        },
        end: {
          line: 34,
          column: 42
        }
      },
      "58": {
        start: {
          line: 35,
          column: 20
        },
        end: {
          line: 35,
          column: 33
        }
      },
      "59": {
        start: {
          line: 35,
          column: 34
        },
        end: {
          line: 35,
          column: 43
        }
      },
      "60": {
        start: {
          line: 37,
          column: 12
        },
        end: {
          line: 37,
          column: 39
        }
      },
      "61": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 38,
          column: 34
        }
      },
      "62": {
        start: {
          line: 38,
          column: 35
        },
        end: {
          line: 38,
          column: 41
        }
      },
      "63": {
        start: {
          line: 38,
          column: 54
        },
        end: {
          line: 38,
          column: 64
        }
      },
      "64": {
        start: {
          line: 39,
          column: 8
        },
        end: {
          line: 39,
          column: 35
        }
      },
      "65": {
        start: {
          line: 39,
          column: 23
        },
        end: {
          line: 39,
          column: 35
        }
      },
      "66": {
        start: {
          line: 39,
          column: 36
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "67": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 62
        }
      },
      "68": {
        start: {
          line: 43,
          column: 0
        },
        end: {
          line: 43,
          column: 46
        }
      },
      "69": {
        start: {
          line: 44,
          column: 37
        },
        end: {
          line: 44,
          column: 78
        }
      },
      "70": {
        start: {
          line: 45,
          column: 32
        },
        end: {
          line: 45,
          column: 68
        }
      },
      "71": {
        start: {
          line: 46,
          column: 50
        },
        end: {
          line: 379,
          column: 3
        }
      },
      "72": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 77,
          column: 6
        }
      },
      "73": {
        start: {
          line: 53,
          column: 8
        },
        end: {
          line: 76,
          column: 11
        }
      },
      "74": {
        start: {
          line: 55,
          column: 12
        },
        end: {
          line: 75,
          column: 15
        }
      },
      "75": {
        start: {
          line: 56,
          column: 16
        },
        end: {
          line: 74,
          column: 17
        }
      },
      "76": {
        start: {
          line: 58,
          column: 24
        },
        end: {
          line: 59,
          column: 50
        }
      },
      "77": {
        start: {
          line: 59,
          column: 28
        },
        end: {
          line: 59,
          column: 50
        }
      },
      "78": {
        start: {
          line: 60,
          column: 24
        },
        end: {
          line: 60,
          column: 37
        }
      },
      "79": {
        start: {
          line: 62,
          column: 24
        },
        end: {
          line: 62,
          column: 50
        }
      },
      "80": {
        start: {
          line: 63,
          column: 24
        },
        end: {
          line: 63,
          column: 119
        }
      },
      "81": {
        start: {
          line: 65,
          column: 24
        },
        end: {
          line: 65,
          column: 34
        }
      },
      "82": {
        start: {
          line: 66,
          column: 24
        },
        end: {
          line: 66,
          column: 48
        }
      },
      "83": {
        start: {
          line: 67,
          column: 24
        },
        end: {
          line: 67,
          column: 84
        }
      },
      "84": {
        start: {
          line: 68,
          column: 24
        },
        end: {
          line: 68,
          column: 48
        }
      },
      "85": {
        start: {
          line: 70,
          column: 24
        },
        end: {
          line: 70,
          column: 44
        }
      },
      "86": {
        start: {
          line: 71,
          column: 24
        },
        end: {
          line: 71,
          column: 91
        }
      },
      "87": {
        start: {
          line: 72,
          column: 24
        },
        end: {
          line: 72,
          column: 48
        }
      },
      "88": {
        start: {
          line: 73,
          column: 28
        },
        end: {
          line: 73,
          column: 50
        }
      },
      "89": {
        start: {
          line: 81,
          column: 4
        },
        end: {
          line: 146,
          column: 6
        }
      },
      "90": {
        start: {
          line: 82,
          column: 8
        },
        end: {
          line: 145,
          column: 11
        }
      },
      "91": {
        start: {
          line: 84,
          column: 12
        },
        end: {
          line: 144,
          column: 15
        }
      },
      "92": {
        start: {
          line: 85,
          column: 16
        },
        end: {
          line: 143,
          column: 17
        }
      },
      "93": {
        start: {
          line: 87,
          column: 24
        },
        end: {
          line: 87,
          column: 47
        }
      },
      "94": {
        start: {
          line: 89,
          column: 24
        },
        end: {
          line: 89,
          column: 64
        }
      },
      "95": {
        start: {
          line: 92,
          column: 24
        },
        end: {
          line: 92,
          column: 34
        }
      },
      "96": {
        start: {
          line: 93,
          column: 24
        },
        end: {
          line: 93,
          column: 37
        }
      },
      "97": {
        start: {
          line: 95,
          column: 24
        },
        end: {
          line: 95,
          column: 50
        }
      },
      "98": {
        start: {
          line: 96,
          column: 24
        },
        end: {
          line: 96,
          column: 157
        }
      },
      "99": {
        start: {
          line: 98,
          column: 24
        },
        end: {
          line: 98,
          column: 55
        }
      },
      "100": {
        start: {
          line: 99,
          column: 24
        },
        end: {
          line: 101,
          column: 85
        }
      },
      "101": {
        start: {
          line: 101,
          column: 52
        },
        end: {
          line: 101,
          column: 81
        }
      },
      "102": {
        start: {
          line: 102,
          column: 24
        },
        end: {
          line: 102,
          column: 77
        }
      },
      "103": {
        start: {
          line: 103,
          column: 24
        },
        end: {
          line: 109,
          column: 31
        }
      },
      "104": {
        start: {
          line: 111,
          column: 24
        },
        end: {
          line: 111,
          column: 55
        }
      },
      "105": {
        start: {
          line: 112,
          column: 24
        },
        end: {
          line: 112,
          column: 114
        }
      },
      "106": {
        start: {
          line: 113,
          column: 24
        },
        end: {
          line: 113,
          column: 37
        }
      },
      "107": {
        start: {
          line: 115,
          column: 24
        },
        end: {
          line: 115,
          column: 50
        }
      },
      "108": {
        start: {
          line: 116,
          column: 24
        },
        end: {
          line: 116,
          column: 177
        }
      },
      "109": {
        start: {
          line: 118,
          column: 24
        },
        end: {
          line: 118,
          column: 53
        }
      },
      "110": {
        start: {
          line: 119,
          column: 24
        },
        end: {
          line: 121,
          column: 76
        }
      },
      "111": {
        start: {
          line: 121,
          column: 50
        },
        end: {
          line: 121,
          column: 72
        }
      },
      "112": {
        start: {
          line: 122,
          column: 24
        },
        end: {
          line: 122,
          column: 77
        }
      },
      "113": {
        start: {
          line: 123,
          column: 24
        },
        end: {
          line: 129,
          column: 31
        }
      },
      "114": {
        start: {
          line: 131,
          column: 24
        },
        end: {
          line: 131,
          column: 52
        }
      },
      "115": {
        start: {
          line: 132,
          column: 24
        },
        end: {
          line: 132,
          column: 105
        }
      },
      "116": {
        start: {
          line: 133,
          column: 24
        },
        end: {
          line: 133,
          column: 115
        }
      },
      "117": {
        start: {
          line: 134,
          column: 24
        },
        end: {
          line: 134,
          column: 77
        }
      },
      "118": {
        start: {
          line: 135,
          column: 24
        },
        end: {
          line: 140,
          column: 31
        }
      },
      "119": {
        start: {
          line: 141,
          column: 28
        },
        end: {
          line: 141,
          column: 52
        }
      },
      "120": {
        start: {
          line: 142,
          column: 28
        },
        end: {
          line: 142,
          column: 50
        }
      },
      "121": {
        start: {
          line: 150,
          column: 4
        },
        end: {
          line: 167,
          column: 6
        }
      },
      "122": {
        start: {
          line: 151,
          column: 8
        },
        end: {
          line: 166,
          column: 11
        }
      },
      "123": {
        start: {
          line: 153,
          column: 12
        },
        end: {
          line: 165,
          column: 15
        }
      },
      "124": {
        start: {
          line: 154,
          column: 16
        },
        end: {
          line: 164,
          column: 17
        }
      },
      "125": {
        start: {
          line: 156,
          column: 24
        },
        end: {
          line: 156,
          column: 50
        }
      },
      "126": {
        start: {
          line: 157,
          column: 24
        },
        end: {
          line: 157,
          column: 171
        }
      },
      "127": {
        start: {
          line: 158,
          column: 28
        },
        end: {
          line: 158,
          column: 61
        }
      },
      "128": {
        start: {
          line: 160,
          column: 24
        },
        end: {
          line: 160,
          column: 44
        }
      },
      "129": {
        start: {
          line: 161,
          column: 24
        },
        end: {
          line: 161,
          column: 87
        }
      },
      "130": {
        start: {
          line: 162,
          column: 24
        },
        end: {
          line: 162,
          column: 52
        }
      },
      "131": {
        start: {
          line: 163,
          column: 28
        },
        end: {
          line: 163,
          column: 50
        }
      },
      "132": {
        start: {
          line: 171,
          column: 4
        },
        end: {
          line: 176,
          column: 6
        }
      },
      "133": {
        start: {
          line: 172,
          column: 8
        },
        end: {
          line: 173,
          column: 22
        }
      },
      "134": {
        start: {
          line: 173,
          column: 12
        },
        end: {
          line: 173,
          column: 22
        }
      },
      "135": {
        start: {
          line: 174,
          column: 30
        },
        end: {
          line: 174,
          column: 110
        }
      },
      "136": {
        start: {
          line: 174,
          column: 69
        },
        end: {
          line: 174,
          column: 104
        }
      },
      "137": {
        start: {
          line: 175,
          column: 8
        },
        end: {
          line: 175,
          column: 60
        }
      },
      "138": {
        start: {
          line: 180,
          column: 4
        },
        end: {
          line: 236,
          column: 6
        }
      },
      "139": {
        start: {
          line: 181,
          column: 26
        },
        end: {
          line: 181,
          column: 28
        }
      },
      "140": {
        start: {
          line: 182,
          column: 40
        },
        end: {
          line: 182,
          column: 96
        }
      },
      "141": {
        start: {
          line: 183,
          column: 27
        },
        end: {
          line: 183,
          column: 70
        }
      },
      "142": {
        start: {
          line: 185,
          column: 8
        },
        end: {
          line: 193,
          column: 9
        }
      },
      "143": {
        start: {
          line: 186,
          column: 12
        },
        end: {
          line: 186,
          column: 81
        }
      },
      "144": {
        start: {
          line: 187,
          column: 12
        },
        end: {
          line: 189,
          column: 13
        }
      },
      "145": {
        start: {
          line: 188,
          column: 16
        },
        end: {
          line: 188,
          column: 69
        }
      },
      "146": {
        start: {
          line: 190,
          column: 12
        },
        end: {
          line: 192,
          column: 13
        }
      },
      "147": {
        start: {
          line: 191,
          column: 16
        },
        end: {
          line: 191,
          column: 61
        }
      },
      "148": {
        start: {
          line: 195,
          column: 8
        },
        end: {
          line: 200,
          column: 9
        }
      },
      "149": {
        start: {
          line: 196,
          column: 12
        },
        end: {
          line: 196,
          column: 66
        }
      },
      "150": {
        start: {
          line: 197,
          column: 12
        },
        end: {
          line: 199,
          column: 13
        }
      },
      "151": {
        start: {
          line: 198,
          column: 16
        },
        end: {
          line: 198,
          column: 65
        }
      },
      "152": {
        start: {
          line: 202,
          column: 8
        },
        end: {
          line: 207,
          column: 9
        }
      },
      "153": {
        start: {
          line: 203,
          column: 12
        },
        end: {
          line: 203,
          column: 80
        }
      },
      "154": {
        start: {
          line: 204,
          column: 12
        },
        end: {
          line: 206,
          column: 13
        }
      },
      "155": {
        start: {
          line: 205,
          column: 16
        },
        end: {
          line: 205,
          column: 67
        }
      },
      "156": {
        start: {
          line: 209,
          column: 8
        },
        end: {
          line: 211,
          column: 9
        }
      },
      "157": {
        start: {
          line: 210,
          column: 12
        },
        end: {
          line: 210,
          column: 90
        }
      },
      "158": {
        start: {
          line: 213,
          column: 8
        },
        end: {
          line: 218,
          column: 9
        }
      },
      "159": {
        start: {
          line: 214,
          column: 12
        },
        end: {
          line: 214,
          column: 67
        }
      },
      "160": {
        start: {
          line: 215,
          column: 12
        },
        end: {
          line: 217,
          column: 13
        }
      },
      "161": {
        start: {
          line: 216,
          column: 16
        },
        end: {
          line: 216,
          column: 64
        }
      },
      "162": {
        start: {
          line: 220,
          column: 8
        },
        end: {
          line: 222,
          column: 9
        }
      },
      "163": {
        start: {
          line: 221,
          column: 12
        },
        end: {
          line: 221,
          column: 83
        }
      },
      "164": {
        start: {
          line: 224,
          column: 32
        },
        end: {
          line: 224,
          column: 64
        }
      },
      "165": {
        start: {
          line: 226,
          column: 8
        },
        end: {
          line: 234,
          column: 9
        }
      },
      "166": {
        start: {
          line: 227,
          column: 12
        },
        end: {
          line: 233,
          column: 14
        }
      },
      "167": {
        start: {
          line: 235,
          column: 8
        },
        end: {
          line: 235,
          column: 45
        }
      },
      "168": {
        start: {
          line: 240,
          column: 4
        },
        end: {
          line: 246,
          column: 6
        }
      },
      "169": {
        start: {
          line: 241,
          column: 8
        },
        end: {
          line: 242,
          column: 80
        }
      },
      "170": {
        start: {
          line: 242,
          column: 12
        },
        end: {
          line: 242,
          column: 80
        }
      },
      "171": {
        start: {
          line: 242,
          column: 47
        },
        end: {
          line: 242,
          column: 76
        }
      },
      "172": {
        start: {
          line: 243,
          column: 8
        },
        end: {
          line: 244,
          column: 27
        }
      },
      "173": {
        start: {
          line: 244,
          column: 12
        },
        end: {
          line: 244,
          column: 27
        }
      },
      "174": {
        start: {
          line: 245,
          column: 8
        },
        end: {
          line: 245,
          column: 18
        }
      },
      "175": {
        start: {
          line: 250,
          column: 4
        },
        end: {
          line: 376,
          column: 6
        }
      },
      "176": {
        start: {
          line: 251,
          column: 8
        },
        end: {
          line: 375,
          column: 11
        }
      },
      "177": {
        start: {
          line: 253,
          column: 12
        },
        end: {
          line: 374,
          column: 15
        }
      },
      "178": {
        start: {
          line: 254,
          column: 16
        },
        end: {
          line: 373,
          column: 17
        }
      },
      "179": {
        start: {
          line: 256,
          column: 24
        },
        end: {
          line: 256,
          column: 51
        }
      },
      "180": {
        start: {
          line: 257,
          column: 24
        },
        end: {
          line: 257,
          column: 48
        }
      },
      "181": {
        start: {
          line: 258,
          column: 24
        },
        end: {
          line: 258,
          column: 44
        }
      },
      "182": {
        start: {
          line: 259,
          column: 24
        },
        end: {
          line: 259,
          column: 37
        }
      },
      "183": {
        start: {
          line: 261,
          column: 24
        },
        end: {
          line: 261,
          column: 50
        }
      },
      "184": {
        start: {
          line: 262,
          column: 24
        },
        end: {
          line: 262,
          column: 64
        }
      },
      "185": {
        start: {
          line: 264,
          column: 24
        },
        end: {
          line: 264,
          column: 34
        }
      },
      "186": {
        start: {
          line: 265,
          column: 24
        },
        end: {
          line: 281,
          column: 26
        }
      },
      "187": {
        start: {
          line: 282,
          column: 24
        },
        end: {
          line: 301,
          column: 26
        }
      },
      "188": {
        start: {
          line: 302,
          column: 24
        },
        end: {
          line: 302,
          column: 164
        }
      },
      "189": {
        start: {
          line: 304,
          column: 24
        },
        end: {
          line: 304,
          column: 54
        }
      },
      "190": {
        start: {
          line: 305,
          column: 24
        },
        end: {
          line: 305,
          column: 74
        }
      },
      "191": {
        start: {
          line: 306,
          column: 24
        },
        end: {
          line: 306,
          column: 48
        }
      },
      "192": {
        start: {
          line: 308,
          column: 24
        },
        end: {
          line: 308,
          column: 44
        }
      },
      "193": {
        start: {
          line: 309,
          column: 24
        },
        end: {
          line: 309,
          column: 90
        }
      },
      "194": {
        start: {
          line: 310,
          column: 24
        },
        end: {
          line: 310,
          column: 48
        }
      },
      "195": {
        start: {
          line: 312,
          column: 24
        },
        end: {
          line: 312,
          column: 50
        }
      },
      "196": {
        start: {
          line: 313,
          column: 24
        },
        end: {
          line: 348,
          column: 48
        }
      },
      "197": {
        start: {
          line: 350,
          column: 24
        },
        end: {
          line: 350,
          column: 51
        }
      },
      "198": {
        start: {
          line: 351,
          column: 24
        },
        end: {
          line: 351,
          column: 90
        }
      },
      "199": {
        start: {
          line: 352,
          column: 24
        },
        end: {
          line: 352,
          column: 48
        }
      },
      "200": {
        start: {
          line: 354,
          column: 24
        },
        end: {
          line: 354,
          column: 44
        }
      },
      "201": {
        start: {
          line: 355,
          column: 24
        },
        end: {
          line: 355,
          column: 87
        }
      },
      "202": {
        start: {
          line: 356,
          column: 24
        },
        end: {
          line: 356,
          column: 48
        }
      },
      "203": {
        start: {
          line: 358,
          column: 24
        },
        end: {
          line: 366,
          column: 25
        }
      },
      "204": {
        start: {
          line: 359,
          column: 28
        },
        end: {
          line: 359,
          column: 48
        }
      },
      "205": {
        start: {
          line: 361,
          column: 29
        },
        end: {
          line: 366,
          column: 25
        }
      },
      "206": {
        start: {
          line: 362,
          column: 28
        },
        end: {
          line: 362,
          column: 49
        }
      },
      "207": {
        start: {
          line: 365,
          column: 28
        },
        end: {
          line: 365,
          column: 49
        }
      },
      "208": {
        start: {
          line: 367,
          column: 24
        },
        end: {
          line: 372,
          column: 31
        }
      },
      "209": {
        start: {
          line: 377,
          column: 4
        },
        end: {
          line: 377,
          column: 53
        }
      },
      "210": {
        start: {
          line: 378,
          column: 4
        },
        end: {
          line: 378,
          column: 40
        }
      },
      "211": {
        start: {
          line: 380,
          column: 0
        },
        end: {
          line: 380,
          column: 68
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 44
          },
          end: {
            line: 6,
            column: 45
          }
        },
        loc: {
          start: {
            line: 6,
            column: 89
          },
          end: {
            line: 14,
            column: 1
          }
        },
        line: 6
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 7,
            column: 13
          },
          end: {
            line: 7,
            column: 18
          }
        },
        loc: {
          start: {
            line: 7,
            column: 26
          },
          end: {
            line: 7,
            column: 112
          }
        },
        line: 7
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 71
          }
        },
        loc: {
          start: {
            line: 7,
            column: 89
          },
          end: {
            line: 7,
            column: 108
          }
        },
        line: 7
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 8,
            column: 36
          },
          end: {
            line: 8,
            column: 37
          }
        },
        loc: {
          start: {
            line: 8,
            column: 63
          },
          end: {
            line: 13,
            column: 5
          }
        },
        line: 8
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 9,
            column: 17
          },
          end: {
            line: 9,
            column: 26
          }
        },
        loc: {
          start: {
            line: 9,
            column: 34
          },
          end: {
            line: 9,
            column: 99
          }
        },
        line: 9
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 10,
            column: 17
          },
          end: {
            line: 10,
            column: 25
          }
        },
        loc: {
          start: {
            line: 10,
            column: 33
          },
          end: {
            line: 10,
            column: 102
          }
        },
        line: 10
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 11,
            column: 17
          },
          end: {
            line: 11,
            column: 21
          }
        },
        loc: {
          start: {
            line: 11,
            column: 30
          },
          end: {
            line: 11,
            column: 118
          }
        },
        line: 11
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 15,
            column: 48
          },
          end: {
            line: 15,
            column: 49
          }
        },
        loc: {
          start: {
            line: 15,
            column: 73
          },
          end: {
            line: 41,
            column: 1
          }
        },
        line: 15
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 16,
            column: 30
          },
          end: {
            line: 16,
            column: 31
          }
        },
        loc: {
          start: {
            line: 16,
            column: 41
          },
          end: {
            line: 16,
            column: 83
          }
        },
        line: 16
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 17,
            column: 128
          },
          end: {
            line: 17,
            column: 129
          }
        },
        loc: {
          start: {
            line: 17,
            column: 139
          },
          end: {
            line: 17,
            column: 155
          }
        },
        line: 17
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 18,
            column: 13
          },
          end: {
            line: 18,
            column: 17
          }
        },
        loc: {
          start: {
            line: 18,
            column: 21
          },
          end: {
            line: 18,
            column: 70
          }
        },
        line: 18
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 31
          }
        },
        loc: {
          start: {
            line: 18,
            column: 43
          },
          end: {
            line: 18,
            column: 67
          }
        },
        line: 18
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 19,
            column: 13
          },
          end: {
            line: 19,
            column: 17
          }
        },
        loc: {
          start: {
            line: 19,
            column: 22
          },
          end: {
            line: 40,
            column: 5
          }
        },
        line: 19
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 46,
            column: 50
          },
          end: {
            line: 46,
            column: 51
          }
        },
        loc: {
          start: {
            line: 46,
            column: 62
          },
          end: {
            line: 379,
            column: 1
          }
        },
        line: 46
      },
      "14": {
        name: "AssessmentServiceIntegration",
        decl: {
          start: {
            line: 47,
            column: 13
          },
          end: {
            line: 47,
            column: 41
          }
        },
        loc: {
          start: {
            line: 47,
            column: 44
          },
          end: {
            line: 48,
            column: 5
          }
        },
        line: 47
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 52,
            column: 46
          },
          end: {
            line: 52,
            column: 47
          }
        },
        loc: {
          start: {
            line: 52,
            column: 58
          },
          end: {
            line: 77,
            column: 5
          }
        },
        line: 52
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 53,
            column: 48
          },
          end: {
            line: 53,
            column: 49
          }
        },
        loc: {
          start: {
            line: 53,
            column: 60
          },
          end: {
            line: 76,
            column: 9
          }
        },
        line: 53
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 55,
            column: 37
          },
          end: {
            line: 55,
            column: 38
          }
        },
        loc: {
          start: {
            line: 55,
            column: 51
          },
          end: {
            line: 75,
            column: 13
          }
        },
        line: 55
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 81,
            column: 65
          },
          end: {
            line: 81,
            column: 66
          }
        },
        loc: {
          start: {
            line: 81,
            column: 96
          },
          end: {
            line: 146,
            column: 5
          }
        },
        line: 81
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 82,
            column: 48
          },
          end: {
            line: 82,
            column: 49
          }
        },
        loc: {
          start: {
            line: 82,
            column: 60
          },
          end: {
            line: 145,
            column: 9
          }
        },
        line: 82
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 84,
            column: 37
          },
          end: {
            line: 84,
            column: 38
          }
        },
        loc: {
          start: {
            line: 84,
            column: 51
          },
          end: {
            line: 144,
            column: 13
          }
        },
        line: 84
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 101,
            column: 33
          },
          end: {
            line: 101,
            column: 34
          }
        },
        loc: {
          start: {
            line: 101,
            column: 50
          },
          end: {
            line: 101,
            column: 83
          }
        },
        line: 101
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 121,
            column: 33
          },
          end: {
            line: 121,
            column: 34
          }
        },
        loc: {
          start: {
            line: 121,
            column: 48
          },
          end: {
            line: 121,
            column: 74
          }
        },
        line: 121
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 150,
            column: 55
          },
          end: {
            line: 150,
            column: 56
          }
        },
        loc: {
          start: {
            line: 150,
            column: 86
          },
          end: {
            line: 167,
            column: 5
          }
        },
        line: 150
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 151,
            column: 48
          },
          end: {
            line: 151,
            column: 49
          }
        },
        loc: {
          start: {
            line: 151,
            column: 60
          },
          end: {
            line: 166,
            column: 9
          }
        },
        line: 151
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 153,
            column: 37
          },
          end: {
            line: 153,
            column: 38
          }
        },
        loc: {
          start: {
            line: 153,
            column: 51
          },
          end: {
            line: 165,
            column: 13
          }
        },
        line: 153
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 171,
            column: 62
          },
          end: {
            line: 171,
            column: 63
          }
        },
        loc: {
          start: {
            line: 171,
            column: 81
          },
          end: {
            line: 176,
            column: 5
          }
        },
        line: 171
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 174,
            column: 45
          },
          end: {
            line: 174,
            column: 46
          }
        },
        loc: {
          start: {
            line: 174,
            column: 67
          },
          end: {
            line: 174,
            column: 106
          }
        },
        line: 174
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 180,
            column: 66
          },
          end: {
            line: 180,
            column: 67
          }
        },
        loc: {
          start: {
            line: 180,
            column: 98
          },
          end: {
            line: 236,
            column: 5
          }
        },
        line: 180
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 240,
            column: 49
          },
          end: {
            line: 240,
            column: 50
          }
        },
        loc: {
          start: {
            line: 240,
            column: 66
          },
          end: {
            line: 246,
            column: 5
          }
        },
        line: 240
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 242,
            column: 32
          },
          end: {
            line: 242,
            column: 33
          }
        },
        loc: {
          start: {
            line: 242,
            column: 45
          },
          end: {
            line: 242,
            column: 78
          }
        },
        line: 242
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 250,
            column: 47
          },
          end: {
            line: 250,
            column: 48
          }
        },
        loc: {
          start: {
            line: 250,
            column: 59
          },
          end: {
            line: 376,
            column: 5
          }
        },
        line: 250
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 251,
            column: 48
          },
          end: {
            line: 251,
            column: 49
          }
        },
        loc: {
          start: {
            line: 251,
            column: 60
          },
          end: {
            line: 375,
            column: 9
          }
        },
        line: 251
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 253,
            column: 37
          },
          end: {
            line: 253,
            column: 38
          }
        },
        loc: {
          start: {
            line: 253,
            column: 51
          },
          end: {
            line: 374,
            column: 13
          }
        },
        line: 253
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 16
          },
          end: {
            line: 14,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 21
          }
        }, {
          start: {
            line: 6,
            column: 25
          },
          end: {
            line: 6,
            column: 39
          }
        }, {
          start: {
            line: 6,
            column: 44
          },
          end: {
            line: 14,
            column: 1
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 7,
            column: 35
          },
          end: {
            line: 7,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 56
          },
          end: {
            line: 7,
            column: 61
          }
        }, {
          start: {
            line: 7,
            column: 64
          },
          end: {
            line: 7,
            column: 109
          }
        }],
        line: 7
      },
      "2": {
        loc: {
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 16
          },
          end: {
            line: 8,
            column: 17
          }
        }, {
          start: {
            line: 8,
            column: 22
          },
          end: {
            line: 8,
            column: 33
          }
        }],
        line: 8
      },
      "3": {
        loc: {
          start: {
            line: 11,
            column: 32
          },
          end: {
            line: 11,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 11,
            column: 46
          },
          end: {
            line: 11,
            column: 67
          }
        }, {
          start: {
            line: 11,
            column: 70
          },
          end: {
            line: 11,
            column: 115
          }
        }],
        line: 11
      },
      "4": {
        loc: {
          start: {
            line: 12,
            column: 51
          },
          end: {
            line: 12,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 12,
            column: 51
          },
          end: {
            line: 12,
            column: 61
          }
        }, {
          start: {
            line: 12,
            column: 65
          },
          end: {
            line: 12,
            column: 67
          }
        }],
        line: 12
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 18
          },
          end: {
            line: 41,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 19
          },
          end: {
            line: 15,
            column: 23
          }
        }, {
          start: {
            line: 15,
            column: 27
          },
          end: {
            line: 15,
            column: 43
          }
        }, {
          start: {
            line: 15,
            column: 48
          },
          end: {
            line: 41,
            column: 1
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 16,
            column: 43
          },
          end: {
            line: 16,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 43
          },
          end: {
            line: 16,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "7": {
        loc: {
          start: {
            line: 16,
            column: 134
          },
          end: {
            line: 16,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 16,
            column: 167
          },
          end: {
            line: 16,
            column: 175
          }
        }, {
          start: {
            line: 16,
            column: 178
          },
          end: {
            line: 16,
            column: 184
          }
        }],
        line: 16
      },
      "8": {
        loc: {
          start: {
            line: 17,
            column: 74
          },
          end: {
            line: 17,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 74
          },
          end: {
            line: 17,
            column: 102
          }
        }, {
          start: {
            line: 17,
            column: 107
          },
          end: {
            line: 17,
            column: 155
          }
        }],
        line: 17
      },
      "9": {
        loc: {
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 20,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 20
      },
      "10": {
        loc: {
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 21,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 15
          },
          end: {
            line: 21,
            column: 16
          }
        }, {
          start: {
            line: 21,
            column: 21
          },
          end: {
            line: 21,
            column: 44
          }
        }],
        line: 21
      },
      "11": {
        loc: {
          start: {
            line: 21,
            column: 28
          },
          end: {
            line: 21,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 21,
            column: 28
          },
          end: {
            line: 21,
            column: 33
          }
        }, {
          start: {
            line: 21,
            column: 38
          },
          end: {
            line: 21,
            column: 43
          }
        }],
        line: 21
      },
      "12": {
        loc: {
          start: {
            line: 22,
            column: 12
          },
          end: {
            line: 22,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 12
          },
          end: {
            line: 22,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "13": {
        loc: {
          start: {
            line: 22,
            column: 23
          },
          end: {
            line: 22,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 23
          },
          end: {
            line: 22,
            column: 24
          }
        }, {
          start: {
            line: 22,
            column: 29
          },
          end: {
            line: 22,
            column: 125
          }
        }, {
          start: {
            line: 22,
            column: 130
          },
          end: {
            line: 22,
            column: 158
          }
        }],
        line: 22
      },
      "14": {
        loc: {
          start: {
            line: 22,
            column: 33
          },
          end: {
            line: 22,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 45
          },
          end: {
            line: 22,
            column: 56
          }
        }, {
          start: {
            line: 22,
            column: 59
          },
          end: {
            line: 22,
            column: 125
          }
        }],
        line: 22
      },
      "15": {
        loc: {
          start: {
            line: 22,
            column: 59
          },
          end: {
            line: 22,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 116
          }
        }, {
          start: {
            line: 22,
            column: 119
          },
          end: {
            line: 22,
            column: 125
          }
        }],
        line: 22
      },
      "16": {
        loc: {
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 67
          },
          end: {
            line: 22,
            column: 77
          }
        }, {
          start: {
            line: 22,
            column: 82
          },
          end: {
            line: 22,
            column: 115
          }
        }],
        line: 22
      },
      "17": {
        loc: {
          start: {
            line: 22,
            column: 82
          },
          end: {
            line: 22,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 83
          },
          end: {
            line: 22,
            column: 98
          }
        }, {
          start: {
            line: 22,
            column: 103
          },
          end: {
            line: 22,
            column: 112
          }
        }],
        line: 22
      },
      "18": {
        loc: {
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 23,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 12
          },
          end: {
            line: 23,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "19": {
        loc: {
          start: {
            line: 24,
            column: 12
          },
          end: {
            line: 36,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 25,
            column: 23
          }
        }, {
          start: {
            line: 25,
            column: 24
          },
          end: {
            line: 25,
            column: 46
          }
        }, {
          start: {
            line: 26,
            column: 16
          },
          end: {
            line: 26,
            column: 72
          }
        }, {
          start: {
            line: 27,
            column: 16
          },
          end: {
            line: 27,
            column: 65
          }
        }, {
          start: {
            line: 28,
            column: 16
          },
          end: {
            line: 28,
            column: 65
          }
        }, {
          start: {
            line: 29,
            column: 16
          },
          end: {
            line: 35,
            column: 43
          }
        }],
        line: 24
      },
      "20": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 74
          }
        }, {
          start: {
            line: 30,
            column: 79
          },
          end: {
            line: 30,
            column: 90
          }
        }, {
          start: {
            line: 30,
            column: 94
          },
          end: {
            line: 30,
            column: 105
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 30,
            column: 42
          },
          end: {
            line: 30,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 42
          },
          end: {
            line: 30,
            column: 54
          }
        }, {
          start: {
            line: 30,
            column: 58
          },
          end: {
            line: 30,
            column: 73
          }
        }],
        line: 30
      },
      "23": {
        loc: {
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "24": {
        loc: {
          start: {
            line: 31,
            column: 24
          },
          end: {
            line: 31,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 31,
            column: 24
          },
          end: {
            line: 31,
            column: 35
          }
        }, {
          start: {
            line: 31,
            column: 40
          },
          end: {
            line: 31,
            column: 42
          }
        }, {
          start: {
            line: 31,
            column: 47
          },
          end: {
            line: 31,
            column: 59
          }
        }, {
          start: {
            line: 31,
            column: 63
          },
          end: {
            line: 31,
            column: 75
          }
        }],
        line: 31
      },
      "25": {
        loc: {
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 20
          },
          end: {
            line: 32,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      },
      "26": {
        loc: {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 35
          }
        }, {
          start: {
            line: 32,
            column: 39
          },
          end: {
            line: 32,
            column: 53
          }
        }],
        line: 32
      },
      "27": {
        loc: {
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 20
          },
          end: {
            line: 33,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "28": {
        loc: {
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 24
          },
          end: {
            line: 33,
            column: 25
          }
        }, {
          start: {
            line: 33,
            column: 29
          },
          end: {
            line: 33,
            column: 43
          }
        }],
        line: 33
      },
      "29": {
        loc: {
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 20
          },
          end: {
            line: 34,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "30": {
        loc: {
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 8
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "31": {
        loc: {
          start: {
            line: 39,
            column: 52
          },
          end: {
            line: 39,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 60
          },
          end: {
            line: 39,
            column: 65
          }
        }, {
          start: {
            line: 39,
            column: 68
          },
          end: {
            line: 39,
            column: 74
          }
        }],
        line: 39
      },
      "32": {
        loc: {
          start: {
            line: 56,
            column: 16
          },
          end: {
            line: 74,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 57,
            column: 20
          },
          end: {
            line: 60,
            column: 37
          }
        }, {
          start: {
            line: 61,
            column: 20
          },
          end: {
            line: 63,
            column: 119
          }
        }, {
          start: {
            line: 64,
            column: 20
          },
          end: {
            line: 68,
            column: 48
          }
        }, {
          start: {
            line: 69,
            column: 20
          },
          end: {
            line: 72,
            column: 48
          }
        }, {
          start: {
            line: 73,
            column: 20
          },
          end: {
            line: 73,
            column: 50
          }
        }],
        line: 56
      },
      "33": {
        loc: {
          start: {
            line: 58,
            column: 24
          },
          end: {
            line: 59,
            column: 50
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 58,
            column: 24
          },
          end: {
            line: 59,
            column: 50
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 58
      },
      "34": {
        loc: {
          start: {
            line: 85,
            column: 16
          },
          end: {
            line: 143,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 86,
            column: 20
          },
          end: {
            line: 89,
            column: 64
          }
        }, {
          start: {
            line: 90,
            column: 20
          },
          end: {
            line: 93,
            column: 37
          }
        }, {
          start: {
            line: 94,
            column: 20
          },
          end: {
            line: 96,
            column: 157
          }
        }, {
          start: {
            line: 97,
            column: 20
          },
          end: {
            line: 109,
            column: 31
          }
        }, {
          start: {
            line: 110,
            column: 20
          },
          end: {
            line: 113,
            column: 37
          }
        }, {
          start: {
            line: 114,
            column: 20
          },
          end: {
            line: 116,
            column: 177
          }
        }, {
          start: {
            line: 117,
            column: 20
          },
          end: {
            line: 129,
            column: 31
          }
        }, {
          start: {
            line: 130,
            column: 20
          },
          end: {
            line: 140,
            column: 31
          }
        }, {
          start: {
            line: 141,
            column: 20
          },
          end: {
            line: 141,
            column: 52
          }
        }, {
          start: {
            line: 142,
            column: 20
          },
          end: {
            line: 142,
            column: 50
          }
        }],
        line: 85
      },
      "35": {
        loc: {
          start: {
            line: 154,
            column: 16
          },
          end: {
            line: 164,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 155,
            column: 20
          },
          end: {
            line: 157,
            column: 171
          }
        }, {
          start: {
            line: 158,
            column: 20
          },
          end: {
            line: 158,
            column: 61
          }
        }, {
          start: {
            line: 159,
            column: 20
          },
          end: {
            line: 162,
            column: 52
          }
        }, {
          start: {
            line: 163,
            column: 20
          },
          end: {
            line: 163,
            column: 50
          }
        }],
        line: 154
      },
      "36": {
        loc: {
          start: {
            line: 172,
            column: 8
          },
          end: {
            line: 173,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 172,
            column: 8
          },
          end: {
            line: 173,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 172
      },
      "37": {
        loc: {
          start: {
            line: 185,
            column: 8
          },
          end: {
            line: 193,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 185,
            column: 8
          },
          end: {
            line: 193,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 185
      },
      "38": {
        loc: {
          start: {
            line: 187,
            column: 12
          },
          end: {
            line: 189,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 187,
            column: 12
          },
          end: {
            line: 189,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 187
      },
      "39": {
        loc: {
          start: {
            line: 187,
            column: 16
          },
          end: {
            line: 187,
            column: 98
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 187,
            column: 16
          },
          end: {
            line: 187,
            column: 59
          }
        }, {
          start: {
            line: 187,
            column: 63
          },
          end: {
            line: 187,
            column: 98
          }
        }],
        line: 187
      },
      "40": {
        loc: {
          start: {
            line: 190,
            column: 12
          },
          end: {
            line: 192,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 190,
            column: 12
          },
          end: {
            line: 192,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 190
      },
      "41": {
        loc: {
          start: {
            line: 195,
            column: 8
          },
          end: {
            line: 200,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 195,
            column: 8
          },
          end: {
            line: 200,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 195
      },
      "42": {
        loc: {
          start: {
            line: 195,
            column: 12
          },
          end: {
            line: 195,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 195,
            column: 12
          },
          end: {
            line: 195,
            column: 49
          }
        }, {
          start: {
            line: 195,
            column: 53
          },
          end: {
            line: 195,
            column: 90
          }
        }],
        line: 195
      },
      "43": {
        loc: {
          start: {
            line: 197,
            column: 12
          },
          end: {
            line: 199,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 197,
            column: 12
          },
          end: {
            line: 199,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 197
      },
      "44": {
        loc: {
          start: {
            line: 202,
            column: 8
          },
          end: {
            line: 207,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 202,
            column: 8
          },
          end: {
            line: 207,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 202
      },
      "45": {
        loc: {
          start: {
            line: 202,
            column: 12
          },
          end: {
            line: 202,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 202,
            column: 12
          },
          end: {
            line: 202,
            column: 49
          }
        }, {
          start: {
            line: 202,
            column: 53
          },
          end: {
            line: 202,
            column: 85
          }
        }],
        line: 202
      },
      "46": {
        loc: {
          start: {
            line: 204,
            column: 12
          },
          end: {
            line: 206,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 204,
            column: 12
          },
          end: {
            line: 206,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 204
      },
      "47": {
        loc: {
          start: {
            line: 204,
            column: 16
          },
          end: {
            line: 204,
            column: 107
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 204,
            column: 16
          },
          end: {
            line: 204,
            column: 70
          }
        }, {
          start: {
            line: 204,
            column: 74
          },
          end: {
            line: 204,
            column: 107
          }
        }],
        line: 204
      },
      "48": {
        loc: {
          start: {
            line: 209,
            column: 8
          },
          end: {
            line: 211,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 209,
            column: 8
          },
          end: {
            line: 211,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 209
      },
      "49": {
        loc: {
          start: {
            line: 209,
            column: 12
          },
          end: {
            line: 209,
            column: 95
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 209,
            column: 12
          },
          end: {
            line: 209,
            column: 51
          }
        }, {
          start: {
            line: 209,
            column: 55
          },
          end: {
            line: 209,
            column: 95
          }
        }],
        line: 209
      },
      "50": {
        loc: {
          start: {
            line: 213,
            column: 8
          },
          end: {
            line: 218,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 213,
            column: 8
          },
          end: {
            line: 218,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 213
      },
      "51": {
        loc: {
          start: {
            line: 215,
            column: 12
          },
          end: {
            line: 217,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 215,
            column: 12
          },
          end: {
            line: 217,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 215
      },
      "52": {
        loc: {
          start: {
            line: 220,
            column: 8
          },
          end: {
            line: 222,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 220,
            column: 8
          },
          end: {
            line: 222,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 220
      },
      "53": {
        loc: {
          start: {
            line: 226,
            column: 8
          },
          end: {
            line: 234,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 226,
            column: 8
          },
          end: {
            line: 234,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 226
      },
      "54": {
        loc: {
          start: {
            line: 241,
            column: 8
          },
          end: {
            line: 242,
            column: 80
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 241,
            column: 8
          },
          end: {
            line: 242,
            column: 80
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 241
      },
      "55": {
        loc: {
          start: {
            line: 243,
            column: 8
          },
          end: {
            line: 244,
            column: 27
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 243,
            column: 8
          },
          end: {
            line: 244,
            column: 27
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 243
      },
      "56": {
        loc: {
          start: {
            line: 254,
            column: 16
          },
          end: {
            line: 373,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 255,
            column: 20
          },
          end: {
            line: 259,
            column: 37
          }
        }, {
          start: {
            line: 260,
            column: 20
          },
          end: {
            line: 262,
            column: 64
          }
        }, {
          start: {
            line: 263,
            column: 20
          },
          end: {
            line: 302,
            column: 164
          }
        }, {
          start: {
            line: 303,
            column: 20
          },
          end: {
            line: 306,
            column: 48
          }
        }, {
          start: {
            line: 307,
            column: 20
          },
          end: {
            line: 310,
            column: 48
          }
        }, {
          start: {
            line: 311,
            column: 20
          },
          end: {
            line: 348,
            column: 48
          }
        }, {
          start: {
            line: 349,
            column: 20
          },
          end: {
            line: 352,
            column: 48
          }
        }, {
          start: {
            line: 353,
            column: 20
          },
          end: {
            line: 356,
            column: 48
          }
        }, {
          start: {
            line: 357,
            column: 20
          },
          end: {
            line: 372,
            column: 31
          }
        }],
        line: 254
      },
      "57": {
        loc: {
          start: {
            line: 358,
            column: 24
          },
          end: {
            line: 366,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 358,
            column: 24
          },
          end: {
            line: 366,
            column: 25
          }
        }, {
          start: {
            line: 361,
            column: 29
          },
          end: {
            line: 366,
            column: 25
          }
        }],
        line: 358
      },
      "58": {
        loc: {
          start: {
            line: 358,
            column: 28
          },
          end: {
            line: 358,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 358,
            column: 28
          },
          end: {
            line: 358,
            column: 46
          }
        }, {
          start: {
            line: 358,
            column: 50
          },
          end: {
            line: 358,
            column: 65
          }
        }],
        line: 358
      },
      "59": {
        loc: {
          start: {
            line: 361,
            column: 29
          },
          end: {
            line: 366,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 361,
            column: 29
          },
          end: {
            line: 366,
            column: 25
          }
        }, {
          start: {
            line: 364,
            column: 29
          },
          end: {
            line: 366,
            column: 25
          }
        }],
        line: 361
      },
      "60": {
        loc: {
          start: {
            line: 361,
            column: 33
          },
          end: {
            line: 361,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 361,
            column: 33
          },
          end: {
            line: 361,
            column: 48
          }
        }, {
          start: {
            line: 361,
            column: 52
          },
          end: {
            line: 361,
            column: 64
          }
        }],
        line: 361
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0, 0, 0],
      "33": [0, 0],
      "34": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "35": [0, 0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0, 0, 0, 0, 0, 0, 0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/assessmentServiceIntegration.ts",
      mappings: ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGH,+EAAsG;AACtG,qEAAsF;AAWtF;IAAA;IAoVA,CAAC;IAjVC;;OAEG;IACU,uCAAU,GAAvB;uCAA2B,OAAO;;;;;wBAChC,IAAI,IAAI,CAAC,WAAW;4BAAE,sBAAO;;;;wBAG3B,qBAAM,2DAA4B,CAAC,UAAU,EAAE,EAAA;;wBAA/C,SAA+C,CAAC;wBAChD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;wBACxB,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;;;;wBAE5D,OAAO,CAAC,IAAI,CAAC,2CAA2C,EAAE,OAAK,CAAC,CAAC;;;;;;KAGpE;IAED;;OAEG;IACU,0DAA6B,GAA1C,UACE,SAA6B,EAC7B,QAA4B;uCAC3B,OAAO;;;;;wBACF,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBAE7B,kCAAkC;wBAClC,qBAAM,IAAI,CAAC,UAAU,EAAE,EAAA;;wBADvB,kCAAkC;wBAClC,SAAuB,CAAC;;;;wBAIK,qBAAM,2DAA4B,CAAC,6BAA6B,CACzF,SAAS,EACT,QAAQ,CACT,EAAA;;wBAHK,kBAAkB,GAAG,SAG1B;wBAEK,qBAAqB,GAAG,kBAAkB;6BAC7C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;6BACX,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,UAAU,CAAC,IAAI,EAArB,CAAqB,CAAC,CAAC;wBAEjC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;wBAE3D,sBAAO;gCACL,qBAAqB,uBAAA;gCACrB,eAAe,EAAE,kBAAkB;gCACnC,WAAW,EAAE,aAAa;gCAC1B,UAAU,EAAE,IAAI,CAAC,0BAA0B,CAAC,kBAAkB,CAAC;gCAC/D,cAAc,gBAAA;6BACf,EAAC;;;wBAGF,OAAO,CAAC,IAAI,CAAC,uDAAuD,EAAE,kBAAgB,CAAC,CAAC;;;;wBAI7D,qBAAM,iDAAuB,CAAC,wBAAwB,CAC7E,SAAS,EACT,QAAQ,EACR,iCAAiC,CAClC,EAAA;;wBAJK,gBAAgB,GAAG,SAIxB;wBAEK,qBAAqB,GAAG,gBAAgB,CAAC,qBAAqB;6BACjE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;6BACX,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,UAAU,EAAd,CAAc,CAAC,CAAC;wBAExB,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;wBAE3D,sBAAO;gCACL,qBAAqB,uBAAA;gCACrB,gBAAgB,kBAAA;gCAChB,WAAW,EAAE,UAAU;gCACvB,UAAU,EAAE,gBAAgB,CAAC,eAAe;gCAC5C,cAAc,gBAAA;6BACf,EAAC;;;wBAGF,OAAO,CAAC,IAAI,CAAC,iDAAiD,EAAE,eAAa,CAAC,CAAC;wBAGzE,qBAAqB,GAAG,IAAI,CAAC,8BAA8B,CAC/D,QAAQ,CAAC,SAAS,EAClB,SAAS,CACV,CAAC;wBAEI,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;wBAE3D,sBAAO;gCACL,qBAAqB,uBAAA;gCACrB,WAAW,EAAE,OAAO;gCACpB,UAAU,EAAE,EAAE,EAAE,2CAA2C;gCAC3D,cAAc,gBAAA;6BACf,EAAC;;;;;;KAGP;IAED;;OAEG;IACU,gDAAmB,GAAhC,UACE,SAA6B,EAC7B,QAA4B;uCAC3B,OAAO;;;;;;wBAEC,qBAAM,iDAAuB,CAAC,wBAAwB,CAC3D,SAAS,EACT,QAAQ,EACR,2BAA2B,CAC5B,EAAA;4BAJD,sBAAO,SAIN,EAAC;;;wBAEF,OAAO,CAAC,IAAI,CAAC,uCAAuC,EAAE,OAAK,CAAC,CAAC;wBAC7D,sBAAO,IAAI,EAAC;;;;;KAEf;IAED;;OAEG;IACY,uDAA0B,GAAzC,UAA0C,OAAiC;QACzE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAEpC,IAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,KAAK,IAAK,OAAA,GAAG,GAAG,KAAK,CAAC,eAAe,EAA3B,CAA2B,EAAE,CAAC,CAAC,CAAC;QACvF,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACY,2DAA8B,GAA7C,UACE,SAAmB,EACnB,SAA6B;QAE7B,IAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAM,yBAAyB,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QAC3F,IAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAEjE,8BAA8B;QAC9B,IAAI,SAAS,CAAC,QAAQ,CAAC,uBAAuB,CAAC,EAAE,CAAC;YAChD,WAAW,CAAC,IAAI,CAAC,0BAA0B,EAAE,sBAAsB,CAAC,CAAC;YAErE,IAAI,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;gBACvF,WAAW,CAAC,IAAI,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;YACvD,CAAC;YAED,IAAI,yBAAyB,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;gBACxD,WAAW,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;QAED,6BAA6B;QAC7B,IAAI,SAAS,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACnF,WAAW,CAAC,IAAI,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;YAEtD,IAAI,yBAAyB,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,CAAC;gBAC5D,WAAW,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YACnD,CAAC;QACH,CAAC;QAED,gCAAgC;QAChC,IAAI,SAAS,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC9E,WAAW,CAAC,IAAI,CAAC,8BAA8B,EAAE,iBAAiB,CAAC,CAAC;YAEpE,IAAI,yBAAyB,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBAChG,WAAW,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,IAAI,SAAS,CAAC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACxF,WAAW,CAAC,IAAI,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,qBAAqB,CAAC,CAAC;QAChF,CAAC;QAED,0BAA0B;QAC1B,IAAI,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YACxC,WAAW,CAAC,IAAI,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC;YAEvD,IAAI,yBAAyB,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;gBAC7D,WAAW,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,IAAI,SAAS,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;YAC7C,WAAW,CAAC,IAAI,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;QACzE,CAAC;QAED,+CAA+C;QAC/C,IAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;QAE3D,uDAAuD;QACvD,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO;gBACL,8BAA8B;gBAC9B,0BAA0B;gBAC1B,iBAAiB;gBACjB,cAAc;gBACd,gBAAgB;aACjB,CAAC;QACJ,CAAC;QAED,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACY,0CAAa,GAA5B,UAA6B,KAAwC;QACnE,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,OAAO,CAAC,KAAK,QAAQ,EAArB,CAAqB,CAAa,CAAC;QACtF,IAAI,OAAO,KAAK,KAAK,QAAQ;YAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QAC9C,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACU,wCAAW,GAAxB;uCAA4B,OAAO;;;;;wBAM7B,kBAAkB,GAAG,KAAK,CAAC;wBAC3B,eAAe,GAAG,KAAK,CAAC;wBACtB,YAAY,GAAG,IAAI,CAAC;;;;wBAGxB,qBAAM,IAAI,CAAC,UAAU,EAAE,EAAA;;wBAAvB,SAAuB,CAAC;wBAElB,YAAY,GAAuB;4BACvC,YAAY,EAAE,MAAM;4BACpB,gBAAgB,EAAE,KAAK;4BACvB,0BAA0B,EAAE,CAAC,aAAa,CAAC;4BAC3C,aAAa,EAAE,CAAC,QAAQ,CAAC;4BACzB,sBAAsB,EAAE,CAAC,QAAQ,CAAC;4BAClC,iBAAiB,EAAE,CAAC,YAAY,CAAC;4BACjC,mBAAmB,EAAE,CAAC;4BACtB,aAAa,EAAE,CAAC;4BAChB,cAAc,EAAE,CAAC;4BACjB,aAAa,EAAE,CAAC;4BAChB,iBAAiB,EAAE,EAAE;4BACrB,0BAA0B,EAAE,MAAM;4BAClC,0BAA0B,EAAE,MAAM;4BAClC,yBAAyB,EAAE,MAAM;4BACjC,QAAQ,EAAE,MAAM;yBACjB,CAAC;wBAEI,YAAY,GAAuB;4BACvC,MAAM,EAAE;gCACN,kBAAkB,EAAE,CAAC;gCACrB,YAAY,EAAE,CAAC;gCACf,aAAa,EAAE,CAAC;gCAChB,YAAY,EAAE,CAAC;gCACf,gBAAgB,EAAE,EAAE;gCACpB,cAAc,EAAE,EAAE;6BACnB;4BACD,iBAAiB,EAAE,MAAM;4BACzB,SAAS,EAAE,CAAC,uBAAuB,CAAC;4BACpC,gBAAgB,EAAE,CAAC,YAAY,CAAC;4BAChC,mBAAmB,EAAE,aAAa;4BAClC,kBAAkB,EAAE,CAAC,MAAM,CAAC;4BAC5B,qBAAqB,EAAE,EAAE;4BACzB,kBAAkB,EAAE,EAAE;4BACtB,gBAAgB,EAAE,EAAE;4BACpB,kBAAkB,EAAE,CAAC,uBAAuB,CAAC;4BAC7C,uBAAuB,EAAE,aAAa;yBACvC,CAAC;wBAEwB,qBAAM,2DAA4B,CAAC,6BAA6B,CACxF,YAAY,EACZ,YAAY,CACb,EAAA;;wBAHK,iBAAiB,GAAG,SAGzB;wBACD,kBAAkB,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC;;;;wBAElD,OAAO,CAAC,IAAI,CAAC,0CAA0C,EAAE,OAAK,CAAC,CAAC;;;;wBAIzC,qBAAM,iDAAuB,CAAC,wBAAwB,CAC3E;gCACE,YAAY,EAAE,MAAM;gCACpB,gBAAgB,EAAE,KAAK;gCACvB,0BAA0B,EAAE,CAAC,aAAa,CAAC;gCAC3C,aAAa,EAAE,CAAC,QAAQ,CAAC;gCACzB,sBAAsB,EAAE,CAAC,QAAQ,CAAC;gCAClC,iBAAiB,EAAE,CAAC,YAAY,CAAC;gCACjC,mBAAmB,EAAE,CAAC;gCACtB,aAAa,EAAE,CAAC;gCAChB,cAAc,EAAE,CAAC;gCACjB,aAAa,EAAE,CAAC;gCAChB,iBAAiB,EAAE,EAAE;gCACrB,0BAA0B,EAAE,MAAM;gCAClC,0BAA0B,EAAE,MAAM;gCAClC,yBAAyB,EAAE,MAAM;gCACjC,QAAQ,EAAE,MAAM;6BACjB,EACD;gCACE,MAAM,EAAE;oCACN,kBAAkB,EAAE,CAAC;oCACrB,YAAY,EAAE,CAAC;oCACf,aAAa,EAAE,CAAC;oCAChB,YAAY,EAAE,CAAC;oCACf,gBAAgB,EAAE,EAAE;oCACpB,cAAc,EAAE,EAAE;iCACnB;gCACD,iBAAiB,EAAE,MAAM;gCACzB,SAAS,EAAE,CAAC,uBAAuB,CAAC;gCACpC,gBAAgB,EAAE,CAAC,YAAY,CAAC;gCAChC,mBAAmB,EAAE,aAAa;gCAClC,kBAAkB,EAAE,CAAC,MAAM,CAAC;gCAC5B,qBAAqB,EAAE,EAAE;gCACzB,kBAAkB,EAAE,EAAE;gCACtB,gBAAgB,EAAE,EAAE;gCACpB,kBAAkB,EAAE,CAAC,uBAAuB,CAAC;gCAC7C,uBAAuB,EAAE,aAAa;6BACvC,EACD,cAAc,CACf,EAAA;;wBAvCK,cAAc,GAAG,SAuCtB;wBACD,eAAe,GAAG,cAAc,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC;;;;wBAElE,OAAO,CAAC,IAAI,CAAC,uCAAuC,EAAE,OAAK,CAAC,CAAC;;;wBAI/D,IAAI,kBAAkB,IAAI,eAAe,EAAE,CAAC;4BAC1C,OAAO,GAAG,SAAS,CAAC;wBACtB,CAAC;6BAAM,IAAI,eAAe,IAAI,YAAY,EAAE,CAAC;4BAC3C,OAAO,GAAG,UAAU,CAAC;wBACvB,CAAC;6BAAM,CAAC;4BACN,OAAO,GAAG,UAAU,CAAC;wBACvB,CAAC;wBAED,sBAAO;gCACL,WAAW,EAAE,kBAAkB;gCAC/B,QAAQ,EAAE,eAAe;gCACzB,KAAK,EAAE,YAAY;gCACnB,OAAO,SAAA;6BACR,EAAC;;;;KACH;IAlVc,wCAAW,GAAG,KAAK,CAAC;IAmVrC,mCAAC;CAAA,AApVD,IAoVC;AApVY,oEAA4B",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/assessmentServiceIntegration.ts"],
      sourcesContent: ["/**\n * Assessment Service Integration Layer\n * Provides a clean interface for integrating algorithmic assessment with existing services\n */\n\nimport { AssessmentResponse, AssessmentInsights } from './assessmentScoring';\nimport { AlgorithmicAssessmentService, AlgorithmicMatchResult } from './algorithmicAssessmentService';\nimport { EnhancedFallbackService, FallbackInsights } from './enhancedFallbackService';\n\nexport interface IntegratedAssessmentResult {\n  careerRecommendations: string[];\n  detailedMatches?: AlgorithmicMatchResult[];\n  fallbackInsights?: FallbackInsights;\n  serviceUsed: 'algorithmic' | 'fallback' | 'basic';\n  confidence: number;\n  processingTime: number;\n}\n\nexport class AssessmentServiceIntegration {\n  private static initialized = false;\n\n  /**\n   * Initialize the assessment services\n   */\n  static async initialize(): Promise<void> {\n    if (this.initialized) return;\n\n    try {\n      await AlgorithmicAssessmentService.initialize();\n      this.initialized = true;\n      console.log('Assessment services initialized successfully');\n    } catch (error) {\n      console.warn('Failed to initialize assessment services:', error);\n      // Continue without algorithmic service\n    }\n  }\n\n  /**\n   * Generate career recommendations using the best available service\n   */\n  static async generateCareerRecommendations(\n    responses: AssessmentResponse,\n    insights: AssessmentInsights\n  ): Promise<IntegratedAssessmentResult> {\n    const startTime = Date.now();\n    \n    // Ensure services are initialized\n    await this.initialize();\n\n    try {\n      // Try algorithmic assessment first\n      const algorithmicMatches = await AlgorithmicAssessmentService.generateCareerRecommendations(\n        responses,\n        insights\n      );\n\n      const careerRecommendations = algorithmicMatches\n        .slice(0, 5)\n        .map(match => match.careerPath.name);\n\n      const processingTime = Math.max(1, Date.now() - startTime);\n\n      return {\n        careerRecommendations,\n        detailedMatches: algorithmicMatches,\n        serviceUsed: 'algorithmic',\n        confidence: this.calculateAverageConfidence(algorithmicMatches),\n        processingTime\n      };\n\n    } catch (algorithmicError) {\n      console.warn('Algorithmic service failed, trying enhanced fallback:', algorithmicError);\n\n      try {\n        // Try enhanced fallback service\n        const fallbackInsights = await EnhancedFallbackService.generateFallbackInsights(\n          responses,\n          insights,\n          'Algorithmic service unavailable'\n        );\n\n        const careerRecommendations = fallbackInsights.careerRecommendations\n          .slice(0, 5)\n          .map(rec => rec.careerPath);\n\n        const processingTime = Math.max(1, Date.now() - startTime);\n\n        return {\n          careerRecommendations,\n          fallbackInsights,\n          serviceUsed: 'fallback',\n          confidence: fallbackInsights.confidenceScore,\n          processingTime\n        };\n\n      } catch (fallbackError) {\n        console.warn('Enhanced fallback failed, using basic fallback:', fallbackError);\n\n        // Use basic rule-based fallback\n        const careerRecommendations = this.generateBasicCareerSuggestions(\n          insights.topSkills,\n          responses\n        );\n\n        const processingTime = Math.max(1, Date.now() - startTime);\n\n        return {\n          careerRecommendations,\n          serviceUsed: 'basic',\n          confidence: 60, // Basic confidence for rule-based approach\n          processingTime\n        };\n      }\n    }\n  }\n\n  /**\n   * Get enhanced assessment insights with fallback support\n   */\n  static async getEnhancedInsights(\n    responses: AssessmentResponse,\n    insights: AssessmentInsights\n  ): Promise<FallbackInsights | null> {\n    try {\n      return await EnhancedFallbackService.generateFallbackInsights(\n        responses,\n        insights,\n        'Enhanced insights request'\n      );\n    } catch (error) {\n      console.warn('Failed to generate enhanced insights:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Calculate average confidence from algorithmic matches\n   */\n  private static calculateAverageConfidence(matches: AlgorithmicMatchResult[]): number {\n    if (matches.length === 0) return 50;\n\n    const totalConfidence = matches.reduce((sum, match) => sum + match.confidenceLevel, 0);\n    return Math.round(totalConfidence / matches.length);\n  }\n\n  /**\n   * Basic career suggestions as final fallback\n   */\n  private static generateBasicCareerSuggestions(\n    topSkills: string[],\n    responses: AssessmentResponse\n  ): string[] {\n    const suggestions: string[] = [];\n    const skillDevelopmentInterests = this.getArrayValue(responses.skill_development_interest);\n    const careerValues = this.getArrayValue(responses.career_values);\n    \n    // Technical programming paths\n    if (topSkills.includes('technical_programming')) {\n      suggestions.push('Full-Stack Web Developer', 'Mobile App Developer');\n      \n      if (skillDevelopmentInterests.includes('ai_ml') || topSkills.includes('data_analysis')) {\n        suggestions.push('AI/ML Engineer', 'Data Scientist');\n      }\n      \n      if (skillDevelopmentInterests.includes('cybersecurity')) {\n        suggestions.push('Cybersecurity Specialist');\n      }\n    }\n\n    // Creative and content paths\n    if (topSkills.includes('writing_content') || topSkills.includes('design_creative')) {\n      suggestions.push('UX/UI Designer', 'Content Creator');\n      \n      if (skillDevelopmentInterests.includes('digital_marketing')) {\n        suggestions.push('Digital Marketing Specialist');\n      }\n    }\n\n    // Business and leadership paths\n    if (topSkills.includes('sales_marketing') || topSkills.includes('leadership')) {\n      suggestions.push('Digital Marketing Specialist', 'Product Manager');\n      \n      if (skillDevelopmentInterests.includes('entrepreneurship') || careerValues.includes('autonomy')) {\n        suggestions.push('Entrepreneur / Startup Founder');\n      }\n    }\n\n    // Education and coaching paths\n    if (topSkills.includes('teaching_training') || topSkills.includes('coaching_mentoring')) {\n      suggestions.push('Online Coaching', 'Course Creation', 'Training Specialist');\n    }\n\n    // Data and analysis paths\n    if (topSkills.includes('data_analysis')) {\n      suggestions.push('Data Scientist', 'Business Analyst');\n      \n      if (skillDevelopmentInterests.includes('financial_planning')) {\n        suggestions.push('Financial Advisor / Planner');\n      }\n    }\n\n    // Project management paths\n    if (topSkills.includes('project_management')) {\n      suggestions.push('Product Manager', 'Project Manager', 'Scrum Master');\n    }\n\n    // Remove duplicates and return top suggestions\n    const uniqueSuggestions = Array.from(new Set(suggestions));\n    \n    // If still no matches, provide diverse default options\n    if (uniqueSuggestions.length === 0) {\n      return [\n        'Digital Marketing Specialist',\n        'Full-Stack Web Developer', \n        'Product Manager',\n        'Data Analyst',\n        'UX/UI Designer'\n      ];\n    }\n    \n    return uniqueSuggestions.slice(0, 5);\n  }\n\n  /**\n   * Utility method for extracting array values\n   */\n  private static getArrayValue(value: string | string[] | number | null): string[] {\n    if (Array.isArray(value)) return value.filter(v => typeof v === 'string') as string[];\n    if (typeof value === 'string') return [value];\n    return [];\n  }\n\n  /**\n   * Health check for assessment services\n   */\n  static async healthCheck(): Promise<{\n    algorithmic: boolean;\n    fallback: boolean;\n    basic: boolean;\n    overall: 'healthy' | 'degraded' | 'critical';\n  }> {\n    let algorithmicHealthy = false;\n    let fallbackHealthy = false;\n    const basicHealthy = true; // Basic service is always available\n\n    try {\n      await this.initialize();\n      // Test algorithmic service with minimal data\n      const testResponse: AssessmentResponse = {\n        current_role: 'Test',\n        years_experience: '1-2',\n        skill_development_interest: ['coding_tech'],\n        career_values: ['growth'],\n        work_style_preferences: ['remote'],\n        biggest_obstacles: ['skill_gaps'],\n        financial_readiness: 3,\n        support_level: 3,\n        risk_tolerance: 3,\n        urgency_level: 3,\n        skills_confidence: 50,\n        desired_outcomes_work_life: 'Test',\n        desired_outcomes_financial: 'Test',\n        desired_outcomes_personal: 'Test',\n        location: 'Test'\n      };\n\n      const testInsights: AssessmentInsights = {\n        scores: {\n          financialReadiness: 3,\n          supportLevel: 3,\n          riskTolerance: 3,\n          urgencyLevel: 3,\n          skillsConfidence: 50,\n          readinessScore: 60\n        },\n        primaryMotivation: 'Test',\n        topSkills: ['technical_programming'],\n        biggestObstacles: ['skill_gaps'],\n        recommendedTimeline: '6-12 months',\n        keyRecommendations: ['Test'],\n        careerPathSuggestions: [],\n        careerPathAnalysis: [],\n        overallSkillGaps: [],\n        learningPriorities: ['technical_programming'],\n        estimatedTransitionTime: '6-12 months'\n      };\n\n      const algorithmicResult = await AlgorithmicAssessmentService.generateCareerRecommendations(\n        testResponse,\n        testInsights\n      );\n      algorithmicHealthy = algorithmicResult.length > 0;\n    } catch (error) {\n      console.warn('Algorithmic service health check failed:', error);\n    }\n\n    try {\n      const fallbackResult = await EnhancedFallbackService.generateFallbackInsights(\n        {\n          current_role: 'Test',\n          years_experience: '1-2',\n          skill_development_interest: ['coding_tech'],\n          career_values: ['growth'],\n          work_style_preferences: ['remote'],\n          biggest_obstacles: ['skill_gaps'],\n          financial_readiness: 3,\n          support_level: 3,\n          risk_tolerance: 3,\n          urgency_level: 3,\n          skills_confidence: 50,\n          desired_outcomes_work_life: 'Test',\n          desired_outcomes_financial: 'Test',\n          desired_outcomes_personal: 'Test',\n          location: 'Test'\n        },\n        {\n          scores: {\n            financialReadiness: 3,\n            supportLevel: 3,\n            riskTolerance: 3,\n            urgencyLevel: 3,\n            skillsConfidence: 50,\n            readinessScore: 60\n          },\n          primaryMotivation: 'Test',\n          topSkills: ['technical_programming'],\n          biggestObstacles: ['skill_gaps'],\n          recommendedTimeline: '6-12 months',\n          keyRecommendations: ['Test'],\n          careerPathSuggestions: [],\n          careerPathAnalysis: [],\n          overallSkillGaps: [],\n          learningPriorities: ['technical_programming'],\n          estimatedTransitionTime: '6-12 months'\n        },\n        'Health check'\n      );\n      fallbackHealthy = fallbackResult.careerRecommendations.length > 0;\n    } catch (error) {\n      console.warn('Fallback service health check failed:', error);\n    }\n\n    let overall: 'healthy' | 'degraded' | 'critical';\n    if (algorithmicHealthy && fallbackHealthy) {\n      overall = 'healthy';\n    } else if (fallbackHealthy || basicHealthy) {\n      overall = 'degraded';\n    } else {\n      overall = 'critical';\n    }\n\n    return {\n      algorithmic: algorithmicHealthy,\n      fallback: fallbackHealthy,\n      basic: basicHealthy,\n      overall\n    };\n  }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c8e15960ab3ce31b004e38ca8758afb620ca2243"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1as9fh3sbl = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1as9fh3sbl();
var __awaiter =
/* istanbul ignore next */
(cov_1as9fh3sbl().s[0]++,
/* istanbul ignore next */
(cov_1as9fh3sbl().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1as9fh3sbl().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1as9fh3sbl().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1as9fh3sbl().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1as9fh3sbl().f[1]++;
    cov_1as9fh3sbl().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1as9fh3sbl().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_1as9fh3sbl().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().f[2]++;
      cov_1as9fh3sbl().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1as9fh3sbl().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_1as9fh3sbl().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_1as9fh3sbl().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1as9fh3sbl().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().f[4]++;
      cov_1as9fh3sbl().s[4]++;
      try {
        /* istanbul ignore next */
        cov_1as9fh3sbl().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1as9fh3sbl().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().f[5]++;
      cov_1as9fh3sbl().s[7]++;
      try {
        /* istanbul ignore next */
        cov_1as9fh3sbl().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1as9fh3sbl().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().f[6]++;
      cov_1as9fh3sbl().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1as9fh3sbl().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1as9fh3sbl().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1as9fh3sbl().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1as9fh3sbl().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1as9fh3sbl().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1as9fh3sbl().s[12]++,
/* istanbul ignore next */
(cov_1as9fh3sbl().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_1as9fh3sbl().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1as9fh3sbl().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1as9fh3sbl().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_1as9fh3sbl().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1as9fh3sbl().f[8]++;
        cov_1as9fh3sbl().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1as9fh3sbl().b[6][0]++;
          cov_1as9fh3sbl().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1as9fh3sbl().b[6][1]++;
        }
        cov_1as9fh3sbl().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1as9fh3sbl().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1as9fh3sbl().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1as9fh3sbl().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1as9fh3sbl().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1as9fh3sbl().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1as9fh3sbl().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1as9fh3sbl().f[9]++;
    cov_1as9fh3sbl().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1as9fh3sbl().f[10]++;
    cov_1as9fh3sbl().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().f[11]++;
      cov_1as9fh3sbl().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1as9fh3sbl().f[12]++;
    cov_1as9fh3sbl().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().b[9][0]++;
      cov_1as9fh3sbl().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1as9fh3sbl().b[9][1]++;
    }
    cov_1as9fh3sbl().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_1as9fh3sbl().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_1as9fh3sbl().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1as9fh3sbl().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1as9fh3sbl().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().s[25]++;
      try {
        /* istanbul ignore next */
        cov_1as9fh3sbl().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1as9fh3sbl().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_1as9fh3sbl().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1as9fh3sbl().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1as9fh3sbl().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1as9fh3sbl().b[15][0]++,
        /* istanbul ignore next */
        (cov_1as9fh3sbl().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1as9fh3sbl().b[16][1]++,
        /* istanbul ignore next */
        (cov_1as9fh3sbl().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1as9fh3sbl().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1as9fh3sbl().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1as9fh3sbl().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1as9fh3sbl().b[12][0]++;
          cov_1as9fh3sbl().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1as9fh3sbl().b[12][1]++;
        }
        cov_1as9fh3sbl().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1as9fh3sbl().b[18][0]++;
          cov_1as9fh3sbl().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1as9fh3sbl().b[18][1]++;
        }
        cov_1as9fh3sbl().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[19][1]++;
            cov_1as9fh3sbl().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[19][2]++;
            cov_1as9fh3sbl().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[19][3]++;
            cov_1as9fh3sbl().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[19][4]++;
            cov_1as9fh3sbl().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[19][5]++;
            cov_1as9fh3sbl().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_1as9fh3sbl().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1as9fh3sbl().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1as9fh3sbl().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1as9fh3sbl().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1as9fh3sbl().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1as9fh3sbl().b[20][0]++;
              cov_1as9fh3sbl().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1as9fh3sbl().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1as9fh3sbl().b[20][1]++;
            }
            cov_1as9fh3sbl().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_1as9fh3sbl().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1as9fh3sbl().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1as9fh3sbl().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1as9fh3sbl().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1as9fh3sbl().b[23][0]++;
              cov_1as9fh3sbl().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1as9fh3sbl().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1as9fh3sbl().b[23][1]++;
            }
            cov_1as9fh3sbl().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_1as9fh3sbl().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1as9fh3sbl().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1as9fh3sbl().b[25][0]++;
              cov_1as9fh3sbl().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1as9fh3sbl().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_1as9fh3sbl().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1as9fh3sbl().b[25][1]++;
            }
            cov_1as9fh3sbl().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_1as9fh3sbl().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_1as9fh3sbl().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1as9fh3sbl().b[27][0]++;
              cov_1as9fh3sbl().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1as9fh3sbl().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1as9fh3sbl().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1as9fh3sbl().b[27][1]++;
            }
            cov_1as9fh3sbl().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1as9fh3sbl().b[29][0]++;
              cov_1as9fh3sbl().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1as9fh3sbl().b[29][1]++;
            }
            cov_1as9fh3sbl().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1as9fh3sbl().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1as9fh3sbl().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1as9fh3sbl().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1as9fh3sbl().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1as9fh3sbl().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().b[30][0]++;
      cov_1as9fh3sbl().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1as9fh3sbl().b[30][1]++;
    }
    cov_1as9fh3sbl().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1as9fh3sbl().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1as9fh3sbl().b[31][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_1as9fh3sbl().s[67]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1as9fh3sbl().s[68]++;
exports.AssessmentServiceIntegration = void 0;
var algorithmicAssessmentService_1 =
/* istanbul ignore next */
(cov_1as9fh3sbl().s[69]++, require("./algorithmicAssessmentService"));
var enhancedFallbackService_1 =
/* istanbul ignore next */
(cov_1as9fh3sbl().s[70]++, require("./enhancedFallbackService"));
var AssessmentServiceIntegration =
/* istanbul ignore next */
(/** @class */cov_1as9fh3sbl().s[71]++, function () {
  /* istanbul ignore next */
  cov_1as9fh3sbl().f[13]++;
  function AssessmentServiceIntegration() {
    /* istanbul ignore next */
    cov_1as9fh3sbl().f[14]++;
  }
  /**
   * Initialize the assessment services
   */
  /* istanbul ignore next */
  cov_1as9fh3sbl().s[72]++;
  AssessmentServiceIntegration.initialize = function () {
    /* istanbul ignore next */
    cov_1as9fh3sbl().f[15]++;
    cov_1as9fh3sbl().s[73]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1as9fh3sbl().f[16]++;
      var error_1;
      /* istanbul ignore next */
      cov_1as9fh3sbl().s[74]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1as9fh3sbl().f[17]++;
        cov_1as9fh3sbl().s[75]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[32][0]++;
            cov_1as9fh3sbl().s[76]++;
            if (this.initialized) {
              /* istanbul ignore next */
              cov_1as9fh3sbl().b[33][0]++;
              cov_1as9fh3sbl().s[77]++;
              return [2 /*return*/];
            } else
            /* istanbul ignore next */
            {
              cov_1as9fh3sbl().b[33][1]++;
            }
            cov_1as9fh3sbl().s[78]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[32][1]++;
            cov_1as9fh3sbl().s[79]++;
            _a.trys.push([1, 3,, 4]);
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[80]++;
            return [4 /*yield*/, algorithmicAssessmentService_1.AlgorithmicAssessmentService.initialize()];
          case 2:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[32][2]++;
            cov_1as9fh3sbl().s[81]++;
            _a.sent();
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[82]++;
            this.initialized = true;
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[83]++;
            console.log('Assessment services initialized successfully');
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[84]++;
            return [3 /*break*/, 4];
          case 3:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[32][3]++;
            cov_1as9fh3sbl().s[85]++;
            error_1 = _a.sent();
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[86]++;
            console.warn('Failed to initialize assessment services:', error_1);
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[87]++;
            return [3 /*break*/, 4];
          case 4:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[32][4]++;
            cov_1as9fh3sbl().s[88]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Generate career recommendations using the best available service
   */
  /* istanbul ignore next */
  cov_1as9fh3sbl().s[89]++;
  AssessmentServiceIntegration.generateCareerRecommendations = function (responses, insights) {
    /* istanbul ignore next */
    cov_1as9fh3sbl().f[18]++;
    cov_1as9fh3sbl().s[90]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1as9fh3sbl().f[19]++;
      var startTime, algorithmicMatches, careerRecommendations, processingTime, algorithmicError_1, fallbackInsights, careerRecommendations, processingTime, fallbackError_1, careerRecommendations, processingTime;
      /* istanbul ignore next */
      cov_1as9fh3sbl().s[91]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1as9fh3sbl().f[20]++;
        cov_1as9fh3sbl().s[92]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[34][0]++;
            cov_1as9fh3sbl().s[93]++;
            startTime = Date.now();
            // Ensure services are initialized
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[94]++;
            return [4 /*yield*/, this.initialize()];
          case 1:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[34][1]++;
            cov_1as9fh3sbl().s[95]++;
            // Ensure services are initialized
            _a.sent();
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[96]++;
            _a.label = 2;
          case 2:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[34][2]++;
            cov_1as9fh3sbl().s[97]++;
            _a.trys.push([2, 4,, 9]);
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[98]++;
            return [4 /*yield*/, algorithmicAssessmentService_1.AlgorithmicAssessmentService.generateCareerRecommendations(responses, insights)];
          case 3:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[34][3]++;
            cov_1as9fh3sbl().s[99]++;
            algorithmicMatches = _a.sent();
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[100]++;
            careerRecommendations = algorithmicMatches.slice(0, 5).map(function (match) {
              /* istanbul ignore next */
              cov_1as9fh3sbl().f[21]++;
              cov_1as9fh3sbl().s[101]++;
              return match.careerPath.name;
            });
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[102]++;
            processingTime = Math.max(1, Date.now() - startTime);
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[103]++;
            return [2 /*return*/, {
              careerRecommendations: careerRecommendations,
              detailedMatches: algorithmicMatches,
              serviceUsed: 'algorithmic',
              confidence: this.calculateAverageConfidence(algorithmicMatches),
              processingTime: processingTime
            }];
          case 4:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[34][4]++;
            cov_1as9fh3sbl().s[104]++;
            algorithmicError_1 = _a.sent();
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[105]++;
            console.warn('Algorithmic service failed, trying enhanced fallback:', algorithmicError_1);
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[106]++;
            _a.label = 5;
          case 5:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[34][5]++;
            cov_1as9fh3sbl().s[107]++;
            _a.trys.push([5, 7,, 8]);
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[108]++;
            return [4 /*yield*/, enhancedFallbackService_1.EnhancedFallbackService.generateFallbackInsights(responses, insights, 'Algorithmic service unavailable')];
          case 6:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[34][6]++;
            cov_1as9fh3sbl().s[109]++;
            fallbackInsights = _a.sent();
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[110]++;
            careerRecommendations = fallbackInsights.careerRecommendations.slice(0, 5).map(function (rec) {
              /* istanbul ignore next */
              cov_1as9fh3sbl().f[22]++;
              cov_1as9fh3sbl().s[111]++;
              return rec.careerPath;
            });
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[112]++;
            processingTime = Math.max(1, Date.now() - startTime);
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[113]++;
            return [2 /*return*/, {
              careerRecommendations: careerRecommendations,
              fallbackInsights: fallbackInsights,
              serviceUsed: 'fallback',
              confidence: fallbackInsights.confidenceScore,
              processingTime: processingTime
            }];
          case 7:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[34][7]++;
            cov_1as9fh3sbl().s[114]++;
            fallbackError_1 = _a.sent();
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[115]++;
            console.warn('Enhanced fallback failed, using basic fallback:', fallbackError_1);
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[116]++;
            careerRecommendations = this.generateBasicCareerSuggestions(insights.topSkills, responses);
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[117]++;
            processingTime = Math.max(1, Date.now() - startTime);
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[118]++;
            return [2 /*return*/, {
              careerRecommendations: careerRecommendations,
              serviceUsed: 'basic',
              confidence: 60,
              // Basic confidence for rule-based approach
              processingTime: processingTime
            }];
          case 8:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[34][8]++;
            cov_1as9fh3sbl().s[119]++;
            return [3 /*break*/, 9];
          case 9:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[34][9]++;
            cov_1as9fh3sbl().s[120]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Get enhanced assessment insights with fallback support
   */
  /* istanbul ignore next */
  cov_1as9fh3sbl().s[121]++;
  AssessmentServiceIntegration.getEnhancedInsights = function (responses, insights) {
    /* istanbul ignore next */
    cov_1as9fh3sbl().f[23]++;
    cov_1as9fh3sbl().s[122]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1as9fh3sbl().f[24]++;
      var error_2;
      /* istanbul ignore next */
      cov_1as9fh3sbl().s[123]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1as9fh3sbl().f[25]++;
        cov_1as9fh3sbl().s[124]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[35][0]++;
            cov_1as9fh3sbl().s[125]++;
            _a.trys.push([0, 2,, 3]);
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[126]++;
            return [4 /*yield*/, enhancedFallbackService_1.EnhancedFallbackService.generateFallbackInsights(responses, insights, 'Enhanced insights request')];
          case 1:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[35][1]++;
            cov_1as9fh3sbl().s[127]++;
            return [2 /*return*/, _a.sent()];
          case 2:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[35][2]++;
            cov_1as9fh3sbl().s[128]++;
            error_2 = _a.sent();
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[129]++;
            console.warn('Failed to generate enhanced insights:', error_2);
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[130]++;
            return [2 /*return*/, null];
          case 3:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[35][3]++;
            cov_1as9fh3sbl().s[131]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Calculate average confidence from algorithmic matches
   */
  /* istanbul ignore next */
  cov_1as9fh3sbl().s[132]++;
  AssessmentServiceIntegration.calculateAverageConfidence = function (matches) {
    /* istanbul ignore next */
    cov_1as9fh3sbl().f[26]++;
    cov_1as9fh3sbl().s[133]++;
    if (matches.length === 0) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().b[36][0]++;
      cov_1as9fh3sbl().s[134]++;
      return 50;
    } else
    /* istanbul ignore next */
    {
      cov_1as9fh3sbl().b[36][1]++;
    }
    var totalConfidence =
    /* istanbul ignore next */
    (cov_1as9fh3sbl().s[135]++, matches.reduce(function (sum, match) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().f[27]++;
      cov_1as9fh3sbl().s[136]++;
      return sum + match.confidenceLevel;
    }, 0));
    /* istanbul ignore next */
    cov_1as9fh3sbl().s[137]++;
    return Math.round(totalConfidence / matches.length);
  };
  /**
   * Basic career suggestions as final fallback
   */
  /* istanbul ignore next */
  cov_1as9fh3sbl().s[138]++;
  AssessmentServiceIntegration.generateBasicCareerSuggestions = function (topSkills, responses) {
    /* istanbul ignore next */
    cov_1as9fh3sbl().f[28]++;
    var suggestions =
    /* istanbul ignore next */
    (cov_1as9fh3sbl().s[139]++, []);
    var skillDevelopmentInterests =
    /* istanbul ignore next */
    (cov_1as9fh3sbl().s[140]++, this.getArrayValue(responses.skill_development_interest));
    var careerValues =
    /* istanbul ignore next */
    (cov_1as9fh3sbl().s[141]++, this.getArrayValue(responses.career_values));
    // Technical programming paths
    /* istanbul ignore next */
    cov_1as9fh3sbl().s[142]++;
    if (topSkills.includes('technical_programming')) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().b[37][0]++;
      cov_1as9fh3sbl().s[143]++;
      suggestions.push('Full-Stack Web Developer', 'Mobile App Developer');
      /* istanbul ignore next */
      cov_1as9fh3sbl().s[144]++;
      if (
      /* istanbul ignore next */
      (cov_1as9fh3sbl().b[39][0]++, skillDevelopmentInterests.includes('ai_ml')) ||
      /* istanbul ignore next */
      (cov_1as9fh3sbl().b[39][1]++, topSkills.includes('data_analysis'))) {
        /* istanbul ignore next */
        cov_1as9fh3sbl().b[38][0]++;
        cov_1as9fh3sbl().s[145]++;
        suggestions.push('AI/ML Engineer', 'Data Scientist');
      } else
      /* istanbul ignore next */
      {
        cov_1as9fh3sbl().b[38][1]++;
      }
      cov_1as9fh3sbl().s[146]++;
      if (skillDevelopmentInterests.includes('cybersecurity')) {
        /* istanbul ignore next */
        cov_1as9fh3sbl().b[40][0]++;
        cov_1as9fh3sbl().s[147]++;
        suggestions.push('Cybersecurity Specialist');
      } else
      /* istanbul ignore next */
      {
        cov_1as9fh3sbl().b[40][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_1as9fh3sbl().b[37][1]++;
    }
    // Creative and content paths
    cov_1as9fh3sbl().s[148]++;
    if (
    /* istanbul ignore next */
    (cov_1as9fh3sbl().b[42][0]++, topSkills.includes('writing_content')) ||
    /* istanbul ignore next */
    (cov_1as9fh3sbl().b[42][1]++, topSkills.includes('design_creative'))) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().b[41][0]++;
      cov_1as9fh3sbl().s[149]++;
      suggestions.push('UX/UI Designer', 'Content Creator');
      /* istanbul ignore next */
      cov_1as9fh3sbl().s[150]++;
      if (skillDevelopmentInterests.includes('digital_marketing')) {
        /* istanbul ignore next */
        cov_1as9fh3sbl().b[43][0]++;
        cov_1as9fh3sbl().s[151]++;
        suggestions.push('Digital Marketing Specialist');
      } else
      /* istanbul ignore next */
      {
        cov_1as9fh3sbl().b[43][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_1as9fh3sbl().b[41][1]++;
    }
    // Business and leadership paths
    cov_1as9fh3sbl().s[152]++;
    if (
    /* istanbul ignore next */
    (cov_1as9fh3sbl().b[45][0]++, topSkills.includes('sales_marketing')) ||
    /* istanbul ignore next */
    (cov_1as9fh3sbl().b[45][1]++, topSkills.includes('leadership'))) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().b[44][0]++;
      cov_1as9fh3sbl().s[153]++;
      suggestions.push('Digital Marketing Specialist', 'Product Manager');
      /* istanbul ignore next */
      cov_1as9fh3sbl().s[154]++;
      if (
      /* istanbul ignore next */
      (cov_1as9fh3sbl().b[47][0]++, skillDevelopmentInterests.includes('entrepreneurship')) ||
      /* istanbul ignore next */
      (cov_1as9fh3sbl().b[47][1]++, careerValues.includes('autonomy'))) {
        /* istanbul ignore next */
        cov_1as9fh3sbl().b[46][0]++;
        cov_1as9fh3sbl().s[155]++;
        suggestions.push('Entrepreneur / Startup Founder');
      } else
      /* istanbul ignore next */
      {
        cov_1as9fh3sbl().b[46][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_1as9fh3sbl().b[44][1]++;
    }
    // Education and coaching paths
    cov_1as9fh3sbl().s[156]++;
    if (
    /* istanbul ignore next */
    (cov_1as9fh3sbl().b[49][0]++, topSkills.includes('teaching_training')) ||
    /* istanbul ignore next */
    (cov_1as9fh3sbl().b[49][1]++, topSkills.includes('coaching_mentoring'))) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().b[48][0]++;
      cov_1as9fh3sbl().s[157]++;
      suggestions.push('Online Coaching', 'Course Creation', 'Training Specialist');
    } else
    /* istanbul ignore next */
    {
      cov_1as9fh3sbl().b[48][1]++;
    }
    // Data and analysis paths
    cov_1as9fh3sbl().s[158]++;
    if (topSkills.includes('data_analysis')) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().b[50][0]++;
      cov_1as9fh3sbl().s[159]++;
      suggestions.push('Data Scientist', 'Business Analyst');
      /* istanbul ignore next */
      cov_1as9fh3sbl().s[160]++;
      if (skillDevelopmentInterests.includes('financial_planning')) {
        /* istanbul ignore next */
        cov_1as9fh3sbl().b[51][0]++;
        cov_1as9fh3sbl().s[161]++;
        suggestions.push('Financial Advisor / Planner');
      } else
      /* istanbul ignore next */
      {
        cov_1as9fh3sbl().b[51][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_1as9fh3sbl().b[50][1]++;
    }
    // Project management paths
    cov_1as9fh3sbl().s[162]++;
    if (topSkills.includes('project_management')) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().b[52][0]++;
      cov_1as9fh3sbl().s[163]++;
      suggestions.push('Product Manager', 'Project Manager', 'Scrum Master');
    } else
    /* istanbul ignore next */
    {
      cov_1as9fh3sbl().b[52][1]++;
    }
    // Remove duplicates and return top suggestions
    var uniqueSuggestions =
    /* istanbul ignore next */
    (cov_1as9fh3sbl().s[164]++, Array.from(new Set(suggestions)));
    // If still no matches, provide diverse default options
    /* istanbul ignore next */
    cov_1as9fh3sbl().s[165]++;
    if (uniqueSuggestions.length === 0) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().b[53][0]++;
      cov_1as9fh3sbl().s[166]++;
      return ['Digital Marketing Specialist', 'Full-Stack Web Developer', 'Product Manager', 'Data Analyst', 'UX/UI Designer'];
    } else
    /* istanbul ignore next */
    {
      cov_1as9fh3sbl().b[53][1]++;
    }
    cov_1as9fh3sbl().s[167]++;
    return uniqueSuggestions.slice(0, 5);
  };
  /**
   * Utility method for extracting array values
   */
  /* istanbul ignore next */
  cov_1as9fh3sbl().s[168]++;
  AssessmentServiceIntegration.getArrayValue = function (value) {
    /* istanbul ignore next */
    cov_1as9fh3sbl().f[29]++;
    cov_1as9fh3sbl().s[169]++;
    if (Array.isArray(value)) {
      /* istanbul ignore next */
      cov_1as9fh3sbl().b[54][0]++;
      cov_1as9fh3sbl().s[170]++;
      return value.filter(function (v) {
        /* istanbul ignore next */
        cov_1as9fh3sbl().f[30]++;
        cov_1as9fh3sbl().s[171]++;
        return typeof v === 'string';
      });
    } else
    /* istanbul ignore next */
    {
      cov_1as9fh3sbl().b[54][1]++;
    }
    cov_1as9fh3sbl().s[172]++;
    if (typeof value === 'string') {
      /* istanbul ignore next */
      cov_1as9fh3sbl().b[55][0]++;
      cov_1as9fh3sbl().s[173]++;
      return [value];
    } else
    /* istanbul ignore next */
    {
      cov_1as9fh3sbl().b[55][1]++;
    }
    cov_1as9fh3sbl().s[174]++;
    return [];
  };
  /**
   * Health check for assessment services
   */
  /* istanbul ignore next */
  cov_1as9fh3sbl().s[175]++;
  AssessmentServiceIntegration.healthCheck = function () {
    /* istanbul ignore next */
    cov_1as9fh3sbl().f[31]++;
    cov_1as9fh3sbl().s[176]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_1as9fh3sbl().f[32]++;
      var algorithmicHealthy, fallbackHealthy, basicHealthy, testResponse, testInsights, algorithmicResult, error_3, fallbackResult, error_4, overall;
      /* istanbul ignore next */
      cov_1as9fh3sbl().s[177]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_1as9fh3sbl().f[33]++;
        cov_1as9fh3sbl().s[178]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[56][0]++;
            cov_1as9fh3sbl().s[179]++;
            algorithmicHealthy = false;
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[180]++;
            fallbackHealthy = false;
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[181]++;
            basicHealthy = true;
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[182]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[56][1]++;
            cov_1as9fh3sbl().s[183]++;
            _a.trys.push([1, 4,, 5]);
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[184]++;
            return [4 /*yield*/, this.initialize()];
          case 2:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[56][2]++;
            cov_1as9fh3sbl().s[185]++;
            _a.sent();
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[186]++;
            testResponse = {
              current_role: 'Test',
              years_experience: '1-2',
              skill_development_interest: ['coding_tech'],
              career_values: ['growth'],
              work_style_preferences: ['remote'],
              biggest_obstacles: ['skill_gaps'],
              financial_readiness: 3,
              support_level: 3,
              risk_tolerance: 3,
              urgency_level: 3,
              skills_confidence: 50,
              desired_outcomes_work_life: 'Test',
              desired_outcomes_financial: 'Test',
              desired_outcomes_personal: 'Test',
              location: 'Test'
            };
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[187]++;
            testInsights = {
              scores: {
                financialReadiness: 3,
                supportLevel: 3,
                riskTolerance: 3,
                urgencyLevel: 3,
                skillsConfidence: 50,
                readinessScore: 60
              },
              primaryMotivation: 'Test',
              topSkills: ['technical_programming'],
              biggestObstacles: ['skill_gaps'],
              recommendedTimeline: '6-12 months',
              keyRecommendations: ['Test'],
              careerPathSuggestions: [],
              careerPathAnalysis: [],
              overallSkillGaps: [],
              learningPriorities: ['technical_programming'],
              estimatedTransitionTime: '6-12 months'
            };
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[188]++;
            return [4 /*yield*/, algorithmicAssessmentService_1.AlgorithmicAssessmentService.generateCareerRecommendations(testResponse, testInsights)];
          case 3:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[56][3]++;
            cov_1as9fh3sbl().s[189]++;
            algorithmicResult = _a.sent();
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[190]++;
            algorithmicHealthy = algorithmicResult.length > 0;
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[191]++;
            return [3 /*break*/, 5];
          case 4:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[56][4]++;
            cov_1as9fh3sbl().s[192]++;
            error_3 = _a.sent();
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[193]++;
            console.warn('Algorithmic service health check failed:', error_3);
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[194]++;
            return [3 /*break*/, 5];
          case 5:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[56][5]++;
            cov_1as9fh3sbl().s[195]++;
            _a.trys.push([5, 7,, 8]);
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[196]++;
            return [4 /*yield*/, enhancedFallbackService_1.EnhancedFallbackService.generateFallbackInsights({
              current_role: 'Test',
              years_experience: '1-2',
              skill_development_interest: ['coding_tech'],
              career_values: ['growth'],
              work_style_preferences: ['remote'],
              biggest_obstacles: ['skill_gaps'],
              financial_readiness: 3,
              support_level: 3,
              risk_tolerance: 3,
              urgency_level: 3,
              skills_confidence: 50,
              desired_outcomes_work_life: 'Test',
              desired_outcomes_financial: 'Test',
              desired_outcomes_personal: 'Test',
              location: 'Test'
            }, {
              scores: {
                financialReadiness: 3,
                supportLevel: 3,
                riskTolerance: 3,
                urgencyLevel: 3,
                skillsConfidence: 50,
                readinessScore: 60
              },
              primaryMotivation: 'Test',
              topSkills: ['technical_programming'],
              biggestObstacles: ['skill_gaps'],
              recommendedTimeline: '6-12 months',
              keyRecommendations: ['Test'],
              careerPathSuggestions: [],
              careerPathAnalysis: [],
              overallSkillGaps: [],
              learningPriorities: ['technical_programming'],
              estimatedTransitionTime: '6-12 months'
            }, 'Health check')];
          case 6:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[56][6]++;
            cov_1as9fh3sbl().s[197]++;
            fallbackResult = _a.sent();
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[198]++;
            fallbackHealthy = fallbackResult.careerRecommendations.length > 0;
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[199]++;
            return [3 /*break*/, 8];
          case 7:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[56][7]++;
            cov_1as9fh3sbl().s[200]++;
            error_4 = _a.sent();
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[201]++;
            console.warn('Fallback service health check failed:', error_4);
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[202]++;
            return [3 /*break*/, 8];
          case 8:
            /* istanbul ignore next */
            cov_1as9fh3sbl().b[56][8]++;
            cov_1as9fh3sbl().s[203]++;
            if (
            /* istanbul ignore next */
            (cov_1as9fh3sbl().b[58][0]++, algorithmicHealthy) &&
            /* istanbul ignore next */
            (cov_1as9fh3sbl().b[58][1]++, fallbackHealthy)) {
              /* istanbul ignore next */
              cov_1as9fh3sbl().b[57][0]++;
              cov_1as9fh3sbl().s[204]++;
              overall = 'healthy';
            } else {
              /* istanbul ignore next */
              cov_1as9fh3sbl().b[57][1]++;
              cov_1as9fh3sbl().s[205]++;
              if (
              /* istanbul ignore next */
              (cov_1as9fh3sbl().b[60][0]++, fallbackHealthy) ||
              /* istanbul ignore next */
              (cov_1as9fh3sbl().b[60][1]++, basicHealthy)) {
                /* istanbul ignore next */
                cov_1as9fh3sbl().b[59][0]++;
                cov_1as9fh3sbl().s[206]++;
                overall = 'degraded';
              } else {
                /* istanbul ignore next */
                cov_1as9fh3sbl().b[59][1]++;
                cov_1as9fh3sbl().s[207]++;
                overall = 'critical';
              }
            }
            /* istanbul ignore next */
            cov_1as9fh3sbl().s[208]++;
            return [2 /*return*/, {
              algorithmic: algorithmicHealthy,
              fallback: fallbackHealthy,
              basic: basicHealthy,
              overall: overall
            }];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_1as9fh3sbl().s[209]++;
  AssessmentServiceIntegration.initialized = false;
  /* istanbul ignore next */
  cov_1as9fh3sbl().s[210]++;
  return AssessmentServiceIntegration;
}());
/* istanbul ignore next */
cov_1as9fh3sbl().s[211]++;
exports.AssessmentServiceIntegration = AssessmentServiceIntegration;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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