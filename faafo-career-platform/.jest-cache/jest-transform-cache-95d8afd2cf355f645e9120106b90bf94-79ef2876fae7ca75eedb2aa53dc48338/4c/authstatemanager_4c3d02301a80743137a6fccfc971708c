c528a6c3e0dc768ae8663b56bc7b94f3
"use strict";

/**
 * Unified Authentication State Manager
 * Ensures consistent authentication state across the application
 */
/* istanbul ignore next */
function cov_15d5esx45j() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/auth-state-manager.ts";
  var hash = "054c3615089354638bcd2b8cb9f0cf9db23bcf46";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/auth-state-manager.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 15
        },
        end: {
          line: 16,
          column: 1
        }
      },
      "1": {
        start: {
          line: 7,
          column: 4
        },
        end: {
          line: 14,
          column: 6
        }
      },
      "2": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 12,
          column: 9
        }
      },
      "3": {
        start: {
          line: 8,
          column: 24
        },
        end: {
          line: 8,
          column: 25
        }
      },
      "4": {
        start: {
          line: 8,
          column: 31
        },
        end: {
          line: 8,
          column: 47
        }
      },
      "5": {
        start: {
          line: 9,
          column: 12
        },
        end: {
          line: 9,
          column: 29
        }
      },
      "6": {
        start: {
          line: 10,
          column: 12
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "7": {
        start: {
          line: 10,
          column: 29
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "8": {
        start: {
          line: 11,
          column: 16
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "9": {
        start: {
          line: 13,
          column: 8
        },
        end: {
          line: 13,
          column: 17
        }
      },
      "10": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 43
        }
      },
      "11": {
        start: {
          line: 17,
          column: 16
        },
        end: {
          line: 25,
          column: 1
        }
      },
      "12": {
        start: {
          line: 18,
          column: 28
        },
        end: {
          line: 18,
          column: 110
        }
      },
      "13": {
        start: {
          line: 18,
          column: 91
        },
        end: {
          line: 18,
          column: 106
        }
      },
      "14": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 24,
          column: 7
        }
      },
      "15": {
        start: {
          line: 20,
          column: 36
        },
        end: {
          line: 20,
          column: 97
        }
      },
      "16": {
        start: {
          line: 20,
          column: 42
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "17": {
        start: {
          line: 20,
          column: 85
        },
        end: {
          line: 20,
          column: 95
        }
      },
      "18": {
        start: {
          line: 21,
          column: 35
        },
        end: {
          line: 21,
          column: 100
        }
      },
      "19": {
        start: {
          line: 21,
          column: 41
        },
        end: {
          line: 21,
          column: 73
        }
      },
      "20": {
        start: {
          line: 21,
          column: 88
        },
        end: {
          line: 21,
          column: 98
        }
      },
      "21": {
        start: {
          line: 22,
          column: 32
        },
        end: {
          line: 22,
          column: 116
        }
      },
      "22": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 78
        }
      },
      "23": {
        start: {
          line: 26,
          column: 18
        },
        end: {
          line: 52,
          column: 1
        }
      },
      "24": {
        start: {
          line: 27,
          column: 12
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "25": {
        start: {
          line: 27,
          column: 43
        },
        end: {
          line: 27,
          column: 68
        }
      },
      "26": {
        start: {
          line: 27,
          column: 57
        },
        end: {
          line: 27,
          column: 68
        }
      },
      "27": {
        start: {
          line: 27,
          column: 69
        },
        end: {
          line: 27,
          column: 81
        }
      },
      "28": {
        start: {
          line: 27,
          column: 119
        },
        end: {
          line: 27,
          column: 196
        }
      },
      "29": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 28,
          column: 160
        }
      },
      "30": {
        start: {
          line: 28,
          column: 141
        },
        end: {
          line: 28,
          column: 153
        }
      },
      "31": {
        start: {
          line: 29,
          column: 23
        },
        end: {
          line: 29,
          column: 68
        }
      },
      "32": {
        start: {
          line: 29,
          column: 45
        },
        end: {
          line: 29,
          column: 65
        }
      },
      "33": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 70
        }
      },
      "34": {
        start: {
          line: 31,
          column: 15
        },
        end: {
          line: 31,
          column: 70
        }
      },
      "35": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 49,
          column: 66
        }
      },
      "36": {
        start: {
          line: 32,
          column: 50
        },
        end: {
          line: 49,
          column: 66
        }
      },
      "37": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 169
        }
      },
      "38": {
        start: {
          line: 33,
          column: 160
        },
        end: {
          line: 33,
          column: 169
        }
      },
      "39": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 52
        }
      },
      "40": {
        start: {
          line: 34,
          column: 26
        },
        end: {
          line: 34,
          column: 52
        }
      },
      "41": {
        start: {
          line: 35,
          column: 12
        },
        end: {
          line: 47,
          column: 13
        }
      },
      "42": {
        start: {
          line: 36,
          column: 32
        },
        end: {
          line: 36,
          column: 39
        }
      },
      "43": {
        start: {
          line: 36,
          column: 40
        },
        end: {
          line: 36,
          column: 46
        }
      },
      "44": {
        start: {
          line: 37,
          column: 24
        },
        end: {
          line: 37,
          column: 34
        }
      },
      "45": {
        start: {
          line: 37,
          column: 35
        },
        end: {
          line: 37,
          column: 72
        }
      },
      "46": {
        start: {
          line: 38,
          column: 24
        },
        end: {
          line: 38,
          column: 34
        }
      },
      "47": {
        start: {
          line: 38,
          column: 35
        },
        end: {
          line: 38,
          column: 45
        }
      },
      "48": {
        start: {
          line: 38,
          column: 46
        },
        end: {
          line: 38,
          column: 55
        }
      },
      "49": {
        start: {
          line: 38,
          column: 56
        },
        end: {
          line: 38,
          column: 65
        }
      },
      "50": {
        start: {
          line: 39,
          column: 24
        },
        end: {
          line: 39,
          column: 41
        }
      },
      "51": {
        start: {
          line: 39,
          column: 42
        },
        end: {
          line: 39,
          column: 55
        }
      },
      "52": {
        start: {
          line: 39,
          column: 56
        },
        end: {
          line: 39,
          column: 65
        }
      },
      "53": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 128
        }
      },
      "54": {
        start: {
          line: 41,
          column: 110
        },
        end: {
          line: 41,
          column: 116
        }
      },
      "55": {
        start: {
          line: 41,
          column: 117
        },
        end: {
          line: 41,
          column: 126
        }
      },
      "56": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 106
        }
      },
      "57": {
        start: {
          line: 42,
          column: 81
        },
        end: {
          line: 42,
          column: 97
        }
      },
      "58": {
        start: {
          line: 42,
          column: 98
        },
        end: {
          line: 42,
          column: 104
        }
      },
      "59": {
        start: {
          line: 43,
          column: 20
        },
        end: {
          line: 43,
          column: 89
        }
      },
      "60": {
        start: {
          line: 43,
          column: 57
        },
        end: {
          line: 43,
          column: 72
        }
      },
      "61": {
        start: {
          line: 43,
          column: 73
        },
        end: {
          line: 43,
          column: 80
        }
      },
      "62": {
        start: {
          line: 43,
          column: 81
        },
        end: {
          line: 43,
          column: 87
        }
      },
      "63": {
        start: {
          line: 44,
          column: 20
        },
        end: {
          line: 44,
          column: 87
        }
      },
      "64": {
        start: {
          line: 44,
          column: 47
        },
        end: {
          line: 44,
          column: 62
        }
      },
      "65": {
        start: {
          line: 44,
          column: 63
        },
        end: {
          line: 44,
          column: 78
        }
      },
      "66": {
        start: {
          line: 44,
          column: 79
        },
        end: {
          line: 44,
          column: 85
        }
      },
      "67": {
        start: {
          line: 45,
          column: 20
        },
        end: {
          line: 45,
          column: 42
        }
      },
      "68": {
        start: {
          line: 45,
          column: 30
        },
        end: {
          line: 45,
          column: 42
        }
      },
      "69": {
        start: {
          line: 46,
          column: 20
        },
        end: {
          line: 46,
          column: 33
        }
      },
      "70": {
        start: {
          line: 46,
          column: 34
        },
        end: {
          line: 46,
          column: 43
        }
      },
      "71": {
        start: {
          line: 48,
          column: 12
        },
        end: {
          line: 48,
          column: 39
        }
      },
      "72": {
        start: {
          line: 49,
          column: 22
        },
        end: {
          line: 49,
          column: 34
        }
      },
      "73": {
        start: {
          line: 49,
          column: 35
        },
        end: {
          line: 49,
          column: 41
        }
      },
      "74": {
        start: {
          line: 49,
          column: 54
        },
        end: {
          line: 49,
          column: 64
        }
      },
      "75": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 50,
          column: 35
        }
      },
      "76": {
        start: {
          line: 50,
          column: 23
        },
        end: {
          line: 50,
          column: 35
        }
      },
      "77": {
        start: {
          line: 50,
          column: 36
        },
        end: {
          line: 50,
          column: 89
        }
      },
      "78": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 62
        }
      },
      "79": {
        start: {
          line: 54,
          column: 0
        },
        end: {
          line: 54,
          column: 50
        }
      },
      "80": {
        start: {
          line: 55,
          column: 0
        },
        end: {
          line: 55,
          column: 58
        }
      },
      "81": {
        start: {
          line: 56,
          column: 14
        },
        end: {
          line: 56,
          column: 40
        }
      },
      "82": {
        start: {
          line: 57,
          column: 48
        },
        end: {
          line: 493,
          column: 3
        }
      },
      "83": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 67,
          column: 10
        }
      },
      "84": {
        start: {
          line: 68,
          column: 8
        },
        end: {
          line: 68,
          column: 46
        }
      },
      "85": {
        start: {
          line: 69,
          column: 8
        },
        end: {
          line: 69,
          column: 35
        }
      },
      "86": {
        start: {
          line: 70,
          column: 8
        },
        end: {
          line: 70,
          column: 41
        }
      },
      "87": {
        start: {
          line: 71,
          column: 8
        },
        end: {
          line: 71,
          column: 53
        }
      },
      "88": {
        start: {
          line: 72,
          column: 8
        },
        end: {
          line: 72,
          column: 56
        }
      },
      "89": {
        start: {
          line: 73,
          column: 8
        },
        end: {
          line: 73,
          column: 52
        }
      },
      "90": {
        start: {
          line: 75,
          column: 8
        },
        end: {
          line: 75,
          column: 41
        }
      },
      "91": {
        start: {
          line: 76,
          column: 8
        },
        end: {
          line: 76,
          column: 39
        }
      },
      "92": {
        start: {
          line: 77,
          column: 8
        },
        end: {
          line: 77,
          column: 46
        }
      },
      "93": {
        start: {
          line: 78,
          column: 8
        },
        end: {
          line: 78,
          column: 33
        }
      },
      "94": {
        start: {
          line: 79,
          column: 8
        },
        end: {
          line: 79,
          column: 43
        }
      },
      "95": {
        start: {
          line: 84,
          column: 4
        },
        end: {
          line: 86,
          column: 6
        }
      },
      "96": {
        start: {
          line: 85,
          column: 8
        },
        end: {
          line: 85,
          column: 40
        }
      },
      "97": {
        start: {
          line: 90,
          column: 4
        },
        end: {
          line: 98,
          column: 6
        }
      },
      "98": {
        start: {
          line: 91,
          column: 20
        },
        end: {
          line: 91,
          column: 24
        }
      },
      "99": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 95,
          column: 9
        }
      },
      "100": {
        start: {
          line: 94,
          column: 12
        },
        end: {
          line: 94,
          column: 35
        }
      },
      "101": {
        start: {
          line: 96,
          column: 8
        },
        end: {
          line: 96,
          column: 37
        }
      },
      "102": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 97,
          column: 72
        }
      },
      "103": {
        start: {
          line: 97,
          column: 29
        },
        end: {
          line: 97,
          column: 69
        }
      },
      "104": {
        start: {
          line: 102,
          column: 4
        },
        end: {
          line: 109,
          column: 6
        }
      },
      "105": {
        start: {
          line: 103,
          column: 20
        },
        end: {
          line: 103,
          column: 24
        }
      },
      "106": {
        start: {
          line: 104,
          column: 8
        },
        end: {
          line: 106,
          column: 9
        }
      },
      "107": {
        start: {
          line: 105,
          column: 12
        },
        end: {
          line: 105,
          column: 19
        }
      },
      "108": {
        start: {
          line: 107,
          column: 8
        },
        end: {
          line: 107,
          column: 102
        }
      },
      "109": {
        start: {
          line: 107,
          column: 64
        },
        end: {
          line: 107,
          column: 98
        }
      },
      "110": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 108,
          column: 91
        }
      },
      "111": {
        start: {
          line: 108,
          column: 53
        },
        end: {
          line: 108,
          column: 87
        }
      },
      "112": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 123,
          column: 6
        }
      },
      "113": {
        start: {
          line: 114,
          column: 8
        },
        end: {
          line: 116,
          column: 9
        }
      },
      "114": {
        start: {
          line: 115,
          column: 12
        },
        end: {
          line: 115,
          column: 19
        }
      },
      "115": {
        start: {
          line: 117,
          column: 23
        },
        end: {
          line: 117,
          column: 47
        }
      },
      "116": {
        start: {
          line: 118,
          column: 8
        },
        end: {
          line: 118,
          column: 65
        }
      },
      "117": {
        start: {
          line: 120,
          column: 8
        },
        end: {
          line: 120,
          column: 31
        }
      },
      "118": {
        start: {
          line: 122,
          column: 8
        },
        end: {
          line: 122,
          column: 56
        }
      },
      "119": {
        start: {
          line: 127,
          column: 4
        },
        end: {
          line: 150,
          column: 6
        }
      },
      "120": {
        start: {
          line: 129,
          column: 8
        },
        end: {
          line: 130,
          column: 19
        }
      },
      "121": {
        start: {
          line: 130,
          column: 12
        },
        end: {
          line: 130,
          column: 19
        }
      },
      "122": {
        start: {
          line: 132,
          column: 32
        },
        end: {
          line: 134,
          column: 49
        }
      },
      "123": {
        start: {
          line: 135,
          column: 8
        },
        end: {
          line: 149,
          column: 9
        }
      },
      "124": {
        start: {
          line: 136,
          column: 12
        },
        end: {
          line: 148,
          column: 13
        }
      },
      "125": {
        start: {
          line: 137,
          column: 16
        },
        end: {
          line: 142,
          column: 20
        }
      },
      "126": {
        start: {
          line: 144,
          column: 16
        },
        end: {
          line: 144,
          column: 61
        }
      },
      "127": {
        start: {
          line: 147,
          column: 16
        },
        end: {
          line: 147,
          column: 79
        }
      },
      "128": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 208,
          column: 6
        }
      },
      "129": {
        start: {
          line: 155,
          column: 8
        },
        end: {
          line: 207,
          column: 11
        }
      },
      "130": {
        start: {
          line: 158,
          column: 12
        },
        end: {
          line: 206,
          column: 15
        }
      },
      "131": {
        start: {
          line: 159,
          column: 16
        },
        end: {
          line: 205,
          column: 17
        }
      },
      "132": {
        start: {
          line: 161,
          column: 24
        },
        end: {
          line: 161,
          column: 50
        }
      },
      "133": {
        start: {
          line: 162,
          column: 24
        },
        end: {
          line: 162,
          column: 72
        }
      },
      "134": {
        start: {
          line: 163,
          column: 24
        },
        end: {
          line: 163,
          column: 72
        }
      },
      "135": {
        start: {
          line: 165,
          column: 24
        },
        end: {
          line: 165,
          column: 44
        }
      },
      "136": {
        start: {
          line: 166,
          column: 24
        },
        end: {
          line: 189,
          column: 25
        }
      },
      "137": {
        start: {
          line: 167,
          column: 28
        },
        end: {
          line: 167,
          column: 117
        }
      },
      "138": {
        start: {
          line: 168,
          column: 28
        },
        end: {
          line: 176,
          column: 31
        }
      },
      "139": {
        start: {
          line: 177,
          column: 28
        },
        end: {
          line: 177,
          column: 54
        }
      },
      "140": {
        start: {
          line: 180,
          column: 28
        },
        end: {
          line: 188,
          column: 31
        }
      },
      "141": {
        start: {
          line: 190,
          column: 24
        },
        end: {
          line: 190,
          column: 48
        }
      },
      "142": {
        start: {
          line: 192,
          column: 24
        },
        end: {
          line: 192,
          column: 44
        }
      },
      "143": {
        start: {
          line: 193,
          column: 24
        },
        end: {
          line: 193,
          column: 79
        }
      },
      "144": {
        start: {
          line: 194,
          column: 24
        },
        end: {
          line: 202,
          column: 27
        }
      },
      "145": {
        start: {
          line: 203,
          column: 24
        },
        end: {
          line: 203,
          column: 48
        }
      },
      "146": {
        start: {
          line: 204,
          column: 28
        },
        end: {
          line: 204,
          column: 50
        }
      },
      "147": {
        start: {
          line: 212,
          column: 4
        },
        end: {
          line: 268,
          column: 6
        }
      },
      "148": {
        start: {
          line: 213,
          column: 8
        },
        end: {
          line: 267,
          column: 11
        }
      },
      "149": {
        start: {
          line: 215,
          column: 12
        },
        end: {
          line: 266,
          column: 15
        }
      },
      "150": {
        start: {
          line: 216,
          column: 16
        },
        end: {
          line: 265,
          column: 17
        }
      },
      "151": {
        start: {
          line: 218,
          column: 24
        },
        end: {
          line: 218,
          column: 52
        }
      },
      "152": {
        start: {
          line: 219,
          column: 24
        },
        end: {
          line: 219,
          column: 80
        }
      },
      "153": {
        start: {
          line: 219,
          column: 56
        },
        end: {
          line: 219,
          column: 80
        }
      },
      "154": {
        start: {
          line: 220,
          column: 24
        },
        end: {
          line: 220,
          column: 82
        }
      },
      "155": {
        start: {
          line: 222,
          column: 24
        },
        end: {
          line: 222,
          column: 34
        }
      },
      "156": {
        start: {
          line: 223,
          column: 24
        },
        end: {
          line: 223,
          column: 53
        }
      },
      "157": {
        start: {
          line: 224,
          column: 28
        },
        end: {
          line: 230,
          column: 28
        }
      },
      "158": {
        start: {
          line: 232,
          column: 24
        },
        end: {
          line: 232,
          column: 45
        }
      },
      "159": {
        start: {
          line: 233,
          column: 24
        },
        end: {
          line: 233,
          column: 67
        }
      },
      "160": {
        start: {
          line: 233,
          column: 43
        },
        end: {
          line: 233,
          column: 67
        }
      },
      "161": {
        start: {
          line: 234,
          column: 24
        },
        end: {
          line: 234,
          column: 80
        }
      },
      "162": {
        start: {
          line: 234,
          column: 56
        },
        end: {
          line: 234,
          column: 80
        }
      },
      "163": {
        start: {
          line: 235,
          column: 24
        },
        end: {
          line: 235,
          column: 82
        }
      },
      "164": {
        start: {
          line: 237,
          column: 24
        },
        end: {
          line: 237,
          column: 34
        }
      },
      "165": {
        start: {
          line: 238,
          column: 24
        },
        end: {
          line: 238,
          column: 53
        }
      },
      "166": {
        start: {
          line: 239,
          column: 28
        },
        end: {
          line: 239,
          column: 99
        }
      },
      "167": {
        start: {
          line: 240,
          column: 28
        },
        end: {
          line: 240,
          column: 66
        }
      },
      "168": {
        start: {
          line: 242,
          column: 24
        },
        end: {
          line: 242,
          column: 95
        }
      },
      "169": {
        start: {
          line: 243,
          column: 24
        },
        end: {
          line: 243,
          column: 70
        }
      },
      "170": {
        start: {
          line: 243,
          column: 46
        },
        end: {
          line: 243,
          column: 70
        }
      },
      "171": {
        start: {
          line: 244,
          column: 24
        },
        end: {
          line: 250,
          column: 27
        }
      },
      "172": {
        start: {
          line: 251,
          column: 24
        },
        end: {
          line: 251,
          column: 52
        }
      },
      "173": {
        start: {
          line: 252,
          column: 28
        },
        end: {
          line: 252,
          column: 96
        }
      },
      "174": {
        start: {
          line: 254,
          column: 24
        },
        end: {
          line: 254,
          column: 34
        }
      },
      "175": {
        start: {
          line: 255,
          column: 24
        },
        end: {
          line: 255,
          column: 53
        }
      },
      "176": {
        start: {
          line: 256,
          column: 29
        },
        end: {
          line: 256,
          column: 54
        }
      },
      "177": {
        start: {
          line: 258,
          column: 24
        },
        end: {
          line: 258,
          column: 44
        }
      },
      "178": {
        start: {
          line: 259,
          column: 24
        },
        end: {
          line: 259,
          column: 76
        }
      },
      "179": {
        start: {
          line: 260,
          column: 24
        },
        end: {
          line: 262,
          column: 27
        }
      },
      "180": {
        start: {
          line: 263,
          column: 24
        },
        end: {
          line: 263,
          column: 53
        }
      },
      "181": {
        start: {
          line: 264,
          column: 29
        },
        end: {
          line: 264,
          column: 51
        }
      },
      "182": {
        start: {
          line: 272,
          column: 4
        },
        end: {
          line: 282,
          column: 6
        }
      },
      "183": {
        start: {
          line: 273,
          column: 8
        },
        end: {
          line: 281,
          column: 11
        }
      },
      "184": {
        start: {
          line: 286,
          column: 4
        },
        end: {
          line: 293,
          column: 6
        }
      },
      "185": {
        start: {
          line: 287,
          column: 18
        },
        end: {
          line: 287,
          column: 28
        }
      },
      "186": {
        start: {
          line: 288,
          column: 8
        },
        end: {
          line: 288,
          column: 45
        }
      },
      "187": {
        start: {
          line: 290,
          column: 8
        },
        end: {
          line: 292,
          column: 9
        }
      },
      "188": {
        start: {
          line: 291,
          column: 12
        },
        end: {
          line: 291,
          column: 71
        }
      },
      "189": {
        start: {
          line: 297,
          column: 4
        },
        end: {
          line: 313,
          column: 6
        }
      },
      "190": {
        start: {
          line: 298,
          column: 27
        },
        end: {
          line: 298,
          column: 50
        }
      },
      "191": {
        start: {
          line: 299,
          column: 8
        },
        end: {
          line: 301,
          column: 9
        }
      },
      "192": {
        start: {
          line: 300,
          column: 12
        },
        end: {
          line: 300,
          column: 24
        }
      },
      "193": {
        start: {
          line: 302,
          column: 18
        },
        end: {
          line: 302,
          column: 28
        }
      },
      "194": {
        start: {
          line: 303,
          column: 32
        },
        end: {
          line: 303,
          column: 50
        }
      },
      "195": {
        start: {
          line: 305,
          column: 8
        },
        end: {
          line: 307,
          column: 9
        }
      },
      "196": {
        start: {
          line: 306,
          column: 12
        },
        end: {
          line: 306,
          column: 24
        }
      },
      "197": {
        start: {
          line: 309,
          column: 8
        },
        end: {
          line: 311,
          column: 9
        }
      },
      "198": {
        start: {
          line: 310,
          column: 12
        },
        end: {
          line: 310,
          column: 24
        }
      },
      "199": {
        start: {
          line: 312,
          column: 8
        },
        end: {
          line: 312,
          column: 21
        }
      },
      "200": {
        start: {
          line: 317,
          column: 4
        },
        end: {
          line: 356,
          column: 6
        }
      },
      "201": {
        start: {
          line: 318,
          column: 8
        },
        end: {
          line: 355,
          column: 11
        }
      },
      "202": {
        start: {
          line: 320,
          column: 12
        },
        end: {
          line: 354,
          column: 15
        }
      },
      "203": {
        start: {
          line: 321,
          column: 16
        },
        end: {
          line: 353,
          column: 17
        }
      },
      "204": {
        start: {
          line: 323,
          column: 24
        },
        end: {
          line: 323,
          column: 50
        }
      },
      "205": {
        start: {
          line: 324,
          column: 24
        },
        end: {
          line: 324,
          column: 61
        }
      },
      "206": {
        start: {
          line: 326,
          column: 24
        },
        end: {
          line: 326,
          column: 42
        }
      },
      "207": {
        start: {
          line: 328,
          column: 24
        },
        end: {
          line: 331,
          column: 25
        }
      },
      "208": {
        start: {
          line: 329,
          column: 28
        },
        end: {
          line: 329,
          column: 74
        }
      },
      "209": {
        start: {
          line: 330,
          column: 28
        },
        end: {
          line: 330,
          column: 71
        }
      },
      "210": {
        start: {
          line: 333,
          column: 24
        },
        end: {
          line: 336,
          column: 32
        }
      },
      "211": {
        start: {
          line: 339,
          column: 24
        },
        end: {
          line: 339,
          column: 34
        }
      },
      "212": {
        start: {
          line: 341,
          column: 24
        },
        end: {
          line: 343,
          column: 25
        }
      },
      "213": {
        start: {
          line: 342,
          column: 28
        },
        end: {
          line: 342,
          column: 61
        }
      },
      "214": {
        start: {
          line: 344,
          column: 24
        },
        end: {
          line: 344,
          column: 48
        }
      },
      "215": {
        start: {
          line: 346,
          column: 24
        },
        end: {
          line: 346,
          column: 44
        }
      },
      "216": {
        start: {
          line: 347,
          column: 24
        },
        end: {
          line: 347,
          column: 77
        }
      },
      "217": {
        start: {
          line: 348,
          column: 24
        },
        end: {
          line: 350,
          column: 27
        }
      },
      "218": {
        start: {
          line: 351,
          column: 24
        },
        end: {
          line: 351,
          column: 48
        }
      },
      "219": {
        start: {
          line: 352,
          column: 28
        },
        end: {
          line: 352,
          column: 50
        }
      },
      "220": {
        start: {
          line: 360,
          column: 4
        },
        end: {
          line: 398,
          column: 6
        }
      },
      "221": {
        start: {
          line: 361,
          column: 8
        },
        end: {
          line: 397,
          column: 11
        }
      },
      "222": {
        start: {
          line: 364,
          column: 12
        },
        end: {
          line: 396,
          column: 15
        }
      },
      "223": {
        start: {
          line: 365,
          column: 16
        },
        end: {
          line: 395,
          column: 17
        }
      },
      "224": {
        start: {
          line: 367,
          column: 24
        },
        end: {
          line: 367,
          column: 55
        }
      },
      "225": {
        start: {
          line: 368,
          column: 24
        },
        end: {
          line: 370,
          column: 25
        }
      },
      "226": {
        start: {
          line: 369,
          column: 28
        },
        end: {
          line: 369,
          column: 50
        }
      },
      "227": {
        start: {
          line: 371,
          column: 24
        },
        end: {
          line: 371,
          column: 37
        }
      },
      "228": {
        start: {
          line: 373,
          column: 24
        },
        end: {
          line: 373,
          column: 50
        }
      },
      "229": {
        start: {
          line: 374,
          column: 24
        },
        end: {
          line: 380,
          column: 32
        }
      },
      "230": {
        start: {
          line: 382,
          column: 24
        },
        end: {
          line: 382,
          column: 45
        }
      },
      "231": {
        start: {
          line: 383,
          column: 24
        },
        end: {
          line: 383,
          column: 66
        }
      },
      "232": {
        start: {
          line: 383,
          column: 42
        },
        end: {
          line: 383,
          column: 66
        }
      },
      "233": {
        start: {
          line: 384,
          column: 24
        },
        end: {
          line: 384,
          column: 62
        }
      },
      "234": {
        start: {
          line: 386,
          column: 24
        },
        end: {
          line: 386,
          column: 54
        }
      },
      "235": {
        start: {
          line: 387,
          column: 24
        },
        end: {
          line: 387,
          column: 69
        }
      },
      "236": {
        start: {
          line: 388,
          column: 24
        },
        end: {
          line: 388,
          column: 37
        }
      },
      "237": {
        start: {
          line: 389,
          column: 28
        },
        end: {
          line: 389,
          column: 52
        }
      },
      "238": {
        start: {
          line: 391,
          column: 24
        },
        end: {
          line: 391,
          column: 44
        }
      },
      "239": {
        start: {
          line: 392,
          column: 24
        },
        end: {
          line: 392,
          column: 79
        }
      },
      "240": {
        start: {
          line: 393,
          column: 24
        },
        end: {
          line: 393,
          column: 48
        }
      },
      "241": {
        start: {
          line: 394,
          column: 28
        },
        end: {
          line: 394,
          column: 50
        }
      },
      "242": {
        start: {
          line: 402,
          column: 4
        },
        end: {
          line: 449,
          column: 6
        }
      },
      "243": {
        start: {
          line: 403,
          column: 20
        },
        end: {
          line: 403,
          column: 24
        }
      },
      "244": {
        start: {
          line: 405,
          column: 8
        },
        end: {
          line: 407,
          column: 9
        }
      },
      "245": {
        start: {
          line: 406,
          column: 12
        },
        end: {
          line: 406,
          column: 19
        }
      },
      "246": {
        start: {
          line: 409,
          column: 8
        },
        end: {
          line: 409,
          column: 28
        }
      },
      "247": {
        start: {
          line: 411,
          column: 8
        },
        end: {
          line: 418,
          column: 40
        }
      },
      "248": {
        start: {
          line: 412,
          column: 12
        },
        end: {
          line: 414,
          column: 13
        }
      },
      "249": {
        start: {
          line: 413,
          column: 16
        },
        end: {
          line: 413,
          column: 23
        }
      },
      "250": {
        start: {
          line: 415,
          column: 12
        },
        end: {
          line: 417,
          column: 13
        }
      },
      "251": {
        start: {
          line: 416,
          column: 16
        },
        end: {
          line: 416,
          column: 40
        }
      },
      "252": {
        start: {
          line: 420,
          column: 8
        },
        end: {
          line: 448,
          column: 9
        }
      },
      "253": {
        start: {
          line: 421,
          column: 12
        },
        end: {
          line: 430,
          column: 14
        }
      },
      "254": {
        start: {
          line: 422,
          column: 16
        },
        end: {
          line: 423,
          column: 27
        }
      },
      "255": {
        start: {
          line: 423,
          column: 20
        },
        end: {
          line: 423,
          column: 27
        }
      },
      "256": {
        start: {
          line: 424,
          column: 16
        },
        end: {
          line: 429,
          column: 17
        }
      },
      "257": {
        start: {
          line: 425,
          column: 39
        },
        end: {
          line: 425,
          column: 87
        }
      },
      "258": {
        start: {
          line: 426,
          column: 20
        },
        end: {
          line: 428,
          column: 21
        }
      },
      "259": {
        start: {
          line: 427,
          column: 24
        },
        end: {
          line: 427,
          column: 71
        }
      },
      "260": {
        start: {
          line: 431,
          column: 12
        },
        end: {
          line: 431,
          column: 74
        }
      },
      "261": {
        start: {
          line: 433,
          column: 12
        },
        end: {
          line: 439,
          column: 14
        }
      },
      "262": {
        start: {
          line: 434,
          column: 16
        },
        end: {
          line: 435,
          column: 27
        }
      },
      "263": {
        start: {
          line: 435,
          column: 20
        },
        end: {
          line: 435,
          column: 27
        }
      },
      "264": {
        start: {
          line: 436,
          column: 16
        },
        end: {
          line: 438,
          column: 17
        }
      },
      "265": {
        start: {
          line: 437,
          column: 20
        },
        end: {
          line: 437,
          column: 41
        }
      },
      "266": {
        start: {
          line: 440,
          column: 12
        },
        end: {
          line: 440,
          column: 70
        }
      },
      "267": {
        start: {
          line: 442,
          column: 12
        },
        end: {
          line: 446,
          column: 14
        }
      },
      "268": {
        start: {
          line: 443,
          column: 16
        },
        end: {
          line: 444,
          column: 27
        }
      },
      "269": {
        start: {
          line: 444,
          column: 20
        },
        end: {
          line: 444,
          column: 27
        }
      },
      "270": {
        start: {
          line: 445,
          column: 16
        },
        end: {
          line: 445,
          column: 43
        }
      },
      "271": {
        start: {
          line: 447,
          column: 12
        },
        end: {
          line: 447,
          column: 84
        }
      },
      "272": {
        start: {
          line: 453,
          column: 4
        },
        end: {
          line: 455,
          column: 6
        }
      },
      "273": {
        start: {
          line: 454,
          column: 8
        },
        end: {
          line: 454,
          column: 40
        }
      },
      "274": {
        start: {
          line: 459,
          column: 4
        },
        end: {
          line: 461,
          column: 6
        }
      },
      "275": {
        start: {
          line: 460,
          column: 8
        },
        end: {
          line: 460,
          column: 40
        }
      },
      "276": {
        start: {
          line: 465,
          column: 4
        },
        end: {
          line: 491,
          column: 6
        }
      },
      "277": {
        start: {
          line: 467,
          column: 8
        },
        end: {
          line: 467,
          column: 32
        }
      },
      "278": {
        start: {
          line: 469,
          column: 8
        },
        end: {
          line: 472,
          column: 9
        }
      },
      "279": {
        start: {
          line: 470,
          column: 12
        },
        end: {
          line: 470,
          column: 53
        }
      },
      "280": {
        start: {
          line: 471,
          column: 12
        },
        end: {
          line: 471,
          column: 45
        }
      },
      "281": {
        start: {
          line: 474,
          column: 8
        },
        end: {
          line: 487,
          column: 9
        }
      },
      "282": {
        start: {
          line: 475,
          column: 12
        },
        end: {
          line: 478,
          column: 13
        }
      },
      "283": {
        start: {
          line: 476,
          column: 16
        },
        end: {
          line: 476,
          column: 81
        }
      },
      "284": {
        start: {
          line: 477,
          column: 16
        },
        end: {
          line: 477,
          column: 49
        }
      },
      "285": {
        start: {
          line: 479,
          column: 12
        },
        end: {
          line: 482,
          column: 13
        }
      },
      "286": {
        start: {
          line: 480,
          column: 16
        },
        end: {
          line: 480,
          column: 77
        }
      },
      "287": {
        start: {
          line: 481,
          column: 16
        },
        end: {
          line: 481,
          column: 47
        }
      },
      "288": {
        start: {
          line: 483,
          column: 12
        },
        end: {
          line: 486,
          column: 13
        }
      },
      "289": {
        start: {
          line: 484,
          column: 16
        },
        end: {
          line: 484,
          column: 91
        }
      },
      "290": {
        start: {
          line: 485,
          column: 16
        },
        end: {
          line: 485,
          column: 54
        }
      },
      "291": {
        start: {
          line: 489,
          column: 8
        },
        end: {
          line: 489,
          column: 31
        }
      },
      "292": {
        start: {
          line: 490,
          column: 8
        },
        end: {
          line: 490,
          column: 42
        }
      },
      "293": {
        start: {
          line: 492,
          column: 4
        },
        end: {
          line: 492,
          column: 38
        }
      },
      "294": {
        start: {
          line: 495,
          column: 23
        },
        end: {
          line: 495,
          column: 27
        }
      },
      "295": {
        start: {
          line: 500,
          column: 4
        },
        end: {
          line: 502,
          column: 5
        }
      },
      "296": {
        start: {
          line: 501,
          column: 8
        },
        end: {
          line: 501,
          column: 60
        }
      },
      "297": {
        start: {
          line: 503,
          column: 4
        },
        end: {
          line: 503,
          column: 28
        }
      },
      "298": {
        start: {
          line: 510,
          column: 4
        },
        end: {
          line: 513,
          column: 5
        }
      },
      "299": {
        start: {
          line: 511,
          column: 8
        },
        end: {
          line: 511,
          column: 35
        }
      },
      "300": {
        start: {
          line: 512,
          column: 8
        },
        end: {
          line: 512,
          column: 32
        }
      },
      "301": {
        start: {
          line: 516,
          column: 0
        },
        end: {
          line: 524,
          column: 1
        }
      },
      "302": {
        start: {
          line: 517,
          column: 4
        },
        end: {
          line: 519,
          column: 7
        }
      },
      "303": {
        start: {
          line: 518,
          column: 8
        },
        end: {
          line: 518,
          column: 34
        }
      },
      "304": {
        start: {
          line: 521,
          column: 4
        },
        end: {
          line: 523,
          column: 7
        }
      },
      "305": {
        start: {
          line: 522,
          column: 8
        },
        end: {
          line: 522,
          column: 34
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 42
          },
          end: {
            line: 6,
            column: 43
          }
        },
        loc: {
          start: {
            line: 6,
            column: 54
          },
          end: {
            line: 16,
            column: 1
          }
        },
        line: 6
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 33
          }
        },
        loc: {
          start: {
            line: 7,
            column: 44
          },
          end: {
            line: 14,
            column: 5
          }
        },
        line: 7
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 17,
            column: 44
          },
          end: {
            line: 17,
            column: 45
          }
        },
        loc: {
          start: {
            line: 17,
            column: 89
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 17
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 18,
            column: 13
          },
          end: {
            line: 18,
            column: 18
          }
        },
        loc: {
          start: {
            line: 18,
            column: 26
          },
          end: {
            line: 18,
            column: 112
          }
        },
        line: 18
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 71
          }
        },
        loc: {
          start: {
            line: 18,
            column: 89
          },
          end: {
            line: 18,
            column: 108
          }
        },
        line: 18
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 19,
            column: 36
          },
          end: {
            line: 19,
            column: 37
          }
        },
        loc: {
          start: {
            line: 19,
            column: 63
          },
          end: {
            line: 24,
            column: 5
          }
        },
        line: 19
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 20,
            column: 17
          },
          end: {
            line: 20,
            column: 26
          }
        },
        loc: {
          start: {
            line: 20,
            column: 34
          },
          end: {
            line: 20,
            column: 99
          }
        },
        line: 20
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 21,
            column: 17
          },
          end: {
            line: 21,
            column: 25
          }
        },
        loc: {
          start: {
            line: 21,
            column: 33
          },
          end: {
            line: 21,
            column: 102
          }
        },
        line: 21
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 22,
            column: 17
          },
          end: {
            line: 22,
            column: 21
          }
        },
        loc: {
          start: {
            line: 22,
            column: 30
          },
          end: {
            line: 22,
            column: 118
          }
        },
        line: 22
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 26,
            column: 48
          },
          end: {
            line: 26,
            column: 49
          }
        },
        loc: {
          start: {
            line: 26,
            column: 73
          },
          end: {
            line: 52,
            column: 1
          }
        },
        line: 26
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 27,
            column: 30
          },
          end: {
            line: 27,
            column: 31
          }
        },
        loc: {
          start: {
            line: 27,
            column: 41
          },
          end: {
            line: 27,
            column: 83
          }
        },
        line: 27
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 28,
            column: 128
          },
          end: {
            line: 28,
            column: 129
          }
        },
        loc: {
          start: {
            line: 28,
            column: 139
          },
          end: {
            line: 28,
            column: 155
          }
        },
        line: 28
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 29,
            column: 13
          },
          end: {
            line: 29,
            column: 17
          }
        },
        loc: {
          start: {
            line: 29,
            column: 21
          },
          end: {
            line: 29,
            column: 70
          }
        },
        line: 29
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 29,
            column: 30
          },
          end: {
            line: 29,
            column: 31
          }
        },
        loc: {
          start: {
            line: 29,
            column: 43
          },
          end: {
            line: 29,
            column: 67
          }
        },
        line: 29
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 30,
            column: 13
          },
          end: {
            line: 30,
            column: 17
          }
        },
        loc: {
          start: {
            line: 30,
            column: 22
          },
          end: {
            line: 51,
            column: 5
          }
        },
        line: 30
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 57,
            column: 48
          },
          end: {
            line: 57,
            column: 49
          }
        },
        loc: {
          start: {
            line: 57,
            column: 60
          },
          end: {
            line: 493,
            column: 1
          }
        },
        line: 57
      },
      "16": {
        name: "AuthenticationStateManager",
        decl: {
          start: {
            line: 58,
            column: 13
          },
          end: {
            line: 58,
            column: 39
          }
        },
        loc: {
          start: {
            line: 58,
            column: 42
          },
          end: {
            line: 80,
            column: 5
          }
        },
        line: 58
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 84,
            column: 52
          },
          end: {
            line: 84,
            column: 53
          }
        },
        loc: {
          start: {
            line: 84,
            column: 64
          },
          end: {
            line: 86,
            column: 5
          }
        },
        line: 84
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 90,
            column: 53
          },
          end: {
            line: 90,
            column: 54
          }
        },
        loc: {
          start: {
            line: 90,
            column: 73
          },
          end: {
            line: 98,
            column: 5
          }
        },
        line: 90
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 94,
            column: 19
          },
          end: {
            line: 94,
            column: 20
          }
        },
        loc: {
          start: {
            line: 94,
            column: 31
          },
          end: {
            line: 94,
            column: 34
          }
        },
        line: 94
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 97,
            column: 15
          },
          end: {
            line: 97,
            column: 16
          }
        },
        loc: {
          start: {
            line: 97,
            column: 27
          },
          end: {
            line: 97,
            column: 71
          }
        },
        line: 97
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 102,
            column: 59
          },
          end: {
            line: 102,
            column: 60
          }
        },
        loc: {
          start: {
            line: 102,
            column: 71
          },
          end: {
            line: 109,
            column: 5
          }
        },
        line: 102
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 107,
            column: 42
          },
          end: {
            line: 107,
            column: 43
          }
        },
        loc: {
          start: {
            line: 107,
            column: 62
          },
          end: {
            line: 107,
            column: 100
          }
        },
        line: 107
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 108,
            column: 31
          },
          end: {
            line: 108,
            column: 32
          }
        },
        loc: {
          start: {
            line: 108,
            column: 51
          },
          end: {
            line: 108,
            column: 89
          }
        },
        line: 108
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 113,
            column: 52
          },
          end: {
            line: 113,
            column: 53
          }
        },
        loc: {
          start: {
            line: 113,
            column: 71
          },
          end: {
            line: 123,
            column: 5
          }
        },
        line: 113
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 127,
            column: 64
          },
          end: {
            line: 127,
            column: 65
          }
        },
        loc: {
          start: {
            line: 127,
            column: 94
          },
          end: {
            line: 150,
            column: 5
          }
        },
        line: 127
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 154,
            column: 56
          },
          end: {
            line: 154,
            column: 57
          }
        },
        loc: {
          start: {
            line: 154,
            column: 68
          },
          end: {
            line: 208,
            column: 5
          }
        },
        line: 154
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 155,
            column: 48
          },
          end: {
            line: 155,
            column: 49
          }
        },
        loc: {
          start: {
            line: 155,
            column: 60
          },
          end: {
            line: 207,
            column: 9
          }
        },
        line: 155
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 158,
            column: 37
          },
          end: {
            line: 158,
            column: 38
          }
        },
        loc: {
          start: {
            line: 158,
            column: 51
          },
          end: {
            line: 206,
            column: 13
          }
        },
        line: 158
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 212,
            column: 59
          },
          end: {
            line: 212,
            column: 60
          }
        },
        loc: {
          start: {
            line: 212,
            column: 71
          },
          end: {
            line: 268,
            column: 5
          }
        },
        line: 212
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 213,
            column: 48
          },
          end: {
            line: 213,
            column: 49
          }
        },
        loc: {
          start: {
            line: 213,
            column: 60
          },
          end: {
            line: 267,
            column: 9
          }
        },
        line: 213
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 215,
            column: 37
          },
          end: {
            line: 215,
            column: 38
          }
        },
        loc: {
          start: {
            line: 215,
            column: 51
          },
          end: {
            line: 266,
            column: 13
          }
        },
        line: 215
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 272,
            column: 54
          },
          end: {
            line: 272,
            column: 55
          }
        },
        loc: {
          start: {
            line: 272,
            column: 66
          },
          end: {
            line: 282,
            column: 5
          }
        },
        line: 272
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 286,
            column: 62
          },
          end: {
            line: 286,
            column: 63
          }
        },
        loc: {
          start: {
            line: 286,
            column: 74
          },
          end: {
            line: 293,
            column: 5
          }
        },
        line: 286
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 297,
            column: 62
          },
          end: {
            line: 297,
            column: 63
          }
        },
        loc: {
          start: {
            line: 297,
            column: 74
          },
          end: {
            line: 313,
            column: 5
          }
        },
        line: 297
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 317,
            column: 55
          },
          end: {
            line: 317,
            column: 56
          }
        },
        loc: {
          start: {
            line: 317,
            column: 73
          },
          end: {
            line: 356,
            column: 5
          }
        },
        line: 317
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 318,
            column: 48
          },
          end: {
            line: 318,
            column: 49
          }
        },
        loc: {
          start: {
            line: 318,
            column: 60
          },
          end: {
            line: 355,
            column: 9
          }
        },
        line: 318
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 320,
            column: 37
          },
          end: {
            line: 320,
            column: 38
          }
        },
        loc: {
          start: {
            line: 320,
            column: 51
          },
          end: {
            line: 354,
            column: 13
          }
        },
        line: 320
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 360,
            column: 61
          },
          end: {
            line: 360,
            column: 62
          }
        },
        loc: {
          start: {
            line: 360,
            column: 73
          },
          end: {
            line: 398,
            column: 5
          }
        },
        line: 360
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 361,
            column: 48
          },
          end: {
            line: 361,
            column: 49
          }
        },
        loc: {
          start: {
            line: 361,
            column: 60
          },
          end: {
            line: 397,
            column: 9
          }
        },
        line: 361
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 364,
            column: 37
          },
          end: {
            line: 364,
            column: 38
          }
        },
        loc: {
          start: {
            line: 364,
            column: 51
          },
          end: {
            line: 396,
            column: 13
          }
        },
        line: 364
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 402,
            column: 71
          },
          end: {
            line: 402,
            column: 72
          }
        },
        loc: {
          start: {
            line: 402,
            column: 83
          },
          end: {
            line: 449,
            column: 5
          }
        },
        line: 402
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 411,
            column: 48
          },
          end: {
            line: 411,
            column: 49
          }
        },
        loc: {
          start: {
            line: 411,
            column: 60
          },
          end: {
            line: 418,
            column: 9
          }
        },
        line: 411
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 421,
            column: 40
          },
          end: {
            line: 421,
            column: 41
          }
        },
        loc: {
          start: {
            line: 421,
            column: 57
          },
          end: {
            line: 430,
            column: 13
          }
        },
        line: 421
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 433,
            column: 38
          },
          end: {
            line: 433,
            column: 39
          }
        },
        loc: {
          start: {
            line: 433,
            column: 50
          },
          end: {
            line: 439,
            column: 13
          }
        },
        line: 433
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 442,
            column: 45
          },
          end: {
            line: 442,
            column: 46
          }
        },
        loc: {
          start: {
            line: 442,
            column: 57
          },
          end: {
            line: 446,
            column: 13
          }
        },
        line: 442
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 453,
            column: 66
          },
          end: {
            line: 453,
            column: 67
          }
        },
        loc: {
          start: {
            line: 453,
            column: 86
          },
          end: {
            line: 455,
            column: 5
          }
        },
        line: 453
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 459,
            column: 69
          },
          end: {
            line: 459,
            column: 70
          }
        },
        loc: {
          start: {
            line: 459,
            column: 89
          },
          end: {
            line: 461,
            column: 5
          }
        },
        line: 459
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 465,
            column: 51
          },
          end: {
            line: 465,
            column: 52
          }
        },
        loc: {
          start: {
            line: 465,
            column: 63
          },
          end: {
            line: 491,
            column: 5
          }
        },
        line: 465
      },
      "49": {
        name: "getAuthStateManager",
        decl: {
          start: {
            line: 499,
            column: 9
          },
          end: {
            line: 499,
            column: 28
          }
        },
        loc: {
          start: {
            line: 499,
            column: 31
          },
          end: {
            line: 504,
            column: 1
          }
        },
        line: 499
      },
      "50": {
        name: "destroyAuthStateManager",
        decl: {
          start: {
            line: 509,
            column: 9
          },
          end: {
            line: 509,
            column: 32
          }
        },
        loc: {
          start: {
            line: 509,
            column: 35
          },
          end: {
            line: 514,
            column: 1
          }
        },
        line: 509
      },
      "51": {
        name: "(anonymous_51)",
        decl: {
          start: {
            line: 517,
            column: 44
          },
          end: {
            line: 517,
            column: 45
          }
        },
        loc: {
          start: {
            line: 517,
            column: 56
          },
          end: {
            line: 519,
            column: 5
          }
        },
        line: 517
      },
      "52": {
        name: "(anonymous_52)",
        decl: {
          start: {
            line: 521,
            column: 40
          },
          end: {
            line: 521,
            column: 41
          }
        },
        loc: {
          start: {
            line: 521,
            column: 52
          },
          end: {
            line: 523,
            column: 5
          }
        },
        line: 521
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 15
          },
          end: {
            line: 16,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 16
          },
          end: {
            line: 6,
            column: 20
          }
        }, {
          start: {
            line: 6,
            column: 24
          },
          end: {
            line: 6,
            column: 37
          }
        }, {
          start: {
            line: 6,
            column: 42
          },
          end: {
            line: 16,
            column: 1
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 14,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 14,
            column: 5
          }
        }],
        line: 7
      },
      "2": {
        loc: {
          start: {
            line: 10,
            column: 29
          },
          end: {
            line: 11,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 10,
            column: 29
          },
          end: {
            line: 11,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 10
      },
      "3": {
        loc: {
          start: {
            line: 17,
            column: 16
          },
          end: {
            line: 25,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 21
          }
        }, {
          start: {
            line: 17,
            column: 25
          },
          end: {
            line: 17,
            column: 39
          }
        }, {
          start: {
            line: 17,
            column: 44
          },
          end: {
            line: 25,
            column: 1
          }
        }],
        line: 17
      },
      "4": {
        loc: {
          start: {
            line: 18,
            column: 35
          },
          end: {
            line: 18,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 56
          },
          end: {
            line: 18,
            column: 61
          }
        }, {
          start: {
            line: 18,
            column: 64
          },
          end: {
            line: 18,
            column: 109
          }
        }],
        line: 18
      },
      "5": {
        loc: {
          start: {
            line: 19,
            column: 16
          },
          end: {
            line: 19,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 16
          },
          end: {
            line: 19,
            column: 17
          }
        }, {
          start: {
            line: 19,
            column: 22
          },
          end: {
            line: 19,
            column: 33
          }
        }],
        line: 19
      },
      "6": {
        loc: {
          start: {
            line: 22,
            column: 32
          },
          end: {
            line: 22,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 46
          },
          end: {
            line: 22,
            column: 67
          }
        }, {
          start: {
            line: 22,
            column: 70
          },
          end: {
            line: 22,
            column: 115
          }
        }],
        line: 22
      },
      "7": {
        loc: {
          start: {
            line: 23,
            column: 51
          },
          end: {
            line: 23,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 23,
            column: 51
          },
          end: {
            line: 23,
            column: 61
          }
        }, {
          start: {
            line: 23,
            column: 65
          },
          end: {
            line: 23,
            column: 67
          }
        }],
        line: 23
      },
      "8": {
        loc: {
          start: {
            line: 26,
            column: 18
          },
          end: {
            line: 52,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 19
          },
          end: {
            line: 26,
            column: 23
          }
        }, {
          start: {
            line: 26,
            column: 27
          },
          end: {
            line: 26,
            column: 43
          }
        }, {
          start: {
            line: 26,
            column: 48
          },
          end: {
            line: 52,
            column: 1
          }
        }],
        line: 26
      },
      "9": {
        loc: {
          start: {
            line: 27,
            column: 43
          },
          end: {
            line: 27,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 43
          },
          end: {
            line: 27,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "10": {
        loc: {
          start: {
            line: 27,
            column: 134
          },
          end: {
            line: 27,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 27,
            column: 167
          },
          end: {
            line: 27,
            column: 175
          }
        }, {
          start: {
            line: 27,
            column: 178
          },
          end: {
            line: 27,
            column: 184
          }
        }],
        line: 27
      },
      "11": {
        loc: {
          start: {
            line: 28,
            column: 74
          },
          end: {
            line: 28,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 74
          },
          end: {
            line: 28,
            column: 102
          }
        }, {
          start: {
            line: 28,
            column: 107
          },
          end: {
            line: 28,
            column: 155
          }
        }],
        line: 28
      },
      "12": {
        loc: {
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "13": {
        loc: {
          start: {
            line: 32,
            column: 15
          },
          end: {
            line: 32,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 15
          },
          end: {
            line: 32,
            column: 16
          }
        }, {
          start: {
            line: 32,
            column: 21
          },
          end: {
            line: 32,
            column: 44
          }
        }],
        line: 32
      },
      "14": {
        loc: {
          start: {
            line: 32,
            column: 28
          },
          end: {
            line: 32,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 28
          },
          end: {
            line: 32,
            column: 33
          }
        }, {
          start: {
            line: 32,
            column: 38
          },
          end: {
            line: 32,
            column: 43
          }
        }],
        line: 32
      },
      "15": {
        loc: {
          start: {
            line: 33,
            column: 12
          },
          end: {
            line: 33,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 12
          },
          end: {
            line: 33,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "16": {
        loc: {
          start: {
            line: 33,
            column: 23
          },
          end: {
            line: 33,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 23
          },
          end: {
            line: 33,
            column: 24
          }
        }, {
          start: {
            line: 33,
            column: 29
          },
          end: {
            line: 33,
            column: 125
          }
        }, {
          start: {
            line: 33,
            column: 130
          },
          end: {
            line: 33,
            column: 158
          }
        }],
        line: 33
      },
      "17": {
        loc: {
          start: {
            line: 33,
            column: 33
          },
          end: {
            line: 33,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 33,
            column: 45
          },
          end: {
            line: 33,
            column: 56
          }
        }, {
          start: {
            line: 33,
            column: 59
          },
          end: {
            line: 33,
            column: 125
          }
        }],
        line: 33
      },
      "18": {
        loc: {
          start: {
            line: 33,
            column: 59
          },
          end: {
            line: 33,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 33,
            column: 67
          },
          end: {
            line: 33,
            column: 116
          }
        }, {
          start: {
            line: 33,
            column: 119
          },
          end: {
            line: 33,
            column: 125
          }
        }],
        line: 33
      },
      "19": {
        loc: {
          start: {
            line: 33,
            column: 67
          },
          end: {
            line: 33,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 67
          },
          end: {
            line: 33,
            column: 77
          }
        }, {
          start: {
            line: 33,
            column: 82
          },
          end: {
            line: 33,
            column: 115
          }
        }],
        line: 33
      },
      "20": {
        loc: {
          start: {
            line: 33,
            column: 82
          },
          end: {
            line: 33,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 83
          },
          end: {
            line: 33,
            column: 98
          }
        }, {
          start: {
            line: 33,
            column: 103
          },
          end: {
            line: 33,
            column: 112
          }
        }],
        line: 33
      },
      "21": {
        loc: {
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "22": {
        loc: {
          start: {
            line: 35,
            column: 12
          },
          end: {
            line: 47,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 36,
            column: 23
          }
        }, {
          start: {
            line: 36,
            column: 24
          },
          end: {
            line: 36,
            column: 46
          }
        }, {
          start: {
            line: 37,
            column: 16
          },
          end: {
            line: 37,
            column: 72
          }
        }, {
          start: {
            line: 38,
            column: 16
          },
          end: {
            line: 38,
            column: 65
          }
        }, {
          start: {
            line: 39,
            column: 16
          },
          end: {
            line: 39,
            column: 65
          }
        }, {
          start: {
            line: 40,
            column: 16
          },
          end: {
            line: 46,
            column: 43
          }
        }],
        line: 35
      },
      "23": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "24": {
        loc: {
          start: {
            line: 41,
            column: 24
          },
          end: {
            line: 41,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 41,
            column: 24
          },
          end: {
            line: 41,
            column: 74
          }
        }, {
          start: {
            line: 41,
            column: 79
          },
          end: {
            line: 41,
            column: 90
          }
        }, {
          start: {
            line: 41,
            column: 94
          },
          end: {
            line: 41,
            column: 105
          }
        }],
        line: 41
      },
      "25": {
        loc: {
          start: {
            line: 41,
            column: 42
          },
          end: {
            line: 41,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 41,
            column: 42
          },
          end: {
            line: 41,
            column: 54
          }
        }, {
          start: {
            line: 41,
            column: 58
          },
          end: {
            line: 41,
            column: 73
          }
        }],
        line: 41
      },
      "26": {
        loc: {
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "27": {
        loc: {
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 35
          }
        }, {
          start: {
            line: 42,
            column: 40
          },
          end: {
            line: 42,
            column: 42
          }
        }, {
          start: {
            line: 42,
            column: 47
          },
          end: {
            line: 42,
            column: 59
          }
        }, {
          start: {
            line: 42,
            column: 63
          },
          end: {
            line: 42,
            column: 75
          }
        }],
        line: 42
      },
      "28": {
        loc: {
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "29": {
        loc: {
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 43,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 43,
            column: 35
          }
        }, {
          start: {
            line: 43,
            column: 39
          },
          end: {
            line: 43,
            column: 53
          }
        }],
        line: 43
      },
      "30": {
        loc: {
          start: {
            line: 44,
            column: 20
          },
          end: {
            line: 44,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 20
          },
          end: {
            line: 44,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "31": {
        loc: {
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 25
          }
        }, {
          start: {
            line: 44,
            column: 29
          },
          end: {
            line: 44,
            column: 43
          }
        }],
        line: 44
      },
      "32": {
        loc: {
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 45
      },
      "33": {
        loc: {
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "34": {
        loc: {
          start: {
            line: 50,
            column: 52
          },
          end: {
            line: 50,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 50,
            column: 60
          },
          end: {
            line: 50,
            column: 65
          }
        }, {
          start: {
            line: 50,
            column: 68
          },
          end: {
            line: 50,
            column: 74
          }
        }],
        line: 50
      },
      "35": {
        loc: {
          start: {
            line: 92,
            column: 8
          },
          end: {
            line: 95,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 92,
            column: 8
          },
          end: {
            line: 95,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 92
      },
      "36": {
        loc: {
          start: {
            line: 104,
            column: 8
          },
          end: {
            line: 106,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 104,
            column: 8
          },
          end: {
            line: 106,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 104
      },
      "37": {
        loc: {
          start: {
            line: 114,
            column: 8
          },
          end: {
            line: 116,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 114,
            column: 8
          },
          end: {
            line: 116,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 114
      },
      "38": {
        loc: {
          start: {
            line: 129,
            column: 8
          },
          end: {
            line: 130,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 129,
            column: 8
          },
          end: {
            line: 130,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 129
      },
      "39": {
        loc: {
          start: {
            line: 132,
            column: 32
          },
          end: {
            line: 134,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 132,
            column: 32
          },
          end: {
            line: 132,
            column: 85
          }
        }, {
          start: {
            line: 133,
            column: 12
          },
          end: {
            line: 133,
            column: 147
          }
        }, {
          start: {
            line: 134,
            column: 12
          },
          end: {
            line: 134,
            column: 49
          }
        }],
        line: 132
      },
      "40": {
        loc: {
          start: {
            line: 133,
            column: 13
          },
          end: {
            line: 133,
            column: 76
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 133,
            column: 62
          },
          end: {
            line: 133,
            column: 68
          }
        }, {
          start: {
            line: 133,
            column: 71
          },
          end: {
            line: 133,
            column: 76
          }
        }],
        line: 133
      },
      "41": {
        loc: {
          start: {
            line: 133,
            column: 13
          },
          end: {
            line: 133,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 133,
            column: 13
          },
          end: {
            line: 133,
            column: 42
          }
        }, {
          start: {
            line: 133,
            column: 46
          },
          end: {
            line: 133,
            column: 59
          }
        }],
        line: 133
      },
      "42": {
        loc: {
          start: {
            line: 133,
            column: 83
          },
          end: {
            line: 133,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 133,
            column: 132
          },
          end: {
            line: 133,
            column: 138
          }
        }, {
          start: {
            line: 133,
            column: 141
          },
          end: {
            line: 133,
            column: 146
          }
        }],
        line: 133
      },
      "43": {
        loc: {
          start: {
            line: 133,
            column: 83
          },
          end: {
            line: 133,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 133,
            column: 83
          },
          end: {
            line: 133,
            column: 112
          }
        }, {
          start: {
            line: 133,
            column: 116
          },
          end: {
            line: 133,
            column: 129
          }
        }],
        line: 133
      },
      "44": {
        loc: {
          start: {
            line: 135,
            column: 8
          },
          end: {
            line: 149,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 135,
            column: 8
          },
          end: {
            line: 149,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 135
      },
      "45": {
        loc: {
          start: {
            line: 140,
            column: 28
          },
          end: {
            line: 140,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 140,
            column: 77
          },
          end: {
            line: 140,
            column: 83
          }
        }, {
          start: {
            line: 140,
            column: 86
          },
          end: {
            line: 140,
            column: 91
          }
        }],
        line: 140
      },
      "46": {
        loc: {
          start: {
            line: 140,
            column: 28
          },
          end: {
            line: 140,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 140,
            column: 28
          },
          end: {
            line: 140,
            column: 57
          }
        }, {
          start: {
            line: 140,
            column: 61
          },
          end: {
            line: 140,
            column: 74
          }
        }],
        line: 140
      },
      "47": {
        loc: {
          start: {
            line: 159,
            column: 16
          },
          end: {
            line: 205,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 160,
            column: 20
          },
          end: {
            line: 163,
            column: 72
          }
        }, {
          start: {
            line: 164,
            column: 20
          },
          end: {
            line: 190,
            column: 48
          }
        }, {
          start: {
            line: 191,
            column: 20
          },
          end: {
            line: 203,
            column: 48
          }
        }, {
          start: {
            line: 204,
            column: 20
          },
          end: {
            line: 204,
            column: 50
          }
        }],
        line: 159
      },
      "48": {
        loc: {
          start: {
            line: 166,
            column: 24
          },
          end: {
            line: 189,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 166,
            column: 24
          },
          end: {
            line: 189,
            column: 25
          }
        }, {
          start: {
            line: 179,
            column: 29
          },
          end: {
            line: 189,
            column: 25
          }
        }],
        line: 166
      },
      "49": {
        loc: {
          start: {
            line: 166,
            column: 28
          },
          end: {
            line: 166,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 166,
            column: 69
          },
          end: {
            line: 166,
            column: 75
          }
        }, {
          start: {
            line: 166,
            column: 78
          },
          end: {
            line: 166,
            column: 90
          }
        }],
        line: 166
      },
      "50": {
        loc: {
          start: {
            line: 166,
            column: 28
          },
          end: {
            line: 166,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 166,
            column: 28
          },
          end: {
            line: 166,
            column: 44
          }
        }, {
          start: {
            line: 166,
            column: 48
          },
          end: {
            line: 166,
            column: 66
          }
        }],
        line: 166
      },
      "51": {
        loc: {
          start: {
            line: 167,
            column: 38
          },
          end: {
            line: 167,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 167,
            column: 39
          },
          end: {
            line: 167,
            column: 106
          }
        }, {
          start: {
            line: 167,
            column: 111
          },
          end: {
            line: 167,
            column: 116
          }
        }],
        line: 167
      },
      "52": {
        loc: {
          start: {
            line: 167,
            column: 39
          },
          end: {
            line: 167,
            column: 106
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 167,
            column: 87
          },
          end: {
            line: 167,
            column: 93
          }
        }, {
          start: {
            line: 167,
            column: 96
          },
          end: {
            line: 167,
            column: 106
          }
        }],
        line: 167
      },
      "53": {
        loc: {
          start: {
            line: 167,
            column: 39
          },
          end: {
            line: 167,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 167,
            column: 39
          },
          end: {
            line: 167,
            column: 67
          }
        }, {
          start: {
            line: 167,
            column: 71
          },
          end: {
            line: 167,
            column: 84
          }
        }],
        line: 167
      },
      "54": {
        loc: {
          start: {
            line: 172,
            column: 43
          },
          end: {
            line: 172,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 172,
            column: 43
          },
          end: {
            line: 172,
            column: 60
          }
        }, {
          start: {
            line: 172,
            column: 64
          },
          end: {
            line: 172,
            column: 68
          }
        }],
        line: 172
      },
      "55": {
        loc: {
          start: {
            line: 201,
            column: 35
          },
          end: {
            line: 201,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 201,
            column: 62
          },
          end: {
            line: 201,
            column: 77
          }
        }, {
          start: {
            line: 201,
            column: 80
          },
          end: {
            line: 201,
            column: 102
          }
        }],
        line: 201
      },
      "56": {
        loc: {
          start: {
            line: 216,
            column: 16
          },
          end: {
            line: 265,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 217,
            column: 20
          },
          end: {
            line: 220,
            column: 82
          }
        }, {
          start: {
            line: 221,
            column: 20
          },
          end: {
            line: 223,
            column: 53
          }
        }, {
          start: {
            line: 224,
            column: 20
          },
          end: {
            line: 230,
            column: 28
          }
        }, {
          start: {
            line: 231,
            column: 20
          },
          end: {
            line: 235,
            column: 82
          }
        }, {
          start: {
            line: 236,
            column: 20
          },
          end: {
            line: 238,
            column: 53
          }
        }, {
          start: {
            line: 239,
            column: 20
          },
          end: {
            line: 239,
            column: 99
          }
        }, {
          start: {
            line: 240,
            column: 20
          },
          end: {
            line: 240,
            column: 66
          }
        }, {
          start: {
            line: 241,
            column: 20
          },
          end: {
            line: 251,
            column: 52
          }
        }, {
          start: {
            line: 252,
            column: 20
          },
          end: {
            line: 252,
            column: 96
          }
        }, {
          start: {
            line: 253,
            column: 20
          },
          end: {
            line: 255,
            column: 53
          }
        }, {
          start: {
            line: 256,
            column: 20
          },
          end: {
            line: 256,
            column: 54
          }
        }, {
          start: {
            line: 257,
            column: 20
          },
          end: {
            line: 263,
            column: 53
          }
        }, {
          start: {
            line: 264,
            column: 20
          },
          end: {
            line: 264,
            column: 51
          }
        }],
        line: 216
      },
      "57": {
        loc: {
          start: {
            line: 219,
            column: 24
          },
          end: {
            line: 219,
            column: 80
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 219,
            column: 24
          },
          end: {
            line: 219,
            column: 80
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 219
      },
      "58": {
        loc: {
          start: {
            line: 233,
            column: 24
          },
          end: {
            line: 233,
            column: 67
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 233,
            column: 24
          },
          end: {
            line: 233,
            column: 67
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 233
      },
      "59": {
        loc: {
          start: {
            line: 234,
            column: 24
          },
          end: {
            line: 234,
            column: 80
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 234,
            column: 24
          },
          end: {
            line: 234,
            column: 80
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 234
      },
      "60": {
        loc: {
          start: {
            line: 243,
            column: 24
          },
          end: {
            line: 243,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 243,
            column: 24
          },
          end: {
            line: 243,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 243
      },
      "61": {
        loc: {
          start: {
            line: 243,
            column: 30
          },
          end: {
            line: 243,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 243,
            column: 30
          },
          end: {
            line: 243,
            column: 35
          }
        }, {
          start: {
            line: 243,
            column: 39
          },
          end: {
            line: 243,
            column: 43
          }
        }],
        line: 243
      },
      "62": {
        loc: {
          start: {
            line: 247,
            column: 37
          },
          end: {
            line: 247,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 247,
            column: 37
          },
          end: {
            line: 247,
            column: 44
          }
        }, {
          start: {
            line: 247,
            column: 48
          },
          end: {
            line: 247,
            column: 53
          }
        }],
        line: 247
      },
      "63": {
        loc: {
          start: {
            line: 261,
            column: 35
          },
          end: {
            line: 261,
            column: 106
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 261,
            column: 62
          },
          end: {
            line: 261,
            column: 77
          }
        }, {
          start: {
            line: 261,
            column: 80
          },
          end: {
            line: 261,
            column: 106
          }
        }],
        line: 261
      },
      "64": {
        loc: {
          start: {
            line: 290,
            column: 8
          },
          end: {
            line: 292,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 290,
            column: 8
          },
          end: {
            line: 292,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 290
      },
      "65": {
        loc: {
          start: {
            line: 299,
            column: 8
          },
          end: {
            line: 301,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 299,
            column: 8
          },
          end: {
            line: 301,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 299
      },
      "66": {
        loc: {
          start: {
            line: 305,
            column: 8
          },
          end: {
            line: 307,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 305,
            column: 8
          },
          end: {
            line: 307,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 305
      },
      "67": {
        loc: {
          start: {
            line: 309,
            column: 8
          },
          end: {
            line: 311,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 309,
            column: 8
          },
          end: {
            line: 311,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 309
      },
      "68": {
        loc: {
          start: {
            line: 321,
            column: 16
          },
          end: {
            line: 353,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 322,
            column: 20
          },
          end: {
            line: 336,
            column: 32
          }
        }, {
          start: {
            line: 337,
            column: 20
          },
          end: {
            line: 344,
            column: 48
          }
        }, {
          start: {
            line: 345,
            column: 20
          },
          end: {
            line: 351,
            column: 48
          }
        }, {
          start: {
            line: 352,
            column: 20
          },
          end: {
            line: 352,
            column: 50
          }
        }],
        line: 321
      },
      "69": {
        loc: {
          start: {
            line: 328,
            column: 24
          },
          end: {
            line: 331,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 328,
            column: 24
          },
          end: {
            line: 331,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 328
      },
      "70": {
        loc: {
          start: {
            line: 341,
            column: 24
          },
          end: {
            line: 343,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 341,
            column: 24
          },
          end: {
            line: 343,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 341
      },
      "71": {
        loc: {
          start: {
            line: 349,
            column: 35
          },
          end: {
            line: 349,
            column: 94
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 349,
            column: 62
          },
          end: {
            line: 349,
            column: 77
          }
        }, {
          start: {
            line: 349,
            column: 80
          },
          end: {
            line: 349,
            column: 94
          }
        }],
        line: 349
      },
      "72": {
        loc: {
          start: {
            line: 365,
            column: 16
          },
          end: {
            line: 395,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 366,
            column: 20
          },
          end: {
            line: 371,
            column: 37
          }
        }, {
          start: {
            line: 372,
            column: 20
          },
          end: {
            line: 380,
            column: 32
          }
        }, {
          start: {
            line: 381,
            column: 20
          },
          end: {
            line: 384,
            column: 62
          }
        }, {
          start: {
            line: 385,
            column: 20
          },
          end: {
            line: 388,
            column: 37
          }
        }, {
          start: {
            line: 389,
            column: 20
          },
          end: {
            line: 389,
            column: 52
          }
        }, {
          start: {
            line: 390,
            column: 20
          },
          end: {
            line: 393,
            column: 48
          }
        }, {
          start: {
            line: 394,
            column: 20
          },
          end: {
            line: 394,
            column: 50
          }
        }],
        line: 365
      },
      "73": {
        loc: {
          start: {
            line: 368,
            column: 24
          },
          end: {
            line: 370,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 368,
            column: 24
          },
          end: {
            line: 370,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 368
      },
      "74": {
        loc: {
          start: {
            line: 368,
            column: 28
          },
          end: {
            line: 368,
            column: 131
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 368,
            column: 28
          },
          end: {
            line: 368,
            column: 57
          }
        }, {
          start: {
            line: 368,
            column: 61
          },
          end: {
            line: 368,
            column: 131
          }
        }],
        line: 368
      },
      "75": {
        loc: {
          start: {
            line: 368,
            column: 63
          },
          end: {
            line: 368,
            column: 130
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 368,
            column: 116
          },
          end: {
            line: 368,
            column: 122
          }
        }, {
          start: {
            line: 368,
            column: 125
          },
          end: {
            line: 368,
            column: 130
          }
        }],
        line: 368
      },
      "76": {
        loc: {
          start: {
            line: 368,
            column: 63
          },
          end: {
            line: 368,
            column: 113
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 368,
            column: 63
          },
          end: {
            line: 368,
            column: 96
          }
        }, {
          start: {
            line: 368,
            column: 100
          },
          end: {
            line: 368,
            column: 113
          }
        }],
        line: 368
      },
      "77": {
        loc: {
          start: {
            line: 383,
            column: 24
          },
          end: {
            line: 383,
            column: 66
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 383,
            column: 24
          },
          end: {
            line: 383,
            column: 66
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 383
      },
      "78": {
        loc: {
          start: {
            line: 387,
            column: 49
          },
          end: {
            line: 387,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 387,
            column: 49
          },
          end: {
            line: 387,
            column: 56
          }
        }, {
          start: {
            line: 387,
            column: 60
          },
          end: {
            line: 387,
            column: 65
          }
        }],
        line: 387
      },
      "79": {
        loc: {
          start: {
            line: 405,
            column: 8
          },
          end: {
            line: 407,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 405,
            column: 8
          },
          end: {
            line: 407,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 405
      },
      "80": {
        loc: {
          start: {
            line: 405,
            column: 12
          },
          end: {
            line: 405,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 405,
            column: 12
          },
          end: {
            line: 405,
            column: 37
          }
        }, {
          start: {
            line: 405,
            column: 41
          },
          end: {
            line: 405,
            column: 57
          }
        }],
        line: 405
      },
      "81": {
        loc: {
          start: {
            line: 412,
            column: 12
          },
          end: {
            line: 414,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 412,
            column: 12
          },
          end: {
            line: 414,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 412
      },
      "82": {
        loc: {
          start: {
            line: 415,
            column: 12
          },
          end: {
            line: 417,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 415,
            column: 12
          },
          end: {
            line: 417,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 415
      },
      "83": {
        loc: {
          start: {
            line: 420,
            column: 8
          },
          end: {
            line: 448,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 420,
            column: 8
          },
          end: {
            line: 448,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 420
      },
      "84": {
        loc: {
          start: {
            line: 422,
            column: 16
          },
          end: {
            line: 423,
            column: 27
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 422,
            column: 16
          },
          end: {
            line: 423,
            column: 27
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 422
      },
      "85": {
        loc: {
          start: {
            line: 424,
            column: 16
          },
          end: {
            line: 429,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 424,
            column: 16
          },
          end: {
            line: 429,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 424
      },
      "86": {
        loc: {
          start: {
            line: 425,
            column: 39
          },
          end: {
            line: 425,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 425,
            column: 56
          },
          end: {
            line: 425,
            column: 80
          }
        }, {
          start: {
            line: 425,
            column: 83
          },
          end: {
            line: 425,
            column: 87
          }
        }],
        line: 425
      },
      "87": {
        loc: {
          start: {
            line: 426,
            column: 20
          },
          end: {
            line: 428,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 426,
            column: 20
          },
          end: {
            line: 428,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 426
      },
      "88": {
        loc: {
          start: {
            line: 434,
            column: 16
          },
          end: {
            line: 435,
            column: 27
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 434,
            column: 16
          },
          end: {
            line: 435,
            column: 27
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 434
      },
      "89": {
        loc: {
          start: {
            line: 436,
            column: 16
          },
          end: {
            line: 438,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 436,
            column: 16
          },
          end: {
            line: 438,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 436
      },
      "90": {
        loc: {
          start: {
            line: 443,
            column: 16
          },
          end: {
            line: 444,
            column: 27
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 443,
            column: 16
          },
          end: {
            line: 444,
            column: 27
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 443
      },
      "91": {
        loc: {
          start: {
            line: 469,
            column: 8
          },
          end: {
            line: 472,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 469,
            column: 8
          },
          end: {
            line: 472,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 469
      },
      "92": {
        loc: {
          start: {
            line: 474,
            column: 8
          },
          end: {
            line: 487,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 474,
            column: 8
          },
          end: {
            line: 487,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 474
      },
      "93": {
        loc: {
          start: {
            line: 475,
            column: 12
          },
          end: {
            line: 478,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 475,
            column: 12
          },
          end: {
            line: 478,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 475
      },
      "94": {
        loc: {
          start: {
            line: 479,
            column: 12
          },
          end: {
            line: 482,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 479,
            column: 12
          },
          end: {
            line: 482,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 479
      },
      "95": {
        loc: {
          start: {
            line: 483,
            column: 12
          },
          end: {
            line: 486,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 483,
            column: 12
          },
          end: {
            line: 486,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 483
      },
      "96": {
        loc: {
          start: {
            line: 500,
            column: 4
          },
          end: {
            line: 502,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 500,
            column: 4
          },
          end: {
            line: 502,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 500
      },
      "97": {
        loc: {
          start: {
            line: 510,
            column: 4
          },
          end: {
            line: 513,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 510,
            column: 4
          },
          end: {
            line: 513,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 510
      },
      "98": {
        loc: {
          start: {
            line: 516,
            column: 0
          },
          end: {
            line: 524,
            column: 1
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 516,
            column: 0
          },
          end: {
            line: 524,
            column: 1
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 516
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0,
      "265": 0,
      "266": 0,
      "267": 0,
      "268": 0,
      "269": 0,
      "270": 0,
      "271": 0,
      "272": 0,
      "273": 0,
      "274": 0,
      "275": 0,
      "276": 0,
      "277": 0,
      "278": 0,
      "279": 0,
      "280": 0,
      "281": 0,
      "282": 0,
      "283": 0,
      "284": 0,
      "285": 0,
      "286": 0,
      "287": 0,
      "288": 0,
      "289": 0,
      "290": 0,
      "291": 0,
      "292": 0,
      "293": 0,
      "294": 0,
      "295": 0,
      "296": 0,
      "297": 0,
      "298": 0,
      "299": 0,
      "300": 0,
      "301": 0,
      "302": 0,
      "303": 0,
      "304": 0,
      "305": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0, 0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0, 0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0, 0, 0, 0, 0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0],
      "81": [0, 0],
      "82": [0, 0],
      "83": [0, 0],
      "84": [0, 0],
      "85": [0, 0],
      "86": [0, 0],
      "87": [0, 0],
      "88": [0, 0],
      "89": [0, 0],
      "90": [0, 0],
      "91": [0, 0],
      "92": [0, 0],
      "93": [0, 0],
      "94": [0, 0],
      "95": [0, 0],
      "96": [0, 0],
      "97": [0, 0],
      "98": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/auth-state-manager.ts",
      mappings: ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2cH,kDAKC;AAMD,0DAKC;AAzdD,yCAAsD;AA0BtD;IAyBE;QAxBQ,UAAK,GAAc;YACzB,eAAe,EAAE,KAAK;YACtB,SAAS,EAAE,IAAI;YACf,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,IAAI;YACf,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,IAAI;SACZ,CAAC;QAEM,yBAAoB,GAAoC,IAAI,GAAG,EAAE,CAAC;QAElE,cAAS,GAAoC,IAAI,GAAG,EAAE,CAAC;QACvD,yBAAoB,GAA0B,IAAI,CAAC;QAC1C,2BAAsB,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,qCAAqC;QAC9E,oBAAe,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,gBAAgB;QAC5D,qBAAgB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,iBAAiB;QAE1E,qDAAqD;QAC7C,yBAAoB,GAA2C,IAAI,CAAC;QACpE,uBAAkB,GAAwB,IAAI,CAAC;QAC/C,8BAAyB,GAAwB,IAAI,CAAC;QACtD,gBAAW,GAAG,KAAK,CAAC;QAG1B,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,6CAAQ,GAAR;QACE,oBAAY,IAAI,CAAC,KAAK,EAAG;IAC3B,CAAC;IAED;;OAEG;IACH,8CAAS,GAAT,UAAU,QAAoC;QAA9C,iBAOC;QANC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,8DAA8D;YAC9D,OAAO,cAAO,CAAC,CAAC;QAClB,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC7B,OAAO,cAAM,OAAA,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAA/B,CAA+B,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,oDAAe,GAAvB;QAAA,iBAMC;QALC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,KAAI,CAAC,QAAQ,EAAE,CAAC,EAAzB,CAAyB,CAAC,CAAC;QACzE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,KAAI,CAAC,QAAQ,EAAE,CAAC,EAAzB,CAAyB,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACK,6CAAQ,GAAhB,UAAiB,OAA2B;QAC1C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,OAAO;QACT,CAAC;QAED,IAAM,QAAQ,gBAAQ,IAAI,CAAC,KAAK,CAAE,CAAC;QACnC,IAAI,CAAC,KAAK,yBAAQ,IAAI,CAAC,KAAK,GAAK,OAAO,CAAE,CAAC;QAE3C,mBAAmB;QACnB,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,uCAAuC;QACvC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACK,yDAAoB,GAA5B,UAA6B,QAAmB,EAAE,QAAmB;;QACnE,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAO;QAE1C,qCAAqC;QACrC,IAAM,iBAAiB,GACrB,QAAQ,CAAC,eAAe,KAAK,QAAQ,CAAC,eAAe;YACrD,CAAA,MAAA,QAAQ,CAAC,IAAI,0CAAE,EAAE,OAAK,MAAA,QAAQ,CAAC,IAAI,0CAAE,EAAE,CAAA;YACvC,QAAQ,CAAC,OAAO,KAAK,QAAQ,CAAC,OAAO,CAAC;QAExC,IAAI,iBAAiB,EAAE,CAAC;YACtB,IAAI,CAAC;gBACH,YAAY,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,SAAS,CAAC;oBACvD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,eAAe,EAAE,QAAQ,CAAC,eAAe;oBACzC,MAAM,EAAE,MAAA,QAAQ,CAAC,IAAI,0CAAE,EAAE;oBACzB,OAAO,EAAE,QAAQ,CAAC,OAAO;iBAC1B,CAAC,CAAC,CAAC;gBAEJ,uDAAuD;gBACvD,YAAY,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;YAC/C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACG,iDAAY,GAAlB;uCAAsB,OAAO;;;;;;;wBAEzB,IAAI,CAAC,QAAQ,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;wBAEhC,qBAAM,IAAA,kBAAU,GAAE,EAAA;;wBAA5B,OAAO,GAAG,SAAkB;wBAElC,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,EAAE,CAAC;4BAGZ,OAAO,GAAG,CAAA,MAAC,OAAe,CAAC,IAAI,0CAAE,OAAO,KAAI,KAAK,CAAC;4BAExD,IAAI,CAAC,QAAQ,CAAC;gCACZ,eAAe,EAAE,IAAI;gCACrB,SAAS,EAAE,KAAK;gCAChB,IAAI,EAAE,OAAO,CAAC,IAAY;gCAC1B,SAAS,EAAG,OAAe,CAAC,SAAS,IAAI,IAAI;gCAC7C,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;gCACxB,OAAO,SAAA;gCACP,KAAK,EAAE,IAAI;6BACZ,CAAC,CAAC;4BAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;wBAC5B,CAAC;6BAAM,CAAC;4BACN,IAAI,CAAC,QAAQ,CAAC;gCACZ,eAAe,EAAE,KAAK;gCACtB,SAAS,EAAE,KAAK;gCAChB,IAAI,EAAE,IAAI;gCACV,SAAS,EAAE,IAAI;gCACf,YAAY,EAAE,IAAI;gCAClB,OAAO,EAAE,KAAK;gCACd,KAAK,EAAE,IAAI;6BACZ,CAAC,CAAC;wBACL,CAAC;;;;wBAED,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,OAAK,CAAC,CAAC;wBACrD,IAAI,CAAC,QAAQ,CAAC;4BACZ,eAAe,EAAE,KAAK;4BACtB,SAAS,EAAE,KAAK;4BAChB,IAAI,EAAE,IAAI;4BACV,SAAS,EAAE,IAAI;4BACf,YAAY,EAAE,IAAI;4BAClB,OAAO,EAAE,KAAK;4BACd,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,sBAAsB;yBACvE,CAAC,CAAC;;;;;;KAEN;IAED;;OAEG;IACG,oDAAe,GAArB;uCAAyB,OAAO;;;;;;6BAGxB,IAAI,CAAC,kBAAkB,EAAE,EAAzB,wBAAyB;wBAC3B,qBAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,EAAA;;wBAAzC,SAAyC,CAAC;wBAC1C,sBAAO,KAAK,EAAC;4BAIE,qBAAM,KAAK,CAAC,4BAA4B,EAAE;4BACzD,MAAM,EAAE,KAAK;4BACb,WAAW,EAAE,aAAa;4BAC1B,OAAO,EAAE;gCACP,eAAe,EAAE,UAAU;6BAC5B;yBACF,CAAC,EAAA;;wBANI,QAAQ,GAAG,SAMf;6BAEE,CAAC,QAAQ,CAAC,EAAE,EAAZ,wBAAY;6BACV,CAAA,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAA,EAAvB,wBAAuB;wBACzB,qBAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,EAAA;;wBAAzC,SAAyC,CAAC;wBAC1C,sBAAO,KAAK,EAAC;4BAEf,MAAM,IAAI,KAAK,CAAC,qCAA8B,QAAQ,CAAC,MAAM,CAAE,CAAC,CAAC;4BAGlC,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAAhD,KAA2B,SAAqB,EAA9C,KAAK,WAAA,EAAE,IAAI,UAAA,EAAE,OAAO,aAAA;6BAExB,CAAA,KAAK,IAAI,IAAI,CAAA,EAAb,wBAAa;wBACf,IAAI,CAAC,QAAQ,CAAC;4BACZ,eAAe,EAAE,IAAI;4BACrB,IAAI,MAAA;4BACJ,OAAO,EAAE,OAAO,IAAI,KAAK;4BACzB,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;4BACxB,KAAK,EAAE,IAAI;yBACZ,CAAC,CAAC;wBACH,sBAAO,IAAI,EAAC;4BAEZ,qBAAM,IAAI,CAAC,WAAW,CAAC,2BAA2B,CAAC,EAAA;;wBAAnD,SAAmD,CAAC;wBACpD,sBAAO,KAAK,EAAC;;;;wBAGf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,OAAK,CAAC,CAAC;wBAClD,IAAI,CAAC,QAAQ,CAAC;4BACZ,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,0BAA0B;yBAC3E,CAAC,CAAC;wBACH,sBAAO,KAAK,EAAC;;;;;KAEhB;IAED;;OAEG;IACH,+CAAU,GAAV;QACE,IAAI,CAAC,QAAQ,CAAC;YACZ,eAAe,EAAE,KAAK;YACtB,SAAS,EAAE,KAAK;YAChB,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,IAAI;YACf,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,uDAAkB,GAAlB;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,QAAQ,CAAC,EAAE,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC;QAErC,oDAAoD;QACpD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,YAAY,CAAC,OAAO,CAAC,oBAAoB,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,uDAAkB,GAAlB;QACU,IAAA,YAAY,GAAK,IAAI,CAAC,KAAK,aAAf,CAAgB;QAEpC,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAM,iBAAiB,GAAG,GAAG,GAAG,YAAY,CAAC;QAE7C,yBAAyB;QACzB,IAAI,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC9C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,iCAAiC;QACjC,IAAI,iBAAiB,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACG,gDAAW,GAAjB,UAAkB,MAAe;uCAAG,OAAO;;;;;;wBAEvC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;wBAErC,0BAA0B;wBAC1B,IAAI,CAAC,UAAU,EAAE,CAAC;wBAElB,qBAAqB;wBACrB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;4BAClC,YAAY,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC;4BAC9C,YAAY,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;wBAC7C,CAAC;wBAED,yBAAyB;wBACzB,qBAAM,IAAA,eAAO,EAAC;gCACZ,QAAQ,EAAE,KAAK;gCACf,WAAW,EAAE,QAAQ;6BACtB,CAAC,EAAA;;wBAJF,yBAAyB;wBACzB,SAGE,CAAC;wBAEH,qCAAqC;wBACrC,IAAI,MAAM,EAAE,CAAC;4BACX,IAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;wBACnC,CAAC;;;;wBAED,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,OAAK,CAAC,CAAC;wBACnD,IAAI,CAAC,QAAQ,CAAC;4BACZ,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc;yBAC/D,CAAC,CAAC;;;;;;KAEN;IAED;;OAEG;IACG,sDAAiB,GAAvB;uCAA2B,OAAO;;;;;;wBAC1B,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACrC,IAAI,CAAC,YAAY,CAAC,eAAe,IAAI,CAAC,CAAA,MAAA,YAAY,CAAC,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;4BAC5D,sBAAO;wBACT,CAAC;;;;wBAGkB,qBAAM,KAAK,CAAC,uCAAgC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAE,EAAE;gCACnF,MAAM,EAAE,KAAK;gCACb,WAAW,EAAE,aAAa;gCAC1B,OAAO,EAAE;oCACP,cAAc,EAAE,kBAAkB;iCACnC;6BACF,CAAC,EAAA;;wBANI,QAAQ,GAAG,SAMf;6BAEE,QAAQ,CAAC,EAAE,EAAX,wBAAW;wBACO,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAAjC,OAAO,GAAK,CAAA,SAAqB,CAAA,QAA1B;wBACf,IAAI,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,KAAK,EAAE,CAAC,CAAC;;;;;wBAG/C,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,OAAK,CAAC,CAAC;;;;;;KAExD;IAED;;OAEG;IACK,gEAA2B,GAAnC;QAAA,iBAgDC;QA/CC,gCAAgC;QAChC,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAClD,OAAO;QACT,CAAC;QAED,wBAAwB;QACxB,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,qCAAqC;QACrC,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC;YACtC,IAAI,KAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,OAAO;YACT,CAAC;YACD,IAAI,KAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;gBAC/B,KAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAEhC,wDAAwD;QACxD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,IAAI,CAAC,oBAAoB,GAAG,UAAC,KAAmB;gBAC9C,IAAI,KAAI,CAAC,WAAW;oBAAE,OAAO;gBAC7B,IAAI,KAAK,CAAC,GAAG,KAAK,oBAAoB,EAAE,CAAC;oBACvC,IAAM,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;oBACtE,IAAI,YAAY,EAAE,CAAC;wBACjB,KAAI,CAAC,QAAQ,CAAC,EAAE,YAAY,cAAA,EAAE,CAAC,CAAC;oBAClC,CAAC;gBACH,CAAC;YACH,CAAC,CAAC;YACF,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAE9D,2CAA2C;YAC3C,IAAI,CAAC,kBAAkB,GAAG;gBACxB,IAAI,KAAI,CAAC,WAAW;oBAAE,OAAO;gBAC7B,IAAI,KAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;oBAC/B,KAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,CAAC;YACH,CAAC,CAAC;YACF,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAE1D,6CAA6C;YAC7C,IAAI,CAAC,yBAAyB,GAAG;gBAC/B,IAAI,KAAI,CAAC,WAAW;oBAAE,OAAO;gBAC7B,KAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,CAAC,CAAC;YACF,MAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,2DAAsB,GAAtB,UAAuB,QAAoC;QACzD,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,8DAAyB,GAAzB,UAA0B,QAAoC;QAC5D,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,4CAAO,GAAP;QACE,kDAAkD;QAClD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,iBAAiB;QACjB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACzC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACnC,CAAC;QAED,yBAAyB;QACzB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC9B,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACjE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACnC,CAAC;YACD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC5B,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAC7D,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YACjC,CAAC;YACD,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;gBACnC,MAAM,CAAC,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;gBAC3E,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;YACxC,CAAC;QACH,CAAC;QAED,kBAAkB;QAClB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;IACpC,CAAC;IACH,iCAAC;AAAD,CAAC,AAvaD,IAuaC;AAED,qBAAqB;AACrB,IAAI,gBAAgB,GAAsC,IAAI,CAAC;AAE/D;;GAEG;AACH,SAAgB,mBAAmB;IACjC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtB,gBAAgB,GAAG,IAAI,0BAA0B,EAAE,CAAC;IACtD,CAAC;IACD,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED;;;GAGG;AACH,SAAgB,uBAAuB;IACrC,IAAI,gBAAgB,EAAE,CAAC;QACrB,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAC3B,gBAAgB,GAAG,IAAI,CAAC;IAC1B,CAAC;AACH,CAAC;AAED,+DAA+D;AAC/D,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;IAClC,MAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE;QACtC,uBAAuB,EAAE,CAAC;IAC5B,CAAC,CAAC,CAAC;IAEH,kDAAkD;IAClD,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE;QAClC,uBAAuB,EAAE,CAAC;IAC5B,CAAC,CAAC,CAAC;AACL,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/auth-state-manager.ts"],
      sourcesContent: ["/**\n * Unified Authentication State Manager\n * Ensures consistent authentication state across the application\n */\n\nimport { getSession, signOut } from 'next-auth/react';\nimport { User } from '@prisma/client';\n\nexport interface AuthState {\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  user: User | null;\n  sessionId: string | null;\n  lastActivity: number | null;\n  isAdmin: boolean;\n  error: string | null;\n}\n\nexport interface AuthStateManager {\n  getState(): AuthState;\n  refreshState(): Promise<void>;\n  validateSession(): Promise<boolean>;\n  clearState(): void;\n  updateLastActivity(): void;\n  checkSessionExpiry(): boolean;\n  forceLogout(reason?: string): Promise<void>;\n  addStateChangeListener(listener: (state: AuthState) => void): () => void;\n  removeStateChangeListener(listener: (state: AuthState) => void): void;\n  updateAdminStatus(): Promise<void>;\n}\n\nclass AuthenticationStateManager implements AuthStateManager {\n  private state: AuthState = {\n    isAuthenticated: false,\n    isLoading: true,\n    user: null,\n    sessionId: null,\n    lastActivity: null,\n    isAdmin: false,\n    error: null\n  };\n\n  private stateChangeListeners: Set<(state: AuthState) => void> = new Set();\n\n  private listeners: Set<(state: AuthState) => void> = new Set();\n  private sessionCheckInterval: NodeJS.Timeout | null = null;\n  private readonly SESSION_CHECK_INTERVAL = 10 * 60 * 1000; // 10 minutes (reduced from 1 minute)\n  private readonly SESSION_TIMEOUT = 30 * 24 * 60 * 60 * 1000; // 30 days in ms\n  private readonly ACTIVITY_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours in ms\n\n  // Store event listener references for proper cleanup\n  private storageEventListener: ((event: StorageEvent) => void) | null = null;\n  private focusEventListener: (() => void) | null = null;\n  private beforeUnloadEventListener: (() => void) | null = null;\n  private isDestroyed = false;\n\n  constructor() {\n    this.initializeSessionMonitoring();\n  }\n\n  /**\n   * Get current authentication state\n   */\n  getState(): AuthState {\n    return { ...this.state };\n  }\n\n  /**\n   * Subscribe to state changes\n   */\n  subscribe(listener: (state: AuthState) => void): () => void {\n    if (this.isDestroyed) {\n      // Return a no-op unsubscribe function for destroyed instances\n      return () => {};\n    }\n    this.listeners.add(listener);\n    return () => this.listeners.delete(listener);\n  }\n\n  /**\n   * Notify all listeners of state changes\n   */\n  private notifyListeners(): void {\n    if (this.isDestroyed) {\n      return;\n    }\n    this.stateChangeListeners.forEach(listener => listener(this.getState()));\n    this.listeners.forEach(listener => listener(this.getState()));\n  }\n\n  /**\n   * Update state and notify listeners\n   */\n  private setState(updates: Partial<AuthState>): void {\n    if (this.isDestroyed) {\n      return;\n    }\n\n    const oldState = { ...this.state };\n    this.state = { ...this.state, ...updates };\n\n    // Notify listeners\n    this.notifyListeners();\n\n    // Broadcast state change to other tabs\n    this.broadcastStateChange(oldState, this.state);\n  }\n\n  /**\n   * Broadcast state changes to other tabs via localStorage\n   */\n  private broadcastStateChange(oldState: AuthState, newState: AuthState): void {\n    if (typeof window === 'undefined') return;\n\n    // Only broadcast significant changes\n    const significantChange =\n      oldState.isAuthenticated !== newState.isAuthenticated ||\n      oldState.user?.id !== newState.user?.id ||\n      oldState.isAdmin !== newState.isAdmin;\n\n    if (significantChange) {\n      try {\n        localStorage.setItem('auth_state_change', JSON.stringify({\n          timestamp: Date.now(),\n          isAuthenticated: newState.isAuthenticated,\n          userId: newState.user?.id,\n          isAdmin: newState.isAdmin\n        }));\n\n        // Remove the item immediately to trigger storage event\n        localStorage.removeItem('auth_state_change');\n      } catch (error) {\n        console.error('Failed to broadcast auth state change:', error);\n      }\n    }\n  }\n\n  /**\n   * Refresh authentication state from session\n   */\n  async refreshState(): Promise<void> {\n    try {\n      this.setState({ isLoading: true, error: null });\n\n      const session = await getSession();\n      \n      if (session?.user) {\n        // Get admin status from session if available, otherwise default to false\n        // Admin status will be updated separately via API call if needed\n        const isAdmin = (session as any).user?.isAdmin || false;\n\n        this.setState({\n          isAuthenticated: true,\n          isLoading: false,\n          user: session.user as User,\n          sessionId: (session as any).sessionId || null,\n          lastActivity: Date.now(),\n          isAdmin,\n          error: null\n        });\n\n        this.updateLastActivity();\n      } else {\n        this.setState({\n          isAuthenticated: false,\n          isLoading: false,\n          user: null,\n          sessionId: null,\n          lastActivity: null,\n          isAdmin: false,\n          error: null\n        });\n      }\n    } catch (error) {\n      console.error('Error refreshing auth state:', error);\n      this.setState({\n        isAuthenticated: false,\n        isLoading: false,\n        user: null,\n        sessionId: null,\n        lastActivity: null,\n        isAdmin: false,\n        error: error instanceof Error ? error.message : 'Authentication error'\n      });\n    }\n  }\n\n  /**\n   * Validate current session\n   */\n  async validateSession(): Promise<boolean> {\n    try {\n      // Check session expiry\n      if (this.checkSessionExpiry()) {\n        await this.forceLogout('Session expired');\n        return false;\n      }\n\n      // Validate with server\n      const response = await fetch('/api/auth/validate-session', {\n        method: 'GET',\n        credentials: 'same-origin',\n        headers: {\n          'Cache-Control': 'no-cache'\n        }\n      });\n\n      if (!response.ok) {\n        if (response.status === 401) {\n          await this.forceLogout('Session invalid');\n          return false;\n        }\n        throw new Error(`Session validation failed: ${response.status}`);\n      }\n\n      const { valid, user, isAdmin } = await response.json();\n\n      if (valid && user) {\n        this.setState({\n          isAuthenticated: true,\n          user,\n          isAdmin: isAdmin || false,\n          lastActivity: Date.now(),\n          error: null\n        });\n        return true;\n      } else {\n        await this.forceLogout('Session validation failed');\n        return false;\n      }\n    } catch (error) {\n      console.error('Session validation error:', error);\n      this.setState({\n        error: error instanceof Error ? error.message : 'Session validation error'\n      });\n      return false;\n    }\n  }\n\n  /**\n   * Clear authentication state\n   */\n  clearState(): void {\n    this.setState({\n      isAuthenticated: false,\n      isLoading: false,\n      user: null,\n      sessionId: null,\n      lastActivity: null,\n      isAdmin: false,\n      error: null\n    });\n  }\n\n  /**\n   * Update last activity timestamp\n   */\n  updateLastActivity(): void {\n    const now = Date.now();\n    this.setState({ lastActivity: now });\n    \n    // Store in localStorage for persistence across tabs\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('auth_last_activity', now.toString());\n    }\n  }\n\n  /**\n   * Check if session has expired\n   */\n  checkSessionExpiry(): boolean {\n    const { lastActivity } = this.state;\n    \n    if (!lastActivity) {\n      return true;\n    }\n\n    const now = Date.now();\n    const timeSinceActivity = now - lastActivity;\n\n    // Check activity timeout\n    if (timeSinceActivity > this.ACTIVITY_TIMEOUT) {\n      return true;\n    }\n\n    // Check absolute session timeout\n    if (timeSinceActivity > this.SESSION_TIMEOUT) {\n      return true;\n    }\n\n    return false;\n  }\n\n  /**\n   * Force logout with reason\n   */\n  async forceLogout(reason?: string): Promise<void> {\n    try {\n      console.log('Force logout:', reason);\n      \n      // Clear local state first\n      this.clearState();\n      \n      // Clear localStorage\n      if (typeof window !== 'undefined') {\n        localStorage.removeItem('auth_last_activity');\n        localStorage.removeItem('auth_session_id');\n      }\n\n      // Sign out from NextAuth\n      await signOut({ \n        redirect: false,\n        callbackUrl: '/login'\n      });\n\n      // Set error state if reason provided\n      if (reason) {\n        this.setState({ error: reason });\n      }\n    } catch (error) {\n      console.error('Error during force logout:', error);\n      this.setState({\n        error: error instanceof Error ? error.message : 'Logout error'\n      });\n    }\n  }\n\n  /**\n   * Update admin status via API call (optional, called separately)\n   */\n  async updateAdminStatus(): Promise<void> {\n    const currentState = this.getState();\n    if (!currentState.isAuthenticated || !currentState.user?.id) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/auth/check-admin?userId=${currentState.user.id}`, {\n        method: 'GET',\n        credentials: 'same-origin',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (response.ok) {\n        const { isAdmin } = await response.json();\n        this.setState({ isAdmin: isAdmin || false });\n      }\n    } catch (error) {\n      console.error('Error updating admin status:', error);\n    }\n  }\n\n  /**\n   * Initialize session monitoring\n   */\n  private initializeSessionMonitoring(): void {\n    // Prevent double initialization\n    if (this.sessionCheckInterval || this.isDestroyed) {\n      return;\n    }\n\n    // Initial state refresh\n    this.refreshState();\n\n    // Set up periodic session validation\n    this.sessionCheckInterval = setInterval(() => {\n      if (this.isDestroyed) {\n        return;\n      }\n      if (this.state.isAuthenticated) {\n        this.validateSession();\n      }\n    }, this.SESSION_CHECK_INTERVAL);\n\n    // Listen for storage events (cross-tab synchronization)\n    if (typeof window !== 'undefined') {\n      this.storageEventListener = (event: StorageEvent) => {\n        if (this.isDestroyed) return;\n        if (event.key === 'auth_last_activity') {\n          const lastActivity = event.newValue ? parseInt(event.newValue) : null;\n          if (lastActivity) {\n            this.setState({ lastActivity });\n          }\n        }\n      };\n      window.addEventListener('storage', this.storageEventListener);\n\n      // Listen for focus events to refresh state\n      this.focusEventListener = () => {\n        if (this.isDestroyed) return;\n        if (this.state.isAuthenticated) {\n          this.refreshState();\n        }\n      };\n      window.addEventListener('focus', this.focusEventListener);\n\n      // Listen for beforeunload to update activity\n      this.beforeUnloadEventListener = () => {\n        if (this.isDestroyed) return;\n        this.updateLastActivity();\n      };\n      window.addEventListener('beforeunload', this.beforeUnloadEventListener);\n    }\n  }\n\n  /**\n   * Add state change listener (alias for subscribe)\n   */\n  addStateChangeListener(listener: (state: AuthState) => void): () => void {\n    return this.subscribe(listener);\n  }\n\n  /**\n   * Remove state change listener\n   */\n  removeStateChangeListener(listener: (state: AuthState) => void): void {\n    this.listeners.delete(listener);\n  }\n\n  /**\n   * Cleanup resources\n   */\n  destroy(): void {\n    // Mark as destroyed to prevent further operations\n    this.isDestroyed = true;\n\n    // Clear interval\n    if (this.sessionCheckInterval) {\n      clearInterval(this.sessionCheckInterval);\n      this.sessionCheckInterval = null;\n    }\n\n    // Remove event listeners\n    if (typeof window !== 'undefined') {\n      if (this.storageEventListener) {\n        window.removeEventListener('storage', this.storageEventListener);\n        this.storageEventListener = null;\n      }\n      if (this.focusEventListener) {\n        window.removeEventListener('focus', this.focusEventListener);\n        this.focusEventListener = null;\n      }\n      if (this.beforeUnloadEventListener) {\n        window.removeEventListener('beforeunload', this.beforeUnloadEventListener);\n        this.beforeUnloadEventListener = null;\n      }\n    }\n\n    // Clear listeners\n    this.listeners.clear();\n    this.stateChangeListeners.clear();\n  }\n}\n\n// Singleton instance\nlet authStateManager: AuthenticationStateManager | null = null;\n\n/**\n * Get the global authentication state manager instance\n */\nexport function getAuthStateManager(): AuthenticationStateManager {\n  if (!authStateManager) {\n    authStateManager = new AuthenticationStateManager();\n  }\n  return authStateManager;\n}\n\n/**\n * Destroy the global authentication state manager instance\n * This should be called when the application is shutting down\n */\nexport function destroyAuthStateManager(): void {\n  if (authStateManager) {\n    authStateManager.destroy();\n    authStateManager = null;\n  }\n}\n\n// Automatically cleanup on page unload to prevent memory leaks\nif (typeof window !== 'undefined') {\n  window.addEventListener('beforeunload', () => {\n    destroyAuthStateManager();\n  });\n\n  // Also cleanup on page hide (for mobile browsers)\n  window.addEventListener('pagehide', () => {\n    destroyAuthStateManager();\n  });\n}\n\n\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "054c3615089354638bcd2b8cb9f0cf9db23bcf46"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_15d5esx45j = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_15d5esx45j();
var __assign =
/* istanbul ignore next */
(cov_15d5esx45j().s[0]++,
/* istanbul ignore next */
(cov_15d5esx45j().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_15d5esx45j().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_15d5esx45j().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_15d5esx45j().f[0]++;
  cov_15d5esx45j().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_15d5esx45j().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_15d5esx45j().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_15d5esx45j().f[1]++;
    cov_15d5esx45j().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_15d5esx45j().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_15d5esx45j().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_15d5esx45j().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_15d5esx45j().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_15d5esx45j().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_15d5esx45j().b[2][0]++;
          cov_15d5esx45j().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_15d5esx45j().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_15d5esx45j().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_15d5esx45j().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_15d5esx45j().s[11]++,
/* istanbul ignore next */
(cov_15d5esx45j().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_15d5esx45j().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_15d5esx45j().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_15d5esx45j().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_15d5esx45j().f[3]++;
    cov_15d5esx45j().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_15d5esx45j().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_15d5esx45j().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_15d5esx45j().f[4]++;
      cov_15d5esx45j().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_15d5esx45j().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_15d5esx45j().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_15d5esx45j().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_15d5esx45j().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_15d5esx45j().f[6]++;
      cov_15d5esx45j().s[15]++;
      try {
        /* istanbul ignore next */
        cov_15d5esx45j().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_15d5esx45j().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_15d5esx45j().f[7]++;
      cov_15d5esx45j().s[18]++;
      try {
        /* istanbul ignore next */
        cov_15d5esx45j().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_15d5esx45j().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_15d5esx45j().f[8]++;
      cov_15d5esx45j().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_15d5esx45j().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_15d5esx45j().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_15d5esx45j().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_15d5esx45j().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_15d5esx45j().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_15d5esx45j().s[23]++,
/* istanbul ignore next */
(cov_15d5esx45j().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_15d5esx45j().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_15d5esx45j().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_15d5esx45j().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_15d5esx45j().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_15d5esx45j().f[10]++;
        cov_15d5esx45j().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_15d5esx45j().b[9][0]++;
          cov_15d5esx45j().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_15d5esx45j().b[9][1]++;
        }
        cov_15d5esx45j().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_15d5esx45j().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_15d5esx45j().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_15d5esx45j().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_15d5esx45j().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_15d5esx45j().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_15d5esx45j().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_15d5esx45j().f[11]++;
    cov_15d5esx45j().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_15d5esx45j().f[12]++;
    cov_15d5esx45j().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_15d5esx45j().f[13]++;
      cov_15d5esx45j().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_15d5esx45j().f[14]++;
    cov_15d5esx45j().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_15d5esx45j().b[12][0]++;
      cov_15d5esx45j().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_15d5esx45j().b[12][1]++;
    }
    cov_15d5esx45j().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_15d5esx45j().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_15d5esx45j().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_15d5esx45j().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_15d5esx45j().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_15d5esx45j().s[36]++;
      try {
        /* istanbul ignore next */
        cov_15d5esx45j().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_15d5esx45j().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_15d5esx45j().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_15d5esx45j().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_15d5esx45j().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_15d5esx45j().b[18][0]++,
        /* istanbul ignore next */
        (cov_15d5esx45j().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_15d5esx45j().b[19][1]++,
        /* istanbul ignore next */
        (cov_15d5esx45j().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_15d5esx45j().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_15d5esx45j().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_15d5esx45j().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_15d5esx45j().b[15][0]++;
          cov_15d5esx45j().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_15d5esx45j().b[15][1]++;
        }
        cov_15d5esx45j().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_15d5esx45j().b[21][0]++;
          cov_15d5esx45j().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_15d5esx45j().b[21][1]++;
        }
        cov_15d5esx45j().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_15d5esx45j().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_15d5esx45j().b[22][1]++;
            cov_15d5esx45j().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_15d5esx45j().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_15d5esx45j().b[22][2]++;
            cov_15d5esx45j().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_15d5esx45j().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_15d5esx45j().b[22][3]++;
            cov_15d5esx45j().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_15d5esx45j().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_15d5esx45j().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_15d5esx45j().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_15d5esx45j().b[22][4]++;
            cov_15d5esx45j().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_15d5esx45j().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_15d5esx45j().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_15d5esx45j().b[22][5]++;
            cov_15d5esx45j().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_15d5esx45j().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_15d5esx45j().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_15d5esx45j().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_15d5esx45j().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_15d5esx45j().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_15d5esx45j().b[23][0]++;
              cov_15d5esx45j().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_15d5esx45j().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_15d5esx45j().b[23][1]++;
            }
            cov_15d5esx45j().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_15d5esx45j().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_15d5esx45j().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_15d5esx45j().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_15d5esx45j().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_15d5esx45j().b[26][0]++;
              cov_15d5esx45j().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_15d5esx45j().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_15d5esx45j().b[26][1]++;
            }
            cov_15d5esx45j().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_15d5esx45j().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_15d5esx45j().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_15d5esx45j().b[28][0]++;
              cov_15d5esx45j().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_15d5esx45j().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_15d5esx45j().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_15d5esx45j().b[28][1]++;
            }
            cov_15d5esx45j().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_15d5esx45j().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_15d5esx45j().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_15d5esx45j().b[30][0]++;
              cov_15d5esx45j().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_15d5esx45j().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_15d5esx45j().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_15d5esx45j().b[30][1]++;
            }
            cov_15d5esx45j().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_15d5esx45j().b[32][0]++;
              cov_15d5esx45j().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_15d5esx45j().b[32][1]++;
            }
            cov_15d5esx45j().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_15d5esx45j().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_15d5esx45j().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_15d5esx45j().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_15d5esx45j().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_15d5esx45j().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_15d5esx45j().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_15d5esx45j().b[33][0]++;
      cov_15d5esx45j().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_15d5esx45j().b[33][1]++;
    }
    cov_15d5esx45j().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_15d5esx45j().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_15d5esx45j().b[34][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_15d5esx45j().s[78]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_15d5esx45j().s[79]++;
exports.getAuthStateManager = getAuthStateManager;
/* istanbul ignore next */
cov_15d5esx45j().s[80]++;
exports.destroyAuthStateManager = destroyAuthStateManager;
var react_1 =
/* istanbul ignore next */
(cov_15d5esx45j().s[81]++, require("next-auth/react"));
var AuthenticationStateManager =
/* istanbul ignore next */
(/** @class */cov_15d5esx45j().s[82]++, function () {
  /* istanbul ignore next */
  cov_15d5esx45j().f[15]++;
  function AuthenticationStateManager() {
    /* istanbul ignore next */
    cov_15d5esx45j().f[16]++;
    cov_15d5esx45j().s[83]++;
    this.state = {
      isAuthenticated: false,
      isLoading: true,
      user: null,
      sessionId: null,
      lastActivity: null,
      isAdmin: false,
      error: null
    };
    /* istanbul ignore next */
    cov_15d5esx45j().s[84]++;
    this.stateChangeListeners = new Set();
    /* istanbul ignore next */
    cov_15d5esx45j().s[85]++;
    this.listeners = new Set();
    /* istanbul ignore next */
    cov_15d5esx45j().s[86]++;
    this.sessionCheckInterval = null;
    /* istanbul ignore next */
    cov_15d5esx45j().s[87]++;
    this.SESSION_CHECK_INTERVAL = 10 * 60 * 1000; // 10 minutes (reduced from 1 minute)
    /* istanbul ignore next */
    cov_15d5esx45j().s[88]++;
    this.SESSION_TIMEOUT = 30 * 24 * 60 * 60 * 1000; // 30 days in ms
    /* istanbul ignore next */
    cov_15d5esx45j().s[89]++;
    this.ACTIVITY_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours in ms
    // Store event listener references for proper cleanup
    /* istanbul ignore next */
    cov_15d5esx45j().s[90]++;
    this.storageEventListener = null;
    /* istanbul ignore next */
    cov_15d5esx45j().s[91]++;
    this.focusEventListener = null;
    /* istanbul ignore next */
    cov_15d5esx45j().s[92]++;
    this.beforeUnloadEventListener = null;
    /* istanbul ignore next */
    cov_15d5esx45j().s[93]++;
    this.isDestroyed = false;
    /* istanbul ignore next */
    cov_15d5esx45j().s[94]++;
    this.initializeSessionMonitoring();
  }
  /**
   * Get current authentication state
   */
  /* istanbul ignore next */
  cov_15d5esx45j().s[95]++;
  AuthenticationStateManager.prototype.getState = function () {
    /* istanbul ignore next */
    cov_15d5esx45j().f[17]++;
    cov_15d5esx45j().s[96]++;
    return __assign({}, this.state);
  };
  /**
   * Subscribe to state changes
   */
  /* istanbul ignore next */
  cov_15d5esx45j().s[97]++;
  AuthenticationStateManager.prototype.subscribe = function (listener) {
    /* istanbul ignore next */
    cov_15d5esx45j().f[18]++;
    var _this =
    /* istanbul ignore next */
    (cov_15d5esx45j().s[98]++, this);
    /* istanbul ignore next */
    cov_15d5esx45j().s[99]++;
    if (this.isDestroyed) {
      /* istanbul ignore next */
      cov_15d5esx45j().b[35][0]++;
      cov_15d5esx45j().s[100]++;
      // Return a no-op unsubscribe function for destroyed instances
      return function () {
        /* istanbul ignore next */
        cov_15d5esx45j().f[19]++;
      };
    } else
    /* istanbul ignore next */
    {
      cov_15d5esx45j().b[35][1]++;
    }
    cov_15d5esx45j().s[101]++;
    this.listeners.add(listener);
    /* istanbul ignore next */
    cov_15d5esx45j().s[102]++;
    return function () {
      /* istanbul ignore next */
      cov_15d5esx45j().f[20]++;
      cov_15d5esx45j().s[103]++;
      return _this.listeners.delete(listener);
    };
  };
  /**
   * Notify all listeners of state changes
   */
  /* istanbul ignore next */
  cov_15d5esx45j().s[104]++;
  AuthenticationStateManager.prototype.notifyListeners = function () {
    /* istanbul ignore next */
    cov_15d5esx45j().f[21]++;
    var _this =
    /* istanbul ignore next */
    (cov_15d5esx45j().s[105]++, this);
    /* istanbul ignore next */
    cov_15d5esx45j().s[106]++;
    if (this.isDestroyed) {
      /* istanbul ignore next */
      cov_15d5esx45j().b[36][0]++;
      cov_15d5esx45j().s[107]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_15d5esx45j().b[36][1]++;
    }
    cov_15d5esx45j().s[108]++;
    this.stateChangeListeners.forEach(function (listener) {
      /* istanbul ignore next */
      cov_15d5esx45j().f[22]++;
      cov_15d5esx45j().s[109]++;
      return listener(_this.getState());
    });
    /* istanbul ignore next */
    cov_15d5esx45j().s[110]++;
    this.listeners.forEach(function (listener) {
      /* istanbul ignore next */
      cov_15d5esx45j().f[23]++;
      cov_15d5esx45j().s[111]++;
      return listener(_this.getState());
    });
  };
  /**
   * Update state and notify listeners
   */
  /* istanbul ignore next */
  cov_15d5esx45j().s[112]++;
  AuthenticationStateManager.prototype.setState = function (updates) {
    /* istanbul ignore next */
    cov_15d5esx45j().f[24]++;
    cov_15d5esx45j().s[113]++;
    if (this.isDestroyed) {
      /* istanbul ignore next */
      cov_15d5esx45j().b[37][0]++;
      cov_15d5esx45j().s[114]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_15d5esx45j().b[37][1]++;
    }
    var oldState =
    /* istanbul ignore next */
    (cov_15d5esx45j().s[115]++, __assign({}, this.state));
    /* istanbul ignore next */
    cov_15d5esx45j().s[116]++;
    this.state = __assign(__assign({}, this.state), updates);
    // Notify listeners
    /* istanbul ignore next */
    cov_15d5esx45j().s[117]++;
    this.notifyListeners();
    // Broadcast state change to other tabs
    /* istanbul ignore next */
    cov_15d5esx45j().s[118]++;
    this.broadcastStateChange(oldState, this.state);
  };
  /**
   * Broadcast state changes to other tabs via localStorage
   */
  /* istanbul ignore next */
  cov_15d5esx45j().s[119]++;
  AuthenticationStateManager.prototype.broadcastStateChange = function (oldState, newState) {
    /* istanbul ignore next */
    cov_15d5esx45j().f[25]++;
    var _a, _b, _c;
    /* istanbul ignore next */
    cov_15d5esx45j().s[120]++;
    if (typeof window === 'undefined') {
      /* istanbul ignore next */
      cov_15d5esx45j().b[38][0]++;
      cov_15d5esx45j().s[121]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_15d5esx45j().b[38][1]++;
    }
    // Only broadcast significant changes
    var significantChange =
    /* istanbul ignore next */
    (cov_15d5esx45j().s[122]++,
    /* istanbul ignore next */
    (cov_15d5esx45j().b[39][0]++, oldState.isAuthenticated !== newState.isAuthenticated) ||
    /* istanbul ignore next */
    (cov_15d5esx45j().b[39][1]++, (
    /* istanbul ignore next */
    (cov_15d5esx45j().b[41][0]++, (_a = oldState.user) === null) ||
    /* istanbul ignore next */
    (cov_15d5esx45j().b[41][1]++, _a === void 0) ?
    /* istanbul ignore next */
    (cov_15d5esx45j().b[40][0]++, void 0) :
    /* istanbul ignore next */
    (cov_15d5esx45j().b[40][1]++, _a.id)) !== (
    /* istanbul ignore next */
    (cov_15d5esx45j().b[43][0]++, (_b = newState.user) === null) ||
    /* istanbul ignore next */
    (cov_15d5esx45j().b[43][1]++, _b === void 0) ?
    /* istanbul ignore next */
    (cov_15d5esx45j().b[42][0]++, void 0) :
    /* istanbul ignore next */
    (cov_15d5esx45j().b[42][1]++, _b.id))) ||
    /* istanbul ignore next */
    (cov_15d5esx45j().b[39][2]++, oldState.isAdmin !== newState.isAdmin));
    /* istanbul ignore next */
    cov_15d5esx45j().s[123]++;
    if (significantChange) {
      /* istanbul ignore next */
      cov_15d5esx45j().b[44][0]++;
      cov_15d5esx45j().s[124]++;
      try {
        /* istanbul ignore next */
        cov_15d5esx45j().s[125]++;
        localStorage.setItem('auth_state_change', JSON.stringify({
          timestamp: Date.now(),
          isAuthenticated: newState.isAuthenticated,
          userId:
          /* istanbul ignore next */
          (cov_15d5esx45j().b[46][0]++, (_c = newState.user) === null) ||
          /* istanbul ignore next */
          (cov_15d5esx45j().b[46][1]++, _c === void 0) ?
          /* istanbul ignore next */
          (cov_15d5esx45j().b[45][0]++, void 0) :
          /* istanbul ignore next */
          (cov_15d5esx45j().b[45][1]++, _c.id),
          isAdmin: newState.isAdmin
        }));
        // Remove the item immediately to trigger storage event
        /* istanbul ignore next */
        cov_15d5esx45j().s[126]++;
        localStorage.removeItem('auth_state_change');
      } catch (error) {
        /* istanbul ignore next */
        cov_15d5esx45j().s[127]++;
        console.error('Failed to broadcast auth state change:', error);
      }
    } else
    /* istanbul ignore next */
    {
      cov_15d5esx45j().b[44][1]++;
    }
  };
  /**
   * Refresh authentication state from session
   */
  /* istanbul ignore next */
  cov_15d5esx45j().s[128]++;
  AuthenticationStateManager.prototype.refreshState = function () {
    /* istanbul ignore next */
    cov_15d5esx45j().f[26]++;
    cov_15d5esx45j().s[129]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_15d5esx45j().f[27]++;
      var session, isAdmin, error_1;
      var _a;
      /* istanbul ignore next */
      cov_15d5esx45j().s[130]++;
      return __generator(this, function (_b) {
        /* istanbul ignore next */
        cov_15d5esx45j().f[28]++;
        cov_15d5esx45j().s[131]++;
        switch (_b.label) {
          case 0:
            /* istanbul ignore next */
            cov_15d5esx45j().b[47][0]++;
            cov_15d5esx45j().s[132]++;
            _b.trys.push([0, 2,, 3]);
            /* istanbul ignore next */
            cov_15d5esx45j().s[133]++;
            this.setState({
              isLoading: true,
              error: null
            });
            /* istanbul ignore next */
            cov_15d5esx45j().s[134]++;
            return [4 /*yield*/, (0, react_1.getSession)()];
          case 1:
            /* istanbul ignore next */
            cov_15d5esx45j().b[47][1]++;
            cov_15d5esx45j().s[135]++;
            session = _b.sent();
            /* istanbul ignore next */
            cov_15d5esx45j().s[136]++;
            if (
            /* istanbul ignore next */
            (cov_15d5esx45j().b[50][0]++, session === null) ||
            /* istanbul ignore next */
            (cov_15d5esx45j().b[50][1]++, session === void 0) ?
            /* istanbul ignore next */
            (cov_15d5esx45j().b[49][0]++, void 0) :
            /* istanbul ignore next */
            (cov_15d5esx45j().b[49][1]++, session.user)) {
              /* istanbul ignore next */
              cov_15d5esx45j().b[48][0]++;
              cov_15d5esx45j().s[137]++;
              isAdmin =
              /* istanbul ignore next */
              (cov_15d5esx45j().b[51][0]++,
              /* istanbul ignore next */
              (cov_15d5esx45j().b[53][0]++, (_a = session.user) === null) ||
              /* istanbul ignore next */
              (cov_15d5esx45j().b[53][1]++, _a === void 0) ?
              /* istanbul ignore next */
              (cov_15d5esx45j().b[52][0]++, void 0) :
              /* istanbul ignore next */
              (cov_15d5esx45j().b[52][1]++, _a.isAdmin)) ||
              /* istanbul ignore next */
              (cov_15d5esx45j().b[51][1]++, false);
              /* istanbul ignore next */
              cov_15d5esx45j().s[138]++;
              this.setState({
                isAuthenticated: true,
                isLoading: false,
                user: session.user,
                sessionId:
                /* istanbul ignore next */
                (cov_15d5esx45j().b[54][0]++, session.sessionId) ||
                /* istanbul ignore next */
                (cov_15d5esx45j().b[54][1]++, null),
                lastActivity: Date.now(),
                isAdmin: isAdmin,
                error: null
              });
              /* istanbul ignore next */
              cov_15d5esx45j().s[139]++;
              this.updateLastActivity();
            } else {
              /* istanbul ignore next */
              cov_15d5esx45j().b[48][1]++;
              cov_15d5esx45j().s[140]++;
              this.setState({
                isAuthenticated: false,
                isLoading: false,
                user: null,
                sessionId: null,
                lastActivity: null,
                isAdmin: false,
                error: null
              });
            }
            /* istanbul ignore next */
            cov_15d5esx45j().s[141]++;
            return [3 /*break*/, 3];
          case 2:
            /* istanbul ignore next */
            cov_15d5esx45j().b[47][2]++;
            cov_15d5esx45j().s[142]++;
            error_1 = _b.sent();
            /* istanbul ignore next */
            cov_15d5esx45j().s[143]++;
            console.error('Error refreshing auth state:', error_1);
            /* istanbul ignore next */
            cov_15d5esx45j().s[144]++;
            this.setState({
              isAuthenticated: false,
              isLoading: false,
              user: null,
              sessionId: null,
              lastActivity: null,
              isAdmin: false,
              error: error_1 instanceof Error ?
              /* istanbul ignore next */
              (cov_15d5esx45j().b[55][0]++, error_1.message) :
              /* istanbul ignore next */
              (cov_15d5esx45j().b[55][1]++, 'Authentication error')
            });
            /* istanbul ignore next */
            cov_15d5esx45j().s[145]++;
            return [3 /*break*/, 3];
          case 3:
            /* istanbul ignore next */
            cov_15d5esx45j().b[47][3]++;
            cov_15d5esx45j().s[146]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Validate current session
   */
  /* istanbul ignore next */
  cov_15d5esx45j().s[147]++;
  AuthenticationStateManager.prototype.validateSession = function () {
    /* istanbul ignore next */
    cov_15d5esx45j().f[29]++;
    cov_15d5esx45j().s[148]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_15d5esx45j().f[30]++;
      var response, _a, valid, user, isAdmin, error_2;
      /* istanbul ignore next */
      cov_15d5esx45j().s[149]++;
      return __generator(this, function (_b) {
        /* istanbul ignore next */
        cov_15d5esx45j().f[31]++;
        cov_15d5esx45j().s[150]++;
        switch (_b.label) {
          case 0:
            /* istanbul ignore next */
            cov_15d5esx45j().b[56][0]++;
            cov_15d5esx45j().s[151]++;
            _b.trys.push([0, 11,, 12]);
            /* istanbul ignore next */
            cov_15d5esx45j().s[152]++;
            if (!this.checkSessionExpiry()) {
              /* istanbul ignore next */
              cov_15d5esx45j().b[57][0]++;
              cov_15d5esx45j().s[153]++;
              return [3 /*break*/, 2];
            } else
            /* istanbul ignore next */
            {
              cov_15d5esx45j().b[57][1]++;
            }
            cov_15d5esx45j().s[154]++;
            return [4 /*yield*/, this.forceLogout('Session expired')];
          case 1:
            /* istanbul ignore next */
            cov_15d5esx45j().b[56][1]++;
            cov_15d5esx45j().s[155]++;
            _b.sent();
            /* istanbul ignore next */
            cov_15d5esx45j().s[156]++;
            return [2 /*return*/, false];
          case 2:
            /* istanbul ignore next */
            cov_15d5esx45j().b[56][2]++;
            cov_15d5esx45j().s[157]++;
            return [4 /*yield*/, fetch('/api/auth/validate-session', {
              method: 'GET',
              credentials: 'same-origin',
              headers: {
                'Cache-Control': 'no-cache'
              }
            })];
          case 3:
            /* istanbul ignore next */
            cov_15d5esx45j().b[56][3]++;
            cov_15d5esx45j().s[158]++;
            response = _b.sent();
            /* istanbul ignore next */
            cov_15d5esx45j().s[159]++;
            if (!!response.ok) {
              /* istanbul ignore next */
              cov_15d5esx45j().b[58][0]++;
              cov_15d5esx45j().s[160]++;
              return [3 /*break*/, 6];
            } else
            /* istanbul ignore next */
            {
              cov_15d5esx45j().b[58][1]++;
            }
            cov_15d5esx45j().s[161]++;
            if (!(response.status === 401)) {
              /* istanbul ignore next */
              cov_15d5esx45j().b[59][0]++;
              cov_15d5esx45j().s[162]++;
              return [3 /*break*/, 5];
            } else
            /* istanbul ignore next */
            {
              cov_15d5esx45j().b[59][1]++;
            }
            cov_15d5esx45j().s[163]++;
            return [4 /*yield*/, this.forceLogout('Session invalid')];
          case 4:
            /* istanbul ignore next */
            cov_15d5esx45j().b[56][4]++;
            cov_15d5esx45j().s[164]++;
            _b.sent();
            /* istanbul ignore next */
            cov_15d5esx45j().s[165]++;
            return [2 /*return*/, false];
          case 5:
            /* istanbul ignore next */
            cov_15d5esx45j().b[56][5]++;
            cov_15d5esx45j().s[166]++;
            throw new Error("Session validation failed: ".concat(response.status));
          case 6:
            /* istanbul ignore next */
            cov_15d5esx45j().b[56][6]++;
            cov_15d5esx45j().s[167]++;
            return [4 /*yield*/, response.json()];
          case 7:
            /* istanbul ignore next */
            cov_15d5esx45j().b[56][7]++;
            cov_15d5esx45j().s[168]++;
            _a = _b.sent(), valid = _a.valid, user = _a.user, isAdmin = _a.isAdmin;
            /* istanbul ignore next */
            cov_15d5esx45j().s[169]++;
            if (!(
            /* istanbul ignore next */
            (cov_15d5esx45j().b[61][0]++, valid) &&
            /* istanbul ignore next */
            (cov_15d5esx45j().b[61][1]++, user))) {
              /* istanbul ignore next */
              cov_15d5esx45j().b[60][0]++;
              cov_15d5esx45j().s[170]++;
              return [3 /*break*/, 8];
            } else
            /* istanbul ignore next */
            {
              cov_15d5esx45j().b[60][1]++;
            }
            cov_15d5esx45j().s[171]++;
            this.setState({
              isAuthenticated: true,
              user: user,
              isAdmin:
              /* istanbul ignore next */
              (cov_15d5esx45j().b[62][0]++, isAdmin) ||
              /* istanbul ignore next */
              (cov_15d5esx45j().b[62][1]++, false),
              lastActivity: Date.now(),
              error: null
            });
            /* istanbul ignore next */
            cov_15d5esx45j().s[172]++;
            return [2 /*return*/, true];
          case 8:
            /* istanbul ignore next */
            cov_15d5esx45j().b[56][8]++;
            cov_15d5esx45j().s[173]++;
            return [4 /*yield*/, this.forceLogout('Session validation failed')];
          case 9:
            /* istanbul ignore next */
            cov_15d5esx45j().b[56][9]++;
            cov_15d5esx45j().s[174]++;
            _b.sent();
            /* istanbul ignore next */
            cov_15d5esx45j().s[175]++;
            return [2 /*return*/, false];
          case 10:
            /* istanbul ignore next */
            cov_15d5esx45j().b[56][10]++;
            cov_15d5esx45j().s[176]++;
            return [3 /*break*/, 12];
          case 11:
            /* istanbul ignore next */
            cov_15d5esx45j().b[56][11]++;
            cov_15d5esx45j().s[177]++;
            error_2 = _b.sent();
            /* istanbul ignore next */
            cov_15d5esx45j().s[178]++;
            console.error('Session validation error:', error_2);
            /* istanbul ignore next */
            cov_15d5esx45j().s[179]++;
            this.setState({
              error: error_2 instanceof Error ?
              /* istanbul ignore next */
              (cov_15d5esx45j().b[63][0]++, error_2.message) :
              /* istanbul ignore next */
              (cov_15d5esx45j().b[63][1]++, 'Session validation error')
            });
            /* istanbul ignore next */
            cov_15d5esx45j().s[180]++;
            return [2 /*return*/, false];
          case 12:
            /* istanbul ignore next */
            cov_15d5esx45j().b[56][12]++;
            cov_15d5esx45j().s[181]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Clear authentication state
   */
  /* istanbul ignore next */
  cov_15d5esx45j().s[182]++;
  AuthenticationStateManager.prototype.clearState = function () {
    /* istanbul ignore next */
    cov_15d5esx45j().f[32]++;
    cov_15d5esx45j().s[183]++;
    this.setState({
      isAuthenticated: false,
      isLoading: false,
      user: null,
      sessionId: null,
      lastActivity: null,
      isAdmin: false,
      error: null
    });
  };
  /**
   * Update last activity timestamp
   */
  /* istanbul ignore next */
  cov_15d5esx45j().s[184]++;
  AuthenticationStateManager.prototype.updateLastActivity = function () {
    /* istanbul ignore next */
    cov_15d5esx45j().f[33]++;
    var now =
    /* istanbul ignore next */
    (cov_15d5esx45j().s[185]++, Date.now());
    /* istanbul ignore next */
    cov_15d5esx45j().s[186]++;
    this.setState({
      lastActivity: now
    });
    // Store in localStorage for persistence across tabs
    /* istanbul ignore next */
    cov_15d5esx45j().s[187]++;
    if (typeof window !== 'undefined') {
      /* istanbul ignore next */
      cov_15d5esx45j().b[64][0]++;
      cov_15d5esx45j().s[188]++;
      localStorage.setItem('auth_last_activity', now.toString());
    } else
    /* istanbul ignore next */
    {
      cov_15d5esx45j().b[64][1]++;
    }
  };
  /**
   * Check if session has expired
   */
  /* istanbul ignore next */
  cov_15d5esx45j().s[189]++;
  AuthenticationStateManager.prototype.checkSessionExpiry = function () {
    /* istanbul ignore next */
    cov_15d5esx45j().f[34]++;
    var lastActivity =
    /* istanbul ignore next */
    (cov_15d5esx45j().s[190]++, this.state.lastActivity);
    /* istanbul ignore next */
    cov_15d5esx45j().s[191]++;
    if (!lastActivity) {
      /* istanbul ignore next */
      cov_15d5esx45j().b[65][0]++;
      cov_15d5esx45j().s[192]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_15d5esx45j().b[65][1]++;
    }
    var now =
    /* istanbul ignore next */
    (cov_15d5esx45j().s[193]++, Date.now());
    var timeSinceActivity =
    /* istanbul ignore next */
    (cov_15d5esx45j().s[194]++, now - lastActivity);
    // Check activity timeout
    /* istanbul ignore next */
    cov_15d5esx45j().s[195]++;
    if (timeSinceActivity > this.ACTIVITY_TIMEOUT) {
      /* istanbul ignore next */
      cov_15d5esx45j().b[66][0]++;
      cov_15d5esx45j().s[196]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_15d5esx45j().b[66][1]++;
    }
    // Check absolute session timeout
    cov_15d5esx45j().s[197]++;
    if (timeSinceActivity > this.SESSION_TIMEOUT) {
      /* istanbul ignore next */
      cov_15d5esx45j().b[67][0]++;
      cov_15d5esx45j().s[198]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_15d5esx45j().b[67][1]++;
    }
    cov_15d5esx45j().s[199]++;
    return false;
  };
  /**
   * Force logout with reason
   */
  /* istanbul ignore next */
  cov_15d5esx45j().s[200]++;
  AuthenticationStateManager.prototype.forceLogout = function (reason) {
    /* istanbul ignore next */
    cov_15d5esx45j().f[35]++;
    cov_15d5esx45j().s[201]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_15d5esx45j().f[36]++;
      var error_3;
      /* istanbul ignore next */
      cov_15d5esx45j().s[202]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_15d5esx45j().f[37]++;
        cov_15d5esx45j().s[203]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_15d5esx45j().b[68][0]++;
            cov_15d5esx45j().s[204]++;
            _a.trys.push([0, 2,, 3]);
            /* istanbul ignore next */
            cov_15d5esx45j().s[205]++;
            console.log('Force logout:', reason);
            // Clear local state first
            /* istanbul ignore next */
            cov_15d5esx45j().s[206]++;
            this.clearState();
            // Clear localStorage
            /* istanbul ignore next */
            cov_15d5esx45j().s[207]++;
            if (typeof window !== 'undefined') {
              /* istanbul ignore next */
              cov_15d5esx45j().b[69][0]++;
              cov_15d5esx45j().s[208]++;
              localStorage.removeItem('auth_last_activity');
              /* istanbul ignore next */
              cov_15d5esx45j().s[209]++;
              localStorage.removeItem('auth_session_id');
            } else
            /* istanbul ignore next */
            {
              cov_15d5esx45j().b[69][1]++;
            }
            // Sign out from NextAuth
            cov_15d5esx45j().s[210]++;
            return [4 /*yield*/, (0, react_1.signOut)({
              redirect: false,
              callbackUrl: '/login'
            })];
          case 1:
            /* istanbul ignore next */
            cov_15d5esx45j().b[68][1]++;
            cov_15d5esx45j().s[211]++;
            // Sign out from NextAuth
            _a.sent();
            // Set error state if reason provided
            /* istanbul ignore next */
            cov_15d5esx45j().s[212]++;
            if (reason) {
              /* istanbul ignore next */
              cov_15d5esx45j().b[70][0]++;
              cov_15d5esx45j().s[213]++;
              this.setState({
                error: reason
              });
            } else
            /* istanbul ignore next */
            {
              cov_15d5esx45j().b[70][1]++;
            }
            cov_15d5esx45j().s[214]++;
            return [3 /*break*/, 3];
          case 2:
            /* istanbul ignore next */
            cov_15d5esx45j().b[68][2]++;
            cov_15d5esx45j().s[215]++;
            error_3 = _a.sent();
            /* istanbul ignore next */
            cov_15d5esx45j().s[216]++;
            console.error('Error during force logout:', error_3);
            /* istanbul ignore next */
            cov_15d5esx45j().s[217]++;
            this.setState({
              error: error_3 instanceof Error ?
              /* istanbul ignore next */
              (cov_15d5esx45j().b[71][0]++, error_3.message) :
              /* istanbul ignore next */
              (cov_15d5esx45j().b[71][1]++, 'Logout error')
            });
            /* istanbul ignore next */
            cov_15d5esx45j().s[218]++;
            return [3 /*break*/, 3];
          case 3:
            /* istanbul ignore next */
            cov_15d5esx45j().b[68][3]++;
            cov_15d5esx45j().s[219]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Update admin status via API call (optional, called separately)
   */
  /* istanbul ignore next */
  cov_15d5esx45j().s[220]++;
  AuthenticationStateManager.prototype.updateAdminStatus = function () {
    /* istanbul ignore next */
    cov_15d5esx45j().f[38]++;
    cov_15d5esx45j().s[221]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_15d5esx45j().f[39]++;
      var currentState, response, isAdmin, error_4;
      var _a;
      /* istanbul ignore next */
      cov_15d5esx45j().s[222]++;
      return __generator(this, function (_b) {
        /* istanbul ignore next */
        cov_15d5esx45j().f[40]++;
        cov_15d5esx45j().s[223]++;
        switch (_b.label) {
          case 0:
            /* istanbul ignore next */
            cov_15d5esx45j().b[72][0]++;
            cov_15d5esx45j().s[224]++;
            currentState = this.getState();
            /* istanbul ignore next */
            cov_15d5esx45j().s[225]++;
            if (
            /* istanbul ignore next */
            (cov_15d5esx45j().b[74][0]++, !currentState.isAuthenticated) ||
            /* istanbul ignore next */
            (cov_15d5esx45j().b[74][1]++, !(
            /* istanbul ignore next */
            (cov_15d5esx45j().b[76][0]++, (_a = currentState.user) === null) ||
            /* istanbul ignore next */
            (cov_15d5esx45j().b[76][1]++, _a === void 0) ?
            /* istanbul ignore next */
            (cov_15d5esx45j().b[75][0]++, void 0) :
            /* istanbul ignore next */
            (cov_15d5esx45j().b[75][1]++, _a.id)))) {
              /* istanbul ignore next */
              cov_15d5esx45j().b[73][0]++;
              cov_15d5esx45j().s[226]++;
              return [2 /*return*/];
            } else
            /* istanbul ignore next */
            {
              cov_15d5esx45j().b[73][1]++;
            }
            cov_15d5esx45j().s[227]++;
            _b.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_15d5esx45j().b[72][1]++;
            cov_15d5esx45j().s[228]++;
            _b.trys.push([1, 5,, 6]);
            /* istanbul ignore next */
            cov_15d5esx45j().s[229]++;
            return [4 /*yield*/, fetch("/api/auth/check-admin?userId=".concat(currentState.user.id), {
              method: 'GET',
              credentials: 'same-origin',
              headers: {
                'Content-Type': 'application/json'
              }
            })];
          case 2:
            /* istanbul ignore next */
            cov_15d5esx45j().b[72][2]++;
            cov_15d5esx45j().s[230]++;
            response = _b.sent();
            /* istanbul ignore next */
            cov_15d5esx45j().s[231]++;
            if (!response.ok) {
              /* istanbul ignore next */
              cov_15d5esx45j().b[77][0]++;
              cov_15d5esx45j().s[232]++;
              return [3 /*break*/, 4];
            } else
            /* istanbul ignore next */
            {
              cov_15d5esx45j().b[77][1]++;
            }
            cov_15d5esx45j().s[233]++;
            return [4 /*yield*/, response.json()];
          case 3:
            /* istanbul ignore next */
            cov_15d5esx45j().b[72][3]++;
            cov_15d5esx45j().s[234]++;
            isAdmin = _b.sent().isAdmin;
            /* istanbul ignore next */
            cov_15d5esx45j().s[235]++;
            this.setState({
              isAdmin:
              /* istanbul ignore next */
              (cov_15d5esx45j().b[78][0]++, isAdmin) ||
              /* istanbul ignore next */
              (cov_15d5esx45j().b[78][1]++, false)
            });
            /* istanbul ignore next */
            cov_15d5esx45j().s[236]++;
            _b.label = 4;
          case 4:
            /* istanbul ignore next */
            cov_15d5esx45j().b[72][4]++;
            cov_15d5esx45j().s[237]++;
            return [3 /*break*/, 6];
          case 5:
            /* istanbul ignore next */
            cov_15d5esx45j().b[72][5]++;
            cov_15d5esx45j().s[238]++;
            error_4 = _b.sent();
            /* istanbul ignore next */
            cov_15d5esx45j().s[239]++;
            console.error('Error updating admin status:', error_4);
            /* istanbul ignore next */
            cov_15d5esx45j().s[240]++;
            return [3 /*break*/, 6];
          case 6:
            /* istanbul ignore next */
            cov_15d5esx45j().b[72][6]++;
            cov_15d5esx45j().s[241]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Initialize session monitoring
   */
  /* istanbul ignore next */
  cov_15d5esx45j().s[242]++;
  AuthenticationStateManager.prototype.initializeSessionMonitoring = function () {
    /* istanbul ignore next */
    cov_15d5esx45j().f[41]++;
    var _this =
    /* istanbul ignore next */
    (cov_15d5esx45j().s[243]++, this);
    // Prevent double initialization
    /* istanbul ignore next */
    cov_15d5esx45j().s[244]++;
    if (
    /* istanbul ignore next */
    (cov_15d5esx45j().b[80][0]++, this.sessionCheckInterval) ||
    /* istanbul ignore next */
    (cov_15d5esx45j().b[80][1]++, this.isDestroyed)) {
      /* istanbul ignore next */
      cov_15d5esx45j().b[79][0]++;
      cov_15d5esx45j().s[245]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_15d5esx45j().b[79][1]++;
    }
    // Initial state refresh
    cov_15d5esx45j().s[246]++;
    this.refreshState();
    // Set up periodic session validation
    /* istanbul ignore next */
    cov_15d5esx45j().s[247]++;
    this.sessionCheckInterval = setInterval(function () {
      /* istanbul ignore next */
      cov_15d5esx45j().f[42]++;
      cov_15d5esx45j().s[248]++;
      if (_this.isDestroyed) {
        /* istanbul ignore next */
        cov_15d5esx45j().b[81][0]++;
        cov_15d5esx45j().s[249]++;
        return;
      } else
      /* istanbul ignore next */
      {
        cov_15d5esx45j().b[81][1]++;
      }
      cov_15d5esx45j().s[250]++;
      if (_this.state.isAuthenticated) {
        /* istanbul ignore next */
        cov_15d5esx45j().b[82][0]++;
        cov_15d5esx45j().s[251]++;
        _this.validateSession();
      } else
      /* istanbul ignore next */
      {
        cov_15d5esx45j().b[82][1]++;
      }
    }, this.SESSION_CHECK_INTERVAL);
    // Listen for storage events (cross-tab synchronization)
    /* istanbul ignore next */
    cov_15d5esx45j().s[252]++;
    if (typeof window !== 'undefined') {
      /* istanbul ignore next */
      cov_15d5esx45j().b[83][0]++;
      cov_15d5esx45j().s[253]++;
      this.storageEventListener = function (event) {
        /* istanbul ignore next */
        cov_15d5esx45j().f[43]++;
        cov_15d5esx45j().s[254]++;
        if (_this.isDestroyed) {
          /* istanbul ignore next */
          cov_15d5esx45j().b[84][0]++;
          cov_15d5esx45j().s[255]++;
          return;
        } else
        /* istanbul ignore next */
        {
          cov_15d5esx45j().b[84][1]++;
        }
        cov_15d5esx45j().s[256]++;
        if (event.key === 'auth_last_activity') {
          /* istanbul ignore next */
          cov_15d5esx45j().b[85][0]++;
          var lastActivity =
          /* istanbul ignore next */
          (cov_15d5esx45j().s[257]++, event.newValue ?
          /* istanbul ignore next */
          (cov_15d5esx45j().b[86][0]++, parseInt(event.newValue)) :
          /* istanbul ignore next */
          (cov_15d5esx45j().b[86][1]++, null));
          /* istanbul ignore next */
          cov_15d5esx45j().s[258]++;
          if (lastActivity) {
            /* istanbul ignore next */
            cov_15d5esx45j().b[87][0]++;
            cov_15d5esx45j().s[259]++;
            _this.setState({
              lastActivity: lastActivity
            });
          } else
          /* istanbul ignore next */
          {
            cov_15d5esx45j().b[87][1]++;
          }
        } else
        /* istanbul ignore next */
        {
          cov_15d5esx45j().b[85][1]++;
        }
      };
      /* istanbul ignore next */
      cov_15d5esx45j().s[260]++;
      window.addEventListener('storage', this.storageEventListener);
      // Listen for focus events to refresh state
      /* istanbul ignore next */
      cov_15d5esx45j().s[261]++;
      this.focusEventListener = function () {
        /* istanbul ignore next */
        cov_15d5esx45j().f[44]++;
        cov_15d5esx45j().s[262]++;
        if (_this.isDestroyed) {
          /* istanbul ignore next */
          cov_15d5esx45j().b[88][0]++;
          cov_15d5esx45j().s[263]++;
          return;
        } else
        /* istanbul ignore next */
        {
          cov_15d5esx45j().b[88][1]++;
        }
        cov_15d5esx45j().s[264]++;
        if (_this.state.isAuthenticated) {
          /* istanbul ignore next */
          cov_15d5esx45j().b[89][0]++;
          cov_15d5esx45j().s[265]++;
          _this.refreshState();
        } else
        /* istanbul ignore next */
        {
          cov_15d5esx45j().b[89][1]++;
        }
      };
      /* istanbul ignore next */
      cov_15d5esx45j().s[266]++;
      window.addEventListener('focus', this.focusEventListener);
      // Listen for beforeunload to update activity
      /* istanbul ignore next */
      cov_15d5esx45j().s[267]++;
      this.beforeUnloadEventListener = function () {
        /* istanbul ignore next */
        cov_15d5esx45j().f[45]++;
        cov_15d5esx45j().s[268]++;
        if (_this.isDestroyed) {
          /* istanbul ignore next */
          cov_15d5esx45j().b[90][0]++;
          cov_15d5esx45j().s[269]++;
          return;
        } else
        /* istanbul ignore next */
        {
          cov_15d5esx45j().b[90][1]++;
        }
        cov_15d5esx45j().s[270]++;
        _this.updateLastActivity();
      };
      /* istanbul ignore next */
      cov_15d5esx45j().s[271]++;
      window.addEventListener('beforeunload', this.beforeUnloadEventListener);
    } else
    /* istanbul ignore next */
    {
      cov_15d5esx45j().b[83][1]++;
    }
  };
  /**
   * Add state change listener (alias for subscribe)
   */
  /* istanbul ignore next */
  cov_15d5esx45j().s[272]++;
  AuthenticationStateManager.prototype.addStateChangeListener = function (listener) {
    /* istanbul ignore next */
    cov_15d5esx45j().f[46]++;
    cov_15d5esx45j().s[273]++;
    return this.subscribe(listener);
  };
  /**
   * Remove state change listener
   */
  /* istanbul ignore next */
  cov_15d5esx45j().s[274]++;
  AuthenticationStateManager.prototype.removeStateChangeListener = function (listener) {
    /* istanbul ignore next */
    cov_15d5esx45j().f[47]++;
    cov_15d5esx45j().s[275]++;
    this.listeners.delete(listener);
  };
  /**
   * Cleanup resources
   */
  /* istanbul ignore next */
  cov_15d5esx45j().s[276]++;
  AuthenticationStateManager.prototype.destroy = function () {
    /* istanbul ignore next */
    cov_15d5esx45j().f[48]++;
    cov_15d5esx45j().s[277]++;
    // Mark as destroyed to prevent further operations
    this.isDestroyed = true;
    // Clear interval
    /* istanbul ignore next */
    cov_15d5esx45j().s[278]++;
    if (this.sessionCheckInterval) {
      /* istanbul ignore next */
      cov_15d5esx45j().b[91][0]++;
      cov_15d5esx45j().s[279]++;
      clearInterval(this.sessionCheckInterval);
      /* istanbul ignore next */
      cov_15d5esx45j().s[280]++;
      this.sessionCheckInterval = null;
    } else
    /* istanbul ignore next */
    {
      cov_15d5esx45j().b[91][1]++;
    }
    // Remove event listeners
    cov_15d5esx45j().s[281]++;
    if (typeof window !== 'undefined') {
      /* istanbul ignore next */
      cov_15d5esx45j().b[92][0]++;
      cov_15d5esx45j().s[282]++;
      if (this.storageEventListener) {
        /* istanbul ignore next */
        cov_15d5esx45j().b[93][0]++;
        cov_15d5esx45j().s[283]++;
        window.removeEventListener('storage', this.storageEventListener);
        /* istanbul ignore next */
        cov_15d5esx45j().s[284]++;
        this.storageEventListener = null;
      } else
      /* istanbul ignore next */
      {
        cov_15d5esx45j().b[93][1]++;
      }
      cov_15d5esx45j().s[285]++;
      if (this.focusEventListener) {
        /* istanbul ignore next */
        cov_15d5esx45j().b[94][0]++;
        cov_15d5esx45j().s[286]++;
        window.removeEventListener('focus', this.focusEventListener);
        /* istanbul ignore next */
        cov_15d5esx45j().s[287]++;
        this.focusEventListener = null;
      } else
      /* istanbul ignore next */
      {
        cov_15d5esx45j().b[94][1]++;
      }
      cov_15d5esx45j().s[288]++;
      if (this.beforeUnloadEventListener) {
        /* istanbul ignore next */
        cov_15d5esx45j().b[95][0]++;
        cov_15d5esx45j().s[289]++;
        window.removeEventListener('beforeunload', this.beforeUnloadEventListener);
        /* istanbul ignore next */
        cov_15d5esx45j().s[290]++;
        this.beforeUnloadEventListener = null;
      } else
      /* istanbul ignore next */
      {
        cov_15d5esx45j().b[95][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_15d5esx45j().b[92][1]++;
    }
    // Clear listeners
    cov_15d5esx45j().s[291]++;
    this.listeners.clear();
    /* istanbul ignore next */
    cov_15d5esx45j().s[292]++;
    this.stateChangeListeners.clear();
  };
  /* istanbul ignore next */
  cov_15d5esx45j().s[293]++;
  return AuthenticationStateManager;
}());
// Singleton instance
var authStateManager =
/* istanbul ignore next */
(cov_15d5esx45j().s[294]++, null);
/**
 * Get the global authentication state manager instance
 */
function getAuthStateManager() {
  /* istanbul ignore next */
  cov_15d5esx45j().f[49]++;
  cov_15d5esx45j().s[295]++;
  if (!authStateManager) {
    /* istanbul ignore next */
    cov_15d5esx45j().b[96][0]++;
    cov_15d5esx45j().s[296]++;
    authStateManager = new AuthenticationStateManager();
  } else
  /* istanbul ignore next */
  {
    cov_15d5esx45j().b[96][1]++;
  }
  cov_15d5esx45j().s[297]++;
  return authStateManager;
}
/**
 * Destroy the global authentication state manager instance
 * This should be called when the application is shutting down
 */
function destroyAuthStateManager() {
  /* istanbul ignore next */
  cov_15d5esx45j().f[50]++;
  cov_15d5esx45j().s[298]++;
  if (authStateManager) {
    /* istanbul ignore next */
    cov_15d5esx45j().b[97][0]++;
    cov_15d5esx45j().s[299]++;
    authStateManager.destroy();
    /* istanbul ignore next */
    cov_15d5esx45j().s[300]++;
    authStateManager = null;
  } else
  /* istanbul ignore next */
  {
    cov_15d5esx45j().b[97][1]++;
  }
}
// Automatically cleanup on page unload to prevent memory leaks
/* istanbul ignore next */
cov_15d5esx45j().s[301]++;
if (typeof window !== 'undefined') {
  /* istanbul ignore next */
  cov_15d5esx45j().b[98][0]++;
  cov_15d5esx45j().s[302]++;
  window.addEventListener('beforeunload', function () {
    /* istanbul ignore next */
    cov_15d5esx45j().f[51]++;
    cov_15d5esx45j().s[303]++;
    destroyAuthStateManager();
  });
  // Also cleanup on page hide (for mobile browsers)
  /* istanbul ignore next */
  cov_15d5esx45j().s[304]++;
  window.addEventListener('pagehide', function () {
    /* istanbul ignore next */
    cov_15d5esx45j().f[52]++;
    cov_15d5esx45j().s[305]++;
    destroyAuthStateManager();
  });
} else
/* istanbul ignore next */
{
  cov_15d5esx45j().b[98][1]++;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfMTVkNWVzeDQ1aiIsInBhdGgiLCJoYXNoIiwiZ2xvYmFsIiwiRnVuY3Rpb24iLCJnY3YiLCJjb3ZlcmFnZURhdGEiLCJzdGF0ZW1lbnRNYXAiLCJzdGFydCIsImxpbmUiLCJjb2x1bW4iLCJlbmQiLCJmbk1hcCIsIm5hbWUiLCJkZWNsIiwibG9jIiwiYnJhbmNoTWFwIiwidHlwZSIsImxvY2F0aW9ucyIsInVuZGVmaW5lZCIsInMiLCJmIiwiYiIsImlucHV0U291cmNlTWFwIiwiZmlsZSIsIm1hcHBpbmdzIiwibmFtZXMiLCJzb3VyY2VzIiwic291cmNlc0NvbnRlbnQiLCJ2ZXJzaW9uIiwiX2NvdmVyYWdlU2NoZW1hIiwiY292ZXJhZ2UiLCJhY3R1YWxDb3ZlcmFnZSIsImV4cG9ydHMiLCJnZXRBdXRoU3RhdGVNYW5hZ2VyIiwiZGVzdHJveUF1dGhTdGF0ZU1hbmFnZXIiLCJyZWFjdF8xIiwicmVxdWlyZSIsIkF1dGhlbnRpY2F0aW9uU3RhdGVNYW5hZ2VyIiwic3RhdGUiLCJpc0F1dGhlbnRpY2F0ZWQiLCJpc0xvYWRpbmciLCJ1c2VyIiwic2Vzc2lvbklkIiwibGFzdEFjdGl2aXR5IiwiaXNBZG1pbiIsImVycm9yIiwic3RhdGVDaGFuZ2VMaXN0ZW5lcnMiLCJTZXQiLCJsaXN0ZW5lcnMiLCJzZXNzaW9uQ2hlY2tJbnRlcnZhbCIsIlNFU1NJT05fQ0hFQ0tfSU5URVJWQUwiLCJTRVNTSU9OX1RJTUVPVVQiLCJBQ1RJVklUWV9USU1FT1VUIiwic3RvcmFnZUV2ZW50TGlzdGVuZXIiLCJmb2N1c0V2ZW50TGlzdGVuZXIiLCJiZWZvcmVVbmxvYWRFdmVudExpc3RlbmVyIiwiaXNEZXN0cm95ZWQiLCJpbml0aWFsaXplU2Vzc2lvbk1vbml0b3JpbmciLCJwcm90b3R5cGUiLCJnZXRTdGF0ZSIsIl9fYXNzaWduIiwic3Vic2NyaWJlIiwibGlzdGVuZXIiLCJfdGhpcyIsImFkZCIsImRlbGV0ZSIsIm5vdGlmeUxpc3RlbmVycyIsImZvckVhY2giLCJzZXRTdGF0ZSIsInVwZGF0ZXMiLCJvbGRTdGF0ZSIsImJyb2FkY2FzdFN0YXRlQ2hhbmdlIiwibmV3U3RhdGUiLCJ3aW5kb3ciLCJzaWduaWZpY2FudENoYW5nZSIsIl9hIiwiaWQiLCJfYiIsImxvY2FsU3RvcmFnZSIsInNldEl0ZW0iLCJKU09OIiwic3RyaW5naWZ5IiwidGltZXN0YW1wIiwiRGF0ZSIsIm5vdyIsInVzZXJJZCIsIl9jIiwicmVtb3ZlSXRlbSIsImNvbnNvbGUiLCJyZWZyZXNoU3RhdGUiLCJQcm9taXNlIiwiZ2V0U2Vzc2lvbiIsInNlc3Npb24iLCJzZW50IiwidXBkYXRlTGFzdEFjdGl2aXR5IiwiZXJyb3JfMSIsIkVycm9yIiwibWVzc2FnZSIsInZhbGlkYXRlU2Vzc2lvbiIsImNoZWNrU2Vzc2lvbkV4cGlyeSIsImZvcmNlTG9nb3V0IiwiZmV0Y2giLCJtZXRob2QiLCJjcmVkZW50aWFscyIsImhlYWRlcnMiLCJyZXNwb25zZSIsIm9rIiwic3RhdHVzIiwiY29uY2F0IiwianNvbiIsInZhbGlkIiwiZXJyb3JfMiIsImNsZWFyU3RhdGUiLCJ0b1N0cmluZyIsInRpbWVTaW5jZUFjdGl2aXR5IiwicmVhc29uIiwibG9nIiwic2lnbk91dCIsInJlZGlyZWN0IiwiY2FsbGJhY2tVcmwiLCJlcnJvcl8zIiwidXBkYXRlQWRtaW5TdGF0dXMiLCJjdXJyZW50U3RhdGUiLCJlcnJvcl80Iiwic2V0SW50ZXJ2YWwiLCJldmVudCIsImtleSIsIm5ld1ZhbHVlIiwicGFyc2VJbnQiLCJhZGRFdmVudExpc3RlbmVyIiwiYWRkU3RhdGVDaGFuZ2VMaXN0ZW5lciIsInJlbW92ZVN0YXRlQ2hhbmdlTGlzdGVuZXIiLCJkZXN0cm95IiwiY2xlYXJJbnRlcnZhbCIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJjbGVhciIsImF1dGhTdGF0ZU1hbmFnZXIiXSwic291cmNlcyI6WyIvVXNlcnMvZGQ2MC9mYWFmby9mYWFmby9mYWFmby1jYXJlZXItcGxhdGZvcm0vc3JjL2xpYi9hdXRoLXN0YXRlLW1hbmFnZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBVbmlmaWVkIEF1dGhlbnRpY2F0aW9uIFN0YXRlIE1hbmFnZXJcbiAqIEVuc3VyZXMgY29uc2lzdGVudCBhdXRoZW50aWNhdGlvbiBzdGF0ZSBhY3Jvc3MgdGhlIGFwcGxpY2F0aW9uXG4gKi9cblxuaW1wb3J0IHsgZ2V0U2Vzc2lvbiwgc2lnbk91dCB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XG5pbXBvcnQgeyBVc2VyIH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG5leHBvcnQgaW50ZXJmYWNlIEF1dGhTdGF0ZSB7XG4gIGlzQXV0aGVudGljYXRlZDogYm9vbGVhbjtcbiAgaXNMb2FkaW5nOiBib29sZWFuO1xuICB1c2VyOiBVc2VyIHwgbnVsbDtcbiAgc2Vzc2lvbklkOiBzdHJpbmcgfCBudWxsO1xuICBsYXN0QWN0aXZpdHk6IG51bWJlciB8IG51bGw7XG4gIGlzQWRtaW46IGJvb2xlYW47XG4gIGVycm9yOiBzdHJpbmcgfCBudWxsO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEF1dGhTdGF0ZU1hbmFnZXIge1xuICBnZXRTdGF0ZSgpOiBBdXRoU3RhdGU7XG4gIHJlZnJlc2hTdGF0ZSgpOiBQcm9taXNlPHZvaWQ+O1xuICB2YWxpZGF0ZVNlc3Npb24oKTogUHJvbWlzZTxib29sZWFuPjtcbiAgY2xlYXJTdGF0ZSgpOiB2b2lkO1xuICB1cGRhdGVMYXN0QWN0aXZpdHkoKTogdm9pZDtcbiAgY2hlY2tTZXNzaW9uRXhwaXJ5KCk6IGJvb2xlYW47XG4gIGZvcmNlTG9nb3V0KHJlYXNvbj86IHN0cmluZyk6IFByb21pc2U8dm9pZD47XG4gIGFkZFN0YXRlQ2hhbmdlTGlzdGVuZXIobGlzdGVuZXI6IChzdGF0ZTogQXV0aFN0YXRlKSA9PiB2b2lkKTogKCkgPT4gdm9pZDtcbiAgcmVtb3ZlU3RhdGVDaGFuZ2VMaXN0ZW5lcihsaXN0ZW5lcjogKHN0YXRlOiBBdXRoU3RhdGUpID0+IHZvaWQpOiB2b2lkO1xuICB1cGRhdGVBZG1pblN0YXR1cygpOiBQcm9taXNlPHZvaWQ+O1xufVxuXG5jbGFzcyBBdXRoZW50aWNhdGlvblN0YXRlTWFuYWdlciBpbXBsZW1lbnRzIEF1dGhTdGF0ZU1hbmFnZXIge1xuICBwcml2YXRlIHN0YXRlOiBBdXRoU3RhdGUgPSB7XG4gICAgaXNBdXRoZW50aWNhdGVkOiBmYWxzZSxcbiAgICBpc0xvYWRpbmc6IHRydWUsXG4gICAgdXNlcjogbnVsbCxcbiAgICBzZXNzaW9uSWQ6IG51bGwsXG4gICAgbGFzdEFjdGl2aXR5OiBudWxsLFxuICAgIGlzQWRtaW46IGZhbHNlLFxuICAgIGVycm9yOiBudWxsXG4gIH07XG5cbiAgcHJpdmF0ZSBzdGF0ZUNoYW5nZUxpc3RlbmVyczogU2V0PChzdGF0ZTogQXV0aFN0YXRlKSA9PiB2b2lkPiA9IG5ldyBTZXQoKTtcblxuICBwcml2YXRlIGxpc3RlbmVyczogU2V0PChzdGF0ZTogQXV0aFN0YXRlKSA9PiB2b2lkPiA9IG5ldyBTZXQoKTtcbiAgcHJpdmF0ZSBzZXNzaW9uQ2hlY2tJbnRlcnZhbDogTm9kZUpTLlRpbWVvdXQgfCBudWxsID0gbnVsbDtcbiAgcHJpdmF0ZSByZWFkb25seSBTRVNTSU9OX0NIRUNLX0lOVEVSVkFMID0gMTAgKiA2MCAqIDEwMDA7IC8vIDEwIG1pbnV0ZXMgKHJlZHVjZWQgZnJvbSAxIG1pbnV0ZSlcbiAgcHJpdmF0ZSByZWFkb25seSBTRVNTSU9OX1RJTUVPVVQgPSAzMCAqIDI0ICogNjAgKiA2MCAqIDEwMDA7IC8vIDMwIGRheXMgaW4gbXNcbiAgcHJpdmF0ZSByZWFkb25seSBBQ1RJVklUWV9USU1FT1VUID0gMjQgKiA2MCAqIDYwICogMTAwMDsgLy8gMjQgaG91cnMgaW4gbXNcblxuICAvLyBTdG9yZSBldmVudCBsaXN0ZW5lciByZWZlcmVuY2VzIGZvciBwcm9wZXIgY2xlYW51cFxuICBwcml2YXRlIHN0b3JhZ2VFdmVudExpc3RlbmVyOiAoKGV2ZW50OiBTdG9yYWdlRXZlbnQpID0+IHZvaWQpIHwgbnVsbCA9IG51bGw7XG4gIHByaXZhdGUgZm9jdXNFdmVudExpc3RlbmVyOiAoKCkgPT4gdm9pZCkgfCBudWxsID0gbnVsbDtcbiAgcHJpdmF0ZSBiZWZvcmVVbmxvYWRFdmVudExpc3RlbmVyOiAoKCkgPT4gdm9pZCkgfCBudWxsID0gbnVsbDtcbiAgcHJpdmF0ZSBpc0Rlc3Ryb3llZCA9IGZhbHNlO1xuXG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMuaW5pdGlhbGl6ZVNlc3Npb25Nb25pdG9yaW5nKCk7XG4gIH1cblxuICAvKipcbiAgICogR2V0IGN1cnJlbnQgYXV0aGVudGljYXRpb24gc3RhdGVcbiAgICovXG4gIGdldFN0YXRlKCk6IEF1dGhTdGF0ZSB7XG4gICAgcmV0dXJuIHsgLi4udGhpcy5zdGF0ZSB9O1xuICB9XG5cbiAgLyoqXG4gICAqIFN1YnNjcmliZSB0byBzdGF0ZSBjaGFuZ2VzXG4gICAqL1xuICBzdWJzY3JpYmUobGlzdGVuZXI6IChzdGF0ZTogQXV0aFN0YXRlKSA9PiB2b2lkKTogKCkgPT4gdm9pZCB7XG4gICAgaWYgKHRoaXMuaXNEZXN0cm95ZWQpIHtcbiAgICAgIC8vIFJldHVybiBhIG5vLW9wIHVuc3Vic2NyaWJlIGZ1bmN0aW9uIGZvciBkZXN0cm95ZWQgaW5zdGFuY2VzXG4gICAgICByZXR1cm4gKCkgPT4ge307XG4gICAgfVxuICAgIHRoaXMubGlzdGVuZXJzLmFkZChsaXN0ZW5lcik7XG4gICAgcmV0dXJuICgpID0+IHRoaXMubGlzdGVuZXJzLmRlbGV0ZShsaXN0ZW5lcik7XG4gIH1cblxuICAvKipcbiAgICogTm90aWZ5IGFsbCBsaXN0ZW5lcnMgb2Ygc3RhdGUgY2hhbmdlc1xuICAgKi9cbiAgcHJpdmF0ZSBub3RpZnlMaXN0ZW5lcnMoKTogdm9pZCB7XG4gICAgaWYgKHRoaXMuaXNEZXN0cm95ZWQpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgdGhpcy5zdGF0ZUNoYW5nZUxpc3RlbmVycy5mb3JFYWNoKGxpc3RlbmVyID0+IGxpc3RlbmVyKHRoaXMuZ2V0U3RhdGUoKSkpO1xuICAgIHRoaXMubGlzdGVuZXJzLmZvckVhY2gobGlzdGVuZXIgPT4gbGlzdGVuZXIodGhpcy5nZXRTdGF0ZSgpKSk7XG4gIH1cblxuICAvKipcbiAgICogVXBkYXRlIHN0YXRlIGFuZCBub3RpZnkgbGlzdGVuZXJzXG4gICAqL1xuICBwcml2YXRlIHNldFN0YXRlKHVwZGF0ZXM6IFBhcnRpYWw8QXV0aFN0YXRlPik6IHZvaWQge1xuICAgIGlmICh0aGlzLmlzRGVzdHJveWVkKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc3Qgb2xkU3RhdGUgPSB7IC4uLnRoaXMuc3RhdGUgfTtcbiAgICB0aGlzLnN0YXRlID0geyAuLi50aGlzLnN0YXRlLCAuLi51cGRhdGVzIH07XG5cbiAgICAvLyBOb3RpZnkgbGlzdGVuZXJzXG4gICAgdGhpcy5ub3RpZnlMaXN0ZW5lcnMoKTtcblxuICAgIC8vIEJyb2FkY2FzdCBzdGF0ZSBjaGFuZ2UgdG8gb3RoZXIgdGFic1xuICAgIHRoaXMuYnJvYWRjYXN0U3RhdGVDaGFuZ2Uob2xkU3RhdGUsIHRoaXMuc3RhdGUpO1xuICB9XG5cbiAgLyoqXG4gICAqIEJyb2FkY2FzdCBzdGF0ZSBjaGFuZ2VzIHRvIG90aGVyIHRhYnMgdmlhIGxvY2FsU3RvcmFnZVxuICAgKi9cbiAgcHJpdmF0ZSBicm9hZGNhc3RTdGF0ZUNoYW5nZShvbGRTdGF0ZTogQXV0aFN0YXRlLCBuZXdTdGF0ZTogQXV0aFN0YXRlKTogdm9pZCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm47XG5cbiAgICAvLyBPbmx5IGJyb2FkY2FzdCBzaWduaWZpY2FudCBjaGFuZ2VzXG4gICAgY29uc3Qgc2lnbmlmaWNhbnRDaGFuZ2UgPVxuICAgICAgb2xkU3RhdGUuaXNBdXRoZW50aWNhdGVkICE9PSBuZXdTdGF0ZS5pc0F1dGhlbnRpY2F0ZWQgfHxcbiAgICAgIG9sZFN0YXRlLnVzZXI/LmlkICE9PSBuZXdTdGF0ZS51c2VyPy5pZCB8fFxuICAgICAgb2xkU3RhdGUuaXNBZG1pbiAhPT0gbmV3U3RhdGUuaXNBZG1pbjtcblxuICAgIGlmIChzaWduaWZpY2FudENoYW5nZSkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2F1dGhfc3RhdGVfY2hhbmdlJywgSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgICAgICBpc0F1dGhlbnRpY2F0ZWQ6IG5ld1N0YXRlLmlzQXV0aGVudGljYXRlZCxcbiAgICAgICAgICB1c2VySWQ6IG5ld1N0YXRlLnVzZXI/LmlkLFxuICAgICAgICAgIGlzQWRtaW46IG5ld1N0YXRlLmlzQWRtaW5cbiAgICAgICAgfSkpO1xuXG4gICAgICAgIC8vIFJlbW92ZSB0aGUgaXRlbSBpbW1lZGlhdGVseSB0byB0cmlnZ2VyIHN0b3JhZ2UgZXZlbnRcbiAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2F1dGhfc3RhdGVfY2hhbmdlJyk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gYnJvYWRjYXN0IGF1dGggc3RhdGUgY2hhbmdlOicsIGVycm9yKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogUmVmcmVzaCBhdXRoZW50aWNhdGlvbiBzdGF0ZSBmcm9tIHNlc3Npb25cbiAgICovXG4gIGFzeW5jIHJlZnJlc2hTdGF0ZSgpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICB0cnkge1xuICAgICAgdGhpcy5zZXRTdGF0ZSh7IGlzTG9hZGluZzogdHJ1ZSwgZXJyb3I6IG51bGwgfSk7XG5cbiAgICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCBnZXRTZXNzaW9uKCk7XG4gICAgICBcbiAgICAgIGlmIChzZXNzaW9uPy51c2VyKSB7XG4gICAgICAgIC8vIEdldCBhZG1pbiBzdGF0dXMgZnJvbSBzZXNzaW9uIGlmIGF2YWlsYWJsZSwgb3RoZXJ3aXNlIGRlZmF1bHQgdG8gZmFsc2VcbiAgICAgICAgLy8gQWRtaW4gc3RhdHVzIHdpbGwgYmUgdXBkYXRlZCBzZXBhcmF0ZWx5IHZpYSBBUEkgY2FsbCBpZiBuZWVkZWRcbiAgICAgICAgY29uc3QgaXNBZG1pbiA9IChzZXNzaW9uIGFzIGFueSkudXNlcj8uaXNBZG1pbiB8fCBmYWxzZTtcblxuICAgICAgICB0aGlzLnNldFN0YXRlKHtcbiAgICAgICAgICBpc0F1dGhlbnRpY2F0ZWQ6IHRydWUsXG4gICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgICB1c2VyOiBzZXNzaW9uLnVzZXIgYXMgVXNlcixcbiAgICAgICAgICBzZXNzaW9uSWQ6IChzZXNzaW9uIGFzIGFueSkuc2Vzc2lvbklkIHx8IG51bGwsXG4gICAgICAgICAgbGFzdEFjdGl2aXR5OiBEYXRlLm5vdygpLFxuICAgICAgICAgIGlzQWRtaW4sXG4gICAgICAgICAgZXJyb3I6IG51bGxcbiAgICAgICAgfSk7XG5cbiAgICAgICAgdGhpcy51cGRhdGVMYXN0QWN0aXZpdHkoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRoaXMuc2V0U3RhdGUoe1xuICAgICAgICAgIGlzQXV0aGVudGljYXRlZDogZmFsc2UsXG4gICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgICB1c2VyOiBudWxsLFxuICAgICAgICAgIHNlc3Npb25JZDogbnVsbCxcbiAgICAgICAgICBsYXN0QWN0aXZpdHk6IG51bGwsXG4gICAgICAgICAgaXNBZG1pbjogZmFsc2UsXG4gICAgICAgICAgZXJyb3I6IG51bGxcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHJlZnJlc2hpbmcgYXV0aCBzdGF0ZTonLCBlcnJvcik7XG4gICAgICB0aGlzLnNldFN0YXRlKHtcbiAgICAgICAgaXNBdXRoZW50aWNhdGVkOiBmYWxzZSxcbiAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgdXNlcjogbnVsbCxcbiAgICAgICAgc2Vzc2lvbklkOiBudWxsLFxuICAgICAgICBsYXN0QWN0aXZpdHk6IG51bGwsXG4gICAgICAgIGlzQWRtaW46IGZhbHNlLFxuICAgICAgICBlcnJvcjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnQXV0aGVudGljYXRpb24gZXJyb3InXG4gICAgICB9KTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogVmFsaWRhdGUgY3VycmVudCBzZXNzaW9uXG4gICAqL1xuICBhc3luYyB2YWxpZGF0ZVNlc3Npb24oKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIENoZWNrIHNlc3Npb24gZXhwaXJ5XG4gICAgICBpZiAodGhpcy5jaGVja1Nlc3Npb25FeHBpcnkoKSkge1xuICAgICAgICBhd2FpdCB0aGlzLmZvcmNlTG9nb3V0KCdTZXNzaW9uIGV4cGlyZWQnKTtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuXG4gICAgICAvLyBWYWxpZGF0ZSB3aXRoIHNlcnZlclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9hdXRoL3ZhbGlkYXRlLXNlc3Npb24nLCB7XG4gICAgICAgIG1ldGhvZDogJ0dFVCcsXG4gICAgICAgIGNyZWRlbnRpYWxzOiAnc2FtZS1vcmlnaW4nLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NhY2hlLUNvbnRyb2wnOiAnbm8tY2FjaGUnXG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwMSkge1xuICAgICAgICAgIGF3YWl0IHRoaXMuZm9yY2VMb2dvdXQoJ1Nlc3Npb24gaW52YWxpZCcpO1xuICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFNlc3Npb24gdmFsaWRhdGlvbiBmYWlsZWQ6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgICAgfVxuXG4gICAgICBjb25zdCB7IHZhbGlkLCB1c2VyLCBpc0FkbWluIH0gPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmICh2YWxpZCAmJiB1c2VyKSB7XG4gICAgICAgIHRoaXMuc2V0U3RhdGUoe1xuICAgICAgICAgIGlzQXV0aGVudGljYXRlZDogdHJ1ZSxcbiAgICAgICAgICB1c2VyLFxuICAgICAgICAgIGlzQWRtaW46IGlzQWRtaW4gfHwgZmFsc2UsXG4gICAgICAgICAgbGFzdEFjdGl2aXR5OiBEYXRlLm5vdygpLFxuICAgICAgICAgIGVycm9yOiBudWxsXG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGF3YWl0IHRoaXMuZm9yY2VMb2dvdXQoJ1Nlc3Npb24gdmFsaWRhdGlvbiBmYWlsZWQnKTtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdTZXNzaW9uIHZhbGlkYXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgICAgdGhpcy5zZXRTdGF0ZSh7XG4gICAgICAgIGVycm9yOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdTZXNzaW9uIHZhbGlkYXRpb24gZXJyb3InXG4gICAgICB9KTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQ2xlYXIgYXV0aGVudGljYXRpb24gc3RhdGVcbiAgICovXG4gIGNsZWFyU3RhdGUoKTogdm9pZCB7XG4gICAgdGhpcy5zZXRTdGF0ZSh7XG4gICAgICBpc0F1dGhlbnRpY2F0ZWQ6IGZhbHNlLFxuICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgIHVzZXI6IG51bGwsXG4gICAgICBzZXNzaW9uSWQ6IG51bGwsXG4gICAgICBsYXN0QWN0aXZpdHk6IG51bGwsXG4gICAgICBpc0FkbWluOiBmYWxzZSxcbiAgICAgIGVycm9yOiBudWxsXG4gICAgfSk7XG4gIH1cblxuICAvKipcbiAgICogVXBkYXRlIGxhc3QgYWN0aXZpdHkgdGltZXN0YW1wXG4gICAqL1xuICB1cGRhdGVMYXN0QWN0aXZpdHkoKTogdm9pZCB7XG4gICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcbiAgICB0aGlzLnNldFN0YXRlKHsgbGFzdEFjdGl2aXR5OiBub3cgfSk7XG4gICAgXG4gICAgLy8gU3RvcmUgaW4gbG9jYWxTdG9yYWdlIGZvciBwZXJzaXN0ZW5jZSBhY3Jvc3MgdGFic1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2F1dGhfbGFzdF9hY3Rpdml0eScsIG5vdy50b1N0cmluZygpKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQ2hlY2sgaWYgc2Vzc2lvbiBoYXMgZXhwaXJlZFxuICAgKi9cbiAgY2hlY2tTZXNzaW9uRXhwaXJ5KCk6IGJvb2xlYW4ge1xuICAgIGNvbnN0IHsgbGFzdEFjdGl2aXR5IH0gPSB0aGlzLnN0YXRlO1xuICAgIFxuICAgIGlmICghbGFzdEFjdGl2aXR5KSB7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG5cbiAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpO1xuICAgIGNvbnN0IHRpbWVTaW5jZUFjdGl2aXR5ID0gbm93IC0gbGFzdEFjdGl2aXR5O1xuXG4gICAgLy8gQ2hlY2sgYWN0aXZpdHkgdGltZW91dFxuICAgIGlmICh0aW1lU2luY2VBY3Rpdml0eSA+IHRoaXMuQUNUSVZJVFlfVElNRU9VVCkge1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgYWJzb2x1dGUgc2Vzc2lvbiB0aW1lb3V0XG4gICAgaWYgKHRpbWVTaW5jZUFjdGl2aXR5ID4gdGhpcy5TRVNTSU9OX1RJTUVPVVQpIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cblxuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuXG4gIC8qKlxuICAgKiBGb3JjZSBsb2dvdXQgd2l0aCByZWFzb25cbiAgICovXG4gIGFzeW5jIGZvcmNlTG9nb3V0KHJlYXNvbj86IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygnRm9yY2UgbG9nb3V0OicsIHJlYXNvbik7XG4gICAgICBcbiAgICAgIC8vIENsZWFyIGxvY2FsIHN0YXRlIGZpcnN0XG4gICAgICB0aGlzLmNsZWFyU3RhdGUoKTtcbiAgICAgIFxuICAgICAgLy8gQ2xlYXIgbG9jYWxTdG9yYWdlXG4gICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2F1dGhfbGFzdF9hY3Rpdml0eScpO1xuICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnYXV0aF9zZXNzaW9uX2lkJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIFNpZ24gb3V0IGZyb20gTmV4dEF1dGhcbiAgICAgIGF3YWl0IHNpZ25PdXQoeyBcbiAgICAgICAgcmVkaXJlY3Q6IGZhbHNlLFxuICAgICAgICBjYWxsYmFja1VybDogJy9sb2dpbidcbiAgICAgIH0pO1xuXG4gICAgICAvLyBTZXQgZXJyb3Igc3RhdGUgaWYgcmVhc29uIHByb3ZpZGVkXG4gICAgICBpZiAocmVhc29uKSB7XG4gICAgICAgIHRoaXMuc2V0U3RhdGUoeyBlcnJvcjogcmVhc29uIH0pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkdXJpbmcgZm9yY2UgbG9nb3V0OicsIGVycm9yKTtcbiAgICAgIHRoaXMuc2V0U3RhdGUoe1xuICAgICAgICBlcnJvcjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnTG9nb3V0IGVycm9yJ1xuICAgICAgfSk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFVwZGF0ZSBhZG1pbiBzdGF0dXMgdmlhIEFQSSBjYWxsIChvcHRpb25hbCwgY2FsbGVkIHNlcGFyYXRlbHkpXG4gICAqL1xuICBhc3luYyB1cGRhdGVBZG1pblN0YXR1cygpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBjb25zdCBjdXJyZW50U3RhdGUgPSB0aGlzLmdldFN0YXRlKCk7XG4gICAgaWYgKCFjdXJyZW50U3RhdGUuaXNBdXRoZW50aWNhdGVkIHx8ICFjdXJyZW50U3RhdGUudXNlcj8uaWQpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9hdXRoL2NoZWNrLWFkbWluP3VzZXJJZD0ke2N1cnJlbnRTdGF0ZS51c2VyLmlkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgICAgY3JlZGVudGlhbHM6ICdzYW1lLW9yaWdpbicsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nXG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgeyBpc0FkbWluIH0gPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHRoaXMuc2V0U3RhdGUoeyBpc0FkbWluOiBpc0FkbWluIHx8IGZhbHNlIH0pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBhZG1pbiBzdGF0dXM6JywgZXJyb3IpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBJbml0aWFsaXplIHNlc3Npb24gbW9uaXRvcmluZ1xuICAgKi9cbiAgcHJpdmF0ZSBpbml0aWFsaXplU2Vzc2lvbk1vbml0b3JpbmcoKTogdm9pZCB7XG4gICAgLy8gUHJldmVudCBkb3VibGUgaW5pdGlhbGl6YXRpb25cbiAgICBpZiAodGhpcy5zZXNzaW9uQ2hlY2tJbnRlcnZhbCB8fCB0aGlzLmlzRGVzdHJveWVkKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gSW5pdGlhbCBzdGF0ZSByZWZyZXNoXG4gICAgdGhpcy5yZWZyZXNoU3RhdGUoKTtcblxuICAgIC8vIFNldCB1cCBwZXJpb2RpYyBzZXNzaW9uIHZhbGlkYXRpb25cbiAgICB0aGlzLnNlc3Npb25DaGVja0ludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgaWYgKHRoaXMuaXNEZXN0cm95ZWQpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgaWYgKHRoaXMuc3RhdGUuaXNBdXRoZW50aWNhdGVkKSB7XG4gICAgICAgIHRoaXMudmFsaWRhdGVTZXNzaW9uKCk7XG4gICAgICB9XG4gICAgfSwgdGhpcy5TRVNTSU9OX0NIRUNLX0lOVEVSVkFMKTtcblxuICAgIC8vIExpc3RlbiBmb3Igc3RvcmFnZSBldmVudHMgKGNyb3NzLXRhYiBzeW5jaHJvbml6YXRpb24pXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICB0aGlzLnN0b3JhZ2VFdmVudExpc3RlbmVyID0gKGV2ZW50OiBTdG9yYWdlRXZlbnQpID0+IHtcbiAgICAgICAgaWYgKHRoaXMuaXNEZXN0cm95ZWQpIHJldHVybjtcbiAgICAgICAgaWYgKGV2ZW50LmtleSA9PT0gJ2F1dGhfbGFzdF9hY3Rpdml0eScpIHtcbiAgICAgICAgICBjb25zdCBsYXN0QWN0aXZpdHkgPSBldmVudC5uZXdWYWx1ZSA/IHBhcnNlSW50KGV2ZW50Lm5ld1ZhbHVlKSA6IG51bGw7XG4gICAgICAgICAgaWYgKGxhc3RBY3Rpdml0eSkge1xuICAgICAgICAgICAgdGhpcy5zZXRTdGF0ZSh7IGxhc3RBY3Rpdml0eSB9KTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignc3RvcmFnZScsIHRoaXMuc3RvcmFnZUV2ZW50TGlzdGVuZXIpO1xuXG4gICAgICAvLyBMaXN0ZW4gZm9yIGZvY3VzIGV2ZW50cyB0byByZWZyZXNoIHN0YXRlXG4gICAgICB0aGlzLmZvY3VzRXZlbnRMaXN0ZW5lciA9ICgpID0+IHtcbiAgICAgICAgaWYgKHRoaXMuaXNEZXN0cm95ZWQpIHJldHVybjtcbiAgICAgICAgaWYgKHRoaXMuc3RhdGUuaXNBdXRoZW50aWNhdGVkKSB7XG4gICAgICAgICAgdGhpcy5yZWZyZXNoU3RhdGUoKTtcbiAgICAgICAgfVxuICAgICAgfTtcbiAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdmb2N1cycsIHRoaXMuZm9jdXNFdmVudExpc3RlbmVyKTtcblxuICAgICAgLy8gTGlzdGVuIGZvciBiZWZvcmV1bmxvYWQgdG8gdXBkYXRlIGFjdGl2aXR5XG4gICAgICB0aGlzLmJlZm9yZVVubG9hZEV2ZW50TGlzdGVuZXIgPSAoKSA9PiB7XG4gICAgICAgIGlmICh0aGlzLmlzRGVzdHJveWVkKSByZXR1cm47XG4gICAgICAgIHRoaXMudXBkYXRlTGFzdEFjdGl2aXR5KCk7XG4gICAgICB9O1xuICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2JlZm9yZXVubG9hZCcsIHRoaXMuYmVmb3JlVW5sb2FkRXZlbnRMaXN0ZW5lcik7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEFkZCBzdGF0ZSBjaGFuZ2UgbGlzdGVuZXIgKGFsaWFzIGZvciBzdWJzY3JpYmUpXG4gICAqL1xuICBhZGRTdGF0ZUNoYW5nZUxpc3RlbmVyKGxpc3RlbmVyOiAoc3RhdGU6IEF1dGhTdGF0ZSkgPT4gdm9pZCk6ICgpID0+IHZvaWQge1xuICAgIHJldHVybiB0aGlzLnN1YnNjcmliZShsaXN0ZW5lcik7XG4gIH1cblxuICAvKipcbiAgICogUmVtb3ZlIHN0YXRlIGNoYW5nZSBsaXN0ZW5lclxuICAgKi9cbiAgcmVtb3ZlU3RhdGVDaGFuZ2VMaXN0ZW5lcihsaXN0ZW5lcjogKHN0YXRlOiBBdXRoU3RhdGUpID0+IHZvaWQpOiB2b2lkIHtcbiAgICB0aGlzLmxpc3RlbmVycy5kZWxldGUobGlzdGVuZXIpO1xuICB9XG5cbiAgLyoqXG4gICAqIENsZWFudXAgcmVzb3VyY2VzXG4gICAqL1xuICBkZXN0cm95KCk6IHZvaWQge1xuICAgIC8vIE1hcmsgYXMgZGVzdHJveWVkIHRvIHByZXZlbnQgZnVydGhlciBvcGVyYXRpb25zXG4gICAgdGhpcy5pc0Rlc3Ryb3llZCA9IHRydWU7XG5cbiAgICAvLyBDbGVhciBpbnRlcnZhbFxuICAgIGlmICh0aGlzLnNlc3Npb25DaGVja0ludGVydmFsKSB7XG4gICAgICBjbGVhckludGVydmFsKHRoaXMuc2Vzc2lvbkNoZWNrSW50ZXJ2YWwpO1xuICAgICAgdGhpcy5zZXNzaW9uQ2hlY2tJbnRlcnZhbCA9IG51bGw7XG4gICAgfVxuXG4gICAgLy8gUmVtb3ZlIGV2ZW50IGxpc3RlbmVyc1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgaWYgKHRoaXMuc3RvcmFnZUV2ZW50TGlzdGVuZXIpIHtcbiAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3N0b3JhZ2UnLCB0aGlzLnN0b3JhZ2VFdmVudExpc3RlbmVyKTtcbiAgICAgICAgdGhpcy5zdG9yYWdlRXZlbnRMaXN0ZW5lciA9IG51bGw7XG4gICAgICB9XG4gICAgICBpZiAodGhpcy5mb2N1c0V2ZW50TGlzdGVuZXIpIHtcbiAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2ZvY3VzJywgdGhpcy5mb2N1c0V2ZW50TGlzdGVuZXIpO1xuICAgICAgICB0aGlzLmZvY3VzRXZlbnRMaXN0ZW5lciA9IG51bGw7XG4gICAgICB9XG4gICAgICBpZiAodGhpcy5iZWZvcmVVbmxvYWRFdmVudExpc3RlbmVyKSB7XG4gICAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdiZWZvcmV1bmxvYWQnLCB0aGlzLmJlZm9yZVVubG9hZEV2ZW50TGlzdGVuZXIpO1xuICAgICAgICB0aGlzLmJlZm9yZVVubG9hZEV2ZW50TGlzdGVuZXIgPSBudWxsO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIENsZWFyIGxpc3RlbmVyc1xuICAgIHRoaXMubGlzdGVuZXJzLmNsZWFyKCk7XG4gICAgdGhpcy5zdGF0ZUNoYW5nZUxpc3RlbmVycy5jbGVhcigpO1xuICB9XG59XG5cbi8vIFNpbmdsZXRvbiBpbnN0YW5jZVxubGV0IGF1dGhTdGF0ZU1hbmFnZXI6IEF1dGhlbnRpY2F0aW9uU3RhdGVNYW5hZ2VyIHwgbnVsbCA9IG51bGw7XG5cbi8qKlxuICogR2V0IHRoZSBnbG9iYWwgYXV0aGVudGljYXRpb24gc3RhdGUgbWFuYWdlciBpbnN0YW5jZVxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0QXV0aFN0YXRlTWFuYWdlcigpOiBBdXRoZW50aWNhdGlvblN0YXRlTWFuYWdlciB7XG4gIGlmICghYXV0aFN0YXRlTWFuYWdlcikge1xuICAgIGF1dGhTdGF0ZU1hbmFnZXIgPSBuZXcgQXV0aGVudGljYXRpb25TdGF0ZU1hbmFnZXIoKTtcbiAgfVxuICByZXR1cm4gYXV0aFN0YXRlTWFuYWdlcjtcbn1cblxuLyoqXG4gKiBEZXN0cm95IHRoZSBnbG9iYWwgYXV0aGVudGljYXRpb24gc3RhdGUgbWFuYWdlciBpbnN0YW5jZVxuICogVGhpcyBzaG91bGQgYmUgY2FsbGVkIHdoZW4gdGhlIGFwcGxpY2F0aW9uIGlzIHNodXR0aW5nIGRvd25cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGRlc3Ryb3lBdXRoU3RhdGVNYW5hZ2VyKCk6IHZvaWQge1xuICBpZiAoYXV0aFN0YXRlTWFuYWdlcikge1xuICAgIGF1dGhTdGF0ZU1hbmFnZXIuZGVzdHJveSgpO1xuICAgIGF1dGhTdGF0ZU1hbmFnZXIgPSBudWxsO1xuICB9XG59XG5cbi8vIEF1dG9tYXRpY2FsbHkgY2xlYW51cCBvbiBwYWdlIHVubG9hZCB0byBwcmV2ZW50IG1lbW9yeSBsZWFrc1xuaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdiZWZvcmV1bmxvYWQnLCAoKSA9PiB7XG4gICAgZGVzdHJveUF1dGhTdGF0ZU1hbmFnZXIoKTtcbiAgfSk7XG5cbiAgLy8gQWxzbyBjbGVhbnVwIG9uIHBhZ2UgaGlkZSAoZm9yIG1vYmlsZSBicm93c2VycylcbiAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3BhZ2VoaWRlJywgKCkgPT4ge1xuICAgIGRlc3Ryb3lBdXRoU3RhdGVNYW5hZ2VyKCk7XG4gIH0pO1xufVxuXG5cbiJdLCJtYXBwaW5ncyI6Ijs7QUFBQTs7OztBQUFBO0FBQUEsU0FBQUEsZUFBQTtFQUFBLElBQUFDLElBQUE7RUFBQSxJQUFBQyxJQUFBO0VBQUEsSUFBQUMsTUFBQSxPQUFBQyxRQUFBO0VBQUEsSUFBQUMsR0FBQTtFQUFBLElBQUFDLFlBQUE7SUFBQUwsSUFBQTtJQUFBTSxZQUFBO01BQUE7UUFBQUMsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO0lBQUE7SUFBQUUsS0FBQTtNQUFBO1FBQUFDLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtJQUFBO0lBQUFPLFNBQUE7TUFBQTtRQUFBRCxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO0lBQUE7SUFBQVcsQ0FBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtJQUFBO0lBQUFDLENBQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO0lBQUE7SUFBQUMsQ0FBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtJQUFBO0lBQUFDLGNBQUE7TUFBQUMsSUFBQTtNQUFBQyxRQUFBO01BQUFDLEtBQUE7TUFBQUMsT0FBQTtNQUFBQyxjQUFBO01BQUFDLE9BQUE7SUFBQTtJQUFBQyxlQUFBO0lBQUE1QixJQUFBO0VBQUE7RUFBQSxJQUFBNkIsUUFBQSxHQUFBNUIsTUFBQSxDQUFBRSxHQUFBLE1BQUFGLE1BQUEsQ0FBQUUsR0FBQTtFQUFBLEtBQUEwQixRQUFBLENBQUE5QixJQUFBLEtBQUE4QixRQUFBLENBQUE5QixJQUFBLEVBQUFDLElBQUEsS0FBQUEsSUFBQTtJQUFBNkIsUUFBQSxDQUFBOUIsSUFBQSxJQUFBSyxZQUFBO0VBQUE7RUFBQSxJQUFBMEIsY0FBQSxHQUFBRCxRQUFBLENBQUE5QixJQUFBO0VBQUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUE4Y0FnQyxPQUFBLENBQUFDLG1CQUFBLEdBQUFBLG1CQUFBO0FBS0M7QUFBQWxDLGNBQUEsR0FBQW9CLENBQUE7QUFNRGEsT0FBQSxDQUFBRSx1QkFBQSxHQUFBQSx1QkFBQTtBQXBkQSxJQUFBQyxPQUFBO0FBQUE7QUFBQSxDQUFBcEMsY0FBQSxHQUFBb0IsQ0FBQSxRQUFBaUIsT0FBQTtBQTBCQSxJQUFBQywwQkFBQTtBQUFBO0FBQUEsY0FBQXRDLGNBQUEsR0FBQW9CLENBQUE7RUFBQTtFQUFBcEIsY0FBQSxHQUFBcUIsQ0FBQTtFQXlCRSxTQUFBaUIsMkJBQUE7SUFBQTtJQUFBdEMsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtJQXhCUSxLQUFBbUIsS0FBSyxHQUFjO01BQ3pCQyxlQUFlLEVBQUUsS0FBSztNQUN0QkMsU0FBUyxFQUFFLElBQUk7TUFDZkMsSUFBSSxFQUFFLElBQUk7TUFDVkMsU0FBUyxFQUFFLElBQUk7TUFDZkMsWUFBWSxFQUFFLElBQUk7TUFDbEJDLE9BQU8sRUFBRSxLQUFLO01BQ2RDLEtBQUssRUFBRTtLQUNSO0lBQUM7SUFBQTlDLGNBQUEsR0FBQW9CLENBQUE7SUFFTSxLQUFBMkIsb0JBQW9CLEdBQW9DLElBQUlDLEdBQUcsRUFBRTtJQUFDO0lBQUFoRCxjQUFBLEdBQUFvQixDQUFBO0lBRWxFLEtBQUE2QixTQUFTLEdBQW9DLElBQUlELEdBQUcsRUFBRTtJQUFDO0lBQUFoRCxjQUFBLEdBQUFvQixDQUFBO0lBQ3ZELEtBQUE4QixvQkFBb0IsR0FBMEIsSUFBSTtJQUFDO0lBQUFsRCxjQUFBLEdBQUFvQixDQUFBO0lBQzFDLEtBQUErQixzQkFBc0IsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLElBQUksQ0FBQyxDQUFDO0lBQUE7SUFBQW5ELGNBQUEsR0FBQW9CLENBQUE7SUFDekMsS0FBQWdDLGVBQWUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsSUFBSSxDQUFDLENBQUM7SUFBQTtJQUFBcEQsY0FBQSxHQUFBb0IsQ0FBQTtJQUM1QyxLQUFBaUMsZ0JBQWdCLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsSUFBSSxDQUFDLENBQUM7SUFFekQ7SUFBQTtJQUFBckQsY0FBQSxHQUFBb0IsQ0FBQTtJQUNRLEtBQUFrQyxvQkFBb0IsR0FBMkMsSUFBSTtJQUFDO0lBQUF0RCxjQUFBLEdBQUFvQixDQUFBO0lBQ3BFLEtBQUFtQyxrQkFBa0IsR0FBd0IsSUFBSTtJQUFDO0lBQUF2RCxjQUFBLEdBQUFvQixDQUFBO0lBQy9DLEtBQUFvQyx5QkFBeUIsR0FBd0IsSUFBSTtJQUFDO0lBQUF4RCxjQUFBLEdBQUFvQixDQUFBO0lBQ3RELEtBQUFxQyxXQUFXLEdBQUcsS0FBSztJQUFDO0lBQUF6RCxjQUFBLEdBQUFvQixDQUFBO0lBRzFCLElBQUksQ0FBQ3NDLDJCQUEyQixFQUFFO0VBQ3BDO0VBRUE7OztFQUFBO0VBQUExRCxjQUFBLEdBQUFvQixDQUFBO0VBR0FrQiwwQkFBQSxDQUFBcUIsU0FBQSxDQUFBQyxRQUFRLEdBQVI7SUFBQTtJQUFBNUQsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtJQUNFLE9BQUF5QyxRQUFBLEtBQVksSUFBSSxDQUFDdEIsS0FBSztFQUN4QixDQUFDO0VBRUQ7OztFQUFBO0VBQUF2QyxjQUFBLEdBQUFvQixDQUFBO0VBR0FrQiwwQkFBQSxDQUFBcUIsU0FBQSxDQUFBRyxTQUFTLEdBQVQsVUFBVUMsUUFBb0M7SUFBQTtJQUFBL0QsY0FBQSxHQUFBcUIsQ0FBQTtJQUE5QyxJQUFBMkMsS0FBQTtJQUFBO0lBQUEsQ0FBQWhFLGNBQUEsR0FBQW9CLENBQUE7SUFPQztJQUFBcEIsY0FBQSxHQUFBb0IsQ0FBQTtJQU5DLElBQUksSUFBSSxDQUFDcUMsV0FBVyxFQUFFO01BQUE7TUFBQXpELGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDcEI7TUFDQSxPQUFPO1FBQUE7UUFBQXBCLGNBQUEsR0FBQXFCLENBQUE7TUFBTyxDQUFDO0lBQ2pCLENBQUM7SUFBQTtJQUFBO01BQUFyQixjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7SUFDRCxJQUFJLENBQUM2QixTQUFTLENBQUNnQixHQUFHLENBQUNGLFFBQVEsQ0FBQztJQUFDO0lBQUEvRCxjQUFBLEdBQUFvQixDQUFBO0lBQzdCLE9BQU87TUFBQTtNQUFBcEIsY0FBQSxHQUFBcUIsQ0FBQTtNQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFNLE9BQUE0QyxLQUFJLENBQUNmLFNBQVMsQ0FBQ2lCLE1BQU0sQ0FBQ0gsUUFBUSxDQUFDO0lBQS9CLENBQStCO0VBQzlDLENBQUM7RUFFRDs7O0VBQUE7RUFBQS9ELGNBQUEsR0FBQW9CLENBQUE7RUFHUWtCLDBCQUFBLENBQUFxQixTQUFBLENBQUFRLGVBQWUsR0FBdkI7SUFBQTtJQUFBbkUsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBLElBQUEyQyxLQUFBO0lBQUE7SUFBQSxDQUFBaEUsY0FBQSxHQUFBb0IsQ0FBQTtJQU1DO0lBQUFwQixjQUFBLEdBQUFvQixDQUFBO0lBTEMsSUFBSSxJQUFJLENBQUNxQyxXQUFXLEVBQUU7TUFBQTtNQUFBekQsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUNwQjtJQUNGLENBQUM7SUFBQTtJQUFBO01BQUFwQixjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7SUFDRCxJQUFJLENBQUMyQixvQkFBb0IsQ0FBQ3FCLE9BQU8sQ0FBQyxVQUFBTCxRQUFRO01BQUE7TUFBQS9ELGNBQUEsR0FBQXFCLENBQUE7TUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7TUFBSSxPQUFBMkMsUUFBUSxDQUFDQyxLQUFJLENBQUNKLFFBQVEsRUFBRSxDQUFDO0lBQXpCLENBQXlCLENBQUM7SUFBQztJQUFBNUQsY0FBQSxHQUFBb0IsQ0FBQTtJQUN6RSxJQUFJLENBQUM2QixTQUFTLENBQUNtQixPQUFPLENBQUMsVUFBQUwsUUFBUTtNQUFBO01BQUEvRCxjQUFBLEdBQUFxQixDQUFBO01BQUFyQixjQUFBLEdBQUFvQixDQUFBO01BQUksT0FBQTJDLFFBQVEsQ0FBQ0MsS0FBSSxDQUFDSixRQUFRLEVBQUUsQ0FBQztJQUF6QixDQUF5QixDQUFDO0VBQy9ELENBQUM7RUFFRDs7O0VBQUE7RUFBQTVELGNBQUEsR0FBQW9CLENBQUE7RUFHUWtCLDBCQUFBLENBQUFxQixTQUFBLENBQUFVLFFBQVEsR0FBaEIsVUFBaUJDLE9BQTJCO0lBQUE7SUFBQXRFLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFDMUMsSUFBSSxJQUFJLENBQUNxQyxXQUFXLEVBQUU7TUFBQTtNQUFBekQsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUNwQjtJQUNGLENBQUM7SUFBQTtJQUFBO01BQUFwQixjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFFRCxJQUFNaUQsUUFBUTtJQUFBO0lBQUEsQ0FBQXZFLGNBQUEsR0FBQW9CLENBQUEsU0FBQXlDLFFBQUEsS0FBUSxJQUFJLENBQUN0QixLQUFLLENBQUU7SUFBQztJQUFBdkMsY0FBQSxHQUFBb0IsQ0FBQTtJQUNuQyxJQUFJLENBQUNtQixLQUFLLEdBQUFzQixRQUFBLENBQUFBLFFBQUEsS0FBUSxJQUFJLENBQUN0QixLQUFLLEdBQUsrQixPQUFPLENBQUU7SUFFMUM7SUFBQTtJQUFBdEUsY0FBQSxHQUFBb0IsQ0FBQTtJQUNBLElBQUksQ0FBQytDLGVBQWUsRUFBRTtJQUV0QjtJQUFBO0lBQUFuRSxjQUFBLEdBQUFvQixDQUFBO0lBQ0EsSUFBSSxDQUFDb0Qsb0JBQW9CLENBQUNELFFBQVEsRUFBRSxJQUFJLENBQUNoQyxLQUFLLENBQUM7RUFDakQsQ0FBQztFQUVEOzs7RUFBQTtFQUFBdkMsY0FBQSxHQUFBb0IsQ0FBQTtFQUdRa0IsMEJBQUEsQ0FBQXFCLFNBQUEsQ0FBQWEsb0JBQW9CLEdBQTVCLFVBQTZCRCxRQUFtQixFQUFFRSxRQUFtQjtJQUFBO0lBQUF6RSxjQUFBLEdBQUFxQixDQUFBOzs7O0lBQ25FLElBQUksT0FBT3FELE1BQU0sS0FBSyxXQUFXLEVBQUU7TUFBQTtNQUFBMUUsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBO0lBQUEsQ0FBTztJQUFBO0lBQUE7TUFBQXBCLGNBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUUxQztJQUNBLElBQU1xRCxpQkFBaUI7SUFBQTtJQUFBLENBQUEzRSxjQUFBLEdBQUFvQixDQUFBO0lBQ3JCO0lBQUEsQ0FBQXBCLGNBQUEsR0FBQXNCLENBQUEsV0FBQWlELFFBQVEsQ0FBQy9CLGVBQWUsS0FBS2lDLFFBQVEsQ0FBQ2pDLGVBQWU7SUFBQTtJQUFBLENBQUF4QyxjQUFBLEdBQUFzQixDQUFBLFdBQ3JEO0lBQUE7SUFBQSxDQUFBdEIsY0FBQSxHQUFBc0IsQ0FBQSxZQUFBc0QsRUFBQSxHQUFBTCxRQUFRLENBQUM3QixJQUFJO0lBQUE7SUFBQSxDQUFBMUMsY0FBQSxHQUFBc0IsQ0FBQSxXQUFBc0QsRUFBQTtJQUFBO0lBQUEsQ0FBQTVFLGNBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUFBLENBQUF0QixjQUFBLEdBQUFzQixDQUFBLFdBQUFzRCxFQUFBLENBQUVDLEVBQUU7SUFBSztJQUFBLENBQUE3RSxjQUFBLEdBQUFzQixDQUFBLFlBQUF3RCxFQUFBLEdBQUFMLFFBQVEsQ0FBQy9CLElBQUk7SUFBQTtJQUFBLENBQUExQyxjQUFBLEdBQUFzQixDQUFBLFdBQUF3RCxFQUFBO0lBQUE7SUFBQSxDQUFBOUUsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUEsQ0FBQXRCLGNBQUEsR0FBQXNCLENBQUEsV0FBQXdELEVBQUEsQ0FBRUQsRUFBRTtJQUFBO0lBQUEsQ0FBQTdFLGNBQUEsR0FBQXNCLENBQUEsV0FDdkNpRCxRQUFRLENBQUMxQixPQUFPLEtBQUs0QixRQUFRLENBQUM1QixPQUFPO0lBQUM7SUFBQTdDLGNBQUEsR0FBQW9CLENBQUE7SUFFeEMsSUFBSXVELGlCQUFpQixFQUFFO01BQUE7TUFBQTNFLGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDckIsSUFBSTtRQUFBO1FBQUFwQixjQUFBLEdBQUFvQixDQUFBO1FBQ0YyRCxZQUFZLENBQUNDLE9BQU8sQ0FBQyxtQkFBbUIsRUFBRUMsSUFBSSxDQUFDQyxTQUFTLENBQUM7VUFDdkRDLFNBQVMsRUFBRUMsSUFBSSxDQUFDQyxHQUFHLEVBQUU7VUFDckI3QyxlQUFlLEVBQUVpQyxRQUFRLENBQUNqQyxlQUFlO1VBQ3pDOEMsTUFBTTtVQUFFO1VBQUEsQ0FBQXRGLGNBQUEsR0FBQXNCLENBQUEsWUFBQWlFLEVBQUEsR0FBQWQsUUFBUSxDQUFDL0IsSUFBSTtVQUFBO1VBQUEsQ0FBQTFDLGNBQUEsR0FBQXNCLENBQUEsV0FBQWlFLEVBQUE7VUFBQTtVQUFBLENBQUF2RixjQUFBLEdBQUFzQixDQUFBO1VBQUE7VUFBQSxDQUFBdEIsY0FBQSxHQUFBc0IsQ0FBQSxXQUFBaUUsRUFBQSxDQUFFVixFQUFFO1VBQ3pCaEMsT0FBTyxFQUFFNEIsUUFBUSxDQUFDNUI7U0FDbkIsQ0FBQyxDQUFDO1FBRUg7UUFBQTtRQUFBN0MsY0FBQSxHQUFBb0IsQ0FBQTtRQUNBMkQsWUFBWSxDQUFDUyxVQUFVLENBQUMsbUJBQW1CLENBQUM7TUFDOUMsQ0FBQyxDQUFDLE9BQU8xQyxLQUFLLEVBQUU7UUFBQTtRQUFBOUMsY0FBQSxHQUFBb0IsQ0FBQTtRQUNkcUUsT0FBTyxDQUFDM0MsS0FBSyxDQUFDLHdDQUF3QyxFQUFFQSxLQUFLLENBQUM7TUFDaEU7SUFDRixDQUFDO0lBQUE7SUFBQTtNQUFBOUMsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0VBQ0gsQ0FBQztFQUVEOzs7RUFBQTtFQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtFQUdNa0IsMEJBQUEsQ0FBQXFCLFNBQUEsQ0FBQStCLFlBQVksR0FBbEI7SUFBQTtJQUFBMUYsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTttQ0FBc0J1RSxPQUFPO01BQUE7TUFBQTNGLGNBQUEsR0FBQXFCLENBQUE7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBRXpCLElBQUksQ0FBQ2dELFFBQVEsQ0FBQztjQUFFNUIsU0FBUyxFQUFFLElBQUk7Y0FBRUssS0FBSyxFQUFFO1lBQUksQ0FBRSxDQUFDO1lBQUM7WUFBQTlDLGNBQUEsR0FBQW9CLENBQUE7WUFFaEMscUJBQU0sSUFBQWdCLE9BQUEsQ0FBQXdELFVBQVUsR0FBRTs7Ozs7WUFBNUJDLE9BQU8sR0FBR2YsRUFBQSxDQUFBZ0IsSUFBQSxFQUFrQjtZQUFBO1lBQUE5RixjQUFBLEdBQUFvQixDQUFBO1lBRWxDO1lBQUk7WUFBQSxDQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQSxXQUFBdUUsT0FBTztZQUFBO1lBQUEsQ0FBQTdGLGNBQUEsR0FBQXNCLENBQUEsV0FBUHVFLE9BQU87WUFBQTtZQUFBLENBQUE3RixjQUFBLEdBQUFzQixDQUFBO1lBQUE7WUFBQSxDQUFBdEIsY0FBQSxHQUFBc0IsQ0FBQSxXQUFQdUUsT0FBTyxDQUFFbkQsSUFBSSxHQUFFO2NBQUE7Y0FBQTFDLGNBQUEsR0FBQXNCLENBQUE7Y0FBQXRCLGNBQUEsR0FBQW9CLENBQUE7Y0FHWHlCLE9BQU87Y0FBRztjQUFBLENBQUE3QyxjQUFBLEdBQUFzQixDQUFBO2NBQUE7Y0FBQSxDQUFBdEIsY0FBQSxHQUFBc0IsQ0FBQSxZQUFBc0QsRUFBQSxHQUFDaUIsT0FBZSxDQUFDbkQsSUFBSTtjQUFBO2NBQUEsQ0FBQTFDLGNBQUEsR0FBQXNCLENBQUEsV0FBQXNELEVBQUE7Y0FBQTtjQUFBLENBQUE1RSxjQUFBLEdBQUFzQixDQUFBO2NBQUE7Y0FBQSxDQUFBdEIsY0FBQSxHQUFBc0IsQ0FBQSxXQUFBc0QsRUFBQSxDQUFFL0IsT0FBTztjQUFBO2NBQUEsQ0FBQTdDLGNBQUEsR0FBQXNCLENBQUEsV0FBSSxLQUFLO2NBQUM7Y0FBQXRCLGNBQUEsR0FBQW9CLENBQUE7Y0FFeEQsSUFBSSxDQUFDaUQsUUFBUSxDQUFDO2dCQUNaN0IsZUFBZSxFQUFFLElBQUk7Z0JBQ3JCQyxTQUFTLEVBQUUsS0FBSztnQkFDaEJDLElBQUksRUFBRW1ELE9BQU8sQ0FBQ25ELElBQVk7Z0JBQzFCQyxTQUFTO2dCQUFHO2dCQUFBLENBQUEzQyxjQUFBLEdBQUFzQixDQUFBLFdBQUF1RSxPQUFlLENBQUNsRCxTQUFTO2dCQUFBO2dCQUFBLENBQUEzQyxjQUFBLEdBQUFzQixDQUFBLFdBQUksSUFBSTtnQkFDN0NzQixZQUFZLEVBQUV3QyxJQUFJLENBQUNDLEdBQUcsRUFBRTtnQkFDeEJ4QyxPQUFPLEVBQUFBLE9BQUE7Z0JBQ1BDLEtBQUssRUFBRTtlQUNSLENBQUM7Y0FBQztjQUFBOUMsY0FBQSxHQUFBb0IsQ0FBQTtjQUVILElBQUksQ0FBQzJFLGtCQUFrQixFQUFFO1lBQzNCLENBQUMsTUFBTTtjQUFBO2NBQUEvRixjQUFBLEdBQUFzQixDQUFBO2NBQUF0QixjQUFBLEdBQUFvQixDQUFBO2NBQ0wsSUFBSSxDQUFDaUQsUUFBUSxDQUFDO2dCQUNaN0IsZUFBZSxFQUFFLEtBQUs7Z0JBQ3RCQyxTQUFTLEVBQUUsS0FBSztnQkFDaEJDLElBQUksRUFBRSxJQUFJO2dCQUNWQyxTQUFTLEVBQUUsSUFBSTtnQkFDZkMsWUFBWSxFQUFFLElBQUk7Z0JBQ2xCQyxPQUFPLEVBQUUsS0FBSztnQkFDZEMsS0FBSyxFQUFFO2VBQ1IsQ0FBQztZQUNKO1lBQUM7WUFBQTlDLGNBQUEsR0FBQW9CLENBQUE7Ozs7Ozs7OztZQUVEcUUsT0FBTyxDQUFDM0MsS0FBSyxDQUFDLDhCQUE4QixFQUFFa0QsT0FBSyxDQUFDO1lBQUM7WUFBQWhHLGNBQUEsR0FBQW9CLENBQUE7WUFDckQsSUFBSSxDQUFDaUQsUUFBUSxDQUFDO2NBQ1o3QixlQUFlLEVBQUUsS0FBSztjQUN0QkMsU0FBUyxFQUFFLEtBQUs7Y0FDaEJDLElBQUksRUFBRSxJQUFJO2NBQ1ZDLFNBQVMsRUFBRSxJQUFJO2NBQ2ZDLFlBQVksRUFBRSxJQUFJO2NBQ2xCQyxPQUFPLEVBQUUsS0FBSztjQUNkQyxLQUFLLEVBQUVrRCxPQUFLLFlBQVlDLEtBQUs7Y0FBQTtjQUFBLENBQUFqRyxjQUFBLEdBQUFzQixDQUFBLFdBQUcwRSxPQUFLLENBQUNFLE9BQU87Y0FBQTtjQUFBLENBQUFsRyxjQUFBLEdBQUFzQixDQUFBLFdBQUcsc0JBQXNCO2FBQ3ZFLENBQUM7WUFBQztZQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTs7Ozs7Ozs7OztHQUVOO0VBRUQ7OztFQUFBO0VBQUFwQixjQUFBLEdBQUFvQixDQUFBO0VBR01rQiwwQkFBQSxDQUFBcUIsU0FBQSxDQUFBd0MsZUFBZSxHQUFyQjtJQUFBO0lBQUFuRyxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO21DQUF5QnVFLE9BQU87TUFBQTtNQUFBM0YsY0FBQSxHQUFBcUIsQ0FBQTs7Ozs7Ozs7Ozs7Ozs7OztpQkFHeEIsSUFBSSxDQUFDK0Usa0JBQWtCLEVBQUUsRUFBekI7Y0FBQTtjQUFBcEcsY0FBQSxHQUFBc0IsQ0FBQTtjQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtjQUFBO1lBQUEsQ0FBeUI7WUFBQTtZQUFBO2NBQUFwQixjQUFBLEdBQUFzQixDQUFBO1lBQUE7WUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7WUFDM0IscUJBQU0sSUFBSSxDQUFDaUYsV0FBVyxDQUFDLGlCQUFpQixDQUFDOzs7OztZQUF6Q3ZCLEVBQUEsQ0FBQWdCLElBQUEsRUFBeUM7WUFBQztZQUFBOUYsY0FBQSxHQUFBb0IsQ0FBQTtZQUMxQyxzQkFBTyxLQUFLOzs7OztZQUlHLHFCQUFNa0YsS0FBSyxDQUFDLDRCQUE0QixFQUFFO2NBQ3pEQyxNQUFNLEVBQUUsS0FBSztjQUNiQyxXQUFXLEVBQUUsYUFBYTtjQUMxQkMsT0FBTyxFQUFFO2dCQUNQLGVBQWUsRUFBRTs7YUFFcEIsQ0FBQzs7Ozs7WUFOSUMsUUFBUSxHQUFHNUIsRUFBQSxDQUFBZ0IsSUFBQSxFQU1mO1lBQUE7WUFBQTlGLGNBQUEsR0FBQW9CLENBQUE7aUJBRUUsQ0FBQ3NGLFFBQVEsQ0FBQ0MsRUFBRSxFQUFaO2NBQUE7Y0FBQTNHLGNBQUEsR0FBQXNCLENBQUE7Y0FBQXRCLGNBQUEsR0FBQW9CLENBQUE7Y0FBQTtZQUFBLENBQVk7WUFBQTtZQUFBO2NBQUFwQixjQUFBLEdBQUFzQixDQUFBO1lBQUE7WUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7a0JBQ1ZzRixRQUFRLENBQUNFLE1BQU0sS0FBSyxHQUFHLEdBQXZCO2NBQUE7Y0FBQTVHLGNBQUEsR0FBQXNCLENBQUE7Y0FBQXRCLGNBQUEsR0FBQW9CLENBQUE7Y0FBQTtZQUFBLENBQXVCO1lBQUE7WUFBQTtjQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQTtZQUFBO1lBQUF0QixjQUFBLEdBQUFvQixDQUFBO1lBQ3pCLHFCQUFNLElBQUksQ0FBQ2lGLFdBQVcsQ0FBQyxpQkFBaUIsQ0FBQzs7Ozs7WUFBekN2QixFQUFBLENBQUFnQixJQUFBLEVBQXlDO1lBQUM7WUFBQTlGLGNBQUEsR0FBQW9CLENBQUE7WUFDMUMsc0JBQU8sS0FBSzs7Ozs7WUFFZCxNQUFNLElBQUk2RSxLQUFLLENBQUMsOEJBQUFZLE1BQUEsQ0FBOEJILFFBQVEsQ0FBQ0UsTUFBTSxDQUFFLENBQUM7Ozs7O1lBR2pDLHFCQUFNRixRQUFRLENBQUNJLElBQUksRUFBRTs7Ozs7WUFBaERsQyxFQUFBLEdBQTJCRSxFQUFBLENBQUFnQixJQUFBLEVBQXFCLEVBQTlDaUIsS0FBSyxHQUFBbkMsRUFBQSxDQUFBbUMsS0FBQSxFQUFFckUsSUFBSSxHQUFBa0MsRUFBQSxDQUFBbEMsSUFBQSxFQUFFRyxPQUFPLEdBQUErQixFQUFBLENBQUEvQixPQUFBO1lBQUE7WUFBQTdDLGNBQUEsR0FBQW9CLENBQUE7O1lBRXhCO1lBQUEsQ0FBQXBCLGNBQUEsR0FBQXNCLENBQUEsV0FBQXlGLEtBQUs7WUFBQTtZQUFBLENBQUEvRyxjQUFBLEdBQUFzQixDQUFBLFdBQUlvQixJQUFJLElBQWI7Y0FBQTtjQUFBMUMsY0FBQSxHQUFBc0IsQ0FBQTtjQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtjQUFBO1lBQUEsQ0FBYTtZQUFBO1lBQUE7Y0FBQXBCLGNBQUEsR0FBQXNCLENBQUE7WUFBQTtZQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtZQUNmLElBQUksQ0FBQ2lELFFBQVEsQ0FBQztjQUNaN0IsZUFBZSxFQUFFLElBQUk7Y0FDckJFLElBQUksRUFBQUEsSUFBQTtjQUNKRyxPQUFPO2NBQUU7Y0FBQSxDQUFBN0MsY0FBQSxHQUFBc0IsQ0FBQSxXQUFBdUIsT0FBTztjQUFBO2NBQUEsQ0FBQTdDLGNBQUEsR0FBQXNCLENBQUEsV0FBSSxLQUFLO2NBQ3pCc0IsWUFBWSxFQUFFd0MsSUFBSSxDQUFDQyxHQUFHLEVBQUU7Y0FDeEJ2QyxLQUFLLEVBQUU7YUFDUixDQUFDO1lBQUM7WUFBQTlDLGNBQUEsR0FBQW9CLENBQUE7WUFDSCxzQkFBTyxJQUFJOzs7OztZQUVYLHFCQUFNLElBQUksQ0FBQ2lGLFdBQVcsQ0FBQywyQkFBMkIsQ0FBQzs7Ozs7WUFBbkR2QixFQUFBLENBQUFnQixJQUFBLEVBQW1EO1lBQUM7WUFBQTlGLGNBQUEsR0FBQW9CLENBQUE7WUFDcEQsc0JBQU8sS0FBSzs7Ozs7Ozs7Ozs7OztZQUdkcUUsT0FBTyxDQUFDM0MsS0FBSyxDQUFDLDJCQUEyQixFQUFFa0UsT0FBSyxDQUFDO1lBQUM7WUFBQWhILGNBQUEsR0FBQW9CLENBQUE7WUFDbEQsSUFBSSxDQUFDaUQsUUFBUSxDQUFDO2NBQ1p2QixLQUFLLEVBQUVrRSxPQUFLLFlBQVlmLEtBQUs7Y0FBQTtjQUFBLENBQUFqRyxjQUFBLEdBQUFzQixDQUFBLFdBQUcwRixPQUFLLENBQUNkLE9BQU87Y0FBQTtjQUFBLENBQUFsRyxjQUFBLEdBQUFzQixDQUFBLFdBQUcsMEJBQTBCO2FBQzNFLENBQUM7WUFBQztZQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtZQUNILHNCQUFPLEtBQUs7Ozs7Ozs7OztHQUVmO0VBRUQ7OztFQUFBO0VBQUFwQixjQUFBLEdBQUFvQixDQUFBO0VBR0FrQiwwQkFBQSxDQUFBcUIsU0FBQSxDQUFBc0QsVUFBVSxHQUFWO0lBQUE7SUFBQWpILGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFDRSxJQUFJLENBQUNpRCxRQUFRLENBQUM7TUFDWjdCLGVBQWUsRUFBRSxLQUFLO01BQ3RCQyxTQUFTLEVBQUUsS0FBSztNQUNoQkMsSUFBSSxFQUFFLElBQUk7TUFDVkMsU0FBUyxFQUFFLElBQUk7TUFDZkMsWUFBWSxFQUFFLElBQUk7TUFDbEJDLE9BQU8sRUFBRSxLQUFLO01BQ2RDLEtBQUssRUFBRTtLQUNSLENBQUM7RUFDSixDQUFDO0VBRUQ7OztFQUFBO0VBQUE5QyxjQUFBLEdBQUFvQixDQUFBO0VBR0FrQiwwQkFBQSxDQUFBcUIsU0FBQSxDQUFBb0Msa0JBQWtCLEdBQWxCO0lBQUE7SUFBQS9GLGNBQUEsR0FBQXFCLENBQUE7SUFDRSxJQUFNZ0UsR0FBRztJQUFBO0lBQUEsQ0FBQXJGLGNBQUEsR0FBQW9CLENBQUEsU0FBR2dFLElBQUksQ0FBQ0MsR0FBRyxFQUFFO0lBQUM7SUFBQXJGLGNBQUEsR0FBQW9CLENBQUE7SUFDdkIsSUFBSSxDQUFDaUQsUUFBUSxDQUFDO01BQUV6QixZQUFZLEVBQUV5QztJQUFHLENBQUUsQ0FBQztJQUVwQztJQUFBO0lBQUFyRixjQUFBLEdBQUFvQixDQUFBO0lBQ0EsSUFBSSxPQUFPc0QsTUFBTSxLQUFLLFdBQVcsRUFBRTtNQUFBO01BQUExRSxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQ2pDMkQsWUFBWSxDQUFDQyxPQUFPLENBQUMsb0JBQW9CLEVBQUVLLEdBQUcsQ0FBQzZCLFFBQVEsRUFBRSxDQUFDO0lBQzVELENBQUM7SUFBQTtJQUFBO01BQUFsSCxjQUFBLEdBQUFzQixDQUFBO0lBQUE7RUFDSCxDQUFDO0VBRUQ7OztFQUFBO0VBQUF0QixjQUFBLEdBQUFvQixDQUFBO0VBR0FrQiwwQkFBQSxDQUFBcUIsU0FBQSxDQUFBeUMsa0JBQWtCLEdBQWxCO0lBQUE7SUFBQXBHLGNBQUEsR0FBQXFCLENBQUE7SUFDVSxJQUFBdUIsWUFBWTtJQUFBO0lBQUEsQ0FBQTVDLGNBQUEsR0FBQW9CLENBQUEsU0FBSyxJQUFJLENBQUNtQixLQUFLLENBQUFLLFlBQWY7SUFBZ0I7SUFBQTVDLGNBQUEsR0FBQW9CLENBQUE7SUFFcEMsSUFBSSxDQUFDd0IsWUFBWSxFQUFFO01BQUE7TUFBQTVDLGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDakIsT0FBTyxJQUFJO0lBQ2IsQ0FBQztJQUFBO0lBQUE7TUFBQXBCLGNBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUVELElBQU0rRCxHQUFHO0lBQUE7SUFBQSxDQUFBckYsY0FBQSxHQUFBb0IsQ0FBQSxTQUFHZ0UsSUFBSSxDQUFDQyxHQUFHLEVBQUU7SUFDdEIsSUFBTThCLGlCQUFpQjtJQUFBO0lBQUEsQ0FBQW5ILGNBQUEsR0FBQW9CLENBQUEsU0FBR2lFLEdBQUcsR0FBR3pDLFlBQVk7SUFFNUM7SUFBQTtJQUFBNUMsY0FBQSxHQUFBb0IsQ0FBQTtJQUNBLElBQUkrRixpQkFBaUIsR0FBRyxJQUFJLENBQUM5RCxnQkFBZ0IsRUFBRTtNQUFBO01BQUFyRCxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQzdDLE9BQU8sSUFBSTtJQUNiLENBQUM7SUFBQTtJQUFBO01BQUFwQixjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFFRDtJQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUNBLElBQUkrRixpQkFBaUIsR0FBRyxJQUFJLENBQUMvRCxlQUFlLEVBQUU7TUFBQTtNQUFBcEQsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUM1QyxPQUFPLElBQUk7SUFDYixDQUFDO0lBQUE7SUFBQTtNQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBRUQsT0FBTyxLQUFLO0VBQ2QsQ0FBQztFQUVEOzs7RUFBQTtFQUFBcEIsY0FBQSxHQUFBb0IsQ0FBQTtFQUdNa0IsMEJBQUEsQ0FBQXFCLFNBQUEsQ0FBQTBDLFdBQVcsR0FBakIsVUFBa0JlLE1BQWU7SUFBQTtJQUFBcEgsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTttQ0FBR3VFLE9BQU87TUFBQTtNQUFBM0YsY0FBQSxHQUFBcUIsQ0FBQTs7Ozs7Ozs7Ozs7Ozs7OztZQUV2Q29FLE9BQU8sQ0FBQzRCLEdBQUcsQ0FBQyxlQUFlLEVBQUVELE1BQU0sQ0FBQztZQUVwQztZQUFBO1lBQUFwSCxjQUFBLEdBQUFvQixDQUFBO1lBQ0EsSUFBSSxDQUFDNkYsVUFBVSxFQUFFO1lBRWpCO1lBQUE7WUFBQWpILGNBQUEsR0FBQW9CLENBQUE7WUFDQSxJQUFJLE9BQU9zRCxNQUFNLEtBQUssV0FBVyxFQUFFO2NBQUE7Y0FBQTFFLGNBQUEsR0FBQXNCLENBQUE7Y0FBQXRCLGNBQUEsR0FBQW9CLENBQUE7Y0FDakMyRCxZQUFZLENBQUNTLFVBQVUsQ0FBQyxvQkFBb0IsQ0FBQztjQUFDO2NBQUF4RixjQUFBLEdBQUFvQixDQUFBO2NBQzlDMkQsWUFBWSxDQUFDUyxVQUFVLENBQUMsaUJBQWlCLENBQUM7WUFDNUMsQ0FBQztZQUFBO1lBQUE7Y0FBQXhGLGNBQUEsR0FBQXNCLENBQUE7WUFBQTtZQUVEO1lBQUF0QixjQUFBLEdBQUFvQixDQUFBO1lBQ0EscUJBQU0sSUFBQWdCLE9BQUEsQ0FBQWtGLE9BQU8sRUFBQztjQUNaQyxRQUFRLEVBQUUsS0FBSztjQUNmQyxXQUFXLEVBQUU7YUFDZCxDQUFDOzs7OztZQUpGO1lBQ0E1QyxFQUFBLENBQUFrQixJQUFBLEVBR0U7WUFFRjtZQUFBO1lBQUE5RixjQUFBLEdBQUFvQixDQUFBO1lBQ0EsSUFBSWdHLE1BQU0sRUFBRTtjQUFBO2NBQUFwSCxjQUFBLEdBQUFzQixDQUFBO2NBQUF0QixjQUFBLEdBQUFvQixDQUFBO2NBQ1YsSUFBSSxDQUFDaUQsUUFBUSxDQUFDO2dCQUFFdkIsS0FBSyxFQUFFc0U7Y0FBTSxDQUFFLENBQUM7WUFDbEMsQ0FBQztZQUFBO1lBQUE7Y0FBQXBILGNBQUEsR0FBQXNCLENBQUE7WUFBQTtZQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTs7Ozs7Ozs7O1lBRURxRSxPQUFPLENBQUMzQyxLQUFLLENBQUMsNEJBQTRCLEVBQUUyRSxPQUFLLENBQUM7WUFBQztZQUFBekgsY0FBQSxHQUFBb0IsQ0FBQTtZQUNuRCxJQUFJLENBQUNpRCxRQUFRLENBQUM7Y0FDWnZCLEtBQUssRUFBRTJFLE9BQUssWUFBWXhCLEtBQUs7Y0FBQTtjQUFBLENBQUFqRyxjQUFBLEdBQUFzQixDQUFBLFdBQUdtRyxPQUFLLENBQUN2QixPQUFPO2NBQUE7Y0FBQSxDQUFBbEcsY0FBQSxHQUFBc0IsQ0FBQSxXQUFHLGNBQWM7YUFDL0QsQ0FBQztZQUFDO1lBQUF0QixjQUFBLEdBQUFvQixDQUFBOzs7Ozs7Ozs7O0dBRU47RUFFRDs7O0VBQUE7RUFBQXBCLGNBQUEsR0FBQW9CLENBQUE7RUFHTWtCLDBCQUFBLENBQUFxQixTQUFBLENBQUErRCxpQkFBaUIsR0FBdkI7SUFBQTtJQUFBMUgsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTttQ0FBMkJ1RSxPQUFPO01BQUE7TUFBQTNGLGNBQUEsR0FBQXFCLENBQUE7Ozs7Ozs7Ozs7Ozs7O1lBQzFCc0csWUFBWSxHQUFHLElBQUksQ0FBQy9ELFFBQVEsRUFBRTtZQUFDO1lBQUE1RCxjQUFBLEdBQUFvQixDQUFBO1lBQ3JDO1lBQUk7WUFBQSxDQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQSxZQUFDcUcsWUFBWSxDQUFDbkYsZUFBZTtZQUFBO1lBQUEsQ0FBQXhDLGNBQUEsR0FBQXNCLENBQUEsV0FBSTtZQUFDO1lBQUEsQ0FBQXRCLGNBQUEsR0FBQXNCLENBQUEsWUFBQXNELEVBQUEsR0FBQStDLFlBQVksQ0FBQ2pGLElBQUk7WUFBQTtZQUFBLENBQUExQyxjQUFBLEdBQUFzQixDQUFBLFdBQUFzRCxFQUFBO1lBQUE7WUFBQSxDQUFBNUUsY0FBQSxHQUFBc0IsQ0FBQTtZQUFBO1lBQUEsQ0FBQXRCLGNBQUEsR0FBQXNCLENBQUEsV0FBQXNELEVBQUEsQ0FBRUMsRUFBRSxLQUFFO2NBQUE7Y0FBQTdFLGNBQUEsR0FBQXNCLENBQUE7Y0FBQXRCLGNBQUEsR0FBQW9CLENBQUE7Y0FDM0Q7WUFDRixDQUFDO1lBQUE7WUFBQTtjQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQTtZQUFBO1lBQUF0QixjQUFBLEdBQUFvQixDQUFBOzs7Ozs7Ozs7WUFHa0IscUJBQU1rRixLQUFLLENBQUMsZ0NBQUFPLE1BQUEsQ0FBZ0NjLFlBQVksQ0FBQ2pGLElBQUksQ0FBQ21DLEVBQUUsQ0FBRSxFQUFFO2NBQ25GMEIsTUFBTSxFQUFFLEtBQUs7Y0FDYkMsV0FBVyxFQUFFLGFBQWE7Y0FDMUJDLE9BQU8sRUFBRTtnQkFDUCxjQUFjLEVBQUU7O2FBRW5CLENBQUM7Ozs7O1lBTklDLFFBQVEsR0FBRzVCLEVBQUEsQ0FBQWdCLElBQUEsRUFNZjtZQUFBO1lBQUE5RixjQUFBLEdBQUFvQixDQUFBO2lCQUVFc0YsUUFBUSxDQUFDQyxFQUFFLEVBQVg7Y0FBQTtjQUFBM0csY0FBQSxHQUFBc0IsQ0FBQTtjQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtjQUFBO1lBQUEsQ0FBVztZQUFBO1lBQUE7Y0FBQXBCLGNBQUEsR0FBQXNCLENBQUE7WUFBQTtZQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtZQUNPLHFCQUFNc0YsUUFBUSxDQUFDSSxJQUFJLEVBQUU7Ozs7O1lBQWpDakUsT0FBTyxHQUFLaUMsRUFBQSxDQUFBZ0IsSUFBQSxFQUFxQixDQUFBakQsT0FBMUI7WUFBQTtZQUFBN0MsY0FBQSxHQUFBb0IsQ0FBQTtZQUNmLElBQUksQ0FBQ2lELFFBQVEsQ0FBQztjQUFFeEIsT0FBTztjQUFFO2NBQUEsQ0FBQTdDLGNBQUEsR0FBQXNCLENBQUEsV0FBQXVCLE9BQU87Y0FBQTtjQUFBLENBQUE3QyxjQUFBLEdBQUFzQixDQUFBLFdBQUksS0FBSztZQUFBLENBQUUsQ0FBQztZQUFDO1lBQUF0QixjQUFBLEdBQUFvQixDQUFBOzs7Ozs7Ozs7Ozs7OztZQUcvQ3FFLE9BQU8sQ0FBQzNDLEtBQUssQ0FBQyw4QkFBOEIsRUFBRThFLE9BQUssQ0FBQztZQUFDO1lBQUE1SCxjQUFBLEdBQUFvQixDQUFBOzs7Ozs7Ozs7O0dBRXhEO0VBRUQ7OztFQUFBO0VBQUFwQixjQUFBLEdBQUFvQixDQUFBO0VBR1FrQiwwQkFBQSxDQUFBcUIsU0FBQSxDQUFBRCwyQkFBMkIsR0FBbkM7SUFBQTtJQUFBMUQsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBLElBQUEyQyxLQUFBO0lBQUE7SUFBQSxDQUFBaEUsY0FBQSxHQUFBb0IsQ0FBQTtJQUNFO0lBQUE7SUFBQXBCLGNBQUEsR0FBQW9CLENBQUE7SUFDQTtJQUFJO0lBQUEsQ0FBQXBCLGNBQUEsR0FBQXNCLENBQUEsZUFBSSxDQUFDNEIsb0JBQW9CO0lBQUE7SUFBQSxDQUFBbEQsY0FBQSxHQUFBc0IsQ0FBQSxXQUFJLElBQUksQ0FBQ21DLFdBQVcsR0FBRTtNQUFBO01BQUF6RCxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQ2pEO0lBQ0YsQ0FBQztJQUFBO0lBQUE7TUFBQXBCLGNBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUVEO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBQ0EsSUFBSSxDQUFDc0UsWUFBWSxFQUFFO0lBRW5CO0lBQUE7SUFBQTFGLGNBQUEsR0FBQW9CLENBQUE7SUFDQSxJQUFJLENBQUM4QixvQkFBb0IsR0FBRzJFLFdBQVcsQ0FBQztNQUFBO01BQUE3SCxjQUFBLEdBQUFxQixDQUFBO01BQUFyQixjQUFBLEdBQUFvQixDQUFBO01BQ3RDLElBQUk0QyxLQUFJLENBQUNQLFdBQVcsRUFBRTtRQUFBO1FBQUF6RCxjQUFBLEdBQUFzQixDQUFBO1FBQUF0QixjQUFBLEdBQUFvQixDQUFBO1FBQ3BCO01BQ0YsQ0FBQztNQUFBO01BQUE7UUFBQXBCLGNBQUEsR0FBQXNCLENBQUE7TUFBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUNELElBQUk0QyxLQUFJLENBQUN6QixLQUFLLENBQUNDLGVBQWUsRUFBRTtRQUFBO1FBQUF4QyxjQUFBLEdBQUFzQixDQUFBO1FBQUF0QixjQUFBLEdBQUFvQixDQUFBO1FBQzlCNEMsS0FBSSxDQUFDbUMsZUFBZSxFQUFFO01BQ3hCLENBQUM7TUFBQTtNQUFBO1FBQUFuRyxjQUFBLEdBQUFzQixDQUFBO01BQUE7SUFDSCxDQUFDLEVBQUUsSUFBSSxDQUFDNkIsc0JBQXNCLENBQUM7SUFFL0I7SUFBQTtJQUFBbkQsY0FBQSxHQUFBb0IsQ0FBQTtJQUNBLElBQUksT0FBT3NELE1BQU0sS0FBSyxXQUFXLEVBQUU7TUFBQTtNQUFBMUUsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUNqQyxJQUFJLENBQUNrQyxvQkFBb0IsR0FBRyxVQUFDd0UsS0FBbUI7UUFBQTtRQUFBOUgsY0FBQSxHQUFBcUIsQ0FBQTtRQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtRQUM5QyxJQUFJNEMsS0FBSSxDQUFDUCxXQUFXLEVBQUU7VUFBQTtVQUFBekQsY0FBQSxHQUFBc0IsQ0FBQTtVQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtVQUFBO1FBQUEsQ0FBTztRQUFBO1FBQUE7VUFBQXBCLGNBQUEsR0FBQXNCLENBQUE7UUFBQTtRQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtRQUM3QixJQUFJMEcsS0FBSyxDQUFDQyxHQUFHLEtBQUssb0JBQW9CLEVBQUU7VUFBQTtVQUFBL0gsY0FBQSxHQUFBc0IsQ0FBQTtVQUN0QyxJQUFNc0IsWUFBWTtVQUFBO1VBQUEsQ0FBQTVDLGNBQUEsR0FBQW9CLENBQUEsU0FBRzBHLEtBQUssQ0FBQ0UsUUFBUTtVQUFBO1VBQUEsQ0FBQWhJLGNBQUEsR0FBQXNCLENBQUEsV0FBRzJHLFFBQVEsQ0FBQ0gsS0FBSyxDQUFDRSxRQUFRLENBQUM7VUFBQTtVQUFBLENBQUFoSSxjQUFBLEdBQUFzQixDQUFBLFdBQUcsSUFBSTtVQUFDO1VBQUF0QixjQUFBLEdBQUFvQixDQUFBO1VBQ3RFLElBQUl3QixZQUFZLEVBQUU7WUFBQTtZQUFBNUMsY0FBQSxHQUFBc0IsQ0FBQTtZQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtZQUNoQjRDLEtBQUksQ0FBQ0ssUUFBUSxDQUFDO2NBQUV6QixZQUFZLEVBQUFBO1lBQUEsQ0FBRSxDQUFDO1VBQ2pDLENBQUM7VUFBQTtVQUFBO1lBQUE1QyxjQUFBLEdBQUFzQixDQUFBO1VBQUE7UUFDSCxDQUFDO1FBQUE7UUFBQTtVQUFBdEIsY0FBQSxHQUFBc0IsQ0FBQTtRQUFBO01BQ0gsQ0FBQztNQUFDO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQ0ZzRCxNQUFNLENBQUN3RCxnQkFBZ0IsQ0FBQyxTQUFTLEVBQUUsSUFBSSxDQUFDNUUsb0JBQW9CLENBQUM7TUFFN0Q7TUFBQTtNQUFBdEQsY0FBQSxHQUFBb0IsQ0FBQTtNQUNBLElBQUksQ0FBQ21DLGtCQUFrQixHQUFHO1FBQUE7UUFBQXZELGNBQUEsR0FBQXFCLENBQUE7UUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7UUFDeEIsSUFBSTRDLEtBQUksQ0FBQ1AsV0FBVyxFQUFFO1VBQUE7VUFBQXpELGNBQUEsR0FBQXNCLENBQUE7VUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7VUFBQTtRQUFBLENBQU87UUFBQTtRQUFBO1VBQUFwQixjQUFBLEdBQUFzQixDQUFBO1FBQUE7UUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7UUFDN0IsSUFBSTRDLEtBQUksQ0FBQ3pCLEtBQUssQ0FBQ0MsZUFBZSxFQUFFO1VBQUE7VUFBQXhDLGNBQUEsR0FBQXNCLENBQUE7VUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7VUFDOUI0QyxLQUFJLENBQUMwQixZQUFZLEVBQUU7UUFDckIsQ0FBQztRQUFBO1FBQUE7VUFBQTFGLGNBQUEsR0FBQXNCLENBQUE7UUFBQTtNQUNILENBQUM7TUFBQztNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUNGc0QsTUFBTSxDQUFDd0QsZ0JBQWdCLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQzNFLGtCQUFrQixDQUFDO01BRXpEO01BQUE7TUFBQXZELGNBQUEsR0FBQW9CLENBQUE7TUFDQSxJQUFJLENBQUNvQyx5QkFBeUIsR0FBRztRQUFBO1FBQUF4RCxjQUFBLEdBQUFxQixDQUFBO1FBQUFyQixjQUFBLEdBQUFvQixDQUFBO1FBQy9CLElBQUk0QyxLQUFJLENBQUNQLFdBQVcsRUFBRTtVQUFBO1VBQUF6RCxjQUFBLEdBQUFzQixDQUFBO1VBQUF0QixjQUFBLEdBQUFvQixDQUFBO1VBQUE7UUFBQSxDQUFPO1FBQUE7UUFBQTtVQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQTtRQUFBO1FBQUF0QixjQUFBLEdBQUFvQixDQUFBO1FBQzdCNEMsS0FBSSxDQUFDK0Isa0JBQWtCLEVBQUU7TUFDM0IsQ0FBQztNQUFDO01BQUEvRixjQUFBLEdBQUFvQixDQUFBO01BQ0ZzRCxNQUFNLENBQUN3RCxnQkFBZ0IsQ0FBQyxjQUFjLEVBQUUsSUFBSSxDQUFDMUUseUJBQXlCLENBQUM7SUFDekUsQ0FBQztJQUFBO0lBQUE7TUFBQXhELGNBQUEsR0FBQXNCLENBQUE7SUFBQTtFQUNILENBQUM7RUFFRDs7O0VBQUE7RUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7RUFHQWtCLDBCQUFBLENBQUFxQixTQUFBLENBQUF3RSxzQkFBc0IsR0FBdEIsVUFBdUJwRSxRQUFvQztJQUFBO0lBQUEvRCxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ3pELE9BQU8sSUFBSSxDQUFDMEMsU0FBUyxDQUFDQyxRQUFRLENBQUM7RUFDakMsQ0FBQztFQUVEOzs7RUFBQTtFQUFBL0QsY0FBQSxHQUFBb0IsQ0FBQTtFQUdBa0IsMEJBQUEsQ0FBQXFCLFNBQUEsQ0FBQXlFLHlCQUF5QixHQUF6QixVQUEwQnJFLFFBQW9DO0lBQUE7SUFBQS9ELGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFDNUQsSUFBSSxDQUFDNkIsU0FBUyxDQUFDaUIsTUFBTSxDQUFDSCxRQUFRLENBQUM7RUFDakMsQ0FBQztFQUVEOzs7RUFBQTtFQUFBL0QsY0FBQSxHQUFBb0IsQ0FBQTtFQUdBa0IsMEJBQUEsQ0FBQXFCLFNBQUEsQ0FBQTBFLE9BQU8sR0FBUDtJQUFBO0lBQUFySSxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ0U7SUFDQSxJQUFJLENBQUNxQyxXQUFXLEdBQUcsSUFBSTtJQUV2QjtJQUFBO0lBQUF6RCxjQUFBLEdBQUFvQixDQUFBO0lBQ0EsSUFBSSxJQUFJLENBQUM4QixvQkFBb0IsRUFBRTtNQUFBO01BQUFsRCxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQzdCa0gsYUFBYSxDQUFDLElBQUksQ0FBQ3BGLG9CQUFvQixDQUFDO01BQUM7TUFBQWxELGNBQUEsR0FBQW9CLENBQUE7TUFDekMsSUFBSSxDQUFDOEIsb0JBQW9CLEdBQUcsSUFBSTtJQUNsQyxDQUFDO0lBQUE7SUFBQTtNQUFBbEQsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBRUQ7SUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7SUFDQSxJQUFJLE9BQU9zRCxNQUFNLEtBQUssV0FBVyxFQUFFO01BQUE7TUFBQTFFLGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDakMsSUFBSSxJQUFJLENBQUNrQyxvQkFBb0IsRUFBRTtRQUFBO1FBQUF0RCxjQUFBLEdBQUFzQixDQUFBO1FBQUF0QixjQUFBLEdBQUFvQixDQUFBO1FBQzdCc0QsTUFBTSxDQUFDNkQsbUJBQW1CLENBQUMsU0FBUyxFQUFFLElBQUksQ0FBQ2pGLG9CQUFvQixDQUFDO1FBQUM7UUFBQXRELGNBQUEsR0FBQW9CLENBQUE7UUFDakUsSUFBSSxDQUFDa0Msb0JBQW9CLEdBQUcsSUFBSTtNQUNsQyxDQUFDO01BQUE7TUFBQTtRQUFBdEQsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQ0QsSUFBSSxJQUFJLENBQUNtQyxrQkFBa0IsRUFBRTtRQUFBO1FBQUF2RCxjQUFBLEdBQUFzQixDQUFBO1FBQUF0QixjQUFBLEdBQUFvQixDQUFBO1FBQzNCc0QsTUFBTSxDQUFDNkQsbUJBQW1CLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQ2hGLGtCQUFrQixDQUFDO1FBQUM7UUFBQXZELGNBQUEsR0FBQW9CLENBQUE7UUFDN0QsSUFBSSxDQUFDbUMsa0JBQWtCLEdBQUcsSUFBSTtNQUNoQyxDQUFDO01BQUE7TUFBQTtRQUFBdkQsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQ0QsSUFBSSxJQUFJLENBQUNvQyx5QkFBeUIsRUFBRTtRQUFBO1FBQUF4RCxjQUFBLEdBQUFzQixDQUFBO1FBQUF0QixjQUFBLEdBQUFvQixDQUFBO1FBQ2xDc0QsTUFBTSxDQUFDNkQsbUJBQW1CLENBQUMsY0FBYyxFQUFFLElBQUksQ0FBQy9FLHlCQUF5QixDQUFDO1FBQUM7UUFBQXhELGNBQUEsR0FBQW9CLENBQUE7UUFDM0UsSUFBSSxDQUFDb0MseUJBQXlCLEdBQUcsSUFBSTtNQUN2QyxDQUFDO01BQUE7TUFBQTtRQUFBeEQsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBO0lBQ0gsQ0FBQztJQUFBO0lBQUE7TUFBQXRCLGNBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUVEO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBQ0EsSUFBSSxDQUFDNkIsU0FBUyxDQUFDdUYsS0FBSyxFQUFFO0lBQUM7SUFBQXhJLGNBQUEsR0FBQW9CLENBQUE7SUFDdkIsSUFBSSxDQUFDMkIsb0JBQW9CLENBQUN5RixLQUFLLEVBQUU7RUFDbkMsQ0FBQztFQUFBO0VBQUF4SSxjQUFBLEdBQUFvQixDQUFBO0VBQ0gsT0FBQWtCLDBCQUFDO0FBQUQsQ0FBQyxDQXZhRDtBQXlhQTtBQUNBLElBQUltRyxnQkFBZ0I7QUFBQTtBQUFBLENBQUF6SSxjQUFBLEdBQUFvQixDQUFBLFNBQXNDLElBQUk7QUFFOUQ7OztBQUdBLFNBQWdCYyxtQkFBbUJBLENBQUE7RUFBQTtFQUFBbEMsY0FBQSxHQUFBcUIsQ0FBQTtFQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtFQUNqQyxJQUFJLENBQUNxSCxnQkFBZ0IsRUFBRTtJQUFBO0lBQUF6SSxjQUFBLEdBQUFzQixDQUFBO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBQ3JCcUgsZ0JBQWdCLEdBQUcsSUFBSW5HLDBCQUEwQixFQUFFO0VBQ3JELENBQUM7RUFBQTtFQUFBO0lBQUF0QyxjQUFBLEdBQUFzQixDQUFBO0VBQUE7RUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7RUFDRCxPQUFPcUgsZ0JBQWdCO0FBQ3pCO0FBRUE7Ozs7QUFJQSxTQUFnQnRHLHVCQUF1QkEsQ0FBQTtFQUFBO0VBQUFuQyxjQUFBLEdBQUFxQixDQUFBO0VBQUFyQixjQUFBLEdBQUFvQixDQUFBO0VBQ3JDLElBQUlxSCxnQkFBZ0IsRUFBRTtJQUFBO0lBQUF6SSxjQUFBLEdBQUFzQixDQUFBO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBQ3BCcUgsZ0JBQWdCLENBQUNKLE9BQU8sRUFBRTtJQUFDO0lBQUFySSxjQUFBLEdBQUFvQixDQUFBO0lBQzNCcUgsZ0JBQWdCLEdBQUcsSUFBSTtFQUN6QixDQUFDO0VBQUE7RUFBQTtJQUFBekksY0FBQSxHQUFBc0IsQ0FBQTtFQUFBO0FBQ0g7QUFFQTtBQUFBO0FBQUF0QixjQUFBLEdBQUFvQixDQUFBO0FBQ0EsSUFBSSxPQUFPc0QsTUFBTSxLQUFLLFdBQVcsRUFBRTtFQUFBO0VBQUExRSxjQUFBLEdBQUFzQixDQUFBO0VBQUF0QixjQUFBLEdBQUFvQixDQUFBO0VBQ2pDc0QsTUFBTSxDQUFDd0QsZ0JBQWdCLENBQUMsY0FBYyxFQUFFO0lBQUE7SUFBQWxJLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFDdENlLHVCQUF1QixFQUFFO0VBQzNCLENBQUMsQ0FBQztFQUVGO0VBQUE7RUFBQW5DLGNBQUEsR0FBQW9CLENBQUE7RUFDQXNELE1BQU0sQ0FBQ3dELGdCQUFnQixDQUFDLFVBQVUsRUFBRTtJQUFBO0lBQUFsSSxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ2xDZSx1QkFBdUIsRUFBRTtFQUMzQixDQUFDLENBQUM7QUFDSixDQUFDO0FBQUE7QUFBQTtFQUFBbkMsY0FBQSxHQUFBc0IsQ0FBQTtBQUFBIiwiaWdub3JlTGlzdCI6W119