4ec5b68f2d80468e74591c30d1f6e5a3
"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var SkillMarketDataService_1 = require("@/lib/skills/SkillMarketDataService");
describe('SkillMarketDataService', function () {
    var service;
    beforeEach(function () {
        service = new SkillMarketDataService_1.SkillMarketDataService();
    });
    describe('Market Data Collection', function () {
        it('should fetch market data for a single skill', function () { return __awaiter(void 0, void 0, void 0, function () {
            var marketData;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.getSkillMarketData('javascript')];
                    case 1:
                        marketData = _a.sent();
                        expect(marketData).toBeDefined();
                        expect(marketData.skill).toBe('javascript');
                        expect(marketData.demand).toBeGreaterThan(0);
                        expect(marketData.supply).toBeGreaterThan(0);
                        expect(marketData.averageSalary).toBeGreaterThan(0);
                        expect(marketData.growth).toBeDefined();
                        expect(marketData.difficulty).toBeGreaterThan(0);
                        expect(marketData.timeToLearn).toBeGreaterThan(0);
                        expect(marketData.category).toBeDefined();
                        expect(marketData.lastUpdated).toBeInstanceOf(Date);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should fetch market data for multiple skills', function () { return __awaiter(void 0, void 0, void 0, function () {
            var skills, marketDataList;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        skills = ['javascript', 'react', 'nodejs'];
                        return [4 /*yield*/, service.getMultipleSkillsMarketData(skills)];
                    case 1:
                        marketDataList = _a.sent();
                        expect(marketDataList).toHaveLength(3);
                        expect(marketDataList.every(function (data) { return data.skill; })).toBe(true);
                        expect(marketDataList.every(function (data) { return data.demand > 0; })).toBe(true);
                        expect(marketDataList.every(function (data) { return data.supply > 0; })).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle unknown skills gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var marketData;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.getSkillMarketData('unknown-skill-xyz')];
                    case 1:
                        marketData = _a.sent();
                        expect(marketData).toBeDefined();
                        expect(marketData.skill).toBe('unknown-skill-xyz');
                        expect(marketData.demand).toBe(0);
                        expect(marketData.supply).toBe(0);
                        expect(marketData.averageSalary).toBe(0);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should cache market data to avoid repeated API calls', function () { return __awaiter(void 0, void 0, void 0, function () {
            var startTime, firstCallTime, secondStartTime, secondCallTime;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        startTime = Date.now();
                        return [4 /*yield*/, service.getSkillMarketData('javascript')];
                    case 1:
                        _a.sent();
                        firstCallTime = Date.now() - startTime;
                        secondStartTime = Date.now();
                        return [4 /*yield*/, service.getSkillMarketData('javascript')];
                    case 2:
                        _a.sent();
                        secondCallTime = Date.now() - secondStartTime;
                        expect(secondCallTime).toBeLessThan(firstCallTime);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Market Trends Analysis', function () {
        it('should analyze market trends for skills', function () { return __awaiter(void 0, void 0, void 0, function () {
            var trends;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.analyzeMarketTrends(['javascript', 'react', 'python'])];
                    case 1:
                        trends = _a.sent();
                        expect(trends).toBeDefined();
                        expect(trends.topDemandSkills).toBeDefined();
                        expect(trends.topDemandSkills.length).toBeGreaterThan(0);
                        expect(trends.fastestGrowingSkills).toBeDefined();
                        expect(trends.highestPayingSkills).toBeDefined();
                        expect(trends.marketGaps).toBeDefined();
                        expect(trends.emergingSkills).toBeDefined();
                        expect(trends.decliningSkills).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should identify market gaps (high demand, low supply)', function () { return __awaiter(void 0, void 0, void 0, function () {
            var trends, gap;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.analyzeMarketTrends(['javascript', 'react', 'python'])];
                    case 1:
                        trends = _a.sent();
                        expect(trends.marketGaps).toBeDefined();
                        expect(Array.isArray(trends.marketGaps)).toBe(true);
                        if (trends.marketGaps.length > 0) {
                            gap = trends.marketGaps[0];
                            expect(gap.skill).toBeDefined();
                            expect(gap.demandSupplyRatio).toBeGreaterThan(1);
                            expect(gap.opportunityScore).toBeGreaterThan(0);
                        }
                        return [2 /*return*/];
                }
            });
        }); });
        it('should rank skills by growth potential', function () { return __awaiter(void 0, void 0, void 0, function () {
            var trends, first, second;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.analyzeMarketTrends(['javascript', 'react', 'python'])];
                    case 1:
                        trends = _a.sent();
                        expect(trends.fastestGrowingSkills).toBeDefined();
                        expect(Array.isArray(trends.fastestGrowingSkills)).toBe(true);
                        if (trends.fastestGrowingSkills.length > 1) {
                            first = trends.fastestGrowingSkills[0];
                            second = trends.fastestGrowingSkills[1];
                            expect(first.growth).toBeGreaterThanOrEqual(second.growth);
                        }
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Salary Analysis', function () {
        it('should provide salary insights for skills', function () { return __awaiter(void 0, void 0, void 0, function () {
            var salaryInsights;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.getSalaryInsights(['javascript', 'react', 'python'])];
                    case 1:
                        salaryInsights = _a.sent();
                        expect(salaryInsights).toBeDefined();
                        expect(salaryInsights.averageSalary).toBeGreaterThan(0);
                        expect(salaryInsights.salaryRange).toBeDefined();
                        expect(salaryInsights.salaryRange.min).toBeGreaterThan(0);
                        expect(salaryInsights.salaryRange.max).toBeGreaterThan(salaryInsights.salaryRange.min);
                        expect(salaryInsights.topPayingSkills).toBeDefined();
                        expect(salaryInsights.salaryGrowthPotential).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should calculate salary percentiles', function () { return __awaiter(void 0, void 0, void 0, function () {
            var salaryInsights;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.getSalaryInsights(['javascript', 'react', 'python'])];
                    case 1:
                        salaryInsights = _a.sent();
                        expect(salaryInsights.percentiles).toBeDefined();
                        expect(salaryInsights.percentiles.p25).toBeGreaterThan(0);
                        expect(salaryInsights.percentiles.p50).toBeGreaterThan(salaryInsights.percentiles.p25);
                        expect(salaryInsights.percentiles.p75).toBeGreaterThan(salaryInsights.percentiles.p50);
                        expect(salaryInsights.percentiles.p90).toBeGreaterThan(salaryInsights.percentiles.p75);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Geographic Analysis', function () {
        it('should provide location-based market data', function () { return __awaiter(void 0, void 0, void 0, function () {
            var locationData;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.getLocationBasedMarketData('javascript', 'San Francisco')];
                    case 1:
                        locationData = _a.sent();
                        expect(locationData).toBeDefined();
                        expect(locationData.skill).toBe('javascript');
                        expect(locationData.location).toBe('San Francisco');
                        expect(locationData.localDemand).toBeGreaterThan(0);
                        expect(locationData.localSalary).toBeGreaterThan(0);
                        expect(locationData.costOfLivingAdjustment).toBeDefined();
                        expect(locationData.remoteOpportunities).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should compare market data across multiple locations', function () { return __awaiter(void 0, void 0, void 0, function () {
            var locations, comparison;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        locations = ['San Francisco', 'New York', 'Austin'];
                        return [4 /*yield*/, service.compareLocationMarkets('javascript', locations)];
                    case 1:
                        comparison = _a.sent();
                        expect(comparison).toBeDefined();
                        expect(comparison.skill).toBe('javascript');
                        expect(comparison.locationComparison).toHaveLength(3);
                        comparison.locationComparison.forEach(function (loc) {
                            expect(loc.location).toBeDefined();
                            expect(loc.demand).toBeGreaterThan(0);
                            expect(loc.salary).toBeGreaterThan(0);
                            expect(loc.costOfLivingIndex).toBeGreaterThan(0);
                        });
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Industry Analysis', function () {
        it('should provide industry-specific market data', function () { return __awaiter(void 0, void 0, void 0, function () {
            var industryData;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.getIndustryMarketData('javascript', 'technology')];
                    case 1:
                        industryData = _a.sent();
                        expect(industryData).toBeDefined();
                        expect(industryData.skill).toBe('javascript');
                        expect(industryData.industry).toBe('technology');
                        expect(industryData.industryDemand).toBeGreaterThan(0);
                        expect(industryData.averageIndustrySalary).toBeGreaterThan(0);
                        expect(industryData.growthProjection).toBeDefined();
                        expect(industryData.keyCompanies).toBeDefined();
                        expect(Array.isArray(industryData.keyCompanies)).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should rank industries by skill demand', function () { return __awaiter(void 0, void 0, void 0, function () {
            var industryRanking, first, second;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.rankIndustriesBySkillDemand('javascript')];
                    case 1:
                        industryRanking = _a.sent();
                        expect(industryRanking).toBeDefined();
                        expect(industryRanking.skill).toBe('javascript');
                        expect(industryRanking.industries).toBeDefined();
                        expect(Array.isArray(industryRanking.industries)).toBe(true);
                        if (industryRanking.industries.length > 1) {
                            first = industryRanking.industries[0];
                            second = industryRanking.industries[1];
                            expect(first.demandScore).toBeGreaterThanOrEqual(second.demandScore);
                        }
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Learning Path Recommendations', function () {
        it('should recommend skills based on market data', function () { return __awaiter(void 0, void 0, void 0, function () {
            var recommendations;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.getMarketBasedRecommendations({
                            currentSkills: ['javascript'],
                            careerGoal: 'Full Stack Developer',
                            location: 'San Francisco',
                            experienceLevel: 'intermediate',
                        })];
                    case 1:
                        recommendations = _a.sent();
                        expect(recommendations).toBeDefined();
                        expect(recommendations.recommendedSkills).toBeDefined();
                        expect(Array.isArray(recommendations.recommendedSkills)).toBe(true);
                        expect(recommendations.learningPriority).toBeDefined();
                        expect(recommendations.marketOpportunities).toBeDefined();
                        expect(recommendations.salaryImpact).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should prioritize skills by market opportunity', function () { return __awaiter(void 0, void 0, void 0, function () {
            var recommendations, first, second;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.getMarketBasedRecommendations({
                            currentSkills: ['html', 'css'],
                            careerGoal: 'Frontend Developer',
                            location: 'Remote',
                            experienceLevel: 'beginner',
                        })];
                    case 1:
                        recommendations = _a.sent();
                        expect(recommendations.learningPriority).toBeDefined();
                        expect(Array.isArray(recommendations.learningPriority)).toBe(true);
                        if (recommendations.learningPriority.length > 1) {
                            first = recommendations.learningPriority[0];
                            second = recommendations.learningPriority[1];
                            expect(first.priorityScore).toBeGreaterThanOrEqual(second.priorityScore);
                        }
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Data Freshness & Updates', function () {
        it('should track data freshness and update when stale', function () { return __awaiter(void 0, void 0, void 0, function () {
            var marketData, dataAge;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.getSkillMarketData('javascript')];
                    case 1:
                        marketData = _a.sent();
                        dataAge = Date.now() - marketData.lastUpdated.getTime();
                        expect(dataAge).toBeLessThan(24 * 60 * 60 * 1000); // Less than 24 hours old
                        return [2 /*return*/];
                }
            });
        }); });
        it('should force refresh market data when requested', function () { return __awaiter(void 0, void 0, void 0, function () {
            var oldData, newData;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.getSkillMarketData('javascript')];
                    case 1:
                        oldData = _a.sent();
                        return [4 /*yield*/, service.getSkillMarketData('javascript', { forceRefresh: true })];
                    case 2:
                        newData = _a.sent();
                        expect(newData.lastUpdated.getTime()).toBeGreaterThanOrEqual(oldData.lastUpdated.getTime());
                        return [2 /*return*/];
                }
            });
        }); });
        it('should batch update multiple skills efficiently', function () { return __awaiter(void 0, void 0, void 0, function () {
            var skills, startTime, endTime, duration;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        skills = ['javascript', 'react', 'nodejs', 'python', 'typescript'];
                        startTime = Date.now();
                        return [4 /*yield*/, service.batchUpdateMarketData(skills)];
                    case 1:
                        _a.sent();
                        endTime = Date.now();
                        duration = endTime - startTime;
                        // Batch update should be more efficient than individual calls
                        expect(duration).toBeLessThan(skills.length * 1000); // Less than 1 second per skill
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Error Handling', function () {
        it('should handle API failures gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var originalFetch, marketData;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        originalFetch = global.fetch;
                        global.fetch = jest.fn().mockRejectedValue(new Error('API Error'));
                        return [4 /*yield*/, service.getSkillMarketData('javascript')];
                    case 1:
                        marketData = _a.sent();
                        expect(marketData).toBeDefined();
                        expect(marketData.skill).toBe('javascript');
                        expect(marketData.isStale).toBe(true);
                        global.fetch = originalFetch;
                        return [2 /*return*/];
                }
            });
        }); });
        it('should validate input parameters', function () { return __awaiter(void 0, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, expect(service.getSkillMarketData('')).rejects.toThrow('Invalid skill name')];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, expect(service.getMultipleSkillsMarketData([])).rejects.toThrow('No skills provided')];
                    case 2:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle rate limiting gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var originalFetch, marketData;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        originalFetch = global.fetch;
                        global.fetch = jest.fn().mockResolvedValue({
                            ok: false,
                            status: 429,
                            json: function () { return Promise.resolve({ error: 'Rate limited' }); },
                        });
                        return [4 /*yield*/, service.getSkillMarketData('javascript')];
                    case 1:
                        marketData = _a.sent();
                        expect(marketData).toBeDefined();
                        expect(marketData.isStale).toBe(true);
                        global.fetch = originalFetch;
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Performance & Caching', function () {
        it('should implement intelligent caching strategy', function () { return __awaiter(void 0, void 0, void 0, function () {
            var cacheStats;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.getCacheStatistics()];
                    case 1:
                        cacheStats = _a.sent();
                        expect(cacheStats).toBeDefined();
                        expect(cacheStats.hitRate).toBeGreaterThanOrEqual(0);
                        expect(cacheStats.missRate).toBeGreaterThanOrEqual(0);
                        expect(cacheStats.totalRequests).toBeGreaterThanOrEqual(0);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should clear cache when requested', function () { return __awaiter(void 0, void 0, void 0, function () {
            var cacheStats;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, service.getSkillMarketData('javascript')];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, service.clearCache()];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, service.getCacheStatistics()];
                    case 3:
                        cacheStats = _a.sent();
                        expect(cacheStats.totalRequests).toBe(0);
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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