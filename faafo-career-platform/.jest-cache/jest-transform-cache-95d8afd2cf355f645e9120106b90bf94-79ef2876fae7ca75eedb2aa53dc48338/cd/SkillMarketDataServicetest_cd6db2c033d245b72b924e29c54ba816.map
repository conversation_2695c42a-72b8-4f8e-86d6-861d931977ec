{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/SkillMarketDataService.test.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,8EAA6E;AAE7E,QAAQ,CAAC,wBAAwB,EAAE;IACjC,IAAI,OAA+B,CAAC;IAEpC,UAAU,CAAC;QACT,OAAO,GAAG,IAAI,+CAAsB,EAAE,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE;QACjC,EAAE,CAAC,6CAA6C,EAAE;;;;4BAC7B,qBAAM,OAAO,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAA;;wBAA3D,UAAU,GAAG,SAA8C;wBAEjE,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;wBACjC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAC5C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC7C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC7C,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBACpD,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;wBACxC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBACjD,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAClD,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC1C,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;;;;aACrD,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE;;;;;wBAC3C,MAAM,GAAG,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;wBAC1B,qBAAM,OAAO,CAAC,2BAA2B,CAAC,MAAM,CAAC,EAAA;;wBAAlE,cAAc,GAAG,SAAiD;wBAExE,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBACvC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,KAAK,EAAV,CAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC5D,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,GAAG,CAAC,EAAf,CAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACjE,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,GAAG,CAAC,EAAf,CAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aAClE,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE;;;;4BACzB,qBAAM,OAAO,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,EAAA;;wBAAlE,UAAU,GAAG,SAAqD;wBAExE,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;wBACjC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;wBACnD,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBAClC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBAClC,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;;;aAC1C,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE;;;;;wBACnD,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBAC7B,qBAAM,OAAO,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAA;;wBAA9C,SAA8C,CAAC;wBACzC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBAEvC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBACnC,qBAAM,OAAO,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAA;;wBAA9C,SAA8C,CAAC;wBACzC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,eAAe,CAAC;wBAEpD,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;;;;aACpD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE;QACjC,EAAE,CAAC,yCAAyC,EAAE;;;;4BAC7B,qBAAM,OAAO,CAAC,mBAAmB,CAAC,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,EAAA;;wBAA7E,MAAM,GAAG,SAAoE;wBAEnF,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC7B,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC7C,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBACzD,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,WAAW,EAAE,CAAC;wBAClD,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;wBACjD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;wBACxC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC5C,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC9C,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE;;;;4BAC3C,qBAAM,OAAO,CAAC,mBAAmB,CAAC,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,EAAA;;wBAA7E,MAAM,GAAG,SAAoE;wBAEnF,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;wBACxC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAEpD,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BAC3B,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;4BACjC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;4BAChC,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;4BACjD,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAClD,CAAC;;;;aACF,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE;;;;4BAC5B,qBAAM,OAAO,CAAC,mBAAmB,CAAC,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,EAAA;;wBAA7E,MAAM,GAAG,SAAoE;wBAEnF,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,WAAW,EAAE,CAAC;wBAClD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAE9D,IAAI,MAAM,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACrC,KAAK,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;4BACvC,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;4BAC9C,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,sBAAsB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;wBAC7D,CAAC;;;;aACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE;QAC1B,EAAE,CAAC,2CAA2C,EAAE;;;;4BACvB,qBAAM,OAAO,CAAC,iBAAiB,CAAC,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,EAAA;;wBAAnF,cAAc,GAAG,SAAkE;wBAEzF,MAAM,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;wBACrC,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBACxD,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;wBACjD,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC1D,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;wBACvF,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;wBACrD,MAAM,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC5D,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE;;;;4BACjB,qBAAM,OAAO,CAAC,iBAAiB,CAAC,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,EAAA;;wBAAnF,cAAc,GAAG,SAAkE;wBAEzF,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;wBACjD,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC1D,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;wBACvF,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;wBACvF,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;;;;aACxF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE;QAC9B,EAAE,CAAC,2CAA2C,EAAE;;;;4BACzB,qBAAM,OAAO,CAAC,0BAA0B,CAAC,YAAY,EAAE,eAAe,CAAC,EAAA;;wBAAtF,YAAY,GAAG,SAAuE;wBAE5F,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;wBACnC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAC9C,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;wBACpD,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBACpD,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBACpD,MAAM,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC1D,MAAM,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aACxD,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE;;;;;wBACnD,SAAS,GAAG,CAAC,eAAe,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;wBACvC,qBAAM,OAAO,CAAC,sBAAsB,CAAC,YAAY,EAAE,SAAS,CAAC,EAAA;;wBAA1E,UAAU,GAAG,SAA6D;wBAEhF,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;wBACjC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAC5C,MAAM,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBAEtD,UAAU,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAA,GAAG;4BACvC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;4BACnC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;4BACtC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;4BACtC,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBACnD,CAAC,CAAC,CAAC;;;;aACJ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE;QAC5B,EAAE,CAAC,8CAA8C,EAAE;;;;4BAC5B,qBAAM,OAAO,CAAC,qBAAqB,CAAC,YAAY,EAAE,YAAY,CAAC,EAAA;;wBAA9E,YAAY,GAAG,SAA+D;wBAEpF,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;wBACnC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAC9C,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBACjD,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBACvD,MAAM,CAAC,YAAY,CAAC,qBAAqB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC9D,MAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;wBACpD,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;wBAChD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aAC7D,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE;;;;4BACnB,qBAAM,OAAO,CAAC,2BAA2B,CAAC,YAAY,CAAC,EAAA;;wBAAzE,eAAe,GAAG,SAAuD;wBAE/E,MAAM,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;wBACtC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBACjD,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;wBACjD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAE7D,IAAI,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACpC,KAAK,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;4BACtC,MAAM,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;4BAC7C,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,sBAAsB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;wBACvE,CAAC;;;;aACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE;QACxC,EAAE,CAAC,8CAA8C,EAAE;;;;4BACzB,qBAAM,OAAO,CAAC,6BAA6B,CAAC;4BAClE,aAAa,EAAE,CAAC,YAAY,CAAC;4BAC7B,UAAU,EAAE,sBAAsB;4BAClC,QAAQ,EAAE,eAAe;4BACzB,eAAe,EAAE,cAAc;yBAChC,CAAC,EAAA;;wBALI,eAAe,GAAG,SAKtB;wBAEF,MAAM,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;wBACtC,MAAM,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,CAAC;wBACxD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACpE,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;wBACvD,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC1D,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aACpD,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE;;;;4BAC3B,qBAAM,OAAO,CAAC,6BAA6B,CAAC;4BAClE,aAAa,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;4BAC9B,UAAU,EAAE,oBAAoB;4BAChC,QAAQ,EAAE,QAAQ;4BAClB,eAAe,EAAE,UAAU;yBAC5B,CAAC,EAAA;;wBALI,eAAe,GAAG,SAKtB;wBAEF,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;wBACvD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAEnE,IAAI,eAAe,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BAC1C,KAAK,GAAG,eAAe,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;4BAC5C,MAAM,GAAG,eAAe,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;4BACnD,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,sBAAsB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;wBAC3E,CAAC;;;;aACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE;QACnC,EAAE,CAAC,mDAAmD,EAAE;;;;4BACnC,qBAAM,OAAO,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAA;;wBAA3D,UAAU,GAAG,SAA8C;wBAC3D,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;wBAE9D,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,yBAAyB;;;;aAC7E,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE;;;;4BACpC,qBAAM,OAAO,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAA;;wBAAxD,OAAO,GAAG,SAA8C;wBAC9C,qBAAM,OAAO,CAAC,kBAAkB,CAAC,YAAY,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,EAAA;;wBAAhF,OAAO,GAAG,SAAsE;wBAEtF,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC,sBAAsB,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;;;;aAC7F,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE;;;;;wBAC9C,MAAM,GAAG,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;wBACnE,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBAE7B,qBAAM,OAAO,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAA;;wBAA3C,SAA2C,CAAC;wBAEtC,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBACrB,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;wBAErC,8DAA8D;wBAC9D,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,+BAA+B;;;;aACrF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE;QACzB,EAAE,CAAC,uCAAuC,EAAE;;;;;wBAEpC,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC;wBACnC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;wBAEhD,qBAAM,OAAO,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAA;;wBAA3D,UAAU,GAAG,SAA8C;wBAEjE,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;wBACjC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAC5C,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAEtC,MAAM,CAAC,KAAK,GAAG,aAAa,CAAC;;;;aAC9B,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE;;;4BACrC,qBAAM,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAA;;wBAAlF,SAAkF,CAAC;wBACnF,qBAAM,MAAM,CAAC,OAAO,CAAC,2BAA2B,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAA;;wBAA3F,SAA2F,CAAC;;;;aAC7F,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE;;;;;wBAErC,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC;wBACnC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;4BACzC,EAAE,EAAE,KAAK;4BACT,MAAM,EAAE,GAAG;4BACX,IAAI,EAAE,cAAM,OAAA,OAAO,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,EAA1C,CAA0C;yBACvD,CAAC,CAAC;wBAEgB,qBAAM,OAAO,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAA;;wBAA3D,UAAU,GAAG,SAA8C;wBAEjE,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;wBACjC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAEtC,MAAM,CAAC,KAAK,GAAG,aAAa,CAAC;;;;aAC9B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE;QAChC,EAAE,CAAC,+CAA+C,EAAE;;;;4BAC/B,qBAAM,OAAO,CAAC,kBAAkB,EAAE,EAAA;;wBAA/C,UAAU,GAAG,SAAkC;wBAErD,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;wBACjC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;wBACrD,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;wBACtD,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;;;;aAC5D,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE;;;;4BACtC,qBAAM,OAAO,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAA;;wBAA9C,SAA8C,CAAC;wBAC/C,qBAAM,OAAO,CAAC,UAAU,EAAE,EAAA;;wBAA1B,SAA0B,CAAC;wBAER,qBAAM,OAAO,CAAC,kBAAkB,EAAE,EAAA;;wBAA/C,UAAU,GAAG,SAAkC;wBACrD,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;;;aAC1C,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/SkillMarketDataService.test.ts"], "sourcesContent": ["import { SkillMarketDataService } from '@/lib/skills/SkillMarketDataService';\n\ndescribe('SkillMarketDataService', () => {\n  let service: SkillMarketDataService;\n\n  beforeEach(() => {\n    service = new SkillMarketDataService();\n  });\n\n  describe('Market Data Collection', () => {\n    it('should fetch market data for a single skill', async () => {\n      const marketData = await service.getSkillMarketData('javascript');\n\n      expect(marketData).toBeDefined();\n      expect(marketData.skill).toBe('javascript');\n      expect(marketData.demand).toBeGreaterThan(0);\n      expect(marketData.supply).toBeGreaterThan(0);\n      expect(marketData.averageSalary).toBeGreaterThan(0);\n      expect(marketData.growth).toBeDefined();\n      expect(marketData.difficulty).toBeGreaterThan(0);\n      expect(marketData.timeToLearn).toBeGreaterThan(0);\n      expect(marketData.category).toBeDefined();\n      expect(marketData.lastUpdated).toBeInstanceOf(Date);\n    });\n\n    it('should fetch market data for multiple skills', async () => {\n      const skills = ['javascript', 'react', 'nodejs'];\n      const marketDataList = await service.getMultipleSkillsMarketData(skills);\n\n      expect(marketDataList).toHaveLength(3);\n      expect(marketDataList.every(data => data.skill)).toBe(true);\n      expect(marketDataList.every(data => data.demand > 0)).toBe(true);\n      expect(marketDataList.every(data => data.supply > 0)).toBe(true);\n    });\n\n    it('should handle unknown skills gracefully', async () => {\n      const marketData = await service.getSkillMarketData('unknown-skill-xyz');\n\n      expect(marketData).toBeDefined();\n      expect(marketData.skill).toBe('unknown-skill-xyz');\n      expect(marketData.demand).toBe(0);\n      expect(marketData.supply).toBe(0);\n      expect(marketData.averageSalary).toBe(0);\n    });\n\n    it('should cache market data to avoid repeated API calls', async () => {\n      const startTime = Date.now();\n      await service.getSkillMarketData('javascript');\n      const firstCallTime = Date.now() - startTime;\n\n      const secondStartTime = Date.now();\n      await service.getSkillMarketData('javascript');\n      const secondCallTime = Date.now() - secondStartTime;\n\n      expect(secondCallTime).toBeLessThan(firstCallTime);\n    });\n  });\n\n  describe('Market Trends Analysis', () => {\n    it('should analyze market trends for skills', async () => {\n      const trends = await service.analyzeMarketTrends(['javascript', 'react', 'python']);\n\n      expect(trends).toBeDefined();\n      expect(trends.topDemandSkills).toBeDefined();\n      expect(trends.topDemandSkills.length).toBeGreaterThan(0);\n      expect(trends.fastestGrowingSkills).toBeDefined();\n      expect(trends.highestPayingSkills).toBeDefined();\n      expect(trends.marketGaps).toBeDefined();\n      expect(trends.emergingSkills).toBeDefined();\n      expect(trends.decliningSkills).toBeDefined();\n    });\n\n    it('should identify market gaps (high demand, low supply)', async () => {\n      const trends = await service.analyzeMarketTrends(['javascript', 'react', 'python']);\n\n      expect(trends.marketGaps).toBeDefined();\n      expect(Array.isArray(trends.marketGaps)).toBe(true);\n      \n      if (trends.marketGaps.length > 0) {\n        const gap = trends.marketGaps[0];\n        expect(gap.skill).toBeDefined();\n        expect(gap.demandSupplyRatio).toBeGreaterThan(1);\n        expect(gap.opportunityScore).toBeGreaterThan(0);\n      }\n    });\n\n    it('should rank skills by growth potential', async () => {\n      const trends = await service.analyzeMarketTrends(['javascript', 'react', 'python']);\n\n      expect(trends.fastestGrowingSkills).toBeDefined();\n      expect(Array.isArray(trends.fastestGrowingSkills)).toBe(true);\n      \n      if (trends.fastestGrowingSkills.length > 1) {\n        const first = trends.fastestGrowingSkills[0];\n        const second = trends.fastestGrowingSkills[1];\n        expect(first.growth).toBeGreaterThanOrEqual(second.growth);\n      }\n    });\n  });\n\n  describe('Salary Analysis', () => {\n    it('should provide salary insights for skills', async () => {\n      const salaryInsights = await service.getSalaryInsights(['javascript', 'react', 'python']);\n\n      expect(salaryInsights).toBeDefined();\n      expect(salaryInsights.averageSalary).toBeGreaterThan(0);\n      expect(salaryInsights.salaryRange).toBeDefined();\n      expect(salaryInsights.salaryRange.min).toBeGreaterThan(0);\n      expect(salaryInsights.salaryRange.max).toBeGreaterThan(salaryInsights.salaryRange.min);\n      expect(salaryInsights.topPayingSkills).toBeDefined();\n      expect(salaryInsights.salaryGrowthPotential).toBeDefined();\n    });\n\n    it('should calculate salary percentiles', async () => {\n      const salaryInsights = await service.getSalaryInsights(['javascript', 'react', 'python']);\n\n      expect(salaryInsights.percentiles).toBeDefined();\n      expect(salaryInsights.percentiles.p25).toBeGreaterThan(0);\n      expect(salaryInsights.percentiles.p50).toBeGreaterThan(salaryInsights.percentiles.p25);\n      expect(salaryInsights.percentiles.p75).toBeGreaterThan(salaryInsights.percentiles.p50);\n      expect(salaryInsights.percentiles.p90).toBeGreaterThan(salaryInsights.percentiles.p75);\n    });\n  });\n\n  describe('Geographic Analysis', () => {\n    it('should provide location-based market data', async () => {\n      const locationData = await service.getLocationBasedMarketData('javascript', 'San Francisco');\n\n      expect(locationData).toBeDefined();\n      expect(locationData.skill).toBe('javascript');\n      expect(locationData.location).toBe('San Francisco');\n      expect(locationData.localDemand).toBeGreaterThan(0);\n      expect(locationData.localSalary).toBeGreaterThan(0);\n      expect(locationData.costOfLivingAdjustment).toBeDefined();\n      expect(locationData.remoteOpportunities).toBeDefined();\n    });\n\n    it('should compare market data across multiple locations', async () => {\n      const locations = ['San Francisco', 'New York', 'Austin'];\n      const comparison = await service.compareLocationMarkets('javascript', locations);\n\n      expect(comparison).toBeDefined();\n      expect(comparison.skill).toBe('javascript');\n      expect(comparison.locationComparison).toHaveLength(3);\n      \n      comparison.locationComparison.forEach(loc => {\n        expect(loc.location).toBeDefined();\n        expect(loc.demand).toBeGreaterThan(0);\n        expect(loc.salary).toBeGreaterThan(0);\n        expect(loc.costOfLivingIndex).toBeGreaterThan(0);\n      });\n    });\n  });\n\n  describe('Industry Analysis', () => {\n    it('should provide industry-specific market data', async () => {\n      const industryData = await service.getIndustryMarketData('javascript', 'technology');\n\n      expect(industryData).toBeDefined();\n      expect(industryData.skill).toBe('javascript');\n      expect(industryData.industry).toBe('technology');\n      expect(industryData.industryDemand).toBeGreaterThan(0);\n      expect(industryData.averageIndustrySalary).toBeGreaterThan(0);\n      expect(industryData.growthProjection).toBeDefined();\n      expect(industryData.keyCompanies).toBeDefined();\n      expect(Array.isArray(industryData.keyCompanies)).toBe(true);\n    });\n\n    it('should rank industries by skill demand', async () => {\n      const industryRanking = await service.rankIndustriesBySkillDemand('javascript');\n\n      expect(industryRanking).toBeDefined();\n      expect(industryRanking.skill).toBe('javascript');\n      expect(industryRanking.industries).toBeDefined();\n      expect(Array.isArray(industryRanking.industries)).toBe(true);\n      \n      if (industryRanking.industries.length > 1) {\n        const first = industryRanking.industries[0];\n        const second = industryRanking.industries[1];\n        expect(first.demandScore).toBeGreaterThanOrEqual(second.demandScore);\n      }\n    });\n  });\n\n  describe('Learning Path Recommendations', () => {\n    it('should recommend skills based on market data', async () => {\n      const recommendations = await service.getMarketBasedRecommendations({\n        currentSkills: ['javascript'],\n        careerGoal: 'Full Stack Developer',\n        location: 'San Francisco',\n        experienceLevel: 'intermediate',\n      });\n\n      expect(recommendations).toBeDefined();\n      expect(recommendations.recommendedSkills).toBeDefined();\n      expect(Array.isArray(recommendations.recommendedSkills)).toBe(true);\n      expect(recommendations.learningPriority).toBeDefined();\n      expect(recommendations.marketOpportunities).toBeDefined();\n      expect(recommendations.salaryImpact).toBeDefined();\n    });\n\n    it('should prioritize skills by market opportunity', async () => {\n      const recommendations = await service.getMarketBasedRecommendations({\n        currentSkills: ['html', 'css'],\n        careerGoal: 'Frontend Developer',\n        location: 'Remote',\n        experienceLevel: 'beginner',\n      });\n\n      expect(recommendations.learningPriority).toBeDefined();\n      expect(Array.isArray(recommendations.learningPriority)).toBe(true);\n      \n      if (recommendations.learningPriority.length > 1) {\n        const first = recommendations.learningPriority[0];\n        const second = recommendations.learningPriority[1];\n        expect(first.priorityScore).toBeGreaterThanOrEqual(second.priorityScore);\n      }\n    });\n  });\n\n  describe('Data Freshness & Updates', () => {\n    it('should track data freshness and update when stale', async () => {\n      const marketData = await service.getSkillMarketData('javascript');\n      const dataAge = Date.now() - marketData.lastUpdated.getTime();\n      \n      expect(dataAge).toBeLessThan(24 * 60 * 60 * 1000); // Less than 24 hours old\n    });\n\n    it('should force refresh market data when requested', async () => {\n      const oldData = await service.getSkillMarketData('javascript');\n      const newData = await service.getSkillMarketData('javascript', { forceRefresh: true });\n\n      expect(newData.lastUpdated.getTime()).toBeGreaterThanOrEqual(oldData.lastUpdated.getTime());\n    });\n\n    it('should batch update multiple skills efficiently', async () => {\n      const skills = ['javascript', 'react', 'nodejs', 'python', 'typescript'];\n      const startTime = Date.now();\n      \n      await service.batchUpdateMarketData(skills);\n      \n      const endTime = Date.now();\n      const duration = endTime - startTime;\n      \n      // Batch update should be more efficient than individual calls\n      expect(duration).toBeLessThan(skills.length * 1000); // Less than 1 second per skill\n    });\n  });\n\n  describe('Error Handling', () => {\n    it('should handle API failures gracefully', async () => {\n      // Mock API failure\n      const originalFetch = global.fetch;\n      global.fetch = jest.fn().mockRejectedValue(new Error('API Error'));\n\n      const marketData = await service.getSkillMarketData('javascript');\n\n      expect(marketData).toBeDefined();\n      expect(marketData.skill).toBe('javascript');\n      expect(marketData.isStale).toBe(true);\n\n      global.fetch = originalFetch;\n    });\n\n    it('should validate input parameters', async () => {\n      await expect(service.getSkillMarketData('')).rejects.toThrow('Invalid skill name');\n      await expect(service.getMultipleSkillsMarketData([])).rejects.toThrow('No skills provided');\n    });\n\n    it('should handle rate limiting gracefully', async () => {\n      // Mock rate limiting\n      const originalFetch = global.fetch;\n      global.fetch = jest.fn().mockResolvedValue({\n        ok: false,\n        status: 429,\n        json: () => Promise.resolve({ error: 'Rate limited' }),\n      });\n\n      const marketData = await service.getSkillMarketData('javascript');\n\n      expect(marketData).toBeDefined();\n      expect(marketData.isStale).toBe(true);\n\n      global.fetch = originalFetch;\n    });\n  });\n\n  describe('Performance & Caching', () => {\n    it('should implement intelligent caching strategy', async () => {\n      const cacheStats = await service.getCacheStatistics();\n\n      expect(cacheStats).toBeDefined();\n      expect(cacheStats.hitRate).toBeGreaterThanOrEqual(0);\n      expect(cacheStats.missRate).toBeGreaterThanOrEqual(0);\n      expect(cacheStats.totalRequests).toBeGreaterThanOrEqual(0);\n    });\n\n    it('should clear cache when requested', async () => {\n      await service.getSkillMarketData('javascript');\n      await service.clearCache();\n      \n      const cacheStats = await service.getCacheStatistics();\n      expect(cacheStats.totalRequests).toBe(0);\n    });\n  });\n});\n"], "version": 3}