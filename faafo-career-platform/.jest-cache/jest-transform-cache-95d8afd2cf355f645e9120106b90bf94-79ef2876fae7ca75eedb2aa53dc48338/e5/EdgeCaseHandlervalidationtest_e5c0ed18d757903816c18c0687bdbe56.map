{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/EdgeCaseHandler.validation.test.ts", "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,gEAA+D;AAK/D,wCAAwC;AACxC,IAAM,kBAAkB,GAAG,cAAM,OAAA,CAAC;IAChC,oBAAoB,EAAE;QACpB,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC3B,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC5B,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;QACzB,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC3B,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;QACxB,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;KACzB;IACR,qBAAqB,EAAE;QACrB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC7B,2BAA2B,EAAE,IAAI,CAAC,EAAE,EAAE;QACtC,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC9B,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC5B,0BAA0B,EAAE,IAAI,CAAC,EAAE,EAAE;QACrC,6BAA6B,EAAE,IAAI,CAAC,EAAE,EAAE;KAClC;IACR,uBAAuB,EAAE;QACvB,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;QAC/B,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;QACzB,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;KACtB;CACT,CAAC,EAtB+B,CAsB/B,CAAC;AAEH,QAAQ,CAAC,oCAAoC,EAAE;IAC7C,IAAI,eAAgC,CAAC;IACrC,IAAI,KAA4C,CAAC;IAEjD,UAAU,CAAC;QACT,KAAK,GAAG,kBAAkB,EAAE,CAAC;QAC7B,eAAe,GAAG,IAAI,iCAAe,CACnC,KAAK,CAAC,oBAAoB,EAC1B,KAAK,CAAC,qBAAqB,EAC3B,KAAK,CAAC,uBAAuB,CAC9B,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE;QACpC,EAAE,CAAC,6CAA6C,EAAE;;;;4BACjC,qBAAM,eAAe,CAAC,qBAAqB,CAAC,IAAW,CAAC,EAAA;;wBAAjE,MAAM,GAAG,SAAwD;wBAEvE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;wBAChD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBAClD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC3C,EAAE,IAAI,CAAC,CAAC,CAAC,mBAAmB;QAE7B,EAAE,CAAC,+CAA+C,EAAE;;;;4BACnC,qBAAM,eAAe,CAAC,4BAA4B,CAAC,SAAgB,CAAC,EAAA;;wBAA7E,MAAM,GAAG,SAAoE;wBAEnF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;wBAChD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBAClD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC3C,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,wCAAwC,EAAE;;;;4BAC5B,qBAAM,eAAe,CAAC,uBAAuB,CAAC,IAAW,CAAC,EAAA;;wBAAnE,MAAM,GAAG,SAA0D;wBAEzE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;wBAChD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBAClD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC3C,EAAE,IAAI,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE;QACvC,EAAE,CAAC,mCAAmC,EAAE;;;;;wBAChC,OAAO,GAAG;4BACd,MAAM,EAAE,EAAE;4BACV,QAAQ,EAAE,EAAE;4BACZ,YAAY,EAAE,EAAE;yBACjB,CAAC;wBAEa,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAA;;wBAA7D,MAAM,GAAG,SAAoD;wBAEnE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;wBACxD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;;;;aACnD,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,kCAAkC,EAAE;;;;;wBAC/B,OAAO,GAAG;4BACd,MAAM,EAAE,GAAG,EAAE,mBAAmB;4BAChC,QAAQ,EAAE,cAAc,EAAE,kBAAkB;4BAC5C,YAAY,EAAE,IAAI,EAAE,mBAAmB;yBACxC,CAAC;wBAEa,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAc,CAAC,EAAA;;wBAApE,MAAM,GAAG,SAA2D;wBAE1E,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;wBACrD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;;;;aACnD,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,sCAAsC,EAAE;;;;;wBACnC,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;wBAChC,OAAO,GAAG;4BACd,MAAM,EAAE,WAAW;4BACnB,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,qBAAqB;4BAC5D,YAAY,EAAE,WAAW;yBAC1B,CAAC;wBAEa,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAA;;wBAA7D,MAAM,GAAG,SAAoD;wBAEnE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;wBACnE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;;;;aACnD,EAAE,IAAI,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE;QAC9B,EAAE,CAAC,uCAAuC,EAAE;;;;;wBACpC,gBAAgB,GAAG;4BACvB,MAAM,EAAE,yBAAyB;4BACjC,QAAQ,EAAE,CAAC,2BAA2B,CAAC;4BACvC,YAAY,EAAE,cAAc;yBAC7B,CAAC;wBAEa,qBAAM,eAAe,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,EAAA;;wBAAtE,MAAM,GAAG,SAA6D;wBAE5E,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,sCAAsC,CAAC,CAAC;wBACvE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBAChD,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aACzC,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,6BAA6B,EAAE;;;;;wBAC1B,UAAU,GAAG;4BACjB,MAAM,EAAE,+BAA+B;4BACvC,QAAQ,EAAE,CAAC,8BAA8B,CAAC;4BAC1C,YAAY,EAAE,qBAAqB;yBACpC,CAAC;wBAEa,qBAAM,eAAe,CAAC,qBAAqB,CAAC,UAAU,CAAC,EAAA;;wBAAhE,MAAM,GAAG,SAAuD;wBAEtE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,sCAAsC,CAAC,CAAC;wBACvE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBAChD,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aACzC,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,yCAAyC,EAAE;;;;;wBACtC,aAAa,GAAG,mCAAmC,CAAC;wBAE3C,qBAAM,eAAe,CAAC,qBAAqB,CAAC,aAAa,CAAC,EAAA;;wBAAnE,MAAM,GAAG,SAA0D;wBAEzE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;wBACtD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;;;;aAChD,EAAE,IAAI,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE;QACjC,EAAE,CAAC,uCAAuC,EAAE;;;;;wBACpC,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,YAAY,EAAE,UAAU;4BACxB,SAAS,EAAE,CAAC;4BACZ,MAAM,EAAE,CAAC;yBACV,CAAC;wBAEa,qBAAM,eAAe,CAAC,4BAA4B,CAAC,OAAO,CAAC,EAAA;;wBAApE,MAAM,GAAG,SAA2D;wBAE1E,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;wBAClE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;;;;aACnD,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,uCAAuC,EAAE;;;;;wBACpC,OAAO,GAAG;4BACd,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC;4BACvB,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,oBAAoB;4BACxD,YAAY,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC;4BAC7B,SAAS,EAAE,IAAI;4BACf,MAAM,EAAE,OAAO;yBAChB,CAAC;wBAEa,qBAAM,eAAe,CAAC,4BAA4B,CAAC,OAAO,CAAC,EAAA;;wBAApE,MAAM,GAAG,SAA2D;wBAE1E,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;wBACnE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;;;;aACnD,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,+BAA+B,EAAE;;;;;wBAC5B,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,QAAQ,EAAE,CAAC,YAAY,CAAC;4BACxB,YAAY,EAAE,UAAU;4BACxB,SAAS,EAAE,CAAC,CAAC;4BACb,MAAM,EAAE,CAAC,GAAG;4BACZ,YAAY,EAAE,CAAC,EAAE;yBAClB,CAAC;wBAEa,qBAAM,eAAe,CAAC,4BAA4B,CAAC,OAAO,CAAC,EAAA;;wBAApE,MAAM,GAAG,SAA2D;wBAE1E,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;wBAC9D,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;;;;aACnD,EAAE,IAAI,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE;QACzC,EAAE,CAAC,2CAA2C,EAAE;;;;;wBACxC,OAAO,GAAG;4BACd,MAAM,EAAE,OAAO;4BACf,QAAQ,EAAE,CAAC,aAAa,EAAE,QAAQ,CAAC;4BACnC,YAAY,EAAE,wBAAwB;yBACvC,CAAC;wBAEF,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC;4BAC5D,EAAE,EAAE,gBAAgB;4BACpB,MAAM,EAAE,OAAO,CAAC,MAAM;yBAChB,CAAC,CAAC;wBAEK,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAA;;wBAA7D,MAAM,GAAG,SAAoD;wBAEnE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;wBAClC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC7C,EAAE,IAAI,CAAC,CAAC;QAET,EAAE,CAAC,yCAAyC,EAAE;;;;;wBACtC,OAAO,GAAG;4BACd,MAAM,EAAE,WAAW;4BACnB,QAAQ,EAAE,CAAC,cAAc,EAAE,SAAS,CAAC;4BACrC,YAAY,EAAE,wBAAwB;yBACvC,CAAC;wBAEa,qBAAM,eAAe,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAA;;wBAA7D,MAAM,GAAG,SAAoD;wBAEnE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;wBACzD,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;;;;aAC/D,EAAE,IAAI,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/EdgeCaseHandler.validation.test.ts"], "sourcesContent": ["/**\n * EdgeCaseHandler Validation Tests\n * Focused on input validation and security edge cases\n * Optimized for fast execution\n */\n\nimport { EdgeCaseHandler } from '@/lib/skills/EdgeCaseHandler';\nimport { SkillAssessmentEngine } from '@/lib/skills/SkillAssessmentEngine';\nimport { SkillMarketDataService } from '@/lib/skills/SkillMarketDataService';\nimport { PersonalizedLearningPathService } from '@/lib/skills/PersonalizedLearningPathService';\n\n// Shared mock factories for performance\nconst createMockServices = () => ({\n  mockAssessmentEngine: {\n    createAssessment: jest.fn(),\n    generateQuestions: jest.fn(),\n    submitResponse: jest.fn(),\n    calculateResults: jest.fn(),\n    getAssessment: jest.fn(),\n    getAssessmentsByUser: jest.fn(),\n  } as any,\n  mockMarketDataService: {\n    getSkillMarketData: jest.fn(),\n    getMultipleSkillsMarketData: jest.fn(),\n    analyzeMarketTrends: jest.fn(),\n    getSalaryInsights: jest.fn(),\n    getLocationBasedMarketData: jest.fn(),\n    getMarketBasedRecommendations: jest.fn(),\n  } as any,\n  mockLearningPathService: {\n    generateLearningPath: jest.fn(),\n    updateProgress: jest.fn(),\n    completeMilestone: jest.fn(),\n  } as any,\n});\n\ndescribe('EdgeCaseHandler - Input Validation', () => {\n  let edgeCaseHandler: EdgeCaseHandler;\n  let mocks: ReturnType<typeof createMockServices>;\n\n  beforeEach(() => {\n    mocks = createMockServices();\n    edgeCaseHandler = new EdgeCaseHandler(\n      mocks.mockAssessmentEngine,\n      mocks.mockMarketDataService,\n      mocks.mockLearningPathService\n    );\n  });\n\n  describe('Null and Undefined Inputs', () => {\n    it('should handle null skill assessment request', async () => {\n      const result = await edgeCaseHandler.handleSkillAssessment(null as any);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Invalid input');\n      expect(result.errorType).toBe('VALIDATION_ERROR');\n      expect(result.fallbackData).toBeDefined();\n    }, 5000); // 5 second timeout\n\n    it('should handle undefined learning path request', async () => {\n      const result = await edgeCaseHandler.handleLearningPathGeneration(undefined as any);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Invalid input');\n      expect(result.errorType).toBe('VALIDATION_ERROR');\n      expect(result.fallbackData).toBeDefined();\n    }, 5000);\n\n    it('should handle null market data request', async () => {\n      const result = await edgeCaseHandler.handleMarketDataRequest(null as any);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Invalid input');\n      expect(result.errorType).toBe('VALIDATION_ERROR');\n      expect(result.fallbackData).toBeDefined();\n    }, 5000);\n  });\n\n  describe('Empty and Invalid Data Types', () => {\n    it('should handle empty string inputs', async () => {\n      const request = {\n        userId: '',\n        skillIds: [],\n        careerPathId: '',\n      };\n\n      const result = await edgeCaseHandler.handleSkillAssessment(request);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Empty required fields');\n      expect(result.errorType).toBe('VALIDATION_ERROR');\n    }, 3000);\n\n    it('should handle invalid data types', async () => {\n      const request = {\n        userId: 123, // Should be string\n        skillIds: 'not-an-array', // Should be array\n        careerPathId: true, // Should be string\n      };\n\n      const result = await edgeCaseHandler.handleSkillAssessment(request as any);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Invalid data types');\n      expect(result.errorType).toBe('VALIDATION_ERROR');\n    }, 3000);\n\n    it('should handle extremely large inputs', async () => {\n      const largeString = 'a'.repeat(10000); // Reduced from 1MB for performance\n      const request = {\n        userId: largeString,\n        skillIds: Array(100).fill('skill-id'), // Reduced from 10000\n        careerPathId: largeString,\n      };\n\n      const result = await edgeCaseHandler.handleSkillAssessment(request);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Values exceed maximum thresholds');\n      expect(result.errorType).toBe('VALIDATION_ERROR');\n    }, 5000);\n  });\n\n  describe('Security Edge Cases', () => {\n    it('should prevent SQL injection attempts', async () => {\n      const maliciousRequest = {\n        userId: \"'; DROP TABLE users; --\",\n        skillIds: [\"'; DELETE FROM skills; --\"],\n        careerPathId: \"1' OR '1'='1\",\n      };\n\n      const result = await edgeCaseHandler.handleSkillAssessment(maliciousRequest);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Potentially malicious input detected');\n      expect(result.errorType).toBe('SECURITY_ERROR');\n      expect(result.securityAlert).toBe(true);\n    }, 3000);\n\n    it('should prevent XSS attempts', async () => {\n      const xssRequest = {\n        userId: '<script>alert(\"xss\")</script>',\n        skillIds: ['<img src=x onerror=alert(1)>'],\n        careerPathId: 'javascript:alert(1)',\n      };\n\n      const result = await edgeCaseHandler.handleSkillAssessment(xssRequest);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Potentially malicious input detected');\n      expect(result.errorType).toBe('SECURITY_ERROR');\n      expect(result.securityAlert).toBe(true);\n    }, 3000);\n\n    it('should handle malformed JSON gracefully', async () => {\n      const malformedJson = '{\"userId\": \"test\", \"skillIds\": [}';\n\n      const result = await edgeCaseHandler.parseAndValidateInput(malformedJson);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Invalid JSON format');\n      expect(result.errorType).toBe('PARSING_ERROR');\n    }, 2000);\n  });\n\n  describe('Boundary Value Testing', () => {\n    it('should handle minimum boundary values', async () => {\n      const request = {\n        userId: 'user-123',\n        skillIds: ['javascript'],\n        careerPathId: 'path-456',\n        timeframe: 0,\n        budget: 0,\n      };\n\n      const result = await edgeCaseHandler.handleLearningPathGeneration(request);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Values below minimum thresholds');\n      expect(result.errorType).toBe('VALIDATION_ERROR');\n    }, 3000);\n\n    it('should handle maximum boundary values', async () => {\n      const request = {\n        userId: 'u'.repeat(256),\n        skillIds: Array(101).fill('skill'), // Reduced from 1001\n        careerPathId: 'c'.repeat(256),\n        timeframe: 1000,\n        budget: 1000001,\n      };\n\n      const result = await edgeCaseHandler.handleLearningPathGeneration(request);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Values exceed maximum thresholds');\n      expect(result.errorType).toBe('VALIDATION_ERROR');\n    }, 5000);\n\n    it('should handle negative values', async () => {\n      const request = {\n        userId: 'user-123',\n        skillIds: ['javascript'],\n        careerPathId: 'path-456',\n        timeframe: -5,\n        budget: -100,\n        availability: -10,\n      };\n\n      const result = await edgeCaseHandler.handleLearningPathGeneration(request);\n\n      expect(result.success).toBe(false);\n      expect(result.error).toContain('Negative values not allowed');\n      expect(result.errorType).toBe('VALIDATION_ERROR');\n    }, 3000);\n  });\n\n  describe('Unicode and Special Characters', () => {\n    it('should handle Unicode characters properly', async () => {\n      const request = {\n        userId: '用户123',\n        skillIds: ['JavaScript™', 'React®'],\n        careerPathId: 'Développeur Full-Stack',\n      };\n\n      mocks.mockAssessmentEngine.createAssessment.mockResolvedValue({\n        id: 'assessment-123',\n        userId: request.userId,\n      } as any);\n\n      const result = await edgeCaseHandler.handleSkillAssessment(request);\n\n      expect(result.success).toBe(true);\n      expect(result.data).toBeDefined();\n      expect(result.sanitizedInput).toBeDefined();\n    }, 3000);\n\n    it('should handle emoji and special Unicode', async () => {\n      const request = {\n        userId: 'user🚀123',\n        skillIds: ['JavaScript💻', 'React⚛️'],\n        careerPathId: 'Full-Stack Developer🌟',\n      };\n\n      const result = await edgeCaseHandler.handleSkillAssessment(request);\n\n      expect(result.success).toBe(true);\n      expect(result.sanitizedInput.userId).not.toContain('🚀');\n      expect(result.sanitizedInput.skillIds[0]).not.toContain('💻');\n    }, 3000);\n  });\n});\n"], "version": 3}