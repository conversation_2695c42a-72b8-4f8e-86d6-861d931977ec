0af97540f73b9bc620a199d652c171a4
"use strict";
/**
 * EdgeCaseHandler Validation Tests
 * Focused on input validation and security edge cases
 * Optimized for fast execution
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EdgeCaseHandler_1 = require("@/lib/skills/EdgeCaseHandler");
// Shared mock factories for performance
var createMockServices = function () { return ({
    mockAssessmentEngine: {
        createAssessment: jest.fn(),
        generateQuestions: jest.fn(),
        submitResponse: jest.fn(),
        calculateResults: jest.fn(),
        getAssessment: jest.fn(),
        getAssessmentsByUser: jest.fn(),
    },
    mockMarketDataService: {
        getSkillMarketData: jest.fn(),
        getMultipleSkillsMarketData: jest.fn(),
        analyzeMarketTrends: jest.fn(),
        getSalaryInsights: jest.fn(),
        getLocationBasedMarketData: jest.fn(),
        getMarketBasedRecommendations: jest.fn(),
    },
    mockLearningPathService: {
        generateLearningPath: jest.fn(),
        updateProgress: jest.fn(),
        completeMilestone: jest.fn(),
    },
}); };
describe('EdgeCaseHandler - Input Validation', function () {
    var edgeCaseHandler;
    var mocks;
    beforeEach(function () {
        mocks = createMockServices();
        edgeCaseHandler = new EdgeCaseHandler_1.EdgeCaseHandler(mocks.mockAssessmentEngine, mocks.mockMarketDataService, mocks.mockLearningPathService);
    });
    describe('Null and Undefined Inputs', function () {
        it('should handle null skill assessment request', function () { return __awaiter(void 0, void 0, void 0, function () {
            var result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(null)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Invalid input');
                        expect(result.errorType).toBe('VALIDATION_ERROR');
                        expect(result.fallbackData).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 5000); // 5 second timeout
        it('should handle undefined learning path request', function () { return __awaiter(void 0, void 0, void 0, function () {
            var result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, edgeCaseHandler.handleLearningPathGeneration(undefined)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Invalid input');
                        expect(result.errorType).toBe('VALIDATION_ERROR');
                        expect(result.fallbackData).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
        it('should handle null market data request', function () { return __awaiter(void 0, void 0, void 0, function () {
            var result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, edgeCaseHandler.handleMarketDataRequest(null)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Invalid input');
                        expect(result.errorType).toBe('VALIDATION_ERROR');
                        expect(result.fallbackData).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
    });
    describe('Empty and Invalid Data Types', function () {
        it('should handle empty string inputs', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: '',
                            skillIds: [],
                            careerPathId: '',
                        };
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Empty required fields');
                        expect(result.errorType).toBe('VALIDATION_ERROR');
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
        it('should handle invalid data types', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 123, // Should be string
                            skillIds: 'not-an-array', // Should be array
                            careerPathId: true, // Should be string
                        };
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Invalid data types');
                        expect(result.errorType).toBe('VALIDATION_ERROR');
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
        it('should handle extremely large inputs', function () { return __awaiter(void 0, void 0, void 0, function () {
            var largeString, request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        largeString = 'a'.repeat(10000);
                        request = {
                            userId: largeString,
                            skillIds: Array(100).fill('skill-id'), // Reduced from 10000
                            careerPathId: largeString,
                        };
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Values exceed maximum thresholds');
                        expect(result.errorType).toBe('VALIDATION_ERROR');
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
    });
    describe('Security Edge Cases', function () {
        it('should prevent SQL injection attempts', function () { return __awaiter(void 0, void 0, void 0, function () {
            var maliciousRequest, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        maliciousRequest = {
                            userId: "'; DROP TABLE users; --",
                            skillIds: ["'; DELETE FROM skills; --"],
                            careerPathId: "1' OR '1'='1",
                        };
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(maliciousRequest)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Potentially malicious input detected');
                        expect(result.errorType).toBe('SECURITY_ERROR');
                        expect(result.securityAlert).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
        it('should prevent XSS attempts', function () { return __awaiter(void 0, void 0, void 0, function () {
            var xssRequest, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        xssRequest = {
                            userId: '<script>alert("xss")</script>',
                            skillIds: ['<img src=x onerror=alert(1)>'],
                            careerPathId: 'javascript:alert(1)',
                        };
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(xssRequest)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Potentially malicious input detected');
                        expect(result.errorType).toBe('SECURITY_ERROR');
                        expect(result.securityAlert).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
        it('should handle malformed JSON gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var malformedJson, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        malformedJson = '{"userId": "test", "skillIds": [}';
                        return [4 /*yield*/, edgeCaseHandler.parseAndValidateInput(malformedJson)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Invalid JSON format');
                        expect(result.errorType).toBe('PARSING_ERROR');
                        return [2 /*return*/];
                }
            });
        }); }, 2000);
    });
    describe('Boundary Value Testing', function () {
        it('should handle minimum boundary values', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                            timeframe: 0,
                            budget: 0,
                        };
                        return [4 /*yield*/, edgeCaseHandler.handleLearningPathGeneration(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Values below minimum thresholds');
                        expect(result.errorType).toBe('VALIDATION_ERROR');
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
        it('should handle maximum boundary values', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'u'.repeat(256),
                            skillIds: Array(101).fill('skill'), // Reduced from 1001
                            careerPathId: 'c'.repeat(256),
                            timeframe: 1000,
                            budget: 1000001,
                        };
                        return [4 /*yield*/, edgeCaseHandler.handleLearningPathGeneration(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Values exceed maximum thresholds');
                        expect(result.errorType).toBe('VALIDATION_ERROR');
                        return [2 /*return*/];
                }
            });
        }); }, 5000);
        it('should handle negative values', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user-123',
                            skillIds: ['javascript'],
                            careerPathId: 'path-456',
                            timeframe: -5,
                            budget: -100,
                            availability: -10,
                        };
                        return [4 /*yield*/, edgeCaseHandler.handleLearningPathGeneration(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(false);
                        expect(result.error).toContain('Negative values not allowed');
                        expect(result.errorType).toBe('VALIDATION_ERROR');
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
    });
    describe('Unicode and Special Characters', function () {
        it('should handle Unicode characters properly', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: '用户123',
                            skillIds: ['JavaScript™', 'React®'],
                            careerPathId: 'Développeur Full-Stack',
                        };
                        mocks.mockAssessmentEngine.createAssessment.mockResolvedValue({
                            id: 'assessment-123',
                            userId: request.userId,
                        });
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(true);
                        expect(result.data).toBeDefined();
                        expect(result.sanitizedInput).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
        it('should handle emoji and special Unicode', function () { return __awaiter(void 0, void 0, void 0, function () {
            var request, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        request = {
                            userId: 'user🚀123',
                            skillIds: ['JavaScript💻', 'React⚛️'],
                            careerPathId: 'Full-Stack Developer🌟',
                        };
                        return [4 /*yield*/, edgeCaseHandler.handleSkillAssessment(request)];
                    case 1:
                        result = _a.sent();
                        expect(result.success).toBe(true);
                        expect(result.sanitizedInput.userId).not.toContain('🚀');
                        expect(result.sanitizedInput.skillIds[0]).not.toContain('💻');
                        return [2 /*return*/];
                }
            });
        }); }, 3000);
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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