8206083448f89f2b8f0a4eaa4aecb4dc
"use strict";

/* istanbul ignore next */
function cov_bw4kgqzov() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/validation.ts";
  var hash = "865088a3364040e11573eab2811e990a6902af90";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/validation.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 3,
          column: 577
        }
      },
      "2": {
        start: {
          line: 4,
          column: 0
        },
        end: {
          line: 4,
          column: 50
        }
      },
      "3": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 38
        }
      },
      "4": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 6,
          column: 26
        }
      },
      "5": {
        start: {
          line: 8,
          column: 39
        },
        end: {
          line: 213,
          column: 3
        }
      },
      "6": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 62,
          column: 6
        }
      },
      "7": {
        start: {
          line: 15,
          column: 8
        },
        end: {
          line: 15,
          column: 49
        }
      },
      "8": {
        start: {
          line: 15,
          column: 34
        },
        end: {
          line: 15,
          column: 47
        }
      },
      "9": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 18,
          column: 9
        }
      },
      "10": {
        start: {
          line: 17,
          column: 12
        },
        end: {
          line: 17,
          column: 22
        }
      },
      "11": {
        start: {
          line: 19,
          column: 24
        },
        end: {
          line: 19,
          column: 29
        }
      },
      "12": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 23,
          column: 11
        }
      },
      "13": {
        start: {
          line: 22,
          column: 12
        },
        end: {
          line: 22,
          column: 55
        }
      },
      "14": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 27,
          column: 11
        }
      },
      "15": {
        start: {
          line: 26,
          column: 12
        },
        end: {
          line: 26,
          column: 55
        }
      },
      "16": {
        start: {
          line: 29,
          column: 8
        },
        end: {
          line: 35,
          column: 9
        }
      },
      "17": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 32,
          column: 15
        }
      },
      "18": {
        start: {
          line: 31,
          column: 16
        },
        end: {
          line: 31,
          column: 59
        }
      },
      "19": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 58
        }
      },
      "20": {
        start: {
          line: 37,
          column: 8
        },
        end: {
          line: 39,
          column: 11
        }
      },
      "21": {
        start: {
          line: 38,
          column: 12
        },
        end: {
          line: 38,
          column: 55
        }
      },
      "22": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 43,
          column: 11
        }
      },
      "23": {
        start: {
          line: 42,
          column: 12
        },
        end: {
          line: 42,
          column: 55
        }
      },
      "24": {
        start: {
          line: 45,
          column: 8
        },
        end: {
          line: 47,
          column: 11
        }
      },
      "25": {
        start: {
          line: 46,
          column: 12
        },
        end: {
          line: 46,
          column: 55
        }
      },
      "26": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 49,
          column: 79
        }
      },
      "27": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 53,
          column: 9
        }
      },
      "28": {
        start: {
          line: 52,
          column: 12
        },
        end: {
          line: 52,
          column: 58
        }
      },
      "29": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 55,
          column: 58
        }
      },
      "30": {
        start: {
          line: 57,
          column: 24
        },
        end: {
          line: 57,
          column: 50
        }
      },
      "31": {
        start: {
          line: 58,
          column: 8
        },
        end: {
          line: 60,
          column: 9
        }
      },
      "32": {
        start: {
          line: 59,
          column: 12
        },
        end: {
          line: 59,
          column: 54
        }
      },
      "33": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 61,
          column: 25
        }
      },
      "34": {
        start: {
          line: 66,
          column: 4
        },
        end: {
          line: 101,
          column: 6
        }
      },
      "35": {
        start: {
          line: 67,
          column: 8
        },
        end: {
          line: 67,
          column: 49
        }
      },
      "36": {
        start: {
          line: 67,
          column: 34
        },
        end: {
          line: 67,
          column: 47
        }
      },
      "37": {
        start: {
          line: 68,
          column: 22
        },
        end: {
          line: 68,
          column: 24
        }
      },
      "38": {
        start: {
          line: 70,
          column: 8
        },
        end: {
          line: 72,
          column: 9
        }
      },
      "39": {
        start: {
          line: 71,
          column: 12
        },
        end: {
          line: 71,
          column: 50
        }
      },
      "40": {
        start: {
          line: 74,
          column: 8
        },
        end: {
          line: 76,
          column: 9
        }
      },
      "41": {
        start: {
          line: 74,
          column: 70
        },
        end: {
          line: 74,
          column: 97
        }
      },
      "42": {
        start: {
          line: 75,
          column: 12
        },
        end: {
          line: 75,
          column: 46
        }
      },
      "43": {
        start: {
          line: 78,
          column: 8
        },
        end: {
          line: 80,
          column: 9
        }
      },
      "44": {
        start: {
          line: 78,
          column: 66
        },
        end: {
          line: 78,
          column: 93
        }
      },
      "45": {
        start: {
          line: 79,
          column: 12
        },
        end: {
          line: 79,
          column: 49
        }
      },
      "46": {
        start: {
          line: 82,
          column: 8
        },
        end: {
          line: 84,
          column: 9
        }
      },
      "47": {
        start: {
          line: 82,
          column: 56
        },
        end: {
          line: 82,
          column: 83
        }
      },
      "48": {
        start: {
          line: 83,
          column: 12
        },
        end: {
          line: 83,
          column: 55
        }
      },
      "49": {
        start: {
          line: 86,
          column: 8
        },
        end: {
          line: 88,
          column: 9
        }
      },
      "50": {
        start: {
          line: 86,
          column: 66
        },
        end: {
          line: 86,
          column: 93
        }
      },
      "51": {
        start: {
          line: 87,
          column: 12
        },
        end: {
          line: 87,
          column: 42
        }
      },
      "52": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 92,
          column: 9
        }
      },
      "53": {
        start: {
          line: 90,
          column: 67
        },
        end: {
          line: 90,
          column: 94
        }
      },
      "54": {
        start: {
          line: 91,
          column: 12
        },
        end: {
          line: 91,
          column: 43
        }
      },
      "55": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 96,
          column: 9
        }
      },
      "56": {
        start: {
          line: 94,
          column: 67
        },
        end: {
          line: 94,
          column: 94
        }
      },
      "57": {
        start: {
          line: 95,
          column: 12
        },
        end: {
          line: 95,
          column: 43
        }
      },
      "58": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 100,
          column: 10
        }
      },
      "59": {
        start: {
          line: 105,
          column: 4
        },
        end: {
          line: 115,
          column: 6
        }
      },
      "60": {
        start: {
          line: 107,
          column: 27
        },
        end: {
          line: 107,
          column: 77
        }
      },
      "61": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 109,
          column: 24
        }
      },
      "62": {
        start: {
          line: 109,
          column: 12
        },
        end: {
          line: 109,
          column: 24
        }
      },
      "63": {
        start: {
          line: 111,
          column: 27
        },
        end: {
          line: 111,
          column: 79
        }
      },
      "64": {
        start: {
          line: 112,
          column: 8
        },
        end: {
          line: 113,
          column: 24
        }
      },
      "65": {
        start: {
          line: 113,
          column: 12
        },
        end: {
          line: 113,
          column: 24
        }
      },
      "66": {
        start: {
          line: 114,
          column: 8
        },
        end: {
          line: 114,
          column: 21
        }
      },
      "67": {
        start: {
          line: 119,
          column: 4
        },
        end: {
          line: 127,
          column: 6
        }
      },
      "68": {
        start: {
          line: 120,
          column: 8
        },
        end: {
          line: 126,
          column: 38
        }
      },
      "69": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 152,
          column: 6
        }
      },
      "70": {
        start: {
          line: 132,
          column: 8
        },
        end: {
          line: 151,
          column: 9
        }
      },
      "71": {
        start: {
          line: 134,
          column: 28
        },
        end: {
          line: 134,
          column: 81
        }
      },
      "72": {
        start: {
          line: 136,
          column: 29
        },
        end: {
          line: 136,
          column: 61
        }
      },
      "73": {
        start: {
          line: 137,
          column: 12
        },
        end: {
          line: 142,
          column: 13
        }
      },
      "74": {
        start: {
          line: 138,
          column: 16
        },
        end: {
          line: 141,
          column: 18
        }
      },
      "75": {
        start: {
          line: 143,
          column: 25
        },
        end: {
          line: 143,
          column: 46
        }
      },
      "76": {
        start: {
          line: 144,
          column: 12
        },
        end: {
          line: 144,
          column: 51
        }
      },
      "77": {
        start: {
          line: 147,
          column: 12
        },
        end: {
          line: 150,
          column: 14
        }
      },
      "78": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 166,
          column: 6
        }
      },
      "79": {
        start: {
          line: 168,
          column: 4
        },
        end: {
          line: 174,
          column: 6
        }
      },
      "80": {
        start: {
          line: 176,
          column: 4
        },
        end: {
          line: 187,
          column: 6
        }
      },
      "81": {
        start: {
          line: 189,
          column: 4
        },
        end: {
          line: 194,
          column: 6
        }
      },
      "82": {
        start: {
          line: 196,
          column: 4
        },
        end: {
          line: 204,
          column: 6
        }
      },
      "83": {
        start: {
          line: 206,
          column: 4
        },
        end: {
          line: 211,
          column: 6
        }
      },
      "84": {
        start: {
          line: 212,
          column: 4
        },
        end: {
          line: 212,
          column: 29
        }
      },
      "85": {
        start: {
          line: 214,
          column: 0
        },
        end: {
          line: 214,
          column: 46
        }
      },
      "86": {
        start: {
          line: 217,
          column: 4
        },
        end: {
          line: 217,
          column: 70
        }
      },
      "87": {
        start: {
          line: 220,
          column: 4
        },
        end: {
          line: 220,
          column: 51
        }
      },
      "88": {
        start: {
          line: 220,
          column: 32
        },
        end: {
          line: 220,
          column: 49
        }
      },
      "89": {
        start: {
          line: 221,
          column: 4
        },
        end: {
          line: 225,
          column: 7
        }
      },
      "90": {
        start: {
          line: 228,
          column: 0
        },
        end: {
          line: 236,
          column: 3
        }
      },
      "91": {
        start: {
          line: 237,
          column: 0
        },
        end: {
          line: 244,
          column: 3
        }
      },
      "92": {
        start: {
          line: 245,
          column: 0
        },
        end: {
          line: 249,
          column: 3
        }
      },
      "93": {
        start: {
          line: 250,
          column: 0
        },
        end: {
          line: 254,
          column: 3
        }
      },
      "94": {
        start: {
          line: 255,
          column: 0
        },
        end: {
          line: 263,
          column: 3
        }
      },
      "95": {
        start: {
          line: 265,
          column: 0
        },
        end: {
          line: 291,
          column: 3
        }
      },
      "96": {
        start: {
          line: 293,
          column: 0
        },
        end: {
          line: 302,
          column: 3
        }
      },
      "97": {
        start: {
          line: 303,
          column: 0
        },
        end: {
          line: 313,
          column: 3
        }
      },
      "98": {
        start: {
          line: 315,
          column: 0
        },
        end: {
          line: 320,
          column: 3
        }
      },
      "99": {
        start: {
          line: 322,
          column: 0
        },
        end: {
          line: 333,
          column: 3
        }
      },
      "100": {
        start: {
          line: 335,
          column: 0
        },
        end: {
          line: 340,
          column: 3
        }
      },
      "101": {
        start: {
          line: 342,
          column: 0
        },
        end: {
          line: 348,
          column: 3
        }
      },
      "102": {
        start: {
          line: 350,
          column: 0
        },
        end: {
          line: 357,
          column: 3
        }
      },
      "103": {
        start: {
          line: 352,
          column: 36
        },
        end: {
          line: 352,
          column: 63
        }
      },
      "104": {
        start: {
          line: 355,
          column: 36
        },
        end: {
          line: 355,
          column: 71
        }
      },
      "105": {
        start: {
          line: 358,
          column: 0
        },
        end: {
          line: 365,
          column: 3
        }
      },
      "106": {
        start: {
          line: 360,
          column: 36
        },
        end: {
          line: 360,
          column: 71
        }
      },
      "107": {
        start: {
          line: 367,
          column: 0
        },
        end: {
          line: 380,
          column: 3
        }
      },
      "108": {
        start: {
          line: 378,
          column: 36
        },
        end: {
          line: 378,
          column: 71
        }
      },
      "109": {
        start: {
          line: 382,
          column: 0
        },
        end: {
          line: 393,
          column: 3
        }
      },
      "110": {
        start: {
          line: 395,
          column: 0
        },
        end: {
          line: 398,
          column: 3
        }
      },
      "111": {
        start: {
          line: 396,
          column: 76
        },
        end: {
          line: 396,
          column: 111
        }
      },
      "112": {
        start: {
          line: 397,
          column: 77
        },
        end: {
          line: 397,
          column: 113
        }
      },
      "113": {
        start: {
          line: 400,
          column: 0
        },
        end: {
          line: 407,
          column: 3
        }
      },
      "114": {
        start: {
          line: 410,
          column: 4
        },
        end: {
          line: 420,
          column: 5
        }
      },
      "115": {
        start: {
          line: 411,
          column: 28
        },
        end: {
          line: 411,
          column: 46
        }
      },
      "116": {
        start: {
          line: 412,
          column: 8
        },
        end: {
          line: 412,
          column: 54
        }
      },
      "117": {
        start: {
          line: 415,
          column: 8
        },
        end: {
          line: 418,
          column: 9
        }
      },
      "118": {
        start: {
          line: 416,
          column: 31
        },
        end: {
          line: 416,
          column: 142
        }
      },
      "119": {
        start: {
          line: 416,
          column: 65
        },
        end: {
          line: 416,
          column: 128
        }
      },
      "120": {
        start: {
          line: 417,
          column: 12
        },
        end: {
          line: 417,
          column: 59
        }
      },
      "121": {
        start: {
          line: 419,
          column: 8
        },
        end: {
          line: 419,
          column: 64
        }
      },
      "122": {
        start: {
          line: 424,
          column: 4
        },
        end: {
          line: 434,
          column: 5
        }
      },
      "123": {
        start: {
          line: 425,
          column: 28
        },
        end: {
          line: 425,
          column: 46
        }
      },
      "124": {
        start: {
          line: 426,
          column: 8
        },
        end: {
          line: 426,
          column: 54
        }
      },
      "125": {
        start: {
          line: 429,
          column: 8
        },
        end: {
          line: 432,
          column: 9
        }
      },
      "126": {
        start: {
          line: 430,
          column: 31
        },
        end: {
          line: 430,
          column: 142
        }
      },
      "127": {
        start: {
          line: 430,
          column: 65
        },
        end: {
          line: 430,
          column: 128
        }
      },
      "128": {
        start: {
          line: 431,
          column: 12
        },
        end: {
          line: 431,
          column: 59
        }
      },
      "129": {
        start: {
          line: 433,
          column: 8
        },
        end: {
          line: 433,
          column: 64
        }
      },
      "130": {
        start: {
          line: 436,
          column: 0
        },
        end: {
          line: 440,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 8,
            column: 39
          },
          end: {
            line: 8,
            column: 40
          }
        },
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 213,
            column: 1
          }
        },
        line: 8
      },
      "1": {
        name: "SecurityValidator",
        decl: {
          start: {
            line: 9,
            column: 13
          },
          end: {
            line: 9,
            column: 30
          }
        },
        loc: {
          start: {
            line: 9,
            column: 33
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 9
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 14,
            column: 38
          },
          end: {
            line: 14,
            column: 39
          }
        },
        loc: {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 62,
            column: 5
          }
        },
        line: 14
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 21,
            column: 48
          },
          end: {
            line: 21,
            column: 49
          }
        },
        loc: {
          start: {
            line: 21,
            column: 67
          },
          end: {
            line: 23,
            column: 9
          }
        },
        line: 21
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 25,
            column: 44
          },
          end: {
            line: 25,
            column: 45
          }
        },
        loc: {
          start: {
            line: 25,
            column: 63
          },
          end: {
            line: 27,
            column: 9
          }
        },
        line: 25
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 30,
            column: 38
          },
          end: {
            line: 30,
            column: 39
          }
        },
        loc: {
          start: {
            line: 30,
            column: 57
          },
          end: {
            line: 32,
            column: 13
          }
        },
        line: 30
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 37,
            column: 44
          },
          end: {
            line: 37,
            column: 45
          }
        },
        loc: {
          start: {
            line: 37,
            column: 63
          },
          end: {
            line: 39,
            column: 9
          }
        },
        line: 37
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 41,
            column: 45
          },
          end: {
            line: 41,
            column: 46
          }
        },
        loc: {
          start: {
            line: 41,
            column: 64
          },
          end: {
            line: 43,
            column: 9
          }
        },
        line: 41
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 45,
            column: 45
          },
          end: {
            line: 45,
            column: 46
          }
        },
        loc: {
          start: {
            line: 45,
            column: 64
          },
          end: {
            line: 47,
            column: 9
          }
        },
        line: 45
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 66,
            column: 41
          },
          end: {
            line: 66,
            column: 42
          }
        },
        loc: {
          start: {
            line: 66,
            column: 67
          },
          end: {
            line: 101,
            column: 5
          }
        },
        line: 66
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 74,
            column: 49
          },
          end: {
            line: 74,
            column: 50
          }
        },
        loc: {
          start: {
            line: 74,
            column: 68
          },
          end: {
            line: 74,
            column: 99
          }
        },
        line: 74
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 78,
            column: 45
          },
          end: {
            line: 78,
            column: 46
          }
        },
        loc: {
          start: {
            line: 78,
            column: 64
          },
          end: {
            line: 78,
            column: 95
          }
        },
        line: 78
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 82,
            column: 35
          },
          end: {
            line: 82,
            column: 36
          }
        },
        loc: {
          start: {
            line: 82,
            column: 54
          },
          end: {
            line: 82,
            column: 85
          }
        },
        line: 82
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 86,
            column: 45
          },
          end: {
            line: 86,
            column: 46
          }
        },
        loc: {
          start: {
            line: 86,
            column: 64
          },
          end: {
            line: 86,
            column: 95
          }
        },
        line: 86
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 90,
            column: 46
          },
          end: {
            line: 90,
            column: 47
          }
        },
        loc: {
          start: {
            line: 90,
            column: 65
          },
          end: {
            line: 90,
            column: 96
          }
        },
        line: 90
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 94,
            column: 46
          },
          end: {
            line: 94,
            column: 47
          }
        },
        loc: {
          start: {
            line: 94,
            column: 65
          },
          end: {
            line: 94,
            column: 96
          }
        },
        line: 94
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 105,
            column: 37
          },
          end: {
            line: 105,
            column: 38
          }
        },
        loc: {
          start: {
            line: 105,
            column: 54
          },
          end: {
            line: 115,
            column: 5
          }
        },
        line: 105
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 119,
            column: 35
          },
          end: {
            line: 119,
            column: 36
          }
        },
        loc: {
          start: {
            line: 119,
            column: 53
          },
          end: {
            line: 127,
            column: 5
          }
        },
        line: 119
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 131,
            column: 38
          },
          end: {
            line: 131,
            column: 39
          }
        },
        loc: {
          start: {
            line: 131,
            column: 60
          },
          end: {
            line: 152,
            column: 5
          }
        },
        line: 131
      },
      "19": {
        name: "sanitizeString",
        decl: {
          start: {
            line: 216,
            column: 9
          },
          end: {
            line: 216,
            column: 23
          }
        },
        loc: {
          start: {
            line: 216,
            column: 31
          },
          end: {
            line: 218,
            column: 1
          }
        },
        line: 216
      },
      "20": {
        name: "sanitizeLongText",
        decl: {
          start: {
            line: 219,
            column: 9
          },
          end: {
            line: 219,
            column: 25
          }
        },
        loc: {
          start: {
            line: 219,
            column: 44
          },
          end: {
            line: 226,
            column: 1
          }
        },
        line: 219
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 352,
            column: 19
          },
          end: {
            line: 352,
            column: 20
          }
        },
        loc: {
          start: {
            line: 352,
            column: 34
          },
          end: {
            line: 352,
            column: 65
          }
        },
        line: 352
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 355,
            column: 19
          },
          end: {
            line: 355,
            column: 20
          }
        },
        loc: {
          start: {
            line: 355,
            column: 34
          },
          end: {
            line: 355,
            column: 73
          }
        },
        line: 355
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 360,
            column: 19
          },
          end: {
            line: 360,
            column: 20
          }
        },
        loc: {
          start: {
            line: 360,
            column: 34
          },
          end: {
            line: 360,
            column: 73
          }
        },
        line: 360
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 378,
            column: 19
          },
          end: {
            line: 378,
            column: 20
          }
        },
        loc: {
          start: {
            line: 378,
            column: 34
          },
          end: {
            line: 378,
            column: 73
          }
        },
        line: 378
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 396,
            column: 59
          },
          end: {
            line: 396,
            column: 60
          }
        },
        loc: {
          start: {
            line: 396,
            column: 74
          },
          end: {
            line: 396,
            column: 113
          }
        },
        line: 396
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 397,
            column: 60
          },
          end: {
            line: 397,
            column: 61
          }
        },
        loc: {
          start: {
            line: 397,
            column: 75
          },
          end: {
            line: 397,
            column: 115
          }
        },
        line: 397
      },
      "27": {
        name: "validateRequestBody",
        decl: {
          start: {
            line: 409,
            column: 9
          },
          end: {
            line: 409,
            column: 28
          }
        },
        loc: {
          start: {
            line: 409,
            column: 43
          },
          end: {
            line: 421,
            column: 1
          }
        },
        line: 409
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 416,
            column: 48
          },
          end: {
            line: 416,
            column: 49
          }
        },
        loc: {
          start: {
            line: 416,
            column: 63
          },
          end: {
            line: 416,
            column: 130
          }
        },
        line: 416
      },
      "29": {
        name: "validateInput",
        decl: {
          start: {
            line: 423,
            column: 9
          },
          end: {
            line: 423,
            column: 22
          }
        },
        loc: {
          start: {
            line: 423,
            column: 37
          },
          end: {
            line: 435,
            column: 1
          }
        },
        line: 423
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 430,
            column: 48
          },
          end: {
            line: 430,
            column: 49
          }
        },
        loc: {
          start: {
            line: 430,
            column: 63
          },
          end: {
            line: 430,
            column: 130
          }
        },
        line: 430
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 15,
            column: 8
          },
          end: {
            line: 15,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 15,
            column: 8
          },
          end: {
            line: 15,
            column: 49
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 15
      },
      "1": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 18,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 18,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "2": {
        loc: {
          start: {
            line: 29,
            column: 8
          },
          end: {
            line: 35,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 8
          },
          end: {
            line: 35,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "3": {
        loc: {
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 53,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 53,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 51
      },
      "4": {
        loc: {
          start: {
            line: 57,
            column: 24
          },
          end: {
            line: 57,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 57,
            column: 24
          },
          end: {
            line: 57,
            column: 41
          }
        }, {
          start: {
            line: 57,
            column: 45
          },
          end: {
            line: 57,
            column: 50
          }
        }],
        line: 57
      },
      "5": {
        loc: {
          start: {
            line: 58,
            column: 8
          },
          end: {
            line: 60,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 58,
            column: 8
          },
          end: {
            line: 60,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 58
      },
      "6": {
        loc: {
          start: {
            line: 67,
            column: 8
          },
          end: {
            line: 67,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 67,
            column: 8
          },
          end: {
            line: 67,
            column: 49
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 67
      },
      "7": {
        loc: {
          start: {
            line: 70,
            column: 8
          },
          end: {
            line: 72,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 70,
            column: 8
          },
          end: {
            line: 72,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 70
      },
      "8": {
        loc: {
          start: {
            line: 70,
            column: 12
          },
          end: {
            line: 70,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 70,
            column: 12
          },
          end: {
            line: 70,
            column: 37
          }
        }, {
          start: {
            line: 70,
            column: 41
          },
          end: {
            line: 70,
            column: 65
          }
        }],
        line: 70
      },
      "9": {
        loc: {
          start: {
            line: 74,
            column: 8
          },
          end: {
            line: 76,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 8
          },
          end: {
            line: 76,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "10": {
        loc: {
          start: {
            line: 78,
            column: 8
          },
          end: {
            line: 80,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 78,
            column: 8
          },
          end: {
            line: 80,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 78
      },
      "11": {
        loc: {
          start: {
            line: 82,
            column: 8
          },
          end: {
            line: 84,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 82,
            column: 8
          },
          end: {
            line: 84,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 82
      },
      "12": {
        loc: {
          start: {
            line: 86,
            column: 8
          },
          end: {
            line: 88,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 86,
            column: 8
          },
          end: {
            line: 88,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 86
      },
      "13": {
        loc: {
          start: {
            line: 90,
            column: 8
          },
          end: {
            line: 92,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 90,
            column: 8
          },
          end: {
            line: 92,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 90
      },
      "14": {
        loc: {
          start: {
            line: 94,
            column: 8
          },
          end: {
            line: 96,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 94,
            column: 8
          },
          end: {
            line: 96,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 94
      },
      "15": {
        loc: {
          start: {
            line: 108,
            column: 8
          },
          end: {
            line: 109,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 108,
            column: 8
          },
          end: {
            line: 109,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 108
      },
      "16": {
        loc: {
          start: {
            line: 112,
            column: 8
          },
          end: {
            line: 113,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 112,
            column: 8
          },
          end: {
            line: 113,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 112
      },
      "17": {
        loc: {
          start: {
            line: 112,
            column: 12
          },
          end: {
            line: 112,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 112,
            column: 12
          },
          end: {
            line: 112,
            column: 36
          }
        }, {
          start: {
            line: 112,
            column: 40
          },
          end: {
            line: 112,
            column: 59
          }
        }],
        line: 112
      },
      "18": {
        loc: {
          start: {
            line: 137,
            column: 12
          },
          end: {
            line: 142,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 137,
            column: 12
          },
          end: {
            line: 142,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 137
      },
      "19": {
        loc: {
          start: {
            line: 149,
            column: 23
          },
          end: {
            line: 149,
            column: 78
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 149,
            column: 48
          },
          end: {
            line: 149,
            column: 61
          }
        }, {
          start: {
            line: 149,
            column: 64
          },
          end: {
            line: 149,
            column: 78
          }
        }],
        line: 149
      },
      "20": {
        loc: {
          start: {
            line: 220,
            column: 4
          },
          end: {
            line: 220,
            column: 51
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 220,
            column: 4
          },
          end: {
            line: 220,
            column: 51
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 220
      },
      "21": {
        loc: {
          start: {
            line: 396,
            column: 83
          },
          end: {
            line: 396,
            column: 110
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 396,
            column: 89
          },
          end: {
            line: 396,
            column: 106
          }
        }, {
          start: {
            line: 396,
            column: 109
          },
          end: {
            line: 396,
            column: 110
          }
        }],
        line: 396
      },
      "22": {
        loc: {
          start: {
            line: 397,
            column: 84
          },
          end: {
            line: 397,
            column: 112
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 397,
            column: 90
          },
          end: {
            line: 397,
            column: 107
          }
        }, {
          start: {
            line: 397,
            column: 110
          },
          end: {
            line: 397,
            column: 112
          }
        }],
        line: 397
      },
      "23": {
        loc: {
          start: {
            line: 415,
            column: 8
          },
          end: {
            line: 418,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 415,
            column: 8
          },
          end: {
            line: 418,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 415
      },
      "24": {
        loc: {
          start: {
            line: 429,
            column: 8
          },
          end: {
            line: 432,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 429,
            column: 8
          },
          end: {
            line: 432,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 429
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/validation.ts",
      mappings: ";;;AA4cA,kDAWC;AAGD,sCAWC;AAreD,2BAAwB;AAExB,uDAAuD;AACvD;IAAA;IA4OA,CAAC;IA1KC;;OAEG;IACI,+BAAa,GAApB,UAAqB,KAAa,EAAE,OAI9B;QAJ8B,wBAAA,EAAA,YAI9B;QACJ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,SAAS,GAAG,KAAK,CAAC;QAEtB,uCAAuC;QACvC,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,UAAA,OAAO;YAC7C,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,0CAA0C;QAC1C,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,UAAA,OAAO;YACzC,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,yBAAyB;QACzB,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YACvB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAA,OAAO;gBAC/B,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEH,2CAA2C;YAC3C,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;QAChD,CAAC;QAED,mCAAmC;QACnC,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,UAAA,OAAO;YACzC,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,oCAAoC;QACpC,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,UAAA,OAAO;YAC1C,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,oCAAoC;QACpC,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,UAAA,OAAO;YAC1C,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,8CAA8C;QAC9C,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,mCAAmC,EAAE,EAAE,CAAC,CAAC;QAEvE,qBAAqB;QACrB,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC9B,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QAChD,CAAC;QAED,0BAA0B;QAC1B,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QAElD,mBAAmB;QACnB,IAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,KAAK,CAAC;QAC7C,IAAI,SAAS,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;YACjC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,kCAAgB,GAAvB,UAAwB,KAAa,EAAE,OAA6C;QAA7C,wBAAA,EAAA,YAA6C;QAClF,IAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,uDAAuD;QACvD,IAAI,OAAO,CAAC,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;QACxC,CAAC;QAED,8BAA8B;QAC9B,IAAI,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAnB,CAAmB,CAAC,EAAE,CAAC;YACzE,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACpC,CAAC;QAED,kCAAkC;QAClC,IAAI,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAnB,CAAmB,CAAC,EAAE,CAAC;YACrE,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACvC,CAAC;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAnB,CAAmB,CAAC,EAAE,CAAC;YAC3D,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC7C,CAAC;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAnB,CAAmB,CAAC,EAAE,CAAC;YACrE,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAChC,CAAC;QAED,2BAA2B;QAC3B,IAAI,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAnB,CAAmB,CAAC,EAAE,CAAC;YACtE,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACjC,CAAC;QAED,2BAA2B;QAC3B,IAAI,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAnB,CAAmB,CAAC,EAAE,CAAC;YACtE,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACjC,CAAC;QAED,OAAO;YACL,OAAO,EAAE,OAAO,CAAC,MAAM,KAAK,CAAC;YAC7B,OAAO,SAAA;SACR,CAAC;IACJ,CAAC;IAED;;OAEG;IACY,8BAAY,GAA3B,UAA4B,KAAa;QACvC,gBAAgB;QAChB,IAAM,YAAY,GAAG,kDAAkD,CAAC;QACxE,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAE1C,6CAA6C;QAC7C,IAAM,YAAY,GAAG,oDAAoD,CAAC;QAC1E,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI;YAAE,OAAO,IAAI,CAAC;QAEjE,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACI,4BAAU,GAAjB,UAAkB,MAAc;QAC9B,OAAO,MAAM;aACV,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;aACtB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;aACvB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;aACvB,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,+BAAa,GAApB,UAAqB,UAAkB;QACrC,IAAI,CAAC;YACH,iCAAiC;YACjC,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC;YAExE,iDAAiD;YACjD,IAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YACpD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACxB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,qCAA8B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE;iBACrE,CAAC;YACJ,CAAC;YAED,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACrC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc;aAC/D,CAAC;QACJ,CAAC;IACH,CAAC;IA1OD,gEAAgE;IACxC,4CAA0B,GAAG;QACnD,mBAAmB,EAAY,4CAA4C;QAC3E,WAAW,EAAoB,gBAAgB;QAC/C,WAAW,EAAoB,mBAAmB;QAClD,UAAU,EAAqB,qBAAqB;QACpD,UAAU,EAAqB,uBAAuB;QACtD,cAAc,EAAiB,uBAAuB;QACtD,cAAc,EAAiB,qBAAqB;QACpD,YAAY,EAAmB,mBAAmB;QAClD,YAAY,EAAmB,aAAa;QAC5C,aAAa,EAAkB,uBAAuB;QACtD,mDAAmD,EAAE,qBAAqB;KAC3E,CAAC;IAEF,gCAAgC;IACR,wCAAsB,GAAG;QAC/C,aAAa,EAAkB,4BAA4B;QAC3D,kBAAkB,EAAa,+BAA+B;QAC9D,yBAAyB,EAAM,+BAA+B;QAC9D,KAAK,EAA0B,qCAAqC;QACpE,eAAe,EAAgB,kCAAkC;KAClE,CAAC;IAEF,0BAA0B;IACF,8BAAY,GAAG;QACrC,qDAAqD;QACrD,qDAAqD;QACrD,qDAAqD;QACrD,kDAAkD;QAClD,iBAAiB;QACjB,iBAAiB;QACjB,eAAe;QACf,aAAa;QACb,mBAAmB;QACnB,aAAa,EAAkB,iBAAiB;KACjD,CAAC;IAEF,yBAAyB;IACD,wCAAsB,GAAG;QAC/C,mCAAmC;QACnC,wEAAwE;QACxE,8BAA8B;QAC9B,kDAAkD;KACnD,CAAC;IAEF,0BAA0B;IACF,yCAAuB,GAAG;QAChD,aAAa,EAAkB,sBAAsB;QACrD,mBAAmB,EAAY,0BAA0B;QACzD,iBAAiB,EAAc,2BAA2B;QAC1D,sBAAsB,EAAS,6BAA6B;QAC5D,yBAAyB,EAAM,oBAAoB;QACnD,+BAA+B,EAAE,uBAAuB;QACxD,eAAe,EAAgB,oCAAoC;KACpE,CAAC;IAEF,0BAA0B;IACF,yCAAuB,GAAG;QAChD,WAAW,EAAoB,0BAA0B;QACzD,WAAW,EAAoB,oBAAoB;QACnD,WAAW,EAAoB,qBAAqB;QACpD,WAAW,EAAoB,oBAAoB;KACpD,CAAC;IA4KJ,wBAAC;CAAA,AA5OD,IA4OC;AA5OY,8CAAiB;AA8O9B,uCAAuC;AACvC,SAAS,cAAc,CAAC,KAAa;IACnC,OAAO,iBAAiB,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;AACpE,CAAC;AAED,SAAS,gBAAgB,CAAC,KAAa,EAAE,SAAwB;IAAxB,0BAAA,EAAA,gBAAwB;IAC/D,OAAO,iBAAiB,CAAC,aAAa,CAAC,KAAK,EAAE;QAC5C,SAAS,WAAA;QACT,gBAAgB,EAAE,IAAI;QACtB,SAAS,EAAE,KAAK;KACjB,CAAC,CAAC;AACL,CAAC;AAED,iDAAiD;AACpC,QAAA,YAAY,GAAG,OAAC,CAAC,MAAM,CAAC;IACnC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;SACd,SAAS,CAAC,cAAc,CAAC;SACzB,IAAI,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;IAC7E,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;SACjB,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC;SAChD,GAAG,CAAC,GAAG,EAAE,mBAAmB,CAAC;SAC7B,KAAK,CAAC,iEAAiE,EACtE,kHAAkH,CAAC;CACxH,CAAC,CAAC;AAEU,QAAA,WAAW,GAAG,OAAC,CAAC,MAAM,CAAC;IAClC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;SACd,SAAS,CAAC,cAAc,CAAC;SACzB,IAAI,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;IAC7E,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;SACjB,GAAG,CAAC,CAAC,EAAE,sBAAsB,CAAC;SAC9B,GAAG,CAAC,GAAG,EAAE,mBAAmB,CAAC;CACjC,CAAC,CAAC;AAEU,QAAA,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC3C,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;SACd,SAAS,CAAC,cAAc,CAAC;SACzB,IAAI,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;CAC9E,CAAC,CAAC;AAEU,QAAA,WAAW,GAAG,OAAC,CAAC,MAAM,CAAC;IAClC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;SACd,SAAS,CAAC,cAAc,CAAC;SACzB,IAAI,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;CAC9E,CAAC,CAAC;AAEU,QAAA,mBAAmB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC1C,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;SACd,SAAS,CAAC,cAAc,CAAC;SACzB,IAAI,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;IAC1E,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;SACjB,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC;SAChD,GAAG,CAAC,GAAG,EAAE,mBAAmB,CAAC;SAC7B,KAAK,CAAC,iEAAiE,EACtE,kHAAkH,CAAC;CACxH,CAAC,CAAC;AAEH,6BAA6B;AAChB,QAAA,mBAAmB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC1C,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,sCAAsC,CAAC,CAAC,QAAQ,EAAE;IAC3E,iBAAiB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,OAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC7E,gBAAgB,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,EAAE;IAEpE,uBAAuB;IACvB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,4CAA4C,CAAC,CAAC,QAAQ,EAAE;IACtF,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,2CAA2C,CAAC,CAAC,QAAQ,EAAE;IACpF,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,wBAAwB,EAAE,6BAA6B,CAAC,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,OAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IACnH,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,2CAA2C,CAAC,CAAC,QAAQ,EAAE;IACrF,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,OAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAE3E,2BAA2B;IAC3B,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,4CAA4C,CAAC,CAAC,QAAQ,EAAE;IACtF,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,+CAA+C,CAAC,CAAC,QAAQ,EAAE;IACxF,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,2CAA2C,CAAC,CAAC,QAAQ,EAAE;IAC5F,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,kDAAkD,CAAC,CAAC,QAAQ,EAAE;IAClG,eAAe,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE;IAEtF,qBAAqB;IACrB,eAAe,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,qCAAqC,CAAC,CAAC,QAAQ,EAAE;IACtG,aAAa,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,2BAA2B,CAAC,CAAC,QAAQ,EAAE;IAC1F,kBAAkB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,qCAAqC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,qCAAqC,CAAC,CAAC,QAAQ,EAAE;IAEvI,wBAAwB;IACxB,iBAAiB,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,gBAAgB,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC7E,kBAAkB,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;IAC1C,aAAa,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;IACrC,SAAS,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;IACjC,SAAS,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;CAClC,CAAC,CAAC;AAEH,gCAAgC;AACnB,QAAA,wBAAwB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC/C,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC;IAC1D,WAAW,EAAE,OAAC,CAAC,KAAK,CAAC;QACnB,OAAC,CAAC,MAAM,EAAE;QACV,OAAC,CAAC,MAAM,EAAE;QACV,OAAC,CAAC,OAAO,EAAE;QACX,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC;QACnB,OAAC,CAAC,IAAI,EAAE;KACT,CAAC;CACH,CAAC,CAAC;AAEU,QAAA,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC3C,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,mCAAmC,CAAC;IACnE,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,KAAK,CAAC;QACzB,OAAC,CAAC,MAAM,EAAE;QACV,OAAC,CAAC,MAAM,EAAE;QACV,OAAC,CAAC,OAAO,EAAE;QACX,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC;QACnB,OAAC,CAAC,IAAI,EAAE;KACT,CAAC,CAAC;IACH,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,EAAE;CACxD,CAAC,CAAC;AAEH,kCAAkC;AACrB,QAAA,iBAAiB,GAAG,OAAC,CAAC,MAAM,CAAC;IACxC,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mCAAmC,CAAC;IACzE,cAAc,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,4CAA4C,CAAC;IACvF,aAAa,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,iCAAiC,CAAC;IACrE,oBAAoB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,oCAAoC,CAAC,CAAC,QAAQ,EAAE;CACzF,CAAC,CAAC;AAEH,uCAAuC;AAC1B,QAAA,sBAAsB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC7C,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,wCAAwC,CAAC;IAChG,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,yBAAyB,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,+CAA+C,CAAC;IACpH,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,aAAa,CAAC;IAClC,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,eAAe,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;IACxG,QAAQ,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,cAAc,EAAE,YAAY,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,yBAAyB,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,kBAAkB,CAAC,CAAC;IAClQ,UAAU,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;IACtE,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,8CAA8C,CAAC,CAAC,QAAQ,EAAE;IACtF,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,0CAA0C,CAAC,CAAC,QAAQ,EAAE;IACnF,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;IAC1E,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,gBAAgB,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;CAC3F,CAAC,CAAC;AAEH,qCAAqC;AACxB,QAAA,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC3C,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC;IAClD,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,2BAA2B,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC;IAC/F,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,0CAA0C,CAAC,CAAC,QAAQ,EAAE;IACnF,SAAS,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;CAClC,CAAC,CAAC;AAEH,uCAAuC;AAC1B,QAAA,sBAAsB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC7C,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC;IAClD,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IACzE,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,yCAAyC,CAAC,CAAC,QAAQ,EAAE;IACjF,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,2BAA2B,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC,CAAC,QAAQ,EAAE;IAC1G,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,0CAA0C,CAAC,CAAC,QAAQ,EAAE;CACpF,CAAC,CAAC;AAEH,kDAAkD;AACrC,QAAA,eAAe,GAAG,OAAC,CAAC,MAAM,CAAC;IACtC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;SACd,SAAS,CAAC,UAAC,GAAG,IAAK,OAAA,cAAc,CAAC,GAAG,CAAC,EAAnB,CAAmB,CAAC;SACvC,IAAI,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,wCAAwC,CAAC,CAAC;IAClG,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;SAChB,SAAS,CAAC,UAAC,GAAG,IAAK,OAAA,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,EAA3B,CAA2B,CAAC;SAC/C,IAAI,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,qBAAqB,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,2CAA2C,CAAC,CAAC;CACzG,CAAC,CAAC;AAEU,QAAA,gBAAgB,GAAG,OAAC,CAAC,MAAM,CAAC;IACvC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;SAChB,SAAS,CAAC,UAAC,GAAG,IAAK,OAAA,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,EAA3B,CAA2B,CAAC;SAC/C,IAAI,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,qBAAqB,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,2CAA2C,CAAC,CAAC;IACxG,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE;SACf,SAAS,CAAC,cAAc,CAAC;SACzB,IAAI,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;CAC5C,CAAC,CAAC;AAEH,wDAAwD;AAC3C,QAAA,iBAAiB,GAAG,OAAC,CAAC,MAAM,CAAC;IACxC,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;SACb,SAAS,CAAC,cAAc,CAAC;SACzB,IAAI,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,uCAAuC,CAAC,CAAC;IAChG,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;SACd,SAAS,CAAC,cAAc,CAAC;SACzB,IAAI,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;IAC7E,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;SAChB,SAAS,CAAC,cAAc,CAAC;SACzB,IAAI,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,qBAAqB,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,0CAA0C,CAAC,CAAC;IACtG,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;SAChB,SAAS,CAAC,UAAC,GAAG,IAAK,OAAA,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,EAA3B,CAA2B,CAAC;SAC/C,IAAI,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,qBAAqB,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,2CAA2C,CAAC,CAAC;CACzG,CAAC,CAAC;AAEH,iCAAiC;AACpB,QAAA,gBAAgB,GAAG,OAAC,CAAC,MAAM,CAAC;IACvC,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,uCAAuC,CAAC;IAC7F,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,uCAAuC,CAAC;IAC7F,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,sBAAsB,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,4CAA4C,CAAC;IAC3G,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,mBAAmB,CAAC;IAC5C,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,mBAAmB,CAAC;IAC5C,eAAe,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,CAAC;QAChC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC;QAClD,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,8BAA8B,CAAC;KAC/D,CAAC,CAAC;IACH,QAAQ,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;CACpC,CAAC,CAAC;AAEH,+BAA+B;AAClB,QAAA,gBAAgB,GAAG,OAAC,CAAC,MAAM,CAAC;IACvC,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,UAAC,GAAG,IAAK,OAAA,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAA3B,CAA2B,CAAC;IACtF,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,UAAC,GAAG,IAAK,OAAA,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAA5B,CAA4B,CAAC;CACzF,CAAC,CAAC;AAEH,oCAAoC;AACvB,QAAA,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC3C,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;IAC1C,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;IACtC,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;IAC5C,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;IACtC,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;IACxC,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;CACzC,CAAC,CAAC;AAEH,4CAA4C;AAC5C,SAAgB,mBAAmB,CAAI,MAAsB,EAAE,IAAa;IAC1E,IAAI,CAAC;QACH,IAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;IAChD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,OAAC,CAAC,QAAQ,EAAE,CAAC;YAChC,IAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,UAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,eAAK,GAAG,CAAC,OAAO,CAAE,EAAvC,CAAuC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjG,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;QACjD,CAAC;QACD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC;IAC1D,CAAC;AACH,CAAC;AAED,qCAAqC;AACrC,SAAgB,aAAa,CAAI,MAAsB,EAAE,IAAa;IACpE,IAAI,CAAC;QACH,IAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;IAChD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,OAAC,CAAC,QAAQ,EAAE,CAAC;YAChC,IAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,UAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,eAAK,GAAG,CAAC,OAAO,CAAE,EAAvC,CAAuC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjG,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;QACjD,CAAC;QACD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC;IAC1D,CAAC;AACH,CAAC;AAWY,QAAA,gBAAgB,GAAG;IAC9B,IAAI,EAAE,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,CAAC,EAAE,OAAO,EAAE,kCAAkC,EAAE,EAAE,4BAA4B;IAC7H,GAAG,EAAE,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,OAAO,EAAE,uBAAuB,EAAE,EAAE,8BAA8B;IACrH,OAAO,EAAE,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,CAAC,EAAE,OAAO,EAAE,mCAAmC,EAAE,EAAE,yBAAyB;CACtH,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/validation.ts"],
      sourcesContent: ["import { z } from 'zod';\n\n// Security: Advanced Input Sanitization and Validation\nexport class SecurityValidator {\n  // Command injection patterns (refined to avoid false positives)\n  private static readonly COMMAND_INJECTION_PATTERNS = [\n    /[;&|`$](?=\\s*\\w)/g,           // Shell metacharacters followed by commands\n    /\\|\\s*\\w+/g,                   // Pipe commands\n    /&&\\s*\\w+/g,                   // Command chaining\n    /;\\s*\\w+/g,                    // Command separation\n    /`[^`]*`/g,                    // Command substitution\n    /\\$\\([^)]*\\)/g,                // Command substitution\n    /\\$\\{[^}]*\\}/g,                // Variable expansion\n    />\\s*\\/\\w+/g,                  // File redirection\n    /<\\s*\\/\\w+/g,                  // File input\n    /\\|\\|\\s*\\w+/g,                 // OR command execution\n    /\\b(rm|del|format|fdisk|kill|shutdown|reboot)\\s+/gi, // Dangerous commands\n  ];\n\n  // Format string attack patterns\n  private static readonly FORMAT_STRING_PATTERNS = [\n    /%[sdxXocp]/g,                 // C-style format specifiers\n    /%\\d+\\$[sdxXocp]/g,            // Positional format specifiers\n    /%[0-9]*[hlL]?[sdxXocp]/g,     // Format with length modifiers\n    /%n/g,                         // Write format specifier (dangerous)\n    /%\\*[sdxXocp]/g,               // Dynamic width format specifiers\n  ];\n\n  // XSS patterns (enhanced)\n  private static readonly XSS_PATTERNS = [\n    /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\n    /<iframe\\b[^<]*(?:(?!<\\/iframe>)<[^<]*)*<\\/iframe>/gi,\n    /<object\\b[^<]*(?:(?!<\\/object>)<[^<]*)*<\\/object>/gi,\n    /<embed\\b[^<]*(?:(?!<\\/embed>)<[^<]*)*<\\/embed>/gi,\n    /<link\\b[^>]*>/gi,\n    /<meta\\b[^>]*>/gi,\n    /javascript:/gi,\n    /vbscript:/gi,\n    /data:text\\/html/gi,\n    /on\\w+\\s*=/gi,                 // Event handlers\n  ];\n\n  // SQL injection patterns\n  private static readonly SQL_INJECTION_PATTERNS = [\n    /('|(\\\\'))+.*(;|--|\\||\\/\\*|\\*\\/)/gi,\n    /(union|select|insert|update|delete|drop|create|alter|exec|execute)\\s/gi,\n    /\\b(or|and)\\s+\\d+\\s*=\\s*\\d+/gi,\n    /\\b(or|and)\\s+['\"]?\\w+['\"]?\\s*=\\s*['\"]?\\w+['\"]?/gi,\n  ];\n\n  // Path traversal patterns\n  private static readonly PATH_TRAVERSAL_PATTERNS = [\n    /\\.\\.[\\/\\\\]/g,                 // Directory traversal\n    /[\\/\\\\]\\.\\.[\\/\\\\]/g,           // Absolute path traversal\n    /\\.\\.[\\/\\\\]\\.\\./g,             // Multiple level traversal\n    /\\.\\.\\/|\\.\\.\\\\|\\.\\.$/g,        // Various traversal patterns\n    /[\\/\\\\]etc[\\/\\\\]passwd/gi,     // Unix system files\n    /[\\/\\\\]windows[\\/\\\\]system32/gi, // Windows system files\n    /\\.\\..*[\\/\\\\]/g,               // Any .. followed by path separator\n  ];\n\n  // LDAP injection patterns\n  private static readonly LDAP_INJECTION_PATTERNS = [\n    /\\*\\)\\(\\&/g,                   // LDAP wildcard injection\n    /\\*\\)\\(\\|/g,                   // LDAP OR injection\n    /\\)\\(\\&\\(/g,                   // LDAP AND injection\n    /\\)\\(\\|\\(/g,                   // LDAP OR injection\n  ];\n\n  /**\n   * Comprehensive input sanitization that handles all major attack vectors\n   */\n  static sanitizeInput(input: string, options: {\n    allowHtml?: boolean;\n    maxLength?: number;\n    preserveNewlines?: boolean;\n  } = {}): string {\n    if (typeof input !== 'string') {\n      return '';\n    }\n\n    let sanitized = input;\n\n    // 1. Remove command injection patterns\n    this.COMMAND_INJECTION_PATTERNS.forEach(pattern => {\n      sanitized = sanitized.replace(pattern, '');\n    });\n\n    // 2. Remove format string attack patterns\n    this.FORMAT_STRING_PATTERNS.forEach(pattern => {\n      sanitized = sanitized.replace(pattern, '');\n    });\n\n    // 3. Remove XSS patterns\n    if (!options.allowHtml) {\n      this.XSS_PATTERNS.forEach(pattern => {\n        sanitized = sanitized.replace(pattern, '');\n      });\n\n      // Remove all HTML tags if HTML not allowed\n      sanitized = sanitized.replace(/<[^>]*>/g, '');\n    }\n\n    // 4. Remove SQL injection patterns\n    this.SQL_INJECTION_PATTERNS.forEach(pattern => {\n      sanitized = sanitized.replace(pattern, '');\n    });\n\n    // 5. Remove path traversal patterns\n    this.PATH_TRAVERSAL_PATTERNS.forEach(pattern => {\n      sanitized = sanitized.replace(pattern, '');\n    });\n\n    // 6. Remove LDAP injection patterns\n    this.LDAP_INJECTION_PATTERNS.forEach(pattern => {\n      sanitized = sanitized.replace(pattern, '');\n    });\n\n    // 7. Remove null bytes and control characters\n    sanitized = sanitized.replace(/[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]/g, '');\n\n    // 8. Handle newlines\n    if (!options.preserveNewlines) {\n      sanitized = sanitized.replace(/[\\r\\n]/g, ' ');\n    }\n\n    // 9. Normalize whitespace\n    sanitized = sanitized.replace(/\\s+/g, ' ').trim();\n\n    // 10. Limit length\n    const maxLength = options.maxLength || 10000;\n    if (sanitized.length > maxLength) {\n      sanitized = sanitized.slice(0, maxLength);\n    }\n\n    return sanitized;\n  }\n\n  /**\n   * Validate that input doesn't contain malicious patterns\n   */\n  static validateSecurity(input: string, options: { isTestEnvironment?: boolean } = {}): { isValid: boolean; threats: string[] } {\n    const threats: string[] = [];\n\n    // Skip validation in test environment for basic inputs\n    if (options.isTestEnvironment && this.isBasicInput(input)) {\n      return { isValid: true, threats: [] };\n    }\n\n    // Check for command injection\n    if (this.COMMAND_INJECTION_PATTERNS.some(pattern => pattern.test(input))) {\n      threats.push('Command Injection');\n    }\n\n    // Check for format string attacks\n    if (this.FORMAT_STRING_PATTERNS.some(pattern => pattern.test(input))) {\n      threats.push('Format String Attack');\n    }\n\n    // Check for XSS\n    if (this.XSS_PATTERNS.some(pattern => pattern.test(input))) {\n      threats.push('Cross-Site Scripting (XSS)');\n    }\n\n    // Check for SQL injection\n    if (this.SQL_INJECTION_PATTERNS.some(pattern => pattern.test(input))) {\n      threats.push('SQL Injection');\n    }\n\n    // Check for path traversal\n    if (this.PATH_TRAVERSAL_PATTERNS.some(pattern => pattern.test(input))) {\n      threats.push('Path Traversal');\n    }\n\n    // Check for LDAP injection\n    if (this.LDAP_INJECTION_PATTERNS.some(pattern => pattern.test(input))) {\n      threats.push('LDAP Injection');\n    }\n\n    return {\n      isValid: threats.length === 0,\n      threats\n    };\n  }\n\n  /**\n   * Check if input is basic/safe (email, simple text, etc.)\n   */\n  private static isBasicInput(input: string): boolean {\n    // Email pattern\n    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n    if (emailPattern.test(input)) return true;\n\n    // Simple alphanumeric with basic punctuation\n    const basicPattern = /^[a-zA-Z0-9\\s.,!?@#$%^&*()_+-=\\[\\]{}|;':\"<>?/~`]+$/;\n    if (basicPattern.test(input) && input.length < 1000) return true;\n\n    return false;\n  }\n\n  /**\n   * Enhanced HTML escaping\n   */\n  static escapeHtml(unsafe: string): string {\n    return unsafe\n      .replace(/&/g, \"&amp;\")\n      .replace(/</g, \"&lt;\")\n      .replace(/>/g, \"&gt;\")\n      .replace(/\"/g, \"&quot;\")\n      .replace(/'/g, \"&#039;\")\n      .replace(/\\//g, \"&#x2F;\");\n  }\n\n  /**\n   * Safe JSON parsing with validation\n   */\n  static safeJsonParse(jsonString: string): { success: boolean; data?: any; error?: string } {\n    try {\n      // First sanitize the JSON string\n      const sanitized = this.sanitizeInput(jsonString, { maxLength: 100000 });\n\n      // Validate it doesn't contain malicious patterns\n      const validation = this.validateSecurity(sanitized);\n      if (!validation.isValid) {\n        return {\n          success: false,\n          error: `Security threats detected: ${validation.threats.join(', ')}`\n        };\n      }\n\n      const parsed = JSON.parse(sanitized);\n      return { success: true, data: parsed };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Invalid JSON'\n      };\n    }\n  }\n}\n\n// Enhanced input sanitization function\nfunction sanitizeString(value: string): string {\n  return SecurityValidator.sanitizeInput(value, { maxLength: 254 });\n}\n\nfunction sanitizeLongText(value: string, maxLength: number = 5000): string {\n  return SecurityValidator.sanitizeInput(value, {\n    maxLength,\n    preserveNewlines: true,\n    allowHtml: false\n  });\n}\n\n// User validation schemas with enhanced security\nexport const signupSchema = z.object({\n  email: z.string()\n    .transform(sanitizeString)\n    .pipe(z.string().email('Invalid email address').max(254, 'Email too long')),\n  password: z.string()\n    .min(8, 'Password must be at least 8 characters')\n    .max(128, 'Password too long')\n    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/,\n      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),\n});\n\nexport const loginSchema = z.object({\n  email: z.string()\n    .transform(sanitizeString)\n    .pipe(z.string().email('Invalid email address').max(254, 'Email too long')),\n  password: z.string()\n    .min(1, 'Password is required')\n    .max(128, 'Password too long'),\n});\n\nexport const forgotPasswordSchema = z.object({\n  email: z.string()\n    .transform(sanitizeString)\n    .pipe(z.string().email('Invalid email address').max(254, 'Email too long')),\n});\n\nexport const emailSchema = z.object({\n  email: z.string()\n    .transform(sanitizeString)\n    .pipe(z.string().email('Invalid email address').max(254, 'Email too long')),\n});\n\nexport const resetPasswordSchema = z.object({\n  token: z.string()\n    .transform(sanitizeString)\n    .pipe(z.string().min(1, 'Token is required').max(256, 'Token too long')),\n  password: z.string()\n    .min(8, 'Password must be at least 8 characters')\n    .max(128, 'Password too long')\n    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/,\n      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),\n});\n\n// Profile validation schemas\nexport const profileUpdateSchema = z.object({\n  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),\n  profilePictureUrl: z.string().url('Invalid URL').optional().or(z.literal('')),\n  socialMediaLinks: z.record(z.string().url('Invalid URL')).optional(),\n\n  // Personal Information\n  firstName: z.string().max(50, 'First name must be less than 50 characters').optional(),\n  lastName: z.string().max(50, 'Last name must be less than 50 characters').optional(),\n  phoneNumber: z.string().regex(/^[\\+]?[1-9][\\d]{0,15}$/, 'Invalid phone number format').optional().or(z.literal('')),\n  location: z.string().max(100, 'Location must be less than 100 characters').optional(),\n  website: z.string().url('Invalid website URL').optional().or(z.literal('')),\n\n  // Professional Information\n  jobTitle: z.string().max(100, 'Job title must be less than 100 characters').optional(),\n  company: z.string().max(100, 'Company name must be less than 100 characters').optional(),\n  currentIndustry: z.string().max(100, 'Industry must be less than 100 characters').optional(),\n  targetIndustry: z.string().max(100, 'Target industry must be less than 100 characters').optional(),\n  experienceLevel: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),\n\n  // Career Development\n  careerInterests: z.array(z.string().max(50)).max(10, 'Maximum 10 career interests allowed').optional(),\n  skillsToLearn: z.array(z.string().max(50)).max(20, 'Maximum 20 skills allowed').optional(),\n  weeklyLearningGoal: z.number().min(1, 'Weekly goal must be at least 1 hour').max(168, 'Weekly goal cannot exceed 168 hours').optional(),\n\n  // Privacy & Preferences\n  profileVisibility: z.enum(['PRIVATE', 'PUBLIC', 'COMMUNITY_ONLY']).optional(),\n  emailNotifications: z.boolean().optional(),\n  profilePublic: z.boolean().optional(),\n  showEmail: z.boolean().optional(),\n  showPhone: z.boolean().optional(),\n});\n\n// Assessment validation schemas\nexport const assessmentResponseSchema = z.object({\n  questionKey: z.string().min(1, 'Question key is required'),\n  answerValue: z.union([\n    z.string(),\n    z.number(),\n    z.boolean(),\n    z.array(z.string()),\n    z.null()\n  ]),\n});\n\nexport const assessmentSaveSchema = z.object({\n  currentStep: z.number().min(0, 'Current step must be non-negative'),\n  formData: z.record(z.union([\n    z.string(),\n    z.number(),\n    z.boolean(),\n    z.array(z.string()),\n    z.null()\n  ])),\n  status: z.enum(['IN_PROGRESS', 'COMPLETED']).optional(),\n});\n\n// Freedom Fund validation schemas\nexport const freedomFundSchema = z.object({\n  monthlyExpenses: z.number().positive('Monthly expenses must be positive'),\n  coverageMonths: z.number().int().positive('Coverage months must be a positive integer'),\n  targetSavings: z.number().positive('Target savings must be positive'),\n  currentSavingsAmount: z.number().min(0, 'Current savings cannot be negative').optional(),\n});\n\n// Learning Resource validation schemas\nexport const learningResourceSchema = z.object({\n  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),\n  description: z.string().min(1, 'Description is required').max(1000, 'Description must be less than 1000 characters'),\n  url: z.string().url('Invalid URL'),\n  type: z.enum(['COURSE', 'ARTICLE', 'VIDEO', 'PODCAST', 'BOOK', 'CERTIFICATION', 'TUTORIAL', 'WORKSHOP']),\n  category: z.enum(['CYBERSECURITY', 'DATA_SCIENCE', 'BLOCKCHAIN', 'PROJECT_MANAGEMENT', 'DIGITAL_MARKETING', 'FINANCIAL_LITERACY', 'LANGUAGE_LEARNING', 'ARTIFICIAL_INTELLIGENCE', 'WEB_DEVELOPMENT', 'MOBILE_DEVELOPMENT', 'CLOUD_COMPUTING', 'ENTREPRENEURSHIP']),\n  skillLevel: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']),\n  author: z.string().max(100, 'Author name must be less than 100 characters').optional(),\n  duration: z.string().max(50, 'Duration must be less than 50 characters').optional(),\n  cost: z.enum(['FREE', 'FREEMIUM', 'PAID', 'SUBSCRIPTION']).default('FREE'),\n  format: z.enum(['SELF_PACED', 'INSTRUCTOR_LED', 'INTERACTIVE', 'HANDS_ON', 'THEORETICAL']),\n});\n\n// Resource Rating validation schemas\nexport const resourceRatingSchema = z.object({\n  resourceId: z.string().uuid('Invalid resource ID'),\n  rating: z.number().int().min(1, 'Rating must be at least 1').max(5, 'Rating must be at most 5'),\n  review: z.string().max(1000, 'Review must be less than 1000 characters').optional(),\n  isHelpful: z.boolean().optional(),\n});\n\n// Learning Progress validation schemas\nexport const learningProgressSchema = z.object({\n  resourceId: z.string().uuid('Invalid resource ID'),\n  status: z.enum(['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'BOOKMARKED']),\n  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),\n  rating: z.number().int().min(1, 'Rating must be at least 1').max(5, 'Rating must be at most 5').optional(),\n  review: z.string().max(1000, 'Review must be less than 1000 characters').optional(),\n});\n\n// Forum validation schemas with enhanced security\nexport const forumPostSchema = z.object({\n  title: z.string()\n    .transform((val) => sanitizeString(val))\n    .pipe(z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters')),\n  content: z.string()\n    .transform((val) => sanitizeLongText(val, 5000))\n    .pipe(z.string().min(1, 'Content is required').max(5000, 'Content must be less than 5000 characters')),\n});\n\nexport const forumReplySchema = z.object({\n  content: z.string()\n    .transform((val) => sanitizeLongText(val, 2000))\n    .pipe(z.string().min(1, 'Content is required').max(2000, 'Content must be less than 2000 characters')),\n  postId: z.string()\n    .transform(sanitizeString)\n    .pipe(z.string().uuid('Invalid post ID')),\n});\n\n// Contact form validation schema with enhanced security\nexport const contactFormSchema = z.object({\n  name: z.string()\n    .transform(sanitizeString)\n    .pipe(z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters')),\n  email: z.string()\n    .transform(sanitizeString)\n    .pipe(z.string().email('Invalid email address').max(254, 'Email too long')),\n  subject: z.string()\n    .transform(sanitizeString)\n    .pipe(z.string().min(1, 'Subject is required').max(200, 'Subject must be less than 200 characters')),\n  message: z.string()\n    .transform((val) => sanitizeLongText(val, 2000))\n    .pipe(z.string().min(1, 'Message is required').max(2000, 'Message must be less than 2000 characters')),\n});\n\n// Career Path validation schemas\nexport const careerPathSchema = z.object({\n  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),\n  slug: z.string().min(1, 'Slug is required').max(100, 'Slug must be less than 100 characters'),\n  overview: z.string().min(1, 'Overview is required').max(2000, 'Overview must be less than 2000 characters'),\n  pros: z.string().min(1, 'Pros are required'),\n  cons: z.string().min(1, 'Cons are required'),\n  actionableSteps: z.array(z.object({\n    title: z.string().min(1, 'Step title is required'),\n    description: z.string().min(1, 'Step description is required'),\n  })),\n  isActive: z.boolean().default(true),\n});\n\n// Pagination validation schema\nexport const paginationSchema = z.object({\n  page: z.string().nullable().optional().transform((val) => val ? parseInt(val, 10) : 1),\n  limit: z.string().nullable().optional().transform((val) => val ? parseInt(val, 10) : 10),\n});\n\n// Resource filter validation schema\nexport const resourceFilterSchema = z.object({\n  category: z.string().nullable().optional(),\n  type: z.string().nullable().optional(),\n  skillLevel: z.string().nullable().optional(),\n  cost: z.string().nullable().optional(),\n  format: z.string().nullable().optional(),\n  search: z.string().nullable().optional(),\n});\n\n// Utility function to validate request body\nexport function validateRequestBody<T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; error: string } {\n  try {\n    const validatedData = schema.parse(data);\n    return { success: true, data: validatedData };\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      const errorMessage = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');\n      return { success: false, error: errorMessage };\n    }\n    return { success: false, error: 'Invalid data format' };\n  }\n}\n\n// Utility function to validate input\nexport function validateInput<T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; error: string } {\n  try {\n    const validatedData = schema.parse(data);\n    return { success: true, data: validatedData };\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      const errorMessage = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');\n      return { success: false, error: errorMessage };\n    }\n    return { success: false, error: 'Invalid data format' };\n  }\n}\n\n// Rate limiting types\nexport interface RateLimitConfig {\n  windowMs: number;\n  maxRequests: number;\n  message?: string;\n}\n\n\n\nexport const rateLimitConfigs = {\n  auth: { windowMs: 15 * 60 * 1000, maxRequests: 5, message: 'Too many authentication attempts' }, // 5 attempts per 15 minutes\n  api: { windowMs: 15 * 60 * 1000, maxRequests: 100, message: 'Too many API requests' }, // 100 requests per 15 minutes\n  contact: { windowMs: 60 * 60 * 1000, maxRequests: 3, message: 'Too many contact form submissions' }, // 3 submissions per hour\n} as const;\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "865088a3364040e11573eab2811e990a6902af90"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_bw4kgqzov = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_bw4kgqzov();
cov_bw4kgqzov().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_bw4kgqzov().s[1]++;
exports.rateLimitConfigs = exports.resourceFilterSchema = exports.paginationSchema = exports.careerPathSchema = exports.contactFormSchema = exports.forumReplySchema = exports.forumPostSchema = exports.learningProgressSchema = exports.resourceRatingSchema = exports.learningResourceSchema = exports.freedomFundSchema = exports.assessmentSaveSchema = exports.assessmentResponseSchema = exports.profileUpdateSchema = exports.resetPasswordSchema = exports.emailSchema = exports.forgotPasswordSchema = exports.loginSchema = exports.signupSchema = exports.SecurityValidator = void 0;
/* istanbul ignore next */
cov_bw4kgqzov().s[2]++;
exports.validateRequestBody = validateRequestBody;
/* istanbul ignore next */
cov_bw4kgqzov().s[3]++;
exports.validateInput = validateInput;
var zod_1 =
/* istanbul ignore next */
(cov_bw4kgqzov().s[4]++, require("zod"));
// Security: Advanced Input Sanitization and Validation
var SecurityValidator =
/* istanbul ignore next */
(/** @class */cov_bw4kgqzov().s[5]++, function () {
  /* istanbul ignore next */
  cov_bw4kgqzov().f[0]++;
  function SecurityValidator() {
    /* istanbul ignore next */
    cov_bw4kgqzov().f[1]++;
  }
  /**
   * Comprehensive input sanitization that handles all major attack vectors
   */
  /* istanbul ignore next */
  cov_bw4kgqzov().s[6]++;
  SecurityValidator.sanitizeInput = function (input, options) {
    /* istanbul ignore next */
    cov_bw4kgqzov().f[2]++;
    cov_bw4kgqzov().s[7]++;
    if (options === void 0) {
      /* istanbul ignore next */
      cov_bw4kgqzov().b[0][0]++;
      cov_bw4kgqzov().s[8]++;
      options = {};
    } else
    /* istanbul ignore next */
    {
      cov_bw4kgqzov().b[0][1]++;
    }
    cov_bw4kgqzov().s[9]++;
    if (typeof input !== 'string') {
      /* istanbul ignore next */
      cov_bw4kgqzov().b[1][0]++;
      cov_bw4kgqzov().s[10]++;
      return '';
    } else
    /* istanbul ignore next */
    {
      cov_bw4kgqzov().b[1][1]++;
    }
    var sanitized =
    /* istanbul ignore next */
    (cov_bw4kgqzov().s[11]++, input);
    // 1. Remove command injection patterns
    /* istanbul ignore next */
    cov_bw4kgqzov().s[12]++;
    this.COMMAND_INJECTION_PATTERNS.forEach(function (pattern) {
      /* istanbul ignore next */
      cov_bw4kgqzov().f[3]++;
      cov_bw4kgqzov().s[13]++;
      sanitized = sanitized.replace(pattern, '');
    });
    // 2. Remove format string attack patterns
    /* istanbul ignore next */
    cov_bw4kgqzov().s[14]++;
    this.FORMAT_STRING_PATTERNS.forEach(function (pattern) {
      /* istanbul ignore next */
      cov_bw4kgqzov().f[4]++;
      cov_bw4kgqzov().s[15]++;
      sanitized = sanitized.replace(pattern, '');
    });
    // 3. Remove XSS patterns
    /* istanbul ignore next */
    cov_bw4kgqzov().s[16]++;
    if (!options.allowHtml) {
      /* istanbul ignore next */
      cov_bw4kgqzov().b[2][0]++;
      cov_bw4kgqzov().s[17]++;
      this.XSS_PATTERNS.forEach(function (pattern) {
        /* istanbul ignore next */
        cov_bw4kgqzov().f[5]++;
        cov_bw4kgqzov().s[18]++;
        sanitized = sanitized.replace(pattern, '');
      });
      // Remove all HTML tags if HTML not allowed
      /* istanbul ignore next */
      cov_bw4kgqzov().s[19]++;
      sanitized = sanitized.replace(/<[^>]*>/g, '');
    } else
    /* istanbul ignore next */
    {
      cov_bw4kgqzov().b[2][1]++;
    }
    // 4. Remove SQL injection patterns
    cov_bw4kgqzov().s[20]++;
    this.SQL_INJECTION_PATTERNS.forEach(function (pattern) {
      /* istanbul ignore next */
      cov_bw4kgqzov().f[6]++;
      cov_bw4kgqzov().s[21]++;
      sanitized = sanitized.replace(pattern, '');
    });
    // 5. Remove path traversal patterns
    /* istanbul ignore next */
    cov_bw4kgqzov().s[22]++;
    this.PATH_TRAVERSAL_PATTERNS.forEach(function (pattern) {
      /* istanbul ignore next */
      cov_bw4kgqzov().f[7]++;
      cov_bw4kgqzov().s[23]++;
      sanitized = sanitized.replace(pattern, '');
    });
    // 6. Remove LDAP injection patterns
    /* istanbul ignore next */
    cov_bw4kgqzov().s[24]++;
    this.LDAP_INJECTION_PATTERNS.forEach(function (pattern) {
      /* istanbul ignore next */
      cov_bw4kgqzov().f[8]++;
      cov_bw4kgqzov().s[25]++;
      sanitized = sanitized.replace(pattern, '');
    });
    // 7. Remove null bytes and control characters
    /* istanbul ignore next */
    cov_bw4kgqzov().s[26]++;
    sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
    // 8. Handle newlines
    /* istanbul ignore next */
    cov_bw4kgqzov().s[27]++;
    if (!options.preserveNewlines) {
      /* istanbul ignore next */
      cov_bw4kgqzov().b[3][0]++;
      cov_bw4kgqzov().s[28]++;
      sanitized = sanitized.replace(/[\r\n]/g, ' ');
    } else
    /* istanbul ignore next */
    {
      cov_bw4kgqzov().b[3][1]++;
    }
    // 9. Normalize whitespace
    cov_bw4kgqzov().s[29]++;
    sanitized = sanitized.replace(/\s+/g, ' ').trim();
    // 10. Limit length
    var maxLength =
    /* istanbul ignore next */
    (cov_bw4kgqzov().s[30]++,
    /* istanbul ignore next */
    (cov_bw4kgqzov().b[4][0]++, options.maxLength) ||
    /* istanbul ignore next */
    (cov_bw4kgqzov().b[4][1]++, 10000));
    /* istanbul ignore next */
    cov_bw4kgqzov().s[31]++;
    if (sanitized.length > maxLength) {
      /* istanbul ignore next */
      cov_bw4kgqzov().b[5][0]++;
      cov_bw4kgqzov().s[32]++;
      sanitized = sanitized.slice(0, maxLength);
    } else
    /* istanbul ignore next */
    {
      cov_bw4kgqzov().b[5][1]++;
    }
    cov_bw4kgqzov().s[33]++;
    return sanitized;
  };
  /**
   * Validate that input doesn't contain malicious patterns
   */
  /* istanbul ignore next */
  cov_bw4kgqzov().s[34]++;
  SecurityValidator.validateSecurity = function (input, options) {
    /* istanbul ignore next */
    cov_bw4kgqzov().f[9]++;
    cov_bw4kgqzov().s[35]++;
    if (options === void 0) {
      /* istanbul ignore next */
      cov_bw4kgqzov().b[6][0]++;
      cov_bw4kgqzov().s[36]++;
      options = {};
    } else
    /* istanbul ignore next */
    {
      cov_bw4kgqzov().b[6][1]++;
    }
    var threats =
    /* istanbul ignore next */
    (cov_bw4kgqzov().s[37]++, []);
    // Skip validation in test environment for basic inputs
    /* istanbul ignore next */
    cov_bw4kgqzov().s[38]++;
    if (
    /* istanbul ignore next */
    (cov_bw4kgqzov().b[8][0]++, options.isTestEnvironment) &&
    /* istanbul ignore next */
    (cov_bw4kgqzov().b[8][1]++, this.isBasicInput(input))) {
      /* istanbul ignore next */
      cov_bw4kgqzov().b[7][0]++;
      cov_bw4kgqzov().s[39]++;
      return {
        isValid: true,
        threats: []
      };
    } else
    /* istanbul ignore next */
    {
      cov_bw4kgqzov().b[7][1]++;
    }
    // Check for command injection
    cov_bw4kgqzov().s[40]++;
    if (this.COMMAND_INJECTION_PATTERNS.some(function (pattern) {
      /* istanbul ignore next */
      cov_bw4kgqzov().f[10]++;
      cov_bw4kgqzov().s[41]++;
      return pattern.test(input);
    })) {
      /* istanbul ignore next */
      cov_bw4kgqzov().b[9][0]++;
      cov_bw4kgqzov().s[42]++;
      threats.push('Command Injection');
    } else
    /* istanbul ignore next */
    {
      cov_bw4kgqzov().b[9][1]++;
    }
    // Check for format string attacks
    cov_bw4kgqzov().s[43]++;
    if (this.FORMAT_STRING_PATTERNS.some(function (pattern) {
      /* istanbul ignore next */
      cov_bw4kgqzov().f[11]++;
      cov_bw4kgqzov().s[44]++;
      return pattern.test(input);
    })) {
      /* istanbul ignore next */
      cov_bw4kgqzov().b[10][0]++;
      cov_bw4kgqzov().s[45]++;
      threats.push('Format String Attack');
    } else
    /* istanbul ignore next */
    {
      cov_bw4kgqzov().b[10][1]++;
    }
    // Check for XSS
    cov_bw4kgqzov().s[46]++;
    if (this.XSS_PATTERNS.some(function (pattern) {
      /* istanbul ignore next */
      cov_bw4kgqzov().f[12]++;
      cov_bw4kgqzov().s[47]++;
      return pattern.test(input);
    })) {
      /* istanbul ignore next */
      cov_bw4kgqzov().b[11][0]++;
      cov_bw4kgqzov().s[48]++;
      threats.push('Cross-Site Scripting (XSS)');
    } else
    /* istanbul ignore next */
    {
      cov_bw4kgqzov().b[11][1]++;
    }
    // Check for SQL injection
    cov_bw4kgqzov().s[49]++;
    if (this.SQL_INJECTION_PATTERNS.some(function (pattern) {
      /* istanbul ignore next */
      cov_bw4kgqzov().f[13]++;
      cov_bw4kgqzov().s[50]++;
      return pattern.test(input);
    })) {
      /* istanbul ignore next */
      cov_bw4kgqzov().b[12][0]++;
      cov_bw4kgqzov().s[51]++;
      threats.push('SQL Injection');
    } else
    /* istanbul ignore next */
    {
      cov_bw4kgqzov().b[12][1]++;
    }
    // Check for path traversal
    cov_bw4kgqzov().s[52]++;
    if (this.PATH_TRAVERSAL_PATTERNS.some(function (pattern) {
      /* istanbul ignore next */
      cov_bw4kgqzov().f[14]++;
      cov_bw4kgqzov().s[53]++;
      return pattern.test(input);
    })) {
      /* istanbul ignore next */
      cov_bw4kgqzov().b[13][0]++;
      cov_bw4kgqzov().s[54]++;
      threats.push('Path Traversal');
    } else
    /* istanbul ignore next */
    {
      cov_bw4kgqzov().b[13][1]++;
    }
    // Check for LDAP injection
    cov_bw4kgqzov().s[55]++;
    if (this.LDAP_INJECTION_PATTERNS.some(function (pattern) {
      /* istanbul ignore next */
      cov_bw4kgqzov().f[15]++;
      cov_bw4kgqzov().s[56]++;
      return pattern.test(input);
    })) {
      /* istanbul ignore next */
      cov_bw4kgqzov().b[14][0]++;
      cov_bw4kgqzov().s[57]++;
      threats.push('LDAP Injection');
    } else
    /* istanbul ignore next */
    {
      cov_bw4kgqzov().b[14][1]++;
    }
    cov_bw4kgqzov().s[58]++;
    return {
      isValid: threats.length === 0,
      threats: threats
    };
  };
  /**
   * Check if input is basic/safe (email, simple text, etc.)
   */
  /* istanbul ignore next */
  cov_bw4kgqzov().s[59]++;
  SecurityValidator.isBasicInput = function (input) {
    /* istanbul ignore next */
    cov_bw4kgqzov().f[16]++;
    // Email pattern
    var emailPattern =
    /* istanbul ignore next */
    (cov_bw4kgqzov().s[60]++, /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/);
    /* istanbul ignore next */
    cov_bw4kgqzov().s[61]++;
    if (emailPattern.test(input)) {
      /* istanbul ignore next */
      cov_bw4kgqzov().b[15][0]++;
      cov_bw4kgqzov().s[62]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_bw4kgqzov().b[15][1]++;
    }
    // Simple alphanumeric with basic punctuation
    var basicPattern =
    /* istanbul ignore next */
    (cov_bw4kgqzov().s[63]++, /^[a-zA-Z0-9\s.,!?@#$%^&*()_+-=\[\]{}|;':"<>?/~`]+$/);
    /* istanbul ignore next */
    cov_bw4kgqzov().s[64]++;
    if (
    /* istanbul ignore next */
    (cov_bw4kgqzov().b[17][0]++, basicPattern.test(input)) &&
    /* istanbul ignore next */
    (cov_bw4kgqzov().b[17][1]++, input.length < 1000)) {
      /* istanbul ignore next */
      cov_bw4kgqzov().b[16][0]++;
      cov_bw4kgqzov().s[65]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_bw4kgqzov().b[16][1]++;
    }
    cov_bw4kgqzov().s[66]++;
    return false;
  };
  /**
   * Enhanced HTML escaping
   */
  /* istanbul ignore next */
  cov_bw4kgqzov().s[67]++;
  SecurityValidator.escapeHtml = function (unsafe) {
    /* istanbul ignore next */
    cov_bw4kgqzov().f[17]++;
    cov_bw4kgqzov().s[68]++;
    return unsafe.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;").replace(/\//g, "&#x2F;");
  };
  /**
   * Safe JSON parsing with validation
   */
  /* istanbul ignore next */
  cov_bw4kgqzov().s[69]++;
  SecurityValidator.safeJsonParse = function (jsonString) {
    /* istanbul ignore next */
    cov_bw4kgqzov().f[18]++;
    cov_bw4kgqzov().s[70]++;
    try {
      // First sanitize the JSON string
      var sanitized =
      /* istanbul ignore next */
      (cov_bw4kgqzov().s[71]++, this.sanitizeInput(jsonString, {
        maxLength: 100000
      }));
      // Validate it doesn't contain malicious patterns
      var validation =
      /* istanbul ignore next */
      (cov_bw4kgqzov().s[72]++, this.validateSecurity(sanitized));
      /* istanbul ignore next */
      cov_bw4kgqzov().s[73]++;
      if (!validation.isValid) {
        /* istanbul ignore next */
        cov_bw4kgqzov().b[18][0]++;
        cov_bw4kgqzov().s[74]++;
        return {
          success: false,
          error: "Security threats detected: ".concat(validation.threats.join(', '))
        };
      } else
      /* istanbul ignore next */
      {
        cov_bw4kgqzov().b[18][1]++;
      }
      var parsed =
      /* istanbul ignore next */
      (cov_bw4kgqzov().s[75]++, JSON.parse(sanitized));
      /* istanbul ignore next */
      cov_bw4kgqzov().s[76]++;
      return {
        success: true,
        data: parsed
      };
    } catch (error) {
      /* istanbul ignore next */
      cov_bw4kgqzov().s[77]++;
      return {
        success: false,
        error: error instanceof Error ?
        /* istanbul ignore next */
        (cov_bw4kgqzov().b[19][0]++, error.message) :
        /* istanbul ignore next */
        (cov_bw4kgqzov().b[19][1]++, 'Invalid JSON')
      };
    }
  };
  // Command injection patterns (refined to avoid false positives)
  /* istanbul ignore next */
  cov_bw4kgqzov().s[78]++;
  SecurityValidator.COMMAND_INJECTION_PATTERNS = [/[;&|`$](?=\s*\w)/g,
  // Shell metacharacters followed by commands
  /\|\s*\w+/g,
  // Pipe commands
  /&&\s*\w+/g,
  // Command chaining
  /;\s*\w+/g,
  // Command separation
  /`[^`]*`/g,
  // Command substitution
  /\$\([^)]*\)/g,
  // Command substitution
  /\$\{[^}]*\}/g,
  // Variable expansion
  />\s*\/\w+/g,
  // File redirection
  /<\s*\/\w+/g,
  // File input
  /\|\|\s*\w+/g,
  // OR command execution
  /\b(rm|del|format|fdisk|kill|shutdown|reboot)\s+/gi // Dangerous commands
  ];
  // Format string attack patterns
  /* istanbul ignore next */
  cov_bw4kgqzov().s[79]++;
  SecurityValidator.FORMAT_STRING_PATTERNS = [/%[sdxXocp]/g,
  // C-style format specifiers
  /%\d+\$[sdxXocp]/g,
  // Positional format specifiers
  /%[0-9]*[hlL]?[sdxXocp]/g,
  // Format with length modifiers
  /%n/g,
  // Write format specifier (dangerous)
  /%\*[sdxXocp]/g // Dynamic width format specifiers
  ];
  // XSS patterns (enhanced)
  /* istanbul ignore next */
  cov_bw4kgqzov().s[80]++;
  SecurityValidator.XSS_PATTERNS = [/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, /<link\b[^>]*>/gi, /<meta\b[^>]*>/gi, /javascript:/gi, /vbscript:/gi, /data:text\/html/gi, /on\w+\s*=/gi // Event handlers
  ];
  // SQL injection patterns
  /* istanbul ignore next */
  cov_bw4kgqzov().s[81]++;
  SecurityValidator.SQL_INJECTION_PATTERNS = [/('|(\\'))+.*(;|--|\||\/\*|\*\/)/gi, /(union|select|insert|update|delete|drop|create|alter|exec|execute)\s/gi, /\b(or|and)\s+\d+\s*=\s*\d+/gi, /\b(or|and)\s+['"]?\w+['"]?\s*=\s*['"]?\w+['"]?/gi];
  // Path traversal patterns
  /* istanbul ignore next */
  cov_bw4kgqzov().s[82]++;
  SecurityValidator.PATH_TRAVERSAL_PATTERNS = [/\.\.[\/\\]/g,
  // Directory traversal
  /[\/\\]\.\.[\/\\]/g,
  // Absolute path traversal
  /\.\.[\/\\]\.\./g,
  // Multiple level traversal
  /\.\.\/|\.\.\\|\.\.$/g,
  // Various traversal patterns
  /[\/\\]etc[\/\\]passwd/gi,
  // Unix system files
  /[\/\\]windows[\/\\]system32/gi,
  // Windows system files
  /\.\..*[\/\\]/g // Any .. followed by path separator
  ];
  // LDAP injection patterns
  /* istanbul ignore next */
  cov_bw4kgqzov().s[83]++;
  SecurityValidator.LDAP_INJECTION_PATTERNS = [/\*\)\(\&/g,
  // LDAP wildcard injection
  /\*\)\(\|/g,
  // LDAP OR injection
  /\)\(\&\(/g,
  // LDAP AND injection
  /\)\(\|\(/g // LDAP OR injection
  ];
  /* istanbul ignore next */
  cov_bw4kgqzov().s[84]++;
  return SecurityValidator;
}());
/* istanbul ignore next */
cov_bw4kgqzov().s[85]++;
exports.SecurityValidator = SecurityValidator;
// Enhanced input sanitization function
function sanitizeString(value) {
  /* istanbul ignore next */
  cov_bw4kgqzov().f[19]++;
  cov_bw4kgqzov().s[86]++;
  return SecurityValidator.sanitizeInput(value, {
    maxLength: 254
  });
}
function sanitizeLongText(value, maxLength) {
  /* istanbul ignore next */
  cov_bw4kgqzov().f[20]++;
  cov_bw4kgqzov().s[87]++;
  if (maxLength === void 0) {
    /* istanbul ignore next */
    cov_bw4kgqzov().b[20][0]++;
    cov_bw4kgqzov().s[88]++;
    maxLength = 5000;
  } else
  /* istanbul ignore next */
  {
    cov_bw4kgqzov().b[20][1]++;
  }
  cov_bw4kgqzov().s[89]++;
  return SecurityValidator.sanitizeInput(value, {
    maxLength: maxLength,
    preserveNewlines: true,
    allowHtml: false
  });
}
// User validation schemas with enhanced security
/* istanbul ignore next */
cov_bw4kgqzov().s[90]++;
exports.signupSchema = zod_1.z.object({
  email: zod_1.z.string().transform(sanitizeString).pipe(zod_1.z.string().email('Invalid email address').max(254, 'Email too long')),
  password: zod_1.z.string().min(8, 'Password must be at least 8 characters').max(128, 'Password too long').regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
});
/* istanbul ignore next */
cov_bw4kgqzov().s[91]++;
exports.loginSchema = zod_1.z.object({
  email: zod_1.z.string().transform(sanitizeString).pipe(zod_1.z.string().email('Invalid email address').max(254, 'Email too long')),
  password: zod_1.z.string().min(1, 'Password is required').max(128, 'Password too long')
});
/* istanbul ignore next */
cov_bw4kgqzov().s[92]++;
exports.forgotPasswordSchema = zod_1.z.object({
  email: zod_1.z.string().transform(sanitizeString).pipe(zod_1.z.string().email('Invalid email address').max(254, 'Email too long'))
});
/* istanbul ignore next */
cov_bw4kgqzov().s[93]++;
exports.emailSchema = zod_1.z.object({
  email: zod_1.z.string().transform(sanitizeString).pipe(zod_1.z.string().email('Invalid email address').max(254, 'Email too long'))
});
/* istanbul ignore next */
cov_bw4kgqzov().s[94]++;
exports.resetPasswordSchema = zod_1.z.object({
  token: zod_1.z.string().transform(sanitizeString).pipe(zod_1.z.string().min(1, 'Token is required').max(256, 'Token too long')),
  password: zod_1.z.string().min(8, 'Password must be at least 8 characters').max(128, 'Password too long').regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
});
// Profile validation schemas
/* istanbul ignore next */
cov_bw4kgqzov().s[95]++;
exports.profileUpdateSchema = zod_1.z.object({
  bio: zod_1.z.string().max(500, 'Bio must be less than 500 characters').optional(),
  profilePictureUrl: zod_1.z.string().url('Invalid URL').optional().or(zod_1.z.literal('')),
  socialMediaLinks: zod_1.z.record(zod_1.z.string().url('Invalid URL')).optional(),
  // Personal Information
  firstName: zod_1.z.string().max(50, 'First name must be less than 50 characters').optional(),
  lastName: zod_1.z.string().max(50, 'Last name must be less than 50 characters').optional(),
  phoneNumber: zod_1.z.string().regex(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number format').optional().or(zod_1.z.literal('')),
  location: zod_1.z.string().max(100, 'Location must be less than 100 characters').optional(),
  website: zod_1.z.string().url('Invalid website URL').optional().or(zod_1.z.literal('')),
  // Professional Information
  jobTitle: zod_1.z.string().max(100, 'Job title must be less than 100 characters').optional(),
  company: zod_1.z.string().max(100, 'Company name must be less than 100 characters').optional(),
  currentIndustry: zod_1.z.string().max(100, 'Industry must be less than 100 characters').optional(),
  targetIndustry: zod_1.z.string().max(100, 'Target industry must be less than 100 characters').optional(),
  experienceLevel: zod_1.z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
  // Career Development
  careerInterests: zod_1.z.array(zod_1.z.string().max(50)).max(10, 'Maximum 10 career interests allowed').optional(),
  skillsToLearn: zod_1.z.array(zod_1.z.string().max(50)).max(20, 'Maximum 20 skills allowed').optional(),
  weeklyLearningGoal: zod_1.z.number().min(1, 'Weekly goal must be at least 1 hour').max(168, 'Weekly goal cannot exceed 168 hours').optional(),
  // Privacy & Preferences
  profileVisibility: zod_1.z.enum(['PRIVATE', 'PUBLIC', 'COMMUNITY_ONLY']).optional(),
  emailNotifications: zod_1.z.boolean().optional(),
  profilePublic: zod_1.z.boolean().optional(),
  showEmail: zod_1.z.boolean().optional(),
  showPhone: zod_1.z.boolean().optional()
});
// Assessment validation schemas
/* istanbul ignore next */
cov_bw4kgqzov().s[96]++;
exports.assessmentResponseSchema = zod_1.z.object({
  questionKey: zod_1.z.string().min(1, 'Question key is required'),
  answerValue: zod_1.z.union([zod_1.z.string(), zod_1.z.number(), zod_1.z.boolean(), zod_1.z.array(zod_1.z.string()), zod_1.z.null()])
});
/* istanbul ignore next */
cov_bw4kgqzov().s[97]++;
exports.assessmentSaveSchema = zod_1.z.object({
  currentStep: zod_1.z.number().min(0, 'Current step must be non-negative'),
  formData: zod_1.z.record(zod_1.z.union([zod_1.z.string(), zod_1.z.number(), zod_1.z.boolean(), zod_1.z.array(zod_1.z.string()), zod_1.z.null()])),
  status: zod_1.z.enum(['IN_PROGRESS', 'COMPLETED']).optional()
});
// Freedom Fund validation schemas
/* istanbul ignore next */
cov_bw4kgqzov().s[98]++;
exports.freedomFundSchema = zod_1.z.object({
  monthlyExpenses: zod_1.z.number().positive('Monthly expenses must be positive'),
  coverageMonths: zod_1.z.number().int().positive('Coverage months must be a positive integer'),
  targetSavings: zod_1.z.number().positive('Target savings must be positive'),
  currentSavingsAmount: zod_1.z.number().min(0, 'Current savings cannot be negative').optional()
});
// Learning Resource validation schemas
/* istanbul ignore next */
cov_bw4kgqzov().s[99]++;
exports.learningResourceSchema = zod_1.z.object({
  title: zod_1.z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: zod_1.z.string().min(1, 'Description is required').max(1000, 'Description must be less than 1000 characters'),
  url: zod_1.z.string().url('Invalid URL'),
  type: zod_1.z.enum(['COURSE', 'ARTICLE', 'VIDEO', 'PODCAST', 'BOOK', 'CERTIFICATION', 'TUTORIAL', 'WORKSHOP']),
  category: zod_1.z.enum(['CYBERSECURITY', 'DATA_SCIENCE', 'BLOCKCHAIN', 'PROJECT_MANAGEMENT', 'DIGITAL_MARKETING', 'FINANCIAL_LITERACY', 'LANGUAGE_LEARNING', 'ARTIFICIAL_INTELLIGENCE', 'WEB_DEVELOPMENT', 'MOBILE_DEVELOPMENT', 'CLOUD_COMPUTING', 'ENTREPRENEURSHIP']),
  skillLevel: zod_1.z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']),
  author: zod_1.z.string().max(100, 'Author name must be less than 100 characters').optional(),
  duration: zod_1.z.string().max(50, 'Duration must be less than 50 characters').optional(),
  cost: zod_1.z.enum(['FREE', 'FREEMIUM', 'PAID', 'SUBSCRIPTION']).default('FREE'),
  format: zod_1.z.enum(['SELF_PACED', 'INSTRUCTOR_LED', 'INTERACTIVE', 'HANDS_ON', 'THEORETICAL'])
});
// Resource Rating validation schemas
/* istanbul ignore next */
cov_bw4kgqzov().s[100]++;
exports.resourceRatingSchema = zod_1.z.object({
  resourceId: zod_1.z.string().uuid('Invalid resource ID'),
  rating: zod_1.z.number().int().min(1, 'Rating must be at least 1').max(5, 'Rating must be at most 5'),
  review: zod_1.z.string().max(1000, 'Review must be less than 1000 characters').optional(),
  isHelpful: zod_1.z.boolean().optional()
});
// Learning Progress validation schemas
/* istanbul ignore next */
cov_bw4kgqzov().s[101]++;
exports.learningProgressSchema = zod_1.z.object({
  resourceId: zod_1.z.string().uuid('Invalid resource ID'),
  status: zod_1.z.enum(['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'BOOKMARKED']),
  notes: zod_1.z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
  rating: zod_1.z.number().int().min(1, 'Rating must be at least 1').max(5, 'Rating must be at most 5').optional(),
  review: zod_1.z.string().max(1000, 'Review must be less than 1000 characters').optional()
});
// Forum validation schemas with enhanced security
/* istanbul ignore next */
cov_bw4kgqzov().s[102]++;
exports.forumPostSchema = zod_1.z.object({
  title: zod_1.z.string().transform(function (val) {
    /* istanbul ignore next */
    cov_bw4kgqzov().f[21]++;
    cov_bw4kgqzov().s[103]++;
    return sanitizeString(val);
  }).pipe(zod_1.z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters')),
  content: zod_1.z.string().transform(function (val) {
    /* istanbul ignore next */
    cov_bw4kgqzov().f[22]++;
    cov_bw4kgqzov().s[104]++;
    return sanitizeLongText(val, 5000);
  }).pipe(zod_1.z.string().min(1, 'Content is required').max(5000, 'Content must be less than 5000 characters'))
});
/* istanbul ignore next */
cov_bw4kgqzov().s[105]++;
exports.forumReplySchema = zod_1.z.object({
  content: zod_1.z.string().transform(function (val) {
    /* istanbul ignore next */
    cov_bw4kgqzov().f[23]++;
    cov_bw4kgqzov().s[106]++;
    return sanitizeLongText(val, 2000);
  }).pipe(zod_1.z.string().min(1, 'Content is required').max(2000, 'Content must be less than 2000 characters')),
  postId: zod_1.z.string().transform(sanitizeString).pipe(zod_1.z.string().uuid('Invalid post ID'))
});
// Contact form validation schema with enhanced security
/* istanbul ignore next */
cov_bw4kgqzov().s[107]++;
exports.contactFormSchema = zod_1.z.object({
  name: zod_1.z.string().transform(sanitizeString).pipe(zod_1.z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters')),
  email: zod_1.z.string().transform(sanitizeString).pipe(zod_1.z.string().email('Invalid email address').max(254, 'Email too long')),
  subject: zod_1.z.string().transform(sanitizeString).pipe(zod_1.z.string().min(1, 'Subject is required').max(200, 'Subject must be less than 200 characters')),
  message: zod_1.z.string().transform(function (val) {
    /* istanbul ignore next */
    cov_bw4kgqzov().f[24]++;
    cov_bw4kgqzov().s[108]++;
    return sanitizeLongText(val, 2000);
  }).pipe(zod_1.z.string().min(1, 'Message is required').max(2000, 'Message must be less than 2000 characters'))
});
// Career Path validation schemas
/* istanbul ignore next */
cov_bw4kgqzov().s[109]++;
exports.careerPathSchema = zod_1.z.object({
  name: zod_1.z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  slug: zod_1.z.string().min(1, 'Slug is required').max(100, 'Slug must be less than 100 characters'),
  overview: zod_1.z.string().min(1, 'Overview is required').max(2000, 'Overview must be less than 2000 characters'),
  pros: zod_1.z.string().min(1, 'Pros are required'),
  cons: zod_1.z.string().min(1, 'Cons are required'),
  actionableSteps: zod_1.z.array(zod_1.z.object({
    title: zod_1.z.string().min(1, 'Step title is required'),
    description: zod_1.z.string().min(1, 'Step description is required')
  })),
  isActive: zod_1.z.boolean().default(true)
});
// Pagination validation schema
/* istanbul ignore next */
cov_bw4kgqzov().s[110]++;
exports.paginationSchema = zod_1.z.object({
  page: zod_1.z.string().nullable().optional().transform(function (val) {
    /* istanbul ignore next */
    cov_bw4kgqzov().f[25]++;
    cov_bw4kgqzov().s[111]++;
    return val ?
    /* istanbul ignore next */
    (cov_bw4kgqzov().b[21][0]++, parseInt(val, 10)) :
    /* istanbul ignore next */
    (cov_bw4kgqzov().b[21][1]++, 1);
  }),
  limit: zod_1.z.string().nullable().optional().transform(function (val) {
    /* istanbul ignore next */
    cov_bw4kgqzov().f[26]++;
    cov_bw4kgqzov().s[112]++;
    return val ?
    /* istanbul ignore next */
    (cov_bw4kgqzov().b[22][0]++, parseInt(val, 10)) :
    /* istanbul ignore next */
    (cov_bw4kgqzov().b[22][1]++, 10);
  })
});
// Resource filter validation schema
/* istanbul ignore next */
cov_bw4kgqzov().s[113]++;
exports.resourceFilterSchema = zod_1.z.object({
  category: zod_1.z.string().nullable().optional(),
  type: zod_1.z.string().nullable().optional(),
  skillLevel: zod_1.z.string().nullable().optional(),
  cost: zod_1.z.string().nullable().optional(),
  format: zod_1.z.string().nullable().optional(),
  search: zod_1.z.string().nullable().optional()
});
// Utility function to validate request body
function validateRequestBody(schema, data) {
  /* istanbul ignore next */
  cov_bw4kgqzov().f[27]++;
  cov_bw4kgqzov().s[114]++;
  try {
    var validatedData =
    /* istanbul ignore next */
    (cov_bw4kgqzov().s[115]++, schema.parse(data));
    /* istanbul ignore next */
    cov_bw4kgqzov().s[116]++;
    return {
      success: true,
      data: validatedData
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_bw4kgqzov().s[117]++;
    if (error instanceof zod_1.z.ZodError) {
      /* istanbul ignore next */
      cov_bw4kgqzov().b[23][0]++;
      var errorMessage =
      /* istanbul ignore next */
      (cov_bw4kgqzov().s[118]++, error.errors.map(function (err) {
        /* istanbul ignore next */
        cov_bw4kgqzov().f[28]++;
        cov_bw4kgqzov().s[119]++;
        return "".concat(err.path.join('.'), ": ").concat(err.message);
      }).join(', '));
      /* istanbul ignore next */
      cov_bw4kgqzov().s[120]++;
      return {
        success: false,
        error: errorMessage
      };
    } else
    /* istanbul ignore next */
    {
      cov_bw4kgqzov().b[23][1]++;
    }
    cov_bw4kgqzov().s[121]++;
    return {
      success: false,
      error: 'Invalid data format'
    };
  }
}
// Utility function to validate input
function validateInput(schema, data) {
  /* istanbul ignore next */
  cov_bw4kgqzov().f[29]++;
  cov_bw4kgqzov().s[122]++;
  try {
    var validatedData =
    /* istanbul ignore next */
    (cov_bw4kgqzov().s[123]++, schema.parse(data));
    /* istanbul ignore next */
    cov_bw4kgqzov().s[124]++;
    return {
      success: true,
      data: validatedData
    };
  } catch (error) {
    /* istanbul ignore next */
    cov_bw4kgqzov().s[125]++;
    if (error instanceof zod_1.z.ZodError) {
      /* istanbul ignore next */
      cov_bw4kgqzov().b[24][0]++;
      var errorMessage =
      /* istanbul ignore next */
      (cov_bw4kgqzov().s[126]++, error.errors.map(function (err) {
        /* istanbul ignore next */
        cov_bw4kgqzov().f[30]++;
        cov_bw4kgqzov().s[127]++;
        return "".concat(err.path.join('.'), ": ").concat(err.message);
      }).join(', '));
      /* istanbul ignore next */
      cov_bw4kgqzov().s[128]++;
      return {
        success: false,
        error: errorMessage
      };
    } else
    /* istanbul ignore next */
    {
      cov_bw4kgqzov().b[24][1]++;
    }
    cov_bw4kgqzov().s[129]++;
    return {
      success: false,
      error: 'Invalid data format'
    };
  }
}
/* istanbul ignore next */
cov_bw4kgqzov().s[130]++;
exports.rateLimitConfigs = {
  auth: {
    windowMs: 15 * 60 * 1000,
    maxRequests: 5,
    message: 'Too many authentication attempts'
  },
  // 5 attempts per 15 minutes
  api: {
    windowMs: 15 * 60 * 1000,
    maxRequests: 100,
    message: 'Too many API requests'
  },
  // 100 requests per 15 minutes
  contact: {
    windowMs: 60 * 60 * 1000,
    maxRequests: 3,
    message: 'Too many contact form submissions'
  } // 3 submissions per hour
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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