ed06f3b4bc9ebcd6af577db545ab45b5
"use strict";
/**
 * Enhanced Assessment Logic Test Suite
 * Tests the improved assessment scoring algorithms and career recommendation systems
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var globals_1 = require("@jest/globals");
var algorithmicAssessmentService_1 = require("../../lib/algorithmicAssessmentService");
var enhancedFallbackService_1 = require("../../lib/enhancedFallbackService");
var assessmentServiceIntegration_1 = require("../../lib/assessmentServiceIntegration");
var assessmentScoring_1 = require("../../lib/assessmentScoring");
// Mock data for testing
var mockAssessmentResponse = {
    current_role: 'Software Developer',
    years_experience: '3-5',
    skill_development_interest: ['coding_tech', 'ai_ml', 'mobile_development'],
    career_values: ['growth', 'autonomy', 'impact'],
    work_style_preferences: ['remote', 'collaborative', 'flexible'],
    biggest_obstacles: ['skill_gaps', 'time_constraints'],
    financial_readiness: 4,
    support_level: 3,
    risk_tolerance: 3,
    urgency_level: 4,
    skills_confidence: 75,
    desired_outcomes_work_life: 'Better work-life balance',
    desired_outcomes_financial: 'Higher salary',
    desired_outcomes_personal: 'Career growth',
    location: 'San Francisco, CA'
};
var mockInsights = {
    scores: {
        financialReadiness: 4,
        supportLevel: 3,
        riskTolerance: 3,
        urgencyLevel: 4,
        skillsConfidence: 75,
        readinessScore: 70
    },
    primaryMotivation: 'Career advancement',
    topSkills: ['technical_programming', 'data_analysis', 'project_management'],
    biggestObstacles: ['skill_gaps', 'time_constraints'],
    recommendedTimeline: '6-12 months',
    keyRecommendations: ['Focus on AI/ML skills', 'Build portfolio projects'],
    careerPathSuggestions: [],
    careerPathAnalysis: [],
    overallSkillGaps: [],
    learningPriorities: ['technical_programming', 'data_analysis', 'project_management'],
    estimatedTransitionTime: '6-12 months'
};
(0, globals_1.describe)('Enhanced Assessment Logic', function () {
    (0, globals_1.beforeAll)(function () { return __awaiter(void 0, void 0, void 0, function () {
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0: 
                // Initialize the assessment services
                return [4 /*yield*/, algorithmicAssessmentService_1.AlgorithmicAssessmentService.initialize()];
                case 1:
                    // Initialize the assessment services
                    _a.sent();
                    return [4 /*yield*/, assessmentServiceIntegration_1.AssessmentServiceIntegration.initialize()];
                case 2:
                    _a.sent();
                    return [2 /*return*/];
            }
        });
    }); });
    (0, globals_1.describe)('AlgorithmicAssessmentService', function () {
        (0, globals_1.it)('should generate career recommendations with algorithmic matching', function () { return __awaiter(void 0, void 0, void 0, function () {
            var recommendations, firstRec;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, algorithmicAssessmentService_1.AlgorithmicAssessmentService.generateCareerRecommendations(mockAssessmentResponse, mockInsights)];
                    case 1:
                        recommendations = _a.sent();
                        (0, globals_1.expect)(recommendations).toBeDefined();
                        (0, globals_1.expect)(Array.isArray(recommendations)).toBe(true);
                        (0, globals_1.expect)(recommendations.length).toBeGreaterThan(0);
                        firstRec = recommendations[0];
                        (0, globals_1.expect)(firstRec).toHaveProperty('careerPath');
                        (0, globals_1.expect)(firstRec).toHaveProperty('matchScore');
                        (0, globals_1.expect)(firstRec).toHaveProperty('matchFactors');
                        (0, globals_1.expect)(firstRec).toHaveProperty('confidenceLevel');
                        (0, globals_1.expect)(firstRec).toHaveProperty('reasoning');
                        (0, globals_1.expect)(firstRec).toHaveProperty('skillGaps');
                        (0, globals_1.expect)(firstRec).toHaveProperty('estimatedTimeline');
                        (0, globals_1.expect)(firstRec).toHaveProperty('successProbability');
                        // Validate match score range
                        (0, globals_1.expect)(firstRec.matchScore).toBeGreaterThanOrEqual(0);
                        (0, globals_1.expect)(firstRec.matchScore).toBeLessThanOrEqual(100);
                        // Validate confidence level
                        (0, globals_1.expect)(firstRec.confidenceLevel).toBeGreaterThanOrEqual(0);
                        (0, globals_1.expect)(firstRec.confidenceLevel).toBeLessThanOrEqual(100);
                        // Validate success probability
                        (0, globals_1.expect)(firstRec.successProbability).toBeGreaterThanOrEqual(0);
                        (0, globals_1.expect)(firstRec.successProbability).toBeLessThanOrEqual(100);
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should calculate skill alignment correctly', function () { return __awaiter(void 0, void 0, void 0, function () {
            var recommendations, techRecommendation;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, algorithmicAssessmentService_1.AlgorithmicAssessmentService.generateCareerRecommendations(mockAssessmentResponse, mockInsights)];
                    case 1:
                        recommendations = _a.sent();
                        techRecommendation = recommendations.find(function (rec) {
                            return rec.careerPath.name.toLowerCase().includes('developer') ||
                                rec.careerPath.name.toLowerCase().includes('engineer');
                        });
                        if (techRecommendation) {
                            (0, globals_1.expect)(techRecommendation.matchFactors.skillAlignment).toBeGreaterThan(50);
                        }
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should provide detailed skill gap analysis', function () { return __awaiter(void 0, void 0, void 0, function () {
            var recommendations, firstRec, skillGap;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, algorithmicAssessmentService_1.AlgorithmicAssessmentService.generateCareerRecommendations(mockAssessmentResponse, mockInsights)];
                    case 1:
                        recommendations = _a.sent();
                        firstRec = recommendations[0];
                        (0, globals_1.expect)(Array.isArray(firstRec.skillGaps)).toBe(true);
                        if (firstRec.skillGaps.length > 0) {
                            skillGap = firstRec.skillGaps[0];
                            (0, globals_1.expect)(skillGap).toHaveProperty('skill');
                            (0, globals_1.expect)(skillGap).toHaveProperty('currentLevel');
                            (0, globals_1.expect)(skillGap).toHaveProperty('requiredLevel');
                            (0, globals_1.expect)(skillGap).toHaveProperty('priority');
                            (0, globals_1.expect)(skillGap).toHaveProperty('estimatedLearningTime');
                            (0, globals_1.expect)(['HIGH', 'MEDIUM', 'LOW']).toContain(skillGap.priority);
                        }
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should generate meaningful reasoning', function () { return __awaiter(void 0, void 0, void 0, function () {
            var recommendations, firstRec;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, algorithmicAssessmentService_1.AlgorithmicAssessmentService.generateCareerRecommendations(mockAssessmentResponse, mockInsights)];
                    case 1:
                        recommendations = _a.sent();
                        firstRec = recommendations[0];
                        (0, globals_1.expect)(Array.isArray(firstRec.reasoning)).toBe(true);
                        (0, globals_1.expect)(firstRec.reasoning.length).toBeGreaterThan(0);
                        (0, globals_1.expect)(firstRec.reasoning[0]).toBeTruthy();
                        (0, globals_1.expect)(typeof firstRec.reasoning[0]).toBe('string');
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should handle edge cases gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var emptyResponse, emptyInsights, recommendations;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        emptyResponse = {
                            current_role: '',
                            years_experience: '',
                            skill_development_interest: [],
                            career_values: [],
                            work_style_preferences: [],
                            biggest_obstacles: [],
                            financial_readiness: 1,
                            support_level: 1,
                            risk_tolerance: 1,
                            urgency_level: 1,
                            skills_confidence: 10,
                            desired_outcomes_work_life: '',
                            desired_outcomes_financial: '',
                            desired_outcomes_personal: '',
                            location: ''
                        };
                        emptyInsights = __assign(__assign({}, mockInsights), { topSkills: [], scores: {
                                financialReadiness: 1,
                                supportLevel: 1,
                                riskTolerance: 1,
                                urgencyLevel: 1,
                                skillsConfidence: 10,
                                readinessScore: 20
                            } });
                        return [4 /*yield*/, algorithmicAssessmentService_1.AlgorithmicAssessmentService.generateCareerRecommendations(emptyResponse, emptyInsights)];
                    case 1:
                        recommendations = _a.sent();
                        (0, globals_1.expect)(recommendations).toBeDefined();
                        (0, globals_1.expect)(Array.isArray(recommendations)).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    (0, globals_1.describe)('EnhancedFallbackService', function () {
        (0, globals_1.it)('should generate comprehensive fallback insights', function () { return __awaiter(void 0, void 0, void 0, function () {
            var fallbackInsights;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, enhancedFallbackService_1.EnhancedFallbackService.generateFallbackInsights(mockAssessmentResponse, mockInsights, 'Testing fallback service')];
                    case 1:
                        fallbackInsights = _a.sent();
                        (0, globals_1.expect)(fallbackInsights).toBeDefined();
                        (0, globals_1.expect)(fallbackInsights).toHaveProperty('careerRecommendations');
                        (0, globals_1.expect)(fallbackInsights).toHaveProperty('skillGapAnalysis');
                        (0, globals_1.expect)(fallbackInsights).toHaveProperty('learningPathRecommendations');
                        (0, globals_1.expect)(fallbackInsights).toHaveProperty('marketInsights');
                        (0, globals_1.expect)(fallbackInsights).toHaveProperty('personalizedAdvice');
                        (0, globals_1.expect)(fallbackInsights).toHaveProperty('confidenceScore');
                        (0, globals_1.expect)(fallbackInsights).toHaveProperty('fallbackReason');
                        (0, globals_1.expect)(fallbackInsights).toHaveProperty('generatedAt');
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should provide quality career recommendations in fallback mode', function () { return __awaiter(void 0, void 0, void 0, function () {
            var fallbackInsights, recommendations, firstRec;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, enhancedFallbackService_1.EnhancedFallbackService.generateFallbackInsights(mockAssessmentResponse, mockInsights)];
                    case 1:
                        fallbackInsights = _a.sent();
                        recommendations = fallbackInsights.careerRecommendations;
                        (0, globals_1.expect)(Array.isArray(recommendations)).toBe(true);
                        (0, globals_1.expect)(recommendations.length).toBeGreaterThan(0);
                        firstRec = recommendations[0];
                        (0, globals_1.expect)(firstRec).toHaveProperty('careerPath');
                        (0, globals_1.expect)(firstRec).toHaveProperty('matchPercentage');
                        (0, globals_1.expect)(firstRec).toHaveProperty('reasoning');
                        (0, globals_1.expect)(firstRec).toHaveProperty('salaryRange');
                        (0, globals_1.expect)(firstRec).toHaveProperty('marketOutlook');
                        (0, globals_1.expect)(firstRec).toHaveProperty('transitionDifficulty');
                        (0, globals_1.expect)(firstRec).toHaveProperty('timeToTransition');
                        (0, globals_1.expect)(firstRec).toHaveProperty('keySkillsNeeded');
                        (0, globals_1.expect)(firstRec).toHaveProperty('successFactors');
                        (0, globals_1.expect)(firstRec).toHaveProperty('potentialChallenges');
                        (0, globals_1.expect)(['LOW', 'MEDIUM', 'HIGH']).toContain(firstRec.transitionDifficulty);
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should provide actionable skill gap analysis', function () { return __awaiter(void 0, void 0, void 0, function () {
            var fallbackInsights, skillGaps, skillGap;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, enhancedFallbackService_1.EnhancedFallbackService.generateFallbackInsights(mockAssessmentResponse, mockInsights)];
                    case 1:
                        fallbackInsights = _a.sent();
                        skillGaps = fallbackInsights.skillGapAnalysis;
                        (0, globals_1.expect)(Array.isArray(skillGaps)).toBe(true);
                        if (skillGaps.length > 0) {
                            skillGap = skillGaps[0];
                            (0, globals_1.expect)(skillGap).toHaveProperty('skill');
                            (0, globals_1.expect)(skillGap).toHaveProperty('currentLevel');
                            (0, globals_1.expect)(skillGap).toHaveProperty('targetLevel');
                            (0, globals_1.expect)(skillGap).toHaveProperty('priority');
                            (0, globals_1.expect)(skillGap).toHaveProperty('learningTime');
                            (0, globals_1.expect)(skillGap).toHaveProperty('recommendedApproach');
                            (0, globals_1.expect)(skillGap).toHaveProperty('marketValue');
                            (0, globals_1.expect)(['HIGH', 'MEDIUM', 'LOW']).toContain(skillGap.priority);
                            (0, globals_1.expect)(skillGap.marketValue).toBeGreaterThanOrEqual(1);
                            (0, globals_1.expect)(skillGap.marketValue).toBeLessThanOrEqual(10);
                        }
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should generate personalized advice', function () { return __awaiter(void 0, void 0, void 0, function () {
            var fallbackInsights, advice;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, enhancedFallbackService_1.EnhancedFallbackService.generateFallbackInsights(mockAssessmentResponse, mockInsights)];
                    case 1:
                        fallbackInsights = _a.sent();
                        advice = fallbackInsights.personalizedAdvice;
                        (0, globals_1.expect)(Array.isArray(advice)).toBe(true);
                        (0, globals_1.expect)(advice.length).toBeGreaterThan(0);
                        advice.forEach(function (adviceItem) {
                            (0, globals_1.expect)(typeof adviceItem).toBe('string');
                            (0, globals_1.expect)(adviceItem.length).toBeGreaterThan(10); // Should be meaningful advice
                        });
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should provide market insights', function () { return __awaiter(void 0, void 0, void 0, function () {
            var fallbackInsights, marketInsights;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, enhancedFallbackService_1.EnhancedFallbackService.generateFallbackInsights(mockAssessmentResponse, mockInsights)];
                    case 1:
                        fallbackInsights = _a.sent();
                        marketInsights = fallbackInsights.marketInsights;
                        (0, globals_1.expect)(marketInsights).toHaveProperty('industryTrends');
                        (0, globals_1.expect)(marketInsights).toHaveProperty('emergingSkills');
                        (0, globals_1.expect)(marketInsights).toHaveProperty('salaryTrends');
                        (0, globals_1.expect)(marketInsights).toHaveProperty('jobMarketHealth');
                        (0, globals_1.expect)(marketInsights).toHaveProperty('geographicOpportunities');
                        (0, globals_1.expect)(marketInsights).toHaveProperty('automationImpact');
                        (0, globals_1.expect)(['EXCELLENT', 'GOOD', 'FAIR', 'CHALLENGING']).toContain(marketInsights.jobMarketHealth);
                        (0, globals_1.expect)(Array.isArray(marketInsights.industryTrends)).toBe(true);
                        (0, globals_1.expect)(Array.isArray(marketInsights.emergingSkills)).toBe(true);
                        (0, globals_1.expect)(Array.isArray(marketInsights.geographicOpportunities)).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should calculate appropriate confidence scores', function () { return __awaiter(void 0, void 0, void 0, function () {
            var fallbackInsights;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, enhancedFallbackService_1.EnhancedFallbackService.generateFallbackInsights(mockAssessmentResponse, mockInsights)];
                    case 1:
                        fallbackInsights = _a.sent();
                        (0, globals_1.expect)(fallbackInsights.confidenceScore).toBeGreaterThanOrEqual(60);
                        (0, globals_1.expect)(fallbackInsights.confidenceScore).toBeLessThanOrEqual(95);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    (0, globals_1.describe)('AssessmentServiceIntegration', function () {
        (0, globals_1.it)('should provide integrated career recommendations', function () { return __awaiter(void 0, void 0, void 0, function () {
            var result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, assessmentServiceIntegration_1.AssessmentServiceIntegration.generateCareerRecommendations(mockAssessmentResponse, mockInsights)];
                    case 1:
                        result = _a.sent();
                        (0, globals_1.expect)(result).toBeDefined();
                        (0, globals_1.expect)(result).toHaveProperty('careerRecommendations');
                        (0, globals_1.expect)(result).toHaveProperty('serviceUsed');
                        (0, globals_1.expect)(result).toHaveProperty('confidence');
                        (0, globals_1.expect)(result).toHaveProperty('processingTime');
                        (0, globals_1.expect)(Array.isArray(result.careerRecommendations)).toBe(true);
                        (0, globals_1.expect)(result.careerRecommendations.length).toBeGreaterThan(0);
                        (0, globals_1.expect)(['algorithmic', 'fallback', 'basic']).toContain(result.serviceUsed);
                        (0, globals_1.expect)(result.confidence).toBeGreaterThanOrEqual(0);
                        (0, globals_1.expect)(result.confidence).toBeLessThanOrEqual(100);
                        (0, globals_1.expect)(result.processingTime).toBeGreaterThan(0);
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should perform health checks correctly', function () { return __awaiter(void 0, void 0, void 0, function () {
            var healthCheck;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, assessmentServiceIntegration_1.AssessmentServiceIntegration.healthCheck()];
                    case 1:
                        healthCheck = _a.sent();
                        (0, globals_1.expect)(healthCheck).toBeDefined();
                        (0, globals_1.expect)(healthCheck).toHaveProperty('algorithmic');
                        (0, globals_1.expect)(healthCheck).toHaveProperty('fallback');
                        (0, globals_1.expect)(healthCheck).toHaveProperty('basic');
                        (0, globals_1.expect)(healthCheck).toHaveProperty('overall');
                        (0, globals_1.expect)(typeof healthCheck.algorithmic).toBe('boolean');
                        (0, globals_1.expect)(typeof healthCheck.fallback).toBe('boolean');
                        (0, globals_1.expect)(typeof healthCheck.basic).toBe('boolean');
                        (0, globals_1.expect)(['healthy', 'degraded', 'critical']).toContain(healthCheck.overall);
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should handle service failures gracefully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var invalidResponse, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        invalidResponse = {
                            current_role: null,
                            years_experience: null,
                            skill_development_interest: null,
                            career_values: null,
                            work_style_preferences: null,
                            biggest_obstacles: null,
                            financial_readiness: null,
                            support_level: null,
                            risk_tolerance: null,
                            urgency_level: null,
                            skills_confidence: null,
                            desired_outcomes_work_life: null,
                            desired_outcomes_financial: null,
                            desired_outcomes_personal: null,
                            location: null
                        };
                        return [4 /*yield*/, assessmentServiceIntegration_1.AssessmentServiceIntegration.generateCareerRecommendations(invalidResponse, mockInsights)];
                    case 1:
                        result = _a.sent();
                        // Should still provide some recommendations even with invalid data
                        (0, globals_1.expect)(result).toBeDefined();
                        (0, globals_1.expect)(Array.isArray(result.careerRecommendations)).toBe(true);
                        (0, globals_1.expect)(result.careerRecommendations.length).toBeGreaterThan(0);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    (0, globals_1.describe)('Integration with Assessment Scoring', function () {
        (0, globals_1.it)('should integrate algorithmic recommendations with assessment insights', function () { return __awaiter(void 0, void 0, void 0, function () {
            var insights, hasOnlyDefaults;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, (0, assessmentScoring_1.generateAssessmentInsights)(mockAssessmentResponse)];
                    case 1:
                        insights = _a.sent();
                        (0, globals_1.expect)(insights).toHaveProperty('careerPathSuggestions');
                        (0, globals_1.expect)(Array.isArray(insights.careerPathSuggestions)).toBe(true);
                        (0, globals_1.expect)(insights.careerPathSuggestions.length).toBeGreaterThan(0);
                        hasOnlyDefaults = insights.careerPathSuggestions.every(function (suggestion) {
                            return ['Digital Marketing Specialist', 'Entrepreneur / Startup Founder', 'Full-Stack Web Developer'].includes(suggestion);
                        });
                        // With proper algorithmic matching, we should get more diverse suggestions
                        (0, globals_1.expect)(hasOnlyDefaults).toBe(false);
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should handle fallback gracefully when algorithmic service fails', function () { return __awaiter(void 0, void 0, void 0, function () {
            var originalImport, insights;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        originalImport = globals_1.jest.requireActual('../../lib/algorithmicAssessmentService');
                        globals_1.jest.doMock('../../lib/algorithmicAssessmentService', function () { return ({
                            AlgorithmicAssessmentService: {
                                generateCareerRecommendations: globals_1.jest.fn().mockRejectedValue(new Error('Service unavailable'))
                            }
                        }); });
                        return [4 /*yield*/, (0, assessmentScoring_1.generateAssessmentInsights)(mockAssessmentResponse)];
                    case 1:
                        insights = _a.sent();
                        (0, globals_1.expect)(insights).toHaveProperty('careerPathSuggestions');
                        (0, globals_1.expect)(Array.isArray(insights.careerPathSuggestions)).toBe(true);
                        (0, globals_1.expect)(insights.careerPathSuggestions.length).toBeGreaterThan(0);
                        // Should still provide meaningful suggestions via fallback
                        (0, globals_1.expect)(insights.careerPathSuggestions).not.toEqual([]);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    (0, globals_1.describe)('Performance and Reliability', function () {
        (0, globals_1.it)('should complete assessment generation within reasonable time', function () { return __awaiter(void 0, void 0, void 0, function () {
            var startTime, recommendations, endTime, duration;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        startTime = Date.now();
                        return [4 /*yield*/, algorithmicAssessmentService_1.AlgorithmicAssessmentService.generateCareerRecommendations(mockAssessmentResponse, mockInsights)];
                    case 1:
                        recommendations = _a.sent();
                        endTime = Date.now();
                        duration = endTime - startTime;
                        (0, globals_1.expect)(duration).toBeLessThan(5000); // Should complete within 5 seconds
                        (0, globals_1.expect)(recommendations.length).toBeGreaterThan(0);
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should handle concurrent requests properly', function () { return __awaiter(void 0, void 0, void 0, function () {
            var promises, results;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        promises = Array(5).fill(null).map(function () {
                            return algorithmicAssessmentService_1.AlgorithmicAssessmentService.generateCareerRecommendations(mockAssessmentResponse, mockInsights);
                        });
                        return [4 /*yield*/, Promise.all(promises)];
                    case 1:
                        results = _a.sent();
                        results.forEach(function (result) {
                            (0, globals_1.expect)(Array.isArray(result)).toBe(true);
                            (0, globals_1.expect)(result.length).toBeGreaterThan(0);
                        });
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should maintain consistency across multiple calls', function () { return __awaiter(void 0, void 0, void 0, function () {
            var result1, result2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, algorithmicAssessmentService_1.AlgorithmicAssessmentService.generateCareerRecommendations(mockAssessmentResponse, mockInsights)];
                    case 1:
                        result1 = _a.sent();
                        return [4 /*yield*/, algorithmicAssessmentService_1.AlgorithmicAssessmentService.generateCareerRecommendations(mockAssessmentResponse, mockInsights)];
                    case 2:
                        result2 = _a.sent();
                        // Results should be consistent for the same input
                        (0, globals_1.expect)(result1.length).toBe(result2.length);
                        (0, globals_1.expect)(result1[0].careerPath.name).toBe(result2[0].careerPath.name);
                        (0, globals_1.expect)(result1[0].matchScore).toBe(result2[0].matchScore);
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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