{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/assessment/enhanced-assessment-logic.test.ts", "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,yCAAsE;AACtE,uFAAsF;AACtF,6EAA4E;AAC5E,uFAAsF;AACtF,iEAA6F;AAE7F,wBAAwB;AACxB,IAAM,sBAAsB,GAAuB;IACjD,YAAY,EAAE,oBAAoB;IAClC,gBAAgB,EAAE,KAAK;IACvB,0BAA0B,EAAE,CAAC,aAAa,EAAE,OAAO,EAAE,oBAAoB,CAAC;IAC1E,aAAa,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC;IAC/C,sBAAsB,EAAE,CAAC,QAAQ,EAAE,eAAe,EAAE,UAAU,CAAC;IAC/D,iBAAiB,EAAE,CAAC,YAAY,EAAE,kBAAkB,CAAC;IACrD,mBAAmB,EAAE,CAAC;IACtB,aAAa,EAAE,CAAC;IAChB,cAAc,EAAE,CAAC;IACjB,aAAa,EAAE,CAAC;IAChB,iBAAiB,EAAE,EAAE;IACrB,0BAA0B,EAAE,0BAA0B;IACtD,0BAA0B,EAAE,eAAe;IAC3C,yBAAyB,EAAE,eAAe;IAC1C,QAAQ,EAAE,mBAAmB;CAC9B,CAAC;AAEF,IAAM,YAAY,GAAG;IACnB,MAAM,EAAE;QACN,kBAAkB,EAAE,CAAC;QACrB,YAAY,EAAE,CAAC;QACf,aAAa,EAAE,CAAC;QAChB,YAAY,EAAE,CAAC;QACf,gBAAgB,EAAE,EAAE;QACpB,cAAc,EAAE,EAAE;KACnB;IACD,iBAAiB,EAAE,oBAAoB;IACvC,SAAS,EAAE,CAAC,uBAAuB,EAAE,eAAe,EAAE,oBAAoB,CAAC;IAC3E,gBAAgB,EAAE,CAAC,YAAY,EAAE,kBAAkB,CAAC;IACpD,mBAAmB,EAAE,aAAa;IAClC,kBAAkB,EAAE,CAAC,uBAAuB,EAAE,0BAA0B,CAAC;IACzE,qBAAqB,EAAE,EAAE;IACzB,kBAAkB,EAAE,EAAE;IACtB,gBAAgB,EAAE,EAAE;IACpB,kBAAkB,EAAE,CAAC,uBAAuB,EAAE,eAAe,EAAE,oBAAoB,CAAC;IACpF,uBAAuB,EAAE,aAAa;CACvC,CAAC;AAEF,IAAA,kBAAQ,EAAC,2BAA2B,EAAE;IACpC,IAAA,mBAAS,EAAC;;;;gBACR,qCAAqC;gBACrC,qBAAM,2DAA4B,CAAC,UAAU,EAAE,EAAA;;oBAD/C,qCAAqC;oBACrC,SAA+C,CAAC;oBAChD,qBAAM,2DAA4B,CAAC,UAAU,EAAE,EAAA;;oBAA/C,SAA+C,CAAC;;;;SACjD,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,8BAA8B,EAAE;QACvC,IAAA,YAAE,EAAC,kEAAkE,EAAE;;;;4BAC7C,qBAAM,2DAA4B,CAAC,6BAA6B,CACtF,sBAAsB,EACtB,YAAY,CACb,EAAA;;wBAHK,eAAe,GAAG,SAGvB;wBAED,IAAA,gBAAM,EAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;wBACtC,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClD,IAAA,gBAAM,EAAC,eAAe,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAG5C,QAAQ,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;wBACpC,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;wBAC9C,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;wBAC9C,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;wBAChD,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;wBACnD,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;wBAC7C,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;wBAC7C,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;wBACrD,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;wBAEtD,6BAA6B;wBAC7B,IAAA,gBAAM,EAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;wBACtD,IAAA,gBAAM,EAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;wBAErD,4BAA4B;wBAC5B,IAAA,gBAAM,EAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;wBAC3D,IAAA,gBAAM,EAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;wBAE1D,+BAA+B;wBAC/B,IAAA,gBAAM,EAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;wBAC9D,IAAA,gBAAM,EAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;;;;aAC9D,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,4CAA4C,EAAE;;;;4BACvB,qBAAM,2DAA4B,CAAC,6BAA6B,CACtF,sBAAsB,EACtB,YAAY,CACb,EAAA;;wBAHK,eAAe,GAAG,SAGvB;wBAEK,kBAAkB,GAAG,eAAe,CAAC,IAAI,CAAC,UAAA,GAAG;4BACjD,OAAA,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC;gCACvD,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;wBADtD,CACsD,CACvD,CAAC;wBAEF,IAAI,kBAAkB,EAAE,CAAC;4BACvB,IAAA,gBAAM,EAAC,kBAAkB,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;wBAC7E,CAAC;;;;aACF,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,4CAA4C,EAAE;;;;4BACvB,qBAAM,2DAA4B,CAAC,6BAA6B,CACtF,sBAAsB,EACtB,YAAY,CACb,EAAA;;wBAHK,eAAe,GAAG,SAGvB;wBAEK,QAAQ,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;wBACpC,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAErD,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BAC5B,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;4BACvC,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;4BACzC,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;4BAChD,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;4BACjD,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;4BAC5C,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;4BAEzD,IAAA,gBAAM,EAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;wBACjE,CAAC;;;;aACF,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,sCAAsC,EAAE;;;;4BACjB,qBAAM,2DAA4B,CAAC,6BAA6B,CACtF,sBAAsB,EACtB,YAAY,CACb,EAAA;;wBAHK,eAAe,GAAG,SAGvB;wBAEK,QAAQ,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;wBACpC,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACrD,IAAA,gBAAM,EAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBACrD,IAAA,gBAAM,EAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;wBAC3C,IAAA,gBAAM,EAAC,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;;;aACrD,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,qCAAqC,EAAE;;;;;wBAClC,aAAa,GAAuB;4BACxC,YAAY,EAAE,EAAE;4BAChB,gBAAgB,EAAE,EAAE;4BACpB,0BAA0B,EAAE,EAAE;4BAC9B,aAAa,EAAE,EAAE;4BACjB,sBAAsB,EAAE,EAAE;4BAC1B,iBAAiB,EAAE,EAAE;4BACrB,mBAAmB,EAAE,CAAC;4BACtB,aAAa,EAAE,CAAC;4BAChB,cAAc,EAAE,CAAC;4BACjB,aAAa,EAAE,CAAC;4BAChB,iBAAiB,EAAE,EAAE;4BACrB,0BAA0B,EAAE,EAAE;4BAC9B,0BAA0B,EAAE,EAAE;4BAC9B,yBAAyB,EAAE,EAAE;4BAC7B,QAAQ,EAAE,EAAE;yBACb,CAAC;wBAEI,aAAa,yBACd,YAAY,KACf,SAAS,EAAE,EAAE,EACb,MAAM,EAAE;gCACN,kBAAkB,EAAE,CAAC;gCACrB,YAAY,EAAE,CAAC;gCACf,aAAa,EAAE,CAAC;gCAChB,YAAY,EAAE,CAAC;gCACf,gBAAgB,EAAE,EAAE;gCACpB,cAAc,EAAE,EAAE;6BACnB,GACF,CAAC;wBAEsB,qBAAM,2DAA4B,CAAC,6BAA6B,CACtF,aAAa,EACb,aAAa,CACd,EAAA;;wBAHK,eAAe,GAAG,SAGvB;wBAED,IAAA,gBAAM,EAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;wBACtC,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aAEnD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,yBAAyB,EAAE;QAClC,IAAA,YAAE,EAAC,iDAAiD,EAAE;;;;4BAC3B,qBAAM,iDAAuB,CAAC,wBAAwB,CAC7E,sBAAsB,EACtB,YAAY,EACZ,0BAA0B,CAC3B,EAAA;;wBAJK,gBAAgB,GAAG,SAIxB;wBAED,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,CAAC;wBACvC,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;wBACjE,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;wBAC5D,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,6BAA6B,CAAC,CAAC;wBACvE,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;wBAC1D,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;wBAC9D,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;wBAC3D,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;wBAC1D,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;;;;aACxD,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,gEAAgE,EAAE;;;;4BAC1C,qBAAM,iDAAuB,CAAC,wBAAwB,CAC7E,sBAAsB,EACtB,YAAY,CACb,EAAA;;wBAHK,gBAAgB,GAAG,SAGxB;wBAEK,eAAe,GAAG,gBAAgB,CAAC,qBAAqB,CAAC;wBAC/D,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClD,IAAA,gBAAM,EAAC,eAAe,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAE5C,QAAQ,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;wBACpC,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;wBAC9C,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;wBACnD,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;wBAC7C,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;wBAC/C,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;wBACjD,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;wBACxD,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;wBACpD,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;wBACnD,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;wBAClD,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;wBAEvD,IAAA,gBAAM,EAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;;;;aAC5E,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,8CAA8C,EAAE;;;;4BACxB,qBAAM,iDAAuB,CAAC,wBAAwB,CAC7E,sBAAsB,EACtB,YAAY,CACb,EAAA;;wBAHK,gBAAgB,GAAG,SAGxB;wBAEK,SAAS,GAAG,gBAAgB,CAAC,gBAAgB,CAAC;wBACpD,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAE5C,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACnB,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;4BAC9B,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;4BACzC,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;4BAChD,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;4BAC/C,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;4BAC5C,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;4BAChD,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;4BACvD,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;4BAE/C,IAAA,gBAAM,EAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;4BAC/D,IAAA,gBAAM,EAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;4BACvD,IAAA,gBAAM,EAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;wBACvD,CAAC;;;;aACF,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,qCAAqC,EAAE;;;;4BACf,qBAAM,iDAAuB,CAAC,wBAAwB,CAC7E,sBAAsB,EACtB,YAAY,CACb,EAAA;;wBAHK,gBAAgB,GAAG,SAGxB;wBAEK,MAAM,GAAG,gBAAgB,CAAC,kBAAkB,CAAC;wBACnD,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACzC,IAAA,gBAAM,EAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAEzC,MAAM,CAAC,OAAO,CAAC,UAAA,UAAU;4BACvB,IAAA,gBAAM,EAAC,OAAO,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;4BACzC,IAAA,gBAAM,EAAC,UAAU,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,8BAA8B;wBAC/E,CAAC,CAAC,CAAC;;;;aACJ,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,gCAAgC,EAAE;;;;4BACV,qBAAM,iDAAuB,CAAC,wBAAwB,CAC7E,sBAAsB,EACtB,YAAY,CACb,EAAA;;wBAHK,gBAAgB,GAAG,SAGxB;wBAEK,cAAc,GAAG,gBAAgB,CAAC,cAAc,CAAC;wBACvD,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;wBACxD,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;wBACxD,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;wBACtD,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;wBACzD,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAC;wBACjE,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;wBAE1D,IAAA,gBAAM,EAAC,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;wBAC/F,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAChE,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAChE,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aAC1E,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,gDAAgD,EAAE;;;;4BAC1B,qBAAM,iDAAuB,CAAC,wBAAwB,CAC7E,sBAAsB,EACtB,YAAY,CACb,EAAA;;wBAHK,gBAAgB,GAAG,SAGxB;wBAED,IAAA,gBAAM,EAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;wBACpE,IAAA,gBAAM,EAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;;;;aAClE,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,8BAA8B,EAAE;QACvC,IAAA,YAAE,EAAC,kDAAkD,EAAE;;;;4BACtC,qBAAM,2DAA4B,CAAC,6BAA6B,CAC7E,sBAAsB,EACtB,YAAY,CACb,EAAA;;wBAHK,MAAM,GAAG,SAGd;wBAED,IAAA,gBAAM,EAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC7B,IAAA,gBAAM,EAAC,MAAM,CAAC,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;wBACvD,IAAA,gBAAM,EAAC,MAAM,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;wBAC7C,IAAA,gBAAM,EAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;wBAC5C,IAAA,gBAAM,EAAC,MAAM,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;wBAEhD,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC/D,IAAA,gBAAM,EAAC,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC/D,IAAA,gBAAM,EAAC,CAAC,aAAa,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;wBAC3E,IAAA,gBAAM,EAAC,MAAM,CAAC,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;wBACpD,IAAA,gBAAM,EAAC,MAAM,CAAC,UAAU,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;wBACnD,IAAA,gBAAM,EAAC,MAAM,CAAC,cAAc,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;;;;aAClD,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,wCAAwC,EAAE;;;;4BACvB,qBAAM,2DAA4B,CAAC,WAAW,EAAE,EAAA;;wBAA9D,WAAW,GAAG,SAAgD;wBAEpE,IAAA,gBAAM,EAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;wBAClC,IAAA,gBAAM,EAAC,WAAW,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;wBAClD,IAAA,gBAAM,EAAC,WAAW,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;wBAC/C,IAAA,gBAAM,EAAC,WAAW,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;wBAC5C,IAAA,gBAAM,EAAC,WAAW,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;wBAE9C,IAAA,gBAAM,EAAC,OAAO,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBACvD,IAAA,gBAAM,EAAC,OAAO,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBACpD,IAAA,gBAAM,EAAC,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBACjD,IAAA,gBAAM,EAAC,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;;;;aAC5E,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,2CAA2C,EAAE;;;;;wBAExC,eAAe,GAAuB;4BAC1C,YAAY,EAAE,IAAW;4BACzB,gBAAgB,EAAE,IAAW;4BAC7B,0BAA0B,EAAE,IAAW;4BACvC,aAAa,EAAE,IAAW;4BAC1B,sBAAsB,EAAE,IAAW;4BACnC,iBAAiB,EAAE,IAAW;4BAC9B,mBAAmB,EAAE,IAAW;4BAChC,aAAa,EAAE,IAAW;4BAC1B,cAAc,EAAE,IAAW;4BAC3B,aAAa,EAAE,IAAW;4BAC1B,iBAAiB,EAAE,IAAW;4BAC9B,0BAA0B,EAAE,IAAW;4BACvC,0BAA0B,EAAE,IAAW;4BACvC,yBAAyB,EAAE,IAAW;4BACtC,QAAQ,EAAE,IAAW;yBACtB,CAAC;wBAEa,qBAAM,2DAA4B,CAAC,6BAA6B,CAC7E,eAAe,EACf,YAAY,CACb,EAAA;;wBAHK,MAAM,GAAG,SAGd;wBAED,mEAAmE;wBACnE,IAAA,gBAAM,EAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC7B,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC/D,IAAA,gBAAM,EAAC,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;;;;aAChE,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,qCAAqC,EAAE;QAC9C,IAAA,YAAE,EAAC,uEAAuE,EAAE;;;;4BACzD,qBAAM,IAAA,8CAA0B,EAAC,sBAAsB,CAAC,EAAA;;wBAAnE,QAAQ,GAAG,SAAwD;wBAEzE,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;wBACzD,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACjE,IAAA,gBAAM,EAAC,QAAQ,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAG3D,eAAe,GAAG,QAAQ,CAAC,qBAAqB,CAAC,KAAK,CAAC,UAAA,UAAU;4BACrE,OAAA,CAAC,8BAA8B,EAAE,gCAAgC,EAAE,0BAA0B,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;wBAAnH,CAAmH,CACpH,CAAC;wBAEF,2EAA2E;wBAC3E,IAAA,gBAAM,EAAC,eAAe,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;;;aACrC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,kEAAkE,EAAE;;;;;wBAE/D,cAAc,GAAG,cAAI,CAAC,aAAa,CAAC,wCAAwC,CAAC,CAAC;wBACpF,cAAI,CAAC,MAAM,CAAC,wCAAwC,EAAE,cAAM,OAAA,CAAC;4BAC3D,4BAA4B,EAAE;gCAC5B,6BAA6B,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;6BAC7F;yBACF,CAAC,EAJ0D,CAI1D,CAAC,CAAC;wBAEa,qBAAM,IAAA,8CAA0B,EAAC,sBAAsB,CAAC,EAAA;;wBAAnE,QAAQ,GAAG,SAAwD;wBAEzE,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;wBACzD,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACjE,IAAA,gBAAM,EAAC,QAAQ,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAEjE,2DAA2D;wBAC3D,IAAA,gBAAM,EAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;;;;aACxD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,6BAA6B,EAAE;QACtC,IAAA,YAAE,EAAC,8DAA8D,EAAE;;;;;wBAC3D,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBAEL,qBAAM,2DAA4B,CAAC,6BAA6B,CACtF,sBAAsB,EACtB,YAAY,CACb,EAAA;;wBAHK,eAAe,GAAG,SAGvB;wBAEK,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBACrB,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;wBAErC,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,mCAAmC;wBACxE,IAAA,gBAAM,EAAC,eAAe,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;;;;aACnD,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,4CAA4C,EAAE;;;;;wBACzC,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;4BACvC,OAAA,2DAA4B,CAAC,6BAA6B,CACxD,sBAAsB,EACtB,YAAY,CACb;wBAHD,CAGC,CACF,CAAC;wBAEc,qBAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAA;;wBAArC,OAAO,GAAG,SAA2B;wBAE3C,OAAO,CAAC,OAAO,CAAC,UAAA,MAAM;4BACpB,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;4BACzC,IAAA,gBAAM,EAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC3C,CAAC,CAAC,CAAC;;;;aACJ,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,mDAAmD,EAAE;;;;4BACtC,qBAAM,2DAA4B,CAAC,6BAA6B,CAC9E,sBAAsB,EACtB,YAAY,CACb,EAAA;;wBAHK,OAAO,GAAG,SAGf;wBAEe,qBAAM,2DAA4B,CAAC,6BAA6B,CAC9E,sBAAsB,EACtB,YAAY,CACb,EAAA;;wBAHK,OAAO,GAAG,SAGf;wBAED,kDAAkD;wBAClD,IAAA,gBAAM,EAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;wBAC5C,IAAA,gBAAM,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;wBACpE,IAAA,gBAAM,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;;;;aAC3D,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/__tests__/assessment/enhanced-assessment-logic.test.ts"], "sourcesContent": ["/**\n * Enhanced Assessment Logic Test Suite\n * Tests the improved assessment scoring algorithms and career recommendation systems\n */\n\nimport { describe, it, expect, beforeAll, jest } from '@jest/globals';\nimport { AlgorithmicAssessmentService } from '../../lib/algorithmicAssessmentService';\nimport { EnhancedFallbackService } from '../../lib/enhancedFallbackService';\nimport { AssessmentServiceIntegration } from '../../lib/assessmentServiceIntegration';\nimport { generateAssessmentInsights, AssessmentResponse } from '../../lib/assessmentScoring';\n\n// Mock data for testing\nconst mockAssessmentResponse: AssessmentResponse = {\n  current_role: 'Software Developer',\n  years_experience: '3-5',\n  skill_development_interest: ['coding_tech', 'ai_ml', 'mobile_development'],\n  career_values: ['growth', 'autonomy', 'impact'],\n  work_style_preferences: ['remote', 'collaborative', 'flexible'],\n  biggest_obstacles: ['skill_gaps', 'time_constraints'],\n  financial_readiness: 4,\n  support_level: 3,\n  risk_tolerance: 3,\n  urgency_level: 4,\n  skills_confidence: 75,\n  desired_outcomes_work_life: 'Better work-life balance',\n  desired_outcomes_financial: 'Higher salary',\n  desired_outcomes_personal: 'Career growth',\n  location: 'San Francisco, CA'\n};\n\nconst mockInsights = {\n  scores: {\n    financialReadiness: 4,\n    supportLevel: 3,\n    riskTolerance: 3,\n    urgencyLevel: 4,\n    skillsConfidence: 75,\n    readinessScore: 70\n  },\n  primaryMotivation: 'Career advancement',\n  topSkills: ['technical_programming', 'data_analysis', 'project_management'],\n  biggestObstacles: ['skill_gaps', 'time_constraints'],\n  recommendedTimeline: '6-12 months',\n  keyRecommendations: ['Focus on AI/ML skills', 'Build portfolio projects'],\n  careerPathSuggestions: [],\n  careerPathAnalysis: [],\n  overallSkillGaps: [],\n  learningPriorities: ['technical_programming', 'data_analysis', 'project_management'],\n  estimatedTransitionTime: '6-12 months'\n};\n\ndescribe('Enhanced Assessment Logic', () => {\n  beforeAll(async () => {\n    // Initialize the assessment services\n    await AlgorithmicAssessmentService.initialize();\n    await AssessmentServiceIntegration.initialize();\n  });\n\n  describe('AlgorithmicAssessmentService', () => {\n    it('should generate career recommendations with algorithmic matching', async () => {\n      const recommendations = await AlgorithmicAssessmentService.generateCareerRecommendations(\n        mockAssessmentResponse,\n        mockInsights\n      );\n\n      expect(recommendations).toBeDefined();\n      expect(Array.isArray(recommendations)).toBe(true);\n      expect(recommendations.length).toBeGreaterThan(0);\n      \n      // Check structure of first recommendation\n      const firstRec = recommendations[0];\n      expect(firstRec).toHaveProperty('careerPath');\n      expect(firstRec).toHaveProperty('matchScore');\n      expect(firstRec).toHaveProperty('matchFactors');\n      expect(firstRec).toHaveProperty('confidenceLevel');\n      expect(firstRec).toHaveProperty('reasoning');\n      expect(firstRec).toHaveProperty('skillGaps');\n      expect(firstRec).toHaveProperty('estimatedTimeline');\n      expect(firstRec).toHaveProperty('successProbability');\n      \n      // Validate match score range\n      expect(firstRec.matchScore).toBeGreaterThanOrEqual(0);\n      expect(firstRec.matchScore).toBeLessThanOrEqual(100);\n      \n      // Validate confidence level\n      expect(firstRec.confidenceLevel).toBeGreaterThanOrEqual(0);\n      expect(firstRec.confidenceLevel).toBeLessThanOrEqual(100);\n      \n      // Validate success probability\n      expect(firstRec.successProbability).toBeGreaterThanOrEqual(0);\n      expect(firstRec.successProbability).toBeLessThanOrEqual(100);\n    });\n\n    it('should calculate skill alignment correctly', async () => {\n      const recommendations = await AlgorithmicAssessmentService.generateCareerRecommendations(\n        mockAssessmentResponse,\n        mockInsights\n      );\n\n      const techRecommendation = recommendations.find(rec => \n        rec.careerPath.name.toLowerCase().includes('developer') ||\n        rec.careerPath.name.toLowerCase().includes('engineer')\n      );\n\n      if (techRecommendation) {\n        expect(techRecommendation.matchFactors.skillAlignment).toBeGreaterThan(50);\n      }\n    });\n\n    it('should provide detailed skill gap analysis', async () => {\n      const recommendations = await AlgorithmicAssessmentService.generateCareerRecommendations(\n        mockAssessmentResponse,\n        mockInsights\n      );\n\n      const firstRec = recommendations[0];\n      expect(Array.isArray(firstRec.skillGaps)).toBe(true);\n      \n      if (firstRec.skillGaps.length > 0) {\n        const skillGap = firstRec.skillGaps[0];\n        expect(skillGap).toHaveProperty('skill');\n        expect(skillGap).toHaveProperty('currentLevel');\n        expect(skillGap).toHaveProperty('requiredLevel');\n        expect(skillGap).toHaveProperty('priority');\n        expect(skillGap).toHaveProperty('estimatedLearningTime');\n        \n        expect(['HIGH', 'MEDIUM', 'LOW']).toContain(skillGap.priority);\n      }\n    });\n\n    it('should generate meaningful reasoning', async () => {\n      const recommendations = await AlgorithmicAssessmentService.generateCareerRecommendations(\n        mockAssessmentResponse,\n        mockInsights\n      );\n\n      const firstRec = recommendations[0];\n      expect(Array.isArray(firstRec.reasoning)).toBe(true);\n      expect(firstRec.reasoning.length).toBeGreaterThan(0);\n      expect(firstRec.reasoning[0]).toBeTruthy();\n      expect(typeof firstRec.reasoning[0]).toBe('string');\n    });\n\n    it('should handle edge cases gracefully', async () => {\n      const emptyResponse: AssessmentResponse = {\n        current_role: '',\n        years_experience: '',\n        skill_development_interest: [],\n        career_values: [],\n        work_style_preferences: [],\n        biggest_obstacles: [],\n        financial_readiness: 1,\n        support_level: 1,\n        risk_tolerance: 1,\n        urgency_level: 1,\n        skills_confidence: 10,\n        desired_outcomes_work_life: '',\n        desired_outcomes_financial: '',\n        desired_outcomes_personal: '',\n        location: ''\n      };\n\n      const emptyInsights = {\n        ...mockInsights,\n        topSkills: [],\n        scores: {\n          financialReadiness: 1,\n          supportLevel: 1,\n          riskTolerance: 1,\n          urgencyLevel: 1,\n          skillsConfidence: 10,\n          readinessScore: 20\n        }\n      };\n\n      const recommendations = await AlgorithmicAssessmentService.generateCareerRecommendations(\n        emptyResponse,\n        emptyInsights\n      );\n\n      expect(recommendations).toBeDefined();\n      expect(Array.isArray(recommendations)).toBe(true);\n      // Should still provide some recommendations even with minimal data\n    });\n  });\n\n  describe('EnhancedFallbackService', () => {\n    it('should generate comprehensive fallback insights', async () => {\n      const fallbackInsights = await EnhancedFallbackService.generateFallbackInsights(\n        mockAssessmentResponse,\n        mockInsights,\n        'Testing fallback service'\n      );\n\n      expect(fallbackInsights).toBeDefined();\n      expect(fallbackInsights).toHaveProperty('careerRecommendations');\n      expect(fallbackInsights).toHaveProperty('skillGapAnalysis');\n      expect(fallbackInsights).toHaveProperty('learningPathRecommendations');\n      expect(fallbackInsights).toHaveProperty('marketInsights');\n      expect(fallbackInsights).toHaveProperty('personalizedAdvice');\n      expect(fallbackInsights).toHaveProperty('confidenceScore');\n      expect(fallbackInsights).toHaveProperty('fallbackReason');\n      expect(fallbackInsights).toHaveProperty('generatedAt');\n    });\n\n    it('should provide quality career recommendations in fallback mode', async () => {\n      const fallbackInsights = await EnhancedFallbackService.generateFallbackInsights(\n        mockAssessmentResponse,\n        mockInsights\n      );\n\n      const recommendations = fallbackInsights.careerRecommendations;\n      expect(Array.isArray(recommendations)).toBe(true);\n      expect(recommendations.length).toBeGreaterThan(0);\n      \n      const firstRec = recommendations[0];\n      expect(firstRec).toHaveProperty('careerPath');\n      expect(firstRec).toHaveProperty('matchPercentage');\n      expect(firstRec).toHaveProperty('reasoning');\n      expect(firstRec).toHaveProperty('salaryRange');\n      expect(firstRec).toHaveProperty('marketOutlook');\n      expect(firstRec).toHaveProperty('transitionDifficulty');\n      expect(firstRec).toHaveProperty('timeToTransition');\n      expect(firstRec).toHaveProperty('keySkillsNeeded');\n      expect(firstRec).toHaveProperty('successFactors');\n      expect(firstRec).toHaveProperty('potentialChallenges');\n      \n      expect(['LOW', 'MEDIUM', 'HIGH']).toContain(firstRec.transitionDifficulty);\n    });\n\n    it('should provide actionable skill gap analysis', async () => {\n      const fallbackInsights = await EnhancedFallbackService.generateFallbackInsights(\n        mockAssessmentResponse,\n        mockInsights\n      );\n\n      const skillGaps = fallbackInsights.skillGapAnalysis;\n      expect(Array.isArray(skillGaps)).toBe(true);\n      \n      if (skillGaps.length > 0) {\n        const skillGap = skillGaps[0];\n        expect(skillGap).toHaveProperty('skill');\n        expect(skillGap).toHaveProperty('currentLevel');\n        expect(skillGap).toHaveProperty('targetLevel');\n        expect(skillGap).toHaveProperty('priority');\n        expect(skillGap).toHaveProperty('learningTime');\n        expect(skillGap).toHaveProperty('recommendedApproach');\n        expect(skillGap).toHaveProperty('marketValue');\n        \n        expect(['HIGH', 'MEDIUM', 'LOW']).toContain(skillGap.priority);\n        expect(skillGap.marketValue).toBeGreaterThanOrEqual(1);\n        expect(skillGap.marketValue).toBeLessThanOrEqual(10);\n      }\n    });\n\n    it('should generate personalized advice', async () => {\n      const fallbackInsights = await EnhancedFallbackService.generateFallbackInsights(\n        mockAssessmentResponse,\n        mockInsights\n      );\n\n      const advice = fallbackInsights.personalizedAdvice;\n      expect(Array.isArray(advice)).toBe(true);\n      expect(advice.length).toBeGreaterThan(0);\n      \n      advice.forEach(adviceItem => {\n        expect(typeof adviceItem).toBe('string');\n        expect(adviceItem.length).toBeGreaterThan(10); // Should be meaningful advice\n      });\n    });\n\n    it('should provide market insights', async () => {\n      const fallbackInsights = await EnhancedFallbackService.generateFallbackInsights(\n        mockAssessmentResponse,\n        mockInsights\n      );\n\n      const marketInsights = fallbackInsights.marketInsights;\n      expect(marketInsights).toHaveProperty('industryTrends');\n      expect(marketInsights).toHaveProperty('emergingSkills');\n      expect(marketInsights).toHaveProperty('salaryTrends');\n      expect(marketInsights).toHaveProperty('jobMarketHealth');\n      expect(marketInsights).toHaveProperty('geographicOpportunities');\n      expect(marketInsights).toHaveProperty('automationImpact');\n      \n      expect(['EXCELLENT', 'GOOD', 'FAIR', 'CHALLENGING']).toContain(marketInsights.jobMarketHealth);\n      expect(Array.isArray(marketInsights.industryTrends)).toBe(true);\n      expect(Array.isArray(marketInsights.emergingSkills)).toBe(true);\n      expect(Array.isArray(marketInsights.geographicOpportunities)).toBe(true);\n    });\n\n    it('should calculate appropriate confidence scores', async () => {\n      const fallbackInsights = await EnhancedFallbackService.generateFallbackInsights(\n        mockAssessmentResponse,\n        mockInsights\n      );\n\n      expect(fallbackInsights.confidenceScore).toBeGreaterThanOrEqual(60);\n      expect(fallbackInsights.confidenceScore).toBeLessThanOrEqual(95);\n    });\n  });\n\n  describe('AssessmentServiceIntegration', () => {\n    it('should provide integrated career recommendations', async () => {\n      const result = await AssessmentServiceIntegration.generateCareerRecommendations(\n        mockAssessmentResponse,\n        mockInsights\n      );\n\n      expect(result).toBeDefined();\n      expect(result).toHaveProperty('careerRecommendations');\n      expect(result).toHaveProperty('serviceUsed');\n      expect(result).toHaveProperty('confidence');\n      expect(result).toHaveProperty('processingTime');\n\n      expect(Array.isArray(result.careerRecommendations)).toBe(true);\n      expect(result.careerRecommendations.length).toBeGreaterThan(0);\n      expect(['algorithmic', 'fallback', 'basic']).toContain(result.serviceUsed);\n      expect(result.confidence).toBeGreaterThanOrEqual(0);\n      expect(result.confidence).toBeLessThanOrEqual(100);\n      expect(result.processingTime).toBeGreaterThan(0);\n    });\n\n    it('should perform health checks correctly', async () => {\n      const healthCheck = await AssessmentServiceIntegration.healthCheck();\n\n      expect(healthCheck).toBeDefined();\n      expect(healthCheck).toHaveProperty('algorithmic');\n      expect(healthCheck).toHaveProperty('fallback');\n      expect(healthCheck).toHaveProperty('basic');\n      expect(healthCheck).toHaveProperty('overall');\n\n      expect(typeof healthCheck.algorithmic).toBe('boolean');\n      expect(typeof healthCheck.fallback).toBe('boolean');\n      expect(typeof healthCheck.basic).toBe('boolean');\n      expect(['healthy', 'degraded', 'critical']).toContain(healthCheck.overall);\n    });\n\n    it('should handle service failures gracefully', async () => {\n      // Test with invalid data to trigger fallbacks\n      const invalidResponse: AssessmentResponse = {\n        current_role: null as any,\n        years_experience: null as any,\n        skill_development_interest: null as any,\n        career_values: null as any,\n        work_style_preferences: null as any,\n        biggest_obstacles: null as any,\n        financial_readiness: null as any,\n        support_level: null as any,\n        risk_tolerance: null as any,\n        urgency_level: null as any,\n        skills_confidence: null as any,\n        desired_outcomes_work_life: null as any,\n        desired_outcomes_financial: null as any,\n        desired_outcomes_personal: null as any,\n        location: null as any\n      };\n\n      const result = await AssessmentServiceIntegration.generateCareerRecommendations(\n        invalidResponse,\n        mockInsights\n      );\n\n      // Should still provide some recommendations even with invalid data\n      expect(result).toBeDefined();\n      expect(Array.isArray(result.careerRecommendations)).toBe(true);\n      expect(result.careerRecommendations.length).toBeGreaterThan(0);\n    });\n  });\n\n  describe('Integration with Assessment Scoring', () => {\n    it('should integrate algorithmic recommendations with assessment insights', async () => {\n      const insights = await generateAssessmentInsights(mockAssessmentResponse);\n      \n      expect(insights).toHaveProperty('careerPathSuggestions');\n      expect(Array.isArray(insights.careerPathSuggestions)).toBe(true);\n      expect(insights.careerPathSuggestions.length).toBeGreaterThan(0);\n      \n      // Should not contain hardcoded default suggestions when algorithmic service works\n      const hasOnlyDefaults = insights.careerPathSuggestions.every(suggestion =>\n        ['Digital Marketing Specialist', 'Entrepreneur / Startup Founder', 'Full-Stack Web Developer'].includes(suggestion)\n      );\n      \n      // With proper algorithmic matching, we should get more diverse suggestions\n      expect(hasOnlyDefaults).toBe(false);\n    });\n\n    it('should handle fallback gracefully when algorithmic service fails', async () => {\n      // Mock the algorithmic service to throw an error\n      const originalImport = jest.requireActual('../../lib/algorithmicAssessmentService');\n      jest.doMock('../../lib/algorithmicAssessmentService', () => ({\n        AlgorithmicAssessmentService: {\n          generateCareerRecommendations: jest.fn().mockRejectedValue(new Error('Service unavailable'))\n        }\n      }));\n\n      const insights = await generateAssessmentInsights(mockAssessmentResponse);\n      \n      expect(insights).toHaveProperty('careerPathSuggestions');\n      expect(Array.isArray(insights.careerPathSuggestions)).toBe(true);\n      expect(insights.careerPathSuggestions.length).toBeGreaterThan(0);\n      \n      // Should still provide meaningful suggestions via fallback\n      expect(insights.careerPathSuggestions).not.toEqual([]);\n    });\n  });\n\n  describe('Performance and Reliability', () => {\n    it('should complete assessment generation within reasonable time', async () => {\n      const startTime = Date.now();\n      \n      const recommendations = await AlgorithmicAssessmentService.generateCareerRecommendations(\n        mockAssessmentResponse,\n        mockInsights\n      );\n      \n      const endTime = Date.now();\n      const duration = endTime - startTime;\n      \n      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds\n      expect(recommendations.length).toBeGreaterThan(0);\n    });\n\n    it('should handle concurrent requests properly', async () => {\n      const promises = Array(5).fill(null).map(() =>\n        AlgorithmicAssessmentService.generateCareerRecommendations(\n          mockAssessmentResponse,\n          mockInsights\n        )\n      );\n\n      const results = await Promise.all(promises);\n      \n      results.forEach(result => {\n        expect(Array.isArray(result)).toBe(true);\n        expect(result.length).toBeGreaterThan(0);\n      });\n    });\n\n    it('should maintain consistency across multiple calls', async () => {\n      const result1 = await AlgorithmicAssessmentService.generateCareerRecommendations(\n        mockAssessmentResponse,\n        mockInsights\n      );\n      \n      const result2 = await AlgorithmicAssessmentService.generateCareerRecommendations(\n        mockAssessmentResponse,\n        mockInsights\n      );\n\n      // Results should be consistent for the same input\n      expect(result1.length).toBe(result2.length);\n      expect(result1[0].careerPath.name).toBe(result2[0].careerPath.name);\n      expect(result1[0].matchScore).toBe(result2[0].matchScore);\n    });\n  });\n});\n"], "version": 3}