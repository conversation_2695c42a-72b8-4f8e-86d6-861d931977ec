c3cd1bd13edd546fcf3dff6722788e49
"use strict";

// Assessment scoring and insights generation
/* istanbul ignore next */
function cov_25mt5yuw2j() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/assessmentScoring.ts";
  var hash = "83beed3e8c9d03a8c4d12eeaa27bbcef989a12ce";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/assessmentScoring.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 16
        },
        end: {
          line: 11,
          column: 1
        }
      },
      "1": {
        start: {
          line: 4,
          column: 28
        },
        end: {
          line: 4,
          column: 110
        }
      },
      "2": {
        start: {
          line: 4,
          column: 91
        },
        end: {
          line: 4,
          column: 106
        }
      },
      "3": {
        start: {
          line: 5,
          column: 4
        },
        end: {
          line: 10,
          column: 7
        }
      },
      "4": {
        start: {
          line: 6,
          column: 36
        },
        end: {
          line: 6,
          column: 97
        }
      },
      "5": {
        start: {
          line: 6,
          column: 42
        },
        end: {
          line: 6,
          column: 70
        }
      },
      "6": {
        start: {
          line: 6,
          column: 85
        },
        end: {
          line: 6,
          column: 95
        }
      },
      "7": {
        start: {
          line: 7,
          column: 35
        },
        end: {
          line: 7,
          column: 100
        }
      },
      "8": {
        start: {
          line: 7,
          column: 41
        },
        end: {
          line: 7,
          column: 73
        }
      },
      "9": {
        start: {
          line: 7,
          column: 88
        },
        end: {
          line: 7,
          column: 98
        }
      },
      "10": {
        start: {
          line: 8,
          column: 32
        },
        end: {
          line: 8,
          column: 116
        }
      },
      "11": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 78
        }
      },
      "12": {
        start: {
          line: 12,
          column: 18
        },
        end: {
          line: 38,
          column: 1
        }
      },
      "13": {
        start: {
          line: 13,
          column: 12
        },
        end: {
          line: 13,
          column: 104
        }
      },
      "14": {
        start: {
          line: 13,
          column: 43
        },
        end: {
          line: 13,
          column: 68
        }
      },
      "15": {
        start: {
          line: 13,
          column: 57
        },
        end: {
          line: 13,
          column: 68
        }
      },
      "16": {
        start: {
          line: 13,
          column: 69
        },
        end: {
          line: 13,
          column: 81
        }
      },
      "17": {
        start: {
          line: 13,
          column: 119
        },
        end: {
          line: 13,
          column: 196
        }
      },
      "18": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 14,
          column: 160
        }
      },
      "19": {
        start: {
          line: 14,
          column: 141
        },
        end: {
          line: 14,
          column: 153
        }
      },
      "20": {
        start: {
          line: 15,
          column: 23
        },
        end: {
          line: 15,
          column: 68
        }
      },
      "21": {
        start: {
          line: 15,
          column: 45
        },
        end: {
          line: 15,
          column: 65
        }
      },
      "22": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 17,
          column: 70
        }
      },
      "23": {
        start: {
          line: 17,
          column: 15
        },
        end: {
          line: 17,
          column: 70
        }
      },
      "24": {
        start: {
          line: 18,
          column: 8
        },
        end: {
          line: 35,
          column: 66
        }
      },
      "25": {
        start: {
          line: 18,
          column: 50
        },
        end: {
          line: 35,
          column: 66
        }
      },
      "26": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 169
        }
      },
      "27": {
        start: {
          line: 19,
          column: 160
        },
        end: {
          line: 19,
          column: 169
        }
      },
      "28": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 20,
          column: 52
        }
      },
      "29": {
        start: {
          line: 20,
          column: 26
        },
        end: {
          line: 20,
          column: 52
        }
      },
      "30": {
        start: {
          line: 21,
          column: 12
        },
        end: {
          line: 33,
          column: 13
        }
      },
      "31": {
        start: {
          line: 22,
          column: 32
        },
        end: {
          line: 22,
          column: 39
        }
      },
      "32": {
        start: {
          line: 22,
          column: 40
        },
        end: {
          line: 22,
          column: 46
        }
      },
      "33": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "34": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 72
        }
      },
      "35": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 34
        }
      },
      "36": {
        start: {
          line: 24,
          column: 35
        },
        end: {
          line: 24,
          column: 45
        }
      },
      "37": {
        start: {
          line: 24,
          column: 46
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "38": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "39": {
        start: {
          line: 25,
          column: 24
        },
        end: {
          line: 25,
          column: 41
        }
      },
      "40": {
        start: {
          line: 25,
          column: 42
        },
        end: {
          line: 25,
          column: 55
        }
      },
      "41": {
        start: {
          line: 25,
          column: 56
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "42": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 128
        }
      },
      "43": {
        start: {
          line: 27,
          column: 110
        },
        end: {
          line: 27,
          column: 116
        }
      },
      "44": {
        start: {
          line: 27,
          column: 117
        },
        end: {
          line: 27,
          column: 126
        }
      },
      "45": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 106
        }
      },
      "46": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 97
        }
      },
      "47": {
        start: {
          line: 28,
          column: 98
        },
        end: {
          line: 28,
          column: 104
        }
      },
      "48": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 89
        }
      },
      "49": {
        start: {
          line: 29,
          column: 57
        },
        end: {
          line: 29,
          column: 72
        }
      },
      "50": {
        start: {
          line: 29,
          column: 73
        },
        end: {
          line: 29,
          column: 80
        }
      },
      "51": {
        start: {
          line: 29,
          column: 81
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "52": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 87
        }
      },
      "53": {
        start: {
          line: 30,
          column: 47
        },
        end: {
          line: 30,
          column: 62
        }
      },
      "54": {
        start: {
          line: 30,
          column: 63
        },
        end: {
          line: 30,
          column: 78
        }
      },
      "55": {
        start: {
          line: 30,
          column: 79
        },
        end: {
          line: 30,
          column: 85
        }
      },
      "56": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 42
        }
      },
      "57": {
        start: {
          line: 31,
          column: 30
        },
        end: {
          line: 31,
          column: 42
        }
      },
      "58": {
        start: {
          line: 32,
          column: 20
        },
        end: {
          line: 32,
          column: 33
        }
      },
      "59": {
        start: {
          line: 32,
          column: 34
        },
        end: {
          line: 32,
          column: 43
        }
      },
      "60": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 39
        }
      },
      "61": {
        start: {
          line: 35,
          column: 22
        },
        end: {
          line: 35,
          column: 34
        }
      },
      "62": {
        start: {
          line: 35,
          column: 35
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "63": {
        start: {
          line: 35,
          column: 54
        },
        end: {
          line: 35,
          column: 64
        }
      },
      "64": {
        start: {
          line: 36,
          column: 8
        },
        end: {
          line: 36,
          column: 35
        }
      },
      "65": {
        start: {
          line: 36,
          column: 23
        },
        end: {
          line: 36,
          column: 35
        }
      },
      "66": {
        start: {
          line: 36,
          column: 36
        },
        end: {
          line: 36,
          column: 89
        }
      },
      "67": {
        start: {
          line: 39,
          column: 0
        },
        end: {
          line: 39,
          column: 62
        }
      },
      "68": {
        start: {
          line: 40,
          column: 0
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "69": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 64
        }
      },
      "70": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 46
        }
      },
      "71": {
        start: {
          line: 43,
          column: 0
        },
        end: {
          line: 43,
          column: 46
        }
      },
      "72": {
        start: {
          line: 45,
          column: 20
        },
        end: {
          line: 53,
          column: 1
        }
      },
      "73": {
        start: {
          line: 46,
          column: 4
        },
        end: {
          line: 48,
          column: 5
        }
      },
      "74": {
        start: {
          line: 47,
          column: 8
        },
        end: {
          line: 47,
          column: 76
        }
      },
      "75": {
        start: {
          line: 47,
          column: 43
        },
        end: {
          line: 47,
          column: 72
        }
      },
      "76": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 51,
          column: 5
        }
      },
      "77": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 50,
          column: 23
        }
      },
      "78": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 52,
          column: 14
        }
      },
      "79": {
        start: {
          line: 55,
          column: 21
        },
        end: {
          line: 63,
          column: 1
        }
      },
      "80": {
        start: {
          line: 56,
          column: 4
        },
        end: {
          line: 58,
          column: 5
        }
      },
      "81": {
        start: {
          line: 57,
          column: 8
        },
        end: {
          line: 57,
          column: 21
        }
      },
      "82": {
        start: {
          line: 59,
          column: 4
        },
        end: {
          line: 61,
          column: 5
        }
      },
      "83": {
        start: {
          line: 60,
          column: 8
        },
        end: {
          line: 60,
          column: 32
        }
      },
      "84": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 62,
          column: 14
        }
      },
      "85": {
        start: {
          line: 65,
          column: 21
        },
        end: {
          line: 74,
          column: 1
        }
      },
      "86": {
        start: {
          line: 66,
          column: 4
        },
        end: {
          line: 68,
          column: 5
        }
      },
      "87": {
        start: {
          line: 67,
          column: 8
        },
        end: {
          line: 67,
          column: 21
        }
      },
      "88": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 72,
          column: 5
        }
      },
      "89": {
        start: {
          line: 70,
          column: 21
        },
        end: {
          line: 70,
          column: 38
        }
      },
      "90": {
        start: {
          line: 71,
          column: 8
        },
        end: {
          line: 71,
          column: 42
        }
      },
      "91": {
        start: {
          line: 73,
          column: 4
        },
        end: {
          line: 73,
          column: 13
        }
      },
      "92": {
        start: {
          line: 77,
          column: 29
        },
        end: {
          line: 77,
          column: 77
        }
      },
      "93": {
        start: {
          line: 79,
          column: 24
        },
        end: {
          line: 79,
          column: 69
        }
      },
      "94": {
        start: {
          line: 81,
          column: 23
        },
        end: {
          line: 81,
          column: 68
        }
      },
      "95": {
        start: {
          line: 83,
          column: 26
        },
        end: {
          line: 83,
          column: 73
        }
      },
      "96": {
        start: {
          line: 85,
          column: 19
        },
        end: {
          line: 85,
          column: 64
        }
      },
      "97": {
        start: {
          line: 86,
          column: 23
        },
        end: {
          line: 86,
          column: 24
        }
      },
      "98": {
        start: {
          line: 87,
          column: 4
        },
        end: {
          line: 104,
          column: 5
        }
      },
      "99": {
        start: {
          line: 89,
          column: 12
        },
        end: {
          line: 89,
          column: 29
        }
      },
      "100": {
        start: {
          line: 90,
          column: 12
        },
        end: {
          line: 90,
          column: 18
        }
      },
      "101": {
        start: {
          line: 92,
          column: 12
        },
        end: {
          line: 92,
          column: 29
        }
      },
      "102": {
        start: {
          line: 93,
          column: 12
        },
        end: {
          line: 93,
          column: 18
        }
      },
      "103": {
        start: {
          line: 95,
          column: 12
        },
        end: {
          line: 95,
          column: 29
        }
      },
      "104": {
        start: {
          line: 96,
          column: 12
        },
        end: {
          line: 96,
          column: 18
        }
      },
      "105": {
        start: {
          line: 98,
          column: 12
        },
        end: {
          line: 98,
          column: 29
        }
      },
      "106": {
        start: {
          line: 99,
          column: 12
        },
        end: {
          line: 99,
          column: 18
        }
      },
      "107": {
        start: {
          line: 102,
          column: 12
        },
        end: {
          line: 102,
          column: 29
        }
      },
      "108": {
        start: {
          line: 103,
          column: 12
        },
        end: {
          line: 103,
          column: 18
        }
      },
      "109": {
        start: {
          line: 106,
          column: 20
        },
        end: {
          line: 106,
          column: 55
        }
      },
      "110": {
        start: {
          line: 107,
          column: 27
        },
        end: {
          line: 107,
          column: 63
        }
      },
      "111": {
        start: {
          line: 109,
          column: 25
        },
        end: {
          line: 114,
          column: 5
        }
      },
      "112": {
        start: {
          line: 115,
          column: 4
        },
        end: {
          line: 122,
          column: 6
        }
      },
      "113": {
        start: {
          line: 125,
          column: 4
        },
        end: {
          line: 216,
          column: 7
        }
      },
      "114": {
        start: {
          line: 127,
          column: 8
        },
        end: {
          line: 215,
          column: 11
        }
      },
      "115": {
        start: {
          line: 128,
          column: 12
        },
        end: {
          line: 214,
          column: 13
        }
      },
      "116": {
        start: {
          line: 130,
          column: 20
        },
        end: {
          line: 130,
          column: 66
        }
      },
      "117": {
        start: {
          line: 131,
          column: 20
        },
        end: {
          line: 131,
          column: 91
        }
      },
      "118": {
        start: {
          line: 132,
          column: 20
        },
        end: {
          line: 132,
          column: 68
        }
      },
      "119": {
        start: {
          line: 133,
          column: 20
        },
        end: {
          line: 133,
          column: 82
        }
      },
      "120": {
        start: {
          line: 134,
          column: 20
        },
        end: {
          line: 134,
          column: 77
        }
      },
      "121": {
        start: {
          line: 135,
          column: 20
        },
        end: {
          line: 135,
          column: 72
        }
      },
      "122": {
        start: {
          line: 136,
          column: 20
        },
        end: {
          line: 147,
          column: 21
        }
      },
      "123": {
        start: {
          line: 137,
          column: 24
        },
        end: {
          line: 137,
          column: 86
        }
      },
      "124": {
        start: {
          line: 139,
          column: 25
        },
        end: {
          line: 147,
          column: 21
        }
      },
      "125": {
        start: {
          line: 140,
          column: 24
        },
        end: {
          line: 140,
          column: 88
        }
      },
      "126": {
        start: {
          line: 142,
          column: 25
        },
        end: {
          line: 147,
          column: 21
        }
      },
      "127": {
        start: {
          line: 143,
          column: 24
        },
        end: {
          line: 143,
          column: 84
        }
      },
      "128": {
        start: {
          line: 146,
          column: 24
        },
        end: {
          line: 146,
          column: 92
        }
      },
      "129": {
        start: {
          line: 148,
          column: 20
        },
        end: {
          line: 148,
          column: 44
        }
      },
      "130": {
        start: {
          line: 149,
          column: 20
        },
        end: {
          line: 151,
          column: 21
        }
      },
      "131": {
        start: {
          line: 150,
          column: 24
        },
        end: {
          line: 150,
          column: 106
        }
      },
      "132": {
        start: {
          line: 152,
          column: 20
        },
        end: {
          line: 154,
          column: 21
        }
      },
      "133": {
        start: {
          line: 153,
          column: 24
        },
        end: {
          line: 153,
          column: 105
        }
      },
      "134": {
        start: {
          line: 155,
          column: 20
        },
        end: {
          line: 157,
          column: 21
        }
      },
      "135": {
        start: {
          line: 156,
          column: 24
        },
        end: {
          line: 156,
          column: 102
        }
      },
      "136": {
        start: {
          line: 158,
          column: 20
        },
        end: {
          line: 160,
          column: 21
        }
      },
      "137": {
        start: {
          line: 159,
          column: 24
        },
        end: {
          line: 159,
          column: 101
        }
      },
      "138": {
        start: {
          line: 161,
          column: 20
        },
        end: {
          line: 163,
          column: 21
        }
      },
      "139": {
        start: {
          line: 162,
          column: 24
        },
        end: {
          line: 162,
          column: 102
        }
      },
      "140": {
        start: {
          line: 164,
          column: 20
        },
        end: {
          line: 164,
          column: 47
        }
      },
      "141": {
        start: {
          line: 165,
          column: 20
        },
        end: {
          line: 165,
          column: 33
        }
      },
      "142": {
        start: {
          line: 167,
          column: 20
        },
        end: {
          line: 167,
          column: 46
        }
      },
      "143": {
        start: {
          line: 168,
          column: 20
        },
        end: {
          line: 168,
          column: 122
        }
      },
      "144": {
        start: {
          line: 169,
          column: 20
        },
        end: {
          line: 181,
          column: 28
        }
      },
      "145": {
        start: {
          line: 183,
          column: 20
        },
        end: {
          line: 183,
          column: 39
        }
      },
      "146": {
        start: {
          line: 184,
          column: 20
        },
        end: {
          line: 184,
          column: 73
        }
      },
      "147": {
        start: {
          line: 185,
          column: 20
        },
        end: {
          line: 185,
          column: 44
        }
      },
      "148": {
        start: {
          line: 187,
          column: 20
        },
        end: {
          line: 187,
          column: 40
        }
      },
      "149": {
        start: {
          line: 188,
          column: 20
        },
        end: {
          line: 188,
          column: 105
        }
      },
      "150": {
        start: {
          line: 189,
          column: 20
        },
        end: {
          line: 189,
          column: 100
        }
      },
      "151": {
        start: {
          line: 190,
          column: 20
        },
        end: {
          line: 190,
          column: 44
        }
      },
      "152": {
        start: {
          line: 193,
          column: 20
        },
        end: {
          line: 195,
          column: 21
        }
      },
      "153": {
        start: {
          line: 194,
          column: 24
        },
        end: {
          line: 194,
          column: 113
        }
      },
      "154": {
        start: {
          line: 197,
          column: 20
        },
        end: {
          line: 199,
          column: 21
        }
      },
      "155": {
        start: {
          line: 198,
          column: 24
        },
        end: {
          line: 198,
          column: 143
        }
      },
      "156": {
        start: {
          line: 200,
          column: 20
        },
        end: {
          line: 213,
          column: 27
        }
      },
      "157": {
        start: {
          line: 220,
          column: 4
        },
        end: {
          line: 221,
          column: 32
        }
      },
      "158": {
        start: {
          line: 221,
          column: 8
        },
        end: {
          line: 221,
          column: 32
        }
      },
      "159": {
        start: {
          line: 222,
          column: 4
        },
        end: {
          line: 223,
          column: 36
        }
      },
      "160": {
        start: {
          line: 223,
          column: 8
        },
        end: {
          line: 223,
          column: 36
        }
      },
      "161": {
        start: {
          line: 224,
          column: 4
        },
        end: {
          line: 225,
          column: 36
        }
      },
      "162": {
        start: {
          line: 225,
          column: 8
        },
        end: {
          line: 225,
          column: 36
        }
      },
      "163": {
        start: {
          line: 226,
          column: 4
        },
        end: {
          line: 226,
          column: 31
        }
      },
      "164": {
        start: {
          line: 230,
          column: 4
        },
        end: {
          line: 231,
          column: 52
        }
      },
      "165": {
        start: {
          line: 231,
          column: 8
        },
        end: {
          line: 231,
          column: 52
        }
      },
      "166": {
        start: {
          line: 232,
          column: 4
        },
        end: {
          line: 233,
          column: 54
        }
      },
      "167": {
        start: {
          line: 233,
          column: 8
        },
        end: {
          line: 233,
          column: 54
        }
      },
      "168": {
        start: {
          line: 234,
          column: 4
        },
        end: {
          line: 235,
          column: 54
        }
      },
      "169": {
        start: {
          line: 235,
          column: 8
        },
        end: {
          line: 235,
          column: 54
        }
      },
      "170": {
        start: {
          line: 236,
          column: 4
        },
        end: {
          line: 236,
          column: 44
        }
      },
      "171": {
        start: {
          line: 242,
          column: 22
        },
        end: {
          line: 242,
          column: 24
        }
      },
      "172": {
        start: {
          line: 243,
          column: 36
        },
        end: {
          line: 243,
          column: 87
        }
      },
      "173": {
        start: {
          line: 244,
          column: 23
        },
        end: {
          line: 244,
          column: 61
        }
      },
      "174": {
        start: {
          line: 246,
          column: 4
        },
        end: {
          line: 254,
          column: 5
        }
      },
      "175": {
        start: {
          line: 247,
          column: 8
        },
        end: {
          line: 247,
          column: 77
        }
      },
      "176": {
        start: {
          line: 248,
          column: 8
        },
        end: {
          line: 250,
          column: 9
        }
      },
      "177": {
        start: {
          line: 249,
          column: 12
        },
        end: {
          line: 249,
          column: 65
        }
      },
      "178": {
        start: {
          line: 251,
          column: 8
        },
        end: {
          line: 253,
          column: 9
        }
      },
      "179": {
        start: {
          line: 252,
          column: 12
        },
        end: {
          line: 252,
          column: 57
        }
      },
      "180": {
        start: {
          line: 256,
          column: 4
        },
        end: {
          line: 261,
          column: 5
        }
      },
      "181": {
        start: {
          line: 257,
          column: 8
        },
        end: {
          line: 257,
          column: 62
        }
      },
      "182": {
        start: {
          line: 258,
          column: 8
        },
        end: {
          line: 260,
          column: 9
        }
      },
      "183": {
        start: {
          line: 259,
          column: 12
        },
        end: {
          line: 259,
          column: 61
        }
      },
      "184": {
        start: {
          line: 263,
          column: 4
        },
        end: {
          line: 268,
          column: 5
        }
      },
      "185": {
        start: {
          line: 264,
          column: 8
        },
        end: {
          line: 264,
          column: 76
        }
      },
      "186": {
        start: {
          line: 265,
          column: 8
        },
        end: {
          line: 267,
          column: 9
        }
      },
      "187": {
        start: {
          line: 266,
          column: 12
        },
        end: {
          line: 266,
          column: 63
        }
      },
      "188": {
        start: {
          line: 270,
          column: 4
        },
        end: {
          line: 272,
          column: 5
        }
      },
      "189": {
        start: {
          line: 271,
          column: 8
        },
        end: {
          line: 271,
          column: 86
        }
      },
      "190": {
        start: {
          line: 274,
          column: 4
        },
        end: {
          line: 279,
          column: 5
        }
      },
      "191": {
        start: {
          line: 275,
          column: 8
        },
        end: {
          line: 275,
          column: 63
        }
      },
      "192": {
        start: {
          line: 276,
          column: 8
        },
        end: {
          line: 278,
          column: 9
        }
      },
      "193": {
        start: {
          line: 277,
          column: 12
        },
        end: {
          line: 277,
          column: 60
        }
      },
      "194": {
        start: {
          line: 281,
          column: 4
        },
        end: {
          line: 283,
          column: 5
        }
      },
      "195": {
        start: {
          line: 282,
          column: 8
        },
        end: {
          line: 282,
          column: 79
        }
      },
      "196": {
        start: {
          line: 285,
          column: 28
        },
        end: {
          line: 285,
          column: 60
        }
      },
      "197": {
        start: {
          line: 287,
          column: 4
        },
        end: {
          line: 295,
          column: 5
        }
      },
      "198": {
        start: {
          line: 288,
          column: 8
        },
        end: {
          line: 294,
          column: 10
        }
      },
      "199": {
        start: {
          line: 296,
          column: 4
        },
        end: {
          line: 296,
          column: 41
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 3,
            column: 45
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 11,
            column: 1
          }
        },
        line: 3
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 4,
            column: 13
          },
          end: {
            line: 4,
            column: 18
          }
        },
        loc: {
          start: {
            line: 4,
            column: 26
          },
          end: {
            line: 4,
            column: 112
          }
        },
        line: 4
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 4,
            column: 70
          },
          end: {
            line: 4,
            column: 71
          }
        },
        loc: {
          start: {
            line: 4,
            column: 89
          },
          end: {
            line: 4,
            column: 108
          }
        },
        line: 4
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 5,
            column: 36
          },
          end: {
            line: 5,
            column: 37
          }
        },
        loc: {
          start: {
            line: 5,
            column: 63
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 5
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 26
          }
        },
        loc: {
          start: {
            line: 6,
            column: 34
          },
          end: {
            line: 6,
            column: 99
          }
        },
        line: 6
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 25
          }
        },
        loc: {
          start: {
            line: 7,
            column: 33
          },
          end: {
            line: 7,
            column: 102
          }
        },
        line: 7
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 8,
            column: 17
          },
          end: {
            line: 8,
            column: 21
          }
        },
        loc: {
          start: {
            line: 8,
            column: 30
          },
          end: {
            line: 8,
            column: 118
          }
        },
        line: 8
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 12,
            column: 48
          },
          end: {
            line: 12,
            column: 49
          }
        },
        loc: {
          start: {
            line: 12,
            column: 73
          },
          end: {
            line: 38,
            column: 1
          }
        },
        line: 12
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 13,
            column: 30
          },
          end: {
            line: 13,
            column: 31
          }
        },
        loc: {
          start: {
            line: 13,
            column: 41
          },
          end: {
            line: 13,
            column: 83
          }
        },
        line: 13
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 14,
            column: 128
          },
          end: {
            line: 14,
            column: 129
          }
        },
        loc: {
          start: {
            line: 14,
            column: 139
          },
          end: {
            line: 14,
            column: 155
          }
        },
        line: 14
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 21
          },
          end: {
            line: 15,
            column: 70
          }
        },
        line: 15
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 15,
            column: 30
          },
          end: {
            line: 15,
            column: 31
          }
        },
        loc: {
          start: {
            line: 15,
            column: 43
          },
          end: {
            line: 15,
            column: 67
          }
        },
        line: 15
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 16,
            column: 13
          },
          end: {
            line: 16,
            column: 17
          }
        },
        loc: {
          start: {
            line: 16,
            column: 22
          },
          end: {
            line: 37,
            column: 5
          }
        },
        line: 16
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 21
          }
        },
        loc: {
          start: {
            line: 45,
            column: 37
          },
          end: {
            line: 53,
            column: 1
          }
        },
        line: 45
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 47,
            column: 28
          },
          end: {
            line: 47,
            column: 29
          }
        },
        loc: {
          start: {
            line: 47,
            column: 41
          },
          end: {
            line: 47,
            column: 74
          }
        },
        line: 47
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 55,
            column: 21
          },
          end: {
            line: 55,
            column: 22
          }
        },
        loc: {
          start: {
            line: 55,
            column: 38
          },
          end: {
            line: 63,
            column: 1
          }
        },
        line: 55
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 65,
            column: 21
          },
          end: {
            line: 65,
            column: 22
          }
        },
        loc: {
          start: {
            line: 65,
            column: 38
          },
          end: {
            line: 74,
            column: 1
          }
        },
        line: 65
      },
      "17": {
        name: "calculateAssessmentScores",
        decl: {
          start: {
            line: 75,
            column: 9
          },
          end: {
            line: 75,
            column: 34
          }
        },
        loc: {
          start: {
            line: 75,
            column: 46
          },
          end: {
            line: 123,
            column: 1
          }
        },
        line: 75
      },
      "18": {
        name: "generateAssessmentInsights",
        decl: {
          start: {
            line: 124,
            column: 9
          },
          end: {
            line: 124,
            column: 35
          }
        },
        loc: {
          start: {
            line: 124,
            column: 47
          },
          end: {
            line: 217,
            column: 1
          }
        },
        line: 124
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 125,
            column: 44
          },
          end: {
            line: 125,
            column: 45
          }
        },
        loc: {
          start: {
            line: 125,
            column: 56
          },
          end: {
            line: 216,
            column: 5
          }
        },
        line: 125
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 127,
            column: 33
          },
          end: {
            line: 127,
            column: 34
          }
        },
        loc: {
          start: {
            line: 127,
            column: 47
          },
          end: {
            line: 215,
            column: 9
          }
        },
        line: 127
      },
      "21": {
        name: "getReadinessLevel",
        decl: {
          start: {
            line: 219,
            column: 9
          },
          end: {
            line: 219,
            column: 26
          }
        },
        loc: {
          start: {
            line: 219,
            column: 34
          },
          end: {
            line: 227,
            column: 1
          }
        },
        line: 219
      },
      "22": {
        name: "getReadinessColor",
        decl: {
          start: {
            line: 229,
            column: 9
          },
          end: {
            line: 229,
            column: 26
          }
        },
        loc: {
          start: {
            line: 229,
            column: 34
          },
          end: {
            line: 237,
            column: 1
          }
        },
        line: 229
      },
      "23": {
        name: "generateFallbackCareerSuggestions",
        decl: {
          start: {
            line: 241,
            column: 9
          },
          end: {
            line: 241,
            column: 42
          }
        },
        loc: {
          start: {
            line: 241,
            column: 65
          },
          end: {
            line: 297,
            column: 1
          }
        },
        line: 241
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 16
          },
          end: {
            line: 11,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 17
          },
          end: {
            line: 3,
            column: 21
          }
        }, {
          start: {
            line: 3,
            column: 25
          },
          end: {
            line: 3,
            column: 39
          }
        }, {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 11,
            column: 1
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 4,
            column: 35
          },
          end: {
            line: 4,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 4,
            column: 56
          },
          end: {
            line: 4,
            column: 61
          }
        }, {
          start: {
            line: 4,
            column: 64
          },
          end: {
            line: 4,
            column: 109
          }
        }],
        line: 4
      },
      "2": {
        loc: {
          start: {
            line: 5,
            column: 16
          },
          end: {
            line: 5,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 5,
            column: 16
          },
          end: {
            line: 5,
            column: 17
          }
        }, {
          start: {
            line: 5,
            column: 22
          },
          end: {
            line: 5,
            column: 33
          }
        }],
        line: 5
      },
      "3": {
        loc: {
          start: {
            line: 8,
            column: 32
          },
          end: {
            line: 8,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 8,
            column: 46
          },
          end: {
            line: 8,
            column: 67
          }
        }, {
          start: {
            line: 8,
            column: 70
          },
          end: {
            line: 8,
            column: 115
          }
        }],
        line: 8
      },
      "4": {
        loc: {
          start: {
            line: 9,
            column: 51
          },
          end: {
            line: 9,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 9,
            column: 51
          },
          end: {
            line: 9,
            column: 61
          }
        }, {
          start: {
            line: 9,
            column: 65
          },
          end: {
            line: 9,
            column: 67
          }
        }],
        line: 9
      },
      "5": {
        loc: {
          start: {
            line: 12,
            column: 18
          },
          end: {
            line: 38,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 12,
            column: 19
          },
          end: {
            line: 12,
            column: 23
          }
        }, {
          start: {
            line: 12,
            column: 27
          },
          end: {
            line: 12,
            column: 43
          }
        }, {
          start: {
            line: 12,
            column: 48
          },
          end: {
            line: 38,
            column: 1
          }
        }],
        line: 12
      },
      "6": {
        loc: {
          start: {
            line: 13,
            column: 43
          },
          end: {
            line: 13,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 13,
            column: 43
          },
          end: {
            line: 13,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 13
      },
      "7": {
        loc: {
          start: {
            line: 13,
            column: 134
          },
          end: {
            line: 13,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 13,
            column: 167
          },
          end: {
            line: 13,
            column: 175
          }
        }, {
          start: {
            line: 13,
            column: 178
          },
          end: {
            line: 13,
            column: 184
          }
        }],
        line: 13
      },
      "8": {
        loc: {
          start: {
            line: 14,
            column: 74
          },
          end: {
            line: 14,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 14,
            column: 74
          },
          end: {
            line: 14,
            column: 102
          }
        }, {
          start: {
            line: 14,
            column: 107
          },
          end: {
            line: 14,
            column: 155
          }
        }],
        line: 14
      },
      "9": {
        loc: {
          start: {
            line: 17,
            column: 8
          },
          end: {
            line: 17,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 17,
            column: 8
          },
          end: {
            line: 17,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 17
      },
      "10": {
        loc: {
          start: {
            line: 18,
            column: 15
          },
          end: {
            line: 18,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 15
          },
          end: {
            line: 18,
            column: 16
          }
        }, {
          start: {
            line: 18,
            column: 21
          },
          end: {
            line: 18,
            column: 44
          }
        }],
        line: 18
      },
      "11": {
        loc: {
          start: {
            line: 18,
            column: 28
          },
          end: {
            line: 18,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 28
          },
          end: {
            line: 18,
            column: 33
          }
        }, {
          start: {
            line: 18,
            column: 38
          },
          end: {
            line: 18,
            column: 43
          }
        }],
        line: 18
      },
      "12": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "13": {
        loc: {
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 19,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 23
          },
          end: {
            line: 19,
            column: 24
          }
        }, {
          start: {
            line: 19,
            column: 29
          },
          end: {
            line: 19,
            column: 125
          }
        }, {
          start: {
            line: 19,
            column: 130
          },
          end: {
            line: 19,
            column: 158
          }
        }],
        line: 19
      },
      "14": {
        loc: {
          start: {
            line: 19,
            column: 33
          },
          end: {
            line: 19,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 19,
            column: 45
          },
          end: {
            line: 19,
            column: 56
          }
        }, {
          start: {
            line: 19,
            column: 59
          },
          end: {
            line: 19,
            column: 125
          }
        }],
        line: 19
      },
      "15": {
        loc: {
          start: {
            line: 19,
            column: 59
          },
          end: {
            line: 19,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 19,
            column: 67
          },
          end: {
            line: 19,
            column: 116
          }
        }, {
          start: {
            line: 19,
            column: 119
          },
          end: {
            line: 19,
            column: 125
          }
        }],
        line: 19
      },
      "16": {
        loc: {
          start: {
            line: 19,
            column: 67
          },
          end: {
            line: 19,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 67
          },
          end: {
            line: 19,
            column: 77
          }
        }, {
          start: {
            line: 19,
            column: 82
          },
          end: {
            line: 19,
            column: 115
          }
        }],
        line: 19
      },
      "17": {
        loc: {
          start: {
            line: 19,
            column: 82
          },
          end: {
            line: 19,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 83
          },
          end: {
            line: 19,
            column: 98
          }
        }, {
          start: {
            line: 19,
            column: 103
          },
          end: {
            line: 19,
            column: 112
          }
        }],
        line: 19
      },
      "18": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 20,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 20,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 20
      },
      "19": {
        loc: {
          start: {
            line: 21,
            column: 12
          },
          end: {
            line: 33,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 24
          },
          end: {
            line: 22,
            column: 46
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 72
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 25,
            column: 65
          }
        }, {
          start: {
            line: 26,
            column: 16
          },
          end: {
            line: 32,
            column: 43
          }
        }],
        line: 21
      },
      "20": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "21": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 74
          }
        }, {
          start: {
            line: 27,
            column: 79
          },
          end: {
            line: 27,
            column: 90
          }
        }, {
          start: {
            line: 27,
            column: 94
          },
          end: {
            line: 27,
            column: 105
          }
        }],
        line: 27
      },
      "22": {
        loc: {
          start: {
            line: 27,
            column: 42
          },
          end: {
            line: 27,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 42
          },
          end: {
            line: 27,
            column: 54
          }
        }, {
          start: {
            line: 27,
            column: 58
          },
          end: {
            line: 27,
            column: 73
          }
        }],
        line: 27
      },
      "23": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "24": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 40
          },
          end: {
            line: 28,
            column: 42
          }
        }, {
          start: {
            line: 28,
            column: 47
          },
          end: {
            line: 28,
            column: 59
          }
        }, {
          start: {
            line: 28,
            column: 63
          },
          end: {
            line: 28,
            column: 75
          }
        }],
        line: 28
      },
      "25": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "26": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 35
          }
        }, {
          start: {
            line: 29,
            column: 39
          },
          end: {
            line: 29,
            column: 53
          }
        }],
        line: 29
      },
      "27": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "28": {
        loc: {
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 24
          },
          end: {
            line: 30,
            column: 25
          }
        }, {
          start: {
            line: 30,
            column: 29
          },
          end: {
            line: 30,
            column: 43
          }
        }],
        line: 30
      },
      "29": {
        loc: {
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 20
          },
          end: {
            line: 31,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "30": {
        loc: {
          start: {
            line: 36,
            column: 8
          },
          end: {
            line: 36,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 36,
            column: 8
          },
          end: {
            line: 36,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 36
      },
      "31": {
        loc: {
          start: {
            line: 36,
            column: 52
          },
          end: {
            line: 36,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 36,
            column: 60
          },
          end: {
            line: 36,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 68
          },
          end: {
            line: 36,
            column: 74
          }
        }],
        line: 36
      },
      "32": {
        loc: {
          start: {
            line: 46,
            column: 4
          },
          end: {
            line: 48,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 4
          },
          end: {
            line: 48,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "33": {
        loc: {
          start: {
            line: 49,
            column: 4
          },
          end: {
            line: 51,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 49,
            column: 4
          },
          end: {
            line: 51,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 49
      },
      "34": {
        loc: {
          start: {
            line: 56,
            column: 4
          },
          end: {
            line: 58,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 56,
            column: 4
          },
          end: {
            line: 58,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 56
      },
      "35": {
        loc: {
          start: {
            line: 59,
            column: 4
          },
          end: {
            line: 61,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 59,
            column: 4
          },
          end: {
            line: 61,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 59
      },
      "36": {
        loc: {
          start: {
            line: 59,
            column: 8
          },
          end: {
            line: 59,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 59,
            column: 8
          },
          end: {
            line: 59,
            column: 28
          }
        }, {
          start: {
            line: 59,
            column: 32
          },
          end: {
            line: 59,
            column: 48
          }
        }],
        line: 59
      },
      "37": {
        loc: {
          start: {
            line: 66,
            column: 4
          },
          end: {
            line: 68,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 66,
            column: 4
          },
          end: {
            line: 68,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 66
      },
      "38": {
        loc: {
          start: {
            line: 69,
            column: 4
          },
          end: {
            line: 72,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 69,
            column: 4
          },
          end: {
            line: 72,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 69
      },
      "39": {
        loc: {
          start: {
            line: 71,
            column: 15
          },
          end: {
            line: 71,
            column: 41
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 71,
            column: 31
          },
          end: {
            line: 71,
            column: 32
          }
        }, {
          start: {
            line: 71,
            column: 35
          },
          end: {
            line: 71,
            column: 41
          }
        }],
        line: 71
      },
      "40": {
        loc: {
          start: {
            line: 77,
            column: 29
          },
          end: {
            line: 77,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 77,
            column: 29
          },
          end: {
            line: 77,
            column: 72
          }
        }, {
          start: {
            line: 77,
            column: 76
          },
          end: {
            line: 77,
            column: 77
          }
        }],
        line: 77
      },
      "41": {
        loc: {
          start: {
            line: 79,
            column: 24
          },
          end: {
            line: 79,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 79,
            column: 24
          },
          end: {
            line: 79,
            column: 64
          }
        }, {
          start: {
            line: 79,
            column: 68
          },
          end: {
            line: 79,
            column: 69
          }
        }],
        line: 79
      },
      "42": {
        loc: {
          start: {
            line: 81,
            column: 23
          },
          end: {
            line: 81,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 81,
            column: 23
          },
          end: {
            line: 81,
            column: 63
          }
        }, {
          start: {
            line: 81,
            column: 67
          },
          end: {
            line: 81,
            column: 68
          }
        }],
        line: 81
      },
      "43": {
        loc: {
          start: {
            line: 83,
            column: 26
          },
          end: {
            line: 83,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 83,
            column: 26
          },
          end: {
            line: 83,
            column: 68
          }
        }, {
          start: {
            line: 83,
            column: 72
          },
          end: {
            line: 83,
            column: 73
          }
        }],
        line: 83
      },
      "44": {
        loc: {
          start: {
            line: 87,
            column: 4
          },
          end: {
            line: 104,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 88,
            column: 8
          },
          end: {
            line: 90,
            column: 18
          }
        }, {
          start: {
            line: 91,
            column: 8
          },
          end: {
            line: 93,
            column: 18
          }
        }, {
          start: {
            line: 94,
            column: 8
          },
          end: {
            line: 96,
            column: 18
          }
        }, {
          start: {
            line: 97,
            column: 8
          },
          end: {
            line: 99,
            column: 18
          }
        }, {
          start: {
            line: 100,
            column: 8
          },
          end: {
            line: 100,
            column: 24
          }
        }, {
          start: {
            line: 101,
            column: 8
          },
          end: {
            line: 103,
            column: 18
          }
        }],
        line: 87
      },
      "45": {
        loc: {
          start: {
            line: 128,
            column: 12
          },
          end: {
            line: 214,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 129,
            column: 16
          },
          end: {
            line: 165,
            column: 33
          }
        }, {
          start: {
            line: 166,
            column: 16
          },
          end: {
            line: 181,
            column: 28
          }
        }, {
          start: {
            line: 182,
            column: 16
          },
          end: {
            line: 185,
            column: 44
          }
        }, {
          start: {
            line: 186,
            column: 16
          },
          end: {
            line: 190,
            column: 44
          }
        }, {
          start: {
            line: 191,
            column: 16
          },
          end: {
            line: 213,
            column: 27
          }
        }],
        line: 128
      },
      "46": {
        loc: {
          start: {
            line: 136,
            column: 20
          },
          end: {
            line: 147,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 20
          },
          end: {
            line: 147,
            column: 21
          }
        }, {
          start: {
            line: 139,
            column: 25
          },
          end: {
            line: 147,
            column: 21
          }
        }],
        line: 136
      },
      "47": {
        loc: {
          start: {
            line: 139,
            column: 25
          },
          end: {
            line: 147,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 139,
            column: 25
          },
          end: {
            line: 147,
            column: 21
          }
        }, {
          start: {
            line: 142,
            column: 25
          },
          end: {
            line: 147,
            column: 21
          }
        }],
        line: 139
      },
      "48": {
        loc: {
          start: {
            line: 142,
            column: 25
          },
          end: {
            line: 147,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 142,
            column: 25
          },
          end: {
            line: 147,
            column: 21
          }
        }, {
          start: {
            line: 145,
            column: 25
          },
          end: {
            line: 147,
            column: 21
          }
        }],
        line: 142
      },
      "49": {
        loc: {
          start: {
            line: 149,
            column: 20
          },
          end: {
            line: 151,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 149,
            column: 20
          },
          end: {
            line: 151,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 149
      },
      "50": {
        loc: {
          start: {
            line: 152,
            column: 20
          },
          end: {
            line: 154,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 152,
            column: 20
          },
          end: {
            line: 154,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 152
      },
      "51": {
        loc: {
          start: {
            line: 155,
            column: 20
          },
          end: {
            line: 157,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 155,
            column: 20
          },
          end: {
            line: 157,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 155
      },
      "52": {
        loc: {
          start: {
            line: 158,
            column: 20
          },
          end: {
            line: 160,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 158,
            column: 20
          },
          end: {
            line: 160,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 158
      },
      "53": {
        loc: {
          start: {
            line: 161,
            column: 20
          },
          end: {
            line: 163,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 161,
            column: 20
          },
          end: {
            line: 163,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 161
      },
      "54": {
        loc: {
          start: {
            line: 193,
            column: 20
          },
          end: {
            line: 195,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 193,
            column: 20
          },
          end: {
            line: 195,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 193
      },
      "55": {
        loc: {
          start: {
            line: 197,
            column: 20
          },
          end: {
            line: 199,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 197,
            column: 20
          },
          end: {
            line: 199,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 197
      },
      "56": {
        loc: {
          start: {
            line: 220,
            column: 4
          },
          end: {
            line: 221,
            column: 32
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 220,
            column: 4
          },
          end: {
            line: 221,
            column: 32
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 220
      },
      "57": {
        loc: {
          start: {
            line: 222,
            column: 4
          },
          end: {
            line: 223,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 222,
            column: 4
          },
          end: {
            line: 223,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 222
      },
      "58": {
        loc: {
          start: {
            line: 224,
            column: 4
          },
          end: {
            line: 225,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 224,
            column: 4
          },
          end: {
            line: 225,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 224
      },
      "59": {
        loc: {
          start: {
            line: 230,
            column: 4
          },
          end: {
            line: 231,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 230,
            column: 4
          },
          end: {
            line: 231,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 230
      },
      "60": {
        loc: {
          start: {
            line: 232,
            column: 4
          },
          end: {
            line: 233,
            column: 54
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 232,
            column: 4
          },
          end: {
            line: 233,
            column: 54
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 232
      },
      "61": {
        loc: {
          start: {
            line: 234,
            column: 4
          },
          end: {
            line: 235,
            column: 54
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 234,
            column: 4
          },
          end: {
            line: 235,
            column: 54
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 234
      },
      "62": {
        loc: {
          start: {
            line: 246,
            column: 4
          },
          end: {
            line: 254,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 246,
            column: 4
          },
          end: {
            line: 254,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 246
      },
      "63": {
        loc: {
          start: {
            line: 248,
            column: 8
          },
          end: {
            line: 250,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 248,
            column: 8
          },
          end: {
            line: 250,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 248
      },
      "64": {
        loc: {
          start: {
            line: 248,
            column: 12
          },
          end: {
            line: 248,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 248,
            column: 12
          },
          end: {
            line: 248,
            column: 55
          }
        }, {
          start: {
            line: 248,
            column: 59
          },
          end: {
            line: 248,
            column: 94
          }
        }],
        line: 248
      },
      "65": {
        loc: {
          start: {
            line: 251,
            column: 8
          },
          end: {
            line: 253,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 251,
            column: 8
          },
          end: {
            line: 253,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 251
      },
      "66": {
        loc: {
          start: {
            line: 256,
            column: 4
          },
          end: {
            line: 261,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 256,
            column: 4
          },
          end: {
            line: 261,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 256
      },
      "67": {
        loc: {
          start: {
            line: 256,
            column: 8
          },
          end: {
            line: 256,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 256,
            column: 8
          },
          end: {
            line: 256,
            column: 45
          }
        }, {
          start: {
            line: 256,
            column: 49
          },
          end: {
            line: 256,
            column: 86
          }
        }],
        line: 256
      },
      "68": {
        loc: {
          start: {
            line: 258,
            column: 8
          },
          end: {
            line: 260,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 258,
            column: 8
          },
          end: {
            line: 260,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 258
      },
      "69": {
        loc: {
          start: {
            line: 263,
            column: 4
          },
          end: {
            line: 268,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 4
          },
          end: {
            line: 268,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 263
      },
      "70": {
        loc: {
          start: {
            line: 263,
            column: 8
          },
          end: {
            line: 263,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 263,
            column: 8
          },
          end: {
            line: 263,
            column: 45
          }
        }, {
          start: {
            line: 263,
            column: 49
          },
          end: {
            line: 263,
            column: 81
          }
        }],
        line: 263
      },
      "71": {
        loc: {
          start: {
            line: 265,
            column: 8
          },
          end: {
            line: 267,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 265,
            column: 8
          },
          end: {
            line: 267,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 265
      },
      "72": {
        loc: {
          start: {
            line: 265,
            column: 12
          },
          end: {
            line: 265,
            column: 103
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 265,
            column: 12
          },
          end: {
            line: 265,
            column: 66
          }
        }, {
          start: {
            line: 265,
            column: 70
          },
          end: {
            line: 265,
            column: 103
          }
        }],
        line: 265
      },
      "73": {
        loc: {
          start: {
            line: 270,
            column: 4
          },
          end: {
            line: 272,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 270,
            column: 4
          },
          end: {
            line: 272,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 270
      },
      "74": {
        loc: {
          start: {
            line: 270,
            column: 8
          },
          end: {
            line: 270,
            column: 91
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 270,
            column: 8
          },
          end: {
            line: 270,
            column: 47
          }
        }, {
          start: {
            line: 270,
            column: 51
          },
          end: {
            line: 270,
            column: 91
          }
        }],
        line: 270
      },
      "75": {
        loc: {
          start: {
            line: 274,
            column: 4
          },
          end: {
            line: 279,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 274,
            column: 4
          },
          end: {
            line: 279,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 274
      },
      "76": {
        loc: {
          start: {
            line: 276,
            column: 8
          },
          end: {
            line: 278,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 276,
            column: 8
          },
          end: {
            line: 278,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 276
      },
      "77": {
        loc: {
          start: {
            line: 281,
            column: 4
          },
          end: {
            line: 283,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 281,
            column: 4
          },
          end: {
            line: 283,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 281
      },
      "78": {
        loc: {
          start: {
            line: 287,
            column: 4
          },
          end: {
            line: 295,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 287,
            column: 4
          },
          end: {
            line: 295,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 287
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0, 0, 0, 0, 0],
      "45": [0, 0, 0, 0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/assessmentScoring.ts",
      mappings: ";AAAA,6CAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwF7C,8DAyDC;AAED,gEA+FC;AAGD,8CAKC;AAGD,8CAKC;AA5MD,6CAA6C;AAC7C,IAAM,aAAa,GAAG,UAAC,KAAwC;IAC7D,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACzB,OAAO,KAAK,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,OAAO,CAAC,KAAK,QAAQ,EAArB,CAAqB,CAAa,CAAC;IAC9D,CAAC;IACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,CAAC,KAAK,CAAC,CAAC;IACjB,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AAEF,6CAA6C;AAC7C,IAAM,cAAc,GAAG,UAAC,KAAwC;IAC9D,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC7C,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AAEF,6CAA6C;AAC7C,IAAM,cAAc,GAAG,UAAC,KAAwC;IAC9D,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,IAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;QACjC,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACpC,CAAC;IACD,OAAO,CAAC,CAAC;AACX,CAAC,CAAC;AAEF,SAAgB,yBAAyB,CAAC,SAA6B;IACrE,gCAAgC;IAChC,IAAM,kBAAkB,GAAG,cAAc,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAE5E,6BAA6B;IAC7B,IAAM,aAAa,GAAG,cAAc,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAEpE,6BAA6B;IAC7B,IAAM,YAAY,GAAG,cAAc,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAEnE,+BAA+B;IAC/B,IAAM,eAAe,GAAG,cAAc,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAExE,4DAA4D;IAC5D,IAAM,QAAQ,GAAG,cAAc,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;IAC/D,IAAI,YAAY,GAAG,CAAC,CAAC,CAAC,yBAAyB;IAE/C,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,WAAW;YACd,YAAY,GAAG,CAAC,CAAC;YACjB,MAAM;QACR,KAAK,YAAY;YACf,YAAY,GAAG,CAAC,CAAC;YACjB,MAAM;QACR,KAAK,aAAa;YAChB,YAAY,GAAG,CAAC,CAAC;YACjB,MAAM;QACR,KAAK,WAAW;YACd,YAAY,GAAG,CAAC,CAAC;YACjB,MAAM;QACR,KAAK,UAAU,CAAC;QAChB,KAAK,WAAW;YACd,YAAY,GAAG,CAAC,CAAC;YACjB,MAAM;IACV,CAAC;IAED,uDAAuD;IACvD,IAAM,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACtD,IAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,+BAA+B;IAE9F,uDAAuD;IACvD,IAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAC/B,CAAC,kBAAkB,GAAG,IAAI;QACzB,eAAe,GAAG,IAAI;QACtB,YAAY,GAAG,GAAG;QAClB,aAAa,GAAG,IAAI;QACpB,CAAC,gBAAgB,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,yBAAyB;KACrE,CAAC;IAEF,OAAO;QACL,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;QAC1D,aAAa,eAAA;QACb,YAAY,cAAA;QACZ,gBAAgB,kBAAA;QAChB,YAAY,cAAA;QACZ,kBAAkB,oBAAA;KACnB,CAAC;AACJ,CAAC;AAED,SAAsB,0BAA0B,CAAC,SAA6B;mCAAG,OAAO;;;;;oBAChF,MAAM,GAAG,yBAAyB,CAAC,SAAS,CAAC,CAAC;oBAG9C,iBAAiB,GAAG,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;oBACvE,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;oBAChD,gBAAgB,GAAG,aAAa,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;oBAC9D,QAAQ,GAAG,cAAc,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;oBAG3D,mBAAmB,GAAG,6BAA6B,CAAC;oBACxD,IAAI,MAAM,CAAC,cAAc,IAAI,EAAE,EAAE,CAAC;wBAChC,mBAAmB,GAAG,uCAAuC,CAAC;oBAChE,CAAC;yBAAM,IAAI,MAAM,CAAC,cAAc,IAAI,EAAE,EAAE,CAAC;wBACvC,mBAAmB,GAAG,yCAAyC,CAAC;oBAClE,CAAC;yBAAM,IAAI,MAAM,CAAC,cAAc,IAAI,EAAE,EAAE,CAAC;wBACvC,mBAAmB,GAAG,qCAAqC,CAAC;oBAC9D,CAAC;yBAAM,CAAC;wBACN,mBAAmB,GAAG,6CAA6C,CAAC;oBACtE,CAAC;oBAGK,kBAAkB,GAAa,EAAE,CAAC;oBAExC,IAAI,MAAM,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;wBAClC,kBAAkB,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;oBACpF,CAAC;oBAED,IAAI,MAAM,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;wBAC5B,kBAAkB,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;oBACnF,CAAC;oBAED,IAAI,MAAM,CAAC,gBAAgB,GAAG,EAAE,EAAE,CAAC;wBACjC,kBAAkB,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;oBAChF,CAAC;oBAED,IAAI,gBAAgB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;wBACjD,kBAAkB,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;oBAC/E,CAAC;oBAED,IAAI,gBAAgB,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,CAAC;wBACnD,kBAAkB,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;oBAChF,CAAC;oBAGG,qBAAqB,GAAa,EAAE,CAAC;;;;oBAI/B,4BAA4B,GAAK,OAAO,CAAC,gCAAgC,CAAC,6BAA9C,CAA+C;oBACpE,qBAAM,4BAA4B,CAAC,6BAA6B,CAAC,SAAS,EAAE;4BACzF,MAAM,QAAA;4BACN,iBAAiB,mBAAA;4BACjB,SAAS,WAAA;4BACT,gBAAgB,kBAAA;4BAChB,mBAAmB,qBAAA;4BACnB,kBAAkB,oBAAA;4BAClB,qBAAqB,EAAE,EAAE;4BACzB,kBAAkB,EAAE,EAAE;4BACtB,gBAAgB,EAAE,EAAE;4BACpB,kBAAkB,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;4BACzC,uBAAuB,EAAE,mBAAmB;yBAC7C,CAAC,EAAA;;oBAZI,MAAM,GAAG,SAYb;oBAEF,qBAAqB,GAAG,MAAM,CAAC,qBAAqB,CAAC;;;;oBAGrD,OAAO,CAAC,IAAI,CAAC,6DAA6D,EAAE,OAAK,CAAC,CAAC;oBACnF,qBAAqB,GAAG,iCAAiC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;;;oBAGlF,oEAAoE;oBACpE,IAAI,MAAM,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;wBAClC,kBAAkB,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;oBAC3F,CAAC;oBAED,2CAA2C;oBAC3C,IAAI,qBAAqB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACvC,qBAAqB,GAAG,CAAC,8BAA8B,EAAE,gCAAgC,EAAE,0BAA0B,CAAC,CAAC;oBACzH,CAAC;oBAED,sBAAO;4BACL,MAAM,QAAA;4BACN,iBAAiB,mBAAA;4BACjB,SAAS,WAAA;4BACT,gBAAgB,kBAAA;4BAChB,mBAAmB,qBAAA;4BACnB,kBAAkB,oBAAA;4BAClB,qBAAqB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,qBAAqB,CAAC,CAAC,EAAE,oBAAoB;4BACvF,mEAAmE;4BACnE,kBAAkB,EAAE,EAAE;4BACtB,gBAAgB,EAAE,EAAE;4BACpB,kBAAkB,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,gDAAgD;4BAC3F,uBAAuB,EAAE,mBAAmB;yBAC7C,EAAC;;;;CACH;AAED,gDAAgD;AAChD,SAAgB,iBAAiB,CAAC,KAAa;IAC7C,IAAI,KAAK,IAAI,EAAE;QAAE,OAAO,gBAAgB,CAAC;IACzC,IAAI,KAAK,IAAI,EAAE;QAAE,OAAO,oBAAoB,CAAC;IAC7C,IAAI,KAAK,IAAI,EAAE;QAAE,OAAO,oBAAoB,CAAC;IAC7C,OAAO,mBAAmB,CAAC;AAC7B,CAAC;AAED,yCAAyC;AACzC,SAAgB,iBAAiB,CAAC,KAAa;IAC7C,IAAI,KAAK,IAAI,EAAE;QAAE,OAAO,oCAAoC,CAAC;IAC7D,IAAI,KAAK,IAAI,EAAE;QAAE,OAAO,sCAAsC,CAAC;IAC/D,IAAI,KAAK,IAAI,EAAE;QAAE,OAAO,sCAAsC,CAAC;IAC/D,OAAO,gCAAgC,CAAC;AAC1C,CAAC;AAED;;GAEG;AACH,SAAS,iCAAiC,CACxC,SAAmB,EACnB,SAA6B;IAE7B,IAAM,WAAW,GAAa,EAAE,CAAC;IACjC,IAAM,yBAAyB,GAAG,aAAa,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;IACtF,IAAM,YAAY,GAAG,aAAa,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;IAE5D,8BAA8B;IAC9B,IAAI,SAAS,CAAC,QAAQ,CAAC,uBAAuB,CAAC,EAAE,CAAC;QAChD,WAAW,CAAC,IAAI,CAAC,0BAA0B,EAAE,sBAAsB,CAAC,CAAC;QAErE,IAAI,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YACvF,WAAW,CAAC,IAAI,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,yBAAyB,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YACxD,WAAW,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,6BAA6B;IAC7B,IAAI,SAAS,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;QACnF,WAAW,CAAC,IAAI,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;QAEtD,IAAI,yBAAyB,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAC5D,WAAW,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAED,gCAAgC;IAChC,IAAI,SAAS,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;QAC9E,WAAW,CAAC,IAAI,CAAC,8BAA8B,EAAE,iBAAiB,CAAC,CAAC;QAEpE,IAAI,yBAAyB,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAChG,WAAW,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED,+BAA+B;IAC/B,IAAI,SAAS,CAAC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;QACxF,WAAW,CAAC,IAAI,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,qBAAqB,CAAC,CAAC;IAChF,CAAC;IAED,0BAA0B;IAC1B,IAAI,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;QACxC,WAAW,CAAC,IAAI,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC;QAEvD,IAAI,yBAAyB,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;YAC7D,WAAW,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED,2BAA2B;IAC3B,IAAI,SAAS,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;QAC7C,WAAW,CAAC,IAAI,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;IACzE,CAAC;IAED,+CAA+C;IAC/C,IAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;IAE3D,uDAAuD;IACvD,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACnC,OAAO;YACL,8BAA8B;YAC9B,0BAA0B;YAC1B,iBAAiB;YACjB,cAAc;YACd,gBAAgB;SACjB,CAAC;IACJ,CAAC;IAED,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACvC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/assessmentScoring.ts"],
      sourcesContent: ["// Assessment scoring and insights generation\n\nexport interface AssessmentScores {\n  readinessScore: number; // 0-100\n  riskTolerance: number; // 1-5\n  urgencyLevel: number; // 1-5\n  skillsConfidence: number; // 0-100\n  supportLevel: number; // 1-5\n  financialReadiness: number; // 1-5\n}\n\nexport interface SkillGap {\n  skill: string;\n  currentLevel: number; // 0-5\n  requiredLevel: number; // 0-5\n  priority: 'HIGH' | 'MEDIUM' | 'LOW';\n  estimatedLearningTime: string;\n  recommendedResources: string[];\n}\n\nexport interface CareerPathAnalysis {\n  careerPath: string;\n  matchPercentage: number;\n  matchReason: string;\n  salaryRange: {\n    min: number;\n    max: number;\n    currency: string;\n  };\n  jobGrowthRate: string;\n  skillGaps: SkillGap[];\n  strengthsAlignment: string[];\n  nextSteps: string[];\n}\n\nexport interface AssessmentInsights {\n  scores: AssessmentScores;\n  primaryMotivation: string;\n  topSkills: string[];\n  biggestObstacles: string[];\n  recommendedTimeline: string;\n  keyRecommendations: string[];\n  careerPathSuggestions: string[];\n  // Enhanced fields\n  careerPathAnalysis: CareerPathAnalysis[];\n  overallSkillGaps: SkillGap[];\n  learningPriorities: string[];\n  estimatedTransitionTime: string;\n}\n\nexport interface AssessmentResponse {\n  [key: string]: string | string[] | number | null;\n}\n\n// Helper function to safely get array values\nconst getArrayValue = (value: string | string[] | number | null): string[] => {\n  if (Array.isArray(value)) {\n    return value.filter(v => typeof v === 'string') as string[];\n  }\n  if (typeof value === 'string') {\n    return [value];\n  }\n  return [];\n};\n\n// Helper function to safely get string value\nconst getStringValue = (value: string | string[] | number | null): string => {\n  if (typeof value === 'string') {\n    return value;\n  }\n  if (Array.isArray(value) && value.length > 0) {\n    return String(value[0]);\n  }\n  return '';\n};\n\n// Helper function to safely get number value\nconst getNumberValue = (value: string | string[] | number | null): number => {\n  if (typeof value === 'number') {\n    return value;\n  }\n  if (typeof value === 'string') {\n    const parsed = parseFloat(value);\n    return isNaN(parsed) ? 0 : parsed;\n  }\n  return 0;\n};\n\nexport function calculateAssessmentScores(responses: AssessmentResponse): AssessmentScores {\n  // Financial comfort (1-5 scale)\n  const financialReadiness = getNumberValue(responses.financial_comfort) || 1;\n  \n  // Risk tolerance (1-5 scale)\n  const riskTolerance = getNumberValue(responses.risk_tolerance) || 1;\n  \n  // Support system (1-5 scale)\n  const supportLevel = getNumberValue(responses.support_system) || 1;\n  \n  // Confidence level (1-5 scale)\n  const confidenceLevel = getNumberValue(responses.confidence_level) || 1;\n  \n  // Calculate urgency based on timeline and employment status\n  const timeline = getStringValue(responses.transition_timeline);\n  let urgencyLevel = 3; // Default medium urgency\n  \n  switch (timeline) {\n    case 'immediate':\n      urgencyLevel = 5;\n      break;\n    case 'short_term':\n      urgencyLevel = 4;\n      break;\n    case 'medium_term':\n      urgencyLevel = 3;\n      break;\n    case 'long_term':\n      urgencyLevel = 2;\n      break;\n    case 'extended':\n    case 'exploring':\n      urgencyLevel = 1;\n      break;\n  }\n  \n  // Calculate skills confidence based on selected skills\n  const topSkills = getArrayValue(responses.top_skills);\n  const skillsConfidence = Math.min(100, topSkills.length * 20); // 20 points per skill, max 100\n  \n  // Calculate overall readiness score (weighted average)\n  const readinessScore = Math.round(\n    (financialReadiness * 0.25 +\n     confidenceLevel * 0.25 +\n     supportLevel * 0.2 +\n     riskTolerance * 0.15 +\n     (skillsConfidence / 100 * 5) * 0.15) * 20 // Convert to 0-100 scale\n  );\n  \n  return {\n    readinessScore: Math.max(0, Math.min(100, readinessScore)),\n    riskTolerance,\n    urgencyLevel,\n    skillsConfidence,\n    supportLevel,\n    financialReadiness,\n  };\n}\n\nexport async function generateAssessmentInsights(responses: AssessmentResponse): Promise<AssessmentInsights> {\n  const scores = calculateAssessmentScores(responses);\n  \n  // Extract key data\n  const primaryMotivation = getStringValue(responses.career_change_motivation);\n  const topSkills = getArrayValue(responses.top_skills);\n  const biggestObstacles = getArrayValue(responses.biggest_obstacles);\n  const timeline = getStringValue(responses.transition_timeline);\n  \n  // Generate timeline recommendation\n  let recommendedTimeline = 'Take time to plan carefully';\n  if (scores.readinessScore >= 80) {\n    recommendedTimeline = 'You appear ready to move forward soon';\n  } else if (scores.readinessScore >= 60) {\n    recommendedTimeline = 'Consider a 3-6 month preparation period';\n  } else if (scores.readinessScore >= 40) {\n    recommendedTimeline = 'Plan for 6-12 months of preparation';\n  } else {\n    recommendedTimeline = 'Focus on building readiness over 12+ months';\n  }\n  \n  // Generate key recommendations\n  const keyRecommendations: string[] = [];\n  \n  if (scores.financialReadiness < 3) {\n    keyRecommendations.push('Build your emergency fund before making the transition');\n  }\n  \n  if (scores.supportLevel < 3) {\n    keyRecommendations.push('Build a stronger support network of mentors and peers');\n  }\n  \n  if (scores.skillsConfidence < 60) {\n    keyRecommendations.push('Focus on developing and validating your key skills');\n  }\n  \n  if (biggestObstacles.includes('fear_of_failure')) {\n    keyRecommendations.push('Work on mindset and confidence-building exercises');\n  }\n  \n  if (biggestObstacles.includes('unclear_direction')) {\n    keyRecommendations.push('Spend time clarifying your career vision and goals');\n  }\n  \n  // Generate career path suggestions using integrated assessment service\n  let careerPathSuggestions: string[] = [];\n\n  try {\n    // Use the assessment service integration layer\n    const { AssessmentServiceIntegration } = require('./assessmentServiceIntegration');\n    const result = await AssessmentServiceIntegration.generateCareerRecommendations(responses, {\n      scores,\n      primaryMotivation,\n      topSkills,\n      biggestObstacles,\n      recommendedTimeline,\n      keyRecommendations,\n      careerPathSuggestions: [],\n      careerPathAnalysis: [],\n      overallSkillGaps: [],\n      learningPriorities: topSkills.slice(0, 3),\n      estimatedTransitionTime: recommendedTimeline\n    });\n\n    careerPathSuggestions = result.careerRecommendations;\n\n  } catch (error) {\n    console.warn('Assessment service integration unavailable, using fallback:', error);\n    careerPathSuggestions = generateFallbackCareerSuggestions(topSkills, responses);\n  }\n\n  // Add financial literacy recommendation for low financial readiness\n  if (scores.financialReadiness < 3) {\n    keyRecommendations.push('Learn financial planning and budgeting for career transitions');\n  }\n\n  // Ensure we have at least some suggestions\n  if (careerPathSuggestions.length === 0) {\n    careerPathSuggestions = ['Digital Marketing Specialist', 'Entrepreneur / Startup Founder', 'Full-Stack Web Developer'];\n  }\n  \n  return {\n    scores,\n    primaryMotivation,\n    topSkills,\n    biggestObstacles,\n    recommendedTimeline,\n    keyRecommendations,\n    careerPathSuggestions: Array.from(new Set(careerPathSuggestions)), // Remove duplicates\n    // Enhanced fields - will be populated by EnhancedAssessmentService\n    careerPathAnalysis: [],\n    overallSkillGaps: [],\n    learningPriorities: topSkills.slice(0, 3), // Use top skills as initial learning priorities\n    estimatedTransitionTime: recommendedTimeline\n  };\n}\n\n// Function to get a readiness level description\nexport function getReadinessLevel(score: number): string {\n  if (score >= 80) return 'High Readiness';\n  if (score >= 60) return 'Moderate Readiness';\n  if (score >= 40) return 'Building Readiness';\n  return 'Early Exploration';\n}\n\n// Function to get readiness color for UI\nexport function getReadinessColor(score: number): string {\n  if (score >= 80) return 'text-green-600 dark:text-green-400';\n  if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';\n  if (score >= 40) return 'text-orange-600 dark:text-orange-400';\n  return 'text-red-600 dark:text-red-400';\n}\n\n/**\n * Enhanced fallback career suggestions with better logic\n */\nfunction generateFallbackCareerSuggestions(\n  topSkills: string[],\n  responses: AssessmentResponse\n): string[] {\n  const suggestions: string[] = [];\n  const skillDevelopmentInterests = getArrayValue(responses.skill_development_interest);\n  const careerValues = getArrayValue(responses.career_values);\n\n  // Technical programming paths\n  if (topSkills.includes('technical_programming')) {\n    suggestions.push('Full-Stack Web Developer', 'Mobile App Developer');\n\n    if (skillDevelopmentInterests.includes('ai_ml') || topSkills.includes('data_analysis')) {\n      suggestions.push('AI/ML Engineer', 'Data Scientist');\n    }\n\n    if (skillDevelopmentInterests.includes('cybersecurity')) {\n      suggestions.push('Cybersecurity Specialist');\n    }\n  }\n\n  // Creative and content paths\n  if (topSkills.includes('writing_content') || topSkills.includes('design_creative')) {\n    suggestions.push('UX/UI Designer', 'Content Creator');\n\n    if (skillDevelopmentInterests.includes('digital_marketing')) {\n      suggestions.push('Digital Marketing Specialist');\n    }\n  }\n\n  // Business and leadership paths\n  if (topSkills.includes('sales_marketing') || topSkills.includes('leadership')) {\n    suggestions.push('Digital Marketing Specialist', 'Product Manager');\n\n    if (skillDevelopmentInterests.includes('entrepreneurship') || careerValues.includes('autonomy')) {\n      suggestions.push('Entrepreneur / Startup Founder');\n    }\n  }\n\n  // Education and coaching paths\n  if (topSkills.includes('teaching_training') || topSkills.includes('coaching_mentoring')) {\n    suggestions.push('Online Coaching', 'Course Creation', 'Training Specialist');\n  }\n\n  // Data and analysis paths\n  if (topSkills.includes('data_analysis')) {\n    suggestions.push('Data Scientist', 'Business Analyst');\n\n    if (skillDevelopmentInterests.includes('financial_planning')) {\n      suggestions.push('Financial Advisor / Planner');\n    }\n  }\n\n  // Project management paths\n  if (topSkills.includes('project_management')) {\n    suggestions.push('Product Manager', 'Project Manager', 'Scrum Master');\n  }\n\n  // Remove duplicates and return top suggestions\n  const uniqueSuggestions = Array.from(new Set(suggestions));\n\n  // If still no matches, provide diverse default options\n  if (uniqueSuggestions.length === 0) {\n    return [\n      'Digital Marketing Specialist',\n      'Full-Stack Web Developer',\n      'Product Manager',\n      'Data Analyst',\n      'UX/UI Designer'\n    ];\n  }\n\n  return uniqueSuggestions.slice(0, 5);\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "83beed3e8c9d03a8c4d12eeaa27bbcef989a12ce"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_25mt5yuw2j = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_25mt5yuw2j();
var __awaiter =
/* istanbul ignore next */
(cov_25mt5yuw2j().s[0]++,
/* istanbul ignore next */
(cov_25mt5yuw2j().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_25mt5yuw2j().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_25mt5yuw2j().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_25mt5yuw2j().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().f[1]++;
    cov_25mt5yuw2j().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_25mt5yuw2j().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_25mt5yuw2j().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_25mt5yuw2j().f[2]++;
      cov_25mt5yuw2j().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_25mt5yuw2j().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_25mt5yuw2j().f[4]++;
      cov_25mt5yuw2j().s[4]++;
      try {
        /* istanbul ignore next */
        cov_25mt5yuw2j().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_25mt5yuw2j().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_25mt5yuw2j().f[5]++;
      cov_25mt5yuw2j().s[7]++;
      try {
        /* istanbul ignore next */
        cov_25mt5yuw2j().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_25mt5yuw2j().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_25mt5yuw2j().f[6]++;
      cov_25mt5yuw2j().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_25mt5yuw2j().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_25mt5yuw2j().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_25mt5yuw2j().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_25mt5yuw2j().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_25mt5yuw2j().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_25mt5yuw2j().s[12]++,
/* istanbul ignore next */
(cov_25mt5yuw2j().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_25mt5yuw2j().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_25mt5yuw2j().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_25mt5yuw2j().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_25mt5yuw2j().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_25mt5yuw2j().f[8]++;
        cov_25mt5yuw2j().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_25mt5yuw2j().b[6][0]++;
          cov_25mt5yuw2j().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_25mt5yuw2j().b[6][1]++;
        }
        cov_25mt5yuw2j().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_25mt5yuw2j().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_25mt5yuw2j().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_25mt5yuw2j().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_25mt5yuw2j().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_25mt5yuw2j().f[9]++;
    cov_25mt5yuw2j().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().f[10]++;
    cov_25mt5yuw2j().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_25mt5yuw2j().f[11]++;
      cov_25mt5yuw2j().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().f[12]++;
    cov_25mt5yuw2j().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_25mt5yuw2j().b[9][0]++;
      cov_25mt5yuw2j().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_25mt5yuw2j().b[9][1]++;
    }
    cov_25mt5yuw2j().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_25mt5yuw2j().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_25mt5yuw2j().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_25mt5yuw2j().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_25mt5yuw2j().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_25mt5yuw2j().s[25]++;
      try {
        /* istanbul ignore next */
        cov_25mt5yuw2j().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_25mt5yuw2j().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_25mt5yuw2j().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_25mt5yuw2j().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_25mt5yuw2j().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_25mt5yuw2j().b[15][0]++,
        /* istanbul ignore next */
        (cov_25mt5yuw2j().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_25mt5yuw2j().b[16][1]++,
        /* istanbul ignore next */
        (cov_25mt5yuw2j().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_25mt5yuw2j().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_25mt5yuw2j().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_25mt5yuw2j().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_25mt5yuw2j().b[12][0]++;
          cov_25mt5yuw2j().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_25mt5yuw2j().b[12][1]++;
        }
        cov_25mt5yuw2j().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_25mt5yuw2j().b[18][0]++;
          cov_25mt5yuw2j().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_25mt5yuw2j().b[18][1]++;
        }
        cov_25mt5yuw2j().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_25mt5yuw2j().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_25mt5yuw2j().b[19][1]++;
            cov_25mt5yuw2j().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_25mt5yuw2j().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_25mt5yuw2j().b[19][2]++;
            cov_25mt5yuw2j().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_25mt5yuw2j().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_25mt5yuw2j().b[19][3]++;
            cov_25mt5yuw2j().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_25mt5yuw2j().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_25mt5yuw2j().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_25mt5yuw2j().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_25mt5yuw2j().b[19][4]++;
            cov_25mt5yuw2j().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_25mt5yuw2j().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_25mt5yuw2j().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_25mt5yuw2j().b[19][5]++;
            cov_25mt5yuw2j().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_25mt5yuw2j().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_25mt5yuw2j().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_25mt5yuw2j().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_25mt5yuw2j().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_25mt5yuw2j().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_25mt5yuw2j().b[20][0]++;
              cov_25mt5yuw2j().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_25mt5yuw2j().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_25mt5yuw2j().b[20][1]++;
            }
            cov_25mt5yuw2j().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_25mt5yuw2j().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_25mt5yuw2j().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_25mt5yuw2j().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_25mt5yuw2j().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_25mt5yuw2j().b[23][0]++;
              cov_25mt5yuw2j().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_25mt5yuw2j().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_25mt5yuw2j().b[23][1]++;
            }
            cov_25mt5yuw2j().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_25mt5yuw2j().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_25mt5yuw2j().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_25mt5yuw2j().b[25][0]++;
              cov_25mt5yuw2j().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_25mt5yuw2j().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_25mt5yuw2j().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_25mt5yuw2j().b[25][1]++;
            }
            cov_25mt5yuw2j().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_25mt5yuw2j().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_25mt5yuw2j().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_25mt5yuw2j().b[27][0]++;
              cov_25mt5yuw2j().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_25mt5yuw2j().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_25mt5yuw2j().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_25mt5yuw2j().b[27][1]++;
            }
            cov_25mt5yuw2j().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_25mt5yuw2j().b[29][0]++;
              cov_25mt5yuw2j().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_25mt5yuw2j().b[29][1]++;
            }
            cov_25mt5yuw2j().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_25mt5yuw2j().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_25mt5yuw2j().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_25mt5yuw2j().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_25mt5yuw2j().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_25mt5yuw2j().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_25mt5yuw2j().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_25mt5yuw2j().b[30][0]++;
      cov_25mt5yuw2j().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_25mt5yuw2j().b[30][1]++;
    }
    cov_25mt5yuw2j().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_25mt5yuw2j().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_25mt5yuw2j().b[31][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_25mt5yuw2j().s[67]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_25mt5yuw2j().s[68]++;
exports.calculateAssessmentScores = calculateAssessmentScores;
/* istanbul ignore next */
cov_25mt5yuw2j().s[69]++;
exports.generateAssessmentInsights = generateAssessmentInsights;
/* istanbul ignore next */
cov_25mt5yuw2j().s[70]++;
exports.getReadinessLevel = getReadinessLevel;
/* istanbul ignore next */
cov_25mt5yuw2j().s[71]++;
exports.getReadinessColor = getReadinessColor;
// Helper function to safely get array values
/* istanbul ignore next */
cov_25mt5yuw2j().s[72]++;
var getArrayValue = function (value) {
  /* istanbul ignore next */
  cov_25mt5yuw2j().f[13]++;
  cov_25mt5yuw2j().s[73]++;
  if (Array.isArray(value)) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[32][0]++;
    cov_25mt5yuw2j().s[74]++;
    return value.filter(function (v) {
      /* istanbul ignore next */
      cov_25mt5yuw2j().f[14]++;
      cov_25mt5yuw2j().s[75]++;
      return typeof v === 'string';
    });
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[32][1]++;
  }
  cov_25mt5yuw2j().s[76]++;
  if (typeof value === 'string') {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[33][0]++;
    cov_25mt5yuw2j().s[77]++;
    return [value];
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[33][1]++;
  }
  cov_25mt5yuw2j().s[78]++;
  return [];
};
// Helper function to safely get string value
/* istanbul ignore next */
cov_25mt5yuw2j().s[79]++;
var getStringValue = function (value) {
  /* istanbul ignore next */
  cov_25mt5yuw2j().f[15]++;
  cov_25mt5yuw2j().s[80]++;
  if (typeof value === 'string') {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[34][0]++;
    cov_25mt5yuw2j().s[81]++;
    return value;
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[34][1]++;
  }
  cov_25mt5yuw2j().s[82]++;
  if (
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[36][0]++, Array.isArray(value)) &&
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[36][1]++, value.length > 0)) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[35][0]++;
    cov_25mt5yuw2j().s[83]++;
    return String(value[0]);
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[35][1]++;
  }
  cov_25mt5yuw2j().s[84]++;
  return '';
};
// Helper function to safely get number value
/* istanbul ignore next */
cov_25mt5yuw2j().s[85]++;
var getNumberValue = function (value) {
  /* istanbul ignore next */
  cov_25mt5yuw2j().f[16]++;
  cov_25mt5yuw2j().s[86]++;
  if (typeof value === 'number') {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[37][0]++;
    cov_25mt5yuw2j().s[87]++;
    return value;
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[37][1]++;
  }
  cov_25mt5yuw2j().s[88]++;
  if (typeof value === 'string') {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[38][0]++;
    var parsed =
    /* istanbul ignore next */
    (cov_25mt5yuw2j().s[89]++, parseFloat(value));
    /* istanbul ignore next */
    cov_25mt5yuw2j().s[90]++;
    return isNaN(parsed) ?
    /* istanbul ignore next */
    (cov_25mt5yuw2j().b[39][0]++, 0) :
    /* istanbul ignore next */
    (cov_25mt5yuw2j().b[39][1]++, parsed);
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[38][1]++;
  }
  cov_25mt5yuw2j().s[91]++;
  return 0;
};
function calculateAssessmentScores(responses) {
  /* istanbul ignore next */
  cov_25mt5yuw2j().f[17]++;
  // Financial comfort (1-5 scale)
  var financialReadiness =
  /* istanbul ignore next */
  (cov_25mt5yuw2j().s[92]++,
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[40][0]++, getNumberValue(responses.financial_comfort)) ||
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[40][1]++, 1));
  // Risk tolerance (1-5 scale)
  var riskTolerance =
  /* istanbul ignore next */
  (cov_25mt5yuw2j().s[93]++,
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[41][0]++, getNumberValue(responses.risk_tolerance)) ||
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[41][1]++, 1));
  // Support system (1-5 scale)
  var supportLevel =
  /* istanbul ignore next */
  (cov_25mt5yuw2j().s[94]++,
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[42][0]++, getNumberValue(responses.support_system)) ||
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[42][1]++, 1));
  // Confidence level (1-5 scale)
  var confidenceLevel =
  /* istanbul ignore next */
  (cov_25mt5yuw2j().s[95]++,
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[43][0]++, getNumberValue(responses.confidence_level)) ||
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[43][1]++, 1));
  // Calculate urgency based on timeline and employment status
  var timeline =
  /* istanbul ignore next */
  (cov_25mt5yuw2j().s[96]++, getStringValue(responses.transition_timeline));
  var urgencyLevel =
  /* istanbul ignore next */
  (cov_25mt5yuw2j().s[97]++, 3); // Default medium urgency
  /* istanbul ignore next */
  cov_25mt5yuw2j().s[98]++;
  switch (timeline) {
    case 'immediate':
      /* istanbul ignore next */
      cov_25mt5yuw2j().b[44][0]++;
      cov_25mt5yuw2j().s[99]++;
      urgencyLevel = 5;
      /* istanbul ignore next */
      cov_25mt5yuw2j().s[100]++;
      break;
    case 'short_term':
      /* istanbul ignore next */
      cov_25mt5yuw2j().b[44][1]++;
      cov_25mt5yuw2j().s[101]++;
      urgencyLevel = 4;
      /* istanbul ignore next */
      cov_25mt5yuw2j().s[102]++;
      break;
    case 'medium_term':
      /* istanbul ignore next */
      cov_25mt5yuw2j().b[44][2]++;
      cov_25mt5yuw2j().s[103]++;
      urgencyLevel = 3;
      /* istanbul ignore next */
      cov_25mt5yuw2j().s[104]++;
      break;
    case 'long_term':
      /* istanbul ignore next */
      cov_25mt5yuw2j().b[44][3]++;
      cov_25mt5yuw2j().s[105]++;
      urgencyLevel = 2;
      /* istanbul ignore next */
      cov_25mt5yuw2j().s[106]++;
      break;
    case 'extended':
      /* istanbul ignore next */
      cov_25mt5yuw2j().b[44][4]++;
    case 'exploring':
      /* istanbul ignore next */
      cov_25mt5yuw2j().b[44][5]++;
      cov_25mt5yuw2j().s[107]++;
      urgencyLevel = 1;
      /* istanbul ignore next */
      cov_25mt5yuw2j().s[108]++;
      break;
  }
  // Calculate skills confidence based on selected skills
  var topSkills =
  /* istanbul ignore next */
  (cov_25mt5yuw2j().s[109]++, getArrayValue(responses.top_skills));
  var skillsConfidence =
  /* istanbul ignore next */
  (cov_25mt5yuw2j().s[110]++, Math.min(100, topSkills.length * 20)); // 20 points per skill, max 100
  // Calculate overall readiness score (weighted average)
  var readinessScore =
  /* istanbul ignore next */
  (cov_25mt5yuw2j().s[111]++, Math.round((financialReadiness * 0.25 + confidenceLevel * 0.25 + supportLevel * 0.2 + riskTolerance * 0.15 + skillsConfidence / 100 * 5 * 0.15) * 20 // Convert to 0-100 scale
  ));
  /* istanbul ignore next */
  cov_25mt5yuw2j().s[112]++;
  return {
    readinessScore: Math.max(0, Math.min(100, readinessScore)),
    riskTolerance: riskTolerance,
    urgencyLevel: urgencyLevel,
    skillsConfidence: skillsConfidence,
    supportLevel: supportLevel,
    financialReadiness: financialReadiness
  };
}
function generateAssessmentInsights(responses) {
  /* istanbul ignore next */
  cov_25mt5yuw2j().f[18]++;
  cov_25mt5yuw2j().s[113]++;
  return __awaiter(this, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_25mt5yuw2j().f[19]++;
    var scores, primaryMotivation, topSkills, biggestObstacles, timeline, recommendedTimeline, keyRecommendations, careerPathSuggestions, AssessmentServiceIntegration, result, error_1;
    /* istanbul ignore next */
    cov_25mt5yuw2j().s[114]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_25mt5yuw2j().f[20]++;
      cov_25mt5yuw2j().s[115]++;
      switch (_a.label) {
        case 0:
          /* istanbul ignore next */
          cov_25mt5yuw2j().b[45][0]++;
          cov_25mt5yuw2j().s[116]++;
          scores = calculateAssessmentScores(responses);
          /* istanbul ignore next */
          cov_25mt5yuw2j().s[117]++;
          primaryMotivation = getStringValue(responses.career_change_motivation);
          /* istanbul ignore next */
          cov_25mt5yuw2j().s[118]++;
          topSkills = getArrayValue(responses.top_skills);
          /* istanbul ignore next */
          cov_25mt5yuw2j().s[119]++;
          biggestObstacles = getArrayValue(responses.biggest_obstacles);
          /* istanbul ignore next */
          cov_25mt5yuw2j().s[120]++;
          timeline = getStringValue(responses.transition_timeline);
          /* istanbul ignore next */
          cov_25mt5yuw2j().s[121]++;
          recommendedTimeline = 'Take time to plan carefully';
          /* istanbul ignore next */
          cov_25mt5yuw2j().s[122]++;
          if (scores.readinessScore >= 80) {
            /* istanbul ignore next */
            cov_25mt5yuw2j().b[46][0]++;
            cov_25mt5yuw2j().s[123]++;
            recommendedTimeline = 'You appear ready to move forward soon';
          } else {
            /* istanbul ignore next */
            cov_25mt5yuw2j().b[46][1]++;
            cov_25mt5yuw2j().s[124]++;
            if (scores.readinessScore >= 60) {
              /* istanbul ignore next */
              cov_25mt5yuw2j().b[47][0]++;
              cov_25mt5yuw2j().s[125]++;
              recommendedTimeline = 'Consider a 3-6 month preparation period';
            } else {
              /* istanbul ignore next */
              cov_25mt5yuw2j().b[47][1]++;
              cov_25mt5yuw2j().s[126]++;
              if (scores.readinessScore >= 40) {
                /* istanbul ignore next */
                cov_25mt5yuw2j().b[48][0]++;
                cov_25mt5yuw2j().s[127]++;
                recommendedTimeline = 'Plan for 6-12 months of preparation';
              } else {
                /* istanbul ignore next */
                cov_25mt5yuw2j().b[48][1]++;
                cov_25mt5yuw2j().s[128]++;
                recommendedTimeline = 'Focus on building readiness over 12+ months';
              }
            }
          }
          /* istanbul ignore next */
          cov_25mt5yuw2j().s[129]++;
          keyRecommendations = [];
          /* istanbul ignore next */
          cov_25mt5yuw2j().s[130]++;
          if (scores.financialReadiness < 3) {
            /* istanbul ignore next */
            cov_25mt5yuw2j().b[49][0]++;
            cov_25mt5yuw2j().s[131]++;
            keyRecommendations.push('Build your emergency fund before making the transition');
          } else
          /* istanbul ignore next */
          {
            cov_25mt5yuw2j().b[49][1]++;
          }
          cov_25mt5yuw2j().s[132]++;
          if (scores.supportLevel < 3) {
            /* istanbul ignore next */
            cov_25mt5yuw2j().b[50][0]++;
            cov_25mt5yuw2j().s[133]++;
            keyRecommendations.push('Build a stronger support network of mentors and peers');
          } else
          /* istanbul ignore next */
          {
            cov_25mt5yuw2j().b[50][1]++;
          }
          cov_25mt5yuw2j().s[134]++;
          if (scores.skillsConfidence < 60) {
            /* istanbul ignore next */
            cov_25mt5yuw2j().b[51][0]++;
            cov_25mt5yuw2j().s[135]++;
            keyRecommendations.push('Focus on developing and validating your key skills');
          } else
          /* istanbul ignore next */
          {
            cov_25mt5yuw2j().b[51][1]++;
          }
          cov_25mt5yuw2j().s[136]++;
          if (biggestObstacles.includes('fear_of_failure')) {
            /* istanbul ignore next */
            cov_25mt5yuw2j().b[52][0]++;
            cov_25mt5yuw2j().s[137]++;
            keyRecommendations.push('Work on mindset and confidence-building exercises');
          } else
          /* istanbul ignore next */
          {
            cov_25mt5yuw2j().b[52][1]++;
          }
          cov_25mt5yuw2j().s[138]++;
          if (biggestObstacles.includes('unclear_direction')) {
            /* istanbul ignore next */
            cov_25mt5yuw2j().b[53][0]++;
            cov_25mt5yuw2j().s[139]++;
            keyRecommendations.push('Spend time clarifying your career vision and goals');
          } else
          /* istanbul ignore next */
          {
            cov_25mt5yuw2j().b[53][1]++;
          }
          cov_25mt5yuw2j().s[140]++;
          careerPathSuggestions = [];
          /* istanbul ignore next */
          cov_25mt5yuw2j().s[141]++;
          _a.label = 1;
        case 1:
          /* istanbul ignore next */
          cov_25mt5yuw2j().b[45][1]++;
          cov_25mt5yuw2j().s[142]++;
          _a.trys.push([1, 3,, 4]);
          /* istanbul ignore next */
          cov_25mt5yuw2j().s[143]++;
          AssessmentServiceIntegration = require('./assessmentServiceIntegration').AssessmentServiceIntegration;
          /* istanbul ignore next */
          cov_25mt5yuw2j().s[144]++;
          return [4 /*yield*/, AssessmentServiceIntegration.generateCareerRecommendations(responses, {
            scores: scores,
            primaryMotivation: primaryMotivation,
            topSkills: topSkills,
            biggestObstacles: biggestObstacles,
            recommendedTimeline: recommendedTimeline,
            keyRecommendations: keyRecommendations,
            careerPathSuggestions: [],
            careerPathAnalysis: [],
            overallSkillGaps: [],
            learningPriorities: topSkills.slice(0, 3),
            estimatedTransitionTime: recommendedTimeline
          })];
        case 2:
          /* istanbul ignore next */
          cov_25mt5yuw2j().b[45][2]++;
          cov_25mt5yuw2j().s[145]++;
          result = _a.sent();
          /* istanbul ignore next */
          cov_25mt5yuw2j().s[146]++;
          careerPathSuggestions = result.careerRecommendations;
          /* istanbul ignore next */
          cov_25mt5yuw2j().s[147]++;
          return [3 /*break*/, 4];
        case 3:
          /* istanbul ignore next */
          cov_25mt5yuw2j().b[45][3]++;
          cov_25mt5yuw2j().s[148]++;
          error_1 = _a.sent();
          /* istanbul ignore next */
          cov_25mt5yuw2j().s[149]++;
          console.warn('Assessment service integration unavailable, using fallback:', error_1);
          /* istanbul ignore next */
          cov_25mt5yuw2j().s[150]++;
          careerPathSuggestions = generateFallbackCareerSuggestions(topSkills, responses);
          /* istanbul ignore next */
          cov_25mt5yuw2j().s[151]++;
          return [3 /*break*/, 4];
        case 4:
          /* istanbul ignore next */
          cov_25mt5yuw2j().b[45][4]++;
          cov_25mt5yuw2j().s[152]++;
          // Add financial literacy recommendation for low financial readiness
          if (scores.financialReadiness < 3) {
            /* istanbul ignore next */
            cov_25mt5yuw2j().b[54][0]++;
            cov_25mt5yuw2j().s[153]++;
            keyRecommendations.push('Learn financial planning and budgeting for career transitions');
          } else
          /* istanbul ignore next */
          {
            cov_25mt5yuw2j().b[54][1]++;
          }
          // Ensure we have at least some suggestions
          cov_25mt5yuw2j().s[154]++;
          if (careerPathSuggestions.length === 0) {
            /* istanbul ignore next */
            cov_25mt5yuw2j().b[55][0]++;
            cov_25mt5yuw2j().s[155]++;
            careerPathSuggestions = ['Digital Marketing Specialist', 'Entrepreneur / Startup Founder', 'Full-Stack Web Developer'];
          } else
          /* istanbul ignore next */
          {
            cov_25mt5yuw2j().b[55][1]++;
          }
          cov_25mt5yuw2j().s[156]++;
          return [2 /*return*/, {
            scores: scores,
            primaryMotivation: primaryMotivation,
            topSkills: topSkills,
            biggestObstacles: biggestObstacles,
            recommendedTimeline: recommendedTimeline,
            keyRecommendations: keyRecommendations,
            careerPathSuggestions: Array.from(new Set(careerPathSuggestions)),
            // Remove duplicates
            // Enhanced fields - will be populated by EnhancedAssessmentService
            careerPathAnalysis: [],
            overallSkillGaps: [],
            learningPriorities: topSkills.slice(0, 3),
            // Use top skills as initial learning priorities
            estimatedTransitionTime: recommendedTimeline
          }];
      }
    });
  });
}
// Function to get a readiness level description
function getReadinessLevel(score) {
  /* istanbul ignore next */
  cov_25mt5yuw2j().f[21]++;
  cov_25mt5yuw2j().s[157]++;
  if (score >= 80) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[56][0]++;
    cov_25mt5yuw2j().s[158]++;
    return 'High Readiness';
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[56][1]++;
  }
  cov_25mt5yuw2j().s[159]++;
  if (score >= 60) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[57][0]++;
    cov_25mt5yuw2j().s[160]++;
    return 'Moderate Readiness';
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[57][1]++;
  }
  cov_25mt5yuw2j().s[161]++;
  if (score >= 40) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[58][0]++;
    cov_25mt5yuw2j().s[162]++;
    return 'Building Readiness';
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[58][1]++;
  }
  cov_25mt5yuw2j().s[163]++;
  return 'Early Exploration';
}
// Function to get readiness color for UI
function getReadinessColor(score) {
  /* istanbul ignore next */
  cov_25mt5yuw2j().f[22]++;
  cov_25mt5yuw2j().s[164]++;
  if (score >= 80) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[59][0]++;
    cov_25mt5yuw2j().s[165]++;
    return 'text-green-600 dark:text-green-400';
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[59][1]++;
  }
  cov_25mt5yuw2j().s[166]++;
  if (score >= 60) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[60][0]++;
    cov_25mt5yuw2j().s[167]++;
    return 'text-yellow-600 dark:text-yellow-400';
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[60][1]++;
  }
  cov_25mt5yuw2j().s[168]++;
  if (score >= 40) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[61][0]++;
    cov_25mt5yuw2j().s[169]++;
    return 'text-orange-600 dark:text-orange-400';
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[61][1]++;
  }
  cov_25mt5yuw2j().s[170]++;
  return 'text-red-600 dark:text-red-400';
}
/**
 * Enhanced fallback career suggestions with better logic
 */
function generateFallbackCareerSuggestions(topSkills, responses) {
  /* istanbul ignore next */
  cov_25mt5yuw2j().f[23]++;
  var suggestions =
  /* istanbul ignore next */
  (cov_25mt5yuw2j().s[171]++, []);
  var skillDevelopmentInterests =
  /* istanbul ignore next */
  (cov_25mt5yuw2j().s[172]++, getArrayValue(responses.skill_development_interest));
  var careerValues =
  /* istanbul ignore next */
  (cov_25mt5yuw2j().s[173]++, getArrayValue(responses.career_values));
  // Technical programming paths
  /* istanbul ignore next */
  cov_25mt5yuw2j().s[174]++;
  if (topSkills.includes('technical_programming')) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[62][0]++;
    cov_25mt5yuw2j().s[175]++;
    suggestions.push('Full-Stack Web Developer', 'Mobile App Developer');
    /* istanbul ignore next */
    cov_25mt5yuw2j().s[176]++;
    if (
    /* istanbul ignore next */
    (cov_25mt5yuw2j().b[64][0]++, skillDevelopmentInterests.includes('ai_ml')) ||
    /* istanbul ignore next */
    (cov_25mt5yuw2j().b[64][1]++, topSkills.includes('data_analysis'))) {
      /* istanbul ignore next */
      cov_25mt5yuw2j().b[63][0]++;
      cov_25mt5yuw2j().s[177]++;
      suggestions.push('AI/ML Engineer', 'Data Scientist');
    } else
    /* istanbul ignore next */
    {
      cov_25mt5yuw2j().b[63][1]++;
    }
    cov_25mt5yuw2j().s[178]++;
    if (skillDevelopmentInterests.includes('cybersecurity')) {
      /* istanbul ignore next */
      cov_25mt5yuw2j().b[65][0]++;
      cov_25mt5yuw2j().s[179]++;
      suggestions.push('Cybersecurity Specialist');
    } else
    /* istanbul ignore next */
    {
      cov_25mt5yuw2j().b[65][1]++;
    }
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[62][1]++;
  }
  // Creative and content paths
  cov_25mt5yuw2j().s[180]++;
  if (
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[67][0]++, topSkills.includes('writing_content')) ||
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[67][1]++, topSkills.includes('design_creative'))) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[66][0]++;
    cov_25mt5yuw2j().s[181]++;
    suggestions.push('UX/UI Designer', 'Content Creator');
    /* istanbul ignore next */
    cov_25mt5yuw2j().s[182]++;
    if (skillDevelopmentInterests.includes('digital_marketing')) {
      /* istanbul ignore next */
      cov_25mt5yuw2j().b[68][0]++;
      cov_25mt5yuw2j().s[183]++;
      suggestions.push('Digital Marketing Specialist');
    } else
    /* istanbul ignore next */
    {
      cov_25mt5yuw2j().b[68][1]++;
    }
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[66][1]++;
  }
  // Business and leadership paths
  cov_25mt5yuw2j().s[184]++;
  if (
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[70][0]++, topSkills.includes('sales_marketing')) ||
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[70][1]++, topSkills.includes('leadership'))) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[69][0]++;
    cov_25mt5yuw2j().s[185]++;
    suggestions.push('Digital Marketing Specialist', 'Product Manager');
    /* istanbul ignore next */
    cov_25mt5yuw2j().s[186]++;
    if (
    /* istanbul ignore next */
    (cov_25mt5yuw2j().b[72][0]++, skillDevelopmentInterests.includes('entrepreneurship')) ||
    /* istanbul ignore next */
    (cov_25mt5yuw2j().b[72][1]++, careerValues.includes('autonomy'))) {
      /* istanbul ignore next */
      cov_25mt5yuw2j().b[71][0]++;
      cov_25mt5yuw2j().s[187]++;
      suggestions.push('Entrepreneur / Startup Founder');
    } else
    /* istanbul ignore next */
    {
      cov_25mt5yuw2j().b[71][1]++;
    }
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[69][1]++;
  }
  // Education and coaching paths
  cov_25mt5yuw2j().s[188]++;
  if (
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[74][0]++, topSkills.includes('teaching_training')) ||
  /* istanbul ignore next */
  (cov_25mt5yuw2j().b[74][1]++, topSkills.includes('coaching_mentoring'))) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[73][0]++;
    cov_25mt5yuw2j().s[189]++;
    suggestions.push('Online Coaching', 'Course Creation', 'Training Specialist');
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[73][1]++;
  }
  // Data and analysis paths
  cov_25mt5yuw2j().s[190]++;
  if (topSkills.includes('data_analysis')) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[75][0]++;
    cov_25mt5yuw2j().s[191]++;
    suggestions.push('Data Scientist', 'Business Analyst');
    /* istanbul ignore next */
    cov_25mt5yuw2j().s[192]++;
    if (skillDevelopmentInterests.includes('financial_planning')) {
      /* istanbul ignore next */
      cov_25mt5yuw2j().b[76][0]++;
      cov_25mt5yuw2j().s[193]++;
      suggestions.push('Financial Advisor / Planner');
    } else
    /* istanbul ignore next */
    {
      cov_25mt5yuw2j().b[76][1]++;
    }
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[75][1]++;
  }
  // Project management paths
  cov_25mt5yuw2j().s[194]++;
  if (topSkills.includes('project_management')) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[77][0]++;
    cov_25mt5yuw2j().s[195]++;
    suggestions.push('Product Manager', 'Project Manager', 'Scrum Master');
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[77][1]++;
  }
  // Remove duplicates and return top suggestions
  var uniqueSuggestions =
  /* istanbul ignore next */
  (cov_25mt5yuw2j().s[196]++, Array.from(new Set(suggestions)));
  // If still no matches, provide diverse default options
  /* istanbul ignore next */
  cov_25mt5yuw2j().s[197]++;
  if (uniqueSuggestions.length === 0) {
    /* istanbul ignore next */
    cov_25mt5yuw2j().b[78][0]++;
    cov_25mt5yuw2j().s[198]++;
    return ['Digital Marketing Specialist', 'Full-Stack Web Developer', 'Product Manager', 'Data Analyst', 'UX/UI Designer'];
  } else
  /* istanbul ignore next */
  {
    cov_25mt5yuw2j().b[78][1]++;
  }
  cov_25mt5yuw2j().s[199]++;
  return uniqueSuggestions.slice(0, 5);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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